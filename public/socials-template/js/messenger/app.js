(function() {

    /*globals moment*/


    var cache = {
        $messengerHTML: $('.js--modal-messenger'),
        $responseTemplate: $('.js--modal-messenger .js--messenger-response-template')
    };

    var pluploadInited = false,
        pluploadFiles;
    attachments = new Array();

    /**
     * Initialise the module
     * @function init
     */

    var init = function(rr_ref, issuename, open_button) {
        /*globals bootbox*/
        // alert(rr_ref);
        console.log('bootbox init');
        if(rr_ref === undefined){
            rr_ref = "0";
        }
        if(issuename === undefined){
            issuename = "0";
        }
        var modal = bootbox.dialog({
            title: false,
            className: 'modal--messenger',
            message: cache.$messengerHTML.html(),
            size: 'large',
            show: false
        });

        if(rr_ref != "0") {
            $(".message-list__item").hide();
            $("."+rr_ref).show();
        }

        modal.on('shown.bs.modal', messengerOpen);
        modal.modal('show');

        $("input.rrec_status").change(function() {
            var post_data = {};
            post_data['survey_id'] = survey_id;
            if($(this).is(":checked")) {
                post_data[issuename] = "0";

            } else {
                post_data[issuename] = "1";
            }
            jQuery.ajax({
                url: '/risk-improvement/form/submission/'+submission_id+'/close',
                type: 'POST',
                dataType: 'json',
                data: post_data,
                complete: function(xhr, textStatus) {
                    open_button.attr('data-issueclosed', post_data[issuename]);
                },
                success: function(data, textStatus, xhr) {
                    open_button.attr('data-issueclosed', post_data[issuename]);
                },
                error: function(xhr, textStatus, errorThrown) {
                    open_button.attr('data-issueclosed', post_data[issuename]);
                }
            });

        });

    };

    /**
     * Set up all JS for elements within modal (as dynamically created)
     * @function messengerOpen
     * @param {event} e
     */

    var messengerOpen = function(e) {
        var $modal = $(e.currentTarget);

console.log('bootbox messengeropen');

        // textarea autogrow
        $modal.find('textarea').autoGrow();

        messengerScroll($modal);
        messengerEvents($modal);
        messengerPopovers($modal);
    };


    /**
     * Sets up Messenger scroll
     * @param $modal
     */
    var messengerScroll = function($modal){

        var messenger_list =  $modal.find('.message-list').slimScroll({
            height: '450px',
            start: 'bottom'
        });

    };



    /**
     * Add messenger events (form submit)
     * @function messengerEvents
     * @param {jQuery} $modal modal element
     */

    var messengerEvents = function($modal) {
        console.log('bootbox events firing');
        
        var $form = $modal.find('.js--messenger__input-form'),
            $attachBtn = $modal.find('.js--messenger__attach');

        $form.on('submit', messengerSubmit);
        $attachBtn.on('click', messengerPluploadInit.bind(null, $modal));

    };

    /**
     * Add popover / tooltips for notification and visibility icons
     * @function messengerPopovers
     * @param {jQuery} $container element to find popovers within
     */

    var messengerPopovers = function($container) {
        $container.find('[data-toggle="popover"]').popover({
            content: function() {

                var $this = $(this),
                    content = $this.data('popover-content'),
                    notificationNames = $this.data('notification-names'),
                    notificationNamesHTML;

                if (notificationNames) {

                    notificationNames = notificationNames.split(',');
                    content += '<ul class="popover-list">';

                    for (var i = 0; i < notificationNames.length; i++) {
                        content += '<li>' + notificationNames[i] + '</li>';
                    }

                    content += '</ul>';
                }

                return content;
            },
            html: true
        });
    };

    /**
     * On submitting messenger form, add new message to message body
     * @function messengerSubmit
     * @param {event} e
     */

    var messengerSubmit = function(e) {
        e.preventDefault();

        console.log('bootbox messengersubmit');

        var $form = $(e.currentTarget),
            $input = $form.find('.messenger__input-field'),
            $messenger = $form.closest('.modal--messenger'),
            $messageList = $messenger.find('.message-list'),
            $messengerBodyContext = $messenger.find('.messenger__body-context'),
            message = $input.val().replace(/\n/g, '<br/>'), // preserve line breaks
            $newMessage = messageCreate(message),
            $plupload = $messenger.find('.js--messenger__plupload'),
            $button = $form.find('button[type="submit"]'),
            scrollTop = 0;

        $button.addClass('disabled');
        $button.find('i.fa').removeClass('hidden');

        // no message - do nothing
        if (!message || message === '') {
            $button.removeClass('disabled');
            $button.find('i.fa').addClass('hidden');
            return;
        }

        var form_data = $($form).serializeArray();



        var attachment_text = "";
        if(attachments) {
            attachment_text = '<div class="message-list__item-files">';
            for (i = 0; i < attachments.length; i++) {

                attachment_text += '<div class="message-list__item-file"><i class="icon icon-paperclip"></i> <a href="'+attachments[i]+'">'+attachments[i]+'</a></div>';

            }
            attachment_text += '</div>';
        }
        form_data.push({name: 'attachments', value : attachment_text});
        // $($form+" :input").each(function(){
        //     var input = $(this);
        // });

        jQuery.ajax({
            url: '/surveys/messaging/send',
            type: 'POST',
            dataType: 'json',
            data: form_data,
            complete: function(xhr, textStatus) {
                //called when complete
            },
            success: function(data, textStatus, xhr) {
                if(data.response == 'success') {
                    // reset input field
                    $input
                        .val('')
                        .focus()
                        .css('height', '');

                    // Close plupload
                    $plupload.addClass('hidden');

                    // add new message to message list
                    $messageList.append($newMessage);
                    // initialise message JS
                    messengerPopovers($newMessage);
                    $("."+$("input[name='rr_ref']").val()).show();

                    $messageList.slimscroll({ scrollBy: '400px' });

                }
                $button.removeClass('disabled');
                $button.find('i.fa').addClass('hidden');
            },
            error: function(xhr, textStatus, errorThrown) {
                //called when there is an error
                $button.removeClass('disabled');
                $button.find('i.fa').addClass('hidden');
            }
        });



    };


    /**
     * Resize messenger's body height so messenger fills screen
     * with input form visible in viewport
     * @function messageCreate
     * @param {string} message message content
     * @returns {jQuery} $newMessage
     */

    var messageCreate = function(message) {
        var $newMessage = cache.$responseTemplate.clone(),
        //$files = $newMessage.find('.message-list__item-files'),
        //$fileName = $newMessage.find('.message-list__item-file'),
            $message = $newMessage.find('.message-list__item-message'),
            $timeDate = $newMessage.find('.message-list__item-time-date'),
            timeDate;

        // format date using moment.js
        if (typeof moment !== 'undefined') {
            timeDate = moment().calendar();
        }

        var attachment_text = "";
        if(attachments) {
            attachment_text = '<div class="message-list__item-files">';
            for (i = 0; i < attachments.length; i++) {

                attachment_text += '<div class="message-list__item-file"><i class="icon icon-paperclip"></i> <a href="'+attachments[i]+'">'+attachments[i]+'</a></div>';

            }
            attachment_text += '</div>';
        }
        // Set content of cloned element
        $message.html(message+attachment_text);
        $timeDate.text(timeDate);


        // if (pluploadFiles) {
        //     for (var i in pluploadFiles) {
        //         var $newFileName = $fileName.clone();
        //         $newFileName
        //             .removeClass('hidden')
        //             .find('a').text(pluploadFiles[i].name);

        //         $files.append($newFileName);

        //     }
        // }

        $newMessage.removeClass('hidden');
        $newMessage.addClass($("input[name='rr_ref']").val());


        return $newMessage;
    };

    /**
     * Init plupload html5 file uploader
     * @function messengerPluploadInit
     * @param {jQuery} $modal modal element
     */

    var messengerPluploadInit = function($modal) {

        var $plupload = $modal.find('.js--messenger__plupload'),
            $button = $modal.find('button[type="submit"]');
        $plupload.toggleClass('hidden');

        if (pluploadInited) {
            return;
        }

        $plupload.plupload({
            // General settings
            runtimes: 'html5,flash,silverlight,html4',
            url: "/surveys/messaging/upload",

            // Maximum file size
            max_file_size: '2mb',

            // Resize images on clientside if we can
            resize: {
                width: 200,
                height: 200,
                quality: 90,
                crop: true // crop to exact dimensions
            },

            // Specify what files to browse for
            filters: [{
                title: "Image files",
                extensions: "jpg,gif,png,jpeg,bmp"
            }, {
                title: "Zip files",
                extensions: "zip,avi"
            }],

            // Rename files by clicking on their titles
            rename: true,

            // Sort files
            sortable: true,

            // Enable ability to drag'n'drop files onto the widget (currently only HTML5 supports that)
            dragdrop: true,


            // Flash settings
            flash_swf_url: '/plupload/js/Moxie.swf',

            // Silverlight settings
            silverlight_xap_url: '/plupload/js/Moxie.xap',

            multipart_params : {
                "survey_id" : survey_id
            },

            init: {
                PostInit: function(e) {
                    $plupload.find('.plupload_button').addClass('btn').addClass('btn-xs').addClass('btn-primary');
                },
                FilesAdded: function(uploader, files) {
                    $plupload.addClass('file-added');
                    uploader.start();
                    $button.addClass('disabled');
                    $button.find('i.fa').removeClass('hidden');
                    pluploadFiles = files;

                },
                UploadComplete: function(uploader, files) {
                    $button.removeClass('disabled');
                    $button.find('i.fa').addClass('hidden');
                },
                FileUploaded: function(uploader, files, object) {
                    var response = JSON.parse(object.response);
                    if(response.response == "success") {
                        var url = "/surveys/messaging/attachment/"+survey_id+'/'+response.name;
                        attachments.push(url);
                    }

                    if(response.response == 'error'){
                        bootbox.alert(response.message);
                    }
                }
            },

        });

        pluploadInited = true;

    };

    $(".show_message_thread").click(function() {
        var refclass = $(this).data("refclass");
        $("input[name='rr_ref']").val(refclass);
        // $('.rrec_status').attr('name', $(this).data("refissue"));
        var issuename = $(this).data("refissue");

        var issueclosed = $(this).attr("data-issueclosed");

        if(issueclosed == '1') {
            $('.rrec_status').attr('checked', false);
        } else {
            $('.rrec_status').attr('checked', true);
        }

        init(refclass, issuename, $(this));
    });






    //init();
}());
