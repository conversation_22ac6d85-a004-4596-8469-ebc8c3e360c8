/**
 * @license
 * Copyright (c) 2018 amCharts (<PERSON><PERSON><PERSON>, <PERSON><PERSON>)
 *
 * This sofware is provided under multiple licenses. Please see below for
 * links to appropriate usage.
 *
 * Free amCharts linkware license. Details and conditions:
 * https://github.com/amcharts/amcharts4/blob/master/LICENSE
 *
 * One of the amCharts commercial licenses. Details and pricing:
 * https://www.amcharts.com/online-store/
 * https://www.amcharts.com/online-store/licenses-explained/
 *
 * If in doubt, contact <NAME_EMAIL>
 *
 * PLEASE DO NOT REMOVE THIS COPYRIGHT NOTICE.
 * @hidden
 */
am4internal_webpackJsonp(["689e"],{XFs4:function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var a={};i.d(a,"GaugeChartDataItem",function(){return Pt}),i.d(a,"GaugeChart",function(){return At}),i.d(a,"RadarChartDataItem",function(){return ft}),i.d(a,"RadarChart",function(){return xt}),i.d(a,"XYChartDataItem",function(){return G}),i.d(a,"XYChart",function(){return q}),i.d(a,"SerialChartDataItem",function(){return r.b}),i.d(a,"SerialChart",function(){return r.a}),i.d(a,"PieChart3DDataItem",function(){return Vt}),i.d(a,"PieChart3D",function(){return St}),i.d(a,"PieChartDataItem",function(){return Ct.b}),i.d(a,"PieChart",function(){return Ct.a}),i.d(a,"SlicedChart",function(){return Ot}),i.d(a,"SlicedChartDataItem",function(){return kt}),i.d(a,"FlowDiagramDataItem",function(){return zt}),i.d(a,"FlowDiagram",function(){return Ut}),i.d(a,"SankeyDiagramDataItem",function(){return Qt}),i.d(a,"SankeyDiagram",function(){return $t}),i.d(a,"ChordDiagramDataItem",function(){return ne}),i.d(a,"ChordDiagram",function(){return re}),i.d(a,"TreeMapDataItem",function(){return ce}),i.d(a,"TreeMap",function(){return de}),i.d(a,"XYChart3DDataItem",function(){return ve}),i.d(a,"XYChart3D",function(){return be}),i.d(a,"ChartDataItem",function(){return wt.b}),i.d(a,"Chart",function(){return wt.a}),i.d(a,"LegendDataItem",function(){return jt.b}),i.d(a,"Legend",function(){return jt.a}),i.d(a,"LegendSettings",function(){return jt.c}),i.d(a,"HeatLegend",function(){return Pe.a}),i.d(a,"SeriesDataItem",function(){return V.b}),i.d(a,"Series",function(){return V.a}),i.d(a,"XYSeriesDataItem",function(){return R}),i.d(a,"XYSeries",function(){return L}),i.d(a,"LineSeriesDataItem",function(){return it}),i.d(a,"LineSeries",function(){return at}),i.d(a,"LineSeriesSegment",function(){return J}),i.d(a,"CandlestickSeriesDataItem",function(){return Ce}),i.d(a,"CandlestickSeries",function(){return Ie}),i.d(a,"OHLCSeriesDataItem",function(){return _e}),i.d(a,"OHLCSeries",function(){return Te}),i.d(a,"ColumnSeriesDataItem",function(){return oe}),i.d(a,"ColumnSeries",function(){return le}),i.d(a,"StepLineSeriesDataItem",function(){return Se}),i.d(a,"StepLineSeries",function(){return Fe}),i.d(a,"RadarSeriesDataItem",function(){return nt}),i.d(a,"RadarSeries",function(){return rt}),i.d(a,"RadarColumnSeriesDataItem",function(){return Oe}),i.d(a,"RadarColumnSeries",function(){return we}),i.d(a,"PieSeriesDataItem",function(){return It.b}),i.d(a,"PieSeries",function(){return It.a}),i.d(a,"FunnelSeries",function(){return Ne}),i.d(a,"FunnelSeriesDataItem",function(){return je}),i.d(a,"PyramidSeries",function(){return We}),i.d(a,"PyramidSeriesDataItem",function(){return Me}),i.d(a,"PictorialStackedSeries",function(){return Ee}),i.d(a,"PictorialStackedSeriesDataItem",function(){return Be}),i.d(a,"PieTick",function(){return He.a}),i.d(a,"FunnelSlice",function(){return Le}),i.d(a,"PieSeries3DDataItem",function(){return _t}),i.d(a,"PieSeries3D",function(){return Tt}),i.d(a,"TreeMapSeriesDataItem",function(){return he}),i.d(a,"TreeMapSeries",function(){return ue}),i.d(a,"ColumnSeries3DDataItem",function(){return fe}),i.d(a,"ColumnSeries3D",function(){return xe}),i.d(a,"ConeSeriesDataItem",function(){return Ke}),i.d(a,"ConeSeries",function(){return Ge}),i.d(a,"CurvedColumnSeries",function(){return Je}),i.d(a,"CurvedColumnSeriesDataItem",function(){return Ze}),i.d(a,"AxisDataItem",function(){return A.b}),i.d(a,"Axis",function(){return A.a}),i.d(a,"Grid",function(){return ct.a}),i.d(a,"AxisTick",function(){return Qe.a}),i.d(a,"AxisLabel",function(){return $e.a}),i.d(a,"AxisLine",function(){return ti.a}),i.d(a,"AxisFill",function(){return ht.a}),i.d(a,"AxisRenderer",function(){return lt.a}),i.d(a,"AxisBreak",function(){return C.a}),i.d(a,"ValueAxisDataItem",function(){return l.b}),i.d(a,"ValueAxis",function(){return l.a}),i.d(a,"CategoryAxisDataItem",function(){return _}),i.d(a,"CategoryAxis",function(){return T}),i.d(a,"CategoryAxisBreak",function(){return I}),i.d(a,"DateAxisDataItem",function(){return x}),i.d(a,"DateAxis",function(){return v}),i.d(a,"DurationAxisDataItem",function(){return ei}),i.d(a,"DurationAxis",function(){return ii}),i.d(a,"DateAxisBreak",function(){return d}),i.d(a,"ValueAxisBreak",function(){return u.a}),i.d(a,"AxisRendererX",function(){return b.a}),i.d(a,"AxisRendererY",function(){return P.a}),i.d(a,"AxisRendererRadial",function(){return mt}),i.d(a,"AxisLabelCircular",function(){return pt.a}),i.d(a,"AxisRendererCircular",function(){return yt}),i.d(a,"AxisFillCircular",function(){return ut}),i.d(a,"GridCircular",function(){return dt}),i.d(a,"AxisRendererX3D",function(){return pe}),i.d(a,"AxisRendererY3D",function(){return ye}),i.d(a,"Tick",function(){return Xe.a}),i.d(a,"Bullet",function(){return et.a}),i.d(a,"LabelBullet",function(){return Kt}),i.d(a,"CircleBullet",function(){return ai}),i.d(a,"ErrorBullet",function(){return ni}),i.d(a,"XYChartScrollbar",function(){return U}),i.d(a,"ClockHand",function(){return bt}),i.d(a,"FlowDiagramNode",function(){return Nt}),i.d(a,"FlowDiagramLink",function(){return Bt}),i.d(a,"SankeyNode",function(){return Gt}),i.d(a,"SankeyLink",function(){return Zt}),i.d(a,"ChordNode",function(){return ee}),i.d(a,"ChordLink",function(){return ae}),i.d(a,"NavigationBarDataItem",function(){return hi}),i.d(a,"NavigationBar",function(){return ui}),i.d(a,"Column",function(){return se}),i.d(a,"Candlestick",function(){return Ae}),i.d(a,"OHLC",function(){return De}),i.d(a,"RadarColumn",function(){return ke}),i.d(a,"Column3D",function(){return me}),i.d(a,"ConeColumn",function(){return Ue}),i.d(a,"CurvedColumn",function(){return qe}),i.d(a,"XYCursor",function(){return B}),i.d(a,"Cursor",function(){return N}),i.d(a,"RadarCursor",function(){return ot});var n=i("m4/l"),r=i("2I/e"),s=i("C6dT"),o=i("vMqJ"),l=i("pR7v"),h=i("+qIf"),u=i("ZoDA"),c=i("aCit"),d=function(t){function e(){var e=t.call(this)||this;return e.className="DateAxisBreak",e.applyTheme(),e}return n.c(e,t),Object.defineProperty(e.prototype,"startDate",{get:function(){return this.getPropertyValue("startDate")},set:function(t){this.setPropertyValue("startDate",t)&&(this.startValue=t.getTime(),this.axis&&(this.axis.invalidate(),this.axis.invalidateSeries()))},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"endDate",{get:function(){return this.getPropertyValue("endDate")},set:function(t){this.setPropertyValue("endDate",t)&&(this.endValue=t.getTime(),this.axis&&(this.axis.invalidate(),this.axis.invalidateSeries()))},enumerable:!0,configurable:!0}),e}(u.a);c.b.registeredClasses.DateAxisBreak=d;var p=i("L91H"),y=i("Mtpk"),g=i("Wglt"),m=i("Gg2j"),f=i("Qkdp"),x=function(t){function e(){var e=t.call(this)||this;return e.className="DateAxisDataItem",e.applyTheme(),e.values.date={},e.values.endDate={},e}return n.c(e,t),Object.defineProperty(e.prototype,"date",{get:function(){return this.dates.date},set:function(t){this.setDate("date",t),this.value=t.getTime()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"endDate",{get:function(){return this.dates.endDate},set:function(t){this.setDate("endDate",t),this.endValue=t.getTime()},enumerable:!0,configurable:!0}),e}(l.b),v=function(t){function e(){var e=t.call(this)||this;return e.gridIntervals=new o.b,e.dateFormats=new h.a,e.periodChangeDateFormats=new h.a,e._baseIntervalReal={timeUnit:"day",count:1},e._minDifference={},e.className="DateAxis",e.setPropertyValue("markUnitChange",!0),e.snapTooltip=!0,e.gridIntervals.pushAll([{timeUnit:"millisecond",count:1},{timeUnit:"millisecond",count:5},{timeUnit:"millisecond",count:10},{timeUnit:"millisecond",count:50},{timeUnit:"millisecond",count:100},{timeUnit:"millisecond",count:500},{timeUnit:"second",count:1},{timeUnit:"second",count:5},{timeUnit:"second",count:10},{timeUnit:"second",count:30},{timeUnit:"minute",count:1},{timeUnit:"minute",count:5},{timeUnit:"minute",count:10},{timeUnit:"minute",count:15},{timeUnit:"minute",count:30},{timeUnit:"hour",count:1},{timeUnit:"hour",count:3},{timeUnit:"hour",count:6},{timeUnit:"hour",count:12},{timeUnit:"day",count:1},{timeUnit:"day",count:2},{timeUnit:"day",count:3},{timeUnit:"day",count:4},{timeUnit:"day",count:5},{timeUnit:"week",count:1},{timeUnit:"month",count:1},{timeUnit:"month",count:2},{timeUnit:"month",count:3},{timeUnit:"month",count:6},{timeUnit:"year",count:1},{timeUnit:"year",count:2},{timeUnit:"year",count:5},{timeUnit:"year",count:10},{timeUnit:"year",count:50},{timeUnit:"year",count:100},{timeUnit:"year",count:200},{timeUnit:"year",count:500},{timeUnit:"year",count:1e3},{timeUnit:"year",count:2e3},{timeUnit:"year",count:5e3},{timeUnit:"year",count:1e4},{timeUnit:"year",count:1e5}]),e.axisFieldName="date",e.applyTheme(),e}return n.c(e,t),e.prototype.fillRule=function(t){var e=t.value,i=t.component,a=i._gridInterval,n=p.getDuration(a.timeUnit,a.count);Math.round((e-i.min)/n)/2==Math.round(Math.round((e-i.min)/n)/2)?t.axisFill.__disabled=!0:t.axisFill.__disabled=!1},e.prototype.applyInternalDefaults=function(){t.prototype.applyInternalDefaults.call(this),this.dateFormats.hasKey("millisecond")||this.dateFormats.setKey("millisecond",this.language.translate("_date_millisecond")),this.dateFormats.hasKey("second")||this.dateFormats.setKey("second",this.language.translate("_date_second")),this.dateFormats.hasKey("minute")||this.dateFormats.setKey("minute",this.language.translate("_date_minute")),this.dateFormats.hasKey("hour")||this.dateFormats.setKey("hour",this.language.translate("_date_hour")),this.dateFormats.hasKey("day")||this.dateFormats.setKey("day",this.language.translate("_date_day")),this.dateFormats.hasKey("week")||this.dateFormats.setKey("week",this.language.translate("_date_day")),this.dateFormats.hasKey("month")||this.dateFormats.setKey("month",this.language.translate("_date_month")),this.dateFormats.hasKey("year")||this.dateFormats.setKey("year",this.language.translate("_date_year")),this.periodChangeDateFormats.hasKey("millisecond")||this.periodChangeDateFormats.setKey("millisecond",this.language.translate("_date_millisecond")),this.periodChangeDateFormats.hasKey("second")||this.periodChangeDateFormats.setKey("second",this.language.translate("_date_second")),this.periodChangeDateFormats.hasKey("minute")||this.periodChangeDateFormats.setKey("minute",this.language.translate("_date_minute")),this.periodChangeDateFormats.hasKey("hour")||this.periodChangeDateFormats.setKey("hour",this.language.translate("_date_hour")),this.periodChangeDateFormats.hasKey("day")||this.periodChangeDateFormats.setKey("day",this.language.translate("_date_day")),this.periodChangeDateFormats.hasKey("week")||this.periodChangeDateFormats.setKey("week",this.language.translate("_date_day")),this.periodChangeDateFormats.hasKey("month")||this.periodChangeDateFormats.setKey("month",this.language.translate("_date_month")+" "+this.language.translate("_date_year"))},e.prototype.createDataItem=function(){return new x},e.prototype.createAxisBreak=function(){return new d},e.prototype.validateDataItems=function(){var e=this.start,i=this.end,a=(this.max-this.min)/this.baseDuration;t.prototype.validateDataItems.call(this),this.maxZoomFactor=(this.max-this.min)/this.baseDuration,this._deltaMinMax=this.baseDuration/2,e+=(i-e)*(1-a/((this.max-this.min)/this.baseDuration)),this.zoom({start:e,end:i},!1,!0)},e.prototype.handleSelectionExtremesChange=function(){},e.prototype.calculateZoom=function(){var e=this;t.prototype.calculateZoom.call(this);var i=this.chooseInterval(0,this.adjustDifference(this._minZoomed,this._maxZoomed),this._gridCount);p.getDuration(i.timeUnit,i.count)<this.baseDuration&&(i=n.a({},this.baseInterval)),this._gridInterval=i,this._gridDate=p.round(new Date(this.min),i.timeUnit,i.count,this.getFirstWeekDay(),this.dateFormatter.utc),this._nextGridUnit=p.getNextUnit(i.timeUnit),this._intervalDuration=p.getDuration(i.timeUnit,i.count);var a=Math.ceil(this._difference/this._intervalDuration);a=Math.max(-5,Math.floor(this.start*a)-3),p.add(this._gridDate,i.timeUnit,a*i.count,this.dateFormatter.utc),g.each(this.series.iterator(),function(t){if(t.baseAxis==e){var i=t.getAxisField(e),a=p.round(new Date(e._minZoomed),e.baseInterval.timeUnit,e.baseInterval.count).getTime(),n=a.toString(),r=t.dataItemsByAxis.getKey(e.uid).getKey(n),s=0;0!=e.start&&(s=r?(r=e.findFirst(r,a,i)).index:t.dataItems.findClosestIndex(e._minZoomed,function(t){return t[i]},"left"));var o=e.baseInterval,l=p.add(p.round(new Date(e._maxZoomed),o.timeUnit,o.count,e.getFirstWeekDay(),e.dateFormatter.utc),o.timeUnit,o.count,e.dateFormatter.utc).getTime(),h=l.toString(),u=t.dataItemsByAxis.getKey(e.uid).getKey(h),c=t.dataItems.length;1!=e.end&&(u?c=u.index:(l-=1,c=t.dataItems.findClosestIndex(l,function(t){return t[i]},"right")+1)),t.startIndex=s,t.endIndex=c,t.dataRangeInvalid&&t.validateDataRange()}})},e.prototype.findFirst=function(t,e,i){var a=t.index;if(a>0){var n=t.component.dataItems.getIndex(a-1),r=n[i];return!r||r.getTime()<e?t:this.findFirst(n,e,i)}return t},e.prototype.validateData=function(){t.prototype.validateData.call(this),y.isNumber(this.baseInterval.count)||(this.baseInterval.count=1)},Object.defineProperty(e.prototype,"minDifference",{get:function(){var t=this,e=Number.MAX_VALUE;return this.series.each(function(i){e>t._minDifference[i.uid]&&(e=t._minDifference[i.uid])}),e!=Number.MAX_VALUE&&0!=e||(e=p.getDuration("day")),e},enumerable:!0,configurable:!0}),e.prototype.seriesDataChangeUpdate=function(t){this._minDifference[t.uid]=Number.MAX_VALUE},e.prototype.postProcessSeriesDataItems=function(){var t=this;this.series.each(function(e){JSON.stringify(e._baseInterval[t.uid])!=JSON.stringify(t.baseInterval)&&(e.dataItems.each(function(e){t.postProcessSeriesDataItem(e)}),e._baseInterval[t.uid]=t.baseInterval)}),this.addEmptyUnitsBreaks()},e.prototype.postProcessSeriesDataItem=function(t){var e=this,i=this.baseInterval,a=t.component.dataItemsByAxis.getKey(this.uid);f.each(t.dates,function(n){var r=t.getDate(n).getTime(),s=p.round(new Date(r),i.timeUnit,i.count,e.getFirstWeekDay(),e.dateFormatter.utc).getTime(),o=p.add(new Date(s),i.timeUnit,i.count,e.dateFormatter.utc);t.setCalculatedValue(n,s,"open"),t.setCalculatedValue(n,o.getTime(),"close"),a.setKey(s.toString(),t)})},e.prototype.addEmptyUnitsBreaks=function(){var t=this;if(this.skipEmptyPeriods&&y.isNumber(this.min)&&y.isNumber(this.max)){var e=this.baseInterval.timeUnit,i=this.baseInterval.count;this.axisBreaks.clear();for(var a=p.round(new Date(this.min),e,i,this.getFirstWeekDay(),this.dateFormatter.utc),n=void 0,r=function(){p.add(a,e,i,s.dateFormatter.utc);var r=a.getTime(),o=r.toString();g.contains(s.series.iterator(),function(e){return!!e.dataItemsByAxis.getKey(t.uid).getKey(o)})?n&&(n.endDate=new Date(r-1),n=void 0):n||((n=s.axisBreaks.create()).startDate=new Date(r))},s=this;a.getTime()<this.max-this.baseDuration;)r()}},e.prototype.fixAxisBreaks=function(){var e=this;t.prototype.fixAxisBreaks.call(this);var i=this.axisBreaks;i.length>0&&i.each(function(t){var i=Math.ceil(e._gridCount*(Math.min(e.end,t.endPosition)-Math.max(e.start,t.startPosition))/(e.end-e.start));t.gridInterval=e.chooseInterval(0,t.adjustedEndValue-t.adjustedStartValue,i);var a=p.round(new Date(t.adjustedStartValue),t.gridInterval.timeUnit,t.gridInterval.count,e.getFirstWeekDay(),e.dateFormatter.utc);a.getTime()>t.startDate.getTime()&&p.add(a,t.gridInterval.timeUnit,t.gridInterval.count,e.dateFormatter.utc),t.gridDate=a})},e.prototype.getFirstWeekDay=function(){return this.dateFormatter?this.dateFormatter.firstDayOfWeek:1},e.prototype.getGridDate=function(t,e){var i=this._gridInterval.timeUnit,a=this._gridInterval.count;p.round(t,i,1,this.getFirstWeekDay(),this.dateFormatter.utc);var n=t.getTime(),r=p.copy(t),s=p.add(r,i,e,this.dateFormatter.utc).getTime(),o=this.isInBreak(s);o&&o.endDate&&(r=new Date(o.endDate.getTime()),p.round(r,i,a,this.getFirstWeekDay(),this.dateFormatter.utc),r.getTime()<o.endDate.getTime()&&p.add(r,i,a,this.dateFormatter.utc),s=r.getTime());var l=this.adjustDifference(n,s);return Math.round(l/p.getDuration(i))<a?this.getGridDate(t,e+a):r},e.prototype.getBreaklessDate=function(t,e,i){var a=new Date(t.endValue);p.round(a,e,i,this.getFirstWeekDay(),this.dateFormatter.utc),p.add(a,e,i,this.dateFormatter.utc);var n=a.getTime();return(t=this.isInBreak(n))?this.getBreaklessDate(t,e,i):a},e.prototype.validateAxisElements=function(){var t=this;if(y.isNumber(this.max)&&y.isNumber(this.min)){this.calculateZoom();var e=this._gridDate.getTime(),i=this._gridInterval.timeUnit,a=this._gridInterval.count,n=p.copy(this._gridDate),r=this._dataItemsIterator;this.resetIterators();for(var s=function(){var t=o.getGridDate(p.copy(n),a);e=t.getTime();var s=p.copy(t);s=p.add(s,i,a,o.dateFormatter.utc);var l=o.dateFormats.getKey(i);o.markUnitChange&&n&&p.checkChange(t,n,o._nextGridUnit,o.dateFormatter.utc)&&"year"!==i&&(l=o.periodChangeDateFormats.getKey(i));var h=o.dateFormatter.format(t,l),u=r.find(function(t){return t.text===h});u.__disabled&&(u.__disabled=!1),o.appendDataItem(u),u.axisBreak=void 0,u.date=t,u.endDate=s,u.text=h,n=t,o.validateDataElement(u)},o=this;e<=this._maxZoomed;)s();var l=this.renderer;g.each(this.axisBreaks.iterator(),function(e){if(e.breakSize>0){var i=e.gridInterval.timeUnit,a=e.gridInterval.count;if(m.getDistance(e.startPoint,e.endPoint)>4*l.minGridDistance)for(var n,s=e.gridDate.getTime(),o=0,h=function(){var l=p.copy(e.gridDate);if(s=p.add(l,i,a*o,t.dateFormatter.utc).getTime(),o++,s>e.adjustedStartValue&&s<e.adjustedEndValue){var h=p.copy(l);h=p.add(h,i,a,t.dateFormatter.utc);var u=t.dateFormats.getKey(i);t.markUnitChange&&n&&p.checkChange(l,n,t._nextGridUnit,t.dateFormatter.utc)&&"year"!==i&&(u=t.periodChangeDateFormats.getKey(i));var c=t.dateFormatter.format(l,u),d=r.find(function(t){return t.text===c});d.__disabled&&(d.__disabled=!1),t.appendDataItem(d),d.axisBreak=e,e.dataItems.moveValue(d),d.date=l,d.endDate=h,d.text=c,n=l,t.validateDataElement(d)}};s<=e.adjustedMax;)h()}})}},e.prototype.validateDataElement=function(t){if(y.isNumber(this.max)&&y.isNumber(this.min)){var e=this.renderer,i=t.value,a=t.endValue;y.isNumber(a)||(a=i);var n=this.valueToPosition(i),r=this.valueToPosition(a),s=r;!t.isRange&&this._gridInterval.count>this.baseInterval.count&&(r=n+(r-n)/(this._gridInterval.count/this.baseInterval.count)),t.position=n;var o=t.tick;o&&!o.disabled&&e.updateTickElement(o,n,r);var l=t.grid;l&&!l.disabled&&e.updateGridElement(l,n,r);var h=t.axisFill;h&&!h.disabled&&(e.updateFillElement(h,n,s),t.isRange||this.fillRule(t));var u=t.mask;u&&e.updateFillElement(u,n,r);var c=t.label;if(c&&!c.disabled){var d=c.location;0==d&&(d=1!=this._gridInterval.count||"week"==this._gridInterval.timeUnit||t.isRange?0:.5),e.updateLabelElement(c,n,r,d)}}},Object.defineProperty(e.prototype,"baseDuration",{get:function(){return p.getDuration(this.baseInterval.timeUnit,this.baseInterval.count)},enumerable:!0,configurable:!0}),e.prototype.adjustMinMax=function(t,e){return{min:t,max:e,step:this.baseDuration}},e.prototype.fixMin=function(t){var e=p.round(new Date(t),this.baseInterval.timeUnit,this.baseInterval.count,this.getFirstWeekDay(),this.dateFormatter.utc).getTime();return e+(p.add(new Date(e),this.baseInterval.timeUnit,this.baseInterval.count,this.dateFormatter.utc).getTime()-e)*this.startLocation},e.prototype.fixMax=function(t){var e=p.round(new Date(t),this.baseInterval.timeUnit,this.baseInterval.count,this.getFirstWeekDay(),this.dateFormatter.utc).getTime();return e+(p.add(new Date(e),this.baseInterval.timeUnit,this.baseInterval.count,this.dateFormatter.utc).getTime()-e)*this.endLocation},e.prototype.chooseInterval=function(t,e,i){var a=this.gridIntervals,r=a.getIndex(t),s=p.getDuration(r.timeUnit,r.count),o=a.length-1;if(t>=o)return n.a({},a.getIndex(o));var l=Math.ceil(e/s);return e<s&&t>0?n.a({},a.getIndex(t-1)):l<=i?n.a({},a.getIndex(t)):t+1<a.length?this.chooseInterval(t+1,e,i):n.a({},a.getIndex(t))},e.prototype.formatLabel=function(t){return this.dateFormatter.format(t)},e.prototype.dateToPosition=function(t){return this.valueToPosition(t.getTime())},e.prototype.anyToPosition=function(t){return t instanceof Date?this.dateToPosition(t):this.valueToPosition(t)},e.prototype.dateToPoint=function(t){var e=this.dateToPosition(t),i=this.renderer.positionToPoint(e),a=this.renderer.positionToAngle(e);return{x:i.x,y:i.y,angle:a}},e.prototype.anyToPoint=function(t){return t instanceof Date?this.dateToPoint(t):this.valueToPoint(t)},e.prototype.positionToDate=function(t){return new Date(this.positionToValue(t))},e.prototype.getX=function(t,e,i){var a=this.getTimeByLocation(t,e,i);return y.isNumber(a)||(a=this.baseValue),this.renderer.positionToPoint(this.valueToPosition(a)).x},e.prototype.getY=function(t,e,i){var a=this.getTimeByLocation(t,e,i),n=t.getValue("valueX","stack");return y.isNumber(a)||(a=this.baseValue),this.renderer.positionToPoint(this.valueToPosition(a+n)).y},e.prototype.getAngle=function(t,e,i,a){var n=this.getTimeByLocation(t,e,i),r=t.getValue(a,"stack");return y.isNumber(n)||(n=this.baseValue),this.positionToAngle(this.valueToPosition(n+r))},e.prototype.getTimeByLocation=function(t,e,i){if(y.hasValue(e)){y.isNumber(i)||(i=t.workingLocations[e],y.isNumber(i)||(i=0));var a=t.values[e].open,n=t.values[e].close;return y.isNumber(a)&&y.isNumber(n)?a+(n-a)*i:void 0}},e.prototype.processSeriesDataItem=function(t,e){var i,a=t.component,n=t["date"+e];if(n){i=n.getTime();var r,s=t["openDate"+e],o=this._prevSeriesTime;if(s&&(r=s.getTime()),y.isNumber(r)){var l=Math.abs(i-r);this._minDifference[a.uid]>l&&(this._minDifference[a.uid]=l)}var h=i-o;h>0&&this._minDifference[a.uid]>h&&(this._minDifference[a.uid]=h),this._prevSeriesTime=i,a._baseInterval[this.uid]?this.postProcessSeriesDataItem(t):this._baseInterval&&(a._baseInterval[this.uid]=this._baseInterval,this.postProcessSeriesDataItem(t))}},e.prototype.updateAxisBySeries=function(){t.prototype.updateAxisBySeries.call(this);var e=this.chooseInterval(0,this.minDifference,1);this.minDifference>=p.getDuration("day",27)&&"week"==e.timeUnit&&(e.timeUnit="month",e.count=1),this.minDifference>=p.getDuration("day",58)&&1==e.count&&(e.count=2),this.minDifference>=p.getDuration("day",87)&&2==e.count&&(e.count=3),this.minDifference>=p.getDuration("day",174)&&5==e.count&&(e.count=6),this.minDifference>=p.getDuration("hour",23)&&"hour"==e.timeUnit&&(e.timeUnit="day",e.count=1),this.minDifference>=p.getDuration("week",1)-p.getDuration("hour",1)&&"day"==e.timeUnit&&(e.timeUnit="week",e.count=1),this.minDifference>=p.getDuration("year",1)-p.getDuration("day",1.01)&&"month"==e.timeUnit&&(e.timeUnit="year",e.count=1),this._baseIntervalReal=e},Object.defineProperty(e.prototype,"baseInterval",{get:function(){return this._baseInterval?this._baseInterval:this._baseIntervalReal},set:function(t){JSON.stringify(this._baseInterval)!=JSON.stringify(t)&&(this._baseInterval=t,y.isNumber(t.count)||(t.count=1),this.invalidate(),this.postProcessSeriesDataItems())},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"skipEmptyPeriods",{get:function(){return this.getPropertyValue("skipEmptyPeriods")},set:function(t){if(this.setPropertyValue("skipEmptyPeriods",t)&&this.invalidateData(),t){var e=this.axisBreaks.template;e.startLine.disabled=!0,e.endLine.disabled=!0,e.fillShape.disabled=!0,e.breakSize=0}},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"tooltipDateFormat",{get:function(){return this.getPropertyValue("tooltipDateFormat")},set:function(t){this.setPropertyValue("tooltipDateFormat",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"markUnitChange",{get:function(){return this.getPropertyValue("markUnitChange")},set:function(t){this.setPropertyValue("markUnitChange",t)&&this.invalidateData()},enumerable:!0,configurable:!0}),e.prototype.getTooltipText=function(t){var e,i=this.positionToDate(t);if(i=p.round(i,this.baseInterval.timeUnit,this.baseInterval.count,this.getFirstWeekDay(),this.dateFormatter.utc),y.hasValue(this.tooltipDateFormat))e=this.dateFormatter.format(i,this.tooltipDateFormat);else{var a=this.dateFormats.getKey(this.baseInterval.timeUnit);e=a?this.dateFormatter.format(i,a):this.getPositionLabel(t)}return this.adapter.apply("getTooltipText",e)},e.prototype.roundPosition=function(t,e){var i=this.baseInterval,a=i.timeUnit,n=i.count,r=this.positionToDate(t);if(p.round(r,a,n,this.getFirstWeekDay(),this.dateFormatter.utc),e>0&&p.add(r,a,e*n,this.dateFormatter.utc),this.isInBreak(r.getTime()))for(;r.getTime()<this.max&&(p.add(r,a,n,this.dateFormatter.utc),this.isInBreak(r.getTime())););return this.dateToPosition(r)},e.prototype.getCellStartPosition=function(t){return this.roundPosition(t,0)},e.prototype.getCellEndPosition=function(t){return this.roundPosition(t,1)},e.prototype.getSeriesDataItem=function(t,e,i){var a,n=this.positionToValue(e),r=p.round(new Date(n),this.baseInterval.timeUnit,this.baseInterval.count,this.getFirstWeekDay(),this.dateFormatter.utc),s=t.dataItemsByAxis.getKey(this.uid).getKey(r.getTime().toString());!s&&i&&(a="Y"==this.axisLetter?"dateY":"dateX",s=t.dataItems.getIndex(t.dataItems.findClosestIndex(r.getTime(),function(t){return t[a]?t[a].getTime():-1/0},"any")));return s},e.prototype.getPositionLabel=function(t){var e=this.positionToDate(t);return this.dateFormatter.format(e,this.getCurrentLabelFormat())},e.prototype.getCurrentLabelFormat=function(){return this.dateFormats.getKey(this._gridInterval?this._gridInterval.timeUnit:"day")},e.prototype.initRenderer=function(){t.prototype.initRenderer.call(this);var e=this.renderer;e&&(e.ticks.template.location=0,e.grid.template.location=0,e.labels.template.location=0,e.baseGrid.disabled=!0)},Object.defineProperty(e.prototype,"basePoint",{get:function(){return{x:0,y:0}},enumerable:!0,configurable:!0}),e.prototype.zoomToDates=function(t,e,i,a){t=this.dateFormatter.parse(t),e=this.dateFormatter.parse(e),this.zoomToValues(t.getTime(),e.getTime(),i,a)},e.prototype.asIs=function(e){return"baseInterval"==e||t.prototype.asIs.call(this,e)},e.prototype.copyFrom=function(e){t.prototype.copyFrom.call(this,e),this.dateFormats=e.dateFormats,this.periodChangeDateFormats=e.periodChangeDateFormats,e._baseInterval&&(this.baseInterval=e.baseInterval)},e.prototype.showTooltipAtPosition=function(e,i){var a=this;if(i||(e=this.toAxisPosition(e)),this.snapTooltip){var n,r=p.round(this.positionToDate(e),this.baseInterval.timeUnit,1,this.getFirstWeekDay(),this.dateFormatter.utc).getTime();if(this.series.each(function(t){if(t.baseAxis==a){var i=a.getSeriesDataItem(t,e,!0);if(i){var s=void 0;t.xAxis==a&&(s=i.dateX),t.yAxis==a&&(s=i.dateY),n?Math.abs(n.getTime()-r)>Math.abs(s.getTime()-r)&&(n=s):n=s}}}),n){var s=n.getTime();n=p.round(new Date(s),this.baseInterval.timeUnit,this.baseInterval.count,this.getFirstWeekDay(),this.dateFormatter.utc),s=n.getTime(),n=new Date(n.getTime()+this.baseDuration*this.renderer.tooltipLocation),e=this.dateToPosition(n),this.series.each(function(t){var e=t.dataItemsByAxis.getKey(a.uid).getKey(s.toString()),i=t.showTooltipAtDataItem(e);i?a.chart._seriesPoints.push({series:t,point:i}):(t.tooltipText||t.tooltipHTML)&&t.hideTooltip()})}}t.prototype.showTooltipAtPosition.call(this,e,!0)},Object.defineProperty(e.prototype,"snapTooltip",{get:function(){return this.getPropertyValue("snapTooltip")},set:function(t){this.setPropertyValue("snapTooltip",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"gridInterval",{get:function(){return this._gridInterval},enumerable:!0,configurable:!0}),e.prototype.makeGap=function(t,e){var i=t.component;if(t&&e&&(!i.connect&&y.isNumber(i.autoGapCount)&&i.baseAxis==this&&t.dates["date"+this.axisLetter].getTime()-e.dates["date"+this.axisLetter].getTime()>i.autoGapCount*this.baseDuration))return!0;return!1},e}(l.a);c.b.registeredClasses.DateAxis=v,c.b.registeredClasses.DateAxisDataItem=x;var b=i("k6kv"),P=i("OXm9"),A=i("AAkI"),C=i("Uslz"),I=function(t){function e(){var e=t.call(this)||this;return e.className="CategoryAxisBreak",e.applyTheme(),e}return n.c(e,t),Object.defineProperty(e.prototype,"startPosition",{get:function(){if(this.axis)return this.axis.indexToPosition(this.adjustedStartValue)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"endPosition",{get:function(){if(this.axis)return this.axis.indexToPosition(this.adjustedEndValue)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"startCategory",{get:function(){return this.getPropertyValue("startCategory")},set:function(t){this.setPropertyValue("startCategory",t)&&this.axis&&(this.axis.invalidateDataItems(),this.axis.invalidateSeries())},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"endCategory",{get:function(){return this.getPropertyValue("endCategory")},set:function(t){this.setPropertyValue("endCategory",t)&&this.axis&&(this.axis.invalidateDataItems(),this.axis.invalidateSeries())},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"startValue",{get:function(){var t=this.getPropertyValue("startCategory");return t?this.axis.categoryToIndex(t):this.getPropertyValue("startValue")},set:function(t){this.setPropertyValue("startValue",t)&&this.axis&&(this.axis.invalidateDataItems(),this.axis.invalidateSeries())},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"endValue",{get:function(){var t=this.getPropertyValue("endCategory");return t?this.axis.categoryToIndex(t):this.getPropertyValue("endValue")},set:function(t){this.setPropertyValue("endValue",t)&&this.axis&&(this.axis.invalidateDataItems(),this.axis.invalidateSeries())},enumerable:!0,configurable:!0}),e}(C.a);c.b.registeredClasses.CategoryAxisBreak=I;var D=i("x79X"),_=function(t){function e(){var e=t.call(this)||this;return e.adapter=new D.a(e),e.seriesDataItems={},e.className="CategoryAxisDataItem",e.text="{category}",e.locations.category=0,e.locations.endCategory=1,e.applyTheme(),e}return n.c(e,t),Object.defineProperty(e.prototype,"category",{get:function(){return this.adapter.isEnabled("category")?this.adapter.apply("category",this.properties.category):this.properties.category},set:function(t){this.setProperty("category",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"endCategory",{get:function(){return this.properties.endCategory},set:function(t){this.setProperty("endCategory",t)},enumerable:!0,configurable:!0}),e}(A.b),T=function(t){function e(){var e=t.call(this)||this;return e.dataItemsByCategory=new h.a,e.className="CategoryAxis",e.axisFieldName="category",e._lastDataItem=e.createDataItem(),e._lastDataItem.component=e,e._disposers.push(e._lastDataItem),e.applyTheme(),e}return n.c(e,t),e.prototype.createDataItem=function(){return new _},e.prototype.createAxisBreak=function(){return new I},e.prototype.processSeriesDataItem=function(e,i){t.prototype.processSeriesDataItem.call(this,e,i);var a=e["category"+this.axisLetter],n=this.dataItemsByCategory.getKey(a);if(n){var r=e.component.uid,s=n.seriesDataItems[r];s||(s=[],n.seriesDataItems[r]=s),s.push(e)}},e.prototype.validateDataRange=function(){var i=this;t.prototype.validateDataRange.call(this),g.each(this._series.iterator(),function(t){if(t.xAxis instanceof e&&t.yAxis instanceof e)t.invalidateDataRange();else{var a=i.positionToIndex(i.start),n=i.positionToIndex(i.end);n>=i.dataItems.length&&n--;for(var r=t.uid,s=void 0,o=void 0,l=a;l<=n;l++){var h=i.dataItems.getIndex(l);if(h){var u=h.seriesDataItems[r];if(u)for(var c=0;c<u.length;c++){var d=u[c];if(d){var p=d.index;(!y.isNumber(s)||p<s)&&(s=p),(!y.isNumber(o)||p>o)&&(o=p)}}}}y.isNumber(s)?t.startIndex=s:t.start=i.start,y.isNumber(o)?t.endIndex=o+1:t.end=i.end,i.axisBreaks.length>0&&t.invalidateDataRange()}})},e.prototype.validate=function(){var e=this;t.prototype.validate.call(this);var i=this.dataItems.length,a=m.fitToRange(Math.floor(this.start*i-1),0,i),n=m.fitToRange(Math.ceil(this.end*i),0,i);this.renderer.invalid&&this.renderer.validate();var r=this.renderer.axisLength/this.renderer.minGridDistance,s=Math.min(this.dataItems.length,Math.ceil((n-a)/r));if(this._startIndex=Math.floor(a/s)*s,this._endIndex=Math.ceil(this.end*i),this.fixAxisBreaks(),this._startIndex==this._endIndex&&this._endIndex++,this._frequency=s,!(this.axisLength<=0)){this.maxZoomFactor=this.dataItems.length,this.dataItems.length<=0&&(this.maxZoomFactor=1),this.resetIterators(),a=m.max(0,this._startIndex-this._frequency),n=m.min(this.dataItems.length,this._endIndex+this._frequency);for(var o=0,l=0;l<a;l++){(u=this.dataItems.getIndex(l)).__disabled=!0}l=n;for(var h=this.dataItems.length;l<h;l++){(u=this.dataItems.getIndex(l)).__disabled=!0}for(l=a;l<n;l++)if(l<this.dataItems.length){var u=this.dataItems.getIndex(l);if(l/this._frequency==Math.round(l/this._frequency))this.isInBreak(l)||(this.appendDataItem(u),this.validateDataElement(u,o)),o++;else u.__disabled=!0}this.appendDataItem(this._lastDataItem),this.validateDataElement(this._lastDataItem,o+1,this.dataItems.length),this.axisBreaks.each(function(t){var i=t.adjustedStartValue,a=t.adjustedEndValue;if(m.intersect({start:i,end:a},{start:e._startIndex,end:e._endIndex}))for(var n=m.fitToRange(Math.ceil(e._frequency/t.breakSize),1,a-i),r=0,s=i;s<=a;s+=n){var o=e.dataItems.getIndex(s);e.appendDataItem(o),e.validateDataElement(o,r),r++}}),this.validateBreaks(),this.validateAxisRanges(),this.ghostLabel.invalidate(),this.renderer.invalidateLayout()}},e.prototype.validateDataElement=function(e,i,a){t.prototype.validateDataElement.call(this,e);var n=this.renderer;y.isNumber(a)||(a=this.categoryToIndex(e.category));var r=this.categoryToIndex(e.endCategory);y.isNumber(r)||(r=a);var s,o,l,h=this.indexToPosition(a,e.locations.category),u=this.indexToPosition(r,e.locations.endCategory);e.position=h,e.isRange&&(s=r,o=this.indexToPosition(a,e.locations.category),l=this.indexToPosition(s,e.locations.endCategory)),e.point=n.positionToPoint(h);var c=e.tick;c&&!c.disabled&&n.updateTickElement(c,h,u);var d=e.grid;d&&!d.disabled&&n.updateGridElement(d,h,u);var p=e.label;p&&!p.disabled&&(e.isRange&&void 0!=p.text||(e.text=e.text),n.updateLabelElement(p,h,u),(e.label.measuredWidth>this.ghostLabel.measuredWidth||e.label.measuredHeight>this.ghostLabel.measuredHeight)&&(this.ghostLabel.text=e.label.currentText));var g=e.axisFill;g&&!g.disabled&&(e.isRange||(s=a+this._frequency,o=this.indexToPosition(a,g.location),l=this.indexToPosition(s,g.location)),n.updateFillElement(g,o,l),e.isRange||this.fillRule(e,i));var m=e.mask;m&&n.updateFillElement(m,o,l)},e.prototype.disposeData=function(){this.dataItemsByCategory.clear(),t.prototype.disposeData.call(this)},e.prototype.processDataItem=function(e,i){t.prototype.processDataItem.call(this,e,i),this.dataItemsByCategory.setKey(e.category,e)},e.prototype.getDataItem=function(t){var e=t[this.dataFields.category],i=this.dataItemsByCategory.getKey(e);return i||this.dataItems.create()},e.prototype.indexToPosition=function(t,e){y.isNumber(e)||(e=.5);var i=this.startIndex,a=this.endIndex,n=this.adjustDifference(i,a),r=this.startLocation;n-=r,n-=1-this.endLocation;var s=this.axisBreaks;return g.eachContinue(s.iterator(),function(e){var n=e.adjustedStartValue,r=e.adjustedEndValue;if(t<i)return!1;if(m.intersect({start:n,end:r},{start:i,end:a})){n=Math.max(i,n),r=Math.min(a,r);var s=e.breakSize;t>r?i+=(r-n)*(1-s):t<n||(t=n+(t-n)*s)}return!0}),m.round((t+e-r-i)/n,5)},e.prototype.categoryToPosition=function(t,e){var i=this.categoryToIndex(t);return this.indexToPosition(i,e)},e.prototype.categoryToPoint=function(t,e){var i=this.categoryToPosition(t,e),a=this.renderer.positionToPoint(i),n=this.renderer.positionToAngle(i);return{x:a.x,y:a.y,angle:n}},e.prototype.anyToPoint=function(t,e){return this.categoryToPoint(t,e)},e.prototype.anyToPosition=function(t,e){return this.categoryToPosition(t,e)},e.prototype.categoryToIndex=function(t){if(y.hasValue(t)){var e=this.dataItemsByCategory.getKey(t);if(e)return e.index}},e.prototype.zoomToCategories=function(t,e){this.zoomToIndexes(this.categoryToIndex(t),this.categoryToIndex(e)+1)},e.prototype.getAnyRangePath=function(t,e,i,a){var n=this.categoryToPosition(t,i),r=this.categoryToPosition(e,a);return this.getPositionRangePath(n,r)},e.prototype.roundPosition=function(t,e){var i=this.positionToIndex(t);return this.indexToPosition(i,e)},e.prototype.getFirstSeriesDataItem=function(t,e){for(var i=0;i<t.dataItems.length;i++){var a=t.dataItems.getIndex(i);if(t.xAxis==this&&a.categoryX==e)return a;if(t.yAxis==this&&a.categoryY==e)return a}},e.prototype.getLastSeriesDataItem=function(t,e){for(var i=t.dataItems.length-1;i>=0;i--){var a=t.dataItems.getIndex(i);if(t.xAxis==this&&a.categoryX==e)return a;if(t.yAxis==this&&a.categoryY==e)return a}},e.prototype.getSeriesDataItem=function(t,e,i){var a=this;if(y.isNumber(e)){var n=this.positionToIndex(e);n>=this.dataItems.length&&n--;var r=this.dataItems.getIndex(n);if(r){var s,o=r.category,l=t.dataItems.getIndex(n);if(l){if(t.xAxis==this&&l.categoryX==o)return l;if(t.yAxis==this&&l.categoryY==o)return l}return t.dataItems.each(function(e){t.xAxis==a&&e.categoryX==o&&(s||(s=e),Math.abs(n-s.index)>Math.abs(n-e.index)&&(s=e)),t.yAxis==a&&e.categoryY==o&&(s||(s=e),Math.abs(n-s.index)>Math.abs(n-e.index)&&(s=e))}),s}}},e.prototype.getX=function(t,e,i){var a;return y.hasValue(e)&&(a=this.categoryToPosition(t.categories[e],i)),y.isNaN(a)?this.basePoint.x:this.renderer.positionToPoint(a).x},e.prototype.getY=function(t,e,i){var a;return y.hasValue(e)&&(a=this.categoryToPosition(t.categories[e],i)),y.isNaN(a)?this.basePoint.y:this.renderer.positionToPoint(a).y},e.prototype.getAngle=function(t,e,i,a){return this.positionToAngle(this.categoryToPosition(t.categories[e],i))},e.prototype.getCellStartPosition=function(t){return this.roundPosition(t,0)},e.prototype.getCellEndPosition=function(t){return this.roundPosition(t,1)},e.prototype.getTooltipText=function(t){var e=this.dataItems.getIndex(this.positionToIndex(t));if(e)return this.adapter.apply("getTooltipText",e.category)},e.prototype.positionToIndex=function(t){(t=m.round(t,10))<0&&(t=0);var e=this.startIndex,i=this.endIndex,a=i-e,n=this.axisBreaks,r=null;return g.eachContinue(n.iterator(),function(n){var s=n.startPosition,o=n.endPosition,l=n.adjustedStartValue,h=n.adjustedEndValue;l=m.max(l,e),h=m.min(h,i);var u=n.breakSize;if(a-=(h-l)*(1-u),t>o)e+=(h-l)*(1-u);else if(!(t<s)){var c=(t-s)/(o-s);return r=l+Math.round(c*(h-l)),!1}return!0}),y.isNumber(r)||(r=Math.floor(t*a+e)),r},e.prototype.positionToCategory=function(t){return this.getPositionLabel(t)},e.prototype.getPositionLabel=function(t){var e=this.dataItems.getIndex(this.positionToIndex(t));if(e)return e.category},Object.defineProperty(e.prototype,"basePoint",{get:function(){return this.renderer.positionToPoint(1)},enumerable:!0,configurable:!0}),e.prototype.initRenderer=function(){t.prototype.initRenderer.call(this),this.renderer.baseGrid.disabled=!0},Object.defineProperty(e.prototype,"frequency",{get:function(){return this._frequency},enumerable:!0,configurable:!0}),e}(A.a);c.b.registeredClasses.CategoryAxis=T,c.b.registeredClasses.CategoryAxisDataItem=_;var V=i("aM7D"),S=i("Vs7R"),F=i("hD5A"),k=i("v9UT"),O=i("hJ5i"),w=i("hGwe"),R=function(t){function e(){var e=t.call(this)||this;return e.className="XYSeriesDataItem",e.values.valueX={stack:0},e.values.valueY={stack:0},e.values.openValueX={},e.values.openValueY={},e.values.dateX={},e.values.dateY={},e.values.openDateX={},e.values.openDateY={},e.setLocation("dateX",.5,0),e.setLocation("dateY",.5,0),e.setLocation("categoryX",.5,0),e.setLocation("categoryY",.5,0),e.applyTheme(),e}return n.c(e,t),Object.defineProperty(e.prototype,"valueX",{get:function(){return this.values.valueX.value},set:function(t){this.setValue("valueX",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"valueY",{get:function(){return this.values.valueY.value},set:function(t){this.setValue("valueY",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"dateX",{get:function(){return this.getDate("dateX")},set:function(t){this.setDate("dateX",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"dateY",{get:function(){return this.getDate("dateY")},set:function(t){this.setDate("dateY",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"categoryX",{get:function(){return this.categories.categoryX},set:function(t){this.setCategory("categoryX",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"categoryY",{get:function(){return this.categories.categoryY},set:function(t){this.setCategory("categoryY",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"openValueX",{get:function(){return this.values.openValueX.value},set:function(t){this.setValue("openValueX",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"openValueY",{get:function(){return this.values.openValueY.value},set:function(t){this.setValue("openValueY",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"openDateX",{get:function(){return this.getDate("openDateX")},set:function(t){this.setDate("openDateX",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"openDateY",{get:function(){return this.getDate("openDateY")},set:function(t){this.setDate("openDateY",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"openCategoryX",{get:function(){return this.categories.openCategoryX},set:function(t){this.setProperty("openCategoryX",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"openCategoryY",{get:function(){return this.categories.openCategoryY},set:function(t){this.setProperty("openCategoryY",t)},enumerable:!0,configurable:!0}),e.prototype.getMin=function(t,e,i){var a,n=this;return y.isNumber(i)||(i=0),O.each(t,function(t){var r;r=e?n.getWorkingValue(t):n.getValue(t),((r+=i)<a||!y.isNumber(a))&&(a=r)}),a},e.prototype.getMax=function(t,e,i){var a,n=this;return y.isNumber(i)||(i=0),O.each(t,function(t){var r;r=e?n.getWorkingValue(t):n.getValue(t),((r+=i)>a||!y.isNumber(a))&&(a=r)}),a},e}(V.b),L=function(t){function e(){var e=t.call(this)||this;return e._xAxis=new F.d,e._yAxis=new F.d,e._xValueFields=[],e._yValueFields=[],e._baseInterval={},e.className="XYSeries",e.isMeasured=!1,e.cursorTooltipEnabled=!0,e.cursorHoverEnabled=!0,e.excludeFromTotal=!1,e.mainContainer.mask=new S.a,e.mainContainer.mask.setElement(e.paper.add("path")),e.stacked=!1,e.snapTooltip=!1,e.tooltip.pointerOrientation="horizontal",e.tooltip.events.on("hidden",function(){e.returnBulletDefaultState()},void 0,!1),e._disposers.push(e._xAxis),e._disposers.push(e._yAxis),e.applyTheme(),e}return n.c(e,t),e.prototype.applyInternalDefaults=function(){t.prototype.applyInternalDefaults.call(this),y.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("X/Y Series"))},e.prototype.createDataItem=function(){return new R},e.prototype.dataChangeUpdate=function(){this._tmin.clear(),this._tmax.clear(),this._smin.clear(),this._smax.clear(),this.xAxis&&this.xAxis.seriesDataChangeUpdate(this),this.yAxis&&this.yAxis.seriesDataChangeUpdate(this)},e.prototype.validateData=function(){if(this.defineFields(),this.data.length>0&&this.dataChangeUpdate(),t.prototype.validateData.call(this),this.updateItemReaderText(),!y.hasValue(this.dataFields[this._xField])||!y.hasValue(this.dataFields[this._yField]))throw Error('Data fields for series "'+(this.name?this.name:this.uid)+'" are not properly defined.')},e.prototype.processDataItem=function(e,i){try{t.prototype.processDataItem.call(this,e,i),e.events.disable(),this.xAxis.processSeriesDataItem(e,"X"),this.yAxis.processSeriesDataItem(e,"Y"),e.events.enable(),this.setInitialWorkingValues(e)}catch(t){this._chart.raiseCriticalError(t)}},e.prototype.updateDataItem=function(e){t.prototype.updateDataItem.call(this,e),this.xAxis.processSeriesDataItem(e,"X"),this.yAxis.processSeriesDataItem(e,"Y")},e.prototype.setInitialWorkingValues=function(t){},e.prototype.disposeData=function(){if(t.prototype.disposeData.call(this),this.xAxis){var e=this.dataItemsByAxis.getKey(this.xAxis.uid);e&&e.clear(),this.xAxis instanceof T&&this.clearCatAxis(this.xAxis)}if(this.yAxis){var i=this.dataItemsByAxis.getKey(this.yAxis.uid);i&&i.clear(),this.yAxis instanceof T&&this.clearCatAxis(this.yAxis)}},e.prototype.clearCatAxis=function(t){var e=this.uid;t.dataItems.each(function(t){t.seriesDataItems[e]&&(t.seriesDataItems[e]=[])})},e.prototype.defineFields=function(){var t=this.xAxis,e=this.yAxis,i=t.axisFieldName,a=i+"X",n="open"+k.capitalize(i)+"X",r=e.axisFieldName,s=r+"Y",o="open"+k.capitalize(r)+"Y";this._xField=a,this._yField=s,this.dataFields[n]&&(this._xOpenField=n),this.dataFields[o]&&(this._yOpenField=o),this.dataFields[o]||this.baseAxis!=this.yAxis||(this._yOpenField=s),this.dataFields[n]||this.baseAxis!=this.xAxis||(this._xOpenField=a),this.stacked&&this.baseAxis==this.xAxis&&(this._xOpenField=a),this.stacked&&this.baseAxis==this.yAxis&&(this._yOpenField=s),this.xAxis instanceof T&&this.yAxis instanceof T&&(this._yOpenField||(this._yOpenField=s)),this._xValueFields=[],this._yValueFields=[],this.addValueField(this.xAxis,this._xValueFields,this._xField),this.addValueField(this.xAxis,this._xValueFields,this._xOpenField),this.addValueField(this.yAxis,this._yValueFields,this._yField),this.addValueField(this.yAxis,this._yValueFields,this._yOpenField)},e.prototype.addValueField=function(t,e,i){t instanceof l.a&&y.hasValue(this.dataFields[i])&&-1==e.indexOf(i)&&e.push(i)},e.prototype.setCategoryAxisField=function(t,e){y.hasValue(this.dataFields[t])||(this.dataFields[t]=e.dataFields.category)},e.prototype.setDateAxisField=function(t,e){y.hasValue(this.dataFields[t])||(this.dataFields[t]=e.dataFields.date)},e.prototype.afterDraw=function(){t.prototype.afterDraw.call(this),this.createMask()},e.prototype.createMask=function(){if(this.mainContainer.mask){var t=this.getMaskPath();g.each(this.axisRanges.iterator(),function(e){e.axisFill.fillPath&&(e.axisFill.validate(),t+=e.axisFill.fillPath)}),this.mainContainer.mask.path=t}},e.prototype.getMaskPath=function(){return w.rectToPath({x:0,y:0,width:this.xAxis.axisLength,height:this.yAxis.axisLength})},e.prototype.getAxisField=function(t){return t==this.xAxis?this.xField:t==this.yAxis?this.yField:void 0},e.prototype.validateDataItems=function(){this.xAxis.updateAxisBySeries(),this.yAxis.updateAxisBySeries(),t.prototype.validateDataItems.call(this),this.xAxis.postProcessSeriesDataItems(),this.yAxis.postProcessSeriesDataItems()},e.prototype.validateDataRange=function(){this.xAxis.dataRangeInvalid&&this.xAxis.validateDataRange(),this.yAxis.dataRangeInvalid&&this.yAxis.validateDataRange(),t.prototype.validateDataRange.call(this)},e.prototype.validate=function(){this.xAxis.invalid&&this.xAxis.validate(),this.yAxis.invalid&&this.yAxis.validate(),this.y=this.yAxis.pixelY,this.x=this.xAxis.pixelX,this._showBullets=!0;var e=this.minBulletDistance;y.isNumber(e)&&this.baseAxis.axisLength/(this.endIndex-this.startIndex)<e&&(this._showBullets=!1),t.prototype.validate.call(this)},Object.defineProperty(e.prototype,"xAxis",{get:function(){if(this.chart){if(!this._xAxis.get()){var t=this.chart.xAxes.getIndex(0);if(!t)throw Error("There are no X axes on chart.");this.xAxis=t}return this._xAxis.get()}},set:function(t){var e=this._xAxis.get();e!=t&&(e&&(this.dataItemsByAxis.removeKey(e.uid),this._xAxis.dispose(),e.series.removeValue(this)),this._xAxis.set(t,t.registerSeries(this)),this.dataItemsByAxis.setKey(t.uid,new h.a),this.invalidateData())},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"yAxis",{get:function(){if(this.chart){if(!this._yAxis.get()){var t=this.chart.yAxes.getIndex(0);if(!t)throw Error("There are no Y axes on chart.");this.yAxis=t}return this._yAxis.get()}},set:function(t){var e=this._yAxis.get();e!=t&&(e&&(this.dataItemsByAxis.removeKey(e.uid),this._yAxis.dispose(),e.series.removeValue(this)),this._yAxis.set(t,t.registerSeries(this)),this.dataItemsByAxis.setKey(t.uid,new h.a),this.invalidateData())},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"baseAxis",{get:function(){return this._baseAxis||(this.yAxis instanceof v&&(this._baseAxis=this.yAxis),this.xAxis instanceof v&&(this._baseAxis=this.xAxis),this.yAxis instanceof T&&(this._baseAxis=this.yAxis),this.xAxis instanceof T&&(this._baseAxis=this.xAxis),this._baseAxis||(this._baseAxis=this.xAxis)),this._baseAxis},set:function(t){this._baseAxis!=t&&(this._baseAxis=t,this.invalidate())},enumerable:!0,configurable:!0}),e.prototype.processValues=function(e){t.prototype.processValues.call(this,e);var i=this.dataItems,a=1/0,n=-1/0,r=1/0,s=-1/0,o=this.startIndex,l=this.endIndex;e||(o=0,l=this.dataItems.length);for(var h=o;h<l;h++){var u=i.getIndex(h);this.getStackValue(u,e);var c=u.getValue("valueX","stack"),d=u.getValue("valueY","stack");a=m.min(u.getMin(this._xValueFields,e,c),a),r=m.min(u.getMin(this._yValueFields,e,d),r),n=m.max(u.getMax(this._xValueFields,e,c),n),s=m.max(u.getMax(this._yValueFields,e,d),s),this.stacked&&(this.baseAxis==this.xAxis&&(r=m.min(r,d)),this.baseAxis==this.yAxis&&(a=m.min(a,c)))}this.xAxis.processSeriesDataItems(),this.yAxis.processSeriesDataItems();var p=this.xAxis.uid,y=this.yAxis.uid;if(!e&&(this._tmin.getKey(p)!=a||this._tmax.getKey(p)!=n||this._tmin.getKey(y)!=r||this._tmax.getKey(y)!=s)){this._tmin.setKey(p,a),this._tmax.setKey(p,n),this._tmin.setKey(y,r),this._tmax.setKey(y,s);var g=this.stackedSeries;g&&(g.isDisposed()?this.stackedSeries=void 0:g.processValues(!1)),this.dispatchImmediately("extremeschanged")}this._smin.getKey(p)==a&&this._smax.getKey(p)==n&&this._smin.getKey(y)==r&&this._smax.getKey(y)==s||(this._smin.setKey(p,a),this._smax.setKey(p,n),this._smin.setKey(y,r),this._smax.setKey(y,s),(this.appeared||0!=this.start||1!=this.end)&&this.dispatchImmediately("selectionextremeschanged")),!e&&this.stacked&&this.processValues(!0)},e.prototype.hideTooltip=function(){t.prototype.hideTooltip.call(this),this.returnBulletDefaultState(),this._prevTooltipDataItem=void 0},e.prototype.showTooltipAtPosition=function(t,e){var i;if(this.visible&&!this.isHiding&&!this.isShowing){var a=this._xAxis.get(),n=this._yAxis.get();a==this.baseAxis&&(i=a.getSeriesDataItem(this,a.toAxisPosition(t),this.snapTooltip)),n==this.baseAxis&&(i=n.getSeriesDataItem(this,n.toAxisPosition(e),this.snapTooltip));var r=this.showTooltipAtDataItem(i);if(r)return r;if(!this.tooltipText)return}this.hideTooltip()},e.prototype.showTooltipAtDataItem=function(t){if(this.returnBulletDefaultState(t),t&&t.visible&&(this.updateLegendValue(t),this.cursorTooltipEnabled)){this.tooltipDataItem=t;var e=this.tooltipXField,i=this.tooltipYField;if(y.hasValue(t[e])&&y.hasValue(t[i])){var a=this.getPoint(t,e,i,t.locations[e],t.locations[i]);if(a){if(this.tooltipX=a.x,this.tooltipY=a.y,this._prevTooltipDataItem!=t&&(this.dispatchImmediately("tooltipshownat",{type:"tooltipshownat",target:this,dataItem:t}),this._prevTooltipDataItem=t),this.cursorHoverEnabled)try{for(var r=n.g(t.sprites),s=r.next();!s.done;s=r.next()){var o=s.value;!o.parent.visible||o.isHidden||o.__disabled||o.disabled||o.isHiding||(o.isHover=!0)}}catch(t){l={error:t}}finally{try{s&&!s.done&&(h=r.return)&&h.call(r)}finally{if(l)throw l.error}}return this.showTooltip()?k.spritePointToSvg({x:a.x,y:a.y},this):void 0}}}var l,h},e.prototype.returnBulletDefaultState=function(t){if(this._prevTooltipDataItem&&this._prevTooltipDataItem!=t)try{for(var e=n.g(this._prevTooltipDataItem.sprites),i=e.next();!i.done;i=e.next()){var a=i.value;a.isDisposed()?this._prevTooltipDataItem=void 0:a.isHover=!1}}catch(t){r={error:t}}finally{try{i&&!i.done&&(s=e.return)&&s.call(e)}finally{if(r)throw r.error}}var r,s},e.prototype.shouldCreateBullet=function(t,e){var i=e.xField;y.hasValue(i)||(i=this.xField);var a=e.yField;return y.hasValue(a)||(a=this.yField),!(this.xAxis instanceof l.a&&!t.hasValue([i])||this.yAxis instanceof l.a&&!t.hasValue([a]))},e.prototype.positionBullet=function(e){t.prototype.positionBullet.call(this,e);var i=e.dataItem,a=e.xField;y.hasValue(a)||(a=this.xField);var n=e.yField;if(y.hasValue(n)||(n=this.yField),this.xAxis instanceof l.a&&!i.hasValue([a])||this.yAxis instanceof l.a&&!i.hasValue([n]))e.visible=!1;else{var r=this.getBulletLocationX(e,a),s=this.getBulletLocationY(e,n),o=this.getPoint(i,a,n,r,s);if(o){var h=o.x,u=o.y;if(y.isNumber(e.locationX)&&this.xOpenField!=this.xField)h-=(h-this.xAxis.getX(i,this.xOpenField))*e.locationX;if(y.isNumber(e.locationY)&&this.yOpenField!=this.yField)u-=(u-this.yAxis.getY(i,this.yOpenField))*e.locationY;e.moveTo({x:h,y:u}),e.visible=!0}else e.visible=!1}},e.prototype.getBulletLocationX=function(t,e){var i=t.locationX,a=t.dataItem;return y.isNumber(i)||(i=a.workingLocations[e]),i},e.prototype.getBulletLocationY=function(t,e){var i=t.locationY,a=t.dataItem;return y.isNumber(i)||(i=a.workingLocations[e]),i},Object.defineProperty(e.prototype,"stacked",{get:function(){return this.getPropertyValue("stacked")},set:function(t){this.setPropertyValue("stacked",t,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"snapTooltip",{get:function(){return this.getPropertyValue("snapTooltip")},set:function(t){this.setPropertyValue("snapTooltip",t)},enumerable:!0,configurable:!0}),e.prototype.show=function(e){var i,a=this;this.xAxis instanceof l.a&&this.xAxis!=this.baseAxis&&(i=this._xValueFields),this.yAxis instanceof l.a&&this.yAxis!=this.baseAxis&&(i=this._yValueFields);var n,r=this.startIndex,s=this.endIndex,o=0,h=this.defaultState.transitionDuration;y.isNumber(e)&&(h=e),g.each(g.indexed(this.dataItems.iterator()),function(t){var e=t[0],l=t[1];a.sequencedInterpolation&&h>0&&(o=a.sequencedInterpolationDelay*e+h*(e-r)/(s-r)),n=l.show(h,o,i)});var u=t.prototype.show.call(this,e);return n&&!n.isFinished()&&(u=n),u},e.prototype.hide=function(e){var i,a,n=this,r=this.xAxis;r instanceof l.a&&r!=this.baseAxis&&(i=this._xValueFields,a=this.stacked||r.minZoomed<0&&r.maxZoomed>0||this.stackedSeries?0:r.min);var s=this.yAxis;s instanceof l.a&&s!=this.baseAxis&&(i=this._yValueFields,a=this.stacked||s.minZoomed<0&&s.maxZoomed>0||this.stackedSeries?0:s.min);var o=this.startIndex,h=this.endIndex,u=this.hiddenState.transitionDuration;y.isNumber(e)&&(u=e);var c,d=0;g.each(g.indexed(this.dataItems.iterator()),function(t){var e=t[0],r=t[1];0==u?r.hide(0,0,a,i):(n.sequencedInterpolation&&u>0&&(d=n.sequencedInterpolationDelay*e+u*(e-o)/(h-o)),c=r.hide(u,d,a,i))});var p=t.prototype.hide.call(this,u);return p&&!p.isFinished()&&p.delay(d),c&&!c.isFinished()&&(p=c),this.validateDataElements(),p},e.prototype.handleDataItemWorkingValueChange=function(e,i){t.prototype.handleDataItemWorkingValueChange.call(this,e,i);var a=this.baseAxis.series;g.each(a.iterator(),function(t){t.stacked&&t.invalidateProcessedData()})},e.prototype.getStackValue=function(t,e){var i=this;if(this.stacked){var a,n=this.chart,r=n.series.indexOf(this);this.xAxis!=this.baseAxis&&this.xAxis instanceof l.a&&(a=this.xField),this.yAxis!=this.baseAxis&&this.yAxis instanceof l.a&&(a=this.yField),t.setCalculatedValue(a,0,"stack"),g.eachContinue(n.series.range(0,r).backwards().iterator(),function(n){if(n.xAxis==i.xAxis&&n.yAxis==i.yAxis){n.stackedSeries=i;var r=n.dataItems.getIndex(t.index);if(r&&r.hasValue(i._xValueFields)&&r.hasValue(i._yValueFields)){var s=t.getValue(a),o=void 0;if(o=e?r.getWorkingValue(a)+r.getValue(a,"stack"):r.getValue(a)+r.getValue(a,"stack"),s>=0&&o>=0||s<0&&o<0)return t.setCalculatedValue(a,o,"stack"),!1}else if(!n.stacked)return!1}return!0})}},Object.defineProperty(e.prototype,"xField",{get:function(){return this._xField},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"yField",{get:function(){return this._yField},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"xOpenField",{get:function(){return this._xOpenField},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"yOpenField",{get:function(){return this._yOpenField},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"tooltipXField",{get:function(){return this._tooltipXField?this._tooltipXField:this._xField},set:function(t){this._tooltipXField=t},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"tooltipYField",{get:function(){return this._tooltipYField?this._tooltipYField:this._yField},set:function(t){this._tooltipYField=t},enumerable:!0,configurable:!0}),e.prototype.min=function(t){return this._tmin.getKey(t.uid)},e.prototype.max=function(t){return this._tmax.getKey(t.uid)},e.prototype.selectionMin=function(t){var e=this._smin.getKey(t.uid);return y.isNumber(e)||(e=this.min(t)),e},e.prototype.selectionMax=function(t){var e=this._smax.getKey(t.uid);return y.isNumber(e)||(e=this.max(t)),e},e.prototype.processConfig=function(e){if(e){if(y.hasValue(e.xAxis)&&y.isString(e.xAxis)&&(this.map.hasKey(e.xAxis)?e.xAxis=this.map.getKey(e.xAxis):(this.processingErrors.push("[XYSeries ("+(this.name||"unnamed")+')] No axis with id "'+e.xAxis+'" found for `xAxis`.'),delete e.xAxis)),y.hasValue(e.yAxis)&&y.isString(e.yAxis)&&(this.map.hasKey(e.yAxis)?e.yAxis=this.map.getKey(e.yAxis):(this.processingErrors.push("[XYSeries ("+(this.name||"unnamed")+')] No axis with id "'+e.yAxis+'" found for `yAxis`.'),delete e.yAxis)),y.hasValue(e.axisRanges)&&y.isArray(e.axisRanges))for(var i=0,a=e.axisRanges.length;i<a;i++){var n=e.axisRanges[i];y.hasValue(n.type)||(n.type="AxisDataItem"),y.hasValue(n.axis)&&y.isString(n.axis)&&this.map.hasKey(n.axis)?n.component=this.map.getKey(n.axis):y.hasValue(n.component)&&y.isString(n.component)&&this.map.hasKey(n.component)&&(n.component=this.map.getKey(n.component))}y.hasValue(e.dataFields)&&y.isObject(e.dataFields)||this.processingErrors.push("`dataFields` is not set for series ["+(this.name||"unnamed")+"]")}t.prototype.processConfig.call(this,e)},e.prototype.getPoint=function(t,e,i,a,n,r,s){var o=this.xAxis.getX(t,e,a),l=this.yAxis.getY(t,i,n);return{x:o=m.fitToRange(o,-2e4,2e4),y:l=m.fitToRange(l,-2e4,2e4)}},e.prototype.updateItemReaderText=function(){var t="";f.each(this.dataFields,function(e,i){t+="{"+e+"} "}),this.itemReaderText=t},Object.defineProperty(e.prototype,"cursorTooltipEnabled",{get:function(){return this.getPropertyValue("cursorTooltipEnabled")},set:function(t){this.setPropertyValue("cursorTooltipEnabled",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"cursorHoverEnabled",{get:function(){return this.getPropertyValue("cursorHoverEnabled")},set:function(t){this.setPropertyValue("cursorHoverEnabled",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"excludeFromTotal",{get:function(){return this.getPropertyValue("excludeFromTotal")},set:function(t){this.setPropertyValue("excludeFromTotal",t)},enumerable:!0,configurable:!0}),e}(V.a);c.b.registeredClasses.XYSeries=L,c.b.registeredClasses.XYSeriesDataItem=R;var X=i("zhwk"),Y=i("tjMS"),j=i("qCRI"),N=function(t){function e(){var e=t.call(this)||this;e.point={x:0,y:0},e._stick="none",e.className="Cursor",e.width=Object(Y.c)(100),e.height=Object(Y.c)(100),e.shouldClone=!1,e.hide(0),e.trackable=!0,e.clickable=!0,e.isMeasured=!1;var i=Object(X.b)();return e._disposers.push(i.body.events.on("down",e.handleCursorDown,e)),e._disposers.push(i.body.events.on("up",e.handleCursorUp,e)),e._disposers.push(i.body.events.on("track",e.handleCursorMove,e)),e.applyTheme(),e}return n.c(e,t),e.prototype.handleCursorMove=function(t){if(!(!this.interactionsEnabled||this.interactions.isTouchProtected&&t.touch)){if(("zoom"==this._generalBehavior||"pan"==this._generalBehavior)&&this.downPoint||Object(X.b)().isLocalElement(t.pointer,this.paper.svg,this.uid)){var e=k.documentPointToSprite(t.pointer.point,this);return"hard"==this._stick&&this._stickPoint&&(e=this._stickPoint),"soft"==this._stick&&this._stickPoint&&(this.fitsToBounds(e)||(e=this._stickPoint)),this.triggerMove(e),e}this.isHidden&&this.isHiding||this.hide()}},e.prototype.hideReal=function(e){if("hard"!=this._stick&&"soft"!=this._stick||!this._stickPoint)return t.prototype.hideReal.call(this,e)},e.prototype.triggerMove=function(t,e){t.x=m.round(t.x,1),t.y=m.round(t.y,1),e&&(this._stick=e),"hard"!=e&&"soft"!=e||(this._stickPoint=t),this.triggerMoveReal(t)},e.prototype.triggerMoveReal=function(t){this.point.x==t.x&&this.point.y==t.y||(this.point=t,this.invalidatePosition(),this.fitsToBounds(t)?this.show(0):this.downPoint||this.hide(0),this.visible&&(this.getPositions(),this.dispatch("cursorpositionchanged")))},e.prototype.triggerDown=function(t){this.triggerDownReal(t)},e.prototype.triggerDownReal=function(t){switch(this._generalBehavior){case"zoom":this.dispatchImmediately("zoomstarted");break;case"select":this.dispatchImmediately("selectstarted");break;case"pan":this.dispatchImmediately("panstarted"),Object(X.b)().setGlobalStyle(j.a.grabbing)}},e.prototype.triggerUp=function(t){this.triggerUpReal(t)},e.prototype.triggerUpReal=function(t){this.updatePoint(this.upPoint);var e=Object(X.b)();if(m.getDistance(this._upPointOrig,this._downPointOrig)>e.getHitOption(this.interactions,"hitTolerance")){switch(this._generalBehavior){case"zoom":this.dispatchImmediately("zoomended");break;case"select":this.dispatchImmediately("selectended");break;case"pan":this.dispatchImmediately("panended"),e.setGlobalStyle(j.a.default)}this.downPoint=void 0,this.updateSelection()}else this.dispatchImmediately("behaviorcanceled"),e.setGlobalStyle(j.a.default),this.downPoint=void 0},e.prototype.updateSelection=function(){},e.prototype.getPositions=function(){this.xPosition=this.point.x/this.innerWidth,this.yPosition=1-this.point.y/this.innerHeight},e.prototype.handleCursorDown=function(t){if(!(!this.interactionsEnabled||this.interactions.isTouchProtected&&t.touch)&&Object(X.b)().isLocalElement(t.pointer,this.paper.svg,this.uid)){var e=k.documentPointToSprite(t.pointer.point,this);this._downPointOrig={x:e.x,y:e.y},t.event.cancelable&&this.shouldPreventGestures(t.touch)&&this.fitsToBounds(e)&&t.event.preventDefault(),this.triggerMove(e),this.triggerDown(e)}},e.prototype.shouldPreventGestures=function(t){return!0},e.prototype.updatePoint=function(t){},e.prototype.handleCursorUp=function(t){if(this.interactionsEnabled&&(("zoom"==this._generalBehavior||"pan"==this._generalBehavior)&&this.downPoint||Object(X.b)().isLocalElement(t.pointer,this.paper.svg,this.uid))){var e=k.documentPointToSprite(t.pointer.point,this);this._upPointOrig={x:e.x,y:e.y},this.triggerMove(e),this.triggerUp(e)}},Object.defineProperty(e.prototype,"chart",{get:function(){return this._chart},set:function(t){this._chart=t,y.hasValue(this._chart.plotContainer)&&Object(X.b)().lockElement(this._chart.plotContainer.interactions)},enumerable:!0,configurable:!0}),e}(s.a);c.b.registeredClasses.Cursor=N;var M=i("8ZqG"),W=i("MIZb"),B=function(t){function e(){var e=t.call(this)||this;e._lineX=new F.d,e._lineY=new F.d,e._xAxis=new F.d,e._yAxis=new F.d,e.className="XYCursor",e.behavior="zoomX",e.maxPanOut=.1;var i=new W.a,a=e.createChild(S.a);a.shouldClone=!1,a.fillOpacity=.2,a.fill=i.getFor("alternativeBackground"),a.isMeasured=!1,a.interactionsEnabled=!1,e.selection=a,e._disposers.push(e.selection);var n=e.createChild(S.a);n.shouldClone=!1,n.stroke=i.getFor("grid"),n.fill=Object(M.c)(),n.strokeDasharray="3,3",n.isMeasured=!1,n.strokeOpacity=.4,n.interactionsEnabled=!1,n.y=0,e.lineX=n,e._disposers.push(e.lineX);var r=e.createChild(S.a);return r.shouldClone=!1,r.stroke=i.getFor("grid"),r.fill=Object(M.c)(),r.strokeDasharray="3,3",r.isMeasured=!1,r.strokeOpacity=.4,r.interactionsEnabled=!1,r.x=0,e.lineY=r,e._disposers.push(e.lineY),e.events.on("sizechanged",e.updateSize,e,!1),e._disposers.push(e._lineX),e._disposers.push(e._lineY),e._disposers.push(e._xAxis),e._disposers.push(e._yAxis),e.mask=e,e.applyTheme(),e}return n.c(e,t),e.prototype.updateSize=function(){this.lineX&&(this.lineX.path=w.moveTo({x:0,y:0})+w.lineTo({x:0,y:this.innerHeight})),this.lineY&&(this.lineY.path=w.moveTo({x:0,y:0})+w.lineTo({x:this.innerWidth,y:0}))},e.prototype.updateSelection=function(){if(this._usesSelection){var t=this.downPoint;if(t){var e=this.point;this.lineX&&(e.x=this.lineX.pixelX),this.lineY&&(e.y=this.lineY.pixelY);var i=this.selection,a=Math.min(e.x,t.x),n=Math.min(e.y,t.y),r=m.round(Math.abs(t.x-e.x),this._positionPrecision),s=m.round(Math.abs(t.y-e.y),this._positionPrecision);switch(this.behavior){case"zoomX":n=0,s=this.pixelHeight;break;case"zoomY":a=0,r=this.pixelWidth;break;case"selectX":n=0,s=this.pixelHeight;break;case"selectY":a=0,r=this.pixelWidth}i.x=a,i.y=n,i.path=w.rectangle(r,s),i.validatePosition()}else this.selection.hide()}},e.prototype.fixPoint=function(t){return t.x=Math.max(0,t.x),t.y=Math.max(0,t.y),t.x=Math.min(this.pixelWidth,t.x),t.y=Math.min(this.pixelHeight,t.y),t},e.prototype.triggerMoveReal=function(e){t.prototype.triggerMoveReal.call(this,e),this.snapToSeries&&!this.snapToSeries.isHidden||this.updateLinePositions(e),this.downPoint&&m.getDistance(this.downPoint,e)>3&&"pan"==this._generalBehavior&&(this.getPanningRanges(),this.dispatch("panning"))},e.prototype.updateLinePositions=function(t){t=this.fixPoint(this.point),this.lineX&&this.lineX.visible&&!this.xAxis&&(this.lineX.x=t.x),this.lineY&&this.lineY.visible&&!this.yAxis&&(this.lineY.y=t.y),this.updateSelection()},e.prototype.triggerDownReal=function(e){if(this.visible&&!this.isHiding)if(this.fitsToBounds(e)){this.downPoint={x:e.x,y:e.y},this.updatePoint(e),this.point.x=this.downPoint.x,this.point.y=this.downPoint.y;var i=this.selection,a=this.downPoint.x,n=this.downPoint.y;this._usesSelection&&(i.x=a,i.y=n,i.path="",i.show()),t.prototype.triggerDownReal.call(this,e)}else this.downPoint=void 0;else this.downPoint=void 0},e.prototype.updatePoint=function(t){this.lineX&&(t.x=this.lineX.pixelX),this.lineY&&(t.y=this.lineY.pixelY)},e.prototype.triggerUpReal=function(e){m.getDistance(this._upPointOrig,this._downPointOrig)>Object(X.b)().getHitOption(this.interactions,"hitTolerance")?this.downPoint&&(this.upPoint=e,this.updatePoint(this.upPoint),this.getRanges(),"selectX"==this.behavior||"selectY"==this.behavior||"selectXY"==this.behavior||this.selection.hide(),t.prototype.triggerUpReal.call(this,e)):(this.selection.hide(0),"pan"==this._generalBehavior&&Object(X.b)().setGlobalStyle(j.a.default));this.downPoint=void 0},e.prototype.getPanningRanges=function(){var t=m.round(this.downPoint.x/this.innerWidth,5),e=m.round(this.downPoint.y/this.innerHeight,5),i=t-m.round(this.point.x/this.innerWidth,5),a=-e+m.round(this.point.y/this.innerHeight,5);this.xRange={start:i,end:1+i},this.yRange={start:a,end:1+a},"panX"==this.behavior&&(this.yRange.start=0,this.yRange.end=1),"panY"==this.behavior&&(this.xRange.start=0,this.xRange.end=1)},e.prototype.getRanges=function(){this.lineX&&(this.upPoint.x=this.lineX.pixelX),this.lineY&&(this.upPoint.y=this.lineY.pixelY),k.used(this.selection);var t=m.round(this.downPoint.x/this.innerWidth,5),e=m.round(this.upPoint.x/this.innerWidth,5),i=m.round(this.downPoint.y/this.innerHeight,5),a=m.round(this.upPoint.y/this.innerHeight,5);this.xRange={start:m.min(t,e),end:m.max(t,e)},this.yRange={start:m.min(i,a),end:m.max(i,a)}},Object.defineProperty(e.prototype,"behavior",{get:function(){return this.getPropertyValue("behavior")},set:function(t){this.setPropertyValue("behavior",t,!0),this._usesSelection=!1,-1!=t.indexOf("zoom")&&(this._generalBehavior="zoom",this._usesSelection=!0),-1!=t.indexOf("select")&&(this._generalBehavior="select",this._usesSelection=!0),-1!=t.indexOf("pan")&&(this._generalBehavior="pan",this._usesSelection=!1)},enumerable:!0,configurable:!0}),e.prototype.shouldPreventGestures=function(t){return!(this.interactions.isTouchProtected&&t||"none"==this.behavior)},Object.defineProperty(e.prototype,"fullWidthLineX",{get:function(){return this.getPropertyValue("fullWidthLineX")},set:function(t){this.setPropertyValue("fullWidthLineX",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"fullWidthLineY",{get:function(){return this.getPropertyValue("fullWidthLineY")},set:function(t){this.setPropertyValue("fullWidthLineY",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"maxPanOut",{get:function(){return this.getPropertyValue("maxPanOut")},set:function(t){this.setPropertyValue("maxPanOut",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"xAxis",{get:function(){return this._xAxis.get()},set:function(t){this._xAxis.get()!=t&&this._xAxis.set(t,new F.c([t.tooltip.events.on("positionchanged",this.handleXTooltipPosition,this,!1)]))},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"yAxis",{get:function(){return this._yAxis.get()},set:function(t){this._yAxis.get()!=t&&this._yAxis.set(t,new F.c([t.tooltip.events.on("positionchanged",this.handleYTooltipPosition,this,!1)]))},enumerable:!0,configurable:!0}),e.prototype.handleXTooltipPosition=function(t){var e=this.xAxis.tooltip,i=k.svgPointToSprite({x:e.pixelX,y:e.pixelY},this),a=i.x;if(this.lineX&&(this.lineX.x=a,this.fitsToBounds(i)||this.hide()),this.xAxis&&this.fullWidthLineX){var n=this.xAxis.currentItemStartPoint,r=this.xAxis.currentItemEndPoint;if(n&&r){this.lineX.x=a;var s=r.x-n.x;this.lineX.path=w.rectangle(s,this.innerHeight,-s/2)}}},e.prototype.handleYTooltipPosition=function(t){var e=this.yAxis.tooltip,i=k.svgPointToSprite({x:e.pixelX,y:e.pixelY},this),a=i.y;if(this.lineY&&(this.lineY.y=a,this.fitsToBounds(i)||this.hide()),this.yAxis&&this.fullWidthLineY){var n=this.yAxis.currentItemStartPoint,r=this.yAxis.currentItemEndPoint;if(n&&r){this.lineY.y=a;var s=r.y-n.y;this.lineY.path=w.rectangle(this.innerWidth,s,0,-s/2)}}},Object.defineProperty(e.prototype,"lineX",{get:function(){return this._lineX.get()},set:function(t){t?(t.setElement(this.paper.add("path")),this._lineX.set(t,t.events.on("positionchanged",this.updateSelection,this,!1)),t.interactionsEnabled=!1,t.parent=this):this._lineX.reset()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"lineY",{get:function(){return this._lineY.get()},set:function(t){t?(t.setElement(this.paper.add("path")),this._lineY.set(t,t.events.on("positionchanged",this.updateSelection,this,!1)),t.parent=this,t.interactionsEnabled=!1):this._lineY.reset()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"selection",{get:function(){return this._selection},set:function(t){this._selection=t,t&&(t.element=this.paper.add("path"),t.parent=this)},enumerable:!0,configurable:!0}),e.prototype.processConfig=function(e){e&&(y.hasValue(e.xAxis)&&y.isString(e.xAxis)&&(this.map.hasKey(e.xAxis)?e.xAxis=this.map.getKey(e.xAxis):(this.processingErrors.push('[XYCursor] No axis with id "'+e.xAxis+'" found for `xAxis`'),delete e.xAxis)),y.hasValue(e.yAxis)&&y.isString(e.yAxis)&&(this.map.hasKey(e.yAxis)?e.yAxis=this.map.getKey(e.yAxis):(this.processingErrors.push('[XYCursor] No axis with id "'+e.yAxis+'" found for `yAxis`'),delete e.yAxis)),y.hasValue(e.snapToSeries)&&y.isString(e.snapToSeries)&&(this.map.hasKey(e.snapToSeries)?e.snapToSeries=this.map.getKey(e.snapToSeries):(this.processingErrors.push('[XYCursor] No series with id "'+e.snapToSeries+'" found for `series`'),delete e.snapToSeries))),t.prototype.processConfig.call(this,e)},Object.defineProperty(e.prototype,"snapToSeries",{get:function(){return this.getPropertyValue("snapToSeries")},set:function(t){this.setPropertyValue("snapToSeries",t)&&(this._snapToDisposer&&this._snapToDisposer.dispose(),t&&(this._snapToDisposer=t.events.on("tooltipshownat",this.handleSnap,this,!1)))},enumerable:!0,configurable:!0}),e.prototype.handleSnap=function(){var t=this.snapToSeries,e=t.tooltipY,i=t.tooltipX;this.xAxis&&this.xAxis.renderer.opposite&&(e-=this.pixelHeight),this.point={x:i,y:e},this.getPositions();var a=i,n=e;i-=this.pixelWidth,this.yAxis&&this.yAxis.renderer.opposite&&(i+=this.pixelWidth);var r=t.tooltip,s=r.animationDuration,o=r.animationEasing;t.baseAxis==t.xAxis&&t.yAxis.showTooltipAtPosition(this.yPosition),t.baseAxis==t.yAxis&&t.xAxis.showTooltipAtPosition(this.xPosition),this.lineX.animate([{property:"y",to:e}],s,o),this.lineY.animate([{property:"x",to:i}],s,o),this.xAxis||this.lineX.animate([{property:"x",to:a}],s,o),this.yAxis||this.lineY.animate([{property:"y",to:n}],s,o)},e.prototype.dispose=function(){this.hide(0),t.prototype.dispose.call(this)},e}(N);c.b.registeredClasses.XYCursor=B;var E=i("BEgH"),H=i("ISWh"),z=i("85D4"),U=function(t){function e(){var e=t.call(this)||this;e._chart=new F.d,e.className="XYChartScrollbar";var i=new W.a;e.padding(0,0,0,0);var a=e.createChild(q);a.shouldClone=!1,a.margin(0,0,0,0),a.padding(0,0,0,0),a.interactionsEnabled=!1,e._scrollbarChart=a,e._disposers.push(e._scrollbarChart),e.minHeight=60,e.minWidth=60;var n=e.createChild(S.a);n.shouldClone=!1,n.setElement(e.paper.add("path")),n.fill=i.getFor("background"),n.fillOpacity=.8,n.interactionsEnabled=!1,n.isMeasured=!1,n.toBack(),e._unselectedOverlay=n,e._disposers.push(e._unselectedOverlay),a.toBack(),e.background.cornerRadius(0,0,0,0);var r=e.thumb.background;r.cornerRadius(0,0,0,0),r.fillOpacity=0,r.fill=i.getFor("background");var s=r.states.getKey("hover");s&&(s.properties.fillOpacity=.2);var o=r.states.getKey("down");return o&&(o.properties.fillOpacity=.4),e._disposers.push(e._chart),e.applyTheme(),e}return n.c(e,t),Object.defineProperty(e.prototype,"series",{get:function(){return this._series||(this._series=new o.b,this._disposers.push(this._series.events.on("inserted",this.handleSeriesAdded,this,!1)),this._disposers.push(this._series.events.on("removed",this.handleSeriesRemoved,this,!1))),this._series},enumerable:!0,configurable:!0}),e.prototype.handleSeriesAdded=function(t){var e=t.newValue,i=this.scrollbarChart;i.zoomOutButton.disabled=!0,this.chart=e.chart;var a=!0,n=!0;g.each(this.series.iterator(),function(t){t!=e&&(t.xAxis==e.xAxis&&(a=!1),t.yAxis==e.yAxis&&(n=!1))});var r=new W.a,s=e.clone();if(a){var o=e.xAxis.clone();i.xAxes.moveValue(o),o.title.disabled=!0,o.rangeChangeDuration=0,o.id=e.uid,(l=o.renderer).ticks.template.disabled=!0,l.inside=!0,l.labels.template.inside=!0,l.line.strokeOpacity=0,l.minLabelPosition=.02,l.maxLabelPosition=.98,l.line.disabled=!0,l.axisFills.template.disabled=!0,l.baseGrid.disabled=!0,l.grid.template.strokeOpacity=.05,l.minWidth=void 0,l.minHeight=void 0,l.padding(0,0,0,0),l.chart=i,l.margin(0,0,0,0),l.labels.template.fillOpacity=.5,s.xAxis=o}if(n){var l,h=e.yAxis.clone();i.yAxes.moveValue(h),h.title.disabled=!0,h.rangeChangeDuration=0,(l=h.renderer).ticks.template.disabled=!0,l.inside=!0,l.labels.template.inside=!0,l.line.strokeOpacity=0,l.minLabelPosition=.02,l.maxLabelPosition=.98,l.line.disabled=!0,l.axisFills.template.disabled=!0,l.grid.template.stroke=r.getFor("background"),l.baseGrid.disabled=!0,l.grid.template.strokeOpacity=.05,l.minWidth=void 0,l.minHeight=void 0,l.chart=i,l.padding(0,0,0,0),l.margin(0,0,0,0),l.labels.template.fillOpacity=.5,s.yAxis=h}s.rangeChangeDuration=0,s.interpolationDuration=0,s.defaultState.transitionDuration=0,s.showOnInit=!1,this._disposers.push(s.events.on("validated",this.zoomOutAxes,this,!1)),this._disposers.push(e.events.on("datavalidated",function(){s.data!=e.data&&(s.data=e.data)},void 0,!1)),s.defaultState.properties.visible=!0,s.filters.push(new z.a),i.series.push(s),this.updateByOrientation()},e.prototype.updateByOrientation=function(){var t=this;this._scrollbarChart&&(g.each(this._scrollbarChart.xAxes.iterator(),function(e){var i=e.renderer;"vertical"==t.orientation?(i.grid.template.disabled=!0,i.labels.template.disabled=!0,i.minGridDistance=10):(i.grid.template.disabled=!1,i.labels.template.disabled=!1,i.minGridDistance=e.clonedFrom.renderer.minGridDistance)}),g.each(this._scrollbarChart.yAxes.iterator(),function(e){var i=e.renderer;"horizontal"==t.orientation?(i.grid.template.disabled=!0,i.labels.template.disabled=!0,i.minGridDistance=10):(i.grid.template.disabled=!1,i.labels.template.disabled=!1,i.minGridDistance=e.clonedFrom.renderer.minGridDistance)}))},e.prototype.handleSeriesRemoved=function(t){t.oldValue.events.off("validated",this.zoomOutAxes,this)},Object.defineProperty(e.prototype,"scrollbarChart",{get:function(){return this._scrollbarChart},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"chart",{get:function(){return this._chart.get()},set:function(t){this._chart.get()!==t&&(this._chart.set(t,t.events.on("datavalidated",this.handleDataChanged,this,!1)),this.handleDataChanged(),this._scrollbarChart.dataProvider=t)},enumerable:!0,configurable:!0}),e.prototype.handleDataChanged=function(){this.chart.data!=this.scrollbarChart.data&&(this.scrollbarChart.data=this.chart.data)},e.prototype.zoomOutAxes=function(){var t=this.scrollbarChart;g.each(t.xAxes.iterator(),function(t){t.zoom({start:0,end:1},!0,!0)}),g.each(t.yAxes.iterator(),function(t){t.zoom({start:0,end:1},!0,!0)})},e.prototype.updateThumb=function(){if(t.prototype.updateThumb.call(this),this._unselectedOverlay){var e=this.thumb,i=e.pixelX||0,a=e.pixelY||0,n=e.pixelWidth||0,r=e.pixelHeight||0,s="";"horizontal"==this.orientation?(s=w.rectToPath({x:-1,y:0,width:i,height:r}),s+=w.rectToPath({x:i+n,y:0,width:(this.pixelWidth||0)-i-n,height:r})):(s=w.rectToPath({x:0,y:0,width:n,height:a}),s+=w.rectToPath({x:0,y:a+r,width:n,height:(this.pixelHeight||0)-a-r})),this._unselectedOverlay.path=s}},e.prototype.processConfig=function(e){if(e&&y.hasValue(e.series)&&y.isArray(e.series))for(var i=0,a=e.series.length;i<a;i++){var n=e.series[i];if(y.hasValue(n)&&y.isString(n)){if(!this.map.hasKey(n))throw Error("XYChartScrollbar error: Series with id `"+n+"` does not exist.");e.series[i]=this.map.getKey(n)}}t.prototype.processConfig.call(this,e)},e}(H.a);c.b.registeredClasses.XYChartScrollbar=U;var K=i("TGuK"),G=function(t){function e(){var e=t.call(this)||this;return e.className="XYChartDataItem",e.applyTheme(),e}return n.c(e,t),e}(r.b),q=function(t){function e(){var e=t.call(this)||this;e._axisRendererX=b.a,e._axisRendererY=P.a,e._seriesPoints=[],e.className="XYChart",e.maskBullets=!0,e.arrangeTooltips=!0;var i=e.chartContainer;i.layout="vertical",e.padding(15,15,15,15);var a=i.createChild(s.a);a.shouldClone=!1,a.layout="vertical",a.width=Object(Y.c)(100),a.zIndex=1,e.topAxesContainer=a;var n=i.createChild(s.a);n.shouldClone=!1,n.layout="horizontal",n.width=Object(Y.c)(100),n.height=Object(Y.c)(100),n.zIndex=0,e.yAxesAndPlotContainer=n;var r=i.createChild(s.a);r.shouldClone=!1,r.width=Object(Y.c)(100),r.layout="vertical",r.zIndex=1,e.bottomAxesContainer=r;var o=n.createChild(s.a);o.shouldClone=!1,o.layout="horizontal",o.height=Object(Y.c)(100),o.contentAlign="right",o.events.on("transformed",e.updateXAxesMargins,e,!1),o.zIndex=1,e.leftAxesContainer=o;var l=n.createChild(s.a);l.shouldClone=!1,l.height=Object(Y.c)(100),l.width=Object(Y.c)(100),l.background.fillOpacity=0,e.plotContainer=l,e.mouseWheelBehavior="none",e._cursorContainer=l;var h=n.createChild(s.a);h.shouldClone=!1,h.layout="horizontal",h.height=Object(Y.c)(100),h.zIndex=1,h.events.on("transformed",e.updateXAxesMargins,e,!1),e.rightAxesContainer=h,e.seriesContainer.parent=l,e.bulletsContainer.parent=l;var u=l.createChild(E.a);return u.shouldClone=!1,u.align="right",u.valign="top",u.zIndex=Number.MAX_SAFE_INTEGER,u.marginTop=5,u.marginRight=5,u.hide(0),e.zoomOutButton=u,e._bulletMask=e.plotContainer,e.applyTheme(),e}return n.c(e,t),e.prototype.applyInternalDefaults=function(){t.prototype.applyInternalDefaults.call(this),this.zoomOutButton.exportable=!1,y.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("X/Y chart"))},e.prototype.draw=function(){t.prototype.draw.call(this),this.seriesContainer.toFront(),this.bulletsContainer.toFront(),this.maskBullets&&(this.bulletsContainer.mask=this._bulletMask),this.updateSeriesLegend()},e.prototype.updatePlotElements=function(){g.each(this.series.iterator(),function(t){t.invalidate()})},e.prototype.validateData=function(){0==this._parseDataFrom&&(g.each(this.xAxes.iterator(),function(t){t.dataChangeUpdate()}),g.each(this.yAxes.iterator(),function(t){t.dataChangeUpdate()}),g.each(this.series.iterator(),function(t){t.dataChangeUpdate()})),t.prototype.validateData.call(this)},e.prototype.updateXAxesMargins=function(){var t=this.leftAxesContainer.measuredWidth,e=this.rightAxesContainer.measuredWidth,i=this.bottomAxesContainer;i.paddingLeft==t&&i.paddingRight==e||(i.paddingLeft=t,i.paddingRight=e);var a=this.topAxesContainer;a.paddingLeft==t&&a.paddingRight==e||(a.paddingLeft=t,a.paddingRight=e)},e.prototype.handleXAxisChange=function(t){this.updateXAxis(t.target)},e.prototype.handleYAxisChange=function(t){this.updateYAxis(t.target)},e.prototype.processXAxis=function(t){var e=t.newValue;e.chart=this,e.renderer||(e.renderer=new this._axisRendererX,e.renderer.observe(["opposite","inside","inversed","minGridDistance"],this.handleXAxisChange,this,!1)),e.axisLetter="X",e.events.on("startendchanged",this.handleXAxisRangeChange,this,!1),e.dataProvider=this,this.updateXAxis(e.renderer),this.processAxis(e)},e.prototype.processYAxis=function(t){var e=t.newValue;e.chart=this,e.renderer||(e.renderer=new this._axisRendererY,e.renderer.observe(["opposite","inside","inversed","minGridDistance"],this.handleYAxisChange,this,!1)),e.axisLetter="Y",e.events.on("startendchanged",this.handleYAxisRangeChange,this,!1),e.dataProvider=this,this.updateYAxis(e.renderer),this.processAxis(e)},e.prototype.handleXAxisRangeChange=function(){var t=this.getCommonAxisRange(this.xAxes);this.scrollbarX&&this.zoomAxes(this.xAxes,t,!0),this.toggleZoomOutButton(),this.updateScrollbar(this.scrollbarX,t)},e.prototype.toggleZoomOutButton=function(){if(this.zoomOutButton){var t=!1;g.eachContinue(this.xAxes.iterator(),function(e){return 0==m.round(e.start,3)&&1==m.round(e.end,3)||(t=!0,!1)}),g.eachContinue(this.yAxes.iterator(),function(e){return 0==m.round(e.start,3)&&1==m.round(e.end,3)||(t=!0,!1)}),this.seriesAppeared||(t=!1),t?this.zoomOutButton.show():this.zoomOutButton.hide()}},e.prototype.seriesAppeared=function(){var t=!1;return g.each(this.series.iterator(),function(e){if(!e.appeared)return t=!1,!1}),t},e.prototype.handleYAxisRangeChange=function(){var t=this.getCommonAxisRange(this.yAxes);this.scrollbarY&&this.zoomAxes(this.yAxes,t,!0),this.toggleZoomOutButton(),this.updateScrollbar(this.scrollbarY,t)},e.prototype.updateScrollbar=function(t,e){t&&(t.skipRangeEvents(),t.start=e.start,t.end=e.end)},e.prototype.getCommonAxisRange=function(t){var e,i;return g.each(t.iterator(),function(t){var a=t.start,n=t.end;t.renderer.inversed&&(a=1-t.end,n=1-t.start),(!y.isNumber(e)||a<e)&&(e=a),(!y.isNumber(i)||n>i)&&(i=n)}),{start:e,end:i}},e.prototype.updateXAxis=function(t){var e=t.axis;t.opposite?(e.parent=this.topAxesContainer,e.toFront()):(e.parent=this.bottomAxesContainer,e.toBack()),e.renderer&&e.renderer.processRenderer()},e.prototype.updateYAxis=function(t){var e=t.axis;t.opposite?(e.parent=this.rightAxesContainer,e.toBack()):(e.parent=this.leftAxesContainer,e.toFront()),e.renderer&&e.renderer.processRenderer()},e.prototype.processAxis=function(t){var e=this;t instanceof T&&this._dataUsers.moveValue(t);var i=t.renderer;i.gridContainer.parent=this.plotContainer,i.gridContainer.toBack(),i.breakContainer.parent=this.plotContainer,i.breakContainer.toFront(),i.breakContainer.zIndex=10,t.addDisposer(new F.b(function(){e.dataUsers.removeValue(t)})),this.plotContainer.events.on("maxsizechanged",function(){e.inited&&(t.invalidateDataItems(),e.updateSeriesMasks())},t,!1)},e.prototype.updateSeriesMasks=function(){k.isIE()&&this.series.each(function(t){var e=t.mainContainer.mask;t.mainContainer.mask=void 0,t.mainContainer.mask=e})},Object.defineProperty(e.prototype,"xAxes",{get:function(){return this._xAxes||(this._xAxes=new o.b,this._xAxes.events.on("inserted",this.processXAxis,this,!1),this._xAxes.events.on("removed",this.handleAxisRemoval,this,!1)),this._xAxes},enumerable:!0,configurable:!0}),e.prototype.handleAxisRemoval=function(t){var e=t.oldValue;this.dataUsers.removeValue(e),e.autoDispose&&e.dispose()},Object.defineProperty(e.prototype,"yAxes",{get:function(){return this._yAxes||(this._yAxes=new o.b,this._yAxes.events.on("inserted",this.processYAxis,this,!1),this._yAxes.events.on("removed",this.handleAxisRemoval,this,!1)),this._yAxes},enumerable:!0,configurable:!0}),e.prototype.handleSeriesAdded=function(e){try{t.prototype.handleSeriesAdded.call(this,e);var i=e.newValue;0!=this.xAxes.length&&0!=this.yAxes.length||(c.b.removeFromInvalidComponents(i),i.dataInvalid=!1),k.used(i.xAxis),k.used(i.yAxis),void 0==i.fill&&(i.fill=this.colors.next()),void 0==i.stroke&&(i.stroke=i.fill)}catch(t){this.raiseCriticalError(t)}},Object.defineProperty(e.prototype,"cursor",{get:function(){return this._cursor},set:function(t){this._cursor!=t&&(this._cursor&&this.removeDispose(this._cursor),this._cursor=t,t&&(this._disposers.push(t),t.chart=this,t.shouldClone=!1,t.parent=this._cursorContainer,t.events.on("cursorpositionchanged",this.handleCursorPositionChange,this,!1),t.events.on("zoomstarted",this.handleCursorZoomStart,this,!1),t.events.on("zoomended",this.handleCursorZoomEnd,this,!1),t.events.on("panstarted",this.handleCursorPanStart,this,!1),t.events.on("panning",this.handleCursorPanning,this,!1),t.events.on("panended",this.handleCursorPanEnd,this,!1),t.events.on("behaviorcanceled",this.handleCursorCanceled,this,!1),t.events.on("hidden",this.handleHideCursor,this,!1),t.zIndex=Number.MAX_SAFE_INTEGER-1,this.tapToActivate&&this.setTapToActivate(this.tapToActivate)))},enumerable:!0,configurable:!0}),e.prototype.createCursor=function(){return new B},e.prototype.handleCursorPositionChange=function(){var t=this.cursor;if(t.visible&&!t.isHiding){var e=this.cursor.xPosition,i=this.cursor.yPosition;this.showSeriesTooltip({x:e,y:i});var a=void 0,n=t.snapToSeries;n&&(n.baseAxis==n.xAxis&&(a=n.yAxis),n.baseAxis==n.yAxis&&(a=n.xAxis)),this._seriesPoints=[],this.showAxisTooltip(this.xAxes,e,a),this.showAxisTooltip(this.yAxes,i,a),this.sortSeriesTooltips(this._seriesPoints)}},e.prototype.handleHideCursor=function(){this.hideObjectTooltip(this.xAxes),this.hideObjectTooltip(this.yAxes),this.hideObjectTooltip(this.series),this.updateSeriesLegend()},e.prototype.updateSeriesLegend=function(){g.each(this.series.iterator(),function(t){t.updateLegendValue()})},e.prototype.hideObjectTooltip=function(t){g.each(t.iterator(),function(t){t.hideTooltip(0)})},e.prototype.showSeriesTooltip=function(t){var e=this;if(t){var i=[];this.series.each(function(a){if(a.xAxis instanceof v&&a.xAxis.snapTooltip||a.yAxis instanceof v&&a.yAxis.snapTooltip);else{var n=a.showTooltipAtPosition(t.x,t.y);n&&(a.tooltip.setBounds({x:0,y:0,width:e.pixelWidth,height:e.pixelHeight}),i.push({series:a,point:n}))}}),this.arrangeTooltips&&this.sortSeriesTooltips(i)}else this.series.each(function(t){t.hideTooltip()})},e.prototype.sortSeriesTooltips=function(t){var e=k.spritePointToSvg({x:-.5,y:-.5},this.plotContainer),i=k.spritePointToSvg({x:this.plotContainer.pixelWidth+.5,y:this.plotContainer.pixelHeight+.5},this.plotContainer),a=0,n=[];O.each(t,function(t){var r=t.point;r&&m.isInRectangle(r,{x:e.x,y:e.y,width:i.x-e.x,height:i.y-e.y})&&(n.push({point:r,series:t.series}),a+=r.y)}),(t=n).sort(function(t,e){return t.point.y>e.point.y?1:t.point.y<e.point.y?-1:0});var r=a/t.length,s=k.svgPointToDocument({x:0,y:0},this.svgContainer.SVGContainer).y;if(t.length>0){var o=e.y,l=i.y;k.spritePointToDocument({x:0,y:o},this);var h=!1;if(r>o+(l-o)/2)for(var u=l,c=t.length-1;c>=0;c--){var d=(f=t[c].series).tooltip,p=t[c].point.y;if(d.setBounds({x:0,y:-s,width:this.pixelWidth,height:u+s}),d.invalid&&d.validate(),d.toBack(),(u=k.spritePointToSvg({x:0,y:d.label.pixelY-d.pixelY+p-d.pixelMarginTop},d).y)<-s){h=!0;break}}if(r<=o+(l-o)/2||h)for(var y=o,g=(c=0,t.length);c<g;c++){var f=t[c].series;p=t[c].point.y;(d=f.tooltip).setBounds({x:0,y:y,width:this.pixelWidth,height:l}),d.invalid&&d.validate(),d.toBack(),y=k.spritePointToSvg({x:0,y:d.label.pixelY+d.label.measuredHeight-d.pixelY+p+d.pixelMarginBottom},d).y}}},e.prototype.showAxisTooltip=function(t,e,i){var a=this;g.each(t.iterator(),function(t){t!=i&&(a.dataItems.length>0||t.dataItems.length>0)&&t.showTooltipAtPosition(e)})},e.prototype.getUpdatedRange=function(t,e){if(t){var i,a,n=t.renderer.inversed;t.renderer instanceof P.a&&(e=m.invertRange(e)),n?(m.invertRange(e),i=1-t.end,a=1-t.start):(i=t.start,a=t.end);var r=a-i;return{start:i+e.start*r,end:i+e.end*r}}},e.prototype.handleCursorZoomEnd=function(t){var e=this.cursor,i=e.behavior;if("zoomX"==i||"zoomXY"==i){var a=e.xRange;a&&this.xAxes.length>0&&((a=this.getUpdatedRange(this.xAxes.getIndex(0),a)).priority="start",this.zoomAxes(this.xAxes,a))}if("zoomY"==i||"zoomXY"==i){var n=e.yRange;n&&this.yAxes.length>0&&((n=this.getUpdatedRange(this.yAxes.getIndex(0),n)).priority="start",this.zoomAxes(this.yAxes,n))}this.handleHideCursor()},e.prototype.handleCursorPanStart=function(t){var e=this.xAxes.getIndex(0);e&&(this._panStartXRange={start:e.start,end:e.end});var i=this.yAxes.getIndex(0);i&&(this._panStartYRange={start:i.start,end:i.end})},e.prototype.handleCursorPanEnd=function(t){var e=this.cursor.behavior;if(this._panEndXRange&&("panX"==e||"panXY"==e)){var i=0;(a=this._panEndXRange).start<0&&(i=a.start),a.end>1&&(i=a.end-1),this.zoomAxes(this.xAxes,{start:a.start-i,end:a.end-i},!1,!0),this._panEndXRange=void 0,this._panStartXRange=void 0}if(this._panEndYRange&&("panY"==e||"panXY"==e)){var a;i=0;(a=this._panEndYRange).start<0&&(i=a.start),a.end>1&&(i=a.end-1),this.zoomAxes(this.yAxes,{start:a.start-i,end:a.end-i},!1,!0),this._panEndYRange=void 0,this._panStartYRange=void 0}},e.prototype.handleCursorCanceled=function(){this._panEndXRange=void 0,this._panStartXRange=void 0},e.prototype.handleCursorPanning=function(t){var e=this.cursor,i=e.behavior,a=e.maxPanOut;if(this._panStartXRange&&("panX"==i||"panXY"==i)){var n=this._panStartXRange,r=e.xRange,s=this.getCommonAxisRange(this.xAxes),o=n.end-n.start,l=r.start*(s.end-s.start),h=Math.max(-a,l+n.start),u=Math.min(l+n.end,1+a);h<=0&&(u=h+o),u>=1&&(h=u-o);var c={start:h,end:u};this._panEndXRange=c,this.zoomAxes(this.xAxes,c,!1,!1,e.maxPanOut)}if(this._panStartYRange&&("panY"==i||"panXY"==i)){n=this._panStartYRange,r=e.yRange,s=this.getCommonAxisRange(this.yAxes),o=n.end-n.start,l=r.start*(s.end-s.start),h=Math.max(-a,l+n.start),u=Math.min(l+n.end,1+a);h<=0&&(u=h+o),u>=1&&(h=u-o);c={start:h,end:u};this._panEndYRange=c,this.zoomAxes(this.yAxes,c,!1,!1,e.maxPanOut)}this.handleHideCursor()},e.prototype.handleCursorZoomStart=function(t){},Object.defineProperty(e.prototype,"scrollbarX",{get:function(){return this._scrollbarX},set:function(t){var e=this;this._scrollbarX&&this.removeDispose(this._scrollbarX),this._scrollbarX=t,t&&(this._disposers.push(t),t.parent=this.topAxesContainer,t.shouldClone=!1,t.startGrip.exportable=!1,t.endGrip.exportable=!1,t.toBack(),t.orientation="horizontal",t.events.on("rangechanged",this.handleXScrollbarChange,this,!1),t.adapter.add("positionValue",function(t){var i=e.xAxes.getIndex(0);return i&&(t.value=i.getPositionLabel(t.position)),t}))},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"scrollbarY",{get:function(){return this._scrollbarY},set:function(t){var e=this;this._scrollbarY&&this.removeDispose(this._scrollbarY),this._scrollbarY=t,t&&(this._disposers.push(t),t.parent=this.rightAxesContainer,t.startGrip.exportable=!1,t.shouldClone=!1,t.endGrip.exportable=!1,t.toFront(),t.orientation="vertical",t.events.on("rangechanged",this.handleYScrollbarChange,this,!1),t.adapter.add("positionValue",function(t){var i=e.yAxes.getIndex(0);return i&&(t.value=i.getPositionLabel(t.position)),t}))},enumerable:!0,configurable:!0}),e.prototype.handleXScrollbarChange=function(t){if(this.inited){var e=t.target,i=e.range;1==i.end&&(i.priority="end"),0==i.start&&(i.priority="start"),i=this.zoomAxes(this.xAxes,i),e.fixRange(i)}},e.prototype.handleYScrollbarChange=function(t){if(this.inited){var e=t.target,i=e.range;1==i.end&&(i.priority="end"),0==i.start&&(i.priority="start"),i=this.zoomAxes(this.yAxes,i),e.fixRange(i)}},e.prototype.zoomAxes=function(t,e,i,a,n){var r={start:0,end:1};return this.showSeriesTooltip(),this.dataInvalid||g.each(t.iterator(),function(t){t.renderer.inversed&&(e=m.invertRange(e)),t.hideTooltip(0),a&&(e.start=t.roundPosition(e.start+1e-4,0),e.end=t.roundPosition(e.end+1e-4,0));var s=t.zoom(e,i,i,n);t.renderer.inversed&&(s=m.invertRange(s)),r=s}),r},Object.defineProperty(e.prototype,"maskBullets",{get:function(){return this.getPropertyValue("maskBullets")},set:function(t){this.setPropertyValue("maskBullets",t,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"arrangeTooltips",{get:function(){return this.getPropertyValue("arrangeTooltips")},set:function(t){this.setPropertyValue("arrangeTooltips",t,!0)},enumerable:!0,configurable:!0}),e.prototype.handleWheel=function(t){var e=this.plotContainer,i=k.documentPointToSvg(t.point,this.htmlContainer,this.svgContainer.cssScale),a=k.svgPointToSprite(i,e),n=t.shift.y,r=this.getCommonAxisRange(this.xAxes),s=this.getCommonAxisRange(this.yAxes),o=this.mouseWheelBehavior;if("panX"==o||"panXY"==o){var l=r.end-r.start,h=Math.max(-0,r.start+.05*n/100),u=Math.min(r.end+.05*n/100,1);h<=0&&(u=h+l),u>=1&&(h=u-l),this.zoomAxes(this.xAxes,{start:h,end:u})}if("panY"==o||"panXY"==o){n*=-1;var c=s.end-s.start,d=Math.max(-0,s.start+.05*n/100),p=Math.min(s.end+.05*n/100,1);d<=0&&(p=d+c),p>=1&&(d=p-c),this.zoomAxes(this.yAxes,{start:d,end:p})}if("zoomX"==o||"zoomXY"==o){var y=a.x/e.maxWidth;h=Math.max(-0,r.start-.05*n/100*y);h=Math.min(h,r.start+(r.end-r.start)*y-.05*.05);u=Math.min(r.end+.05*n/100*(1-y),1);u=Math.max(u,r.start+(r.end-r.start)*y+.05*.05),this.zoomAxes(this.xAxes,{start:h,end:u})}if("zoomY"==o||"zoomXY"==o){var g=a.y/e.maxHeight;d=Math.max(-0,s.start-.05*n/100*(1-g));d=Math.min(d,s.start+(s.end-s.start)*g-.05*.05);p=Math.min(s.end+.05*n/100*g,1);p=Math.max(p,s.start+(s.end-s.start)*g+.05*.05),this.zoomAxes(this.yAxes,{start:d,end:p})}},Object.defineProperty(e.prototype,"mouseWheelBehavior",{get:function(){return this.getPropertyValue("mouseWheelBehavior")},set:function(t){this.setPropertyValue("mouseWheelBehavior",t)&&("none"!=t?(this._mouseWheelDisposer=this.plotContainer.events.on("wheel",this.handleWheel,this,!1),this._disposers.push(this._mouseWheelDisposer)):this._mouseWheelDisposer&&(this.plotContainer.wheelable=!1,this.plotContainer.hoverable=!1,this._mouseWheelDisposer.dispose()))},enumerable:!0,configurable:!0}),e.prototype.dataSourceDateFields=function(e){var i=this;return e=t.prototype.dataSourceDateFields.call(this,e),g.each(this.series.iterator(),function(t){e=i.populateDataSourceFields(e,t.dataFields,["dateX","dateY","openDateX","openDateY"])}),e},e.prototype.dataSourceNumberFields=function(e){var i=this;return e=t.prototype.dataSourceDateFields.call(this,e),g.each(this.series.iterator(),function(t){e=i.populateDataSourceFields(e,t.dataFields,["valueX","valueY","openValueX","openValueY"])}),e},e.prototype.processConfig=function(e){if(e){var i=[],a=[];if(y.hasValue(e.xAxes)&&y.isArray(e.xAxes))for(var n=0,r=e.xAxes.length;n<r;n++){if(!e.xAxes[n].type)throw Error("[XYChart error] No type set for xAxes["+n+"].");y.hasValue(e.xAxes[n].axisRanges)&&(i.push({axisRanges:e.xAxes[n].axisRanges,index:n}),delete e.xAxes[n].axisRanges)}if(y.hasValue(e.yAxes)&&y.isArray(e.yAxes))for(n=0,r=e.yAxes.length;n<r;n++){if(!e.yAxes[n].type)throw Error("[XYChart error] No type set for yAxes["+n+"].");y.hasValue(e.yAxes[n].axisRanges)&&(a.push({axisRanges:e.yAxes[n].axisRanges,index:n}),delete e.yAxes[n].axisRanges)}if(y.hasValue(e.series)&&y.isArray(e.series))for(n=0,r=e.series.length;n<r;n++)e.series[n].type=e.series[n].type||"LineSeries";if(y.hasValue(e.cursor)&&!y.hasValue(e.cursor.type)&&(e.cursor.type="XYCursor"),y.hasValue(e.scrollbarX)&&!y.hasValue(e.scrollbarX.type)&&(e.scrollbarX.type="Scrollbar"),y.hasValue(e.scrollbarY)&&!y.hasValue(e.scrollbarY.type)&&(e.scrollbarY.type="Scrollbar"),t.prototype.processConfig.call(this,e),a.length)for(n=0,r=a.length;n<r;n++)this.yAxes.getIndex(a[n].index).config={axisRanges:a[n].axisRanges};if(i.length)for(n=0,r=i.length;n<r;n++)this.xAxes.getIndex(i[n].index).config={axisRanges:i[n].axisRanges}}},e.prototype.configOrder=function(e,i){return e==i?0:"scrollbarX"==e?1:"scrollbarX"==i?-1:"scrollbarY"==e?1:"scrollbarY"==i?-1:"cursor"==e?1:"cursor"==i?-1:"series"==e?1:"series"==i?-1:t.prototype.configOrder.call(this,e,i)},e.prototype.createSeries=function(){return new L},Object.defineProperty(e.prototype,"zoomOutButton",{get:function(){return this._zoomOutButton},set:function(t){var e=this;this._zoomOutButton=t,t&&t.events.on("hit",function(){e.zoomAxes(e.xAxes,{start:0,end:1}),e.zoomAxes(e.yAxes,{start:0,end:1})},void 0,!1)},enumerable:!0,configurable:!0}),e.prototype.copyFrom=function(e){var i=this;e.xAxes.each(function(t){i.xAxes.push(t.clone())}),e.yAxes.each(function(t){i.yAxes.push(t.clone())}),t.prototype.copyFrom.call(this,e),e.cursor&&(this.cursor=e.cursor.clone()),e.scrollbarX&&(this.scrollbarX=e.scrollbarX.clone()),e.scrollbarY&&(this.scrollbarY=e.scrollbarY.clone())},e.prototype.disposeData=function(){t.prototype.disposeData.call(this);var e=this.scrollbarX;e&&e instanceof U&&e.scrollbarChart.disposeData();var i=this.scrollbarY;i&&i instanceof U&&i.scrollbarChart.disposeData(),this.xAxes.each(function(t){t.disposeData()}),this.yAxes.each(function(t){t.disposeData()})},e.prototype.addData=function(e,i){this.scrollbarX instanceof U&&this.addScrollbarData(this.scrollbarX,i),this.scrollbarY instanceof U&&this.addScrollbarData(this.scrollbarY,i),t.prototype.addData.call(this,e,i)},e.prototype.addScrollbarData=function(t,e){var i=t.scrollbarChart;i._parseDataFrom=i.data.length,i.invalidateData()},e.prototype.removeScrollbarData=function(t,e){var i=t.scrollbarChart;if(y.isNumber(e)){for(;e>0;){var a=this.dataItems.getIndex(0);a&&i.dataItems.remove(a),i.dataUsers.each(function(t){var e=t.dataItems.getIndex(0);e&&t.dataItems.remove(e)}),i._parseDataFrom--,e--}i.invalidateData()}},e.prototype.removeData=function(e){this.scrollbarX instanceof U&&this.removeScrollbarData(this.scrollbarX,e),this.scrollbarY instanceof U&&this.removeScrollbarData(this.scrollbarY,e),t.prototype.removeData.call(this,e)},e.prototype.setTapToActivate=function(e){t.prototype.setTapToActivate.call(this,e),this.cursor&&(this.cursor.interactions.isTouchProtected=e,this.plotContainer.interactions.isTouchProtected=e)},e.prototype.handleTapToActivate=function(){t.prototype.handleTapToActivate.call(this),this.cursor&&(this.cursor.interactions.isTouchProtected=!1,this.plotContainer.interactions.isTouchProtected=!1)},e.prototype.handleTapToActivateDeactivation=function(){t.prototype.handleTapToActivateDeactivation.call(this),this.cursor&&(this.cursor.interactions.isTouchProtected=!0,this.plotContainer.interactions.isTouchProtected=!0)},e}(r.a);c.b.registeredClasses.XYChart=q,K.c.push({relevant:K.b.maybeXS,state:function(t,e){if(t instanceof q&&t.scrollbarX){var i=t.states.create(e);return t.scrollbarX.states.create(e).properties.disabled=!0,i}return null}}),K.c.push({relevant:K.b.maybeXS,state:function(t,e){if(t instanceof q&&t.scrollbarY){var i=t.states.create(e);return t.scrollbarY.states.create(e).properties.disabled=!0,i}return null}});var Z=i("aFzC"),J=function(t){function e(){var e=t.call(this)||this;e.className="LineSeriesSegment",e.isMeasured=!1,e.interactionsEnabled=!1,e.layout="none";var i=e.createChild(S.a);e.fillSprite=i,i.shouldClone=!1,i.setElement(e.paper.add("path")),i.isMeasured=!1,e._disposers.push(i);var a=e.createChild(S.a);return e.strokeSprite=a,a.shouldClone=!1,a.fill=Object(M.c)(),a.setElement(e.paper.add("path")),a.isMeasured=!1,e._disposers.push(a),e}return n.c(e,t),e.prototype.drawSegment=function(t,e,i,a){if(!this.disabled)if(t.length>0&&e.length>0){var n=w.moveTo({x:t[0].x-.2,y:t[0].y-.2})+w.moveTo(t[0])+new Z.b(i,a).smooth(t);0==this.strokeOpacity||0==this.strokeSprite.strokeOpacity||(this.strokeSprite.path=n),(this.fillOpacity>0||this.fillSprite.fillOpacity>0)&&(n+=w.lineTo(e[0])+new Z.b(i,a).smooth(e),n+=w.lineTo(t[0]),n+=w.closePath(),this.fillSprite.path=n)}else this.fillSprite.path="",this.strokeSprite.path=""},e.prototype.copyFrom=function(e){t.prototype.copyFrom.call(this,e);var i=this.strokeSprite;f.copyProperties(e,i.properties,S.b),i.events.copyFrom(e.strokeSprite.events),i.fillOpacity=0;var a=this.fillSprite;f.copyProperties(e,a.properties,S.b),a.events.copyFrom(e.fillSprite.events),a.strokeOpacity=0},e}(s.a);c.b.registeredClasses.LineSeriesSegment=J;var Q=i("PTiM"),$=i("p9TX"),tt=i("GtDR"),et=i("TXRX"),it=function(t){function e(){var e=t.call(this)||this;return e.className="LineSeriesDataItem",e}return n.c(e,t),e}(R),at=function(t){function e(){var e=t.call(this)||this;return e.minDistance=.5,e.segments=new o.e(e.createSegment()),e.segments.template.applyOnClones=!0,e._disposers.push(new o.c(e.segments)),e._disposers.push(e.segments.template),e._segmentsIterator=new g.ListIterator(e.segments,function(){return e.segments.create()}),e._segmentsIterator.createNewItems=!0,e.className="LineSeries",e.strokeOpacity=1,e.fillOpacity=0,e.connect=!0,e.tensionX=1,e.tensionY=1,e.autoGapCount=1.1,e.segmentsContainer=e.mainContainer.createChild(s.a),e.segmentsContainer.isMeasured=!1,e.bulletsContainer.toFront(),e.applyTheme(),e}return n.c(e,t),e.prototype.applyInternalDefaults=function(){t.prototype.applyInternalDefaults.call(this),y.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Line Series"))},e.prototype.createSegment=function(){return new J},e.prototype.createDataItem=function(){return new it},e.prototype.setInitialWorkingValues=function(t){var e=this._yAxis.get(),i=this._xAxis.get();if(this.appeared&&this.visible){var a=this.dataItems.getIndex(t.index-1);if(t.component=this,this.baseAxis==i&&e instanceof l.a){var n=e.minZoomed;a&&(n=a.values.valueY.workingValue),t.setWorkingValue("valueY",n,0),t.setWorkingValue("valueY",t.values.valueY.value),i instanceof v&&(t.setWorkingLocation("dateX",t.locations.dateX-1,0),t.setWorkingLocation("dateX",t.locations.dateX))}if(this.baseAxis==e&&i instanceof l.a){var r=i.minZoomed;a&&(r=a.values.valueX.workingValue),t.setWorkingValue("valueX",r,0),t.setWorkingValue("valueX",t.values.valueX.value),e instanceof v&&(t.setWorkingLocation("dateY",t.locations.dateX-1,0),t.setWorkingLocation("dateY",t.locations.dateY))}}else this.baseAxis==i&&e instanceof l.a&&(i instanceof v&&t.setWorkingLocation("dateX",t.locations.dateX),i instanceof T&&t.setWorkingLocation("categoryX",t.locations.categoryX)),this.baseAxis==e&&i instanceof l.a&&(e instanceof v&&t.setWorkingLocation("dateY",t.locations.dateY),e instanceof T&&t.setWorkingLocation("categoryY",t.locations.categoryY))},e.prototype.updateLegendValue=function(e){t.prototype.updateLegendValue.call(this,e),e&&e.segment&&(this.tooltipColorSource=e.segment)},e.prototype.validate=function(){var e=this;t.prototype.validate.call(this),this._segmentsIterator.reset(),this.openSegmentWrapper(this._adjustedStartIndex),g.each(this.axisRanges.iterator(),function(t){e.openSegmentWrapper(e._adjustedStartIndex,t)}),g.each(this._segmentsIterator.iterator(),function(t){t.__disabled=!0})},e.prototype.sliceData=function(){for(var t=this.startIndex,e=this.endIndex,i=this.startIndex-1;i>=0;i--){if((n=this.dataItems.getIndex(i))&&n.hasValue(this._xValueFields)&&n.hasValue(this._yValueFields)){t=i;break}}this._adjustedStartIndex=this.findAdjustedIndex(t,["stroke","strokeWidth","strokeDasharray","strokeOpacity","fill","fillOpacity","opacity"]);i=this.endIndex;for(var a=this.dataItems.length;i<a;i++){var n;if((n=this.dataItems.getIndex(i))&&n.hasValue(this._xValueFields)&&n.hasValue(this._yValueFields)){e=i+1;break}}this._workingStartIndex=t,this._workingEndIndex=e},e.prototype.findAdjustedIndex=function(t,e){var i=this,a=this.propertyFields,n=t;return O.each(e,function(e){if(y.hasValue(a[e]))for(var r=n;r>=0;r--){var s=i.dataItems.getIndex(r);if(s&&y.hasValue(s.properties[e])){t>r&&(t=r);break}}}),t},e.prototype.openSegmentWrapper=function(t,e){var i={index:t,axisRange:e};do{i=this.openSegment(i.index,i.axisRange)}while(i)},e.prototype.openSegment=function(t,e){var i=!1,a=[];t=Math.min(t,this.dataItems.length);var n,r=Math.min(this._workingEndIndex,this.dataItems.length);this._workingEndIndex=Math.min(this._workingEndIndex,this.dataItems.length);var s=!1,o=this._segmentsIterator.getFirst();o.__disabled=!1,e?(o.parent=e.contents,f.copyProperties(e.contents,o,S.b)):(f.copyProperties(this,o,S.b),o.filters.clear(),o.parent=this.segmentsContainer);for(var l=t;l<r;l++){var h=this.dataItems.getIndex(l);if(h.segment=o,h.hasProperties&&(l==t?this.updateSegmentProperties(h.properties,o):s=this.updateSegmentProperties(h.properties,o,!0)),h.hasValue(this._xValueFields)&&h.hasValue(this._yValueFields))this.addPoints(a,h,this.xField,this.yField);else{if(l==t)continue;if(!this.connect){n=l;break}}if(n=l,this.baseAxis instanceof v){var u=this.dataItems.getIndex(l+1);if(u&&this.baseAxis.makeGap(u,h)){i=!0;break}}if(s)break}return this.closeSegment(o,a,t,n,e,i)},e.prototype.addPoints=function(t,e,i,a,n){var r=this.getPoint(e,i,a,e.workingLocations[i],e.workingLocations[a]);n||(e.point=r),t.push(r)},e.prototype.closeSegment=function(t,e,i,a,n,r){var s=[];if(this.dataFields[this._xOpenField]||this.dataFields[this._yOpenField]||this.stacked)for(var o=a;o>=i;o--){var l=this.dataItems.getIndex(o);l.hasValue(this._xValueFields)&&l.hasValue(this._yValueFields)&&this.addPoints(s,l,this.xOpenField,this.yOpenField,!0)}else{var h=this.baseAxis,u=e.length,c=this.xAxis,d=this.yAxis;u>0&&(h==c?(s.push({x:e[u-1].x,y:d.basePoint.y}),s.push({x:e[0].x,y:d.basePoint.y})):(s.push({x:c.basePoint.x,y:e[u-1].y}),s.push({x:c.basePoint.x,y:e[0].y})))}return this.drawSegment(t,e,s),r&&a++,a<this._workingEndIndex-1?{index:a,axisRange:n}:null},e.prototype.drawSegment=function(t,e,i){t.drawSegment(e,i,this.tensionX,this.tensionY)},e.prototype.updateSegmentProperties=function(t,e,i){var a=!1;return f.each(t,function(t,n){if(y.hasValue(n)){var r=e[t],s=void 0;r&&(s=r.toString?r.toString():r);var o=void 0;n&&(o=n.toString?n.toString():n),r==n||void 0!=s&&void 0!=o&&s==o||(i||(e[t]=n),a=!0)}}),a},Object.defineProperty(e.prototype,"connect",{get:function(){return this.getPropertyValue("connect")},set:function(t){this.setPropertyValue("connect",t)&&this.invalidate()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"tensionX",{get:function(){return this.getPropertyValue("tensionX")},set:function(t){this.setPropertyValue("tensionX",t,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"tensionY",{get:function(){return this.getPropertyValue("tensionY")},set:function(t){this.setPropertyValue("tensionY",t,!0)},enumerable:!0,configurable:!0}),e.prototype.createLegendMarker=function(t){var e=this,i=t.pixelWidth,a=t.pixelHeight;t.disposeChildren();var n=t.createChild(Q.a);if(n.shouldClone=!1,f.copyProperties(this,n,S.b),n.x2=i,n.y=a/2,n.visible=!0,this.fillOpacity>0){var r=t.createChild(tt.a);f.copyProperties(this,r,S.b),r.width=i,r.height=a,r.y=0,r.strokeOpacity=0,r.visible=!0,n.y=0}var o=t.dataItem;o.color=this.stroke,o.colorOrig=this.fill,g.eachContinue(this.bullets.iterator(),function(n){if(n instanceof et.a&&!n.copyToLegendMarker)return!1;var r=!1;if(n instanceof s.a&&g.each(n.children.iterator(),function(t){if(t instanceof $.a)return r=!0,!0}),!r){var o=n.clone();return o.parent=t,o.isMeasured=!0,o.tooltipText=void 0,o.x=i/2,e.fillOpacity>0?o.y=0:o.y=a/2,o.visible=!0,y.hasValue(o.fill)||(o.fill=e.fill),y.hasValue(o.stroke)||(o.stroke=e.stroke),!1}})},e.prototype.disposeData=function(){t.prototype.disposeData.call(this),this.segments.clear()},Object.defineProperty(e.prototype,"autoGapCount",{get:function(){return this.getPropertyValue("autoGapCount")},set:function(t){this.setPropertyValue("autoGapCount",t,!0)},enumerable:!0,configurable:!0}),e}(L);c.b.registeredClasses.LineSeries=at,c.b.registeredClasses.LineSeriesDataItem=it;var nt=function(t){function e(){var e=t.call(this)||this;return e.className="RadarSeriesDataItem",e.setLocation("dateX",0,0),e.setLocation("dateY",0,0),e.setLocation("categoryX",0,0),e.setLocation("categoryY",0,0),e.applyTheme(),e}return n.c(e,t),e}(it),rt=function(t){function e(){var e=t.call(this)||this;return e.className="RadarSeries",e.connectEnds=!0,e.applyTheme(),e}return n.c(e,t),e.prototype.validate=function(){this.chart.invalid&&this.chart.validate(),t.prototype.validate.call(this)},e.prototype.createDataItem=function(){return new nt},e.prototype.getPoint=function(t,e,i,a,n,r,s){r||(r="valueX"),s||(s="valueY");var o=this.yAxis.getX(t,i,n,s),l=this.yAxis.getY(t,i,n,s),h=m.getDistance({x:o,y:l});0==h&&(h=1e-5);var u=this.xAxis.getAngle(t,e,a,r),c=this.chart.startAngle,d=this.chart.endAngle;return u<c||u>d?void 0:{x:h*m.cos(u),y:h*m.sin(u)}},e.prototype.addPoints=function(t,e,i,a,n){var r=this.getPoint(e,i,a,e.locations[i],e.locations[a]);r&&t.push(r)},e.prototype.getMaskPath=function(){var t=this.yAxis.renderer;return w.arc(t.startAngle,t.endAngle-t.startAngle,t.pixelRadius,t.pixelInnerRadius)},e.prototype.drawSegment=function(e,i,a){var n=this.yAxis.renderer;this.connectEnds&&360==Math.abs(n.endAngle-n.startAngle)&&(this.dataFields[this._xOpenField]||this.dataFields[this._yOpenField]||this.stacked)&&(i.push(i[0]),a.length>0&&a.unshift(a[a.length-1])),t.prototype.drawSegment.call(this,e,i,a)},Object.defineProperty(e.prototype,"connectEnds",{get:function(){return this.getPropertyValue("connectEnds")},set:function(t){this.setPropertyValue("connectEnds",t,!0)},enumerable:!0,configurable:!0}),e}(at);c.b.registeredClasses.RadarSeries=rt,c.b.registeredClasses.RadarSeriesDataItem=nt;var st=i("FzPm"),ot=function(t){function e(){var e=t.call(this)||this;return e.className="RadarCursor",e.radius=Object(Y.c)(100),e.innerRadius=Object(Y.c)(0),e.applyTheme(),e.mask=void 0,e}return n.c(e,t),e.prototype.fitsToBounds=function(t){var e=m.getDistance(t);return e<this.truePixelRadius+1&&e>this.pixelInnerRadius-1},Object.defineProperty(e.prototype,"startAngle",{get:function(){return this.getPropertyValue("startAngle")},set:function(t){this.setPropertyValue("startAngle",t,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"endAngle",{get:function(){return this.getPropertyValue("endAngle")},set:function(t){this.setPropertyValue("endAngle",t,!0)},enumerable:!0,configurable:!0}),e.prototype.triggerMoveReal=function(e){this.xAxis&&(!this.xAxis||this.xAxis.cursorTooltipEnabled&&!this.xAxis.tooltip.disabled)||this.updateLineX(this.point),this.yAxis&&(!this.yAxis||this.yAxis.cursorTooltipEnabled&&!this.yAxis.tooltip.disabled)||this.updateLineY(this.point),this.updateSelection(),t.prototype.triggerMoveReal.call(this,e)},e.prototype.updateLineX=function(t){var e=this.pixelRadius,i=this.startAngle,a=this.endAngle,n=this.pixelInnerRadius;if(e>0&&y.isNumber(i)&&y.isNumber(a)&&y.isNumber(n)){var r=m.fitAngleToRange(m.getAngle(t),i,a),s=void 0;if(this.lineX&&this.lineX.visible){if(this.lineX.moveTo({x:0,y:0}),this.xAxis&&this.fullWidthLineX){var o=this.xAxis.currentItemStartPoint,l=this.xAxis.currentItemEndPoint;if(o&&l){var h=m.fitAngleToRange(m.getAngle(o),i,a),u=m.fitAngleToRange(m.getAngle(l),i,a)-h;i<a?u<0&&(u+=360):u>0&&(u-=360),r-=u/2,s=w.moveTo({x:n*m.cos(r),y:n*m.sin(r)})+w.lineTo({x:e*m.cos(r),y:e*m.sin(r)})+w.arcTo(r,u,e)+w.lineTo({x:n*m.cos(r+u),y:n*m.sin(r+u)})+w.arcTo(r+u,-u,n)}}s||(s=w.moveTo({x:n*m.cos(r),y:n*m.sin(r)})+w.lineTo({x:e*m.cos(r),y:e*m.sin(r)})),this.lineX.path=s}}},e.prototype.updateLineY=function(t){if(this.lineY&&this.lineY.visible){var e=this.startAngle,i=this.endAngle,a=this.truePixelRadius,n=m.fitToRange(m.getDistance(t),0,this.truePixelRadius);if(y.isNumber(n)&&y.isNumber(e)){this.lineY.moveTo({x:0,y:0});var r=void 0,s=i-e;if(this.yAxis&&this.fullWidthLineY){var o=this.yAxis.currentItemStartPoint,l=this.yAxis.currentItemEndPoint;if(o&&l){var h=m.fitToRange(m.getDistance(o),0,a);n=m.fitToRange(m.getDistance(l),0,a),r=w.moveTo({x:n*m.cos(e),y:n*m.sin(e)})+w.arcTo(e,s,n),r+=w.moveTo({x:h*m.cos(i),y:h*m.sin(i)})+w.arcTo(i,-s,h)}}r||(r=w.moveTo({x:n*m.cos(e),y:n*m.sin(e)})+w.arcTo(e,i-e,n)),this.lineY.path=r}}},e.prototype.updateSelection=function(){if(this._usesSelection){var t=this.downPoint;if(t){var e=this.point,i=this.pixelRadius,a=this.truePixelRadius,n=this.pixelInnerRadius,r=Math.min(this.startAngle,this.endAngle),s=Math.max(this.startAngle,this.endAngle),o=m.fitAngleToRange(m.getAngle(t),r,s),l=m.fitAngleToRange(m.getAngle(e),r,s),h=m.getDistance(t);if(h<a){var u=m.fitToRange(m.getDistance(e),0,a);this._prevAngle=l;var c=w.moveTo({x:0,y:0}),d=m.sin(o),p=m.cos(o),y=m.sin(l),g=m.cos(l),f=this.behavior;"zoomX"==f||"selectX"==f?c+=w.lineTo({x:i*p,y:i*d})+w.arcTo(o,l-o,i)+w.lineTo({x:n*g,y:n*y})+w.arcTo(l,o-l,n):"zoomY"==f||"selectY"==f?c=w.moveTo({x:u*m.cos(r),y:u*m.sin(r)})+w.arcTo(r,s-r,u)+w.lineTo({x:h*m.cos(s),y:h*m.sin(s)})+w.arcTo(s,r-s,h)+w.closePath():"zoomXY"==f&&(c=w.moveTo({x:u*m.cos(o),y:u*m.sin(o)})+w.arcTo(o,l-o,u)+w.lineTo({x:h*m.cos(l),y:h*m.sin(l)})+w.arcTo(l,o-l,h)+w.closePath()),this.selection.path=c}this.selection.moveTo({x:0,y:0})}}},e.prototype.getPositions=function(){if(this.chart){var t=this.pixelInnerRadius,e=this.truePixelRadius-t,i=this.startAngle,a=this.endAngle,n=(m.fitAngleToRange(m.getAngle(this.point),i,a)-i)/(a-i);this.xPosition=n,this.yPosition=m.fitToRange((m.getDistance(this.point)-t)/e,0,1)}},e.prototype.updatePoint=function(t){},e.prototype.handleXTooltipPosition=function(t){if(this.xAxis.cursorTooltipEnabled){var e=this.xAxis.tooltip;this.updateLineX(k.svgPointToSprite({x:e.pixelX,y:e.pixelY},this))}},e.prototype.handleYTooltipPosition=function(t){if(this.yAxis.cursorTooltipEnabled){var e=this.yAxis.tooltip;this.updateLineY(k.svgPointToSprite({x:e.pixelX,y:e.pixelY},this))}},e.prototype.updateLinePositions=function(t){},e.prototype.getRanges=function(){var t=this.downPoint;if(t){var e=this.upPoint;if(this.chart){var i=this.pixelRadius,a=this.startAngle,n=this.endAngle,r=m.fitAngleToRange(m.getAngle(t),this.startAngle,this.endAngle),s=m.fitAngleToRange(m.getAngle(e),this.startAngle,this.endAngle),o=m.fitToRange(m.getDistance(t),0,i),l=m.fitToRange(m.getDistance(e),0,i),h=0,u=1,c=0,d=1,p=this.behavior;if("zoomX"==p||"selectX"==p||"zoomXY"==p||"selectXY"==p){var y=n-a;h=m.round((r-a)/y,5),u=m.round((s-a)/y,5)}"zoomY"!=p&&"selectY"!=p&&"zoomXY"!=p&&"selectXY"!=p||(c=m.round(o/i,5),d=m.round(l/i,5)),this.xRange={start:Math.min(h,u),end:Math.max(h,u)},this.yRange={start:Math.min(c,d),end:Math.max(c,d)},"selectX"==this.behavior||"selectY"==this.behavior||"selectXY"==this.behavior||this.selection.hide()}}},e.prototype.updateSize=function(){},Object.defineProperty(e.prototype,"radius",{get:function(){return this.getPropertyValue("radius")},set:function(t){this.setPercentProperty("radius",t,!1,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"pixelRadius",{get:function(){return k.relativeRadiusToValue(this.radius,this.truePixelRadius)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"truePixelRadius",{get:function(){return k.relativeToValue(Object(Y.c)(100),m.min(this.innerWidth/2,this.innerHeight/2))},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"innerRadius",{get:function(){return this.getPropertyValue("innerRadius")},set:function(t){this.setPercentProperty("innerRadius",t,!1,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"pixelInnerRadius",{get:function(){var t=this.innerRadius;return t instanceof Y.a&&(t=Object(Y.c)(100*t.value*this.chart.innerRadiusModifyer)),k.relativeRadiusToValue(t,this.truePixelRadius)||0},enumerable:!0,configurable:!0}),e.prototype.fixPoint=function(t){return t},e}(B);c.b.registeredClasses.RadarCursor=ot;var lt=i("Meme"),ht=i("8EhG"),ut=function(t){function e(e){var i=t.call(this,e)||this;return i.className="AxisFillCircular",i.element=i.paper.add("path"),i.radius=Object(Y.c)(100),i.applyTheme(),i}return n.c(e,t),e.prototype.draw=function(){if(t.prototype.draw.call(this),this.axis){var e=this.axis.renderer;this.fillPath=e.getPositionRangePath(this.startPosition,this.endPosition,this.radius,y.hasValue(this.innerRadius)?this.innerRadius:e.innerRadius,this.cornerRadius),this.path=this.fillPath}},Object.defineProperty(e.prototype,"innerRadius",{get:function(){return this.getPropertyValue("innerRadius")},set:function(t){this.setPercentProperty("innerRadius",t,!0,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"radius",{get:function(){return this.getPropertyValue("radius")},set:function(t){this.setPercentProperty("radius",t,!0,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"cornerRadius",{get:function(){return this.getPropertyValue("cornerRadius")},set:function(t){this.setPropertyValue("cornerRadius",t,!0)},enumerable:!0,configurable:!0}),e}(ht.a);c.b.registeredClasses.AxisFillCircular=ut;var ct=i("AaJ4"),dt=function(t){function e(){var e=t.call(this)||this;return e.className="GridCircular",e.pixelPerfect=!1,e.applyTheme(),e}return n.c(e,t),Object.defineProperty(e.prototype,"innerRadius",{get:function(){return this.getPropertyValue("innerRadius")},set:function(t){this.setPercentProperty("innerRadius",t,!0,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"radius",{get:function(){return this.getPropertyValue("radius")},set:function(t){this.setPercentProperty("radius",t,!0,!1,10,!1)},enumerable:!0,configurable:!0}),e}(ct.a);c.b.registeredClasses.GridCircular=dt;var pt=i("IbTV"),yt=function(t){function e(){var e=t.call(this)||this;return e.pixelRadiusReal=0,e.layout="none",e.className="AxisRendererCircular",e.isMeasured=!1,e.startAngle=-90,e.endAngle=270,e.useChartAngles=!0,e.radius=Object(Y.c)(100),e.isMeasured=!1,e.grid.template.location=0,e.labels.template.location=0,e.labels.template.radius=15,e.ticks.template.location=0,e.ticks.template.pixelPerfect=!1,e.tooltipLocation=0,e.line.strokeOpacity=0,e.applyTheme(),e}return n.c(e,t),e.prototype.setAxis=function(e){var i=this;t.prototype.setAxis.call(this,e),e.isMeasured=!1;var a=e.tooltip;a.adapter.add("dx",function(t,e){var a=k.svgPointToSprite({x:e.pixelX,y:e.pixelY},i);return i.pixelRadius*Math.cos(Math.atan2(a.y,a.x))-a.x}),a.adapter.add("dy",function(t,e){var a=k.svgPointToSprite({x:e.pixelX,y:e.pixelY},i);return i.pixelRadius*Math.sin(Math.atan2(a.y,a.x))-a.y})},e.prototype.validate=function(){this.chart&&this.chart.invalid&&this.chart.validate(),t.prototype.validate.call(this)},Object.defineProperty(e.prototype,"axisLength",{get:function(){return 2*Math.PI*this.pixelRadius},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"radius",{get:function(){return this.getPropertyValue("radius")},set:function(t){this.setPercentProperty("radius",t,!1,!1,10,!1)&&this.axis&&this.axis.invalidate()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"pixelRadius",{get:function(){return k.relativeRadiusToValue(this.radius,this.pixelRadiusReal)||0},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"innerRadius",{get:function(){return this.getPropertyValue("innerRadius")},set:function(t){this.setPercentProperty("innerRadius",t,!1,!1,10,!1)&&this.axis&&this.axis.invalidate()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"useChartAngles",{get:function(){return this.getPropertyValue("useChartAngles")},set:function(t){this.setPropertyValue("useChartAngles",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"pixelInnerRadius",{get:function(){return k.relativeRadiusToValue(this.innerRadius,this.pixelRadiusReal)||0},enumerable:!0,configurable:!0}),e.prototype.positionToPoint=function(t){var e=this.positionToCoordinate(t),i=this.startAngle+(this.endAngle-this.startAngle)*e/this.axisLength;return{x:this.pixelRadius*m.cos(i),y:this.pixelRadius*m.sin(i)}},e.prototype.positionToAngle=function(t){var e,i=this.axis,a=(this.endAngle-this.startAngle)/(i.end-i.start);return e=i.renderer.inversed?this.startAngle+(i.end-t)*a:this.startAngle+(t-i.start)*a,m.round(e,3)},e.prototype.updateAxisLine=function(){var t=this.pixelRadius,e=this.startAngle,i=this.endAngle-e;this.line.path=w.moveTo({x:t*m.cos(e),y:t*m.sin(e)})+w.arcTo(e,i,t,t)},e.prototype.updateGridElement=function(t,e,i){e+=(i-e)*t.location;var a=this.positionToPoint(e);if(t.element){var n=m.DEGREES*Math.atan2(a.y,a.x),r=k.relativeRadiusToValue(y.hasValue(t.radius)?t.radius:Object(Y.c)(100),this.pixelRadius),s=k.relativeRadiusToValue(t.innerRadius,this.pixelRadius);t.zIndex=0;var o=k.relativeRadiusToValue(y.isNumber(s)?s:this.innerRadius,this.pixelRadius,!0);t.path=w.moveTo({x:o*m.cos(n),y:o*m.sin(n)})+w.lineTo({x:r*m.cos(n),y:r*m.sin(n)})}this.toggleVisibility(t,e,0,1)},e.prototype.updateTickElement=function(t,e,i){e+=(i-e)*t.location;var a=this.positionToPoint(e);if(t.element){var n=this.pixelRadius,r=m.DEGREES*Math.atan2(a.y,a.x),s=t.length;t.inside&&(s=-s),t.zIndex=1,t.path=w.moveTo({x:n*m.cos(r),y:n*m.sin(r)})+w.lineTo({x:(n+s)*m.cos(r),y:(n+s)*m.sin(r)})}this.toggleVisibility(t,e,0,1)},e.prototype.updateLabelElement=function(t,e,i,a){y.hasValue(a)||(a=t.location),e+=(i-e)*a,t.fixPosition(this.positionToAngle(e),this.pixelRadius),t.zIndex=2,this.toggleVisibility(t,e,this.minLabelPosition,this.maxLabelPosition)},e.prototype.fitsToBounds=function(t){return!0},Object.defineProperty(e.prototype,"startAngle",{get:function(){return this.getPropertyValue("startAngle")},set:function(t){this.setPropertyValue("startAngle",t)&&(this.invalidateAxisItems(),this.axis&&this.axis.invalidateSeries())},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"endAngle",{get:function(){return this.getPropertyValue("endAngle")},set:function(t){this.setPropertyValue("endAngle",t)&&(this.invalidateAxisItems(),this.axis&&this.axis.invalidateSeries())},enumerable:!0,configurable:!0}),e.prototype.getPositionRangePath=function(t,e,i,a,n){var r="";if(y.isNumber(t)&&y.isNumber(e)){y.hasValue(i)||(i=this.radius),t=m.max(t,this.axis.start),(e=m.min(e,this.axis.end))<t&&(e=t);var s=k.relativeRadiusToValue(i,this.pixelRadius),o=k.relativeRadiusToValue(a,this.pixelRadius,!0),l=this.positionToAngle(t),h=this.positionToAngle(e)-l;r=w.arc(l,h,s,o,s,n)}return r},e.prototype.createGrid=function(){return new dt},e.prototype.createFill=function(t){return new ut(t)},e.prototype.createLabel=function(){return new pt.a},e.prototype.pointToPosition=function(t){var e=m.fitAngleToRange(m.getAngle(t),this.startAngle,this.endAngle);return this.coordinateToPosition((e-this.startAngle)/360*this.axisLength)},e}(lt.a);c.b.registeredClasses.AxisRendererCircular=yt;var gt=i("Vk33"),mt=function(t){function e(){var e=t.call(this)||this;return e._chart=new F.d,e.pixelRadiusReal=0,e.className="AxisRendererRadial",e.isMeasured=!1,e.startAngle=-90,e.endAngle=270,e.minGridDistance=30,e.gridType="circles",e.axisAngle=-90,e.isMeasured=!1,e.layout="none",e.radius=Object(Y.c)(100),e.line.strokeOpacity=0,e.labels.template.horizontalCenter="middle",e._disposers.push(e._chart),e.applyTheme(),e}return n.c(e,t),e.prototype.validate=function(){this.chart&&this.chart.invalid&&this.chart.validate(),t.prototype.validate.call(this)},Object.defineProperty(e.prototype,"axisLength",{get:function(){return this.pixelRadius-this.pixelInnerRadius},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"radius",{get:function(){return this.getPropertyValue("radius")},set:function(t){this.setPercentProperty("radius",t,!1,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"pixelRadius",{get:function(){return k.relativeRadiusToValue(this.radius,this.pixelRadiusReal)||0},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"innerRadius",{get:function(){return this.getPropertyValue("innerRadius")},set:function(t){this.setPercentProperty("innerRadius",t,!1,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"pixelInnerRadius",{get:function(){return k.relativeRadiusToValue(this.innerRadius,this.pixelRadiusReal)||0},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"chart",{get:function(){return this._chart.get()},set:function(t){this._chart.set(t,null)},enumerable:!0,configurable:!0}),e.prototype.positionToPoint=function(t){var e=m.fitToRange(this.positionToCoordinate(t),0,1/0);return{x:e*m.cos(this.axisAngle),y:e*m.sin(this.axisAngle)}},e.prototype.updateAxisLine=function(){this.line.path=w.moveTo({x:this.pixelInnerRadius*m.cos(this.axisAngle),y:this.pixelInnerRadius*m.sin(this.axisAngle)})+w.lineTo({x:this.pixelRadius*m.cos(this.axisAngle),y:this.pixelRadius*m.sin(this.axisAngle)});var t=this.axis.title;t.valign="none",t.horizontalCenter="middle",t.verticalCenter="bottom",t.y=-this.axisLength/2;var e=90;this.opposite?this.inside||(e=-90):this.inside&&(e=-90),t.rotation=e},e.prototype.updateGridElement=function(t,e,i){e+=(i-e)*t.location;var a,n=this.positionToPoint(e),r=m.getDistance(n),s=this.startAngle,o=this.endAngle;if(y.isNumber(r)&&t.element){var l=this.chart,h=l.xAxes.getIndex(0),u=l.dataItems.length,c=l.series.getIndex(0);if("polygons"==this.gridType&&u>0&&c&&h&&h instanceof T){var d=h.renderer.grid.template.location,p=h.getAngle(c.dataItems.getIndex(0),"categoryX",d);a=w.moveTo({x:r*m.cos(p),y:r*m.sin(p)});for(var g=l.dataItems.length,f=1;f<g;f++)p=h.getAngle(c.dataItems.getIndex(f),"categoryX",d),a+=w.lineTo({x:r*m.cos(p),y:r*m.sin(p)});p=h.getAngle(c.dataItems.getIndex(g-1),"categoryX",h.renderer.cellEndLocation),a+=w.lineTo({x:r*m.cos(p),y:r*m.sin(p)})}else a=w.moveTo({x:r*m.cos(s),y:r*m.sin(s)})+w.arcTo(s,o-s,r,r);t.path=a}this.toggleVisibility(t,e,0,1)},e.prototype.updateLabelElement=function(t,e,i,a){y.hasValue(a)||(a=t.location),e+=(i-e)*a;var n=this.positionToPoint(e);this.positionItem(t,n),this.toggleVisibility(t,e,this.minLabelPosition,this.maxLabelPosition)},e.prototype.updateBaseGridElement=function(){},e.prototype.fitsToBounds=function(t){return!0},Object.defineProperty(e.prototype,"startAngle",{get:function(){return this.getPropertyValue("startAngle")},set:function(t){this.setPropertyValue("startAngle",t)&&this.invalidateAxisItems()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"endAngle",{get:function(){return this.getPropertyValue("endAngle")},set:function(t){this.setPropertyValue("endAngle",t)&&this.invalidateAxisItems()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"axisAngle",{get:function(){return this.getPropertyValue("axisAngle")},set:function(t){this.setPropertyValue("axisAngle",m.normalizeAngle(t)),this.invalidateAxisItems()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"gridType",{get:function(){return this.chart.xAxes.getIndex(0)instanceof T?this.getPropertyValue("gridType"):"circles"},set:function(t){this.setPropertyValue("gridType",t,!0)},enumerable:!0,configurable:!0}),e.prototype.getPositionRangePath=function(t,e){var i,a=this.pixelInnerRadius,n=this.axisLength+a,r=m.fitToRange(this.positionToCoordinate(t),a,n),s=m.fitToRange(this.positionToCoordinate(e),a,n),o=this.startAngle,l=this.endAngle-o,h=this.chart,u=h.xAxes.getIndex(0),c=h.dataItems.length,d=h.series.getIndex(0);if("polygons"==this.gridType&&c>0&&d&&u&&u instanceof T){var p=u.renderer.grid.template.location,y=u.getAngle(d.dataItems.getIndex(0),"categoryX",p);i=w.moveTo({x:s*m.cos(y),y:s*m.sin(y)});for(var g=h.dataItems.length,f=1;f<g;f++)y=u.getAngle(d.dataItems.getIndex(f),"categoryX",p),i+=w.lineTo({x:s*m.cos(y),y:s*m.sin(y)});y=u.getAngle(d.dataItems.getIndex(g-1),"categoryX",u.renderer.cellEndLocation),i+=w.lineTo({x:s*m.cos(y),y:s*m.sin(y)}),i+=w.moveTo({x:r*m.cos(y),y:r*m.sin(y)});for(f=g-1;f>=0;f--)y=u.getAngle(d.dataItems.getIndex(f),"categoryX",p),i+=w.lineTo({x:r*m.cos(y),y:r*m.sin(y)})}else i=w.arc(o,l,s,r);return i},e.prototype.updateBreakElement=function(t){var e=t.startLine,i=t.endLine,a=t.fillShape,n=t.startPoint,r=t.endPoint;e.radius=Math.abs(n.y),i.radius=Math.abs(r.y),a.radius=Math.abs(r.y),a.innerRadius=Math.abs(n.y)},e.prototype.createBreakSprites=function(t){t.startLine=new gt.a,t.endLine=new gt.a,t.fillShape=new gt.a},e.prototype.updateTooltip=function(){if(this.axis){var t=this.axisAngle;t<0&&(t+=360);var e="vertical";(t>45&&t<135||t>225&&t<315)&&(e="horizontal"),this.axis.updateTooltip(e,{x:-4e3,y:-4e3,width:8e3,height:8e3})}},e.prototype.updateTickElement=function(t,e){var i=this.positionToPoint(e);if(t.element){var a=m.normalizeAngle(this.axisAngle+90);a/90!=Math.round(a/90)?t.pixelPerfect=!1:t.pixelPerfect=!0;var n=-t.length;t.inside&&(n*=-1),t.path=w.moveTo({x:0,y:0})+w.lineTo({x:n*m.cos(a),y:n*m.sin(a)})}this.positionItem(t,i),this.toggleVisibility(t,e,0,1)},e.prototype.positionToCoordinate=function(t){var e,i=this.axis,a=i.axisFullLength,n=this.pixelInnerRadius;return e=i.renderer.inversed?(i.end-t)*a+n:(t-i.start)*a+n,m.round(e,1)},e.prototype.pointToPosition=function(t){var e=m.getDistance(t)-this.pixelInnerRadius;return this.coordinateToPosition(e)},e}(P.a);c.b.registeredClasses.AxisRendererRadial=mt;var ft=function(t){function e(){var e=t.call(this)||this;return e.className="RadarChartDataItem",e.applyTheme(),e}return n.c(e,t),e}(G),xt=function(t){function e(){var e=t.call(this)||this;e._axisRendererX=yt,e._axisRendererY=mt,e.innerRadiusModifyer=1,e.className="RadarChart",e.startAngle=-90,e.endAngle=270,e.radius=Object(Y.c)(80),e.innerRadius=0;var i=e.plotContainer.createChild(s.a);return i.shouldClone=!1,i.layout="absolute",i.align="center",i.valign="middle",e.seriesContainer.parent=i,e.radarContainer=i,e.bulletsContainer.parent=i,e._cursorContainer=i,e._bulletMask=i.createChild(st.a),e._bulletMask.shouldClone=!1,e._bulletMask.element=e.paper.add("path"),e._bulletMask.opacity=0,e.applyTheme(),e}return n.c(e,t),e.prototype.applyInternalDefaults=function(){t.prototype.applyInternalDefaults.call(this),y.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Radar chart"))},e.prototype.processAxis=function(e){t.prototype.processAxis.call(this,e);var i=e.renderer;i.gridContainer.parent=i,i.breakContainer.parent=i,e.parent=this.radarContainer,i.toBack()},e.prototype.handleXAxisRangeChange=function(){t.prototype.handleXAxisRangeChange.call(this),g.each(this.yAxes.iterator(),function(t){t.invalidate()})},e.prototype.handleYAxisRangeChange=function(){t.prototype.handleYAxisRangeChange.call(this),g.each(this.xAxes.iterator(),function(t){t.invalidate()})},e.prototype.createCursor=function(){return new ot},e.prototype.processConfig=function(e){if(e&&(y.hasValue(e.cursor)&&!y.hasValue(e.cursor.type)&&(e.cursor.type="RadarCursor"),y.hasValue(e.series)&&y.isArray(e.series)))for(var i=0,a=e.series.length;i<a;i++)e.series[i].type=e.series[i].type||"RadarSeries";t.prototype.processConfig.call(this,e)},e.prototype.beforeDraw=function(){t.prototype.beforeDraw.call(this);var e=this.plotContainer,i=m.getArcRect(this.startAngle,this.endAngle,1),a={x:0,y:0,width:0,height:0},n=e.innerWidth/i.width,r=e.innerHeight/i.height,s=this.innerRadius;if(s instanceof Y.a){var o=s.value,l=Math.min(n,r);o=Math.max(l*o,l-Math.min(e.innerHeight,e.innerWidth))/l,a=m.getArcRect(this.startAngle,this.endAngle,o),this.innerRadiusModifyer=o/s.value,s=Object(Y.c)(100*o)}i=m.getCommonRectangle([i,a]);var h=Math.min(e.innerWidth/i.width,e.innerHeight/i.height),u=2*k.relativeRadiusToValue(this.radius,h)||0,c=u/2,d=this.startAngle,p=this.endAngle;this._pixelInnerRadius=k.relativeRadiusToValue(s,c),this._bulletMask.path=w.arc(d,p-d,c,this._pixelInnerRadius),g.each(this.xAxes.iterator(),function(t){t.renderer.useChartAngles&&(t.renderer.startAngle=d,t.renderer.endAngle=p),t.width=u,t.height=u,t.renderer.pixelRadiusReal=c,t.renderer.innerRadius=s}),g.each(this.yAxes.iterator(),function(t){t.renderer.startAngle=d,t.renderer.endAngle=p,t.width=u,t.height=u,t.renderer.pixelRadiusReal=c,t.renderer.innerRadius=s});var y=this.cursor;y&&(y.width=u,y.height=u,y.startAngle=d,y.endAngle=p),this.radarContainer.definedBBox={x:c*i.x,y:c*i.y,width:c*i.width,height:c*i.height},this.radarContainer.validatePosition()},e.prototype.createSeries=function(){return new rt},Object.defineProperty(e.prototype,"startAngle",{get:function(){return this.getPropertyValue("startAngle")},set:function(t){this.setPropertyValue("startAngle",t,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"endAngle",{get:function(){return this.getPropertyValue("endAngle")},set:function(t){this.setPropertyValue("endAngle",t,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"radius",{get:function(){return this.getPropertyValue("radius")},set:function(t){this.setPercentProperty("radius",t,!0,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"pixelInnerRadius",{get:function(){return this._pixelInnerRadius},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"innerRadius",{get:function(){return this.getPropertyValue("innerRadius")},set:function(t){this.setPercentProperty("innerRadius",t,!0,!1,10,!1)},enumerable:!0,configurable:!0}),e.prototype.updateXAxis=function(t){t&&t.processRenderer()},e.prototype.updateYAxis=function(t){t&&t.processRenderer()},e}(q);c.b.registeredClasses.RadarChart=xt;var vt=i("DziZ"),bt=function(t){function e(){var e=t.call(this)||this;e._axis=new F.d,e.className="ClockHand";var i=new W.a;e.fill=i.getFor("alternativeBackground"),e.stroke=e.fill;var a=new st.a;a.radius=5,e.pin=a,e.isMeasured=!1,e.startWidth=5,e.endWidth=1,e.width=Object(Y.c)(100),e.height=Object(Y.c)(100),e.radius=Object(Y.c)(100),e.innerRadius=Object(Y.c)(0);var n=new vt.a;return e.hand=n,e._disposers.push(e._axis),e.applyTheme(),e}return n.c(e,t),e.prototype.validate=function(){t.prototype.validate.call(this);var e=this.hand;e.width=this.pixelWidth;var i=Math.max(this.startWidth,this.endWidth);if(e.height=i,e.leftSide=Object(Y.c)(this.startWidth/i*100),e.rightSide=Object(Y.c)(this.endWidth/i*100),this.axis){var a=this.axis.renderer,n=k.relativeRadiusToValue(this.innerRadius,a.pixelRadius),r=k.relativeRadiusToValue(this.radius,a.pixelRadius);e.x=n,e.y=-i/2,e.width=r-n}},Object.defineProperty(e.prototype,"pin",{get:function(){return this._pin},set:function(t){this._pin&&this.removeDispose(this._pin),t&&(this._pin=t,t.parent=this,this._disposers.push(t))},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"hand",{get:function(){return this._hand},set:function(t){this._hand&&this.removeDispose(this._hand),t&&(this._hand=t,t.parent=this,this._disposers.push(t))},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"radius",{get:function(){return this.getPropertyValue("radius")},set:function(t){this.setPercentProperty("radius",t,!0,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"innerRadius",{get:function(){return this.getPropertyValue("innerRadius")},set:function(t){this.setPercentProperty("innerRadius",t,!0,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"startWidth",{get:function(){return this.getPropertyValue("startWidth")},set:function(t){this.setPropertyValue("startWidth",t,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"endWidth",{get:function(){return this.getPropertyValue("endWidth")},set:function(t){this.setPropertyValue("endWidth",t,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"rotationDirection",{get:function(){return this.getPropertyValue("rotationDirection")},set:function(t){this.setPropertyValue("rotationDirection",t)},enumerable:!0,configurable:!0}),e.prototype.showValue=function(t,e,i){if(this._value=t,void 0!=t&&(y.isNumber(e)||(e=0),this.axis)){var a=this.axis.renderer.positionToAngle(this.axis.anyToPosition(t)),n=this.rotation;"clockWise"==this.rotationDirection&&a<n&&(this.rotation=n-360),"counterClockWise"==this.rotationDirection&&a>n&&(this.rotation=n+360),this.animate({property:"rotation",to:a},e,i)}},Object.defineProperty(e.prototype,"value",{get:function(){return this._value},set:function(t){this.showValue(t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"axis",{get:function(){return this._axis.get()},set:function(t){if(this.axis!=t&&this._axis.set(t,new F.c([t.events.on("datavalidated",this.updateValue,this,!1),t.events.on("datarangechanged",this.updateValue,this,!1),t.events.on("dataitemsvalidated",this.updateValue,this,!1),t.events.on("propertychanged",this.invalidate,this,!1)])),t){var e=t.chart;e&&(this.rotation=e.startAngle)}this.parent=t.renderer,this.zIndex=5},enumerable:!0,configurable:!0}),e.prototype.updateValue=function(){this.value=this.value},e.prototype.processConfig=function(e){e&&y.hasValue(e.axis)&&y.isString(e.axis)&&this.map.hasKey(e.axis)&&(e.axis=this.map.getKey(e.axis)),t.prototype.processConfig.call(this,e)},e}(s.a);c.b.registeredClasses.ClockHand=bt;var Pt=function(t){function e(){var e=t.call(this)||this;return e.className="GaugeChartDataItem",e.applyTheme(),e}return n.c(e,t),e}(ft),At=function(t){function e(){var e=t.call(this)||this;return e.className="GaugeChart",e.startAngle=180,e.endAngle=360,e.hands=new o.e(new bt),e.hands.events.on("inserted",e.processHand,e,!1),e._disposers.push(new o.c(e.hands)),e._disposers.push(e.hands.template),e.applyTheme(),e}return n.c(e,t),e.prototype.applyInternalDefaults=function(){t.prototype.applyInternalDefaults.call(this),y.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Gauge chart"))},e.prototype.processHand=function(t){var e=t.newValue;e.axis||(e.axis=this.xAxes.getIndex(0))},e}(xt);c.b.registeredClasses.GaugeChart=At;var Ct=i("quKg"),It=i("Puh1"),Dt=i("nPzZ"),_t=function(t){function e(){var e=t.call(this)||this;return e.className="PieSeries3DDataItem",e.values.depthValue={},e.applyTheme(),e}return n.c(e,t),Object.defineProperty(e.prototype,"depthValue",{get:function(){return this.values.depthValue.value},set:function(t){this.setValue("depthValue",t)},enumerable:!0,configurable:!0}),e}(It.b),Tt=function(t){function e(){var e=t.call(this)||this;return e.className="PieSeries3D",e.applyTheme(),e}return n.c(e,t),e.prototype.createDataItem=function(){return new _t},e.prototype.createSlice=function(){return new Dt.a},e.prototype.validateDataElement=function(e){var i=e.slice,a=this.depth;y.isNumber(a)||(a=this.chart.depth);var n=e.values.depthValue.percent;y.isNumber(n)||(n=100),i.depth=n*a/100;var r=this.angle;y.isNumber(r)||(r=this.chart.angle),i.angle=r,t.prototype.validateDataElement.call(this,e)},e.prototype.validate=function(){t.prototype.validate.call(this);for(var e=this._workingStartIndex;e<this._workingEndIndex;e++){var i=this.dataItems.getIndex(e).slice,a=i.startAngle;a>=-90&&a<90?i.toFront():a>=90&&i.toBack()}},Object.defineProperty(e.prototype,"depth",{get:function(){return this.getPropertyValue("depth")},set:function(t){this.setPropertyValue("depth",t,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"angle",{get:function(){return this.getPropertyValue("angle")},set:function(t){this.setPropertyValue("angle",t)},enumerable:!0,configurable:!0}),e.prototype.positionBullet=function(e){t.prototype.positionBullet.call(this,e);var i=e.dataItem.slice;e.y=e.pixelY-i.depth},e}(It.a);c.b.registeredClasses.PieSeries3D=Tt,c.b.registeredClasses.PieSeries3DDataItem=_t;var Vt=function(t){function e(){var e=t.call(this)||this;return e.className="PieChart3DDataItem",e.applyTheme(),e}return n.c(e,t),e}(Ct.b),St=function(t){function e(){var e=t.call(this)||this;return e.className="PieChart3D",e.depth=20,e.angle=10,e.applyTheme(),e}return n.c(e,t),Object.defineProperty(e.prototype,"depth",{get:function(){return this.getPropertyValue("depth")},set:function(t){this.setPropertyValue("depth",t)&&this.invalidateDataUsers()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"angle",{get:function(){return this.getPropertyValue("angle")},set:function(t){t=m.fitToRange(t,0,90),this.setPropertyValue("angle",t)&&this.invalidateDataUsers()},enumerable:!0,configurable:!0}),e.prototype.createSeries=function(){return new Tt},e}(Ct.a);c.b.registeredClasses.PieChart3D=St;var Ft=i("DXFp"),kt=function(t){function e(){var e=t.call(this)||this;return e.className="SlicedChartDataItem",e.applyTheme(),e}return n.c(e,t),e}(Ft.b),Ot=function(t){function e(){var e=t.call(this)||this;return e.className="SlicedChart",e.seriesContainer.layout="horizontal",e.padding(15,15,15,15),e.applyTheme(),e}return n.c(e,t),e.prototype.applyInternalDefaults=function(){t.prototype.applyInternalDefaults.call(this),y.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Sliced chart"))},e.prototype.validate=function(){t.prototype.validate.call(this)},e}(Ft.a);c.b.registeredClasses.SlicedChart=Ot,c.b.registeredClasses.SlicedChartDataItem=kt;var wt=i("VIOb"),Rt=i("wUYf"),Lt=i("MlsF"),Xt=i("3Cxr"),Yt=i("CnhP"),jt=i("uWmK"),Nt=function(t){function e(){var e=t.call(this)||this;return e.legendSettings=new jt.c,e.className="FlowDiagramNode",e.isMeasured=!1,new W.a,e.draggable=!0,e.inert=!0,e.setStateOnChildren=!0,e.events.on("positionchanged",e.invalidateLinks,e,!1),e.events.on("sizechanged",e.invalidateLinks,e,!1),e}return n.c(e,t),e.prototype.handleHit=function(t){this.isHidden||this.isHiding?this.show():this.hide()},e.prototype.show=function(e){var i=t.prototype.show.call(this,e);return this.outgoingDataItems.each(function(t){(!t.toNode||t.toNode&&!t.toNode.isHidden)&&t.setWorkingValue("value",t.getValue("value"),e)}),this.incomingDataItems.each(function(t){(!t.fromNode||t.fromNode&&!t.fromNode.isHidden)&&t.setWorkingValue("value",t.getValue("value"),e)}),i},e.prototype.hide=function(e){var i=t.prototype.hide.call(this,e);return this.outgoingDataItems.each(function(t){t.setWorkingValue("value",0,e)}),this.incomingDataItems.each(function(t){t.setWorkingValue("value",0,e)}),i},e.prototype.validate=function(){this.isDisposed()||(t.prototype.validate.call(this),this.invalidateLinks())},e.prototype.invalidateLinks=function(){var t=this;this.outgoingDataItems.each(function(e){var i=e.link;if("fromNode"==i.colorMode&&(i.fill=i.dataItem.fromNode.color),"gradient"==i.colorMode){i.fill=i.gradient,i.stroke=i.gradient;var a=i.gradient.stops.getIndex(0);a&&(a.color=t.color,i.gradient.validate())}}),this.incomingDataItems.each(function(e){var i=e.link;if("toNode"==i.colorMode&&(i.fill=i.dataItem.toNode.color),"gradient"==i.colorMode){i.fill=i.gradient,i.stroke=i.gradient;var a=i.gradient.stops.getIndex(1);a&&(a.color=t.color,i.gradient.validate())}})},Object.defineProperty(e.prototype,"incomingDataItems",{get:function(){var t=this;if(!this._incomingDataItems){var e=new o.b;e.events.on("inserted",function(){"name"==t.chart.sortBy?t._incomingSorted=g.sort(t._incomingDataItems.iterator(),function(t,e){return Rt.order(t.fromName,e.fromName)}):"value"==t.chart.sortBy?t._incomingSorted=g.sort(t._incomingDataItems.iterator(),function(t,e){return Lt.b(Xt.order(t.value,e.value))}):t._incomingSorted=t._incomingDataItems.iterator()},void 0,!1),this._incomingDataItems=e}return this._incomingDataItems},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"outgoingDataItems",{get:function(){var t=this;if(!this._outgoingDataItems){var e=new o.b;e.events.on("inserted",function(){"name"==t.chart.sortBy?t._outgoingSorted=g.sort(t._outgoingDataItems.iterator(),function(t,e){return Rt.order(t.fromName,e.fromName)}):"value"==t.chart.sortBy?t._outgoingSorted=g.sort(t._outgoingDataItems.iterator(),function(t,e){return Lt.b(Xt.order(t.value,e.value))}):t._outgoingSorted=t._outgoingDataItems.iterator()},void 0,!1),this._outgoingDataItems=e}return this._outgoingDataItems},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"name",{get:function(){return this.getPropertyValue("name")},set:function(t){this.setPropertyValue("name",t,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"total",{get:function(){return this.getPropertyValue("total")},set:function(t){this.setPropertyValue("total",t,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"totalIncoming",{get:function(){return this.getPropertyValue("totalIncoming")},set:function(t){this.setPropertyValue("totalIncoming",t,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"totalOutgoing",{get:function(){return this.getPropertyValue("totalOutgoing")},set:function(t){this.setPropertyValue("totalOutgoing",t,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"color",{get:function(){return this.getPropertyValue("color")},set:function(t){this.setColorProperty("color",t),this._background&&(this._background.fill=t),this.fill=t},enumerable:!0,configurable:!0}),e.prototype.createLegendMarker=function(t){var e=t.pixelWidth,i=t.pixelHeight;t.removeChildren();var a=t.createChild(Yt.a);a.shouldClone=!1,f.copyProperties(this,a,S.b),a.stroke=this.fill,a.copyFrom(this),a.padding(0,0,0,0),a.width=e,a.height=i;var n=t.dataItem;n.color=a.fill,n.colorOrig=a.fill},Object.defineProperty(e.prototype,"legendDataItem",{get:function(){return this._legendDataItem},set:function(t){this._legendDataItem=t,this._legendDataItem.itemContainer.deepInvalidate()},enumerable:!0,configurable:!0}),e}(s.a);c.b.registeredClasses.FlowDiagramNode=Nt;var Mt=i("sxA1"),Wt=i("jfaP"),Bt=function(t){function e(){var e=t.call(this)||this;e.className="FlowDiagramLink";var i=new W.a;return e.maskBullets=!1,e.colorMode="fromNode",e.layout="none",e.isMeasured=!1,e.startAngle=0,e.endAngle=0,e.strokeOpacity=0,e.verticalCenter="none",e.horizontalCenter="none",e.tooltipText="{fromName}→{toName}:{value.value}",e.tooltipLocation=.5,e.link=e.createChild(S.a),e.link.shouldClone=!1,e.link.setElement(e.paper.add("path")),e.link.isMeasured=!1,e.fillOpacity=.2,e.fill=i.getFor("alternativeBackground"),e.applyTheme(),e}return n.c(e,t),e.prototype.positionBullets=function(){var t=this;g.each(this.bullets.iterator(),function(e){e.parent=t.bulletsContainer,e.maxWidth=t.maxWidth,e.maxHeight=t.maxHeight,t.positionBullet(e)})},Object.defineProperty(e.prototype,"bulletsContainer",{get:function(){if(!this._bulletsContainer){var t=this.createChild(s.a);t.shouldClone=!1,t.layout="none",this._bulletsContainer=t}return this._bulletsContainer},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"bulletsMask",{get:function(){if(!this._bulletsMask){var t=this.createChild(S.a);t.shouldClone=!1,t.setElement(this.paper.add("path")),t.isMeasured=!1,this._bulletsMask=t}return this._bulletsMask},enumerable:!0,configurable:!0}),e.prototype.positionBullet=function(t){var e=t.locationX;y.isNumber(e)||(e=t.locationY),y.isNumber(e)||(e=.5);var i=this.middleLine.positionToPoint(e);t.moveTo(i);var a,n=t.propertyFields.rotation;t.dataItem&&(a=t.dataItem.dataContext[n]);y.isNumber(a)||(a=i.angle),t.rotation=a},Object.defineProperty(e.prototype,"startAngle",{get:function(){return this.getPropertyValue("startAngle")},set:function(t){this.setPropertyValue("startAngle",t,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"endAngle",{get:function(){return this.getPropertyValue("endAngle")},set:function(t){this.setPropertyValue("endAngle",t,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"colorMode",{get:function(){return this.getPropertyValue("colorMode")},set:function(t){if("gradient"==t){var e=this.fill;this.gradient.stops.clear(),e instanceof M.a&&(this.gradient.addColor(e),this.gradient.addColor(e)),this.fill=this.gradient,this.stroke=this.gradient}this.setPropertyValue("colorMode",t,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"maskBullets",{get:function(){return this.getPropertyValue("maskBullets")},set:function(t){this.setPropertyValue("maskBullets",t,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"tooltipLocation",{get:function(){return this.getPropertyValue("tooltipLocation")},set:function(t){this.setPropertyValue("tooltipLocation",t,!0)},enumerable:!0,configurable:!0}),e.prototype.setFill=function(e){t.prototype.setFill.call(this,e);var i=this._gradient;i&&e instanceof M.a&&(i.stops.clear(),i.addColor(e),i.addColor(e))},e.prototype.measureElement=function(){},Object.defineProperty(e.prototype,"bullets",{get:function(){var t=this;return this._bullets||(this._bullets=new o.e(new et.a),this._disposers.push(new o.c(this._bullets)),this._disposers.push(this._bullets.template),this._bullets.events.on("inserted",function(e){e.newValue.events.on("propertychanged",function(e){"locationX"!=e.property&&"locationY"!=e.property||t.positionBullet(e.target)},void 0,!1)},void 0,!1)),this._bullets},enumerable:!0,configurable:!0}),e.prototype.copyFrom=function(e){t.prototype.copyFrom.call(this,e),this.bullets.copyFrom(e.bullets);var i=this.middleLine;i&&(i instanceof Q.a&&e.middleLine instanceof Q.a&&i.copyFrom(e.middleLine),i instanceof Wt.a&&e.middleLine instanceof Wt.a&&i.copyFrom(e.middleLine)),this.link.copyFrom(e.link)},e.prototype.getTooltipX=function(){if(this.middleLine)return this.middleLine.positionToPoint(this.tooltipLocation).x},e.prototype.getTooltipY=function(){if(this.middleLine)return this.middleLine.positionToPoint(this.tooltipLocation).y},Object.defineProperty(e.prototype,"gradient",{get:function(){return this._gradient||(this._gradient=new Mt.a),this._gradient},enumerable:!0,configurable:!0}),e}(s.a);c.b.registeredClasses.FlowDiagramLink=Bt;var Et=i("/e9j"),Ht=i("DHte"),zt=function(t){function e(){var e=t.call(this)||this;return e.className="FlowDiagramDataItem",e.values.value={},e.applyTheme(),e}return n.c(e,t),Object.defineProperty(e.prototype,"fromName",{get:function(){return this.properties.fromName},set:function(t){this.setProperty("fromName",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"toName",{get:function(){return this.properties.toName},set:function(t){this.setProperty("toName",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"color",{get:function(){return this.properties.color},set:function(t){this.setProperty("color",Object(M.e)(t))},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"value",{get:function(){return this.values.value.value},set:function(t){this.setValue("value",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"link",{get:function(){var t=this;if(!this._link){var e=this.component.links.create();this._link=e,this.addSprite(e),this._disposers.push(new F.b(function(){t.component&&t.component.links.removeValue(e)}))}return this._link},enumerable:!0,configurable:!0}),e}(wt.b),Ut=function(t){function e(){var e=t.call(this)||this;e.colors=new Ht.a,e.className="FlowDiagram",e.nodePadding=20,e.sortBy="none",e.sequencedInterpolation=!0,e.colors.step=2,e.minNodeSize=.02;var i=e.chartContainer.createChild(s.a);i.shouldClone=!1,i.layout="none",i.isMeasured=!1,e.linksContainer=i;var a=e.chartContainer.createChild(s.a);return a.shouldClone=!1,a.layout="none",a.isMeasured=!1,e.nodesContainer=a,e.dataItem=e.createDataItem(),e.dataItem.component=e,e.applyTheme(),e}return n.c(e,t),e.prototype.dispose=function(){t.prototype.dispose.call(this),this.dataItem.dispose()},e.prototype.validateData=function(){var e=this;0==this._parseDataFrom&&this.nodes.clear(),this.sortNodes(),this.colors.reset(),t.prototype.validateData.call(this);var i,a,n=0,r=0;g.each(this.dataItems.iterator(),function(t){var s=t.fromName;s&&((o=e.nodes.getKey(s))||((o=e.nodes.create(s)).name=s,o.chart=e,o.dataItem=t),t.fromNode=o,t.fromNode.outgoingDataItems.push(t));var o,l=t.toName;l&&((o=e.nodes.getKey(l))||((o=e.nodes.create(l)).name=l,o.chart=e,o.dataItem=t),t.toNode=o,t.toNode.incomingDataItems.push(t));if(!t.fromNode){var h=new Et.a;h.opacities=[0,1],t.link.strokeModifier=h}if(!t.toNode){var u=new Et.a;u.opacities=[1,0],t.link.strokeModifier=u}var c=t.value;y.isNumber(c)&&(n+=c,r++,(i>c||!y.isNumber(i))&&(i=c),(a<c||!y.isNumber(a))&&(a=c))});var s="value";this.dataItem.setCalculatedValue(s,a,"high"),this.dataItem.setCalculatedValue(s,i,"low"),this.dataItem.setCalculatedValue(s,n,"sum"),this.dataItem.setCalculatedValue(s,n/r,"average"),this.dataItem.setCalculatedValue(s,r,"count"),g.each(this.nodes.iterator(),function(t){var i=t[1];i.fill instanceof M.a&&(i.color=i.fill),void 0==i.color&&(i.color=e.colors.next()),void 0!=i.dataItem.color&&(i.color=i.dataItem.color),i.dataItem.visible||i.hide(0),e.getNodeValue(i)}),this.sortNodes(),this.feedLegend()},e.prototype.handleDataItemWorkingValueChange=function(t,e){this.invalidate()},e.prototype.sortNodes=function(){"name"==this.sortBy?this._sorted=this.nodes.sortedIterator():"value"==this.sortBy?this._sorted=g.sort(this.nodes.iterator(),function(t,e){return Lt.b(Xt.order(t[1].total,e[1].total))}):this._sorted=this.nodes.iterator()},e.prototype.getNodeValue=function(t){var e=0,i=0;g.each(t.incomingDataItems.iterator(),function(t){var i=t.getWorkingValue("value");y.isNumber(i)&&(e+=i)}),g.each(t.outgoingDataItems.iterator(),function(t){var e=t.getWorkingValue("value");y.isNumber(e)&&(i+=e)}),t.total=e+i,t.totalIncoming=e,t.totalOutgoing=i},e.prototype.changeSorting=function(){this.sortNodes()},e.prototype.applyInternalDefaults=function(){t.prototype.applyInternalDefaults.call(this),y.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Flow diagram"))},e.prototype.createDataItem=function(){return new zt},Object.defineProperty(e.prototype,"nodePadding",{get:function(){return this.getPropertyValue("nodePadding")},set:function(t){this.setPropertyValue("nodePadding",t,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"sortBy",{get:function(){return this.getPropertyValue("sortBy")},set:function(t){this.setPropertyValue("sortBy",t),this.changeSorting()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"minNodeSize",{get:function(){return this.getPropertyValue("minNodeSize")},set:function(t){this.setPropertyValue("minNodeSize",t,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"nodes",{get:function(){if(!this._nodes){var t=this.createNode();t.events.on("hit",function(t){t.target.handleHit(t)}),this._nodes=new h.c(t),this._disposers.push(new h.b(this._nodes))}return this._nodes},enumerable:!0,configurable:!0}),e.prototype.createNode=function(){var t=new Nt;return this._disposers.push(t),t},Object.defineProperty(e.prototype,"links",{get:function(){return this._links||(this._links=new o.e(this.createLink()),this._disposers.push(new o.c(this._links))),this._links},enumerable:!0,configurable:!0}),e.prototype.createLink=function(){var t=new Bt;return this._disposers.push(t),t},e.prototype.feedLegend=function(){var t=this.legend;if(t){var e=[];this.nodes.each(function(t,i){e.push(i)}),t.data=e,t.dataFields.name="name"}},e.prototype.disposeData=function(){t.prototype.disposeData.call(this),this.nodes.clear()},e}(wt.a);c.b.registeredClasses.FlowDiagram=Ut;var Kt=function(t){function e(){var e=t.call(this)||this;e.className="LabelBullet";var i=e.createChild($.a);return i.shouldClone=!1,i.verticalCenter="middle",i.horizontalCenter="middle",i.truncate=!0,i.hideOversized=!1,i.maxWidth=500,i.maxHeight=500,i.stroke=Object(M.c)(),i.strokeOpacity=0,i.fill=(new W.a).getFor("text"),e.events.on("maxsizechanged",e.handleMaxSize,e,!1),e.label=i,e.applyTheme(),e}return n.c(e,t),e.prototype.handleMaxSize=function(){this.label.maxWidth=this.maxWidth,this.label.maxHeight=this.maxHeight},e.prototype.copyFrom=function(e){t.prototype.copyFrom.call(this,e),this.label.copyFrom(e.label)},e}(et.a);c.b.registeredClasses.LabelBullet=Kt;var Gt=function(t){function e(){var e=t.call(this)||this;e.nextInCoord=0,e.nextOutCoord=0,e.className="SankeyNode",e.width=10,e.height=10;var i=e.createChild(Kt);i.shouldClone=!1,i.locationX=1,i.locationY=.5,i.label.text="{name}",i.width=150,i.height=150,i.label.horizontalCenter="left",i.label.padding(0,5,0,5),e.nameLabel=i;var a=e.createChild(Kt);a.shouldClone=!1,a.label.hideOversized=!1,a.locationX=.5,a.locationY=.5,a.width=150,a.height=150,a.label.horizontalCenter="middle",e.valueLabel=a;var n=e.hiddenState;return n.properties.fill=(new W.a).getFor("disabledBackground"),n.properties.opacity=.5,n.properties.visible=!0,e.background.hiddenState.copyFrom(n),e}return n.c(e,t),e.prototype.invalidateLinks=function(){var e=this;t.prototype.invalidateLinks.call(this),this.nextInCoord=0,this.nextOutCoord=0;var i=this.chart;if(i){var a=i.orientation;this._incomingSorted&&g.each(this._incomingSorted,function(t){var n=t.link,r=t.getWorkingValue("value");if(y.isNumber(r)){n.parent=e.chart.linksContainer;var s=void 0,o=void 0,l=void 0;if("horizontal"==a?(s=e.pixelX+e.dx,o=e.nextInCoord+e.pixelY+e.dy,l=0):(o=e.pixelY+e.dy,s=e.nextInCoord+e.pixelX+e.dx,l=90),n.endX=s,n.endY=o,n.startAngle=l,n.endAngle=l,n.gradient.rotation=l,n.linkWidth=r*i.valueHeight,!t.fromNode){"horizontal"==a?(n.maxWidth=200,n.startX=e.pixelX+e.dx-n.maxWidth,n.startY=n.endY):(n.maxHeight=200,n.startX=n.endX,n.startY=e.pixelY+e.dy-n.maxHeight),k.used(n.gradient),n.fill=t.toNode.color;var h=n.gradient.stops.getIndex(0);h&&("gradient"==n.colorMode&&(h.color=e.color),h.opacity=0,n.fill=n.gradient,n.stroke=n.gradient,n.gradient.validate())}e.nextInCoord+=n.linkWidth}}),this._outgoingSorted&&g.each(this._outgoingSorted,function(t){var i=t.link;i.parent=e.chart.linksContainer;var n=t.getWorkingValue("value");if(y.isNumber(n)){var r=void 0,s=void 0,o=void 0;if("horizontal"==a?(o=0,r=e.pixelX+e.pixelWidth+e.dx-1,s=e.nextOutCoord+e.pixelY+e.dy):(o=90,r=e.nextOutCoord+e.pixelX+e.dx,s=e.pixelY+e.pixelHeight+e.dy-1),i.startX=r,i.startY=s,i.startAngle=o,i.endAngle=o,i.gradient.rotation=o,i.linkWidth=n*e.chart.valueHeight,!t.toNode){"horizontal"==a?(i.maxWidth=200,i.endX=e.pixelX+i.maxWidth+e.dx,i.endY=i.startY):(i.maxHeight=200,i.endX=i.startX,i.endY=e.pixelY+i.maxHeight+e.dy),i.opacity=e.opacity;var l=i.gradient.stops.getIndex(1);l&&("gradient"==i.colorMode&&(l.color=e.color),l.opacity=0,i.fill=i.gradient,i.stroke=i.gradient,i.gradient.validate())}e.nextOutCoord+=i.linkWidth}})}this.positionBullet(this.nameLabel),this.positionBullet(this.valueLabel)},e.prototype.positionBullet=function(t){t&&(t.x=this.measuredWidth*t.locationX,t.y=this.measuredHeight*t.locationY)},Object.defineProperty(e.prototype,"level",{get:function(){return this.getPropertyValue("level")},set:function(t){this.setPropertyValue("level",t,!0)},enumerable:!0,configurable:!0}),e.prototype.copyFrom=function(e){t.prototype.copyFrom.call(this,e),this.nameLabel.copyFrom(e.nameLabel),this.valueLabel.copyFrom(e.valueLabel)},e}(Nt);c.b.registeredClasses.SankeyNode=Gt;var qt=i("xgTw"),Zt=function(t){function e(){var e=t.call(this)||this;return e.className="SankeyLink",new W.a,e.tension=.8,e.controlPointDistance=.2,e.startAngle=0,e.endAngle=0,e.linkWidth=0,e.startX=0,e.endX=0,e.startY=0,e.endY=0,e.middleLine=e.createChild(qt.a),e.middleLine.shouldClone=!1,e.middleLine.strokeOpacity=0,e.applyTheme(),e}return n.c(e,t),e.prototype.validate=function(){if(t.prototype.validate.call(this),!this.isTemplate){var e=this.startX,i=this.startY,a=this.endX,n=this.endY;y.isNumber(a)||(a=e),y.isNumber(n)||(n=i);var r=this.startAngle,s=this.endAngle,o=this.linkWidth,l="",h=e,u=i,c=a,d=n,p=e+o*m.sin(r),g=a+o*m.sin(s),f=i+o*m.cos(r),x=n+o*m.cos(s),v=e+o/2*m.sin(r),b=a+o/2*m.sin(s),P=i+o/2*m.cos(r),A=n+o/2*m.cos(s);this.zIndex=this.zIndex||this.dataItem.index;var C=this.tension+(1-this.tension)*m.sin(r),I=this.tension+(1-this.tension)*m.cos(r);if(this.middleLine.tensionX=C,this.middleLine.tensionY=I,y.isNumber(o)&&y.isNumber(e)&&y.isNumber(a)&&y.isNumber(i)&&y.isNumber(n)){m.round(h,3)==m.round(c,3)&&(c+=.01),m.round(u,3)==m.round(d,3)&&(d+=.01),m.round(p,3)==m.round(g,3)&&(g+=.01),m.round(f,3)==m.round(x,3)&&(x+=.01);var D=Math.min(p,g,h,c),_=Math.min(f,x,u,d),T=Math.max(p,g,h,c),V=Math.max(f,x,u,d);this._bbox={x:D,y:_,width:T-D,height:V-_};var S=this.controlPointDistance,F=h+(c-h)*S*m.cos(r),k=u+(d-u)*S*m.sin(r),O=c-(c-h)*S*m.cos(s),R=d-(d-u)*S*m.sin(s),L=v+(b-v)*S*m.cos(r),X=P+(A-P)*S*m.sin(r),Y=b-(b-v)*S*m.cos(s),j=A-(A-P)*S*m.sin(s),N=m.getAngle({x:F,y:k},{x:O,y:R}),M=(o/m.cos(N)-o)/m.tan(N)*m.cos(r),W=(o/m.sin(N)-o)*m.tan(N)*m.sin(r),B=-M/2+p+(g-p)*S*m.cos(r),E=-W/2+f+(x-f)*S*m.sin(r),H=-M/2+g-(g-p)*S*m.cos(s),z=-W/2+x-(x-f)*S*m.sin(s);this.middleLine.segments=[[{x:v,y:P},{x:L,y:X},{x:Y,y:j},{x:b,y:A}]],F+=M/2,k+=W/2,O+=M/2,R+=W/2,l+=w.moveTo({x:h,y:u}),l+=new Z.b(C,I).smooth([{x:h,y:u},{x:F,y:k},{x:O,y:R},{x:c,y:d}]),l+=w.lineTo({x:g,y:x}),l+=new Z.b(C,I).smooth([{x:g,y:x},{x:H,y:z},{x:B,y:E},{x:p,y:f}]),l+=w.closePath()}this.link.path=l,this.maskBullets&&(this.bulletsMask.path=l,this.bulletsContainer.mask=this.bulletsMask),this.positionBullets()}},Object.defineProperty(e.prototype,"startX",{get:function(){return this.getPropertyValue("startX")},set:function(t){this.setPropertyValue("startX",t,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"endX",{get:function(){return this.getPropertyValue("endX")},set:function(t){this.setPropertyValue("endX",t,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"startY",{get:function(){return this.getPropertyValue("startY")},set:function(t){this.setPropertyValue("startY",t,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"endY",{get:function(){return this.getPropertyValue("endY")},set:function(t){this.setPropertyValue("endY",t,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"linkWidth",{get:function(){return this.getPropertyValue("linkWidth")},set:function(t){this.setPropertyValue("linkWidth",t,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"controlPointDistance",{get:function(){return this.getPropertyValue("controlPointDistance")},set:function(t){this.setPropertyValue("controlPointDistance",t,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"tension",{get:function(){return this.getPropertyValue("tension")},set:function(t){this.setPropertyValue("tension",t,!0)},enumerable:!0,configurable:!0}),e}(Bt);c.b.registeredClasses.SankeyLink=Zt;var Jt=i("1yyj"),Qt=function(t){function e(){var e=t.call(this)||this;return e.className="SankeyDiagramDataItem",e.applyTheme(),e}return n.c(e,t),e}(zt),$t=function(t){function e(){var e=t.call(this)||this;return e.className="SankeyDiagram",e.orientation="horizontal",e.nodeAlign="middle",e.nodesContainer.width=Object(Y.c)(100),e.nodesContainer.height=Object(Y.c)(100),e.linksContainer.width=Object(Y.c)(100),e.linksContainer.height=Object(Y.c)(100),e.applyTheme(),e}return n.c(e,t),e.prototype.validateData=function(){var e=this;t.prototype.validateData.call(this),this._levelCount=0,this.nodes.each(function(t,i){i.level=e.getNodeLevel(i,0),e._levelCount=m.max(e._levelCount,i.level)})},e.prototype.getNodeLevel=function(t,e){var i=this,a=[e];return g.each(t.incomingDataItems.iterator(),function(t){t.fromNode&&a.push(i.getNodeLevel(t.fromNode,e+1))}),Math.max.apply(Math,n.f(a))},e.prototype.calculateValueHeight=function(){var t=this;this._levelSum={},this._levelNodesCount={},this.maxSum=0;var e,i,a=this.dataItem.values.value.sum;g.each(this._sorted,function(e){var i=e[1];t.getNodeValue(i)}),this.nodes.each(function(e,i){var n=i.level,r=Math.max(i.totalIncoming,i.totalOutgoing);r/a<t.minNodeSize&&(r=a*t.minNodeSize),y.isNumber(t._levelSum[n])?t._levelSum[n]+=r:t._levelSum[n]=r,y.isNumber(t._levelNodesCount[n])?t._levelNodesCount[n]++:t._levelNodesCount[n]=1}),e="horizontal"==this.orientation?this.chartContainer.maxHeight-1:this.chartContainer.maxWidth-1,f.each(this._levelSum,function(a,n){var r=n;n=n*e/(e-(t._levelNodesCount[a]-1)*t.nodePadding),t.maxSum<n&&(t.maxSum=r,i=y.toNumber(a))}),this._maxSumLevel=i;var n=this._levelNodesCount[this._maxSumLevel],r=(e-(n-1)*this.nodePadding)/this.maxSum;if(y.isNumber(this.valueHeight)){var s=void 0;try{s=this._heightAnimation.animationOptions[0].to}catch(t){}if(s!=r){var o=this.interpolationDuration;try{o=this.nodes.template.states.getKey("active").transitionDuration}catch(t){}this._heightAnimation=new Jt.a(this,{property:"valueHeight",from:this.valueHeight,to:r},o).start(),this._disposers.push(this._heightAnimation)}}else this.valueHeight=r},e.prototype.validate=function(){var e=this;t.prototype.validate.call(this),this.calculateValueHeight();var i=this.nodesContainer,a={},n=this._levelNodesCount[this._maxSumLevel],r=this.dataItem.values.value.sum;g.each(this._sorted,function(t){var s,o,l,h=t[1],u=h.level,c=0,d=e._levelNodesCount[u];switch(e.nodeAlign){case"bottom":c=(e.maxSum-e._levelSum[u])*e.valueHeight-(d-n)*e.nodePadding;break;case"middle":c=(e.maxSum-e._levelSum[u])*e.valueHeight/2-(d-n)*e.nodePadding/2}h.parent=i;var p=Math.max(h.totalIncoming,h.totalOutgoing);if(p/r<e.minNodeSize&&(p=r*e.minNodeSize),"horizontal"==e.orientation){o=(s=(e.innerWidth-h.pixelWidth)/e._levelCount)*h.level,l=a[u]||c;var y=p*e.valueHeight;h.height=y,h.minX=o,h.maxX=o,a[u]=l+y+e.nodePadding}else{s=(e.innerHeight-h.pixelHeight)/e._levelCount,o=a[u]||c,l=s*h.level;var g=p*e.valueHeight;h.width=g,h.minY=l,h.maxY=l,a[u]=o+g+e.nodePadding}h.x=o,h.y=l})},e.prototype.showReal=function(e){var i=this;if(this.interpolationDuration>0){var a=this.nodesContainer,n=0;g.each(this.links.iterator(),function(t){t.hide(0)}),g.each(this._sorted,function(t){var e,r=t[1];"horizontal"==i.orientation?(r.dx=-(a.pixelWidth-r.pixelWidth)/i._levelCount,e="dx"):(r.dy=-(a.pixelHeight-r.pixelHeight)/i._levelCount,e="dy");var s=0,o=i.interpolationDuration;i.sequencedInterpolation&&(s=i.sequencedInterpolationDelay*n+o*n/g.length(i.nodes.iterator())),r.opacity=0,r.invalidateLinks(),r.animate([{property:"opacity",from:0,to:1},{property:e,to:0}],i.interpolationDuration,i.interpolationEasing).delay(s),g.each(r.outgoingDataItems.iterator(),function(t){var e=t.link.show(i.interpolationDuration);e&&!e.isFinished()&&e.delay(s)}),g.each(r.incomingDataItems.iterator(),function(t){if(!t.fromNode){var e=t.link.show(i.interpolationDuration);e&&!e.isFinished()&&e.delay(s)}}),n++})}return t.prototype.showReal.call(this)},e.prototype.changeSorting=function(){var t=this;this.sortNodes();var e={};g.each(this._sorted,function(i){var a,n,r=i[1],s=r.level,o=(t.maxSum-t._levelSum[s])*t.valueHeight/2;"horizontal"==t.orientation?(a="y",n=r.pixelHeight):(a="x",n=r.pixelWidth),r.animate({property:a,to:e[s]||o},t.interpolationDuration,t.interpolationEasing),e[s]=(e[s]||o)+n+t.nodePadding,r.invalidateLinks()})},e.prototype.applyInternalDefaults=function(){t.prototype.applyInternalDefaults.call(this),y.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Sankey diagram"))},e.prototype.createDataItem=function(){return new Qt},Object.defineProperty(e.prototype,"nodeAlign",{get:function(){return this.getPropertyValue("nodeAlign")},set:function(t){this.setPropertyValue("nodeAlign",t),this.changeSorting()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"orientation",{get:function(){return this.getPropertyValue("orientation")},set:function(t){this.setPropertyValue("orientation",t,!0);var e=this.nodes.template.nameLabel;"vertical"==t?(this.nodes.template.width=void 0,e.label.horizontalCenter="middle",e.locationX=.5):(this.nodes.template.height=void 0,e.label.horizontalCenter="left",e.locationX=1)},enumerable:!0,configurable:!0}),e.prototype.createNode=function(){var t=new Gt;return this._disposers.push(t),t},e.prototype.createLink=function(){var t=new Zt;return this._disposers.push(t),t},Object.defineProperty(e.prototype,"valueHeight",{get:function(){return this._valueHeight},set:function(t){t!=this._valueHeight&&(this._valueHeight=t,this.invalidate())},enumerable:!0,configurable:!0}),e.prototype.disposeData=function(){t.prototype.disposeData.call(this),this._sorted=this.nodes.iterator()},e}(Ut);c.b.registeredClasses.SankeyDiagram=$t;var te=i("Inf5"),ee=function(t){function e(){var e=t.call(this)||this;e.className="ChordNode";var i=e.createChild(pt.a);i.location=.5,i.radius=5,i.text="{name}",i.zIndex=1,i.shouldClone=!1,e.label=i,e.layout="none",e.events.on("positionchanged",e.updateRotation,e,!1),e.isMeasured=!1,e.slice=e.createChild(te.a),e.slice.isMeasured=!1;var a=e.hiddenState;return a.properties.fill=(new W.a).getFor("disabledBackground"),a.properties.opacity=.5,a.properties.visible=!0,e.setStateOnChildren=!1,e.slice.hiddenState.properties.visible=!0,e.adapter.add("tooltipX",function(t,e){return e.slice.ix*(e.slice.radius-(e.slice.radius-e.slice.pixelInnerRadius)/2)}),e.adapter.add("tooltipY",function(t,e){return e.slice.iy*(e.slice.radius-(e.slice.radius-e.slice.pixelInnerRadius)/2)}),e}return n.c(e,t),e.prototype.invalidateLinks=function(){var e=this;t.prototype.invalidateLinks.call(this);var i=this.label,a=this.slice,n=this.chart;if(n&&a){var r=this.total,s=a.arc,o=a.startAngle;this.children.each(function(t){if(t instanceof et.a){var e=t.locationX;y.isNumber(e)||(e=.5);var i=t.locationY;y.isNumber(i)||(i=1);var n=o+s*e,r=i*a.radius;t.x=r*m.cos(n),t.y=r*m.sin(n)}});var l=o+s*i.location,h=o+(1-r/this.adjustedTotal)*s*.5;y.isNaN(h)&&(h=o),i.fixPosition(l,a.radius),this.nextAngle=h,this._outgoingSorted&&g.each(this._outgoingSorted,function(t){var i=t.link;i.parent=e.chart.linksContainer;var r=t.getWorkingValue("value");if(y.isNumber(r)){if(n.nonRibbon){var l=i.percentWidth;y.isNumber(l)||(l=5),l/=100,i.startAngle=o+s/2-s/2*l,i.arc=s*l}else i.arc=r*n.valueAngle,i.startAngle=e.nextAngle,e.nextAngle+=i.arc;t.toNode||(i.endAngle=i.startAngle),i.radius=a.pixelInnerRadius}}),this._incomingSorted&&g.each(this._incomingSorted,function(t){var i=t.link;if(i.radius=a.pixelInnerRadius,n.nonRibbon){var r=i.percentWidth;y.isNumber(r)||(r=5),r/=100,i.endAngle=o+s/2-s/2*r,i.arc=s*r}else{i.endAngle=e.nextAngle;var l=t.getWorkingValue("value");y.isNumber(l)&&(i.arc=l*n.valueAngle,e.nextAngle+=i.arc)}t.fromNode||(i.startAngle=i.endAngle)})}},e.prototype.updateRotation=function(){var t=this.slice,e=this.trueStartAngle+t.arc/2,i=t.radius,a=i*m.cos(e),n=i*m.sin(e),r=m.getAngle({x:a+this.pixelX,y:n+this.pixelY});t.startAngle=this.trueStartAngle+(r-e),this.dx=-this.pixelX,this.dy=-this.pixelY},e.prototype.copyFrom=function(e){t.prototype.copyFrom.call(this,e),this.label.copyFrom(e.label),this.slice.copyFrom(e.slice)},e}(Nt);c.b.registeredClasses.ChordNode=ee;var ie=function(t){function e(){var e=t.call(this)||this;return e.className="QuadraticCurve",e.element=e.paper.add("path"),e.pixelPerfect=!1,e.fill=Object(M.c)(),e.applyTheme(),e}return n.c(e,t),e.prototype.draw=function(){if(y.isNumber(this.x1+this.x2+this.y1+this.y2+this.cpx+this.cpy)){var t={x:this.x1,y:this.y1},e={x:this.x2,y:this.y2},i={x:this.cpx,y:this.cpy},a=w.moveTo(t)+w.quadraticCurveTo(e,i);this.path=a}},Object.defineProperty(e.prototype,"cpx",{get:function(){return this.getPropertyValue("cpx")},set:function(t){this.setPropertyValue("cpx",t,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"cpy",{get:function(){return this.getPropertyValue("cpy")},set:function(t){this.setPropertyValue("cpy",t,!0)},enumerable:!0,configurable:!0}),e.prototype.positionToPoint=function(t){var e={x:this.x1,y:this.y1},i={x:this.cpx,y:this.cpy},a={x:this.x2,y:this.y2},n=m.getPointOnQuadraticCurve(e,a,i,t),r=m.getPointOnQuadraticCurve(e,a,i,t+.001);return{x:n.x,y:n.y,angle:m.getAngle(n,r)}},e}(Q.a),ae=function(t){function e(){var e=t.call(this)||this;return e.className="ChordLink",e.middleLine=e.createChild(ie),e.middleLine.shouldClone=!1,e.middleLine.strokeOpacity=0,e.applyTheme(),e}return n.c(e,t),e.prototype.validate=function(){if(t.prototype.validate.call(this),!this.isTemplate){var e=this.startAngle,i=this.endAngle,a=this.arc,n=this.radius,r=this.dataItem.fromNode,s=this.dataItem.toNode,o=0,l=0;r&&(o=r.pixelX+r.dx,l=r.pixelY+r.dy);var h=0,u=0;if(s&&(h=s.pixelX+s.dx,u=s.pixelY+s.dy),n>0){var c=n*m.cos(e)+o,d=n*m.sin(e)+l,p=n*m.cos(i)+h,y=n*m.sin(i)+u,g={x:0,y:0},f=w.moveTo({x:c,y:d});f+=w.arcTo(e,a,n),f+=w.quadraticCurveTo({x:p,y:y},g),f+=w.arcTo(i,a,n),f+=w.quadraticCurveTo({x:c,y:d},g),this.link.path=a>0?f:"",this.maskBullets&&(this.bulletsMask.path=f,this.bulletsContainer.mask=this.bulletsMask);var x=e+a/2,v=i+a/2,b=this.middleLine;b.x1=n*m.cos(x)+o,b.y1=n*m.sin(x)+l,b.x2=n*m.cos(v)+h,b.y2=n*m.sin(v)+u,b.cpx=0,b.cpy=0,b.stroke=this.fill,this.positionBullets()}}},Object.defineProperty(e.prototype,"radius",{get:function(){return this.getPropertyValue("radius")},set:function(t){this.setPropertyValue("radius",t,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"arc",{get:function(){return this.getPropertyValue("arc")},set:function(t){this.setPropertyValue("arc",t,!0)},enumerable:!0,configurable:!0}),e}(Bt);c.b.registeredClasses.ChordLink=ae;var ne=function(t){function e(){var e=t.call(this)||this;return e.className="ChordDiagramDataItem",e.applyTheme(),e}return n.c(e,t),e}(zt),re=function(t){function e(){var e=t.call(this)||this;e.valueAngle=0,e.className="ChordDiagram",e.startAngle=-90,e.endAngle=270,e.radius=Object(Y.c)(80),e.innerRadius=-15,e.nodePadding=5;var i=e.chartContainer.createChild(s.a);return i.align="center",i.valign="middle",i.shouldClone=!1,i.layout="absolute",e.chordContainer=i,e.nodesContainer.parent=i,e.linksContainer.parent=i,e.chartContainer.events.on("maxsizechanged",e.invalidate,e,!1),e.applyTheme(),e}return n.c(e,t),e.prototype.validate=function(){var e=this,i=this.chartContainer,a=this.endAngle,n=this.startAngle+this.nodePadding/2,r=m.getArcRect(this.startAngle,this.endAngle,1);r=m.getCommonRectangle([r,{x:0,y:0,width:0,height:0}]);var s=Math.min(i.innerWidth/r.width,i.innerHeight/r.height);y.isNumber(s)||(s=0);var o=k.relativeRadiusToValue(this.radius,s),l=k.relativeRadiusToValue(this.innerRadius,o,!0),h=this.dataItem.values.value.sum,u=0,c=0;g.each(this._sorted,function(t){var i=t[1];e.getNodeValue(i),u++;var a=i.total;i.total/h<e.minNodeSize&&(a=h*e.minNodeSize),c+=a}),this.valueAngle=(a-this.startAngle-this.nodePadding*u)/c,g.each(this._sorted,function(t){var i=t[1],r=i.slice;r.radius=o,r.innerRadius=l;var s,c=i.total;i.total/h<e.minNodeSize&&(c=h*e.minNodeSize),i.adjustedTotal=c,s=e.nonRibbon?(a-e.startAngle)/u-e.nodePadding:e.valueAngle*c,r.arc=s,r.startAngle=n,i.trueStartAngle=n,i.parent=e.nodesContainer,i.validate(),n+=s+e.nodePadding}),this.chordContainer.definedBBox={x:o*r.x,y:o*r.y,width:o*r.width,height:o*r.height},this.chordContainer.invalidateLayout(),t.prototype.validate.call(this)},e.prototype.applyInternalDefaults=function(){t.prototype.applyInternalDefaults.call(this),y.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Chord diagram"))},e.prototype.createDataItem=function(){return new ne},Object.defineProperty(e.prototype,"startAngle",{get:function(){return this.getPropertyValue("startAngle")},set:function(t){this.setPropertyValue("startAngle",t,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"endAngle",{get:function(){return this.getPropertyValue("endAngle")},set:function(t){this.setPropertyValue("endAngle",t,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"radius",{get:function(){return this.getPropertyValue("radius")},set:function(t){this.setPercentProperty("radius",t,!0,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"innerRadius",{get:function(){return this.getPropertyValue("innerRadius")},set:function(t){this.setPercentProperty("innerRadius",t,!0,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"nonRibbon",{get:function(){return this.getPropertyValue("nonRibbon")},set:function(t){this.setPropertyValue("nonRibbon",t,!0),this.links.template.middleLine.strokeOpacity=1,this.links.template.link.fillOpacity=0},enumerable:!0,configurable:!0}),e.prototype.createNode=function(){var t=new ee;return this._disposers.push(t),t},e.prototype.createLink=function(){var t=new ae;return this._disposers.push(t),t},e}(Ut);c.b.registeredClasses.ChordDiagram=re;var se=function(t){function e(){var e=t.call(this)||this;return e.className="Column",e.width=Object(Y.c)(80),e.height=Object(Y.c)(80),e.applyOnClones=!0,e.strokeOpacity=1,e.layout="none",e.createAssets(),e.events.on("childadded",e.handleKidAdded,e,!1),e}return n.c(e,t),e.prototype.handleKidAdded=function(){"none"==this.layout&&(this.layout="absolute")},e.prototype.createAssets=function(){this.column=this.createChild(Yt.a),this.column.shouldClone=!1,this.column.cornerRadius(0,0,0,0),this._disposers.push(this.column)},e.prototype.validate=function(){t.prototype.validate.call(this);var e=this.column;e&&(e.width=m.min(this.pixelWidth,this.maxWidth),e.height=m.min(this.pixelHeight,this.maxHeight),e.invalid&&e.validate())},e.prototype.copyFrom=function(e){t.prototype.copyFrom.call(this,e),this.column&&this.column.copyFrom(e.column)},Object.defineProperty(e.prototype,"bbox",{get:function(){return this.definedBBox?this.definedBBox:this.column?{x:0,y:0,width:this.column.measuredWidth,height:this.column.measuredHeight}:{x:0,y:0,width:m.min(this.pixelWidth,this.maxWidth),height:m.min(this.pixelHeight,this.maxHeight)}},enumerable:!0,configurable:!0}),e}(s.a);c.b.registeredClasses.Column=se;var oe=function(t){function e(){var e=t.call(this)||this;return e.className="ColumnSeriesDataItem",e.locations.dateX=.5,e.locations.dateY=.5,e.locations.categoryX=.5,e.locations.categoryY=.5,e.applyTheme(),e}return n.c(e,t),Object.defineProperty(e.prototype,"column",{get:function(){return this._column},set:function(t){this.setColumn(t)},enumerable:!0,configurable:!0}),e.prototype.setColumn=function(t){var e=this;if(this._column&&t!=this._column&&O.remove(this.sprites,this._column),this._column=t,t){var i=t.dataItem;i&&i!=this&&(i.column=void 0),this.addSprite(t),this._disposers.push(new F.b(function(){e.component&&e.component.columns.removeValue(t)}))}},Object.defineProperty(e.prototype,"rangesColumns",{get:function(){return this._rangesColumns||(this._rangesColumns=new h.a),this._rangesColumns},enumerable:!0,configurable:!0}),e}(R),le=function(t){function e(){var e=t.call(this)||this;e._startLocation=0,e._endLocation=1,e.className="ColumnSeries",e.width=Object(Y.c)(100),e.height=Object(Y.c)(100),e.strokeOpacity=0,e.fillOpacity=1,e.clustered=!0;var i=e.mainContainer.createChild(s.a);return i.shouldClone=!1,i.isMeasured=!1,i.layout="none",e._columnsContainer=i,e.columns.template.pixelPerfect=!1,e.tooltipColorSource=e.columns.template,e.applyTheme(),e}return n.c(e,t),Object.defineProperty(e.prototype,"columnsContainer",{get:function(){return this._columnsContainer},enumerable:!0,configurable:!0}),e.prototype.applyInternalDefaults=function(){t.prototype.applyInternalDefaults.call(this),y.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Column Series"))},e.prototype.createDataItem=function(){return new oe},e.prototype.validate=function(){var i=this,a=this.chart.series,n=0,r=0;g.each(a.iterator(),function(t){t instanceof e&&i.baseAxis==t.baseAxis&&((!t.stacked&&t.clustered||0===n)&&n++,t==i&&(r=n-1))});var s=this.baseAxis.renderer,o=s.cellStartLocation,l=s.cellEndLocation;this._startLocation=o+r/n*(l-o),this._endLocation=o+(r+1)/n*(l-o),t.prototype.validate.call(this);for(var h=0;h<this.startIndex;h++){var u=this.dataItems.getIndex(h);this.disableUnusedColumns(u)}for(h=this.dataItems.length-1;h>this.endIndex;h--){u=this.dataItems.getIndex(h);this.disableUnusedColumns(u)}},e.prototype.validateDataElement=function(e){this.validateDataElementReal(e),t.prototype.validateDataElement.call(this,e)},e.prototype.getStartLocation=function(t){var e=this._startLocation;return this.baseAxis==this.xAxis?e+=t.locations[this.xOpenField]-.5:e+=t.locations[this.yOpenField]-.5,e},e.prototype.handleDataItemWorkingValueChange=function(e,i){this.simplifiedProcessing?this.validateDataElement(e):t.prototype.handleDataItemWorkingValueChange.call(this,e,i)},e.prototype.getEndLocation=function(t){var e=this._endLocation;return this.baseAxis==this.xAxis?e+=t.locations[this.xField]-.5:e+=t.locations[this.yField]-.5,e},e.prototype.validateDataElementReal=function(t){var e,i,a,n,r=this,s=this.getStartLocation(t),o=this.getEndLocation(t),h=this.xField,u=this.xOpenField,c=this.yField,d=this.yOpenField,p=this.columns.template,x=p.percentWidth,v=p.percentHeight,b=p.pixelWidth,P=p.pixelHeight,A=p.maxWidth,C=p.maxHeight,I=p.pixelPaddingLeft,D=p.pixelPaddingRight,_=p.pixelPaddingTop,V=p.pixelPaddingBottom,F=!1;if(this.xAxis instanceof T&&this.yAxis instanceof T){if(!t.hasValue(this._xValueFields)||!t.hasValue(this._yValueFields))return;if(s=0,o=1,!y.isNaN(x))s+=R=m.round((o-s)*(1-x/100)/2,5),o-=R;if(e=this.xAxis.getX(t,u,s),i=this.xAxis.getX(t,h,o),y.isNaN(x))e+=R=(i-e-b)/2,i-=R;if(!y.isNaN(A))e+=R=(i-e-A)/2,i-=R;if(s=0,o=1,!y.isNaN(v))s+=R=m.round((1-v/100)/2,5),o-=R;if(a=this.yAxis.getY(t,d,s),n=this.yAxis.getY(t,c,o),y.isNaN(v))n+=R=(n-a-P)/2,a-=R;if(!y.isNaN(C))n+=R=(n-a-C)/2,a-=R;i=this.fixHorizontalCoordinate(i),e=this.fixHorizontalCoordinate(e),a=this.fixVerticalCoordinate(a),n=this.fixVerticalCoordinate(n)}else if(this.baseAxis==this.xAxis){if(!t.hasValue(this._yValueFields))return;if(!y.isNaN(x))s+=R=m.round((o-s)*(1-x/100)/2,5),o-=R;if(e=this.xAxis.getX(t,u,s),i=this.xAxis.getX(t,h,o),y.isNaN(x))e+=R=(i-e-b)/2,i-=R;if(!y.isNaN(A))e+=R=(i-e-A)/2,i-=R;var k=t.locations[d],O=t.locations[c];this.yAxis instanceof l.a&&this.dataFields[this.yField]!=this.dataFields[this.yOpenField]&&(k=0,O=0),n=this.yAxis.getY(t,d,k),a=this.yAxis.getY(t,c,O);var w=Math.ceil(this.yAxis.axisLength);(a<0&&n<0||a>w&&n>w)&&(F=!0),a=this.fixVerticalCoordinate(a),n=this.fixVerticalCoordinate(n),Math.abs(i-e)-I-D==0&&(F=!0)}else{if(!t.hasValue(this._xValueFields))return;var R;if(!y.isNaN(v))s+=R=m.round((o-s)*(1-v/100)/2,5),o-=R;if(a=this.yAxis.getY(t,d,s),n=this.yAxis.getY(t,c,o),y.isNaN(v))n-=R=(n-a-P)/2,a+=R;if(!y.isNaN(C))n-=R=(n-a-C)/2,a+=R;var L=t.locations[h],X=t.locations[u];this.xAxis instanceof l.a&&this.dataFields[this.xField]!=this.dataFields[this.xOpenField]&&(L=0,X=0),i=this.xAxis.getX(t,h,L),e=this.xAxis.getX(t,u,X);w=Math.ceil(this.xAxis.axisLength);(i<0&&e<0||i>w&&e>w)&&(F=!0),i=this.fixHorizontalCoordinate(i),e=this.fixHorizontalCoordinate(e),Math.abs(a-n)-_-V==0&&(F=!0)}var Y,j=Math.abs(i-e),N=Math.abs(n-a),M=Math.min(e,i),W=Math.min(a,n);F?this.disableUnusedColumns(t):(t.column?Y=t.column:(Y=this.columns.create(),f.copyProperties(this,Y,S.b),f.copyProperties(this.columns.template,Y,S.b),t.addSprite(Y),t.column=Y,this.itemsFocusable()?(Y.role="menuitem",Y.focusable=!0):(Y.role="listitem",Y.focusable=!1),Y.focusable&&(Y.events.once("focus",function(e){Y.readerTitle=r.populateString(r.itemReaderText,t)},void 0,!1),Y.events.once("blur",function(t){Y.readerTitle=""},void 0,!1)),Y.hoverable&&(Y.events.once("over",function(e){Y.readerTitle=r.populateString(r.itemReaderText,t)},void 0,!1),Y.events.once("out",function(t){Y.readerTitle=""},void 0,!1))),Y.width=j,Y.height=N,Y.x=M,Y.y=W,Y.realX=e,Y.realY=a,Y.realWidth=i-e,Y.realHeight=n-a,Y.parent=this.columnsContainer,Y.virtualParent=this,this.setColumnStates(Y),Y.invalid&&Y.validate(),Y.__disabled=!1,g.each(this.axisRanges.iterator(),function(e){var i=t.rangesColumns.getKey(e.uid);i||(i=r.columns.create(),f.copyProperties(e.contents,i,S.b),t.addSprite(i),t.rangesColumns.setKey(e.uid,i)),i.parent=e.contents,i.width=j,i.height=N,i.x=M,i.y=W,r.setColumnStates(i),i.invalid&&i.validate(),i.__disabled=!1}));t.itemWidth=j,t.itemHeight=N},e.prototype.disableUnusedColumns=function(t){t&&(t.column&&(t.column.width=0,t.column.height=0,t.column.__disabled=!0),g.each(this.axisRanges.iterator(),function(e){var i=t.rangesColumns.getKey(e.uid);i&&(i.width=0,i.height=0,i.__disabled=!0)}))},e.prototype.setColumnStates=function(t){var e=t.dataItem;if(this.xAxis instanceof l.a||this.yAxis instanceof l.a){var i,a=void 0,n=void 0;this.baseAxis==this.yAxis?this.xOpenField&&this.xField&&this.xAxis instanceof l.a&&(i=e.getValue(this.xOpenField),a=e.getValue(this.xField),n=e.getValue(this.xAxis.axisFieldName+"X","previousChange")):this.yOpenField&&this.yField&&this.yAxis instanceof l.a&&(i=e.getValue(this.yOpenField),a=e.getValue(this.yField),n=e.getValue(this.yAxis.axisFieldName+"Y","previousChange")),a<i?(e.droppedFromOpen=!0,t.defaultState.copyFrom(this._dropFromOpenState),t.setState(this._dropFromOpenState,0)):(e.droppedFromOpen=!1,t.defaultState.copyFrom(this._riseFromOpenState),t.setState(this._riseFromOpenState,0)),n<0?(e.droppedFromPrevious=!0,t.defaultState.copyFrom(this._dropFromPreviousState),t.setState(this._dropFromPreviousState,0)):(e.droppedFromPrevious=!1,t.defaultState.copyFrom(this._riseFromPreviousState),t.setState(this._riseFromPreviousState,0))}},Object.defineProperty(e.prototype,"columns",{get:function(){return this._columns||(this._columns=new o.e(this.createColumnTemplate()),this._disposers.push(new o.c(this._columns)),this._disposers.push(this._columns.template)),this._columns},enumerable:!0,configurable:!0}),e.prototype.createColumnTemplate=function(){return new se},Object.defineProperty(e.prototype,"clustered",{get:function(){return this.getPropertyValue("clustered")},set:function(t){this.setPropertyValue("clustered",t,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"dropFromOpenState",{get:function(){return this._dropFromOpenState||(this._dropFromOpenState=this.states.create("dropFromOpenState")),this._dropFromOpenState},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"dropFromPreviousState",{get:function(){return this._dropFromPreviousState||(this._dropFromPreviousState=this.states.create("dropFromPreviousState")),this._dropFromPreviousState},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"riseFromOpenState",{get:function(){return this._riseFromOpenState||(this._riseFromOpenState=this.states.create("riseFromOpenState")),this._riseFromOpenState},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"riseFromPreviousState",{get:function(){return this._riseFromPreviousState||(this._riseFromPreviousState=this.states.create("riseFromPreviousState")),this._riseFromPreviousState},enumerable:!0,configurable:!0}),e.prototype.updateLegendValue=function(e){var i=this;if(t.prototype.updateLegendValue.call(this,e),this.legendDataItem){var a,n,r=this.legendDataItem.marker;e&&(a=e.droppedFromOpen?this._dropFromOpenState:this._riseFromOpenState,n=e.droppedFromPrevious?this._dropFromPreviousState:this._riseFromPreviousState),g.each(r.children.iterator(),function(t){e?(t.setState(n),t.setState(a)):(t.setState(i._riseFromPreviousState),t.setState(i._riseFromOpenState))})}},e.prototype.createLegendMarker=function(t){var e=t.pixelWidth,i=t.pixelHeight;t.removeChildren();var a=t.createChild(Yt.a);a.shouldClone=!1,f.copyProperties(this,a,S.b),a.copyFrom(this.columns.template),a.padding(0,0,0,0),a.width=e,a.height=i;var n=t.dataItem;n.color=this.fill,n.colorOrig=this.fill},e.prototype.copyFrom=function(e){t.prototype.copyFrom.call(this,e),this.columns.template.copyFrom(e.columns.template)},e.prototype.getBulletLocationX=function(e,i){if(this.baseAxis==this.xAxis){var a=e.locationX;return y.isNumber(a)||(a=.5),this._endLocation-(this._endLocation-this._startLocation)*a}return t.prototype.getBulletLocationX.call(this,e,i)},e.prototype.getBulletLocationY=function(e,i){if(this.baseAxis==this.yAxis){var a=e.locationY;return y.isNumber(a)||(a=.5),this._endLocation-(this._endLocation-this._startLocation)*a}return t.prototype.getBulletLocationY.call(this,e,i)},e.prototype.fixVerticalCoordinate=function(t){var e=this.columns.template.pixelPaddingBottom,i=-this.columns.template.pixelPaddingTop,a=this.yAxis.axisLength+e;return m.fitToRange(t,i,a)},e.prototype.fixHorizontalCoordinate=function(t){var e=this.columns.template.pixelPaddingLeft,i=this.columns.template.pixelPaddingRight,a=-e,n=this.xAxis.axisLength+i;return m.fitToRange(t,a,n)},e.prototype.disposeData=function(){t.prototype.disposeData.call(this),this.columns.clear()},e}(L);c.b.registeredClasses.ColumnSeries=le,c.b.registeredClasses.ColumnSeriesDataItem=oe;var he=function(t){function e(){var e=t.call(this)||this;return e.className="TreeMapSeriesDataItem",e.applyTheme(),e}return n.c(e,t),Object.defineProperty(e.prototype,"parentName",{get:function(){var t=this.treeMapDataItem;if(t&&t.parent)return t.parent.name},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"value",{get:function(){var t=this.treeMapDataItem;if(t)return t.value},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"treeMapDataItem",{get:function(){return this._dataContext},enumerable:!0,configurable:!0}),e.prototype.hide=function(e,i,a,n){var r=this.treeMapDataItem;return r&&r.hide(e),t.prototype.hide.call(this,e,i,a,n)},e.prototype.show=function(e,i,a){var n=this.treeMapDataItem;return n&&n.show(e,i,a),t.prototype.show.call(this,e,i,a)},e}(oe),ue=function(t){function e(){var e=t.call(this)||this;e.className="TreeMapSeries",e.applyTheme(),e.fillOpacity=1,e.strokeOpacity=1,e.minBulletDistance=0,e.columns.template.tooltipText="{parentName} {name}: {value}",e.columns.template.configField="config";var i=new W.a;return e.stroke=i.getFor("background"),e.dataFields.openValueX="x0",e.dataFields.valueX="x1",e.dataFields.openValueY="y0",e.dataFields.valueY="y1",e.sequencedInterpolation=!1,e.showOnInit=!1,e.columns.template.pixelPerfect=!1,e}return n.c(e,t),e.prototype.processDataItem=function(e,i){i.seriesDataItem=e,t.prototype.processDataItem.call(this,e,i)},e.prototype.createDataItem=function(){return new he},e.prototype.show=function(e){var i=this.defaultState.transitionDuration;return y.isNumber(e)&&(i=e),this.dataItems.each(function(t){t.show(e)}),t.prototype.showReal.call(this,i)},e.prototype.hide=function(e){var i=this.defaultState.transitionDuration;y.isNumber(e)&&(i=e);var a=t.prototype.hideReal.call(this,i);return this.dataItems.each(function(t){t.hide(e)}),a},e.prototype.processValues=function(){},e.prototype.dataChangeUpdate=function(){},e.prototype.processConfig=function(e){e&&(y.hasValue(e.dataFields)&&y.isObject(e.dataFields)||(e.dataFields={})),t.prototype.processConfig.call(this,e)},e.prototype.createLegendMarker=function(t){var e=t.pixelWidth,i=t.pixelHeight;t.removeChildren();var a=t.createChild(Yt.a);a.shouldClone=!1,f.copyProperties(this,a,S.b),a.padding(0,0,0,0),a.width=e,a.height=i;var n=t.dataItem;n.color=a.fill,n.colorOrig=a.fill},e}(le);c.b.registeredClasses.TreeMapSeries=ue,c.b.registeredClasses.TreeMapSeriesDataItem=he;var ce=function(t){function e(){var e=t.call(this)||this;return e.rows=[],e.className="TreeMapDataItem",e.values.value={},e.values.x0={},e.values.y0={},e.values.x1={},e.values.y1={},e.hasChildren.children=!0,e.applyTheme(),e}return n.c(e,t),Object.defineProperty(e.prototype,"legendDataItem",{get:function(){return this._legendDataItem},set:function(t){this._legendDataItem=t,t.label&&(t.label.dataItem=this),t.valueLabel&&(t.valueLabel.dataItem=this)},enumerable:!0,configurable:!0}),e.prototype.getDuration=function(){return 0},Object.defineProperty(e.prototype,"value",{get:function(){var t=0;return this.children&&0!=this.children.length?g.each(this.children.iterator(),function(e){var i=e.value;y.isNumber(i)&&(t+=i)}):t=this.values.value.workingValue,t},set:function(t){this.setValue("value",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"percent",{get:function(){return this.parent?this.value/this.parent.value*100:100},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"x0",{get:function(){return this.values.x0.value},set:function(t){this.setValue("x0",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"x1",{get:function(){return this.values.x1.value},set:function(t){this.setValue("x1",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"y0",{get:function(){return this.values.y0.value},set:function(t){this.setValue("y0",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"y1",{get:function(){return this.values.y1.value},set:function(t){this.setValue("y1",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"name",{get:function(){return this.properties.name},set:function(t){this.setProperty("name",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"children",{get:function(){return this.properties.children},set:function(t){this.setProperty("children",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"level",{get:function(){return this.parent?this.parent.level+1:0},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"color",{get:function(){var t=this.properties.color;return void 0==t&&this.parent&&(t=this.parent.color),void 0==t&&this.component&&(t=this.component.colors.getIndex(this.component.colors.step*this.index)),t},set:function(t){this.setProperty("color",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"fill",{get:function(){return this.color},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"series",{get:function(){return this._series},set:function(t){t!=this._series&&(this._series&&(this.component.series.removeValue(this._series),this._series.dispose()),this._series=t,this._disposers.push(t))},enumerable:!0,configurable:!0}),e.prototype.hide=function(e,i,a,n){return this.setWorkingValue("value",0),this.children&&this.children.each(function(t){t.hide(e,i,a,n)}),t.prototype.hide.call(this,e,i,a,n)},e.prototype.show=function(e,i,a){return this.setWorkingValue("value",this.values.value.value),this.children&&this.children.each(function(t){t.show(e,i,a)}),t.prototype.show.call(this,e,i,a)},e}(G),de=function(t){function e(){var e=t.call(this)||this;e.layoutAlgorithm=e.squarify,e.zoomable=!0,e.className="TreeMap",e.maxLevels=2,e.currentLevel=0,e.colors=new Ht.a,e.sorting="descending";var i=e.xAxes.push(new l.a);i.title.disabled=!0,i.strictMinMax=!0;var a=i.renderer;a.inside=!0,a.labels.template.disabled=!0,a.ticks.template.disabled=!0,a.grid.template.disabled=!0,a.axisFills.template.disabled=!0,a.minGridDistance=100,a.line.disabled=!0,a.baseGrid.disabled=!0;var n=e.yAxes.push(new l.a);n.title.disabled=!0,n.strictMinMax=!0;var r=n.renderer;r.inside=!0,r.labels.template.disabled=!0,r.ticks.template.disabled=!0,r.grid.template.disabled=!0,r.axisFills.template.disabled=!0,r.minGridDistance=100,r.line.disabled=!0,r.baseGrid.disabled=!0,r.inversed=!0,e.xAxis=i,e.yAxis=n;var s=new ue;return e.seriesTemplates=new h.c(s),e._disposers.push(new h.b(e.seriesTemplates)),e._disposers.push(s),e.zoomOutButton.events.on("hit",function(){e.zoomToChartDataItem(e._homeDataItem)},void 0,!1),e.seriesTemplates.events.on("insertKey",function(t){t.newValue.isTemplate=!0},void 0,!1),e.applyTheme(),e}return n.c(e,t),Object.defineProperty(e.prototype,"navigationBar",{get:function(){return this._navigationBar},set:function(t){var e=this;this._navigationBar!=t&&(this._navigationBar=t,t.parent=this,t.toBack(),t.links.template.events.on("hit",function(t){var i=t.target.dataItem.dataContext;i.isDisposed()||(e.zoomToChartDataItem(i),e.createTreeSeries(i))},void 0,!0),this._disposers.push(t))},enumerable:!0,configurable:!0}),e.prototype.validateData=function(){this.series.clear(),this._tempSeries=[],t.prototype.validateData.call(this),this._homeDataItem&&this._homeDataItem.dispose();var e=this.dataItems.template.clone();this._homeDataItem=e,g.each(this.dataItems.iterator(),function(t){t.parent=e}),e.children=this.dataItems,e.x0=0,e.y0=0,e.name=this._homeText;var i=10*Math.round(1e3*this.pixelHeight/this.pixelWidth/10)||1e3;e.x1=1e3,e.y1=i,this.xAxis.min=0,this.xAxis.max=1e3,this.xAxis.getMinMax(),this.yAxis.min=0,this.yAxis.max=i,this.yAxis.getMinMax(),this.layoutItems(e),this.createTreeSeries(e)},e.prototype.layoutItems=function(t,e){if(t){var i=t.children;e||(e=this.sorting),"ascending"==e&&i.values.sort(function(t,e){return t.value-e.value}),"descending"==e&&i.values.sort(function(t,e){return e.value-t.value}),this._updateDataItemIndexes(0),this.layoutAlgorithm(t);for(var a=0,n=i.length;a<n;a++){var r=i.getIndex(a);r.children&&this.layoutItems(r)}}},e.prototype.createTreeSeries=function(t){var e=this;this._tempSeries=[];for(var i=[t],a=t.parent;void 0!=a;)this.initSeries(a),i.push(a),a=a.parent;i.reverse(),this.navigationBar&&(this.navigationBar.data=i),this.createTreeSeriesReal(t),O.each(this._tempSeries,function(t){-1==e.series.indexOf(t)&&e.series.push(t),t.zIndex=t.level})},e.prototype.createTreeSeriesReal=function(t){if(t.children&&t.level<this.currentLevel+this.maxLevels){this.initSeries(t);for(var e=0;e<t.children.length;e++){var i=t.children.getIndex(e);i.children&&this.createTreeSeriesReal(i)}}},e.prototype.setData=function(e){this.currentLevel=0,this.currentlyZoomed=void 0,this.xAxis.start=0,this.xAxis.end=1,this.yAxis.start=0,this.yAxis.end=1,t.prototype.setData.call(this,e)},e.prototype.seriesAppeared=function(){return!0},e.prototype.initSeries=function(t){var e=this;if(!t.series){var i=void 0,a=this.seriesTemplates.getKey(t.level.toString());(i=a?a.clone():this.series.create()).dataItem.dataContext=t,i.name=t.name,i.parentDataItem=t,t.series=i;var n=t.level;i.level=n;var r=t.dataContext;r&&(i.config=r.config),this.dataUsers.removeValue(i),i.data=t.children.values,i.fill=t.color,i.columnsContainer.hide(0),i.bulletsContainer.hide(0),i.columns.template.adapter.add("fill",function(t,e){var i=e.dataItem;if(i){var a=i.treeMapDataItem;if(a)return e.fill=a.color,e.adapter.remove("fill"),a.color}}),this.zoomable&&(t.level>this.currentLevel||t.children&&t.children.length>0)&&(i.columns.template.cursorOverStyle=j.a.pointer,this.zoomable&&i.columns.template.events.on("hit",function(i){var a=i.target.dataItem;t.level>e.currentLevel?e.zoomToChartDataItem(a.treeMapDataItem.parent):e.zoomToSeriesDataItem(a)},this,void 0))}this._tempSeries.push(t.series)},e.prototype.toggleBullets=function(t){var e=this;g.each(this.series.iterator(),function(i){-1==e._tempSeries.indexOf(i)?(i.columnsContainer.hide(),i.bulletsContainer.hide(t)):(i.columnsContainer.show(),i.bulletsContainer.show(t),i.level<e.currentLevel&&i.bulletsContainer.hide(t))})},e.prototype.zoomToSeriesDataItem=function(t){this.zoomToChartDataItem(t.treeMapDataItem)},e.prototype.zoomToChartDataItem=function(t){var e=this,i=this.zoomOutButton;if(i&&(t!=this._homeDataItem?i.show():i.hide()),t&&t.children){this.xAxis.zoomToValues(t.x0,t.x1),this.yAxis.zoomToValues(t.y0,t.y1),this.currentLevel=t.level,this.currentlyZoomed=t,this.createTreeSeries(t);var a=this.xAxis.rangeChangeAnimation||this.yAxis.rangeChangeAnimation;!a||a.isDisposed()||a.isFinished()?this.toggleBullets():(this._dataDisposers.push(a),a.events.once("animationended",function(){e.toggleBullets()}))}},e.prototype.applyInternalDefaults=function(){t.prototype.applyInternalDefaults.call(this),y.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("TreeMap chart"))},e.prototype.createDataItem=function(){return new ce},Object.defineProperty(e.prototype,"maxLevels",{get:function(){return this.getPropertyValue("maxLevels")},set:function(t){this.setPropertyValue("maxLevels",t,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"currentLevel",{get:function(){return this.getPropertyValue("currentLevel")},set:function(t){this.setPropertyValue("currentLevel",t,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"sorting",{get:function(){return this.getPropertyValue("sorting")},set:function(t){this.setPropertyValue("sorting",t,!0)},enumerable:!0,configurable:!0}),e.prototype.createSeries=function(){return new ue},Object.defineProperty(e.prototype,"homeText",{get:function(){return this._homeText},set:function(t){this._homeText=t,this._homeDataItem&&(this._homeDataItem.name=this._homeText)},enumerable:!0,configurable:!0}),e.prototype.processConfig=function(e){if(e){if(y.hasValue(e.layoutAlgorithm)&&y.isString(e.layoutAlgorithm))switch(e.layoutAlgorithm){case"squarify":e.layoutAlgorithm=this.squarify;break;case"binaryTree":e.layoutAlgorithm=this.binaryTree;break;case"slice":e.layoutAlgorithm=this.slice;break;case"dice":e.layoutAlgorithm=this.dice;break;case"sliceDice":e.layoutAlgorithm=this.sliceDice;break;default:delete e.layoutAlgorithm}y.hasValue(e.navigationBar)&&!y.hasValue(e.navigationBar.type)&&(e.navigationBar.type="NavigationBar"),t.prototype.processConfig.call(this,e)}},e.prototype.validateLayout=function(){t.prototype.validateLayout.call(this),this.layoutItems(this.currentlyZoomed)},e.prototype.validateDataItems=function(){t.prototype.validateDataItems.call(this),this.layoutItems(this._homeDataItem),g.each(this.series.iterator(),function(t){t.validateRawData()}),this.zoomToChartDataItem(this._homeDataItem)},e.prototype.binaryTree=function(t){var e,i,a=t.children,n=a.length,r=new Array(n+1);for(r[0]=i=e=0;e<n;++e)r[e+1]=i+=a.getIndex(e).value;!function t(e,i,n,s,o,l,h){if(e>=i-1){var u=a.getIndex(e);return u.x0=s,u.y0=o,u.x1=l,void(u.y1=h)}var c=r[e],d=n/2+c,p=e+1,y=i-1;for(;p<y;){var g=p+y>>>1;r[g]<d?p=g+1:y=g}d-r[p-1]<r[p]-d&&e+1<p&&--p;var m=r[p]-c,f=n-m;if(l-s>h-o){var x=(s*f+l*m)/n;t(e,p,m,s,o,x,h),t(p,i,f,x,o,l,h)}else{var v=(o*f+h*m)/n;t(e,p,m,s,o,l,v),t(p,i,f,s,v,l,h)}}(0,n,t.value,t.x0,t.y0,t.x1,t.y1)},e.prototype.slice=function(t){for(var e,i=t.x0,a=t.x1,n=t.y0,r=t.y1,s=t.children,o=-1,l=s.length,h=t.value&&(r-n)/t.value;++o<l;)(e=s.getIndex(o)).x0=i,e.x1=a,e.y0=n,n+=e.value*h,e.y1=n},e.prototype.dice=function(t){for(var e,i=t.x0,a=t.x1,n=t.y0,r=t.y1,s=t.children,o=-1,l=s.length,h=t.value&&(a-i)/t.value;++o<l;)(e=s.getIndex(o)).y0=n,e.y1=r,e.x0=i,i+=e.value*h,e.x1=i},e.prototype.sliceDice=function(t){1&t.level?this.slice(t):this.dice(t)},e.prototype.squarify=function(t){for(var e,i,a,n,r,s,o,l,h,u,c=(1+Math.sqrt(5))/2,d=t.x0,p=t.x1,y=t.y0,g=t.y1,m=t.children,f=0,x=0,v=m.length,b=t.value;f<v;){i=p-d,a=g-y;do{n=m.getIndex(x++).value}while(!n&&x<v);for(r=s=n,u=n*n*(h=Math.max(a/i,i/a)/(b*c)),l=Math.max(s/u,u/r);x<v;++x){if(n+=e=m.getIndex(x).value,e<r&&(r=e),e>s&&(s=e),u=n*n*h,(o=Math.max(s/u,u/r))>l){n-=e;break}l=o}var P=this.dataItems.template.clone();P.value=n,P.dice=i<a,P.children=m.slice(f,x),P.x0=d,P.y0=y,P.x1=p,P.y1=g,P.dice?(P.y1=b?y+=a*n/b:g,this.dice(P)):(P.x1=b?d+=i*n/b:p,this.slice(P)),b-=n,f=x}},e.prototype.handleDataItemValueChange=function(t,e){"value"==e&&this.invalidateDataItems()},e.prototype.handleDataItemWorkingValueChange=function(t,e){"value"==e&&this.invalidateDataItems()},e.prototype.getLegendLevel=function(t){if(t&&t.children)return t.children.length>1?t:1==t.children.length?this.getLegendLevel(t.children.getIndex(0)):t},e.prototype.feedLegend=function(){var t=this.legend;if(t){t.dataFields.name="name";var e=this.getLegendLevel(this._homeDataItem);if(e){var i=[];e.children.each(function(t){i.push(t)}),t.data=i}}},e.prototype.disposeData=function(){t.prototype.disposeData.call(this),this._homeDataItem=void 0,this.series.clear(),this.navigationBar&&this.navigationBar.disposeData(),this.xAxis.disposeData(),this.yAxis.disposeData()},e.prototype.getExporting=function(){var e=this,i=t.prototype.getExporting.call(this);return i.adapter.add("formatDataFields",function(t){return"csv"!=t.format&&"xlsx"!=t.format||y.hasValue(e.dataFields.children)&&delete t.dataFields[e.dataFields.children],t}),i},e}(q);c.b.registeredClasses.TreeMap=de;var pe=function(t){function e(){var e=t.call(this)||this;return e._chart=new F.d,e.className="AxisRendererX3D",e._disposers.push(e._chart),e.applyTheme(),e}return n.c(e,t),e.prototype.updateGridElement=function(t,e,i){e+=(i-e)*t.location;var a=this.positionToPoint(e);if(t.element){var n=this.chart.dx3D||0,r=this.chart.dy3D||0,s=this.getHeight();t.path=w.moveTo({x:n,y:r})+w.lineTo({x:n,y:s+r})+w.lineTo({x:0,y:s})}this.positionItem(t,a),this.toggleVisibility(t,e,0,1)},e.prototype.updateBaseGridElement=function(){t.prototype.updateBaseGridElement.call(this);var e=this.getHeight(),i=this.chart.dx3D||0,a=this.chart.dy3D||0;this.baseGrid.path=w.moveTo({x:i,y:a})+w.lineTo({x:0,y:0})+w.lineTo({x:0,y:e})},Object.defineProperty(e.prototype,"chart",{get:function(){return this._chart.get()},set:function(t){t&&this._chart.set(t,t.events.on("propertychanged",this.handle3DChanged,this,!1))},enumerable:!0,configurable:!0}),e.prototype.handle3DChanged=function(t){"depth"!=t.property&&"angle"!=t.property||this.invalidate()},e}(b.a);c.b.registeredClasses.AxisRendererX3D=pe;var ye=function(t){function e(){var e=t.call(this)||this;return e._chart=new F.d,e.className="AxisRendererY3D",e._disposers.push(e._chart),e.applyTheme(),e}return n.c(e,t),e.prototype.updateGridElement=function(t,e,i){e+=(i-e)*t.location;var a=this.positionToPoint(e);if(t.element){var n=this.chart.dx3D||0,r=this.chart.dy3D||0,s=this.getWidth();t.path=w.moveTo({x:0,y:0})+w.lineTo({x:n,y:r})+w.lineTo({x:s+n,y:r})}this.positionItem(t,a),this.toggleVisibility(t,e,0,1)},e.prototype.updateBaseGridElement=function(){t.prototype.updateBaseGridElement.call(this);var e=this.chart.dx3D||0,i=this.chart.dy3D||0,a=this.getWidth();this.baseGrid.path=w.moveTo({x:0,y:0})+w.lineTo({x:a,y:0})+w.lineTo({x:a+e,y:i})},Object.defineProperty(e.prototype,"chart",{get:function(){return this._chart.get()},set:function(t){t&&this._chart.set(t,t.events.on("propertychanged",this.handle3DChanged,this,!1))},enumerable:!0,configurable:!0}),e.prototype.handle3DChanged=function(t){"depth"!=t.property&&"angle"!=t.property||this.invalidate()},e}(P.a),ge=i("Mr4Y"),me=function(t){function e(){var e=t.call(this)||this;return e.className="Column3D",e}return n.c(e,t),e.prototype.createAssets=function(){this.column3D=this.createChild(ge.a),this.column3D.shouldClone=!1,this.column3D.strokeOpacity=0,this.column=this.column3D},e.prototype.validate=function(){t.prototype.validate.call(this),this.column3D&&(this.column3D.width=this.pixelWidth,this.column3D.height=this.pixelHeight,this.column3D.invalid&&this.column3D.validate())},e.prototype.copyFrom=function(e){t.prototype.copyFrom.call(this,e),this.column3D&&this.column3D.copyFrom(e.column3D)},e.prototype.setFill=function(e){t.prototype.setFill.call(this,e),this.column.fill=e},e}(se);c.b.registeredClasses.Column3D=me;var fe=function(t){function e(){var e=t.call(this)||this;return e.className="ColumnSeries3DDataItem",e.applyTheme(),e}return n.c(e,t),e}(oe),xe=function(t){function e(){var e=t.call(this)||this;return e.className="ColumnSeries3D",e.columns.template.column3D.applyOnClones=!0,e.columns.template.hiddenState.properties.visible=!0,e.applyTheme(),e}return n.c(e,t),Object.defineProperty(e.prototype,"columnsContainer",{get:function(){return this.chart&&this.chart.columnsContainer?this.chart.columnsContainer:this._columnsContainer},enumerable:!0,configurable:!0}),e.prototype.validateDataElementReal=function(e){t.prototype.validateDataElementReal.call(this,e),e.column&&(e.column.dx=this.dx,e.column.dy=this.dy)},e.prototype.validateDataElements=function(){t.prototype.validateDataElements.call(this),this.chart&&this.chart.invalidateLayout()},e.prototype.createColumnTemplate=function(){return new me},Object.defineProperty(e.prototype,"depth",{get:function(){return this.getPropertyValue("depth")},set:function(t){this.setPropertyValue("depth",t,!0),this.columns.template.column3D.depth=t},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"angle",{get:function(){return this.getPropertyValue("angle")},set:function(t){this.setPropertyValue("angle",t),this.columns.template.column3D.angle=t},enumerable:!0,configurable:!0}),e}(le);c.b.registeredClasses.ColumnSeries3D=xe,c.b.registeredClasses.ColumnSeries3DDataItem=fe;var ve=function(t){function e(){var e=t.call(this)||this;return e.className="XYChart3DDataItem",e.applyTheme(),e}return n.c(e,t),e}(G),be=function(t){function e(){var e=t.call(this)||this;e._axisRendererX=pe,e._axisRendererY=ye,e.className="XYChart3D",e.depth=30,e.angle=30;var i=e.seriesContainer.createChild(s.a);return i.shouldClone=!1,i.isMeasured=!1,i.layout="none",e.columnsContainer=i,e.columnsContainer.mask=e.createChild(S.a),e.applyTheme(),e}return n.c(e,t),e.prototype.updateSeriesMasks=function(){if(t.prototype.updateSeriesMasks.call(this),k.isIE()){var e=this.columnsContainer,i=e.mask;e.mask=void 0,e.mask=i}},Object.defineProperty(e.prototype,"depth",{get:function(){return this.getPropertyValue("depth")},set:function(t){this.setPropertyValue("depth",t),this.fixLayout(),this.invalidateDataUsers()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"angle",{get:function(){return this.getPropertyValue("angle")},set:function(t){this.setPropertyValue("angle",t),this.fixLayout(),this.invalidateDataUsers()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"dx3D",{get:function(){return m.cos(this.angle)*this.depth},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"dy3D",{get:function(){return-m.sin(this.angle)*this.depth},enumerable:!0,configurable:!0}),e.prototype.validateLayout=function(){t.prototype.validateLayout.call(this),this.fixColumns()},e.prototype.fixLayout=function(){this.chartContainer.marginTop=-this.dy3D,this.chartContainer.paddingRight=this.dx3D,this.scrollbarX&&(this.scrollbarX.dy=this.dy3D,this.scrollbarX.dx=this.dx3D),this.scrollbarY&&(this.scrollbarY.dy=this.dy3D,this.scrollbarY.dx=this.dx3D),this.fixColumns(),t.prototype.fixLayout.call(this)},e.prototype.fixColumns=function(){var t=this,e=1,i=0;g.each(this.series.iterator(),function(t){t instanceof xe&&(!t.clustered&&i>0&&e++,t.depthIndex=e-1,i++)});var a=0;g.each(this.series.iterator(),function(i){if(i instanceof xe){i.depth=t.depth/e,i.angle=t.angle,i.dx=t.depth/e*m.cos(t.angle)*i.depthIndex,i.dy=-t.depth/e*m.sin(t.angle)*i.depthIndex;var n=!1;(i.baseAxis==i.xAxis&&i.xAxis.renderer.inversed||i.baseAxis==i.yAxis&&i.yAxis.renderer.inversed)&&(n=!0);var r=1;i.columns.each(function(t){t.zIndex=n?1e3*(1e3-r)+a-100*i.depthIndex:1e3*r+a-100*i.depthIndex,r++}),n?a--:a++}}),this.maskColumns()},e.prototype.processConfig=function(e){if(e&&y.hasValue(e.series)&&y.isArray(e.series))for(var i=0,a=e.series.length;i<a;i++)e.series[i].type=e.series[i].type||"ColumnSeries3D";t.prototype.processConfig.call(this,e)},e.prototype.maskColumns=function(){var t=this.plotContainer.pixelWidth,e=this.plotContainer.pixelHeight,i=this.dx3D,a=this.dy3D,n=w.moveTo({x:0,y:0})+w.lineTo({x:i,y:a})+w.lineTo({x:t+i,y:a})+w.lineTo({x:t+i,y:e+a})+w.lineTo({x:t,y:e})+w.lineTo({x:t,y:e})+w.lineTo({x:0,y:e})+w.closePath(),r=this.columnsContainer;r&&r.mask&&(r.mask.path=n)},e}(q);c.b.registeredClasses.XYChart3D=be;var Pe=i("2OXf"),Ae=function(t){function e(){var e=t.call(this)||this;return e.className="Candlestick",e.layout="none",e}return n.c(e,t),e.prototype.createAssets=function(){t.prototype.createAssets.call(this),this.lowLine=this.createChild(Q.a),this.lowLine.shouldClone=!1,this.highLine=this.createChild(Q.a),this.highLine.shouldClone=!1},e.prototype.copyFrom=function(e){t.prototype.copyFrom.call(this,e),this.lowLine&&this.lowLine.copyFrom(e.lowLine),this.highLine&&this.highLine.copyFrom(e.highLine)},e}(se);c.b.registeredClasses.Candlestick=Ae;var Ce=function(t){function e(){var e=t.call(this)||this;return e.values.lowValueX={},e.values.lowValueY={},e.values.highValueX={},e.values.highValueY={},e.className="CandlestickSeriesDataItem",e.applyTheme(),e}return n.c(e,t),Object.defineProperty(e.prototype,"lowValueX",{get:function(){return this.values.lowValueX.value},set:function(t){this.setValue("lowValueX",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"lowValueY",{get:function(){return this.values.lowValueY.value},set:function(t){this.setValue("lowValueY",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"highValueX",{get:function(){return this.values.highValueX.value},set:function(t){this.setValue("highValueX",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"highValueY",{get:function(){return this.values.highValueY.value},set:function(t){this.setValue("highValueY",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"closeValueX",{get:function(){return this.values.valueX.value},set:function(t){this.setValue("valueX",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"closeValueY",{get:function(){return this.values.valueY.value},set:function(t){this.setValue("valueY",t)},enumerable:!0,configurable:!0}),e}(oe),Ie=function(t){function e(){var e=t.call(this)||this;e.className="CandlestickSeries",e.strokeOpacity=1;var i=new W.a,a=i.getFor("positive"),n=i.getFor("negative");return e.dropFromOpenState.properties.fill=n,e.dropFromOpenState.properties.stroke=n,e.riseFromOpenState.properties.fill=a,e.riseFromOpenState.properties.stroke=a,e.applyTheme(),e}return n.c(e,t),e.prototype.applyInternalDefaults=function(){t.prototype.applyInternalDefaults.call(this),y.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Candlestick Series"))},e.prototype.createDataItem=function(){return new Ce},e.prototype.validateDataElementReal=function(e){t.prototype.validateDataElementReal.call(this,e),this.validateCandlestick(e)},e.prototype.validateCandlestick=function(t){var e=t.column;if(e){var i=e.lowLine,a=e.highLine;if(this.baseAxis==this.xAxis){var n=e.pixelWidth/2;i.x=n,a.x=n;var r=t.getWorkingValue(this.yOpenField),s=t.getWorkingValue(this.yField),o=this.yAxis.getY(t,this.yOpenField),l=this.yAxis.getY(t,this.yField),h=this.yAxis.getY(t,this.yLowField),u=this.yAxis.getY(t,this.yHighField),c=e.pixelY;i.y1=h-c,a.y1=u-c,r<s?(i.y2=o-c,a.y2=l-c):(i.y2=l-c,a.y2=o-c)}if(this.baseAxis==this.yAxis){var d=e.pixelHeight/2;i.y=d,a.y=d;var p=t.getWorkingValue(this.xOpenField),y=t.getWorkingValue(this.xField),m=this.xAxis.getX(t,this.xOpenField),f=this.xAxis.getX(t,this.xField),x=this.xAxis.getX(t,this.xLowField),v=this.xAxis.getX(t,this.xHighField),b=e.pixelX;i.x1=x-b,a.x1=v-b,p<y?(i.x2=m-b,a.x2=f-b):(i.x2=f-b,a.x2=m-b)}g.each(this.axisRanges.iterator(),function(e){var n=t.rangesColumns.getKey(e.uid);if(n){var r=n.lowLine;r.x=i.x,r.y=i.y,r.x1=i.x1,r.x2=i.x2,r.y1=i.y1,r.y2=i.y2;var s=n.highLine;s.x=a.x,s.y=a.y,s.x1=a.x1,s.x2=a.x2,s.y1=a.y1,s.y2=a.y2}})}},Object.defineProperty(e.prototype,"xLowField",{get:function(){return this._xLowField},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"yLowField",{get:function(){return this._yLowField},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"xHighField",{get:function(){return this._xHighField},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"yHighField",{get:function(){return this._yHighField},enumerable:!0,configurable:!0}),e.prototype.defineFields=function(){if(t.prototype.defineFields.call(this),this.baseAxis==this.xAxis){var e=k.capitalize(this.yAxis.axisFieldName);this._yLowField="low"+e+"Y",this._yHighField="high"+e+"Y"}if(this.baseAxis==this.yAxis){var i=k.capitalize(this.xAxis.axisFieldName);this._xLowField="low"+i+"X",this._xHighField="high"+i+"X"}this.addValueField(this.xAxis,this._xValueFields,this._xLowField),this.addValueField(this.xAxis,this._xValueFields,this._xHighField),this.addValueField(this.yAxis,this._yValueFields,this._yLowField),this.addValueField(this.yAxis,this._yValueFields,this._yHighField)},e.prototype.createLegendMarker=function(t){var e=t.pixelWidth,i=t.pixelHeight;t.removeChildren();var a,n,r=t.createChild(Ae);r.shouldClone=!1,r.copyFrom(this.columns.template);var s=r.lowLine,o=r.highLine;this.baseAxis==this.yAxis?(a=e/3,n=i,s.y=i/2,o.y=i/2,s.x2=e/3,o.x2=e/3,o.x=e/3*2,r.column.x=e/3):(a=e,n=i/3,s.x=e/2,o.x=e/2,s.y2=i/3,o.y2=i/3,o.y=i/3*2,r.column.y=i/3),r.width=a,r.height=n,f.copyProperties(this,t,S.b),f.copyProperties(this.columns.template,r,S.b),r.stroke=this.riseFromOpenState.properties.stroke,r.fill=r.stroke;var l=t.dataItem;l.color=r.fill,l.colorOrig=r.fill},e.prototype.createColumnTemplate=function(){return new Ae},e}(le);c.b.registeredClasses.CandlestickSeries=Ie,c.b.registeredClasses.CandlestickSeriesDataItem=Ce;var De=function(t){function e(){var e=t.call(this)||this;return e.className="OHLC",e.layout="none",e}return n.c(e,t),e.prototype.createAssets=function(){this.openLine=this.createChild(Q.a),this.openLine.shouldClone=!1,this.highLowLine=this.createChild(Q.a),this.highLowLine.shouldClone=!1,this.closeLine=this.createChild(Q.a),this.closeLine.shouldClone=!1},e.prototype.copyFrom=function(e){t.prototype.copyFrom.call(this,e),this.openLine&&this.openLine.copyFrom(e.openLine),this.highLowLine&&this.highLowLine.copyFrom(e.highLowLine),this.closeLine&&this.closeLine.copyFrom(e.closeLine)},e}(Ae);c.b.registeredClasses.OHLC=De;var _e=function(t){function e(){var e=t.call(this)||this;return e.className="OHLCSeriesDataItem",e.applyTheme(),e}return n.c(e,t),e}(Ce),Te=function(t){function e(){var e=t.call(this)||this;return e.className="OHLCSeries",e.applyTheme(),e}return n.c(e,t),e.prototype.applyInternalDefaults=function(){t.prototype.applyInternalDefaults.call(this),y.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("OHLC Series"))},e.prototype.createDataItem=function(){return new _e},e.prototype.validateCandlestick=function(t){var e=t.column;if(e){var i=e.openLine,a=e.highLowLine,n=e.closeLine;if(this.baseAxis==this.xAxis){var r=e.pixelWidth/2;a.x=r,t.getWorkingValue(this.yOpenField),t.getWorkingValue(this.yField);var s=this.yAxis.getY(t,this.yOpenField),o=this.yAxis.getY(t,this.yField),l=this.yAxis.getY(t,this.yLowField),h=this.yAxis.getY(t,this.yHighField),u=e.pixelY;i.y1=s-u,i.y2=s-u,i.x1=0,i.x2=r,n.y1=o-u,n.y2=o-u,n.x1=r,n.x2=2*r,a.y1=h-u,a.y2=l-u}if(this.baseAxis==this.yAxis){var c=e.pixelHeight/2;a.y=c,t.getWorkingValue(this.xOpenField),t.getWorkingValue(this.xField);var d=this.xAxis.getX(t,this.xOpenField),p=this.xAxis.getX(t,this.xField),y=this.xAxis.getX(t,this.xLowField),m=this.xAxis.getX(t,this.xHighField),f=e.pixelX;i.x1=d-f,i.x2=d-f,i.y1=c,i.y2=2*c,n.x1=p-f,n.x2=p-f,n.y1=0,n.y2=c,a.x1=m-f,a.x2=y-f}g.each(this.axisRanges.iterator(),function(e){var r=t.rangesColumns.getKey(e.uid);if(r){var s=r.openLine;s.x=i.x,s.y=i.y,s.x1=i.x1,s.x2=i.x2,s.y1=i.y1,s.y2=i.y2;var o=r.closeLine;o.x=n.x,o.y=n.y,o.x1=n.x1,o.x2=n.x2,o.y1=n.y1,o.y2=n.y2;var l=r.highLowLine;l.x=a.x,l.y=a.y,l.x1=a.x1,l.x2=a.x2,l.y1=a.y1,l.y2=a.y2}})}},e.prototype.createLegendMarker=function(t){var e=t.pixelWidth,i=t.pixelHeight;t.removeChildren();var a,n,r=t.createChild(De);r.shouldClone=!1,r.copyFrom(this.columns.template);var s=r.openLine,o=r.closeLine,l=r.highLowLine;this.baseAxis==this.yAxis?(a=e/3,n=i,l.y=i/2,l.x2=e,s.x=e/3*2,s.y2=i/2,o.x=e/3,o.y2=i,o.y1=i/2):(a=e,n=i/3,l.x=e/2,l.y2=i,s.y=i/3*2,s.x2=e/2,o.y=i/3,o.x2=e,o.x1=e/2),r.width=a,r.height=n,f.copyProperties(this,t,S.b),f.copyProperties(this.columns.template,r,S.b),r.stroke=this.riseFromOpenState.properties.stroke;var h=t.dataItem;h.color=r.stroke,h.colorOrig=r.stroke},e.prototype.createColumnTemplate=function(){return new De},e}(Ie);c.b.registeredClasses.OHLCSeries=Te,c.b.registeredClasses.OHLCSeriesDataItem=_e;var Ve=function(t){function e(){var e=t.call(this)||this;return e.className="StepLineSeriesSegment",e}return n.c(e,t),e.prototype.drawSegment=function(t,e,i,a,n,r){if(t.length>0&&e.length>0)if(n){var s=w.moveTo(t[0]);if(t.length>0)for(var o=1;o<t.length;o++){var l=t[o];o/2==Math.round(o/2)?s+=w.moveTo(l):s+=w.lineTo(l)}this.strokeSprite.path=s,(this.fillOpacity>0||this.fillSprite.fillOpacity>0)&&(s=w.moveTo(t[0])+w.polyline(t),s+=w.lineTo(e[0])+w.polyline(e),s+=w.lineTo(t[0]),s+=w.closePath(),this.fillSprite.path=s)}else{s=w.moveTo(t[0])+w.polyline(t);this.strokeSprite.path=s,(this.fillOpacity>0||this.fillSprite.fillOpacity>0)&&(s+=w.lineTo(e[0])+w.polyline(e),s+=w.lineTo(t[0]),s+=w.closePath(),this.fillSprite.path=s)}},e}(J);c.b.registeredClasses.StepLineSeriesSegment=Ve;var Se=function(t){function e(){var e=t.call(this)||this;return e.className="StepLineSeriesDataItem",e.applyTheme(),e}return n.c(e,t),e}(it),Fe=function(t){function e(){var e=t.call(this)||this;return e.className="StepLineSeries",e.applyTheme(),e.startLocation=0,e.endLocation=1,e}return n.c(e,t),e.prototype.createDataItem=function(){return new Se},e.prototype.addPoints=function(t,e,i,a,n){var r=this.startLocation,s=this.endLocation,o=this.xAxis.getX(e,i,r),l=this.yAxis.getY(e,a,r),h=this.xAxis.getX(e,i,s),u=this.yAxis.getY(e,a,s);if(o=m.fitToRange(o,-2e4,2e4),l=m.fitToRange(l,-2e4,2e4),h=m.fitToRange(h,-2e4,2e4),u=m.fitToRange(u,-2e4,2e4),!this.noRisers&&this.connect&&t.length>1){var c=t[t.length-1];this.baseAxis==this.xAxis&&(n?t.push({x:c.x,y:u}):t.push({x:o,y:c.y})),this.baseAxis==this.yAxis&&(n?t.push({x:h,y:c.y}):t.push({x:c.x,y:l}))}var d={x:o,y:l},p={x:h,y:u};n?t.push(p,d):t.push(d,p)},e.prototype.drawSegment=function(t,e,i){var a=!1;this.yAxis==this.baseAxis&&(a=!0),t.drawSegment(e,i,this.tensionX,this.tensionY,this.noRisers,a)},e.prototype.createSegment=function(){return new Ve},Object.defineProperty(e.prototype,"noRisers",{get:function(){return this.getPropertyValue("noRisers")},set:function(t){this.setPropertyValue("noRisers",t,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"startLocation",{get:function(){return this.getPropertyValue("startLocation")},set:function(t){this.setPropertyValue("startLocation",t,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"endLocation",{get:function(){return this.getPropertyValue("endLocation")},set:function(t){this.setPropertyValue("endLocation",t,!0)},enumerable:!0,configurable:!0}),e}(at);c.b.registeredClasses.StepLineSeries=Fe,c.b.registeredClasses.StepLineSeriesDataItem=Se;var ke=function(t){function e(){var e=t.call(this)||this;return e.className="RadarColumn",e}return n.c(e,t),e.prototype.createAssets=function(){this.radarColumn=this.createChild(te.a),this.radarColumn.shouldClone=!1,this.radarColumn.strokeOpacity=void 0,this.column=this.radarColumn},e.prototype.copyFrom=function(e){t.prototype.copyFrom.call(this,e),this.radarColumn&&this.radarColumn.copyFrom(e.radarColumn)},e.prototype.getTooltipX=function(){var t=this.getPropertyValue("tooltipX");return y.isNumber(t)||(t=this.radarColumn.tooltipX),t},e.prototype.getTooltipY=function(){var t=this.getPropertyValue("tooltipX");return y.isNumber(t)||(t=this.radarColumn.tooltipY),t},e}(se);c.b.registeredClasses.RadarColumn=ke;var Oe=function(t){function e(){var e=t.call(this)||this;return e.className="ColumnSeriesDataItem",e.applyTheme(),e}return n.c(e,t),e}(oe),we=function(t){function e(){var e=t.call(this)||this;return e.className="RadarColumnSeries",e.applyTheme(),e}return n.c(e,t),e.prototype.createColumnTemplate=function(){return new ke},e.prototype.validate=function(){this.chart.invalid&&this.chart.validate(),t.prototype.validate.call(this)},e.prototype.validateDataElementReal=function(t){var e,i,a,n,r=this,s=this.chart.startAngle,o=this.chart.endAngle,l=this.yField,h=this.yOpenField,u=this.xField,c=this.xOpenField,d=this.getStartLocation(t),p=this.getEndLocation(t),x=(o-s)/(this.dataItems.length*(this.end-this.start)),v=this.columns.template.percentWidth;y.isNaN(v)&&(v=100);var b=m.round((p-d)*(1-v/100)/2,5);if(d+=b,p-=b,this.baseAxis==this.xAxis?(a=m.getDistance({x:this.yAxis.getX(t,l,t.locations[l],"valueY"),y:this.yAxis.getY(t,l,t.locations[l],"valueY")}),n=m.getDistance({x:this.yAxis.getX(t,h,t.locations[h],"valueY"),y:this.yAxis.getY(t,h,t.locations[h],"valueY")}),e=this.xAxis.getAngle(t,c,d,"valueX"),i=this.xAxis.getAngle(t,u,p,"valueX"),s+=d*x,o-=(1-p)*x):(a=m.getDistance({x:this.yAxis.getX(t,l,d,"valueY"),y:this.yAxis.getY(t,l,d,"valueY")}),n=m.getDistance({x:this.yAxis.getX(t,h,p,"valueY"),y:this.yAxis.getY(t,h,p,"valueY")}),e=this.xAxis.getAngle(t,u,t.locations[u],"valueX"),i=this.xAxis.getAngle(t,c,t.locations[c],"valueX")),i<e){var P=i;i=e,e=P}e=m.fitToRange(e,s,o),i=m.fitToRange(i,s,o);var A=t.column;A||(A=this.columns.create(),t.column=A,f.forceCopyProperties(this.columns.template,A,S.b),t.addSprite(A),this.setColumnStates(A));var C=A.radarColumn;C.startAngle=e;var I=i-e;I>0?(C.arc=I,C.radius=a,C.innerRadius=n,A.__disabled=!1,A.parent=this.columnsContainer,g.each(this.axisRanges.iterator(),function(i){var s=t.rangesColumns.getKey(i.uid);s||(s=r.columns.create(),f.forceCopyProperties(r.columns.template,s,S.b),f.copyProperties(i.contents,s,S.b),s.dataItem&&O.remove(s.dataItem.sprites,s),t.addSprite(s),r.setColumnStates(s),t.rangesColumns.setKey(i.uid,s));var o=s.radarColumn;o.startAngle=e,o.arc=I,o.radius=a,o.innerRadius=n,o.invalid&&o.validate(),s.__disabled=!1,s.parent=i.contents})):this.disableUnusedColumns(t)},e.prototype.getPoint=function(t,e,i,a,n,r,s){r||(r="valueX"),s||(s="valueY");var o=this.yAxis.getX(t,i,n,s),l=this.yAxis.getY(t,i,n,s),h=m.getDistance({x:o,y:l});0==h&&(h=1e-5);var u=this.xAxis.getAngle(t,e,a,r);return{x:h*m.cos(u),y:h*m.sin(u)}},e.prototype.getMaskPath=function(){var t=this.yAxis.renderer;return w.arc(t.startAngle,t.endAngle-t.startAngle,t.pixelRadius,t.pixelInnerRadius)},e}(le);c.b.registeredClasses.RadarColumnSeries=we,c.b.registeredClasses.RadarColumnSeriesDataItem=Oe;var Re=i("AC2I"),Le=function(t){function e(){var e=t.call(this)||this;return e.slice=e.createChild(S.a),e.slice.shouldClone=!1,e.slice.setElement(e.paper.add("path")),e.slice.isMeasured=!1,e.orientation="vertical",e.bottomWidth=Object(Y.c)(100),e.topWidth=Object(Y.c)(100),e.isMeasured=!1,e.width=10,e.height=10,e.expandDistance=0,e.className="FunnelSlice",e.applyTheme(),e}return n.c(e,t),e.prototype.draw=function(){t.prototype.draw.call(this);var e=this.pixelPaddingTop,i=this.pixelPaddingBottom,a=this.pixelPaddingRight,n=this.pixelPaddingLeft,r=this.pixelWidth-a-n,s=this.pixelHeight-e-i,o=this.expandDistance,l="";if("vertical"==this.orientation){var h={x:(r-(f=k.relativeToValue(this.topWidth,r)))/2+n,y:e},u={x:(r+f)/2+n,y:e},c={x:(r+(x=k.relativeToValue(this.bottomWidth,r)))/2+n,y:e+s},d={x:(r-x)/2+n,y:e+s},p={x:u.x+(c.x-u.x)/2+o*s,y:u.y+.5*s},y={x:h.x+(d.x-h.x)/2-o*s,y:h.y+.5*s},g=w.lineTo(c),m="";0!=o&&(g=w.quadraticCurveTo(c,p),m=w.quadraticCurveTo(h,y)),l=w.moveTo(h)+w.lineTo(u)+g+w.lineTo(d)+m,this.tickPoint={x:u.x+(c.x-u.x)/2,y:u.y+(c.y-u.y)/2}}else{var f,x,v={x:n,y:(s-(f=k.relativeToValue(this.topWidth,s)))/2+e},b={x:n,y:(s+f)/2+e},P={x:n+r,y:(s-(x=k.relativeToValue(this.bottomWidth,s)))/2+e},A={x:n+r,y:(s+x)/2+e};p={y:v.y+(P.y-v.y)/2-o*r,x:v.x+.5*r},y={y:b.y+(A.y-b.y)/2+o*r,x:b.x+.5*r},g=w.lineTo(P),m="";0!=o&&(g=w.quadraticCurveTo(P,p),m=w.quadraticCurveTo(b,y)),l=w.moveTo(b)+w.lineTo(v)+g+w.lineTo(A)+m,this.tickPoint={y:b.y+(A.y-b.y)/2,x:b.x+(A.x-b.x)/2}}this.slice.path=l,this.invalidateLayout()},e.prototype.getPoint=function(t,e){var i=this.pixelPaddingTop,a=this.pixelPaddingBottom,n=this.pixelPaddingRight,r=this.pixelPaddingLeft,s=this.pixelWidth-n-r,o=this.pixelHeight-i-a;if("vertical"==this.orientation){var l={x:(s-(d=k.relativeToValue(this.topWidth,s)))/2+r,y:i},h={x:(s+d)/2+r,y:i},u={x:(s+(p=k.relativeToValue(this.bottomWidth,s)))/2+r,y:i+o},c=l.x+({x:(s-p)/2+r,y:i+o}.x-l.x)*e;return{x:c+(h.x+(u.x-h.x)*e-c)*t,y:h.y+(u.y-h.y)*e}}var d,p,y={x:r,y:(o-(d=k.relativeToValue(this.topWidth,o)))/2+i},g={x:r,y:(o+d)/2+i},m={x:r+s,y:(o-(p=k.relativeToValue(this.bottomWidth,o)))/2+i},f=y.y+(m.y-y.y)*t;return{y:f+(g.y+({x:r+s,y:(o+p)/2+i}.y-g.y)*t-f)*e,x:y.x+(m.x-y.x)*t}},Object.defineProperty(e.prototype,"bottomWidth",{get:function(){return this.getPropertyValue("bottomWidth")},set:function(t){this.setPercentProperty("bottomWidth",t,!0,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"topWidth",{get:function(){return this.getPropertyValue("topWidth")},set:function(t){this.setPercentProperty("topWidth",t,!0,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"orientation",{get:function(){return this.getPropertyValue("orientation")},set:function(t){this.setPropertyValue("orientation",t,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"expandDistance",{get:function(){return this.getPropertyValue("expandDistance")},set:function(t){this.setPropertyValue("expandDistance",t,!0)},enumerable:!0,configurable:!0}),e.prototype.copyFrom=function(e){t.prototype.copyFrom.call(this,e),this.slice&&this.slice.copyFrom(e.slice)},e}(s.a);c.b.registeredClasses.FunnelSlice=Le;var Xe=i("qzbU"),Ye=function(t){function e(){var e=t.call(this)||this;return e._label=new F.d,e._slice=new F.d,e.className="FunnelTick",e.element=e.paper.add("path"),e._disposers.push(e._label),e._disposers.push(e._slice),e.setPropertyValue("locationX",0),e.setPropertyValue("locationY",0),e.applyTheme(),e}return n.c(e,t),e.prototype.draw=function(){t.prototype.draw.call(this);var e=this.slice,i=e.getPoint(this.locationX,this.locationY);if(i){var a=this.label,n=e.dataItem.component;if("vertical"==n.orientation){var r=a.pixelX,s=a.pixelY;n.labelsOpposite||(r+=a.maxRight);var o=k.spritePointToSprite(i,e,this.parent),l=k.spritePointToSprite({x:r,y:s},a.parent,this.parent);this.path=w.moveTo(o)+w.lineTo(l)}else{r=a.pixelX,s=a.pixelY;n.labelsOpposite||(s+=a.maxBottom);o=k.spritePointToSprite(i,e,this.parent),l=k.spritePointToSprite({x:r,y:s},a.parent,this.parent);this.path=w.moveTo(o)+w.lineTo(l)}}},Object.defineProperty(e.prototype,"slice",{get:function(){return this._slice.get()},set:function(t){this._slice.set(t,new F.c([t.events.on("transformed",this.invalidate,this,!1),t.events.on("validated",this.invalidate,this,!1)]))},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"label",{get:function(){return this._label.get()},set:function(t){this._label.set(t,t.events.on("transformed",this.invalidate,this,!1))},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"locationX",{get:function(){return this.getPropertyValue("locationX")},set:function(t){this.setPropertyValue("locationX",t,!1,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"locationY",{get:function(){return this.getPropertyValue("locationY")},set:function(t){this.setPropertyValue("locationY",t,!1,!0)},enumerable:!0,configurable:!0}),e}(Xe.a);c.b.registeredClasses.FunnelTick=Ye;var je=function(t){function e(){var e=t.call(this)||this;return e.className="FunnelSeriesDataItem",e.applyTheme(),e}return n.c(e,t),Object.defineProperty(e.prototype,"sliceLink",{get:function(){var t=this;if(!this._sliceLink){var e=this.component.sliceLinks.create();this._sliceLink=e,this._disposers.push(e),e.parent=this.component.slicesContainer,this._disposers.push(new F.b(function(){t.component&&t.component.sliceLinks.removeValue(e)})),this.addSprite(e),e.visible=this.visible}return this._sliceLink},enumerable:!0,configurable:!0}),e}(Re.b),Ne=function(t){function e(){var e=t.call(this)||this;return e._nextY=0,e.className="FunnelSeries",e.orientation="vertical",e.width=Object(Y.c)(100),e.height=Object(Y.c)(100),e.slicesContainer.width=Object(Y.c)(100),e.slicesContainer.height=Object(Y.c)(100),e._disposers.push(e.slicesContainer.events.on("maxsizechanged",e.invalidateDataItems,e,!1)),e.labelsOpposite=!0,e.labelsContainer.layout="absolute",e.bottomRatio=0,e.applyTheme(),e}return n.c(e,t),e.prototype.createSlice=function(){return new Le},e.prototype.createTick=function(){return new Ye},e.prototype.applyInternalDefaults=function(){t.prototype.applyInternalDefaults.call(this),y.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Funnel Series"))},e.prototype.createDataItem=function(){return new je},e.prototype.initSlice=function(t){t.isMeasured=!1,t.defaultState.properties.scale=1,t.observe("scale",this.handleSliceScale,this),t.observe(["dx","dy","x","y"],this.handleSliceMove,this),t.tooltipText="{category}: {value.percent.formatNumber('#.#')}% ({value.value})",t.states.create("hover").properties.expandDistance=.2},e.prototype.initLabel=function(e){t.prototype.initLabel.call(this,e),e.verticalCenter="middle",e.horizontalCenter="middle",e.isMeasured=!0,e.padding(5,5,5,5)},e.prototype.validate=function(){t.prototype.validate.call(this),this._nextY=0},e.prototype.validateDataElements=function(){var e=this.slicesContainer,i=this.labelsContainer,a=this.labels.template;this.alignLabels?(a.interactionsEnabled=!0,e.isMeasured=!0,i.isMeasured=!0):(a.interactionsEnabled=!1,e.isMeasured=!1,i.isMeasured=!1);var n=0,r=0;this.dataItems.each(function(t){y.hasValue(t.value)&&(r++,t.value>0?n+=t.getWorkingValue("value")/t.value:n+=1)}),this._total=1/r*n,this._count=r,t.prototype.validateDataElements.call(this),this.arrangeLabels()},e.prototype.getNextValue=function(t){var e=t.index,i=t.getWorkingValue("value");if(e<this.dataItems.length-1){var a=this.dataItems.getIndex(e+1);if(i=a.getWorkingValue("value"),!a.visible||a.isHiding)return this.getNextValue(a)}return i},e.prototype.formDataElement=function(){},e.prototype.validateDataElement=function(e){if(y.hasValue(e.value)){var i=e.slice;i.orientation=this.orientation;var a=e.sliceLink;a.orientation=this.orientation;var n=e.tick,r=e.label;n.slice=i,n.label=r,this.decorateSlice(e),a.fill=i.fill,e.index==this.dataItems.length-1&&(a.disabled=!0),t.prototype.validateDataElement.call(this,e)}},e.prototype.decorateSlice=function(t){var e=t.slice,i=t.sliceLink,a=t.label,n=t.tick,r=this.slicesContainer.innerWidth,s=this.slicesContainer.innerHeight,o=this.getNextValue(t),l=t.getWorkingValue("value"),h=this.bottomRatio,u=1;if(t.value>0&&(u=l/t.value),"vertical"==this.orientation){var c=i.pixelHeight*u;s+=c,e.topWidth=l/this.dataItem.values.value.high*r,e.bottomWidth=(l-(l-o)*h)/this.dataItem.values.value.high*r,i.topWidth=e.bottomWidth,i.bottomWidth=(l-(l-o))/this.dataItem.values.value.high*r,e.y=this._nextY,e.height=m.max(0,s/this._count*u/this._total-c),e.x=r/2,this.alignLabels?a.x=void 0:a.x=e.x,a.y=e.pixelY+e.pixelHeight*n.locationY,this._nextY+=e.pixelHeight+c,i.y=this._nextY-c,i.x=e.x}else{var d=i.pixelWidth*u;r+=d,e.topWidth=l/this.dataItem.values.value.high*s,e.bottomWidth=(l-(l-o)*h)/this.dataItem.values.value.high*s,i.topWidth=e.bottomWidth,i.bottomWidth=(l-(l-o))/this.dataItem.values.value.high*s,e.x=this._nextY,e.width=r/this._count*u*1/this._total-d,e.y=s/2,this.alignLabels?a.y=this.labelsContainer.measuredHeight:a.y=e.y,a.x=e.pixelX+e.pixelWidth*n.locationX,this._nextY+=e.pixelWidth+d,i.x=this._nextY-d,i.y=e.y}},e.prototype.arrangeLabels=function(){if(this.alignLabels){var t=this.labels.length;if(t>1){var e=this.labels.getIndex(t-1),i=e.pixelY,a=e.pixelX;if(t>1){for(var n=t-2;n>=0;n--){(r=this.labels.getIndex(n)).visible&&(r.invalid&&r.validate(),"vertical"==this.orientation?r.pixelY+r.measuredHeight>i&&(r.y=i-r.measuredHeight):r.pixelX+r.measuredWidth>a&&(r.x=a-r.measuredWidth),i=r.pixelY,a=r.pixelX)}i=0,a=0;for(n=0;n<t;n++){var r;(r=this.labels.getIndex(n)).visible&&(r.invalid&&r.validate(),"vertical"==this.orientation?r.pixelY<i&&(r.y=i):r.pixelX<a&&(r.x=a),i+=r.measuredHeight,a+=r.measuredWidth)}}}}},e.prototype.positionBullet=function(e){t.prototype.positionBullet.call(this,e);var i=e.dataItem.slice,a=e.locationX;y.isNumber(a)||(a=.5);var n=e.locationY;y.isNumber(n)||(n=1),e.x=i.pixelX+i.measuredWidth*a,e.y=i.pixelY+i.measuredHeight*n},Object.defineProperty(e.prototype,"orientation",{get:function(){return this.getPropertyValue("orientation")},set:function(t){this.setPropertyValue("orientation",t)&&(this.labelsOpposite=this.labelsOpposite,this.invalidate(),"vertical"==t?(this.ticks.template.locationX=1,this.ticks.template.locationY=.5,this.labels.template.rotation=0,this.layout="horizontal"):(this.ticks.template.locationX=.5,this.ticks.template.locationY=1,this.labels.template.rotation=-90,this.layout="vertical"))},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"bottomRatio",{get:function(){return this.getPropertyValue("bottomRatio")},set:function(t){this.setPropertyValue("bottomRatio",t)&&this.invalidate()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"sliceLinks",{get:function(){if(!this._sliceLinks){var t=new Le;t.applyOnClones=!0,t.fillOpacity=.5,t.expandDistance=-.3,t.hiddenState.properties.opacity=0,this._disposers.push(t),this._sliceLinks=new o.e(t),this._disposers.push(new o.c(this._sliceLinks))}return this._sliceLinks},enumerable:!0,configurable:!0}),e.prototype.show=function(e){var i=this,a=this.startIndex,n=this.endIndex,r=this.defaultState.transitionDuration;y.isNumber(e)&&(r=e);var s=0;return g.each(g.indexed(this.dataItems.iterator()),function(t){var e=t[0],o=t[1];i.sequencedInterpolation&&(s=i.sequencedInterpolationDelay*e+r*(e-a)/(n-a)),o.show(r,s,["value"])}),t.prototype.show.call(this,e)},e.prototype.hide=function(e){var i=this,a=["value"],n=this.startIndex,r=this.endIndex,s=0,o=this.hiddenState.transitionDuration;y.isNumber(e)&&(o=e),g.each(g.indexed(this.dataItems.iterator()),function(t){var e=t[0],l=t[1];i.sequencedInterpolation&&(s=i.sequencedInterpolationDelay*e+o*(e-n)/(r-n)),l.hide(o,s,0,a)});var l=t.prototype.hide.call(this,e);return l&&!l.isFinished()&&l.delay(s),l},e.prototype.setAlignLabels=function(e){t.prototype.setAlignLabels.call(this,e),this.ticks.template.disabled=!e;var i=this.labelsContainer;i&&(e?(i.height=void 0,i.width=void 0,i.margin(10,10,10,10)):(i.width=Object(Y.c)(100),i.height=Object(Y.c)(100))),this.labelsOpposite=this.labelsOpposite},Object.defineProperty(e.prototype,"labelsOpposite",{get:function(){return this.getPropertyValue("labelsOpposite")},set:function(t){this.setPropertyValue("labelsOpposite",t);var e=this.labels.template,i="none",a="none";this.alignLabels?t?(this.labelsContainer.toFront(),"vertical"==this.orientation?(this.ticks.template.locationX=1,e.horizontalCenter="left",i="right"):(this.ticks.template.locationY=1,e.horizontalCenter="right",a="bottom")):(this.labelsContainer.toBack(),"vertical"==this.orientation?(this.ticks.template.locationX=0,i="left"):(a="top",this.ticks.template.locationY=0)):"vertical"==this.orientation?i="center":a="middle",e.align=i,e.valign=a,this.validateLayout(),this.ticks.each(function(t){t.invalidate()}),this.invalidateDataItems()},enumerable:!0,configurable:!0}),e}(Re.a);c.b.registeredClasses.FunnelSeries=Ne,c.b.registeredClasses.FunnelSeriesDataItem=je;var Me=function(t){function e(){var e=t.call(this)||this;return e.className="PyramidSeriesDataItem",e.applyTheme(),e}return n.c(e,t),e}(je),We=function(t){function e(){var e=t.call(this)||this;return e.className="PyramidSeries",e.topWidth=Object(Y.c)(0),e.bottomWidth=Object(Y.c)(100),e.pyramidHeight=Object(Y.c)(100),e.valueIs="area",e.sliceLinks.template.width=0,e.sliceLinks.template.height=0,e.applyTheme(),e}return n.c(e,t),e.prototype.applyInternalDefaults=function(){t.prototype.applyInternalDefaults.call(this),y.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Pyramid Series"))},e.prototype.createDataItem=function(){return new Me},e.prototype.validate=function(){t.prototype.validate.call(this),this._nextWidth=void 0},e.prototype.getNextValue=function(t){var e=t.index,i=t.getWorkingValue("value");e<this.dataItems.length-1&&(i=this.dataItems.getIndex(e+1).getWorkingValue("value"));return 0==i&&(i=1e-6),i},e.prototype.validateDataElements=function(){var e=this,i=this.slicesContainer.innerWidth,a=this.slicesContainer.innerHeight;if(this.dataItems.each(function(t){var n=t.getWorkingValue("value")/t.value,r=t.sliceLink;"vertical"==e.orientation?a-=r.pixelHeight*n:i-=r.pixelWidth*n}),this._pyramidHeight=k.relativeToValue(this.pyramidHeight,a),this._pyramidWidth=k.relativeToValue(this.pyramidHeight,i),"vertical"==this.orientation){var n=(a-this._pyramidHeight)/2;this.slicesContainer.y=n,this.labelsContainer.y=n,this.ticksContainer.y=n}else{var r=(i-this._pyramidWidth)/2;this.slicesContainer.x=r,this.labelsContainer.x=r,this.ticksContainer.x=r}t.prototype.validateDataElements.call(this)},e.prototype.decorateSlice=function(t){var e=this.dataItem.values.value.sum;if(0!=e){var i=t.slice,a=t.sliceLink,n=t.label,r=t.tick;this.getNextValue(t);var s=t.getWorkingValue("value");0==s&&(s=1e-6);var o=this._pyramidWidth,l=this._pyramidHeight,h=this.slicesContainer.innerWidth,u=this.slicesContainer.innerHeight,c=a.pixelWidth,d=a.pixelHeight;if("vertical"==this.orientation){var p=k.relativeToValue(this.topWidth,h);y.isNumber(this._nextWidth)||(this._nextWidth=p);var g=k.relativeToValue(this.bottomWidth,h),m=this._nextWidth,f=Math.atan2(l,p-g);0==(A=Math.tan(Math.PI/2-f))&&(A=1e-8);var x=void 0,v=void 0;if("area"==this.valueIs){var b=(p+g)/2*l*s/e,P=Math.abs(m*m-2*b*A);v=(2*b-(x=(m-Math.sqrt(P))/A)*m)/x}else{v=m-(x=l*s/this.dataItem.values.value.sum)*A}i.height=x,i.width=h,i.bottomWidth=v,i.topWidth=m,a.topWidth=i.bottomWidth,a.bottomWidth=i.bottomWidth,i.y=this._nextY,this.alignLabels?n.x=0:n.x=h/2,n.y=i.pixelY+i.pixelHeight*r.locationY+i.dy,this._nextY+=i.pixelHeight+d*s/t.value,a.y=this._nextY-d,a.x=h/2}else{p=k.relativeToValue(this.topWidth,u);y.isNumber(this._nextWidth)||(this._nextWidth=p);var A;g=k.relativeToValue(this.bottomWidth,u),m=this._nextWidth,f=Math.atan2(o,p-g);0==(A=Math.tan(Math.PI/2-f))&&(A=1e-8);var C=void 0;v=void 0;if("area"==this.valueIs)v=(2*(b=(p+g)/2*o*s/this.dataItem.values.value.sum)-(C=(m-Math.sqrt(m*m-2*b*A))/A)*m)/C;else v=m-(C=o*s/this.dataItem.values.value.sum)*A;i.width=C,i.height=u,i.bottomWidth=v,i.topWidth=m,a.topWidth=i.bottomWidth,a.bottomWidth=i.bottomWidth,i.x=this._nextY,this.alignLabels?n.y=this.labelsContainer.measuredHeight:n.y=u/2,n.x=i.pixelX+i.pixelWidth*r.locationX+i.dx,this._nextY+=i.pixelWidth+c*s/t.value,a.x=this._nextY-c,a.y=u/2}this._nextWidth=i.bottomWidth}},Object.defineProperty(e.prototype,"topWidth",{get:function(){return this.getPropertyValue("topWidth")},set:function(t){this.setPercentProperty("topWidth",t,!1,!1,10,!1)&&this.invalidate()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"pyramidHeight",{get:function(){return this.getPropertyValue("pyramidHeight")},set:function(t){this.setPercentProperty("pyramidHeight",t,!1,!1,10,!1)&&this.invalidate()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"bottomWidth",{get:function(){return this.getPropertyValue("bottomWidth")},set:function(t){this.setPercentProperty("bottomWidth",t,!1,!1,10,!1)&&this.invalidate()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"valueIs",{get:function(){return this.getPropertyValue("valueIs")},set:function(t){this.setPropertyValue("valueIs",t)&&this.invalidate()},enumerable:!0,configurable:!0}),e}(Ne);c.b.registeredClasses.PyramidSeries=We,c.b.registeredClasses.PyramidSeriesDataItem=Me;var Be=function(t){function e(){var e=t.call(this)||this;return e.className="PictorialStackedSeriesDataItem",e.applyTheme(),e}return n.c(e,t),e}(Me),Ee=function(t){function e(){var e=t.call(this)||this;return e.className="PictorialStackedSeries",e.topWidth=Object(Y.c)(100),e.bottomWidth=Object(Y.c)(100),e.valueIs="height",e.applyTheme(),e.startLocation=0,e.endLocation=1,e._maskSprite=e.slicesContainer.createChild(S.a),e._maskSprite.visible=!1,e._maskSprite.zIndex=100,e._maskSprite.shouldClone=!1,e}return n.c(e,t),e.prototype.validateDataElements=function(){var e=this.slicesContainer.maxWidth,i=this.slicesContainer.maxHeight,a=this._maskSprite,n=a.measuredWidth/a.scale,r=a.measuredHeight/a.scale,s=m.min(i/r,e/n);s==1/0&&(s=1),s=m.max(.001,s);var o=this.startLocation,l=this.endLocation,h=m.min(e,n*s),u=m.min(i,r*s);if(a.scale=s,"vertical"==this.orientation?(this.topWidth=h+4,this.bottomWidth=h+4,this.pyramidHeight=u*(l-o),a.x=e/2,a.y=u/2):(this.topWidth=u+4,this.bottomWidth=u+4,this.pyramidHeight=h*(l-o),a.valign="middle",a.x=h/2,a.y=i/2),a.verticalCenter="middle",a.horizontalCenter="middle",t.prototype.validateDataElements.call(this),"vertical"==this.orientation){var c=(i-u)/2;this.slicesContainer.y=c,this.labelsContainer.y=c,this.ticksContainer.y=c,this.slices.template.dy=o*u}else{var d=(e-h)/2;this.slicesContainer.x=d,this.labelsContainer.x=d,this.ticksContainer.x=d,this.slices.template.dx=o*h}this.slicesContainer.mask=this._maskSprite},e.prototype.applyInternalDefaults=function(){t.prototype.applyInternalDefaults.call(this),y.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Pyramid Series"))},e.prototype.createDataItem=function(){return new Be},Object.defineProperty(e.prototype,"maskSprite",{get:function(){return this._maskSprite},enumerable:!0,configurable:!0}),e.prototype.initSlice=function(e){t.prototype.initSlice.call(this,e);var i=e.states.getKey("hover");i&&(i.properties.expandDistance=0)},Object.defineProperty(e.prototype,"startLocation",{get:function(){return this.getPropertyValue("startLocation")},set:function(t){this.setPropertyValue("startLocation",t)&&this.invalidateDataItems()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"endLocation",{get:function(){return this.getPropertyValue("endLocation")},set:function(t){this.setPropertyValue("endLocation",t)&&this.invalidateDataItems()},enumerable:!0,configurable:!0}),e}(We);c.b.registeredClasses.PictorialStackedSeries=Ee,c.b.registeredClasses.PictorialStackedSeriesDataItem=Be;var He=i("BmDP"),ze=i("ncT3"),Ue=function(t){function e(){var e=t.call(this)||this;return e.className="ConeColumn",e}return n.c(e,t),e.prototype.createAssets=function(){this.coneColumn=this.createChild(ze.a),this.coneColumn.shouldClone=!1,this.column=this.coneColumn},e.prototype.copyFrom=function(e){t.prototype.copyFrom.call(this,e),this.coneColumn&&this.coneColumn.copyFrom(e.coneColumn)},e}(se);c.b.registeredClasses.ConeColumn=Ue;var Ke=function(t){function e(){var e=t.call(this)||this;return e.className="ConeSeriesDataItem",e.applyTheme(),e}return n.c(e,t),e}(oe),Ge=function(t){function e(){var e=t.call(this)||this;return e.className="ConeSeries",e.applyTheme(),e}return n.c(e,t),e.prototype.createColumnTemplate=function(){return new Ue},e.prototype.getMaskPath=function(){var t=0,e=0,i=this.columns.getIndex(0);if(i)return this.baseAxis==this.xAxis?e=i.coneColumn.bottom.radiusY+1:t=i.coneColumn.bottom.radiusY+1,w.rectToPath({x:-t,y:0,width:this.xAxis.axisLength+t,height:this.yAxis.axisLength+e})},e.prototype.validateDataElementReal=function(e){if(t.prototype.validateDataElementReal.call(this,e),e.column){var i=e.column.coneColumn;i.fill=e.column.fill,this.baseAxis==this.yAxis?i.orientation="horizontal":i.orientation="vertical"}},e}(le);c.b.registeredClasses.ConeSeries=Ge,c.b.registeredClasses.ConeSeriesDataItem=Ke;var qe=function(t){function e(){var e=t.call(this)||this;return e.className="CurvedColumn",e}return n.c(e,t),e.prototype.createAssets=function(){this.curvedColumn=this.createChild(S.a),this.curvedColumn.shouldClone=!1,this.setPropertyValue("tension",.7),this.width=Object(Y.c)(120),this.height=Object(Y.c)(120),this.column=this.curvedColumn},e.prototype.draw=function(){t.prototype.draw.call(this);var e,i=this.realWidth,a=this.realHeight,n=this.realX-this.pixelX,r=this.realY-this.pixelY;k.used(this.width);var s=1,o=1;"vertical"==this.orientation?(s=this.tension,e=[{x:0,y:a+r},{x:i/2,y:r},{x:i,y:a+r}]):(o=this.tension,e=[{x:n,y:0},{x:n+i,y:a/2},{x:n,y:a}]);var l=w.moveTo(e[0])+new Z.b(s,o).smooth(e);this.column.path=l},e.prototype.copyFrom=function(e){t.prototype.copyFrom.call(this,e),this.curvedColumn&&this.curvedColumn.copyFrom(e.curvedColumn)},Object.defineProperty(e.prototype,"tension",{get:function(){return this.getPropertyValue("tension")},set:function(t){this.setPropertyValue("tension",t,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"orientation",{get:function(){return this.getPropertyValue("orientation")},set:function(t){this.setPropertyValue("orientation",t,!0)},enumerable:!0,configurable:!0}),e}(se);c.b.registeredClasses.CurvedColumn=qe;var Ze=function(t){function e(){var e=t.call(this)||this;return e.className="CurvedColumnSeriesDataItem",e.applyTheme(),e}return n.c(e,t),e}(oe),Je=function(t){function e(){var e=t.call(this)||this;return e.className="CurvedColumnSeries",e.applyTheme(),e}return n.c(e,t),e.prototype.createColumnTemplate=function(){return new qe},e.prototype.validateDataElementReal=function(e){t.prototype.validateDataElementReal.call(this,e);var i=e.column;(i=e.column)&&(e.column.curvedColumn.fill=e.column.fill,this.baseAxis==this.yAxis?i.orientation="horizontal":i.orientation="vertical")},e}(le);c.b.registeredClasses.CurvedColumnSeries=Je,c.b.registeredClasses.CurvedColumnSeriesDataItem=Ze;var Qe=i("eN1s"),$e=i("TDx+"),ti=i("eAid"),ei=function(t){function e(){var e=t.call(this)||this;return e.className="DurationAxisDataItem",e.applyTheme(),e}return n.c(e,t),e}(l.b),ii=function(t){function e(){var e=t.call(this)||this;return e._baseUnit="second",e.className="DurationAxis",e.setPropertyValue("maxZoomFactor",1e6),e.applyTheme(),e}return n.c(e,t),e.prototype.formatLabel=function(t,e){return this.durationFormatter.format(t,e||this.axisDurationFormat)},e.prototype.adjustMinMax=function(e,i,a,r,s){var o,l,h,u=this.baseUnit;if(this.setPropertyValue("maxPrecision",0),"millisecond"==u||"second"==u||"minute"==u||"hour"==u){r<=1&&(r=1),r=Math.round(r);var c=e,d=i;0===a&&(a=Math.abs(i));var p,y=[60,30,20,15,10,2,1],g=1;"hour"==u&&(y=[24,12,6,4,2,1]);try{for(var f=n.g(y),x=f.next();!x.done;x=f.next()){var v=x.value;if(a/v>r){g=v;break}}}catch(t){l={error:t}}finally{try{x&&!x.done&&(h=f.return)&&h.call(f)}finally{if(l)throw l.error}}var b=Math.ceil((i-e)/g/r),P=Math.log(Math.abs(b))*Math.LOG10E,A=Math.pow(10,Math.floor(P))/10,C=b/A;p=g*(b=m.closest(y,C)*A),this.durationFormatter.getValueUnit(p,this.baseUnit),e=Math.floor(e/p)*p,i=Math.ceil(i/p)*p,s&&((e-=p)<0&&c>=0&&(e=0),(i+=p)>0&&d<=0&&(i=0)),o={min:e,max:i,step:p}}else o=t.prototype.adjustMinMax.call(this,e,i,a,r,s);return this.axisDurationFormat=this.durationFormatter.getFormat(o.step,o.max,this.baseUnit),o},Object.defineProperty(e.prototype,"tooltipDurationFormat",{get:function(){return this._tooltipDurationFormat},set:function(t){this._tooltipDurationFormat=t},enumerable:!0,configurable:!0}),e.prototype.getTooltipText=function(t){var e=m.round(this.positionToValue(t),this._stepDecimalPlaces);return this.adapter.apply("getTooltipText",this.formatLabel(e,this.tooltipDurationFormat))},Object.defineProperty(e.prototype,"baseUnit",{get:function(){return this._baseUnit},set:function(t){this._baseUnit!=t&&(this._baseUnit=t,this.durationFormatter.baseUnit=t,this.invalidate())},enumerable:!0,configurable:!0}),e.prototype.copyFrom=function(e){t.prototype.copyFrom.call(this,e),this.baseUnit=e.baseUnit},e}(l.a);c.b.registeredClasses.DurationAxis=ii,c.b.registeredClasses.DurationAxisDataItem=ei;var ai=function(t){function e(){var e=t.call(this)||this;e.className="CircleBullet";var i=e.createChild(st.a);return i.shouldClone=!1,i.radius=5,i.isMeasured=!1,e.circle=i,e.applyTheme(),e}return n.c(e,t),e.prototype.copyFrom=function(e){t.prototype.copyFrom.call(this,e),this.circle.copyFrom(e.circle)},e}(et.a);c.b.registeredClasses.CircleBullet=ai;var ni=function(t){function e(){var e=t.call(this)||this;return e.className="ErrorBullet",e.errorLine=e.createChild(S.a),e.errorLine.shouldClone=!1,e.width=20,e.height=20,e.strokeOpacity=1,e.isDynamic=!0,e}return n.c(e,t),e.prototype.validatePosition=function(){t.prototype.validatePosition.call(this);var e=this.pixelWidth/2,i=this.pixelHeight/2;this.errorLine.path=w.moveTo({x:-e,y:-i})+w.lineTo({x:e,y:-i})+w.moveTo({x:0,y:-i})+w.lineTo({x:0,y:i})+w.moveTo({x:-e,y:i})+w.lineTo({x:e,y:i})},e.prototype.copyFrom=function(e){t.prototype.copyFrom.call(this,e),this.errorLine.copyFrom(e.errorLine)},e}(et.a);c.b.registeredClasses.ErrorBullet=ni;var ri=i("Y9w3"),si=i("A6AV"),oi=i("Trvg"),li=i("Rnbi"),hi=function(t){function e(){var e=t.call(this)||this;return e.className="NavigationBarDataItem",e.applyTheme(),e}return n.c(e,t),Object.defineProperty(e.prototype,"name",{get:function(){return this.properties.name},set:function(t){this.setProperty("name",t)},enumerable:!0,configurable:!0}),e}(si.a),ui=function(t){function e(){var e=t.call(this)||this;e.className="NavigationBar";var i=new W.a,a=new oi.a;a.valign="middle",a.paddingTop=8,a.paddingBottom=8,e.paddingBottom=2,e.links=new o.e(a),e._disposers.push(new o.c(e.links)),e._disposers.push(a),e._linksIterator=new g.ListIterator(e.links,function(){return e.links.create()}),e._linksIterator.createNewItems=!0;var n=new li.a;n.direction="right",n.width=8,n.height=12,n.fill=i.getFor("alternativeBackground"),n.fillOpacity=.5,n.valign="middle",n.marginLeft=10,n.marginRight=10,e.separators=new o.e(n),e._disposers.push(new o.c(e.separators)),e._disposers.push(n);var r=new oi.a;return e.activeLink=r,r.copyFrom(a),r.valign="middle",r.fontWeight="bold",e.width=Object(Y.c)(100),e.layout="grid",e.dataFields.name="name",e.applyTheme(),e}return n.c(e,t),e.prototype.validateDataElements=function(){this.removeChildren(),this._linksIterator.reset(),t.prototype.validateDataElements.call(this)},e.prototype.validateDataElement=function(e){var i;if(t.prototype.validateDataElement.call(this,e),e.index<this.dataItems.length-1){(i=this._linksIterator.getLast()).parent=this;var a=this.separators.create();a.parent=this,a.valign="middle"}else(i=this.activeLink).events.copyFrom(this.links.template.events),i.hide(0),i.show(),i.parent=this;i.dataItem=e,i.text=e.name,i.validate()},e}(ri.a);c.b.registeredClasses.NavigationBar=ui,c.b.registeredClasses.NavigationBarDataItem=hi,window.am4charts=a}},["XFs4"]);
//# sourceMappingURL=charts.js.map