$(document).ready(function() {

  $(document).on('click', '.mock-js--test-submit', function(){
    var element = $(this);
    var form = element.closest('form');
    var url = form.data('url');
    var boxes = form.find('.page-builder-view__page-item--question');
    //alert(boxes.length);
    //alert(boxes.find('input:checked').length);
    var errors = 0;
    if(boxes.length > 0 && boxes.find('input:checked').length >= boxes.length){
      var selected = [];
      var question = {};
      var box;
      form.find('.page-builder-view__page-item--question').each(function(){
        box = $(this);
        if(box.find('input:checked').length > 0){
          question = {};
          question.id = box.data('id');
          var options = [];
          //alert(box.find('input:checked').length);

          box.find('input').each(function(e){
            var option = {};
            option.id = $(this).val();
            option.selected = $(this).is(':checked');
            options.push(option);
          });
          question.options = options;
          selected.push(question);
        }else{
          //there are questions not selected
          show_error_notify('You have not completed all the questions');
          errors++;
        }
      });
      if(errors == 0){
        var data = {};
        data.data = selected;
        if(url != undefined){
            $.ajax({
                'type' : 'POST',
                'url'  : url,
                'data' : data,
                success: function (response) {
                    if(response.response == 'success'){
                      var test = response.data;
                      var questions = test.questions;
                      
                      $(questions).each(function(key, question){
                        var questionElm = $('[data-id="'+question.id+'"]');
                        if(questionElm.length == 1){
                          questionElm.find('input').attr('disabled', 'disabled');
                          if(question.status == 'pass'){
                            var badge = questionElm.find('.badge--success');
                            badge.length;
                            badge.removeClass('hide');
                          }else{
                            questionElm.find('.badge--danger').removeClass('hide');
                          }
                          $(question.options).each(function(key2, option){
                            var optionElm = questionElm.find('[value="'+option.id+'"]');
                            if(optionElm.length == 1){
                              if(test.show == 1){
                                if(option.status == 'true' && option.selected == 'true'){
                                  optionElm.closest('.page-builder-view__page-item-answer').addClass('answer--chosen answer--correct');
                                  optionElm.closest('.form-group').addClass('has-success');
                                }else if(option.status == 'false' && option.selected == 'true'){
                                  optionElm.closest('.page-builder-view__page-item-answer').addClass('answer--chosen');
                                  optionElm.closest('.form-group').addClass('has-error');
                                }else if(option.status == 'true' && option.selected == 'false'){
                                  optionElm.closest('.page-builder-view__page-item-answer').addClass('answer--correct');
                                  optionElm.closest('.form-group').addClass('has-error');
                                }
                              }
                            }
                          });
                        }
                      });
                      var userDetails = form.closest('.page__content').find('.userDetails');
                      userDetails.find('.score').html(test.pass + '/' + test.amount_questions + ' (' + test.score + '%)');
                      userDetails.find('.status').html(test.status);
                      userDetails.removeClass('hidden');

                      var results = form.find('.page-builder-view__page-results');
                      var buttons = form.find('.page-builder-view__page-buttons');
                      buttons.find('.page-builder-view__page-buttons__submit').remove();
                      form.closest('.page__content').find('.userDetails').removeClass('hidden');

                      if(test.status == 'Complete'){
                        results.find('.page-builder-view__page-results__passed').find('.amount_questions').html(test.pass);
                        results.find('.page-builder-view__page-results__passed').removeClass('hidden');
                        buttons.find('.page-builder-view__page-buttons__passed').removeClass('hidden');
                      }else{
                        results.find('.page-builder-view__page-results__failed').removeClass('hidden');
                        buttons.find('.page-builder-view__page-buttons__failed').removeClass('hidden');
                      }

                    }
                }
            })
        }
      }
    }else{
      //alert('There are questions not checked');
      show_error_notify('You have not completed all the questions');
    }
  });
  function show_success_notify(message){

        $.notify({
            message : message
        },{
            delay: 2000,
            placement: {
                from: 'bottom',
                align: 'right'
            },
            type: 'success'
        })

    }

    function show_error_notify(message){

        $.notify({
            message: message
        },{
            delay: 2000,
            placement: {
                from: 'bottom',
                align : 'right'
            },
            type: 'danger'
        })

    }
  $(document).on('click', '.mock-js--test-submit-preview', function(){
    var element = $(this);
    var form = element.closest('.page-builder-view--lesson');
    var url = form.data('url');
    var boxes = form.find('.page-builder-view__page-item--question');
    //alert(boxes.length);
    //alert(boxes.find('input:checked').length);
    var errors = 0;
    if(boxes.length > 0 && boxes.find('input:checked').length >= boxes.length){
      var selected = [];
      var question = {};
      var box;

      form.find('.page-builder-view__page-item--question').each(function(){
        box = $(this);
        if(box.find('input:checked').length > 0){
          question = {};
          question.id = box.data('id');
          var options = [];
          //alert(box.find('input:checked').length);

          box.find('input').each(function(e){
            var option = {};
            option.id = $(this).val();
            option.selected = $(this).is(':checked');
            options.push(option);
          });
          question.options = options;
          selected.push(question);
        }else{
          //there are questions not selected
          show_error_notify('You have not completed all the questions');
          errors++;
        }
      });
      if(errors == 0){
        var data = {};
        data.data = selected;
        if(url != undefined){
            $.ajax({
                'type' : 'POST',
                'url'  : url,
                'data' : data,
                success: function (response) {
                    if(response.response == 'success'){
                      var test = response.data;
                      var questions = test.questions;
                      
                      $(questions).each(function(key, question){
                        var questionElm = $('[data-id="'+question.id+'"]');
                        if(questionElm.length == 1){
                          questionElm.find('input').attr('disabled', 'disabled');
                          if(question.status == 'pass'){
                            var badge = questionElm.find('.badge--success');
                            badge.length;
                            badge.removeClass('hide');
                          }else{
                            questionElm.find('.badge--danger').removeClass('hide');
                          }
                          $(question.options).each(function(key2, option){
                            var optionElm = questionElm.find('[value="'+option.id+'"]');
                            if(optionElm.length == 1){
                              if(test.show == 1){
                                if(option.status == 'true' && option.selected == 'true'){
                                  optionElm.closest('.page-builder-view__page-item-answer').addClass('answer--chosen answer--correct');
                                  optionElm.closest('.form-group').addClass('has-success');
                                }else if(option.status == 'false' && option.selected == 'true'){
                                  optionElm.closest('.page-builder-view__page-item-answer').addClass('answer--chosen');
                                  optionElm.closest('.form-group').addClass('has-error');
                                }else if(option.status == 'true' && option.selected == 'false'){
                                  optionElm.closest('.page-builder-view__page-item-answer').addClass('answer--correct');
                                  optionElm.closest('.form-group').addClass('has-error');
                                }
                              }
                            }
                          });
                        }
                      });
                      var userDetails = form.find('.page__content').find('.userDetails');
                      userDetails.find('.score').html(test.pass + '/' + test.amount_questions + ' (' + test.score + '%)');
                      userDetails.find('.status').html(test.status);
                      userDetails.removeClass('hidden');

                      var results = form.find('.page-builder-view__page-results');
                      var buttons = form.find('.page-builder-view__page-buttons');
                      buttons.find('.page-builder-view__page-buttons__submit').remove();
                      form.closest('.page__content').find('.userDetails').removeClass('hidden');

                      if(test.status == 'Complete'){
                        results.find('.page-builder-view__page-results__passed').find('.amount_questions').html(test.pass);
                        results.find('.page-builder-view__page-results__passed').removeClass('hidden');
                        buttons.find('.page-builder-view__page-buttons__passed').removeClass('hidden');
                      }else{
                        results.find('.page-builder-view__page-results__failed').removeClass('hidden');
                        buttons.find('.page-builder-view__page-buttons__failed').removeClass('hidden');
                      }

                    }
                }
            })
        }
      }
    }else{
      //alert('There are questions not checked');
      show_error_notify('You have not completed all the questions');
    }
  });
});