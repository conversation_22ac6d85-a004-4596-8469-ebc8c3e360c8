@charset "UTF-8";
/* Theme Name: Risk Reduce
 * Author: Fast Fwd
 * Author URI: http://www.fast-fwd.co.uk
 * Description: Styles for Risk Reduce
 * Version: 1.0
 * License: GNU General Public License v2 or later
 * License URI: http://www.gnu.org/licenses/gpl-2.0.html */
html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, embed,
figure, figcaption, footer, header, hgroup,
menu, nav, output, ruby, section, summary,
time, mark, audio, video {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
  outline: 0; }

/* HTML5 display-role reset for older browsers */
article, aside, details, figcaption, figure,
footer, header, hgroup, menu, nav, section {
  display: block; }

body {
  line-height: 1; }

ol, ul {
  list-style: none; }

blockquote, q {
  quotes: none; }

blockquote:before, blockquote:after,
q:before, q:after {
  content: "";
  content: none; }

table {
  border-collapse: collapse;
  border-spacing: 0; }

strong {
  font-weight: bold; }

a:visited,
a:active,
a:link {
  text-decoration: none; }

a:hover {
  text-decoration: underline; }

html,
body {
  min-height: 100%;
  /*height: 100% */ }

body {
  font: 100%/22px Arial;
  -webkit-font-smoothing: antialiased !important;
  font-smooth: always !important;
  -webkit-text-size-adjust: 100%;
  -moz-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  overflow-x: hidden;
  background: #fff;
  color: #616265; }

.adaptive {
  display: block;
  width: 100%;
  height: auto; }

.inline {
  display: inline; }

.nowrap {
  white-space: nowrap; }

.form-control {
  border-radius: 0 !important; }

a {
  -webkit-transition: background-color 0.5s ease;
  -moz-transition: background-color 0.5s ease;
  -o-transition: background-color 0.5s ease;
  -ms-transition: background-color 0.5s ease;
  transition: background-color 0.5s ease; }

.btn {
  -webkit-transition: background-color 0.5s ease;
  -moz-transition: background-color 0.5s ease;
  -o-transition: background-color 0.5s ease;
  -ms-transition: background-color 0.5s ease;
  transition: background-color 0.5s ease;
  border-radius: 0;
  font-family: Arial; }
  .btn.btn-dark-grey {
    background: #616266;
    color: #fff; }
  .btn.btn-blue, .btn.btn-default {
    background: #445E8C;
    border-color: #445E8C;
    color: #fff; }

.bg-grey {
  background-color: #e8e8e8; }

#crumb {
  background: #DFE1E0;
  padding: 0 25px;
  line-height: 40px;
  color: #999; }
  #crumb a {
    color: #616265;
    font-size: 90%;
    margin: 0 5px; }

a.disabled {
  cursor: default !important;
  pointer-events: none !important;
  color: #b0b1b2; }

#filters .select2 {
  display: block;
  width: 100% !important;
  height: 34px;
  padding: 6px 12px;
  font-size: 14px;
  line-height: 1.42857;
  color: #555;
  background-color: #fff;
  background-image: none;
  border: 1px solid #ccc; }

#filters .select2-container .select2-selection {
  background-color: #fff !important;
  border: none !important; }

#filters .select2-container--default .select2-selection--single {
  height: 18px !important; }
  #filters .select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 20px !important; }

.select2-container {
  width: 100% !important; }

.radio-group {
  background-color: #f2f5f6;
  padding: 10px 20px; }

.right {
  float: right; }

.left {
  float: left; }

@media only screen and (max-width: 1024px) {
  body {
    background: #fff; }

  #filters input, #filters select, #filters .select2 {
    width: 100% !important; }
  #filters .form-group {
    display: block !important;
    margin-bottom: 10px !important; } }
header {
  border-bottom: 1px solid #b0b1b2; }
  header > p {
    padding: 0 30px;
    background: #1d437b url(/img/top-bar.jpg) top left;
    line-height: 40px; }
    header > p a {
      color: #fff;
      padding-left: 40px; }
      header > p a:hover {
        color: #fff; }
      header > p a:visited, header > p a:link {
        color: #fff; }
  header > div {
    padding: 0; }
    header > div form {
      padding: 20px 30px 0 0; }
      header > div form h2 {
        color: #032862;
        font-size: 180%;
        margin-bottom: 15px;
        font-family: "Rockwell W01 Light"; }
      header > div form input {
        background: #e2e3e4 !important;
        border: 1px solid #b0b1b2 !important;
        line-height: 26px !important;
        height: 26px !important; }

@media only screen and (max-width: 992px) {
  header > div form {
    padding: 30px; } }
nav {
  background: #445E8C; }
  nav > ul > li {
    border-bottom: 1px solid #8194b0; }
    nav > ul > li > a {
      color: #fff;
      display: block;
      padding: 0 20px;
      line-height: 56px;
      font-size: 110%; }
      nav > ul > li > a i {
        margin-right: 10px; }
      nav > ul > li > a:active, nav > ul > li > a:visited, nav > ul > li > a:link {
        color: #fff; }
    nav > ul > li.parent ul {
      margin-bottom: 10px;
      display: none; }
      nav > ul > li.parent ul li a {
        color: #fff;
        display: block;
        padding: 0 20px 0 50px;
        line-height: 40px;
        font-size: 100%; }
        nav > ul > li.parent ul li a:before {
          content: "- "; }
    nav > ul > li:last-of-type {
      border: 0; }
    nav > ul > li.active {
      background: #032862; }
    nav > ul > li.active > a,
    nav > ul > li > a:hover {
      border-left: 4px solid #ecac00;
      padding-left: 16px;
      background: #032862;
      color: #fff;
      text-decoration: none; }

footer {
  font-size: 90%;
  padding: 5px 15px;
  background: #e5ecf2; }
  footer ul li {
    border-right: 1px solid #032862;
    line-height: 15px; }
    footer ul li a {
      color: #032862;
      display: inline-block;
      padding: 0 5px; }
      footer ul li a i {
        margin-right: 5px; }
    footer ul li:last-of-type {
      border-right: none; }

h1.title,
h2.title,
h3.title {
  color: #032862;
  font-size: 220%;
  line-height: 45px;
  font-family: "Rockwell W01"; }
  h1.title a,
  h2.title a,
  h3.title a {
    float: right; }
h1.small-title,
h2.small-title,
h3.small-title {
  font-size: 130%;
  color: #032862;
  font-family: "Rockwell W01"; }

h1.heading {
  color: #032862;
  font-size: 120%;
  line-height: 30px;
  font-family: "Rockwell W01"; }

.content h1,
.content h2,
.content h3,
.content h4,
.content p,
.content ul,
.content ol {
  margin-bottom: 20px; }

.accordion {
  margin-top: 20px; }
  .accordion h3 {
    background: #5582AB;
    padding: 5px 10px;
    line-height: 40px;
    color: #fff;
    cursor: pointer; }
    .accordion h3 strong {
      display: inline-block;
      font-weight: normal;
      font-size: 140%; }
    .accordion h3 span {
      position: relative;
      top: -3px;
      font-size: 90%;
      margin-right: 5px; }
  .accordion > div {
    background: #E9E9E9;
    padding: 20px; }

.carousel .item {
  margin: 13px;
  font-weight: bold; }

.owl_prev,
.owl_next {
  float: right; }
  .owl_prev i,
  .owl_next i {
    background: #ABC8CC;
    font-size: 90%;
    display: block;
    line-height: 30px;
    margin-left: 10px;
    padding: 0 5px;
    color: #7DA8AF; }
  .owl_prev:hover,
  .owl_next:hover {
    text-decoration: none; }

.owl_next i {
  padding: 0 3px 0 8px; }

table.table {
  width: 100%; }
  table.table thead th {
    background: #445E8C;
    color: #fff; }
  table.table th,
  table.table td {
    padding: 10px;
    border: 1px solid #445E8C; }
  table.table th {
    background: #445E8C;
    color: #fff;
    border: none; }

.toolbar {
  margin: 10px -15px;
  line-height: 30px;
  font-size: 90%;
  /*.pagination
   * margin: 0
   * a
   *   display: inline-block
   *   padding: 0 9px
   *   border: 1px solid #445E8C
   *   color: #445E8C
   *   &:hover,
   *   &.active
   *     background: #445E8C
   *     color: #fff
   *     text-decoration: none */ }
  .toolbar input {
    line-height: 20px; }

ul.pagination {
  margin-top: 0; }

.crumb {
  font-size: 90%;
  padding: 10px 15px;
  margin-bottom: 20px;
  list-style: none;
  background-color: #F5F5F5;
  border-radius: 4px; }
  .crumb a:last-of-type {
    color: #555;
    text-decoration: none !important;
    pointer-events: none;
    cursor: default; }

table.table {
  border-bottom: 1px solid #ddd !important;
  margin-top: 20px;
  width: 100%; }
  table.table thead th {
    background: #445E8C;
    color: #fff; }
  table.table th,
  table.table td {
    padding: 10px; }

.dataTables_wrapper .dataTables_filter {
  margin-bottom: 10px !important; }
.dataTables_wrapper .dataTables_paginate {
  margin-bottom: 10px !important; }

.data-table tbody tr td a.btn, .data-table tbody tr td form {
  display: inline !important; }

.panel {
  border-radius: 0; }
  .panel .panel-body {
    border-radius: 0; }

.dataTables_wrapper .dataTables_filter input {
  margin-bottom: 15px !important;
  border: 1px solid #b0b1b2 !important;
  transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s; }
.dataTables_wrapper label {
  font-size: 90%; }
.dataTables_wrapper .dataTables_info, .dataTables_wrapper .dataTables_paginate {
  font-size: 90%;
  margin-top: 10px; }
.dataTables_wrapper .dataTables_paginate {
  margin-right: -8px; }
.dataTables_wrapper .paginate_button {
  padding: 0.2em 0.8em !important; }
.dataTables_wrapper table.dataTable {
  border: none !important; }
  .dataTables_wrapper table.dataTable thead th {
    border: none !important;
    border-right: 1px solid #ddd !important;
    padding: 10px 11px !important; }
    .dataTables_wrapper table.dataTable thead th:last-of-type {
      border-right: none !important; }
  .dataTables_wrapper table.dataTable tbody tr td {
    border-left: none !important;
    vertical-align: middle;
    border-right: 1px solid #ddd !important; }
    .dataTables_wrapper table.dataTable tbody tr td:first-of-type {
      border-left: 1px solid #ddd !important; }
    .dataTables_wrapper table.dataTable tbody tr td a.btn {
      display: inline-block !important; }
  .dataTables_wrapper table.dataTable tbody tr:last-of-type td {
    border-bottom: 1px solid #ddd; }

.chrs {
  display: block;
  font-size: 0.8em;
  text-align: right; }

table.table-survey {
  border-left: 4em solid #1e3159;
  position: relative;
  margin-top: 0; }
  table.table-survey th {
    background-color: inherit;
    color: inherit; }
  table.table-survey thead th {
    background-color: #dc9d00;
    color: #fff; }
table:before {
  color: #fff;
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: 2em;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  position: absolute;
  top: 0.6em;
  left: -1em;
  width: 2em;
  text-align: center; }
table.table-survey--client-organisation:before {
  content: ""; }
table.table-survey--dates:before {
  content: ""; }
table.table-survey--broker:before {
  content: ""; }
table.table-survey--underwriter:before {
  content: ""; }
table.table-survey--surveyor:before {
  content: ""; }

.show_message_thread {
  margin: 0 auto;
  text-align: center;
  display: block; }

.decline_survey_request {
  float: right;
  display: block; }

.survey-report {
  padding: 1em;
  margin-bottom: 20px; }
  .survey-report > * {
    margin-bottom: 20px; }
    .survey-report > *:last-child {
      margin-bottom: 0; }
  .survey-report h2 {
    color: #032862;
    font-family: "Rockwell W01";
    text-transform: uppercase; }

.survey-surveyor {
  padding: 1em;
  margin-bottom: 20px; }

@media only screen and (min-width: 768px) {
  .survey-surveyor .btn-primary {
    display: block;
    margin-top: 1.25em; } }
@media only screen and (max-width: 991px) {
  .survey-report .btn,
  .survey-surveyor .btn {
    margin: 1.5% 0.5em;
    width: 97%; } }
@media only screen and (min-width: 992px) {
  .survey-report .btns {
    white-space: nowrap; }
  .survey-report .btn {
    margin: 1.5% 0.5em;
    width: 30.3333%; } }
@media only screen and (min-width: 1200px) {
  .survey-report .btn-primary {
    display: block;
    margin: 1.5% auto; } }
@font-face {
  font-family: "Rockwell W01 Bold";
  src: url("/Fonts/864373f8-943b-449d-a730-462eb66d7058.eot?#iefix");
  src: url("/Fonts/864373f8-943b-449d-a730-462eb66d7058.eot?#iefix") format("eot"), url("/Fonts/49ff3af5-13b2-4add-8470-4cdac3cf650e.woff") format("woff"), url("/Fonts/7d69578b-ab0e-4986-af19-428861ada4e4.ttf") format("truetype"), url("/Fonts/2848532f-bf3e-4430-aae1-bc0d1367eb84.svg#2848532f-bf3e-4430-aae1-bc0d1367eb84") format("svg"); }
@font-face {
  font-family: "RockwellW01-BoldItalic";
  src: url("/Fonts/197ebbbd-d981-48a3-9e9a-8d21cc6fff21.eot?#iefix");
  src: url("/Fonts/197ebbbd-d981-48a3-9e9a-8d21cc6fff21.eot?#iefix") format("eot"), url("/Fonts/689e269c-72ae-41f7-9ba9-97e17f883bd5.woff") format("woff"), url("/Fonts/a6940b46-a224-4791-84c5-372aced917b7.ttf") format("truetype"), url("/Fonts/298a903d-4a78-456b-bbcc-f4f6c36611f5.svg#298a903d-4a78-456b-bbcc-f4f6c36611f5") format("svg"); }
@font-face {
  font-family: "Rockwell W01 Italic";
  src: url("/Fonts/d34aec13-9ff7-4660-9396-4537a6c7fd1a.eot?#iefix");
  src: url("/Fonts/d34aec13-9ff7-4660-9396-4537a6c7fd1a.eot?#iefix") format("eot"), url("/Fonts/f0c81ebb-e6f4-4a1f-9354-06ac9ad7b262.woff") format("woff"), url("/Fonts/080109cd-e993-4dc4-9c21-b34766eba5aa.ttf") format("truetype"), url("/Fonts/34153597-0d00-4984-ac4e-e9cdc8e6cc0c.svg#34153597-0d00-4984-ac4e-e9cdc8e6cc0c") format("svg"); }
@font-face {
  font-family: "Rockwell W01 Light";
  src: url("/Fonts/8a120ae1-7a5b-484b-a170-f67812b59652.eot?#iefix");
  src: url("/Fonts/8a120ae1-7a5b-484b-a170-f67812b59652.eot?#iefix") format("eot"), url("/Fonts/7658dda6-217a-406c-bcb8-7551f88e2c49.woff") format("woff"), url("/Fonts/ec796902-2e28-46d0-8bbd-071b83705a39.ttf") format("truetype"), url("/Fonts/66d3bbb2-4ca8-48e9-b707-f53f776ec476.svg#66d3bbb2-4ca8-48e9-b707-f53f776ec476") format("svg"); }
@font-face {
  font-family: "RockwellW01-LightItalic";
  src: url("/Fonts/8ffb54fb-49c1-474c-858e-1e2cf0505408.eot?#iefix");
  src: url("/Fonts/8ffb54fb-49c1-474c-858e-1e2cf0505408.eot?#iefix") format("eot"), url("/Fonts/b4127bd6-c223-4426-aa55-f931049c1924.woff") format("woff"), url("/Fonts/91b45e0c-c450-4dd3-b05e-d27326ab1156.ttf") format("truetype"), url("/Fonts/64c78c73-af6b-4166-8a8b-323fb6a10043.svg#64c78c73-af6b-4166-8a8b-323fb6a10043") format("svg"); }
@font-face {
  font-family: "Rockwell W01";
  src: url("/Fonts/73e6c08f-deb5-4d6f-827b-597a84046aa4.eot?#iefix");
  src: url("/Fonts/73e6c08f-deb5-4d6f-827b-597a84046aa4.eot?#iefix") format("eot"), url("/Fonts/de4b0540-9702-4662-8f3a-228419485257.woff") format("woff"), url("/Fonts/bc34c4cd-af2c-400d-b4d0-726e2d89f690.ttf") format("truetype"), url("/Fonts/59c05ddc-99f9-44db-82ce-cb7f503b5110.svg#59c05ddc-99f9-44db-82ce-cb7f503b5110") format("svg"); }
div.alert {
  margin: 20px 30px 0; }
  div.alert ul {
    list-style: circle;
    margin-left: 20px; }
  form div.alert {
    margin: 0 0 20px; }

#ui-datepicker-div {
  background: #fff; }

section#title:after {
  content: "";
  display: table;
  clear: both; }
section#title .pull-right {
  float: none !important;
  margin-top: 50px; }
  section#title .pull-right a, section#title .pull-right button, section#title .pull-right form {
    display: block;
    width: 100%;
    text-align: center;
    margin-bottom: 5px; }

@media screen and (min-width: 1200px) {
  section#title {
    margin-bottom: 0; }
    section#title .pull-right {
      float: right !important;
      margin-top: 0; }
      section#title .pull-right a, section#title .pull-right button, section#title .pull-right form {
        width: auto;
        float: right;
        display: inline;
        margin-left: 10px; } }
div.modal-backdrop {
  z-index: 1020;
  height: 1000%; }

.dataTables_wrapper .dataTables_filter {
  margin-bottom: 10px; }

#filters input[type=checkbox] {
  margin-top: 10px; }
#filters .btn {
  margin-top: 19px; }

.fc {
  direction: ltr;
  text-align: left; }

.fc-rtl {
  text-align: right; }

body .fc {
  font-size: 1em; }

.fc-unthemed th, .fc-unthemed td, .fc-unthemed thead, .fc-unthemed tbody, .fc-unthemed .fc-divider, .fc-unthemed .fc-row {
  border-color: #fff; }
.fc-unthemed .fc-popover {
  background-color: #fff; }
.fc-unthemed .fc-divider {
  background: #eee; }
.fc-unthemed .fc-popover .fc-header {
  background: #e5e5e5; }
  .fc-unthemed .fc-popover .fc-header .fc-close {
    background-color: #445e8c;
    color: #fff; }
.fc-unthemed td {
  border-style: double; }
  .fc-unthemed td.fc-mon, .fc-unthemed td.fc-tue, .fc-unthemed td.fc-wed, .fc-unthemed td.fc-thu, .fc-unthemed td.fc-fri {
    background: #e5e5e5; }
  .fc-unthemed td.fc-sat, .fc-unthemed td.fc-sun {
    background: #cdcdcd; }
  .fc-unthemed td.fc-today {
    background-color: #c1c7d1; }

.fc-highlight {
  background: #bce8f1;
  opacity: 0.3;
  filter: alpha(opacity=30); }

.fc-bgevent {
  background: #8fdf82;
  opacity: 0.3;
  filter: alpha(opacity=30); }

.fc-nonbusiness {
  background: #d7d7d7; }

.fc-icon {
  display: inline-block;
  width: 1em;
  height: 1em;
  line-height: 1em;
  font-size: 1em;
  text-align: center;
  overflow: hidden;
  font-family: "Courier New", Courier, monospace;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none; }
  .fc-icon:after {
    position: relative;
    margin: 0 -1em; }

.fc-icon-left-single-arrow:after {
  content: "‹";
  font-weight: bold;
  font-size: 200%;
  top: -7%;
  left: 3%; }

.fc-icon-right-single-arrow:after {
  content: "›";
  font-weight: bold;
  font-size: 200%;
  top: -7%;
  left: -3%; }

.fc-icon-left-double-arrow:after {
  content: "«";
  font-size: 160%;
  top: -7%; }

.fc-icon-right-double-arrow:after {
  content: "»";
  font-size: 160%;
  top: -7%; }

.fc-icon-left-triangle:after {
  content: "◄";
  font-size: 125%;
  top: 3%;
  left: -2%; }

.fc-icon-right-triangle:after {
  content: "►";
  font-size: 125%;
  top: 3%;
  left: 2%; }

.fc-icon-down-triangle:after {
  content: "▼";
  font-size: 125%;
  top: 2%; }

.fc-icon-x:after {
  content: "×";
  font-size: 200%;
  top: 6%; }

.fc button {
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  height: 2.2em;
  padding: 0 0.6em;
  font-size: 1em;
  white-space: nowrap;
  cursor: pointer;
  text-transform: capitalize; }
  .fc button::-moz-focus-inner {
    margin: 0;
    padding: 0; }

.fc-state-default {
  border: 0; }
  .fc-state-default.fc-corner-left {
    border-top-left-radius: 1.1em;
    border-bottom-left-radius: 1.1em; }
  .fc-state-default.fc-corner-right {
    border-top-right-radius: 1.1em;
    border-bottom-right-radius: 1.1em; }

.fc button .fc-icon {
  position: relative;
  top: -0.05em;
  margin: 0 0.2em;
  vertical-align: middle; }

.fc-state-default {
  background-color: #445e8c;
  color: #fff;
  outline: none; }

.fc-state-hover, .fc-state-down, .fc-state-active, .fc-state-disabled {
  background-color: #032862;
  color: #fff; }

.fc-state-hover {
  color: #fff;
  text-decoration: none;
  -webkit-transition: background-position 0.1s linear;
  -moz-transition: background-position 0.1s linear;
  -o-transition: background-position 0.1s linear;
  transition: background-position 0.1s linear; }

.fc-state-down, .fc-state-active {
  background-color: #032862; }

.fc-state-disabled {
  background-color: #dfe1e0;
  color: #999;
  cursor: default;
  opacity: 0.65;
  filter: alpha(opacity=65); }

.fc-button-group {
  display: inline-block; }

.fc-loading > table {
  opacity: 0.2;
  filter: alpha(opacity=20); }

.fc-loading-progress {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  text-align: center;
  z-index: 999;
  font-size: 15em; }
  .fc-loading-progress:before {
    content: "";
    display: inline-block;
    height: 100%;
    vertical-align: middle; }
  .fc-loading-progress i {
    color: #445e8c;
    display: inline-block;
    vertical-align: middle; }

.fc .fc-button-group > * {
  float: left;
  margin: 0 0 0 -1px; }
.fc .fc-button-group > :first-child {
  margin-left: 0; }

.fc-popover {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0 !important;
  left: 0 !important; }
  .fc-popover .fc-header {
    padding: 2px 4px; }
    .fc-popover .fc-header .fc-title {
      margin: 0 2px; }
    .fc-popover .fc-header .fc-close {
      cursor: pointer; }

.fc-ltr .fc-popover .fc-header .fc-title {
  float: left; }

.fc-rtl .fc-popover .fc-header .fc-close {
  float: left; }
.fc-rtl .fc-popover .fc-header .fc-title {
  float: right; }

.fc-ltr .fc-popover .fc-header .fc-close {
  float: right; }

.fc-unthemed .fc-popover .fc-header .fc-title {
  color: #616265;
  font-family: "Rockwell W01";
  padding: 0.25em; }
.fc-unthemed .fc-popover .fc-header .fc-close {
  border-radius: 0.8em;
  font-size: 0.9em;
  line-height: 1.6em;
  margin: 0.25em;
  width: 1.6em;
  height: 1.6em; }

.fc-popover > .ui-widget-header + .ui-widget-content {
  border-top: 0; }

.fc-divider {
  border-style: solid;
  border-width: 1px; }

hr.fc-divider {
  height: 0;
  margin: 0;
  padding: 0 0 2px;
  border-width: 1px 0; }

.fc-clear {
  clear: both; }

.fc-bg, .fc-bgevent-skeleton, .fc-highlight-skeleton, .fc-helper-skeleton {
  position: absolute;
  top: 0;
  left: 0;
  right: 0; }

.fc-bg {
  bottom: 0; }
  .fc-bg table {
    height: 100%; }

.fc table {
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
  border-spacing: 0;
  font-size: 1em; }
.fc th {
  text-align: right;
  border: 0;
  padding: 0 0.1em;
  vertical-align: top; }
.fc td {
  border-style: solid;
  border-width: 1px;
  padding: 0;
  vertical-align: top; }
  .fc td.fc-today {
    border-style: double; }
.fc .fc-row {
  border-style: solid;
  border-width: 0; }

.fc-row {
  position: relative; }
  .fc-row table {
    border-left: 0 hidden transparent;
    border-right: 0 hidden transparent;
    border-bottom: 0 hidden transparent; }
  .fc-row:first-child table {
    border-top: 0 hidden transparent; }
  .fc-row .fc-bg {
    z-index: 1; }
  .fc-row .fc-bgevent-skeleton, .fc-row .fc-highlight-skeleton {
    bottom: 0; }
  .fc-row .fc-bgevent-skeleton table {
    height: 100%; }
  .fc-row .fc-highlight-skeleton table {
    height: 100%; }
  .fc-row .fc-highlight-skeleton td {
    border-color: transparent; }
  .fc-row .fc-bgevent-skeleton {
    z-index: 2; }
    .fc-row .fc-bgevent-skeleton td {
      border-color: transparent; }
  .fc-row .fc-highlight-skeleton {
    z-index: 3; }
  .fc-row .fc-content-skeleton {
    position: relative;
    z-index: 4;
    padding-bottom: 2px; }
  .fc-row .fc-helper-skeleton {
    z-index: 5; }
  .fc-row .fc-content-skeleton td, .fc-row .fc-helper-skeleton td {
    background: none;
    border-color: transparent;
    border-bottom: 0; }
  .fc-row .fc-content-skeleton tbody td, .fc-row .fc-helper-skeleton tbody td {
    border-top: 0; }

.fc-scroller {
  overflow-y: scroll;
  overflow-x: hidden; }
  .fc-scroller > * {
    position: relative;
    width: 100%;
    overflow: hidden; }

.fc-event {
  background-color: #fff;
  border: 1px solid #445e8c;
  position: relative;
  display: block;
  font-size: 0.8em;
  line-height: 1.3;
  font-weight: normal;
  color: inherit;
  cursor: pointer;
  text-decoration: none;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none; }
  .fc-event:hover {
    text-decoration: none; }
  .fc-event.calendar--re-admin {
    color: #fff;
    background-color: #d9604d; }
  .fc-event.calendar--holiday {
    color: #fff;
    background-color: #72ba6c; }
  .fc-event.calendar--survey {
    color: #fff;
    background-color: #dfaa54; }
  .fc-event.calendar--rereview {
    color: #fff;
    background-color: #42b7a7; }
  .fc-event .fa {
    color: #fff;
    background-color: #445e8c;
    display: inline-block;
    margin-right: 0.1em;
    width: 2em;
    height: 2em;
    line-height: 2em;
    text-align: center; }

.ui-widget .fc-event {
  color: #fff;
  text-decoration: none; }

.fc-not-allowed {
  cursor: not-allowed; }
  .fc-not-allowed .fc-event {
    cursor: not-allowed; }

.fc-event .fc-bg {
  z-index: 1;
  background: #fff;
  opacity: 0.25;
  filter: alpha(opacity=25); }
.fc-event .fc-content {
  position: relative;
  z-index: 2; }
.fc-event .fc-resizer {
  position: absolute;
  z-index: 3; }

.fc-ltr .fc-h-event.fc-not-start, .fc-rtl .fc-h-event.fc-not-end {
  margin-left: 0;
  border-left-width: 0;
  padding-left: 1px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0; }

.fc-ltr .fc-h-event.fc-not-end, .fc-rtl .fc-h-event.fc-not-start {
  margin-right: 0;
  border-right-width: 0;
  padding-right: 1px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0; }

.fc-h-event .fc-resizer {
  top: -1px;
  bottom: -1px;
  left: -1px;
  right: -1px;
  width: 5px; }

.fc-ltr .fc-h-event .fc-start-resizer {
  right: auto;
  cursor: w-resize; }
  .fc-ltr .fc-h-event .fc-start-resizer:before, .fc-ltr .fc-h-event .fc-start-resizer:after {
    right: auto;
    cursor: w-resize; }

.fc-rtl .fc-h-event .fc-end-resizer {
  right: auto;
  cursor: w-resize; }
  .fc-rtl .fc-h-event .fc-end-resizer:before, .fc-rtl .fc-h-event .fc-end-resizer:after {
    right: auto;
    cursor: w-resize; }

.fc-ltr .fc-h-event .fc-end-resizer {
  left: auto;
  cursor: e-resize; }
  .fc-ltr .fc-h-event .fc-end-resizer:before, .fc-ltr .fc-h-event .fc-end-resizer:after {
    left: auto;
    cursor: e-resize; }

.fc-rtl .fc-h-event .fc-start-resizer {
  left: auto;
  cursor: e-resize; }
  .fc-rtl .fc-h-event .fc-start-resizer:before, .fc-rtl .fc-h-event .fc-start-resizer:after {
    left: auto;
    cursor: e-resize; }

.fc-day-grid-event {
  margin: 0 0.5em 0.25em;
  padding: 0; }
  .fc-day-grid-event .fc-content {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis; }
  .fc-day-grid-event .fc-time {
    font-weight: bold; }
  .fc-day-grid-event .fc-resizer {
    left: -3px;
    right: -3px;
    width: 7px; }

.fc-more-cell {
  text-align: right; }

a.fc-more {
  margin: 0 0.5em;
  font-size: 0.85em;
  cursor: pointer;
  text-decoration: none; }
  a.fc-more:hover {
    text-decoration: underline; }

.fc-limited {
  display: none; }

.fc-day-grid .fc-row {
  z-index: 1; }

.fc-more-popover {
  z-index: 2; }
  .fc-more-popover .fc-event-container {
    border: 1px solid #e5e5e5;
    margin: 0;
    padding: 0.5em 0 0.25em;
    position: absolute;
    top: 2.1em;
    right: 0;
    bottom: 0;
    left: 0; }

.fc-now-indicator {
  position: absolute;
  border: 0 solid red; }

.fc-toolbar {
  text-align: center;
  margin-bottom: 1em; }
  .fc-toolbar .fc-left {
    float: left; }
  .fc-toolbar .fc-right {
    float: right; }
  .fc-toolbar .fc-center {
    display: inline-block; }

.fc .fc-toolbar > * > * {
  float: left;
  margin-left: 0.75em; }
.fc .fc-toolbar > * > :first-child {
  margin-left: 0; }

.fc-toolbar h2 {
  color: #032862;
  font-size: 1.5em;
  line-height: 1em;
  font-family: "Rockwell W01"; }
.fc-toolbar button {
  position: relative; }
.fc-toolbar .fc-state-hover, .fc-toolbar .ui-state-hover {
  z-index: 2; }
.fc-toolbar .fc-state-down {
  z-index: 3; }
.fc-toolbar .fc-state-active, .fc-toolbar .ui-state-active {
  z-index: 4; }
.fc-toolbar button:focus {
  z-index: 5; }

.fc-prev-button,
.fc-next-button {
  padding: 0;
  width: 2.2em; }
  .fc .fc-prev-button, .fc
  .fc-next-button {
    padding: 0; }

.fc-view-container {
  position: relative; }
  .fc-view-container * {
    -webkit-box-sizing: content-box;
    -moz-box-sizing: content-box;
    box-sizing: content-box; }
    .fc-view-container *:before, .fc-view-container *:after {
      -webkit-box-sizing: content-box;
      -moz-box-sizing: content-box;
      box-sizing: content-box; }

.fc-view {
  position: relative;
  z-index: 1; }
  .fc-view > table {
    position: relative;
    z-index: 1; }

.fc-basicWeek-view .fc-content-skeleton, .fc-basicDay-view .fc-content-skeleton {
  padding-top: 1px;
  padding-bottom: 1em; }

.fc-basic-view .fc-body .fc-row {
  min-height: 4em; }

.fc-row.fc-rigid {
  overflow: hidden; }
  .fc-row.fc-rigid .fc-content-skeleton {
    position: absolute;
    top: 0;
    left: 0;
    right: 0; }

.fc-basic-view .fc-week-number, .fc-basic-view .fc-day-number {
  padding: 0.5em; }
.fc-basic-view td.fc-week-number span, .fc-basic-view td.fc-day-number {
  padding: 0.5em; }
.fc-basic-view .fc-week-number {
  text-align: center; }
  .fc-basic-view .fc-week-number span {
    display: inline-block;
    min-width: 1.25em; }

.fc-ltr .fc-basic-view .fc-day-number {
  text-align: right; }

.fc-rtl .fc-basic-view .fc-day-number {
  text-align: left; }

.fc-day-number.fc-other-month {
  opacity: 0.3;
  filter: alpha(opacity=30); }

.fc-agenda-view .fc-day-grid {
  position: relative;
  z-index: 2; }
  .fc-agenda-view .fc-day-grid .fc-row {
    min-height: 3em; }
    .fc-agenda-view .fc-day-grid .fc-row .fc-content-skeleton {
      padding-top: 1px;
      padding-bottom: 1em; }

.fc .fc-axis {
  vertical-align: middle;
  padding: 0 4px;
  white-space: nowrap; }

.fc-ltr .fc-axis {
  text-align: right; }

.fc-rtl .fc-axis {
  text-align: left; }

.ui-widget td.fc-axis {
  font-weight: normal; }

.fc-time-grid-container {
  position: relative;
  z-index: 1; }

.fc-time-grid {
  position: relative;
  z-index: 1;
  min-height: 100%; }
  .fc-time-grid table {
    border: 0 hidden transparent; }
  .fc-time-grid > .fc-bg {
    z-index: 1; }
  .fc-time-grid .fc-slats, .fc-time-grid > hr {
    position: relative;
    z-index: 2; }
  .fc-time-grid .fc-content-col {
    position: relative; }
  .fc-time-grid .fc-content-skeleton {
    position: absolute;
    z-index: 3;
    top: 0;
    left: 0;
    right: 0; }
  .fc-time-grid .fc-business-container {
    position: relative;
    z-index: 1; }
  .fc-time-grid .fc-bgevent-container {
    position: relative;
    z-index: 2; }
  .fc-time-grid .fc-highlight-container {
    position: relative;
    z-index: 3; }
  .fc-time-grid .fc-event-container {
    position: relative;
    z-index: 4; }
  .fc-time-grid .fc-now-indicator-line {
    z-index: 5; }
  .fc-time-grid .fc-helper-container {
    position: relative;
    z-index: 6; }
  .fc-time-grid .fc-slats td {
    height: 1.5em;
    border-bottom: 0; }
  .fc-time-grid .fc-slats .fc-minor td {
    border-top-style: dotted; }
  .fc-time-grid .fc-slats .ui-widget-content {
    background: none; }
  .fc-time-grid .fc-highlight-container {
    position: relative; }
  .fc-time-grid .fc-highlight {
    position: absolute;
    left: 0;
    right: 0; }

.fc-ltr .fc-time-grid .fc-event-container {
  margin: 0 2.5% 0 2px; }

.fc-rtl .fc-time-grid .fc-event-container {
  margin: 0 2px 0 2.5%; }

.fc-time-grid .fc-event {
  position: absolute;
  z-index: 1; }
.fc-time-grid .fc-bgevent {
  position: absolute;
  z-index: 1;
  left: 0;
  right: 0; }

.fc-v-event.fc-not-start {
  border-top-width: 0;
  padding-top: 1px;
  border-top-left-radius: 0;
  border-top-right-radius: 0; }
.fc-v-event.fc-not-end {
  border-bottom-width: 0;
  padding-bottom: 1px;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0; }

.fc-time-grid-event {
  overflow: hidden; }
  .fc-time-grid-event .fc-time, .fc-time-grid-event .fc-title {
    padding: 0 1px; }
  .fc-time-grid-event .fc-time {
    font-size: 0.85em;
    white-space: nowrap; }
  .fc-time-grid-event.fc-short .fc-content {
    white-space: nowrap; }
  .fc-time-grid-event.fc-short .fc-time, .fc-time-grid-event.fc-short .fc-title {
    display: inline-block;
    vertical-align: top; }
  .fc-time-grid-event.fc-short .fc-time span {
    display: none; }
  .fc-time-grid-event.fc-short .fc-time:before {
    content: attr(data-start); }
  .fc-time-grid-event.fc-short .fc-time:after {
    content: " - "; }
  .fc-time-grid-event.fc-short .fc-title {
    font-size: 0.85em;
    padding: 0; }
  .fc-time-grid-event .fc-resizer {
    left: 0;
    right: 0;
    bottom: 0;
    height: 8px;
    overflow: hidden;
    line-height: 8px;
    font-size: 11px;
    font-family: monospace;
    text-align: center;
    cursor: s-resize; }
    .fc-time-grid-event .fc-resizer:after {
      content: "="; }

.fc-time-grid .fc-now-indicator-line {
  border-top-width: 1px;
  left: 0;
  right: 0; }
.fc-time-grid .fc-now-indicator-arrow {
  margin-top: -5px; }

.fc-ltr .fc-time-grid .fc-now-indicator-arrow {
  left: 0;
  border-width: 5px 0 5px 6px;
  border-top-color: transparent;
  border-bottom-color: transparent; }

.fc-rtl .fc-time-grid .fc-now-indicator-arrow {
  right: 0;
  border-width: 5px 6px 5px 0;
  border-top-color: transparent;
  border-bottom-color: transparent; }

.fc-view-container .fc-day {
  box-sizing: border-box; }

@media print {
  .fc {
    max-width: 100% !important; }

  .fc-event {
    background: #fff !important;
    color: #000 !important;
    page-break-inside: avoid; }
    .fc-event .fc-resizer {
      display: none; }

  th, td, hr, thead, tbody, .fc-row {
    border-color: #ccc !important;
    background: #fff !important; }

  .fc-bg, .fc-bgevent-skeleton, .fc-highlight-skeleton, .fc-helper-skeleton, .fc-bgevent-container, .fc-business-container, .fc-highlight-container, .fc-helper-container {
    display: none; }

  .fc tbody .fc-row {
    height: auto !important;
    min-height: 0 !important; }
    .fc tbody .fc-row .fc-content-skeleton {
      position: static;
      padding-bottom: 0 !important; }
      .fc tbody .fc-row .fc-content-skeleton tbody tr:last-child td {
        padding-bottom: 1em; }
      .fc tbody .fc-row .fc-content-skeleton table {
        height: 1em; }

  .fc-more-cell, .fc-more {
    display: none !important; }

  .fc tr.fc-limited {
    display: table-row !important; }
  .fc td.fc-limited {
    display: table-cell !important; }

  .fc-popover {
    display: none; }

  .fc-time-grid {
    min-height: 0 !important; }

  .fc-agenda-view .fc-axis {
    display: none; }

  .fc-slats {
    display: none !important; }

  .fc-time-grid hr {
    display: none !important; }
  .fc-time-grid .fc-content-skeleton {
    position: static; }
    .fc-time-grid .fc-content-skeleton table {
      height: 4em; }
  .fc-time-grid .fc-event-container {
    margin: 0 !important; }
  .fc-time-grid .fc-event {
    position: static !important;
    margin: 3px 2px !important; }
    .fc-time-grid .fc-event.fc-not-end {
      border-bottom-width: 1px !important; }
      .fc-time-grid .fc-event.fc-not-end:after {
        content: "..."; }
    .fc-time-grid .fc-event.fc-not-start {
      border-top-width: 1px !important; }
      .fc-time-grid .fc-event.fc-not-start:before {
        content: "..."; }
    .fc-time-grid .fc-event .fc-time {
      white-space: normal !important; }
      .fc-time-grid .fc-event .fc-time span {
        display: none; }
      .fc-time-grid .fc-event .fc-time:after {
        content: attr(data-full); }

  .fc-scroller, .fc-day-grid-container, .fc-time-grid-container {
    overflow: visible !important;
    height: auto !important; }

  .fc-row {
    border: 0 !important;
    margin: 0 !important; }

  .fc-button-group, .fc button {
    display: none; } }
body.riskimprovementform-show span.preview a img {
  width: 100px;
  height: auto; }

body.surveysubmissions-show span.preview a img {
  width: 100px;
  height: auto; }

.kanban {
  clear: both;
  margin: 0 30px 30px;
  position: relative;
  padding: 0; }

.kanban--filters {
  background-color: #2e4064;
  color: #fff;
  margin: 0;
  padding: 10px;
  width: 100%;
  z-index: 10; }

.kanban--filters-dropdown {
  clear: both; }
  .kanban--filters-dropdown.in {
    padding-bottom: 12px; }
  .kanban--filters-dropdown form {
    border-top: 2px solid #4285b9;
    padding: 20px 10px 0;
    position: relative;
    top: 12px; }

.kanban--filter-toggle {
  background-color: transparent;
  border: 0;
  cursor: pointer;
  font-size: 2.2em;
  line-height: 1em;
  outline: none; }
  .kanban--filter-toggle:active {
    color: #4285b9; }

.kanban--reset-filters {
  color: #5cb85c; }
  .kanban--reset-filters:active, .kanban--reset-filters:hover {
    color: inherit; }

.kanban--type-acronym {
  background-color: #4285b9;
  border-radius: 100%;
  color: #2e4064;
  display: inline-block;
  font-family: "Rockwell W01";
  font-size: 1.2em;
  height: 2em;
  line-height: 2em;
  text-align: center;
  width: 2em; }

.kanban--board {
  clear: both;
  overflow-x: auto;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
  position: relative;
  white-space: nowrap;
  height: 650px; }
  .kanban--board::-webkit-scrollbar,
  .kanban--board ::-webkit-scrollbar {
    width: 5px;
    height: 5px; }
  .kanban--board::-webkit-scrollbar-button,
  .kanban--board ::-webkit-scrollbar-button {
    width: 0px;
    height: 0px; }
  .kanban--board::-webkit-scrollbar-thumb,
  .kanban--board ::-webkit-scrollbar-thumb {
    border: 0;
    border-radius: 5px; }
  .kanban--board::-webkit-scrollbar-track,
  .kanban--board ::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border: 0;
    border-radius: 10px; }
  .kanban--board::-webkit-scrollbar-corner,
  .kanban--board ::-webkit-scrollbar-corner {
    background: transparent; }

.kanban--columns {
  font-size: 0;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0; }

.kanban--column {
  font-size: 12px;
  background-color: #eaeff2;
  display: inline-block;
  height: 100%;
  width: 25%;
  padding: 0;
  position: relative;
  vertical-align: top;
  margin: 0 1px 0 0; }
  .kanban--column:last-child {
    margin: 0; }
  .kanban--column:nth-child(odd) {
    background-color: #f6f7f9; }
  .kanban--column > * {
    white-space: normal; }
  .kanban--column h2 {
    display: table;
    color: #032862;
    font-family: "Rockwell W01";
    text-align: center;
    text-transform: uppercase;
    font-size: 14px;
    padding: 10px;
    width: 100%;
    height: 64px; }
    .kanban--column h2 > span {
      display: table-cell;
      vertical-align: middle; }

.kanban--cards {
  position: absolute;
  top: 74px;
  right: 10px;
  bottom: 10px;
  left: 10px;
  overflow-x: hidden;
  overflow-y: scroll; }
  .kanban--cards a {
    color: inherit;
    display: block; }
    .kanban--cards a:hover {
      text-decoration: none; }
    .kanban--cards a:hover, .kanban--cards a:active {
      color: inherit; }

.kanban--card {
  background-color: #fff;
  border-radius: 1em;
  margin: 10px;
  padding: 10px; }
  .kanban--card:first-child {
    margin: 0 10px 10px; }
  .kanban--card:last-child {
    margin: 10px 10px 0; }
  .kanban--card dl dt {
    clear: left;
    display: block;
    float: left;
    font-weight: 700;
    width: 50%; }
  .kanban--card dl dd {
    display: block;
    float: left;
    width: 50%; }
  .kanban--card dl:before, .kanban--card dl:after {
    content: " ";
    display: table;
    clear: both; }
  .kanban--card dl:after {
    clear: both; }

.kanban--card_title {
  font-family: "Rockwell W01";
  font-weight: 700;
  font-size: 13px; }

.kanban--surveys::-webkit-scrollbar-thumb,
.kanban--surveys ::-webkit-scrollbar-thumb {
  background: #42b7a7; }
.kanban--surveys .kanban--column h2 {
  color: #fff;
  background-color: #42b7a7; }

.kanban--risk-recommendations::-webkit-scrollbar-thumb,
.kanban--risk-recommendations ::-webkit-scrollbar-thumb {
  background: #ea5656; }
.kanban--risk-recommendations .kanban--column h2 {
  color: #fff;
  background-color: #ea5656; }

section.survey-show {
  padding-left: 15px;
  padding-right: 15px;
  padding-bottom: 30px; }
  section.survey-show #survey-details {
    background-color: #e4e9ef; }
    section.survey-show #survey-details .panel {
      background: none;
      border-radius: 0 !important; }
      section.survey-show #survey-details .panel:first-of-type {
        margin-top: 30px; }
      section.survey-show #survey-details .panel .panel-heading {
        background-color: #579099;
        border-radius: 0 !important;
        color: #fff; }
        section.survey-show #survey-details .panel .panel-heading h4 {
          font-size: 15px; }
        section.survey-show #survey-details .panel .panel-heading::after {
          content: "";
          display: block;
          width: 30px;
          height: 54px;
          position: absolute;
          background-color: #e4e9ef;
          margin-top: -42px;
          right: 6px;
          -ms-transform: rotate(-25deg);
          /* IE 9 */
          -webkit-transform: rotate(-25deg);
          transform: rotate(-25deg); }
      section.survey-show #survey-details .panel .panel-body h4 {
        font-size: 13px; }
      section.survey-show #survey-details .panel .panel-body p {
        font-size: 12px; }
  section.survey-show #next-booking {
    padding-left: 0;
    padding-right: 0; }
    section.survey-show #next-booking #next-booking-panel {
      background-color: #445f8e;
      color: #fff;
      margin-bottom: 0; }
      section.survey-show #next-booking #next-booking-panel .panel-heading {
        background-color: #1e3159;
        color: #fff; }
      section.survey-show #next-booking #next-booking-panel #next-booking-panel__icon {
        padding-left: 0;
        padding-right: 0; }
        section.survey-show #next-booking #next-booking-panel #next-booking-panel__icon .calendar-icon {
          background-image: url("/img/survey_calendar.png");
          background-repeat: no-repeat;
          background-size: cover;
          min-height: 190px;
          width: 100%;
          display: block; }
          section.survey-show #next-booking #next-booking-panel #next-booking-panel__icon .calendar-icon span {
            position: absolute;
            left: 40px;
            top: 95px;
            font-size: 40px;
            color: #445f8e; }
            section.survey-show #next-booking #next-booking-panel #next-booking-panel__icon .calendar-icon span#day {
              margin-top: 40px; }
      section.survey-show #next-booking #next-booking-panel #next-booking-panel__date {
        position: absolute;
        bottom: 0;
        right: 0; }
        section.survey-show #next-booking #next-booking-panel #next-booking-panel__date h3 {
          font-weight: 400;
          font-size: 150%;
          white-space: nowrap; }
      section.survey-show #next-booking #next-booking-panel #next-booking-panel__download h2 {
        color: #fff;
        text-align: center;
        font-size: 190%;
        line-height: 120%; }
      section.survey-show #next-booking #next-booking-panel #next-booking-panel__download img {
        text-align: center;
        font-size: 3em;
        display: block;
        margin: 5px auto; }
      section.survey-show #next-booking #next-booking-panel #next-booking-panel__download .btn {
        text-align: center;
        margin: 10px auto 0;
        display: block;
        background-color: #6199c8;
        width: 120px; }
  section.survey-show #next-booking-panel__review {
    background-color: #dcdfe8; }
    section.survey-show #next-booking-panel__review #next-booking-panel__review__ready #icon {
      padding-left: 0;
      padding-right: 0; }
    section.survey-show #next-booking-panel__review #next-booking-panel__review__ready .small-title {
      position: absolute;
      right: 0;
      bottom: 0; }

body.survey-showriskrecommendation section.details {
  background-color: #DCE0E8;
  margin: 20px 0; }
  body.survey-showriskrecommendation section.details #details__detail .row {
    border-bottom: 1px solid #333;
    padding: 10px 0px;
    margin: 0 -5px; }
    body.survey-showriskrecommendation section.details #details__detail .row div {
      padding: 0 5px; }
body.survey-showriskrecommendation section#photography {
  background-color: #DCE0E8;
  margin: 20px 0 0; }

.assign-users__list .table tr .assign-users__list-item__icon {
  display: none; }
.assign-users__list .table tr.active .assign-users__list-item__icon {
  display: inline-block; }

.learning {
  padding-bottom: 45px;
  margin-bottom: 0; }
  .learning .field-rich-text-editor b {
    font-weight: bold; }
  .learning .field-rich-text-editor i {
    font-style: italic; }
  .learning .field-rich-text-editor ol {
    display: block;
    list-style-type: decimal;
    margin-top: 1em;
    margin-bottom: 1em;
    margin-left: 0;
    margin-right: 0;
    padding-left: 40px; }
  .learning .field-rich-text-editor ul {
    list-style-type: disc;
    margin-top: 1em;
    margin-bottom: 1em;
    margin-left: 0;
    margin-right: 0;
    padding-left: 40px; }
  .learning .messenger__video-upload-placeholder video {
    max-width: 100%; }
  .learning header {
    border-bottom: 0; }
  .learning .messenger__video-upload-placeholder video {
    max-width: 100%; }
  .learning h2 {
    font-size: 20px;
    color: #445e8c; }
  .learning h3 {
    font-size: 17px;
    color: #445e8c;
    font-weight: 700;
    margin: 24px 0 15px 0; }
  .learning .btn-danger {
    background-color: #d9534f;
    border-color: #d43f3a; }
    .learning .btn-danger:hover {
      background-color: #c9302c; }
  .learning .btn-success {
    background-color: #69c057;
    border-color: #4cae4c; }
    .learning .btn-success:hover {
      background-color: #50a63e; }
  .learning .btn-default {
    background-color: #fff;
    border-color: #ccc;
    color: #333 !important; }
    .learning .btn-default:hover {
      background-color: #e6e6e6; }
  .learning div.dataTables_wrapper div.dataTables_length {
    margin-top: 10px;
    padding-top: 6px; }
  .learning .panel-primary {
    border-color: #445e8c; }
    .learning .panel-primary .panel-title {
      color: #FFF; }
    .learning .panel-primary > .panel-heading {
      border-radius: 0;
      background-color: #445e8c;
      border-color: #445e8c; }
  .learning .panel-title {
    margin: 0;
    color: #555; }
  .learning .alert-success {
    color: #69c057; }
  .learning .alert-danger {
    color: #d9534f; }
  .learning.page {
    margin: 0 0 15px 0; }
  .learning hr {
    border-color: #e7e8e9; }
  .learning section#title {
    border-bottom: 1px solid #e7e8e9;
    padding: 25px;
    margin: 0; }
  .learning table.table-bordered.dataTable {
    margin-top: 0 !important; }
  .learning div.dataTables_wrapper div.dataTables_length, .learning div.dataTables_wrapper div.dataTables_filter, .learning div.dataTables_wrapper div.dataTables_info, .learning div.dataTables_wrapper div.dataTables_paginate {
    text-align: center; }
  .learning div.dataTables_wrapper div.dataTables_info {
    margin-bottom: 10px; }
  .learning .table--striped-tbody .odd {
    background-color: #f9f9f9; }
  .learning .tr--child td:first-child {
    padding-left: 28px; }
  .learning div[contenteditable].form-control {
    height: auto; }
  .learning .select--with-text {
    width: auto;
    display: inline-block; }
  .learning .form-control--text-only {
    border-color: transparent;
    background-color: transparent;
    box-shadow: none;
    height: auto;
    min-height: 34px;
    padding-left: 0; }
  .learning .form-control--html {
    border-color: transparent;
    background-color: transparent;
    box-shadow: none;
    height: auto;
    min-height: 34px;
    padding-left: 0;
    padding-right: 0; }
  .learning .form-control--html-no-padding {
    padding-top: 0;
    padding-bottom: 0; }
  .learning .control-label small {
    display: block;
    color: #777;
    font-weight: normal;
    font-size: 12px;
    line-height: 1.2em; }
  .learning .form__validation-message .error {
    color: #d9534f; }
  .learning .icon-sortable, .learning .icon-delete, .learning .icon-duplicate, .learning .icon-edit, .learning .icon-delete-section, .learning .icon-duplicate-section {
    font-size: 14px;
    opacity: 0.4;
    line-height: 1em; }
  .learning .icon-sortable:hover, .learning .icon-delete:hover, .learning .icon-duplicate:hover, .learning .icon-edit:hover, .learning .icon-delete-section:hover, .learning .icon-duplicate-section:hover {
    opacity: 1;
    text-decoration: none; }
  .learning .icon-sortable {
    position: relative; }
    .learning .icon-sortable:hover {
      opacity: 1; }
  .learning .btn:not(.btn-xs) {
    min-width: 62px; }
  .learning .badge {
    font-size: 12px;
    font-weight: normal;
    background-color: #a5acb4;
    padding: 6px 12px;
    line-height: 1.42857;
    border-radius: 20px; }
  .learning .badge--rectangle {
    border-radius: 0; }
  .learning .badge--btn-size {
    font-size: 14px;
    padding: 7px 13px;
    line-height: 1.42857;
    min-width: 150px; }
  .learning .badge--success {
    background-color: #69c057; }
  .learning .badge--danger {
    background-color: #d9534f; }
  .learning .award {
    color: #445e8c;
    line-height: 1em;
    background: url(../img/learning/award.svg);
    height: 1em;
    width: 1em;
    background-size: cover;
    margin: 0 auto; }
  .learning .award--large {
    font-size: 150px; }
  .learning .page__content {
    margin-top: 30px; }
  .learning .progress {
    height: 35px;
    border-radius: 20px;
    background-color: transparent;
    border: 3px solid #f0ad4e; }
  .learning .progress-bar {
    border-radius: 0 20px 20px 0;
    box-shadow: none; }
  .learning .page-builder__item-content {
    clear: both;
    margin-bottom: 15px; }
    .learning .page-builder__item-content .editor__content div[contenteditable] {
      min-height: 300px; }
  .learning .page-builder__item-buttons--complete {
    margin: 0 auto 15px auto;
    text-align: center; }
  .learning .page-builder__item-title {
    cursor: pointer; }
    .learning .page-builder__item-title .icon-sortable, .learning .page-builder__item-title .icon-delete, .learning .page-builder__item-title .icon-duplicate, .learning .page-builder__item-title .icon-delete-section, .learning .page-builder__item-title .icon-duplicate-section {
      float: right;
      margin-left: 1em; }
    .learning .page-builder__item-title .icon-sortable {
      margin-top: 1px; }
  .learning .page-builder__item--document-upload__file-list li {
    position: relative;
    padding-right: 30px; }
  .learning .page-builder__item--document-upload__file-list .icon-delete {
    position: absolute;
    right: 10px;
    top: 10px;
    color: #777; }
  .learning .page-builder-view__page {
    padding: 30px 0; }
  .learning .page-builder-view__progress {
    margin: -6px 0 0 0;
    display: block; }
    .learning .page-builder-view__progress li > a {
      margin-right: 0.5em;
      border-radius: 6px;
      color: #555;
      border: none; }
    .learning .page-builder-view__progress li:last-child a {
      margin-right: 0; }
    .learning .page-builder-view__progress .active a {
      background-color: #69c057;
      cursor: pointer;
      color: #FFF; }
  .learning .page-builder-view__page-item {
    margin-bottom: 30px; }
  .learning .page-builder-view__page-item--image {
    text-align: center;
    padding: 30px;
    background-color: #eeeeee; }
    .learning .page-builder-view__page-item--image img {
      max-width: 100%; }
  .learning .page-builder-view__page-item--video-embed, .learning .page-builder-view__page-item--video-upload {
    padding: 30px;
    background-color: #eeeeee; }
  .learning .page-builder-view__page-item--text-rich b, .learning .page-builder-view__page-item--text-rich strong {
    font-weight: 700; }
  .learning .page-builder-view__page-item--text-rich i, .learning .page-builder-view__page-item--text-rich em {
    font-style: italic; }
  .learning .page-builder-view__page-item--text-rich p {
    margin: 0 0 1em 0; }
  .learning .page-builder-view__page-item--text-rich ul, .learning .page-builder-view__page-item--text-rich ol {
    margin: 0 0 1em 1em; }
  .learning .page-builder-view__page-item--text-rich ul li, .learning .page-builder-view__page-item--text-rich ol li {
    margin-bottom: 0.5em; }
  .learning .page-builder-view__page-item--text-rich ul {
    list-style: disc; }
  .learning .page-builder-view__page-item--text-rich ol {
    list-style: decimal; }
  .learning .page-builder-view__page-item--text-rich h1, .learning .page-builder-view__page-item--text-rich h2 {
    font-size: 26px;
    font-weight: normal; }
  .learning .page-builder-view__page-item--text-rich h3, .learning .page-builder-view__page-item--text-rich h4, .learning .page-builder-view__page-item--text-rich h5, .learning .page-builder-view__page-item--text-rich h6 {
    font-size: 22px;
    font-weight: normal; }
  .learning .page-builder-view__page-item--heading h2 {
    font-size: 26px; }
  .learning .page-builder-view__page-item--document-upload h3 {
    font-size: 22px;
    font-weight: normal; }
  .learning .page-builder-view__page-item--question {
    margin: 60px 0; }
  .learning .page-builder-view__page-item__question-text {
    color: #445e8c;
    margin-bottom: 0.5em;
    font-weight: normal;
    font-size: 18px; }
  .learning .page-builder-view__page-item-answer.answer--chosen label:after {
    content: " - chosen incorrect answer"; }
  .learning .page-builder-view__page-item-answer.answer--correct label:after {
    content: " - correct answer"; }
  .learning .page-builder-view__page-item-answer.answer--chosen.answer--correct label:after {
    content: " - chosen correct answer"; }
  .learning .page-builder-view__page-item__select-text {
    font-style: italic;
    color: #777;
    margin-top: 1em; }
  .learning .page-builder-view__page-nav {
    color: #777;
    position: relative;
    text-align: center; }
    .learning .page-builder-view__page-nav .btn--back {
      background-color: #777;
      left: 0; }
    .learning .page-builder-view__page-nav .btn--next {
      right: 0; }
    .learning .page-builder-view__page-nav .pagination li a {
      color: #777;
      border: none; }
    .learning .page-builder-view__page-nav .pagination .active a {
      color: #FFF;
      background-color: #777; }
  .learning .page-builder-view__page-results__failed, .learning .page-builder-view__page-results__passed {
    margin: 0 0 30px;
    background-color: transparent;
    border: none;
    position: relative;
    padding-left: 50px; }
  .learning .page-builder-view__page-results__failed .fa, .learning .page-builder-view__page-results__passed .fa {
    font-size: 40px;
    position: absolute;
    top: 15px;
    left: 0; }
  .learning .page-builder-view__page-results__failed .alert__title, .learning .page-builder-view__page-results__passed .alert__title {
    font-weight: 700;
    margin-bottom: 0; }
  .learning .page-builder-view__page-results__failed .alert__body, .learning .page-builder-view__page-results__passed .alert__body {
    color: #555;
    margin-top: 0; }
  .learning .add-content {
    list-style: none;
    padding: 0;
    margin: 0 -7.5px; }
    .learning .add-content:before {
      content: " ";
      display: table; }
    .learning .add-content:after {
      content: " ";
      display: table;
      clear: both; }
  .learning .add-content__item {
    position: relative;
    float: left;
    width: 50%;
    min-height: 1px;
    padding-left: 15px;
    padding-right: 15px;
    position: relative;
    min-height: 1px;
    padding-left: 15px;
    padding-right: 15px;
    padding-left: 7.5px;
    padding-right: 7.5px;
    margin-bottom: 15px; }
  .learning .add-content__item-button {
    display: block;
    text-align: center;
    padding: 7.5px; }
  .learning .add-content__item-icon {
    display: block;
    font-size: 30px; }
  .learning .document-downloads {
    list-style: none;
    margin-top: 30px;
    padding: 0; }
    .learning .document-downloads:before {
      content: " ";
      display: table; }
    .learning .document-downloads:after {
      content: " ";
      display: table;
      clear: both; }
  .learning .document-downloads__item {
    margin-bottom: 15px; }
    .learning .document-downloads__item a.pull-right {
      margin-top: -30px;
      margin-right: 10px; }
  .learning .document-downloads__item-btn {
    white-space: normal;
    display: block;
    text-align: left;
    background-color: #eeeeee;
    border: none;
    color: #445e8c !important;
    padding: 12px 18px; }
    .learning .document-downloads__item-btn:hover {
      background-color: #e2e2e2; }
  .learning .document-downloads__btn-context {
    display: block;
    position: relative;
    padding-left: 36px;
    line-height: 1.3em; }
  .learning .document-downloads__item-icon {
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    left: 0;
    font-size: 24px; }
  .learning .editor__toolbar {
    margin-bottom: 15px; }
  .learning .page-list__buttons {
    margin-top: 7.5px; }
  .learning .page-list__item {
    position: relative; }
    .learning .page-list__item .page-list__item-name {
      display: block;
      padding-right: 36px; }
    .learning .page-list__item.active .page-list__item-name {
      color: #FFF; }
    .learning .page-list__item .page-list__item-input-group {
      display: none; }
      .learning .page-list__item .page-list__item-input-group .input-group {
        margin-top: 7.5px; }
    .learning .page-list__item .icon-sortable {
      position: absolute;
      right: 15px;
      top: 13px; }
  .learning .test-question__buttons--add {
    margin-top: 7.5px; }
  .learning .test-question__radio-legend {
    text-align: center; }
  .learning .test-question__radio input[type="radio"], .learning .test-question__radio input[type="checkbox"] {
    position: relative;
    margin: 9px auto 0;
    display: block; }
  .learning .test-question__checkbox input[type="radio"], .learning .test-question__checkbox input[type="checkbox"] {
    position: relative;
    margin: 9px auto 0;
    display: block; }
  .learning .test-question__answer-input-holder {
    position: relative;
    padding-right: 20px; }
  .learning .test-question__answer-delete {
    position: absolute;
    top: 7px;
    color: #333;
    right: 0; }
  .learning .test-question__answer:first-child .test-question__answer-delete, .learning .test-question__answer:nth-child(2) .test-question__answer-delete {
    display: none; }
  .learning .test-question__checkbox:nth-child(3) .test-question__answer-delete {
    display: none; }
  .learning .library {
    margin: 0 -15px; }
  .learning .library__section {
    padding: 30px 15px; }
    .learning .library__section.library__section--highlight {
      background-color: #e7e8e9; }
      .learning .library__section.library__section--highlight .library-item {
        border-color: #e7e8e9;
        margin-bottom: 60px; }
  .learning h2.library__section-title {
    margin: 0 0 40px;
    font-size: 20px; }
  .learning .library-item {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e7e8e9; }
  .learning .library-grid-item {
    position: relative;
    min-height: 1px;
    padding-left: 15px;
    padding-right: 15px;
    position: relative;
    min-height: 1px;
    padding-left: 15px;
    padding-right: 15px; }
    .learning .library-grid-item:nth-child(2n+1) {
      clear: left; }
  .learning .library-item__body:before {
    content: " ";
    display: table; }
  .learning .library-item__body:after {
    content: " ";
    display: table;
    clear: both; }
  .learning .library-item__btns {
    text-align: right;
    margin-top: 20px; }
  .learning .library-item__image {
    position: relative;
    background-color: #000;
    border-radius: 3px;
    overflow: hidden; }
    .learning .library-item__image img {
      opacity: 0.6;
      height: 185px;
      width: 100%; }
    .learning .library-item__image .progress {
      position: absolute;
      top: 50%;
      width: 50%;
      left: 50%;
      margin-left: -25%;
      -webkit-transform: translateY(-50%);
      -ms-transform: translateY(-50%);
      transform: translateY(-50%);
      height: 35px;
      border-radius: 20px;
      background-color: transparent;
      border: 3px solid #f0ad4e; }
    .learning .library-item__image .progress-bar {
      border-radius: 0 20px 20px 0;
      box-shadow: none; }
    .learning .library-item__image .library-item__image-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      font-size: 50px;
      margin: -0.5em 0 0 -0.5em;
      color: #f0ad4e; }
      .learning .library-item__image .library-item__image-icon.icon--pending {
        background: url(../img/learning/icon-training-book.png) 50% 50% no-repeat;
        font-size: 51px;
        width: 1em;
        height: 1em; }
      .learning .library-item__image .library-item__image-icon.icon--unpublished {
        font-size: 16px;
        text-align: center;
        width: 100%;
        height: auto;
        left: 0;
        line-height: 1.2em;
        -webkit-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
        transform: translateY(-50%);
        margin: 0; }
  .learning .library-item__organisation:before {
    content: "Organisation: "; }
  .learning .library__section-header--button .btn, .learning .library__section-header--button .library__section-header__form {
    float: right;
    margin-left: 1em; }
  .learning .library-item--has-status .library-item__body-header:before {
    content: " ";
    display: table; }
  .learning .library-item--has-status .library-item__body-header:after {
    content: " ";
    display: table;
    clear: both; }
  .learning .library-item--has-status .library-item__body-header .library-item__status {
    float: right;
    margin-top: 24px;
    margin-left: 10px; }
  .learning .library-item--in-progress .library-item__status {
    background-color: #f0ad4e; }
  .learning .library-item--completed .library-item__status {
    background-color: #69c057; }
  .learning .library-item--completed .library-item__image-icon {
    color: #69c057; }
  .learning .page-jumbo-header {
    text-align: center;
    background-color: #dfe5f0;
    background-repeat: no-repeat;
    background-position: 50% 50%;
    background-size: cover;
    position: relative;
    color: #FFF;
    padding: 90px 30px 75px;
    margin-top: 30px; }
    .learning .page-jumbo-header:before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.7); }
  .learning h2.page-jumbo-header__title {
    color: #f0ad4e;
    margin-bottom: 45px;
    font-size: 24px;
    font-family: "Rockwell W01";
    line-height: 1.3em; }
  .learning .page-jumbo-header__context {
    position: relative; }
  .learning .page-jumbo-header--has-description {
    padding: 30px 30px 15px; }
    .learning .page-jumbo-header--has-description h2.page-jumbo-header__title {
      margin-bottom: 20px; }
  .learning .course-view__description, .learning .course-view__header-btns {
    margin-bottom: 30px; }
  .learning .course-view__header-progress {
    text-align: right; }
  .learning .course-view__sections {
    margin-bottom: 90px; }
  .learning .course-view__section {
    padding: 90px 0 0 0;
    position: relative; }
    .learning .course-view__section:nth-child(n) {
      padding: 90px 0 0 0;
      position: relative; }
    .learning .course-view__section:before, .learning .course-view__section:nth-child(n):before {
      content: "";
      position: absolute;
      top: 0;
      left: 50%;
      margin-left: -2px;
      width: 4px;
      height: 90px;
      background-color: #eeeeee; }
  .learning h2.certificate-view__title {
    margin-bottom: 24px; }
  .learning h2.certificate-view__title-name {
    color: #445e8c;
    font-size: 26px;
    font-weight: 700;
    margin: 24px 0 24px; }
  .learning .certificate-view__award {
    background-image: url(../img/learning/award--white-tick.svg); }
  .learning .certificate-view__title-course-title {
    font-weight: normal;
    line-height: 1;
    color: #777;
    margin: 20px 0 30px;
    font-weight: 700; }
  .learning .certificate-view__body-header {
    text-align: center;
    padding-bottom: 30px; }
  .learning .certificate-view__lessons-list {
    list-style: none;
    border-top: 1px solid #e7e8e9;
    margin: 0 -15px 30px;
    padding: 30px 0 0 0; }
  .learning .certificate-view__lesson-item {
    position: relative;
    min-height: 1px;
    padding-left: 15px;
    padding-right: 15px;
    margin-bottom: 30px; }
    .learning .certificate-view__lesson-item:nth-child(2n+1) {
      clear: left; }
  .learning .certificate-view__lessons-list--items-1 .certificate-view__lesson-item:nth-child(odd), .learning .certificate-view__lessons-list--items-1 .certificate-view__lesson-item:nth-child(even) {
    padding-left: 30px;
    padding-right: 30px; }
  .learning .certificate-view__lessons-list--items-2 .certificate-view__lesson-item:nth-child(odd), .learning .certificate-view__lessons-list--items-2 .certificate-view__lesson-item:nth-child(even) {
    padding-left: 30px;
    padding-right: 30px; }
  .learning .certificate-view__lessons-list--items-3 .certificate-view__lesson-item:nth-child(odd), .learning .certificate-view__lessons-list--items-3 .certificate-view__lesson-item:nth-child(even) {
    padding-left: 30px;
    padding-right: 30px; }
  .learning .certificate-view__lesson-item-context {
    position: relative;
    padding-right: 46px; }
  .learning .certificate-view__lesson-title {
    margin-bottom: 5px;
    font-weight: 700;
    margin-top: 0; }
  .learning .certificate-view__lesson-icon {
    font-size: 36px;
    color: #69c057;
    position: absolute;
    right: 0;
    top: -6px;
    line-height: 1em; }
    .learning .certificate-view__lesson-icon .fa {
      vertical-align: top; }
  .learning .certificate-view__logo--main {
    max-width: 200px;
    margin: 30px auto 66px; }
  .learning .certificate-view__logos-additional {
    list-style: none;
    position: relative;
    margin: 0 -15px 30px;
    padding: 30px 0 15px;
    text-align: center;
    font-size: 0; }
    .learning .certificate-view__logos-additional:before {
      content: "";
      top: 0;
      left: 15px;
      right: 15px;
      position: absolute;
      border-top: 1px solid #e7e8e9; }
    .learning .certificate-view__logos-additional:after {
      content: "";
      top: 0;
      left: 15px;
      right: 15px;
      position: absolute;
      border-top: 1px solid #e7e8e9;
      top: auto;
      bottom: 1px; }
  .learning .certificate-view__logos-additional__item {
    position: relative;
    float: left;
    width: 33.33333%;
    min-height: 1px;
    padding-left: 15px;
    padding-right: 15px;
    position: relative;
    min-height: 1px;
    padding-left: 15px;
    padding-right: 15px;
    display: inline-block !important;
    float: none !important;
    margin-bottom: 15px;
    font-size: 14px;
    vertical-align: middle; }
  .learning .certificate-view__footer-btn {
    text-align: center;
    margin-bottom: 30px; }
  .learning .course-section__panel {
    margin-bottom: 0;
    border: none;
    box-shadow: 5px 5px 0 #dadbdd !important; }
    .learning .course-section__panel .panel-body {
      padding-left: 30px;
      padding-right: 30px; }
    .learning .course-section__panel .panel-heading {
      padding-left: 30px;
      padding-right: 30px;
      position: relative; }
      .learning .course-section__panel .panel-heading:after {
        content: "";
        font-size: 58px;
        height: 1em;
        width: 1em;
        background: url(../img/learning/tag.svg) 0 0 no-repeat;
        background-size: cover;
        position: absolute;
        top: -2px;
        right: 20px; }
    .learning .course-section__panel .panel-body {
      background-color: #e7e8e9; }
  .learning h3.course-section__body-title {
    margin: 20px 0 20px;
    font-size: 20px;
    font-family: "Rockwell W01"; }
  .learning .course-section__description {
    margin-bottom: 35px; }
  .learning .course-section__status-icon {
    font-size: 36px;
    line-height: 1em; }
  .learning .course-section__panel-body-footer-col:first-child {
    margin-bottom: 15px; }
  .learning .course-section__status-badge {
    margin-left: 1em; }
  .learning .course-section--complete .course-section__status-icon {
    color: #69c057; }
  .learning .course-section--complete .course-section__status-badge {
    background-color: #69c057; }
  .learning .course-section--failed .course-section__status-icon {
    color: #d9534f; }
  .learning .course-section--failed .course-section__status-badge {
    background-color: #d9534f; }
  .learning .course-section--course-completed .course-section__panel {
    background-color: #dfe5f0;
    text-align: center; }
  .learning .course-section--course-completed .panel-body {
    padding-top: 36px; }
  .learning .course-section--course-completed .course-section__body-title, .learning .course-section--course-completed h3.course-section__body-title {
    color: #445e8c;
    margin-bottom: 30px; }
  .learning .lesson-list__lesson {
    position: relative;
    padding-right: 90px; }
  .learning .lesson-list__btns {
    position: absolute;
    right: 0;
    top: 0; }
    .learning .lesson-list__btns .icon {
      margin-left: 0.5em;
      color: #445e8c; }
      .learning .lesson-list__btns .icon:first-child {
        margin-left: 0; }
    .learning .lesson-list__btns button {
      border: 0;
      background-color: transparent;
      outline: none;
      padding: 0; }
  .learning .lesson-list__body {
    padding: 0;
    border-bottom: 0; }
    .learning .lesson-list__body .list-group-item {
      border-radius: 0;
      border-left: 0;
      border-right: 0; }
      .learning .lesson-list__body .list-group-item:first-child {
        border-top: 0; }
  .learning .assign-users__list .table {
    margin: 0 !important;
    width: 100% !important; }
    .learning .assign-users__list .table tr:first-child td {
      border-top: 0; }
    .learning .assign-users__list .table tr.active .assign-users__list-item__icon {
      display: inline-block; }
  .learning .assign-users__filter {
    margin-bottom: 10px; }
  .learning .assign-users__list-item__icon {
    display: none;
    margin-right: 0.5em; }
  .learning .bootbox-body .alert {
    margin: 0; }
  .learning .bootbox-body .alert--bottom-margin {
    margin-bottom: 1em; }
  .learning .bootbox-body .alert--small {
    font-size: 14px;
    padding: 10px; }
  .learning .input__files {
    margin-top: 10px; }
  .learning .input__files--full {
    clear: left; }
    .learning .input__files--full .input__file {
      position: relative;
      float: left;
      width: 50%;
      min-height: 1px;
      padding-left: 15px;
      padding-right: 15px;
      position: relative;
      min-height: 1px;
      padding-left: 15px;
      padding-right: 15px;
      position: relative;
      min-height: 1px;
      padding-left: 15px;
      padding-right: 15px; }
      .learning .input__files--full .input__file:nth-child(2n+1) {
        clear: left; }
  .learning .input__file-inner {
    position: relative;
    border: 1px solid #ccc;
    margin-bottom: 5px; }
  .learning .input__file-icon {
    font-size: 18px;
    width: 18px;
    line-height: 1;
    text-align: center;
    background-color: #FFF;
    position: absolute;
    top: 3px;
    right: 3px; }
  .learning label.input__file-icon {
    color: #337ab7; }
    .learning label.input__file-icon:hover {
      color: #23527c;
      cursor: pointer; }
  .learning .table-controls__date-filters {
    margin-bottom: 15px; }
  .learning .panel--grey {
    background-color: #eeeeee;
    border: none; }
  .learning .panel-heading--with-buttons {
    position: relative;
    overflow: hidden; }
    .learning .panel-heading--with-buttons .panel-title {
      margin-bottom: 10px; }

@media (min-width: 992px) {
  .learning section#title {
    margin-left: -25px;
    margin-right: -25px;
    padding: 25px 50px; } }
@media (min-width: 992px) {
  .learning div.dataTables_wrapper div.dataTables_info {
    margin-bottom: 0; }
  .learning div.dataTables_wrapper div.dataTables_length, .learning div.dataTables_wrapper div.dataTables_filter {
    text-align: left; }
  .learning div.dataTables_wrapper div.dataTables_paginate {
    text-align: right; } }
@media (min-width: 992px) {
  .learning .page-builder-view__page-nav .btn--back, .learning .page-builder-view__page-nav .btn--next {
    position: absolute; }
  .learning .page-builder-view__page-nav .pagination {
    margin-top: 0; } }
@media (min-width: 1200px) {
  .learning .add-content__item {
    float: left;
    width: 25%; } }
@media (min-width: 768px) {
  .learning .page-list__buttons {
    margin-top: 0;
    position: absolute;
    top: 7.5px;
    right: 7.5px;
    display: none; }
  .learning .page-list__item:hover .page-list__buttons {
    display: block; } }
@media (min-width: 768px) {
  .learning .library-grid-item {
    float: left;
    width: 50%; } }
@media (min-width: 992px) {
  .learning .library-grid-item {
    float: left;
    width: 33.33333%; } }
@media (min-width: 992px) {
  .learning .library-grid-item {
    padding-right: 45px; }
    .learning .library-grid-item:nth-child(2n+1) {
      clear: none; }
    .learning .library-grid-item:nth-child(3n+1) {
      clear: left; } }
@media (min-width: 992px) {
  .learning .library {
    margin-left: -30px;
    margin-right: -30px; }
  .learning .library__section {
    padding-left: 30px;
    padding-right: 30px; }
  .learning .library__section-header--button {
    padding-right: 30px; } }
@media (min-width: 1200px) {
  .learning .library {
    margin-left: -30px;
    margin-right: -30px; }
  .learning .library__section {
    padding-left: 30px;
    padding-right: 30px; } }
@media (min-width: 768px) {
  .learning .course-view__header {
    padding: 60px 60px 30px; }
  .learning .course-view__header-btns {
    text-align: left;
    margin-bottom: 0; }
  .learning .course-view__description {
    margin-bottom: 45px; } }
@media (min-width: 768px) {
  .learning .certificate-view__lesson-item {
    float: left;
    width: 50%; } }
@media (min-width: 768px) {
  .learning .certificate-view__lessons-list--items-1 .certificate-view__lesson-item, .learning .certificate-view__lessons-list--items-2 .certificate-view__lesson-item, .learning .certificate-view__lessons-list--items-3 .certificate-view__lesson-item {
    margin-left: 25%; } }
@media (min-width: 768px) {
  .learning .certificate-view__logos-additional__item {
    float: left;
    width: 20%; } }
@media (min-width: 768px) {
  .learning .certificate-view__header {
    margin-bottom: 105px; }
  .learning .certificate-view__award {
    position: absolute;
    top: 20px;
    left: 50%;
    margin-left: -0.5em; }
  .learning .certificate-view__lesson-item {
    padding: 0 90px; }
    .learning .certificate-view__lesson-item:nth-child(odd) {
      padding-right: 30px; }
    .learning .certificate-view__lesson-item:nth-child(even) {
      padding-left: 30px; } }
@media (min-width: 768px) {
  .learning .course-section__panel-body-footer-col:first-child {
    margin: 0; }
  .learning .course-section__panel-body-footer-col:last-child {
    text-align: right; } }
@media (min-width: 768px) {
  .learning .course-edit__lessons-btns {
    text-align: right; } }
@media (min-width: 768px) {
  .learning .assign-users__list {
    max-height: 400px; } }
@media (min-width: 992px) {
  .learning .assign-users__filter {
    margin-bottom: 0; } }
@media (min-width: 768px) {
  .learning .input__files--full .input__file {
    float: left;
    width: 33.33333%; }
    .learning .input__files--full .input__file:nth-child(2n+1) {
      clear: none; }
    .learning .input__files--full .input__file:nth-child(3n+1) {
      clear: left; } }
@media (min-width: 992px) {
  .learning .input__files--full .input__file {
    float: left;
    width: 20%; }
    .learning .input__files--full .input__file:nth-child(3n+1) {
      clear: none; }
    .learning .input__files--full .input__file:nth-child(5n+1) {
      clear: left; } }
@media (min-width: 768px) {
  .learning .input__files:not(.input__files--full) {
    margin-top: 0; } }
@media (min-width: 768px) {
  .learning .table-controls__date-filters {
    text-align: right; }
  .learning .table-controls__date-filters-col {
    width: auto;
    display: inline-block;
    float: none; } }
@media (min-width: 768px) {
  .learning .panel-heading--with-buttons .panel-title {
    float: left;
    margin: 7px 0 0 0; }
  .learning .panel-heading__buttons {
    float: right; } }
section {
  padding: 30px; }
  section.stacked {
    padding: 15px 30px; }

#page {
  position: relative;
  height: 100%;
  background: #fff !important;
  padding: 0;
  min-height: 800px; }

#wrapper {
  -webkit-transition: all 0.5s ease;
  -moz-transition: all 0.5s ease;
  -o-transition: all 0.5s ease;
  transition: all 0.5s ease; }

#wrapper.toggled {
  padding-left: 250px; }

.modal-backdrop {
  display: block;
  width: 100%;
  height: 100%;
  z-index: 999; }

nav {
  z-index: 1000;
  position: fixed;
  left: 250px;
  width: 0;
  height: 100%;
  margin-left: -250px;
  overflow-y: auto;
  -webkit-transition: all 0.5s ease;
  -moz-transition: all 0.5s ease;
  -o-transition: all 0.5s ease;
  transition: all 0.5s ease; }

#wrapper.toggled nav {
  width: 250px; }

#page-content-wrapper {
  width: 100%;
  position: absolute; }

#wrapper.toggled #page-content-wrapper {
  position: absolute;
  margin-right: -250px; }

.sidebar-nav {
  position: absolute;
  top: 0;
  width: 250px;
  list-style: none; }

#menu-toggle {
  display: none;
  position: absolute;
  z-index: 99;
  top: 30px;
  left: -35px;
  width: 100px;
  -moz-transform: rotate(90deg);
  -webkit-transform: rotate(90deg);
  -o-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
  font-size: 120%; }
  #menu-toggle i {
    margin-right: 5px; }

@media screen and (max-width: 768px) {
  #menu-toggle {
    display: inline-block; } }
@media screen and (min-width: 768px) {
  #wrapper {
    padding-left: 250px; }

  #wrapper.toggled {
    padding-left: 0; }

  nav {
    width: 250px; }

  #wrapper.toggled nav {
    width: 0; }

  #page-content-wrapper {
    position: relative; }

  #wrapper.toggled #page-content-wrapper {
    position: relative;
    margin-right: 0; } }
@media screen and (max-width: 768px) {
  nav {
    position: fixed; } }
@media screen and (min-width: 1200px) {
  #page {
    min-width: 100%; }
    #page.no-nav {
      min-width: 1024px;
      max-width: 1024px; } }
@media screen and (min-width: 1270px) {
  #page {
    min-width: 1270px; }
    #page.no-nav {
      min-width: 1024px;
      max-width: 1024px; } }
body.home-home h1 {
  font-size: 120%;
  font-weight: bold;
  margin: 20px 0; }

#ffFormCreate input, #ffFormCreate select {
  font-size: 0.8em; }
#ffFormCreate .tabs {
  border: 0; }
  #ffFormCreate .tabs .ui-tabs-nav li {
    display: inline-block; }
    #ffFormCreate .tabs .ui-tabs-nav li a {
      display: block;
      border: 1px solid #8194B0;
      border-bottom: 0;
      padding: 10px 20px;
      background: #445E8C;
      outline: none;
      text-decoration: none;
      color: #fff; }
    #ffFormCreate .tabs .ui-tabs-nav li.ui-state-active a {
      position: relative;
      background: #fff;
      color: #333; }
      #ffFormCreate .tabs .ui-tabs-nav li.ui-state-active a:after {
        content: "";
        display: block;
        position: absolute;
        bottom: -1px;
        left: 0;
        right: 0;
        height: 1px;
        background: #fff; }
  #ffFormCreate .tabs .ui-tabs-panel {
    padding: 15px;
    border: 1px solid #8194B0; }
#ffFormCreate #fields .elements h2 {
  margin-bottom: 10px; }
#ffFormCreate #fields .ffForm-target > div {
  background: #445E8C;
  color: #fff;
  overflow: auto; }
  #ffFormCreate #fields .ffForm-target > div h2 a {
    color: #fff; }
  #ffFormCreate #fields .ffForm-target > div h2 p {
    margin-bottom: 15px; }
  #ffFormCreate #fields .ffForm-target > div #dependValue {
    color: #333; }
#ffFormCreate #fields #ffForm li a {
  background: #445E8C;
  border-color: #445E8C;
  color: #fff; }

#conditionalText input {
  color: #333; }

#formOptions h2 {
  margin-bottom: 20px; }
#formOptions p {
  margin: 10px 0; }

.ffaccordion .ui-accordion-content {
  padding: 10px 10px 1px 10px; }

body.dashboard-dashboard .panel-dashboard {
  background: #aac0d5; }
  body.dashboard-dashboard .panel-dashboard div.icon {
    text-align: center;
    margin-top: 10px; }
  body.dashboard-dashboard .panel-dashboard i.fa {
    color: #002663; }
  body.dashboard-dashboard .panel-dashboard .panel-body h1 {
    color: #002663;
    font-size: 150%;
    margin: 10px 0; }
  body.dashboard-dashboard .panel-dashboard .panel-body a.btn {
    background: #fff;
    color: #002663;
    margin: 10px 0; }

body.accidentreporting-printpreview div.col-lg-8 {
  width: 100%; }
body.accidentreporting-printpreview .charts {
  margin-top: 30px; }
body.accidentreporting-printpreview h1, body.accidentreporting-printpreview h2 {
  font-size: 160%; }

body.errors-404 section, body.errors-500 section {
  text-align: center; }
  body.errors-404 section h1, body.errors-500 section h1 {
    font-size: 600%;
    line-height: 100px;
    color: #555; }
  body.errors-404 section h2, body.errors-500 section h2 {
    font-size: 200%;
    line-height: 40px; }

/*# sourceMappingURL=main.css.map */
