.ffForm-target
  > div
    background: #f4f4f4
    border: 1px solid #ccc
    margin-bottom: 10px
    &.error
      border: 1px solid #c30
    h2
      position: relative
      padding: 15px
      a
        color: #333
      .handle
        position: relative
        z-index: 1
        cursor: move
        margin-right: 5px
      .minField
        position: absolute
        top: 0
        right: 0
        bottom: 0
        left: 0
        text-align: right
        padding: 15px 40px 0 0
      .delField
        float: right
        position: relative
        z-index: 1
    > ul
      padding: 0 15px 15px
      display: none
      li
        &:not(:first-of-type)
          margin-top: 10px
        .error
          color: #c30
          display: block
          font-size: 80%
        .checkbox
          input
            position: relative
            top: 1px
    p
      select
        color: #333 !important
      a
        color: #fff
					
#ffForm
  ul
    li
      margin-bottom: 10px
      a
        display: block
        padding: 15px
        background: #f4f4f4
        border: 1px solid #ccc
        color: #333
        text-decoration: none
				
#ffgenerated
  background: #efefef
  border: 1px solid #ccc
  padding: 0 15px
  display: flex
  flex-wrap: wrap
  margin-bottom: 20px
  p
    margin: 15px 0
  label
    font-weight: bold
    display: block
    margin-bottom: 2px
    &.radio-inline,
    &.checkbox-inline
      font-weight: normal
      display: inline-block
  h2.ffheader
    font-weight: bolder
    margin-top: 30px
  hr
    margin-top: 10px
    margin-bottom: 10px
    border-top: 1px solid #032862

				