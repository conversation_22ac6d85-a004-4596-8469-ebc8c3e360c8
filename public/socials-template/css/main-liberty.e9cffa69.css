.page--lms {
  padding-bottom: 45px;
  margin-bottom: 0; }
  @media (min-width: 992px) {
    .page--lms {
      padding-left: 15px;
      padding-right: 15px; } }
  @media (min-width: 1200px) {
    .page--lms {
      padding-left: 15px;
      padding-right: 15px; } }
  .page--lms header {
    border-bottom: 0; }
  .page--lms p {
    margin-bottom: 20px; }
  .page--lms h2 {
    font-size: 20px;
    color: #445E8C; }
  .page--lms h3 {
    font-size: 17px;
    color: #445E8C;
    font-weight: bold;
    margin: 24px 0 15px 0; }
  .page--lms .btn-danger {
    background-color: #d9534f;
    border-color: #d43f3a; }
    .page--lms .btn-danger:hover {
      background-color: #c9302c; }
  .page--lms .btn-success {
    background-color: #69c057;
    border-color: #4cae4c; }
    .page--lms .btn-success:hover {
      background-color: #50a63e; }
  .page--lms .btn-default {
    background-color: #fff;
    border-color: #ccc;
    color: #333 !important; }
    .page--lms .btn-default:hover {
      background-color: #e6e6e6; }
  .page--lms div.dataTables_wrapper div.dataTables_length {
    margin-top: 10px;
    padding-top: 6px; }
  .page--lms .panel-primary {
    border-color: #445E8C; }
    .page--lms .panel-primary .panel-title {
      color: #FFF; }
  .page--lms .panel-primary > .panel-heading {
    border-radius: 0;
    background-color: #445E8C;
    border-color: #445E8C; }
  .page--lms .panel-title {
    margin: 0;
    color: #555555; }
  .page--lms .alert-success {
    color: #69c057; }
  .page--lms .alert-danger {
    color: #d9534f; }

.page--lms.page {
  margin: 0 0 15px 0; }

.page--lms hr {
  border-color: #e7e8e9; }

.page--lms h1.page__title {
  border-bottom: 1px solid #e7e8e9;
  padding: 25px 0;
  margin: 0; }

@media (min-width: 992px) {
  .page--lms .page__header {
    margin-left: -30px;
    margin-right: -30px; }
  .page--lms h1.page__title {
    padding-left: 30px;
    padding-right: 30px; } }

@media (min-width: 1200px) {
  .page--lms .page__header {
    margin-left: -30px;
    margin-right: -30px; }
  .page--lms h1.page__title {
    padding-left: 30px;
    padding-right: 30px; } }

.page--lms table.table-bordered.dataTable {
  margin-top: 0 !important; }

.page--lms div.dataTables_wrapper div.dataTables_length,
.page--lms div.dataTables_wrapper div.dataTables_filter,
.page--lms div.dataTables_wrapper div.dataTables_info,
.page--lms div.dataTables_wrapper div.dataTables_paginate {
  text-align: center; }

.page--lms div.dataTables_wrapper div.dataTables_info {
  margin-bottom: 10px; }

.page--lms .table--striped-tbody .odd {
  background-color: #f9f9f9; }

.page--lms .tr--child td:first-child {
  padding-left: 28px; }

@media (min-width: 992px) {
  .page--lms div.dataTables_wrapper div.dataTables_info {
    margin-bottom: 0; }
  .page--lms div.dataTables_wrapper div.dataTables_length,
  .page--lms div.dataTables_wrapper div.dataTables_filter {
    text-align: left; }
  .page--lms div.dataTables_wrapper div.dataTables_paginate {
    text-align: right; } }

.page--lms div[contenteditable].form-control {
  height: auto; }

.page--lms .select--with-text {
  width: auto;
  display: inline-block; }

.page--lms .form-control--text-only,
.page--lms .form-control--html {
  border-color: transparent;
  background-color: transparent;
  box-shadow: none;
  height: auto;
  min-height: 34px;
  padding-left: 0; }

.page--lms .form-control--html {
  padding-right: 0; }

.page--lms .form-control--html-no-padding {
  padding-top: 0;
  padding-bottom: 0; }

.page--lms .control-label small {
  display: block;
  color: #777777;
  font-weight: normal;
  font-size: 12px;
  line-height: 1.2em; }

.page--lms .form__validation-message .error {
  color: #d9534f; }

.page--lms .icon-sortable,
.page--lms .icon-delete,
.page--lms .icon-duplicate,
.page--lms .icon-edit {
  font-size: 14px;
  opacity: 0.4;
  line-height: 1em; }
  .page--lms .icon-sortable:hover,
  .page--lms .icon-delete:hover,
  .page--lms .icon-duplicate:hover,
  .page--lms .icon-edit:hover {
    opacity: 1;
    text-decoration: none; }

.page--lms .icon-sortable {
  position: relative; }
  .page--lms .icon-sortable:hover {
    opacity: 1; }

.page--lms .btn:not(.btn-xs) {
  min-width: 62px; }

.page--lms .badge--rectangle {
  border-radius: 0; }

.page--lms .badge--btn-size {
  font-size: 14px;
  padding: 7px 13px;
  line-height: 1.428571429;
  min-width: 150px; }

.page--lms .badge--success {
  background-color: #69c057; }

.page--lms .badge--danger {
  background-color: #d9534f; }

.page--lms .award {
  color: #445E8C;
  line-height: 1em;
  background: url(/assets/img/award.svg);
  height: 1em;
  width: 1em;
  background-size: cover;
  margin: 0 auto; }

.page--lms .award--large {
  font-size: 150px; }

.page--lms .page__content {
  margin-top: 30px; }

.page--lms .progress {
  height: 35px;
  border-radius: 20px;
  background-color: transparent;
  border: 3px solid #f0ad4e; }

.page--lms .progress-bar {
  border-radius: 0 20px 20px 0;
  box-shadow: none; }

.page--lms .page-builder__item-content {
  clear: both;
  margin-bottom: 15px; }
  .page--lms .page-builder__item-content .editor__content div[contenteditable] {
    min-height: 300px; }

.page--lms .page-builder__item-buttons--complete {
  margin: 0 auto 15px auto;
  text-align: center; }

.page--lms .page-builder__item-title {
  cursor: pointer; }
  .page--lms .page-builder__item-title .icon-sortable,
  .page--lms .page-builder__item-title .icon-delete,
  .page--lms .page-builder__item-title .icon-duplicate {
    float: right;
    margin-left: 1em; }
  .page--lms .page-builder__item-title .icon-sortable {
    margin-top: 1px; }

.page--lms .page-builder__item--document-upload__file-list li {
  position: relative;
  padding-right: 30px; }

.page--lms .page-builder__item--document-upload__file-list .icon-delete {
  position: absolute;
  right: 10px;
  top: 10px;
  color: #777777; }

.page--lms .page-builder-view__page {
  padding: 30px 0; }

.page--lms .page-builder-view__progress {
  margin: -6px 0 0 0;
  display: block; }
  .page--lms .page-builder-view__progress li > a {
    margin-right: 0.5em;
    border-radius: 6px;
    color: #555555;
    border: none; }
  .page--lms .page-builder-view__progress li:last-child a {
    margin-right: 0; }
  .page--lms .page-builder-view__progress .active a {
    background-color: #69c057;
    cursor: pointer;
    color: #FFF; }

.page--lms .page-builder-view__page-item {
  margin-bottom: 30px; }

.page--lms .page-builder-view__page-item--image {
  text-align: center; }
  .page--lms .page-builder-view__page-item--image img {
    max-width: 100%; }

.page--lms .page-builder-view__page-item--image,
.page--lms .page-builder-view__page-item--video-embed,
.page--lms .page-builder-view__page-item--video-upload {
  padding: 30px;
  background-color: #eeeeee; }

.page--lms .page-builder-view__page-item--text-rich b,
.page--lms .page-builder-view__page-item--text-rich strong {
  font-weight: bold; }

.page--lms .page-builder-view__page-item--text-rich i,
.page--lms .page-builder-view__page-item--text-rich em {
  font-style: italic; }

.page--lms .page-builder-view__page-item--text-rich p {
  margin: 0 0 1em 0; }

.page--lms .page-builder-view__page-item--text-rich ul,
.page--lms .page-builder-view__page-item--text-rich ol {
  margin: 0 0 1em 1em; }
  .page--lms .page-builder-view__page-item--text-rich ul li,
  .page--lms .page-builder-view__page-item--text-rich ol li {
    margin-bottom: 0.5em; }

.page--lms .page-builder-view__page-item--text-rich ul {
  list-style: disc; }

.page--lms .page-builder-view__page-item--text-rich ol {
  list-style: decimal; }

.page--lms .page-builder-view__page-item--text-rich h1,
.page--lms .page-builder-view__page-item--text-rich h2 {
  font-size: 26px;
  font-weight: normal; }

.page--lms .page-builder-view__page-item--text-rich h3,
.page--lms .page-builder-view__page-item--text-rich h4,
.page--lms .page-builder-view__page-item--text-rich h5,
.page--lms .page-builder-view__page-item--text-rich h6 {
  font-size: 22px;
  font-weight: normal; }

.page--lms .page-builder-view__page-item--heading h2 {
  font-size: 26px; }

.page--lms .page-builder-view__page-item--document-upload h3 {
  font-size: 22px;
  font-weight: normal; }

.page--lms .page-builder-view__page-item--question {
  margin: 60px 0; }

.page--lms .page-builder-view__page-item__question-text {
  color: #445E8C;
  margin-bottom: 0.5em;
  font-weight: normal;
  font-size: 18px; }

.page--lms .page-builder-view__page-item-answer.answer--chosen label:after {
  content: ' - chosen incorrect answer'; }

.page--lms .page-builder-view__page-item-answer.answer--correct label:after {
  content: ' - correct answer'; }

.page--lms .page-builder-view__page-item-answer.answer--chosen.answer--correct label:after {
  content: ' - chosen correct answer'; }

.page--lms .page-builder-view__page-item__select-text {
  font-style: italic;
  color: #777777;
  margin-top: 1em; }

.page--lms .page-builder-view__page-nav {
  color: #777777;
  position: relative;
  text-align: center; }
  .page--lms .page-builder-view__page-nav .btn--back {
    background-color: #777777;
    left: 0; }
  .page--lms .page-builder-view__page-nav .btn--next {
    right: 0; }
  .page--lms .page-builder-view__page-nav .pagination li a {
    color: #777777;
    border: none; }
  .page--lms .page-builder-view__page-nav .pagination .active a {
    color: #FFF;
    background-color: #777777; }

.page--lms .page-builder-view__page-results__failed,
.page--lms .page-builder-view__page-results__passed {
  margin: 0 0 30px;
  background-color: transparent;
  border: none;
  position: relative;
  padding-left: 50px; }
  .page--lms .page-builder-view__page-results__failed .fa,
  .page--lms .page-builder-view__page-results__passed .fa {
    font-size: 40px;
    position: absolute;
    top: 15px;
    left: 0; }
  .page--lms .page-builder-view__page-results__failed .alert__title,
  .page--lms .page-builder-view__page-results__passed .alert__title {
    font-weight: bold;
    margin-bottom: 0; }
  .page--lms .page-builder-view__page-results__failed .alert__body,
  .page--lms .page-builder-view__page-results__passed .alert__body {
    color: #555555;
    margin-top: 0; }

@media (min-width: 992px) {
  .page--lms .page-builder-view__page-nav .btn--back,
  .page--lms .page-builder-view__page-nav .btn--next {
    position: absolute; }
  .page--lms .page-builder-view__page-nav .pagination {
    margin-top: 0; } }

.page--lms .add-content {
  list-style: none;
  padding: 0;
  margin: 0 -7.5px; }
  .page--lms .add-content:before, .page--lms .add-content:after {
    content: " ";
    display: table; }
  .page--lms .add-content:after {
    clear: both; }

.page--lms .add-content__item {
  position: relative;
  float: left;
  width: 50%;
  min-height: 1px;
  padding-left: 15px;
  padding-right: 15px;
  position: relative;
  min-height: 1px;
  padding-left: 15px;
  padding-right: 15px;
  padding-left: 7.5px;
  padding-right: 7.5px;
  margin-bottom: 15px; }
  @media (min-width: 1200px) {
    .page--lms .add-content__item {
      float: left;
      width: 25%; } }

.page--lms .add-content__item-button {
  display: block;
  text-align: center;
  padding: 7.5px; }

.page--lms .add-content__item-icon {
  display: block;
  font-size: 30px; }

.page--lms .document-downloads {
  list-style: none;
  margin-top: 30px;
  padding: 0; }
  .page--lms .document-downloads:before, .page--lms .document-downloads:after {
    content: " ";
    display: table; }
  .page--lms .document-downloads:after {
    clear: both; }

.page--lms .document-downloads__item {
  margin-bottom: 15px; }

.page--lms .document-downloads__item-btn {
  white-space: normal;
  display: block;
  text-align: left;
  background-color: #eeeeee;
  border: none;
  color: #445E8C !important;
  padding: 12px 18px; }
  .page--lms .document-downloads__item-btn:hover {
    background-color: #e2e2e2; }

.page--lms .document-downloads__btn-context {
  display: block;
  position: relative;
  padding-left: 36px;
  line-height: 1.3em; }

.page--lms .document-downloads__item-icon {
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
      -ms-transform: translateY(-50%);
          transform: translateY(-50%);
  left: 0;
  font-size: 24px; }

.page--lms .editor__toolbar {
  margin-bottom: 15px; }

.page--lms .page-list__buttons {
  margin-top: 7.5px; }

.page--lms .page-list__item {
  position: relative; }
  .page--lms .page-list__item .page-list__item-name {
    display: block;
    padding-right: 36px; }
  .page--lms .page-list__item.active .page-list__item-name {
    color: #FFF; }
  .page--lms .page-list__item .page-list__item-input-group {
    display: none; }
    .page--lms .page-list__item .page-list__item-input-group .input-group {
      margin-top: 7.5px; }
  .page--lms .page-list__item .icon-sortable {
    position: absolute;
    right: 15px;
    top: 13px; }

@media (min-width: 768px) {
  .page--lms .page-list__buttons {
    margin-top: 0;
    position: absolute;
    top: 7.5px;
    right: 7.5px;
    display: none; }
  .page--lms .page-list__item:hover .page-list__buttons {
    display: block; } }

.page--lms .test-question__buttons--add {
  margin-top: 7.5px; }

.page--lms .test-question__radio-legend {
  text-align: center; }

.page--lms .test-question__radio input[type="radio"],
.page--lms .test-question__radio input[type="checkbox"],
.page--lms .test-question__checkbox input[type="radio"],
.page--lms .test-question__checkbox input[type="checkbox"] {
  position: relative;
  margin: 9px auto 0;
  display: block; }

.page--lms .test-question__answer-input-holder {
  position: relative;
  padding-right: 20px; }

.page--lms .test-question__answer-delete {
  position: absolute;
  top: 7px;
  color: #333333;
  right: 0; }

.page--lms .test-question__answer:first-child .test-question__answer-delete,
.page--lms .test-question__answer:nth-child(2) .test-question__answer-delete,
.page--lms .test-question__checkbox:nth-child(3) .test-question__answer-delete {
  display: none; }

.page--lms .library {
  margin: 0 -15px; }

.page--lms .library__section {
  padding: 30px 15px; }
  .page--lms .library__section.library__section--highlight {
    background-color: #e7e8e9; }
    .page--lms .library__section.library__section--highlight .library-item {
      border-color: #e7e8e9;
      margin-bottom: 60px; }

.page--lms h2.library__section-title {
  margin: 0 0 40px;
  font-size: 20px; }

.page--lms .library-item {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e7e8e9; }

.page--lms .library-grid-item {
  position: relative;
  min-height: 1px;
  padding-left: 15px;
  padding-right: 15px;
  position: relative;
  min-height: 1px;
  padding-left: 15px;
  padding-right: 15px; }
  @media (min-width: 768px) {
    .page--lms .library-grid-item {
      float: left;
      width: 50%; } }
  @media (min-width: 992px) {
    .page--lms .library-grid-item {
      float: left;
      width: 33.3333333333%; } }
  .page--lms .library-grid-item:nth-child(2n+1) {
    clear: left; }
  @media (min-width: 992px) {
    .page--lms .library-grid-item {
      padding-right: 45px; }
      .page--lms .library-grid-item:nth-child(2n+1) {
        clear: none; }
      .page--lms .library-grid-item:nth-child(3n+1) {
        clear: left; } }

.page--lms .library-item__body:before, .page--lms .library-item__body:after {
  content: " ";
  display: table; }

.page--lms .library-item__body:after {
  clear: both; }

.page--lms .library-item__btns {
  text-align: right;
  margin-top: 20px; }

.page--lms .library-item__image {
  position: relative;
  background-color: #000;
  border-radius: 3px;
  overflow: hidden; }
  .page--lms .library-item__image img {
    opacity: 0.6; }
  .page--lms .library-item__image .progress {
    position: absolute;
    top: 50%;
    width: 50%;
    left: 50%;
    margin-left: -25%;
    -webkit-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
            transform: translateY(-50%);
    height: 35px;
    border-radius: 20px;
    background-color: transparent;
    border: 3px solid #f0ad4e; }
  .page--lms .library-item__image .progress-bar {
    border-radius: 0 20px 20px 0;
    box-shadow: none; }
  .page--lms .library-item__image .library-item__image-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    font-size: 50px;
    margin: -0.5em 0 0 -0.5em;
    color: #f0ad4e; }
    .page--lms .library-item__image .library-item__image-icon.icon--pending {
      background: url(/img/icon-training-book.png) 50% 50% no-repeat;
      font-size: 51px;
      width: 1em;
      height: 1em; }
    .page--lms .library-item__image .library-item__image-icon.icon--unpublished {
      font-size: 16px;
      text-align: center;
      width: 100%;
      height: auto;
      left: 0;
      line-height: 1.2em;
      -webkit-transform: translateY(-50%);
          -ms-transform: translateY(-50%);
              transform: translateY(-50%);
      margin: 0; }

.page--lms .library-item__organisation:before {
  content: 'Organisation: '; }

.page--lms .library__section-header--button .btn,
.page--lms .library__section-header--button .library__section-header__form {
  float: right;
  margin-left: 1em; }

@media (min-width: 992px) {
  .page--lms .library {
    margin-left: -30px;
    margin-right: -30px; }
  .page--lms .library__section {
    padding-left: 30px;
    padding-right: 30px; }
  .page--lms .library__section-header--button {
    padding-right: 30px; } }

@media (min-width: 1200px) {
  .page--lms .library {
    margin-left: -30px;
    margin-right: -30px; }
  .page--lms .library__section {
    padding-left: 30px;
    padding-right: 30px; } }

.page--lms .library-item--has-status .library-item__body-header:before, .page--lms .library-item--has-status .library-item__body-header:after {
  content: " ";
  display: table; }

.page--lms .library-item--has-status .library-item__body-header:after {
  clear: both; }

.page--lms .library-item--has-status .library-item__body-header .library-item__status {
  float: right;
  margin-top: 24px;
  margin-left: 10px; }

.page--lms .library-item--in-progress .library-item__status {
  background-color: #f0ad4e; }

.page--lms .library-item--completed .library-item__status {
  background-color: #69c057; }

.page--lms .library-item--completed .library-item__image-icon {
  color: #69c057; }

.page--lms .page-jumbo-header {
  text-align: center;
  background-color: #dfe5f0;
  background-repeat: no-repeat;
  background-position: 50% 50%;
  background-size: cover;
  position: relative;
  color: #FFF;
  padding: 90px 30px 75px;
  margin-top: 30px; }
  .page--lms .page-jumbo-header:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7); }

.page--lms h2.page-jumbo-header__title {
  color: #f0ad4e;
  margin-bottom: 45px;
  font-size: 24px;
  font-family: "Rockwell W01";
  line-height: 1.3em; }

.page--lms .page-jumbo-header__context {
  position: relative; }

.page--lms .page-jumbo-header--has-description {
  padding: 30px 30px 15px; }
  .page--lms .page-jumbo-header--has-description h2.page-jumbo-header__title {
    margin-bottom: 20px; }

.page--lms .course-view__description {
  margin-bottom: 30px; }

.page--lms .course-view__header-btns {
  margin-bottom: 30px; }

.page--lms .course-view__header-progress {
  text-align: right; }

.page--lms .course-view__sections {
  margin-bottom: 90px; }

.page--lms .course-view__section,
.page--lms .course-view__section:nth-child(n) {
  padding: 90px 0 0 0;
  position: relative; }
  .page--lms .course-view__section:before,
  .page--lms .course-view__section:nth-child(n):before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    margin-left: -2px;
    width: 4px;
    height: 90px;
    background-color: #eeeeee; }

@media (min-width: 768px) {
  .page--lms .course-view__header {
    padding: 60px 60px 30px; }
  .page--lms .course-view__header-btns {
    text-align: left;
    margin-bottom: 0; }
  .page--lms .course-view__description {
    margin-bottom: 45px; } }

.page--lms h2.certificate-view__title {
  margin-bottom: 24px; }

.page--lms h2.certificate-view__title-name {
  color: #445E8C;
  font-size: 26px;
  font-weight: bold;
  margin: 24px 0 24px; }

.page--lms .certificate-view__award {
  background-image: url(/assets/img/award--white-tick.svg); }

.page--lms .certificate-view__title-course-title {
  font-weight: normal;
  line-height: 1;
  color: #777777;
  margin: 20px 0 30px;
  font-weight: bold; }

.page--lms .certificate-view__body-header {
  text-align: center;
  padding-bottom: 30px; }

.page--lms .certificate-view__lessons-list {
  list-style: none;
  border-top: 1px solid #e7e8e9;
  margin: 0 -15px 30px;
  padding: 30px 0 0 0; }

.page--lms .certificate-view__lesson-item {
  position: relative;
  min-height: 1px;
  padding-left: 15px;
  padding-right: 15px;
  margin-bottom: 30px; }
  @media (min-width: 768px) {
    .page--lms .certificate-view__lesson-item {
      float: left;
      width: 50%; } }
  .page--lms .certificate-view__lesson-item:nth-child(2n+1) {
    clear: left; }

@media (min-width: 768px) {
  .page--lms .certificate-view__lessons-list--items-1 .certificate-view__lesson-item,
  .page--lms .certificate-view__lessons-list--items-2 .certificate-view__lesson-item,
  .page--lms .certificate-view__lessons-list--items-3 .certificate-view__lesson-item {
    margin-left: 25%; } }

.page--lms .certificate-view__lessons-list--items-1 .certificate-view__lesson-item:nth-child(odd), .page--lms .certificate-view__lessons-list--items-1 .certificate-view__lesson-item:nth-child(even),
.page--lms .certificate-view__lessons-list--items-2 .certificate-view__lesson-item:nth-child(odd),
.page--lms .certificate-view__lessons-list--items-2 .certificate-view__lesson-item:nth-child(even),
.page--lms .certificate-view__lessons-list--items-3 .certificate-view__lesson-item:nth-child(odd),
.page--lms .certificate-view__lessons-list--items-3 .certificate-view__lesson-item:nth-child(even) {
  padding-left: 30px;
  padding-right: 30px; }

.page--lms .certificate-view__lesson-item-context {
  position: relative;
  padding-right: 46px; }

.page--lms .certificate-view__lesson-title {
  margin-bottom: 5px;
  font-weight: bold;
  margin-top: 0; }

.page--lms .certificate-view__lesson-icon {
  font-size: 36px;
  color: #69c057;
  position: absolute;
  right: 0;
  top: -6px;
  line-height: 1em; }
  .page--lms .certificate-view__lesson-icon .fa {
    vertical-align: top; }

.page--lms .certificate-view__logo--main {
  max-width: 200px;
  margin: 30px auto 66px; }

.page--lms .certificate-view__logos-additional {
  list-style: none;
  position: relative;
  margin: 0 -15px 30px;
  padding: 30px 0 15px;
  text-align: center;
  font-size: 0; }
  .page--lms .certificate-view__logos-additional:before, .page--lms .certificate-view__logos-additional:after {
    content: '';
    top: 0;
    left: 15px;
    right: 15px;
    position: absolute;
    border-top: 1px solid #e7e8e9; }
  .page--lms .certificate-view__logos-additional:after {
    top: auto;
    bottom: 1px; }

.page--lms .certificate-view__logos-additional__item {
  position: relative;
  float: left;
  width: 33.3333333333%;
  min-height: 1px;
  padding-left: 15px;
  padding-right: 15px;
  position: relative;
  min-height: 1px;
  padding-left: 15px;
  padding-right: 15px;
  display: inline-block !important;
  float: none !important;
  margin-bottom: 15px;
  font-size: 14px;
  vertical-align: middle; }
  @media (min-width: 768px) {
    .page--lms .certificate-view__logos-additional__item {
      float: left;
      width: 20%; } }

.page--lms .certificate-view__footer-btn {
  text-align: center;
  margin-bottom: 30px; }

@media (min-width: 768px) {
  .page--lms .certificate-view__header {
    margin-bottom: 105px; }
  .page--lms .certificate-view__award {
    position: absolute;
    top: 20px;
    left: 50%;
    margin-left: -0.5em; }
  .page--lms .certificate-view__lesson-item {
    padding: 0 90px; }
    .page--lms .certificate-view__lesson-item:nth-child(odd) {
      padding-right: 30px; }
    .page--lms .certificate-view__lesson-item:nth-child(even) {
      padding-left: 30px; } }

.page--lms .course-section__panel {
  margin-bottom: 0;
  border: none;
  box-shadow: 5px 5px 0 #dadbdd !important; }
  .page--lms .course-section__panel .panel-body,
  .page--lms .course-section__panel .panel-heading {
    padding-left: 30px;
    padding-right: 30px; }
  .page--lms .course-section__panel .panel-heading {
    position: relative; }
    .page--lms .course-section__panel .panel-heading:after {
      content: '';
      font-size: 58px;
      height: 1em;
      width: 1em;
      background: url(/assets/img/tag.svg) 0 0 no-repeat;
      background-size: cover;
      position: absolute;
      top: -2px;
      right: 20px; }
  .page--lms .course-section__panel .panel-body {
    background-color: #e7e8e9; }

.page--lms h3.course-section__body-title {
  margin: 20px 0 20px;
  font-size: 20px;
  font-family: "Rockwell W01"; }

.page--lms .course-section__description {
  margin-bottom: 35px; }

.page--lms .course-section__status-icon {
  font-size: 36px;
  line-height: 1em; }

.page--lms .course-section__panel-body-footer-col:first-child {
  margin-bottom: 15px; }

.page--lms .course-section__status-badge {
  margin-left: 1em; }

.page--lms .course-section--complete .course-section__status-icon {
  color: #69c057; }

.page--lms .course-section--complete .course-section__status-badge {
  background-color: #69c057; }

.page--lms .course-section--failed .course-section__status-icon {
  color: #d9534f; }

.page--lms .course-section--failed .course-section__status-badge {
  background-color: #d9534f; }

.page--lms .course-section--course-completed .course-section__panel {
  background-color: #dfe5f0;
  text-align: center; }

.page--lms .course-section--course-completed .panel-body {
  padding-top: 36px; }

.page--lms .course-section--course-completed .course-section__body-title,
.page--lms .course-section--course-completed h3.course-section__body-title {
  color: #445E8C;
  margin-bottom: 30px; }

@media (min-width: 768px) {
  .page--lms .course-section__panel-body-footer-col:first-child {
    margin: 0; }
  .page--lms .course-section__panel-body-footer-col:last-child {
    text-align: right; } }

.page--lms .lesson-list__lesson {
  position: relative;
  padding-right: 90px; }

.page--lms .lesson-list__btns {
  position: absolute;
  right: 0;
  top: 0; }
  .page--lms .lesson-list__btns .icon {
    margin-left: 0.5em;
    color: #445E8C; }
    .page--lms .lesson-list__btns .icon:first-child {
      margin-left: 0; }

.page--lms .lesson-list__body {
  padding: 0;
  border-bottom: 0; }
  .page--lms .lesson-list__body .list-group-item {
    border-radius: 0;
    border-left: 0;
    border-right: 0; }
    .page--lms .lesson-list__body .list-group-item:first-child {
      border-top: 0; }

@media (min-width: 768px) {
  .page--lms .course-edit__lessons-btns {
    text-align: right; } }

.page--lms .assign-users__list .table {
  margin: 0 !important;
  width: 100% !important; }
  .page--lms .assign-users__list .table tr:first-child td {
    border-top: 0; }
  .page--lms .assign-users__list .table tr.active .assign-users__list-item__icon {
    display: inline-block; }

.page--lms .assign-users__filter {
  margin-bottom: 10px; }

.page--lms .assign-users__list-item__icon {
  display: none;
  margin-right: 0.5em; }

@media (min-width: 768px) {
  .page--lms .assign-users__list {
    max-height: 400px; } }

@media (min-width: 992px) {
  .page--lms .assign-users__filter {
    margin-bottom: 0; } }

.page--lms .bootbox-body .alert {
  margin: 0; }

.page--lms .bootbox-body .alert--bottom-margin {
  margin-bottom: 1em; }

.page--lms .bootbox-body .alert--small {
  font-size: 14px;
  padding: 10px; }

.page--lms .input__files {
  margin-top: 10px; }

.page--lms .input__files--full {
  clear: left; }
  .page--lms .input__files--full .input__file {
    position: relative;
    float: left;
    width: 50%;
    min-height: 1px;
    padding-left: 15px;
    padding-right: 15px;
    position: relative;
    min-height: 1px;
    padding-left: 15px;
    padding-right: 15px;
    position: relative;
    min-height: 1px;
    padding-left: 15px;
    padding-right: 15px; }
    @media (min-width: 768px) {
      .page--lms .input__files--full .input__file {
        float: left;
        width: 33.3333333333%; } }
    @media (min-width: 992px) {
      .page--lms .input__files--full .input__file {
        float: left;
        width: 20%; } }

.page--lms .input__file-inner {
  position: relative;
  border: 1px solid #ccc;
  margin-bottom: 5px; }

.page--lms .input__file-icon {
  font-size: 18px;
  width: 18px;
  line-height: 1;
  text-align: center;
  background-color: #FFF;
  position: absolute;
  top: 3px;
  right: 3px; }

@media (min-width: 768px) {
  .page--lms .input__files:not(.input__files--full) {
    margin-top: 0; } }

.page--lms .table-controls__date-filters {
  margin-bottom: 15px; }

@media (min-width: 768px) {
  .page--lms .table-controls__date-filters {
    text-align: right; }
  .page--lms .table-controls__date-filters-col {
    width: auto;
    display: inline-block;
    float: none; } }

.page--lms .panel--grey {
  background-color: #eeeeee;
  border: none; }

.page--lms .panel-heading--with-buttons {
  position: relative;
  overflow: hidden; }
  .page--lms .panel-heading--with-buttons .panel-title {
    margin-bottom: 10px; }

@media (min-width: 768px) {
  .page--lms .panel-heading--with-buttons .panel-title {
    float: left;
    margin: 7px 0 0 0; }
  .page--lms .panel-heading__buttons {
    float: right; } }
