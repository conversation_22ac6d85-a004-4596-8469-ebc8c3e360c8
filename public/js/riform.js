$(function(){

  // ui tabs
  var tabs = $('.tabs').tab() ;
  
  // document action
  $('*[name=formType]').change( function() {
    $('.form-document-type').slideToggle() ;
    if($(this).val() == 'categorised') {
      $('.levels').attr('required', '');
    } else {
      $('.levels').removeAttr('required');
    }
  }) ;
  
	// enable form builder
	$('#ffForm').ffForm({
		dragIcon:  		'<i class="fa fa-arrows"></i>',
		closeIcon:  	'<i class="fa fa-times"></i>',
		minIcon:  	  '<i class="fa fa-caret-down"></i>',
		maxIcon:  	  '<i class="fa fa-caret-right"></i>',
		form:         $('#liberty-form'),

		complete: function( json ) {
  		
  		if(JSON.parse(json).length == 0){
    		alert('You must add at least one field') ;
        tabs.tabs('option','active', 0);
  		} else {

    		// first check we have form options fields
    		// validate other form
    		// if fail go to it
    		tabs.tabs('option','active', 1);
        if( $("#formOptions").valid() ) {

            var isDtrForm = $('input[name="formType"]:checked').val() == 'dtr';
            if (isDtrForm) {
                toggleCsrFieldCheckbox(isDtrForm);
            }

          //tabs.tabs('option','active', 0);
          var options = $("#formOptions").serializeArray();
          var o = {} ;
          var formId = '' ;
          $.each(options, function(i, v){
            if(v.name == 'ID')
              formId = v.value ;
            else
              o[v.name] = v.value ;
          })
          json = JSON.parse(json) ;
          o['fields'] = json ;
          

            
          // notify settings
          if( !o['notify_group_user'] )
            o['notify_group_user'] = 0 ;
          if( !o['notify_liberty_admin'] )
            o['notify_liberty_admin'] = 0 ;
  
          console.log(o['fields']) ;
          
          // check if its an existing form
          $.ajaxSetup({
            headers: {
              'X-CSRF-TOKEN': $('input[name="_token"]').val()
            }
          });
          console.log('here');
          if(formId) {
            $.ajax({
                type: "POST",

                url: "/risk-improvement/form/"+formId,
                data: { form: JSON.stringify(o) }
            }).done(function( data ) {
              window.location.href = "/risk-improvement/form/updated";
            });
          } else {
            $.post( "/risk-improvement/form/create", { form: JSON.stringify(o) })
            .done(function( data ) {
              window.location.href = "/risk-improvement/form";
            });
          }
        }
      }
  		
		}
	}) ;
	
	
  $('#ffGenerate2').on('click', function(e){
    e.preventDefault() ;
    $('#ffGenerate').trigger('click') ;
  });

  var selectedFormType = $('input[name="formType"]:checked').val();
  var isDtrForm = selectedFormType === 'dtr';
  if (isDtrForm) {
      toggleCsrFieldCheckbox(isDtrForm);
  }
  
  $('input[name="formType"]').change(function() {
    // Check the value of the selected radio button
    var selectedValue = $('input[name="formType"]:checked').val();
    if(selectedValue === 'dtr') {
        isDtrForm = true;
        toggleCsrFieldCheckbox(isDtrForm);
        return;
    }
    isDtrForm = false;
    toggleCsrFieldCheckbox(isDtrForm);
  });

  $(document).on('click','#liberty-form > div', function(evt) {
    if (isDtrForm) {
        let csrField = $(this).find('input[name="CSR_Field"]');
        if(csrField.length) {
            csrField.parent().parent().parent().hide();
            csrField.prop('checked', true);
            csrField.val('1');
        }
    }
  });

  function toggleCsrFieldCheckbox(hidden) {
    $('#liberty-form > div').find('input[name="CSR_Field"]').each(function(key, item) {
        if(hidden) {
            $(item).parent().parent().parent().hide();
            $(item).prop('checked', true);
            $(item).val('1');
            return;
        }
        $(item).prop('checked', false);
        $(item).val('0');
        $(item).parent().parent().parent().show();
    });
  }
    // Unescape text field's values
    $('[data-field-type="text"]').each(function() {
        let value = $(this).val();
        let newValue = unescapeHtml(value);
        $(this).val(newValue);
    });

    function unescapeHtml(htmlString) {
        let tempElement = document.createElement("div");
        tempElement.innerHTML = htmlString;
        return tempElement.textContent || tempElement.innerText || "";
    }
});