
// ucwords helper
var ucwords = function(str) {
  return (str + '').replace(/^([a-z\u00E0-\u00FC])|\s+([a-z\u00E0-\u00FC])/g, function($1) {
    return $1.toUpperCase();
  });
};

var field_filter_init = function(el) {
  if (el) {
    var input = el.find(':input'),
        checked = input.filter(':checked'),
        selects = input.filter('select');

    input.on('change',function(e) {
        field_filter($(this));
    });

    if (checked.length) {
        checked.trigger('change');
    }

    if (selects.length) {
      selects.trigger('change');
    }
  }
};

// filtering form fields by data type
var field_filter = function(e) {
  var value = e.val(),
      form = e.closest('form'),
      filtered_fields = form.find('*[data-filter="' + e.attr('name') + '"][data-filter-value]');

  filtered_fields.each(function(index,el) {
    el = $(el);

    if (new RegExp("(\\s|^)" + value + "(\\s|$)").test(el.data('filter-value'))) {
      el.find(':input').removeAttr('disabled');
      el.show();
    } else {
      el.find(':input').attr('disabled','disabled');
      el.hide();
    }
  });
};

// load an anchor's URL in a bootbox modal
var use_modal = function(e) {
    var self = $(this);
    
    $.blockUI({ css: { 
        border: 'none', 
        padding: '15px', 
        backgroundColor: '#000', 
        '-webkit-border-radius': '10px', 
        '-moz-border-radius': '10px', 
        opacity: .5, 
        color: '#fff' 
    } }); 
 

    switch(self[0].nodeName.toLowerCase()) {
        case 'a':
          $.get(self.attr('href'),function(data) {
            setup_modal(self,data);
          });
        break;
    }

    e.preventDefault();
};
  
// load an anchor's URL in a bootbox modal
var select_users = function(id, el) {
  if ($(".user_list").val() != '') {
    var selected_users = $(".user_list").val();
    var selected_users_array = selected_users.split(',');
  } else {
    var selected_users_array = new Array();
  }

  $(el).parents('tr').eq(0).toggleClass('active');

  if ($(el).parents('tr').eq(0).hasClass('active')) {
    selected_users_array.push(id);
  } else {
    var index = selected_users_array.indexOf(id);
    selected_users_array.splice(index, 1);
  }
    
  $(".user_list").val(selected_users_array.join());
  console.log(selected_users_array);
  return false;
};

var select_orgs_sectors = function (id, el) {
  var $for = $(el).data('for');
  var list = $for === 'organisation' ? '.organisation_list' : '.sector_list';
  var selected_items = $(list).val();
  var selected_users_array = selected_items != '' ? selected_items.split(',') : [];
  
  $(el).parents('tr').eq(0).toggleClass('active');
    
  if ($(el).parents('tr').eq(0).hasClass('active')) {
    $(el).find('span').eq(0).removeClass('hidden');
    selected_users_array.push(id);
  } else {
    $(el).find('span').eq(0).addClass('hidden');
    var index = selected_users_array.indexOf(id);
    selected_users_array.splice(index, 1);
  }
  
  $(list).val(selected_users_array.join());
  return false;
};

var setup_modal = function(el,data) {
    var dom = $(data),
        modal = {
            show: false,
            title: dom.filter('h1').html(),
            message: dom.filter('.modal')
        },
        form = modal.message.find('form'),
        btns = 0;

    // redefine any form buttons as bootbox button objects
    if (form) {
        btns = form.find(' > input[type="submit"][class*="btn-"]');

        // solo submit button
        if (btns.length === 1 && btns.find('.btn-submit')) {
            // add a cancel button as well
            modal.buttons = {
                danger: {
                    label: 'Cancel',
                    className: 'btn-danger'
                },
                success: {
                    label: btns.first().val(),
                    className: 'btn-success',
                    callback: function() {
                      var error = 0;
                      if($(this).find('.modal-footer .btn-success').html() == 'Add'){
                        if($(this).find('.bootbox-body .field-filter input[name="method"]:checked').val() == 'new'){
                          $(this).find('.bootbox-body .form-new-course').each(function(){
                            if($(this).val() == null){
                              error++;
                            }
                          });
                        }else{
                          if($(this).find('.bootbox-body input[name="title"]').val() == null){
                            error++;
                          }
                        }
                        console.log($.trim($(this).find('.bootbox-body #source').val()) == '');
                        if((error <= 1 && $(this).find('.bootbox-body .field-filter input[name="method"]:checked').val() == 'new') ||
                          (error < 1 && $(this).find('.bootbox-body .field-filter input[name="method"]:checked').val() == 'duplicate') && $.trim($(this).find('.bootbox-body #source').val()) != ''){
                          $(this).find('.bootbox-body > form').submit();
                        }else{
                          if($(this).find('.bootbox-body .field-filter input[name="method"]:checked').val() == 'duplicate'){
                            show_error_notify('Please complete Course Title and select one course to duplicate');
                          }else{
                            show_error_notify('Please complete Course Title and either Categories or Organisations');
                          }
                        }
                      }else if($(this).find('.modal-footer .btn-success').html() == 'Add Lesson'){
                        if($.trim($(this).find('.bootbox-body #title').val()) != ''){
                          $(this).find('.bootbox-body > form').submit();
                        }else{
                          show_error_notify('Please complete Lesson Title');
                        }
                      }
                        return false;
                    },
                }
            };

            btns.remove();
        } else {
            modal.buttons = {};
        }
    }

    if (modal.message) {
        modal.message = modal.message.html();
    }

    bootbox.dialog(modal).on('show.bs.modal',function(e) {
        $(".assign-users__filter--branch").prop("disabled", true);
        $.unblockUI();
        // $.getScript( '/js/bootstrap-toolkit.min.js', function() {});
        // $.getScript( '/js/select2/select2.min.js', function() {});
        // $.getScript( '/js/interactions/app.9adb9510.js', function() {});
        // $.getScript( '/js/interactions/mockup-interactions.7e12aa83.js', function() {});
        // $.getScript( '/js/interactions/vendor.fa77d764.js', function() {});
        

        $(".assign-users__list").hide();
        var user_response;
        var bootbox = $(this);
        form = $(this).find('.bootbox-body > form');
        $('select').select2();

        $('#field-organisation').change(function() {
          $(".assign-users__list").hide();
          $('.assign-users__table tbody').html('');
          $(".assign-users__filter--branch").html('<option value="">All branches</option>');
          jQuery.ajax({
            url: '/organisation/'+$('#field-organisation').val()+'/user',
            type: 'GET',
            dataType: 'json',
            complete: function(xhr, textStatus) {
              //called when complete
            },
            success: function(data, textStatus, xhr) {
              $('select').select2();
              var i = 0;
              user_response = data;
              var selected_users = $(".user_list").val();
              for(i=0; i<data.length; i++) {
                var selected = '0';
                if( selected_users.match(new RegExp("(?:^|,)"+data[i].id+"(?:,|$)"))) {
                    selected = '1';
                }
                if(data[i].branch == 1) {
                  $('.assign-users__table tbody').append('<tr'+(selected == 1 ? ' class="active"' : '')+'><td><a href="#" onclick="select_users(\''+data[i].id+'\', this)" class="assign-users__list-item"><span class="assign-users__list-item__icon fa fa-close icon icon-x text-danger" aria-hidden="true" title="Unassign user"></span> '+data[i].branch_name+'</a></td><td>'+data[i].parent_branch+'</td></tr>');
                  $('.assign-users__filter--branch').append('<option value="'+data[i].id+'">'+data[i].branch_name+'</option>');
                } else {
                  $('.assign-users__table tbody').append('<tr'+(selected == 1 ? ' class="active"' : '')+'><td><a href="#" onclick="select_users(\''+data[i].id+'\', this)" class="assign-users__list-item"><span class="assign-users__list-item__icon fa fa-close icon icon-x text-danger" aria-hidden="true" title="Unassign user"></span> '+data[i].last_name+', '+data[i].first_name+'</a></td><td>'+data[i].parent_branch+'</td></tr>');
                }
              }
              $(".assign-users__filter--branch").prop("disabled", false);
              $(".assign-users__list").show();
              // $('.data-table').dataTable(
              // {
              //     responsive: true
              // });
              // $.getScript( '/js/learning.js', function() {});
            },
            error: function(xhr, textStatus, errorThrown) {
              //called when there is an error
            }
          }); 
        });

        $('#field-branch').change(function() {
          $(".assign-users__list").hide();
          $('.assign-users__table tbody').html('');
          var selected_users = $(".user_list").val();
          for(i=0; i<user_response.length; i++) {
            var selected = '0';
            if( selected_users.match(new RegExp("(?:^|,)"+user_response[i].id+"(?:,|$)"))) {
                selected = '1';
            }
            if(user_response[i].branch_id == $('#field-branch').val() || (user_response[i].id == $('#field-branch').val() && user_response[i].branch == 1) || $('#field-branch').val() == '')
              if(user_response[i].branch == 1) {
                $('.assign-users__table tbody').append('<tr'+(selected == 1 ? ' class="active"' : '')+'><td><a href="#"onclick="select_users(\''+user_response[i].id+'\', this)" class="assign-users__list-item"><span class="assign-users__list-item__icon fa fa-close icon icon-x text-danger" aria-hidden="true" title="Unassign user"></span>'+user_response[i].branch_name+'</a></td><td>'+user_response[i].parent_branch+'</td></tr>');
              } else {
                $('.assign-users__table tbody').append('<tr'+(selected == 1 ? ' class="active"' : '')+'><td><a href="#"onclick="select_users(\''+user_response[i].id+'\', this)" class="assign-users__list-item"><span class="assign-users__list-item__icon fa fa-close icon icon-x text-danger" aria-hidden="true" title="Unassign user"></span>'+user_response[i].last_name+', '+user_response[i].first_name+'</a></td><td>'+user_response[i].parent_branch+'</td></tr>');
              }
          }
          $(".assign-users__list").show();
        });


        // add a form validation/submit handler if found
        if (form) {
            form.validate({
                errorPlacement: function(error, el) {

                },

                submitHandler: function(form) {
                    var self = $(form);

                    $.ajax({
                        url: self.attr('action'),
                        type: self.attr('method'),
                        data: self.serializeArray(),
                        success: function(data, type, xhr) {
                          console.log(data);

                          var message = xhr.responseJSON.invalid ? xhr.responseJSON.invalid : data.message;
                          if (data.response === 'success') {
                            show_success_notify(message);
                          } else {
                            show_error_notify(message);
                          }
                          
                          bootbox.modal('hide');
                          location.reload();
                        },
                        error: function(xhr,type,exception) {
                          console.log(xhr);
                          self.validate().showErrors(xhr.responseJSON.invalid);
                        }
                    });
                },

                highlight: function(el) {
                    $(el).parents('.form-group').eq(0).addClass('has-error');
                },

                unhighlight: function(el) {
                    $(el).parents('.form-group').eq(0).removeClass('has-error');
                }
            });

            // field filter
            field_filter_init(
                form.find('> .field-filter')
            );
        }
        if($('.data-table').length > 0){
          var DataTable = $.fn.dataTable;

          $.extend( DataTable.ext.classes, {
              sWrapper: 'dataTables_wrapper dt-bootstrap4',
              sFilterInput: 'form-control form-control-sm',
              sLengthSelect: 'selectize',
              sProcessing: 'dataTables_processing card',
              sPageButton: 'paginate_button page-item'
          });
          $('.data-table').on('init.dt', function() {
              $('.selectize').selectric({
                  maxHeight: 196,
                  arrowButtonMarkup: '<b class="button"></b>'
              });
          }).DataTable({
              "order": [[ 0, "asc" ]],
              dom:
              "<'row align-items-center'<'col-12 mb-2'f><'col-12'l>>" +
              "<'row'<'col-sm-12 table-responsive-md'tr>>" +
              "<'row'<'col-sm-12 col-md-7'p><'col-sm-12 col-md-5'i>>",
              renderer: 'bootstrap',
              searching: true,
              oLanguage: {
                  sSearch: '',
                  sSearchPlaceholder: 'Search',
                  oPaginate: {
                      sNext: '<i class="icon icon-chevron-right"></i>',
                      sPrevious: '<i class="icon icon-chevron-left"></i>'
                  }
              },
          });
        }
    }).modal('show');
  function show_error_notify(message){
    $.notify({
        message: message
    }, {
        delay: 2000,
        placement: {
            from: 'bottom',
            align : 'right'
        },
        type: 'danger'
    })
  }

  function show_success_notify(message) {
    $.notify({
      message: message
    },{
        delay: 2000,
        placement: {
            from: 'bottom',
            align : 'right'
        },
        type: 'success'
    })
  }

    $(document).on('change', 'select[name="organisation[]"]', function() {
      let val = $(this).val();
      if (val.length) {
        $('input[name="owner"]').prop('disabled', false).val(true);
      } else {
        $('input[name="owner"]').prop('checked', false).prop('disabled', 'disabled');
      }
    });
};