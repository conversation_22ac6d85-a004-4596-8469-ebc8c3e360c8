function getFloatValue(value) {
  let floatValue = parseFloat(value);
  floatValue = !isNaN(floatValue) ? floatValue : 0;
  return floatValue;
}

// Debounce function to limit the rate at which a function can fire
function debounce(func, wait) {
  let timeout;
  return function (...args) {
    const context = this;
    clearTimeout(timeout);
    timeout = setTimeout(() => func.apply(context, args), wait);
  };
}

// Function to update the numberField and rangeSlider
function updateLineValue(value) {
  $("#dataPredictionModal #numberField").val(value + "%");
  $("#dataPredictionModal #rangeSlider").val(value);

  const slider = document.getElementById("rangeSlider");
  slider.dispatchEvent(new Event("input"));
}

// Debounced version of updateLineValue
const debouncedUpdateLineValue = debounce(updateLineValue, 100);

document.addEventListener("DOMContentLoaded", function () {
  const viewLinks = document.querySelectorAll('a[href="05_view-report.php"]');

  viewLinks.forEach((link) => {
    link.addEventListener("click", function (event) {
      event.preventDefault();

      const tableRow = this.closest("tr");
      tableRow.style.opacity = "0.5";

      window.open(this.href, "_blank");
    });
  });

  // Customize Line Size Modal
  const slider = document.getElementById("rangeSlider");
  const tooltip = document.getElementById("tooltip");
  const progressBar = document.getElementById("progressBar");

  if (slider) {
    // Update tooltip and progress bar dynamically
    slider.addEventListener(
      "input",
      () => {
        console.log("slider input");
        const value = slider.value;
        tooltip.textContent = `${value}%`;

        // Update tooltip position
        const sliderWidth = slider.offsetWidth;
        const thumbOffset =
          ((slider.value - slider.min) / (slider.max - slider.min)) *
          sliderWidth;
        tooltip.style.left = `${thumbOffset}px`;

        // Update progress bar width
        progressBar.style.width = `${
          ((slider.value - slider.min) / (slider.max - slider.min)) * 100
        }%`;
      },
      { once: true }
    );
  }

  // Portfolio Impact Modal
  const rangeInput = document.getElementById("modal_chart_01_valueSlider");
  const rangeValue = document.getElementById("rangeValue");

  function updateLabelPosition() {
    const value = rangeInput.value;
    const min = rangeInput.min ? rangeInput.min : 0;
    const max = rangeInput.max ? rangeInput.max : 100;
    const newValue = ((value - min) / (max - min)) * 100; // Calculate percentage

    // Update label position
    rangeValue.style.left = `calc(${newValue}% + (${8 - newValue * 0.15}px))`;
    rangeValue.textContent = value + "%";
  }

  rangeInput.addEventListener("input", updateLabelPosition);
  updateLabelPosition(); // Initialize position on load

  // Trigger initial update
  slider.dispatchEvent(new Event("input"));

  // Benchmarking Table Sortable
  let sortOrder = {};

  $(".risk-league-table-sortable").each(function () {
    const $table = $(this);

    $table.on("click", "th", function () {
      var sortKey = $(this).data("sort");
      sortOrder[sortKey] = !sortOrder[sortKey];

      $table.find("th").removeClass("ascending descending");
      $(this).addClass(sortOrder[sortKey] ? "ascending" : "descending");

      var rows = $table.find("tbody tr").get();
      rows.sort(function (a, b) {
        var A = $(a).data(sortKey);
        var B = $(b).data(sortKey);

        if (
          sortKey === "risk-score" ||
          sortKey === "c_e" ||
          sortKey === "oh" ||
          sortKey === "ssow" ||
          sortKey === "fd_p" ||
          sortKey === "security" ||
          sortKey === "utilities" ||
          sortKey === "sp" ||
          sortKey === "fr"
        ) {
          A = parseFloat(A);
          B = parseFloat(B);
        } else if (sortKey === "last-updated" || sortKey === "renewal-date") {
          const parseDate = (dateStr) => {
            const parts = dateStr.split("/");
            return new Date(`20${parts[2]}`, parts[1] - 1, parts[0]); // month is 0-indexed
          };
          A = parseDate(A);
          B = parseDate(B);
        } else if (sortKey === "line-size") {
          A = parseFloat(A) / 100;
          B = parseFloat(B) / 100;

          // Handle non-numeric values
          if (isNaN(A)) A = 0; // Treat A as 0 if non-numeric
          if (isNaN(B)) B = 0; // Treat B as 0 if non-numeric

          // Sorting logic
          if (A < B) return sortOrder[sortKey] ? -1 : 1; // Ascending/descending order
          if (A > B) return sortOrder[sortKey] ? 1 : -1;
          return 0;
        } else {
          A = A.toLowerCase();
          B = B.toLowerCase();
        }

        if (A < B) return sortOrder[sortKey] ? -1 : 1;
        if (A > B) return sortOrder[sortKey] ? 1 : -1;
        return 0;
      });
      $.each(rows, function (index, row) {
        $table.find("tbody").append(row);
      });
    });
  });

  // Click event for line size
  $(".line-size").on("click", function () {
    let lineSizeValue = getFloatValue($(this).text());
    debouncedUpdateLineValue(lineSizeValue);

    $(".line-size").removeClass("preview");
    $(this).addClass("preview");

    // Get the current position
    var currentPosition = $(this).closest("tr").find(".col-position").text();
    $('input[name="positionRadio"][value="' + currentPosition + '"]').prop(
      "checked",
      true
    );
  });

  // Change event for numberField
  $("#dataPredictionModal #numberField").on("input", function () {
    let value = getFloatValue($(this).val());
    debouncedUpdateLineValue(value);
  });

  // Change event for rangeSlider
  $("#dataPredictionModal #rangeSlider").on("input", function () {
    let value = getFloatValue($(this).val());
    debouncedUpdateLineValue(value);
  });

  // Click event for Preview Changes button
  $("#previewChangesButton").on("click", function () {
    var newLineSize = $("#dataPredictionModal").find("#rangeSlider").val();
    $(".line-size.preview").text(newLineSize + "%");

    // Get the selected radio button value
    var selectedPosition = $('input[name="positionRadio"]:checked').val();
    $(".line-size.preview")
      .closest("tr")
      .find(".col-position")
      .text(selectedPosition);
    //updateCurrentScoreChartForcast(100);
  });

  // Function to update the number field
  function updateNumberField(increment) {
    let currentValue = parseInt(numberField.value) || 0;
    currentValue += increment;
    numberField.value = currentValue + "%";
    rangeSlider.value = currentValue;

    const slider = document.getElementById("rangeSlider");
    slider.dispatchEvent(new Event("input"));
  }

  // Event listeners for the number field button
  numberFieldAdd.addEventListener("click", function () {
    updateNumberField(1);
  });

  numberFieldSubtract.addEventListener("click", function () {
    updateNumberField(-1);
  });

  var newDataButton = document.getElementById("newDataButton");
  if (newDataButton) {
    newDataButton.addEventListener("click", function (e) {
      e.preventDefault();
      fetchNewData();
    });
  }

  // ANNAH: UPLOAD FILES FOR RAFA
  // var fileUpload = document.getElementById("fileUpload");
  // if (fileUpload) {
  //   fileUpload.addEventListener("change", function (e) {
  //     console.log("fileUpload");
  //     if (this.files.length > 0) {
  //       // fetchNewData();
  //       startUploadAnimation();
  //     }
  //   });
  // }
});

// ANNAH: START UPLOAD ANIMATION
function startUploadAnimation() {
  $(".risk-league-uploadedFiles").show();
  $(".upload-progress-bar").each(function (index) {
    var $uploadContainer = $(this);
    var $uploadContainerParent = $(this).parents(".risk-league-upload");
    var $progressBar = $uploadContainer.find(".progress-bar");

    $progressBar.css("width", "0");
    $uploadContainerParent.removeClass("upload-complete");

    setTimeout(function () {
      $progressBar.css("width", "100%");
    }, 100 + index * 1000);

    setTimeout(function () {
      $uploadContainerParent.addClass("upload-complete");
    }, 1500 + index * 1000);
  });

  $(".risk-league-review-btn").attr("disabled", false);
}
// $('#startUploadButton').on('click', startUploadAnimation);

// Function to set a cookie
function setCookie(name, value, days) {
  let expires = "";
  if (days) {
    const date = new Date();
    date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000); // Convert days to milliseconds
    expires = "; expires=" + date.toUTCString();
  }
  document.cookie =
    name + "=" + (value || "") + expires + "; path=/; SameSite=Lax";
  console.log("Cookie set: " + name + "=" + value);
}

// Function to get a cookie by name
function getCookie(name) {
  const nameEQ = name + "=";
  const ca = document.cookie.split(";");
  for (let i = 0; i < ca.length; i++) {
    let c = ca[i];
    while (c.charAt(0) === " ") c = c.substring(1, c.length); // Trim leading spaces
    if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length); // Return cookie value
  }
  return null; // Return null if cookie not found
}

function setUpdateScoreCookie() {
  const gradingStatus = document.querySelector(
    ".risk-grading-status-wrapper select"
  ).value;
  const textareaValue = document.querySelector(
    ".risk-league-textarea textarea"
  ).value;

  setCookie("update-score", "84", "");

  let targetField = $("#" + $("#hTargetField").val());

  if (targetField.find(".content-wrapper").length > 0) {
    targetField.find(".content-wrapper").html(textareaValue);
  }

  targetField
    .find(".risk-league-legend")
    .attr("class", "risk-league-legend lg " + gradingStatus);
}

function checkUpdateScoreCookie() {
  const score = getCookie("update-score");
  console.log("Current update-score cookie value: " + score); // Debugging log
}

function startUploadAnimation() {
  $(".risk-league-uploadedFiles").show();
  $(".upload-progress-bar").each(function (index) {
    var $uploadContainer = $(this);
    var $uploadContainerParent = $(this).parents(".risk-league-upload");
    var $progressBar = $uploadContainer.find(".progress-bar");

    $progressBar.css("width", "0");
    $uploadContainerParent.removeClass("upload-complete");

    setTimeout(function () {
      $progressBar.css("width", "100%");
    }, 100 + index * 1000);

    setTimeout(function () {
      $uploadContainerParent.addClass("upload-complete");
    }, 1500 + index * 1000);
  });

  $(".risk-league-review-btn").attr("disabled", false);
}

document.addEventListener("DOMContentLoaded", function () {});

function showOverlay() {
  const overlay = document.createElement("div");
  overlay.id = "loadingOverlay";
  overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    `;

  const spinner = document.createElement("div");
  spinner.className = "spinner-border text-light";
  spinner.setAttribute("role", "status");

  const spinnerText = document.createElement("span");
  spinnerText.className = "sr-only";
  spinnerText.textContent = "Loading...";

  spinner.appendChild(spinnerText);
  overlay.appendChild(spinner);
  document.body.appendChild(overlay);
}

function hideOverlay() {
  const overlay = document.getElementById("loadingOverlay");
  if (overlay) {
    overlay.style.opacity = "0";
    overlay.style.transition = "opacity 0.5s ease-out";
    setTimeout(() => {
      overlay.remove();
    }, 500);
  }
}

function fetchNewData() {
  showOverlay();

  setTimeout(() => {
    const after_data_url = "/risk-insights/review-risk-report";
    $.ajax({
      type: "GET",
      url: after_data_url,
      success: function (response) {
        console.log("response", response);
        const new_chart_color = getColorHexByScore(response.portfolio_score);
        console.log("new_chart_color", new_chart_color);
        response.chart_color = new_chart_color;
        console.log("response", response);
        updateCurrentScoreChart(response);
        updateRiskGradingSection(response.risk_grading);
        updateLossEstimatesChart(response);
        updateBenchmarkingTable(response.benchmarking);
      },
      error: function (error) {
        console.log("error", error);
      },
      finally: function () {
        hideOverlay();
      },
    });
  }, 1000);
}

function updateRiskGradingSection(riskGradingData) {
  const riskGradingSection = document.getElementById("risk-grading-section");
  if (riskGradingSection && Array.isArray(riskGradingData)) {
    let newHtml = "";
    riskGradingData.forEach((grade, index) => {
      const borderClass =
        index < riskGradingData.length - 1 ? "border-bottom" : "";
      const colorClass = getColorClass(grade.score);

      const oldScoreElement = Array.from(
        riskGradingSection.querySelectorAll('a[href="03_dashboard.php"]')
      ).find((el) => el.textContent === grade.category);
      const oldScore = oldScoreElement
        ? oldScoreElement.nextElementSibling.textContent
        : null;

      const updatedClass =
        oldScore && oldScore !== grade.score.toString()
          ? "risk-league-updated"
          : "";

      newHtml += `
                <div class="${borderClass} py-1 px-1 d-flex align-items-center ${updatedClass}">
                    <div class="risk-league-grading ${grade.status} mr-2"></div>
                    <a href="03_dashboard.php">${grade.category}</a>
                    <div class="risk-league-rating ${colorClass} ml-auto">${grade.score}</div>
                </div>
            `;
    });
    riskGradingSection.innerHTML = newHtml;
  } else {
    riskGradingSection.innerHTML = "<p>No risk grading data available.</p>";
  }
}

function updateCurrentScoreChart(newData) {
  var chartElement = document.getElementById("current_score_chart");
  var portfolioScoreElement = document.querySelector(
    ".risk-league-portfolio-score .risk-league-rating"
  );

  console.log("newData", newData);
  if (chartElement && portfolioScoreElement) {
    // Update chart data and color
    chartElement.setAttribute(
      "data-chart-data",
      JSON.stringify(newData.chart_data)
    );
    chartElement.setAttribute("data-chart-color", newData.chart_color);

    // Update portfolio score
    portfolioScoreElement.textContent = newData.portfolio_score;
    portfolioScoreElement.className =
      "risk-league-rating " +
      getColorClass(newData.portfolio_score) +
      " lg ml-2";
    portfolioScoreElement.setAttribute("data-color", newData.chart_color);

    // Refresh the chart
    refreshCurrentScoreChart();
  }
}

function refreshCurrentScoreChart() {
  am5.ready(function () {
    var root = am5.registry.rootElements[0];
    if (root) {
      var chart = root.container.children.values[0];
      var series = chart.series.values[0];
      var xAxis = chart.xAxes.values[0];

      var chartElement = document.getElementById("current_score_chart");
      console.log("chartElement", chartElement);
      var newData = JSON.parse(chartElement.getAttribute("data-chart-data"));
      var newColor = parseInt(
        chartElement.getAttribute("data-chart-color").replace("0x", ""),
        16
      );

      // Update data
      xAxis.data.setAll(newData);
      series.data.setAll(newData);

      // Update color
      series.set("stroke", am5.color(newColor));
      series.fills.template.set("fill", am5.color(newColor));

      // Animate changes
      series.appear(1000, 100);
    }
  });
}

function updateLossEstimatesChart(newData) {
  var chartElement = document.getElementById("loss_estimates_chart");

  if (chartElement) {
    // Update chart data
    chartElement.setAttribute(
      "data-chart-data",
      JSON.stringify(newData.loss_estimates)
    );

    // Refresh the chart
    refreshLossEstimatesChart();
  }
}

function refreshLossEstimatesChart() {
  am5.ready(function () {
    var root = am5.registry.rootElements[1]; // Assuming this is the second chart on the page
    if (root) {
      var chart = root.container.children.values[0];
      var series = chart.series.values[0];
      var yAxis = chart.yAxes.values[0];

      var chartElement = document.getElementById("loss_estimates_chart");
      var newData = JSON.parse(chartElement.getAttribute("data-chart-data"));

      // Update data
      yAxis.data.setAll(newData);
      series.data.setAll(newData);

      // Animate changes
      series.appear(1000, 100);
    }
  });
}

function updateBenchmarkingTable(newBenchmarkingData) {
  const tableBody = document.querySelector(".table tbody");
  if (!tableBody) return;

  const oldRows = Array.from(tableBody.querySelectorAll("tr")).map(
    (row) => row.querySelector("a.fs-15").textContent
  );

  let newHtml = "";
  newBenchmarkingData.forEach((org, index) => {
    const isNew = !oldRows.includes(org.name);
    const updatedClass = isNew ? "risk-league-updated" : "";
    const disabledClass = org.position === "-" ? "disabled" : "";

    newHtml += `
            <tr class="${disabledClass} ${updatedClass}">
                <td>
                    <div class="d-flex align-items-center">
                        <div class="risk-league-company-logo mr-2" style="background-image: url('/img/risk-league/${
                          org.slug
                        }.png');"></div>
                        <a href="02_dashboard.php" class="fs-15">${org.name}</a>
                    </div>
                </td>
                <td>${org.sector}</td>
                <td>${org.sub_sector}</td>
                <td>${org.broker}</td>
                <td>${org.position}</td>
                <td>${org.line_size}</td>
                <td>${org.surveys}</td>
                <td>${org.last_updated}</td>
                <td>${org.policy_segment}</td>
                <td>${org.renewal_date}</td>
                <td>${org.underwriter}</td>
                <td>
                    <div class="d-flex align-items-center">
                        <div id="risk_score_chart_${index}" class="risk-league-risk_score_chart"
                             data-series-color="${getColorHexByScore(
                               org.risk_score.risk_league_rating.value
                             )}"
                             data-chart-data='${JSON.stringify(
                               org.risk_score.chart_data
                             )}'></div>
                        <div class="risk-league-rating lg ${getColorClass(
                          org.risk_score.risk_league_rating.value
                        )} ml-3">
                            ${org.risk_score.risk_league_rating.value}
                        </div>
                    </div>
                </td>
            </tr>
        `;
  });

  tableBody.innerHTML = newHtml;

  // Reinitialize the risk score charts
  newBenchmarkingData.forEach((org, index) => {
    initializeRiskScoreChart(`risk_score_chart_${index}`);
  });
}

function initializeRiskScoreChart(chartId) {
  am5.ready(function () {
    var chartElement = document.getElementById(chartId);
    if (!chartElement) return;

    var root = am5.Root.new(chartId);
    root.setThemes([am5themes_Animated.new(root)]);

    var chart = root.container.children.push(
      am5xy.XYChart.new(root, {
        panX: false,
        panY: false,
        wheelX: "none",
        wheelY: "none",
        layout: root.verticalLayout,
        paddingLeft: 0,
        paddingRight: 0,
        paddingTop: 0,
        paddingBottom: 0,
      })
    );

    // ... (rest of the chart initialization code)
    // Make sure to use the data from the data attributes
    var data = JSON.parse(chartElement.getAttribute("data-chart-data"));
    var seriesColor = parseInt(
      chartElement.getAttribute("data-series-color").replace("0x", ""),
      16
    );

    // ... (set up axes, series, etc. using the data and color)

    series.data.setAll(data);
    xAxis.data.setAll(data);

    series.appear(1000);
    chart.appear(1000, 100);
  });
}

function getColorHexByScore(score) {
  if (score <= 20) return "0xDC6788"; // red
  if (score <= 40) return "0xFF9D43"; // orange
  if (score <= 60) return "0xFDCA41"; // yellow
  if (score <= 80) return "0x49C993"; // green
  return "0x49B2C9"; // blue
}

function getColorClass(score) {
  if (score <= 20) return "red";
  if (score <= 40) return "orange";
  if (score <= 60) return "yellow";
  if (score <= 80) return "green";
  return "blue";
}

// Function to update the portfolio score
function updatePortfolioScore() {
  const score = getCookie("update-score");
  if (score) {
    const portfolioScoreElement = document.querySelector(
      "#newSubmission03 .risk-league-portfolio-score .risk-league-rating"
    );

    if (portfolioScoreElement) {
      portfolioScoreElement.textContent = score; // Update the displayed score
      portfolioScoreElement.classList.add("animate__heartBeat"); // Add animation class
      console.log("Animation class added to portfolio score element.");
    } else {
      console.error("Portfolio score element not found.");
    }

    // remove cookie
    document.cookie = "update-score=; Max-Age=-99999999; path=/; SameSite=Lax"; // Set cookie with a past expiration date
  }
}

// Event listener for visibility change
document.addEventListener("visibilitychange", function () {
  if (document.visibilityState === "visible") {
    updatePortfolioScore(); // Update score when the tab is active
  }
});

// Initial call to update the score when the page loads
window.onload = updatePortfolioScore;

function addForecastModeClass() {
  document.body.classList.add("forecast-mode");
  return false;
}

function removeForecastModeClass() {
  document.body.classList.remove("forecast-mode");
  return false;
}

function loadPortfolioImpactChart() {
  am5.ready(function() {
    loadPortfolioImpactChart01();
    loadPortfolioImpactChart02();
    loadPortfolioImpactChart03();
  });
}

function loadPortfolioImpactChart01() {
  var portfolio_impact_chart_01 = document.getElementById("portfolio_impact_chart_01");
  if (!portfolio_impact_chart_01) return;

  // Create root element
  var portfolio_impact_chart_01_root = am5.Root.new("portfolio_impact_chart_01");
  portfolio_impact_chart_01_root.setThemes([am5themes_Animated.new(portfolio_impact_chart_01_root)]);

  // Create chart
  var portfolio_impact_chart_01_chart = portfolio_impact_chart_01_root.container.children.push(am5xy.XYChart.new(portfolio_impact_chart_01_root, {
    panX: false,
    panY: false,
    wheelX: "none",
    wheelY: "none",
    pinchZoomX: false,
    paddingLeft: 0
  }));
  
  // Create axes
  var portfolio_impact_chart_01_xAxis = portfolio_impact_chart_01_chart.xAxes.push(am5xy.DateAxis.new(portfolio_impact_chart_01_root, {
    baseInterval: { timeUnit: "day", count: 1 },
    renderer: am5xy.AxisRendererX.new(portfolio_impact_chart_01_root, {
      minorGridEnabled: true,
      minGridDistance: 80
    })
  }));

  portfolio_impact_chart_01_xAxis.get("renderer").labels.template.setAll({
    visible: false
  });
  portfolio_impact_chart_01_xAxis.get("renderer").grid.template.setAll({
    visible: false
  });

  var portfolio_impact_chart_01_yAxis = portfolio_impact_chart_01_chart.yAxes.push(am5xy.ValueAxis.new(portfolio_impact_chart_01_root, {
    renderer: am5xy.AxisRendererY.new(portfolio_impact_chart_01_root, {})
  }));

  portfolio_impact_chart_01_yAxis.get("renderer").labels.template.setAll({
    visible: false
  });
  portfolio_impact_chart_01_yAxis.get("renderer").grid.template.setAll({
    visible: false
  });

  // Add series
  var portfolio_impact_chart_01_series = portfolio_impact_chart_01_chart.series.push(am5xy.LineSeries.new(portfolio_impact_chart_01_root, {
    name: "Series",
    xAxis: portfolio_impact_chart_01_xAxis,
    yAxis: portfolio_impact_chart_01_yAxis,
    valueYField: "value",
    valueXField: "date",
    tooltip: am5.Tooltip.new(portfolio_impact_chart_01_root, { labelText: "{valueY}" })
  }));

  portfolio_impact_chart_01_series.fills.template.setAll({
    visible: true,
    fillOpacity: 0.2, 
    fillGradient: am5.LinearGradient.new(portfolio_impact_chart_01_root, {
      stops: [{
        color: am5.color(0x49c993),
        opacity: 0.3
      }, {
        color: am5.color(0x49c993),
        opacity: 0
      }],
      rotation: 90
    })
  });

  portfolio_impact_chart_01_series.strokes.template.setAll({
    strokeGradient: am5.LinearGradient.new(portfolio_impact_chart_01_root, {
      stops: [{
        color: am5.color(0x49C993), 
        opacity: 1
      }, {
        color: am5.color(0x49C993),
        opacity: 1
      }],
      rotation: 0
    }),
    strokeWidth: 2
  });

  // Hardcoded data
  var portfolio_impact_chart_01_data = [
    { date: new Date(2024, 8, 21).getTime(), value: 100 },
    { date: new Date(2024, 8, 22).getTime(), value: 105 },
    { date: new Date(2024, 8, 23).getTime(), value: 108 },
    { date: new Date(2024, 8, 24).getTime(), value: 115 },
    { date: new Date(2024, 8, 25).getTime(), value: 110 },
    { date: new Date(2024, 8, 26).getTime(), value: 108 },
    { date: new Date(2024, 8, 27).getTime(), value: 108 },
  ];

  // Set data
  portfolio_impact_chart_01_series.data.setAll(portfolio_impact_chart_01_data);

  // Slider event listener
  var portfolio_impact_slider = document.getElementById("portfolio_impact_slider");
  var portfolio_impact_chart_01_valueSlider_timeout;
  var initialValue = portfolio_impact_chart_01_data[portfolio_impact_chart_01_data.length - 1].value;

  portfolio_impact_slider.addEventListener("input", function() {
    clearTimeout(portfolio_impact_chart_01_valueSlider_timeout); 
    portfolio_impact_chart_01_valueSlider_timeout = setTimeout(function() { 
      var percentage = parseInt(portfolio_impact_slider.value);
      var lastDataPoint = portfolio_impact_chart_01_data[portfolio_impact_chart_01_data.length - 1];
      
      lastDataPoint.value = (percentage > 0) ? 108 + ((140 - 108) * (percentage / 100)) : 108;
      
      portfolio_impact_chart_01_series.data.setAll(portfolio_impact_chart_01_data);
    }, 100); 
  });

  var portfolio_impact_chart_01_rangeDate = new Date(2024, 8, 27);
  var portfolio_impact_chart_01_rangeTime = (portfolio_impact_chart_01_data[5].date+portfolio_impact_chart_01_data[6].date)/2;
  
  // Add series range
  var portfolio_impact_chart_01_seriesRangeDataItem = portfolio_impact_chart_01_xAxis.makeDataItem({});
  var portfolio_impact_chart_01_seriesRange = portfolio_impact_chart_01_series.createAxisRange(portfolio_impact_chart_01_seriesRangeDataItem);
  portfolio_impact_chart_01_seriesRange.fills.template.setAll({
    visible: true,
    opacity: 0.3
  });

  portfolio_impact_chart_01_seriesRange.fills.template.set("fillPattern", am5.LinePattern.new(portfolio_impact_chart_01_root, {
    color: am5.color(0x49C993),
    rotation: -45,
    strokeWidth: 2,
    width: 2000,
    height: 2000,
    fill: am5.color(0xffffff)
  }));

  portfolio_impact_chart_01_seriesRange.strokes.template.set("stroke", am5.color(0x49C993));

  portfolio_impact_chart_01_xAxis.onPrivate("max", function (value) {
    portfolio_impact_chart_01_seriesRangeDataItem.set("endValue", value);
    portfolio_impact_chart_01_seriesRangeDataItem.set("value", portfolio_impact_chart_01_rangeTime);
  });

  // Add axis range
  var portfolio_impact_chart_01_range = portfolio_impact_chart_01_xAxis.createAxisRange(portfolio_impact_chart_01_xAxis.makeDataItem({}));
  var portfolio_impact_chart_01_color = portfolio_impact_chart_01_root.interfaceColors.get("primaryButton");

  portfolio_impact_chart_01_range.set("value", portfolio_impact_chart_01_rangeDate.getTime());
  portfolio_impact_chart_01_range.get("grid").setAll({
    strokeOpacity: 1,
    stroke: portfolio_impact_chart_01_color
  });

  // Make stuff animate on load
  portfolio_impact_chart_01_series.appear(1000);
  portfolio_impact_chart_01_chart.appear(1000, 100);
}

function loadPortfolioImpactChart02() {
  var portfolio_impact_chart_02_element = document.getElementById("portfolio_impact_chart_02");
  if (!portfolio_impact_chart_02_element) return;

  var portfolio_impact_chart_02_root = am5.Root.new("portfolio_impact_chart_02");
  portfolio_impact_chart_02_root.setThemes([am5themes_Animated.new(portfolio_impact_chart_02_root)]);

  var portfolio_impact_chart_02_chart = portfolio_impact_chart_02_root.container.children.push(am5xy.XYChart.new(portfolio_impact_chart_02_root, {
    panX: false,
    panY: false,
    wheelX: "none",
    wheelY: "none",
    paddingLeft: 0
  }));

  portfolio_impact_chart_02_chart.zoomOutButton.set("forceHidden", true);
  portfolio_impact_chart_02_chart.set("colors", am5.ColorSet.new(portfolio_impact_chart_02_root, {
    colors: [
      am5.color(0xF57B03),
      am5.color(0x0CBFD6),
      am5.color(0x9575CD),
    ]
  }));

  var portfolio_impact_chart_02_yRenderer = am5xy.AxisRendererY.new(portfolio_impact_chart_02_root, {
    minGridDistance: 30,
    minorGridEnabled: true,
  });

  portfolio_impact_chart_02_yRenderer.grid.template.set("location", 1);

  var portfolio_impact_chart_02_yAxis = portfolio_impact_chart_02_chart.yAxes.push(am5xy.CategoryAxis.new(portfolio_impact_chart_02_root, {
    maxDeviation: 0,
    categoryField: "network",
    renderer: portfolio_impact_chart_02_yRenderer,
  }));

  // Updated number formatter for x-axis
  var portfolio_impact_chart_02_xAxis = portfolio_impact_chart_02_chart.xAxes.push(am5xy.ValueAxis.new(portfolio_impact_chart_02_root, {
    maxDeviation: 0,
    min: 0,
    numberFormatter: am5.NumberFormatter.new(portfolio_impact_chart_02_root, {
      "numberFormat": "£#,###.#a"
    }),
    extraMax: 0.1,
    renderer: am5xy.AxisRendererX.new(portfolio_impact_chart_02_root, {
      strokeOpacity: 0.1,
      minGridDistance: 80
    })
  }));

  portfolio_impact_chart_02_xAxis.get("renderer").grid.template.setAll({
    strokeGradient: am5.LinearGradient.new(portfolio_impact_chart_02_root, {
      stops: [{
        color: am5.color(0xE0E0E0), 
        opacity: 0.5
      }, {
        color: am5.color(0xE0E0E0), 
        opacity: 1
      }],
      rotation: 90
    }),
    strokeWidth: 1
  });

  portfolio_impact_chart_02_yAxis.get("renderer").grid.template.setAll({
    visible: false
  });
  portfolio_impact_chart_02_xAxis.get("renderer").grid.template.setAll({
    visible: false
  });

  portfolio_impact_chart_02_xAxis.get("renderer").labels.template.setAll({
    visible: false
  });
  portfolio_impact_chart_02_yAxis.get("renderer").labels.template.setAll({
    visible: false
  });

  var portfolio_impact_chart_02_series = portfolio_impact_chart_02_chart.series.push(am5xy.ColumnSeries.new(portfolio_impact_chart_02_root, {
    name: "Series 1",
    xAxis: portfolio_impact_chart_02_xAxis,
    yAxis: portfolio_impact_chart_02_yAxis,
    valueXField: "value",
    categoryYField: "network",
  }));

  portfolio_impact_chart_02_series.columns.template.setAll({
    maxHeight: 36,
    cornerRadiusBR: 4,
    cornerRadiusTR: 4,
    cornerRadiusBL: 4,
    cornerRadiusTL: 4,
    stroke: am5.color(0x000000),
    strokeWidth: 1,
    fillGradient: am5.LinearGradient.new(portfolio_impact_chart_02_root, {
      stops: [{
        opacity: 1
      }, {
        opacity: 0.5
      }],
      rotation: 0
    }),
    tooltipText: "[#ffffff][bold]£{valueX.formatNumber('#,###.#a')}[/]\n[#ffffff][fontWeight: 500]{extraLabel}[/]",
    tooltipBackground: am5.Rectangle.new(portfolio_impact_chart_02_root, {
      fill: am5.color(0x000000),
      fillOpacity: 0.8
    })
  });

  portfolio_impact_chart_02_series.columns.template.adapters.add("fill", function (fill, target) {
    return portfolio_impact_chart_02_chart.get("colors").getIndex(portfolio_impact_chart_02_series.columns.indexOf(target));
  });

  portfolio_impact_chart_02_series.columns.template.adapters.add("stroke", function (stroke, target) {
    return portfolio_impact_chart_02_chart.get("colors").getIndex(portfolio_impact_chart_02_series.columns.indexOf(target));
  });

  // Updated value labels on the left side of the bars
  portfolio_impact_chart_02_series.bullets.push(function(root, series, dataItem) {
    var label = am5.Label.new(root, {
      text: "£{valueX}\n{extraLabel}",
      fontSize: 12,
      fontWeight: "500",
      fill: am5.color(0xFFFFFF),
      populateText: true,
      centerY: am5.p50,
      centerX: am5.p0,
      paddingLeft: 15
    });

    // Custom number formatting for the label and truncate extraLabel
    label.adapters.add("text", function(text, target) {
      var value = dataItem.get("valueX");
      var extraLabel = dataItem.dataContext.extraLabel;
      var truncatedExtraLabel = extraLabel.split('\n')[0].substring(0, 6); // Get first 6 characters of the first line

      var formattedValue;
      if (value >= 1e9) {
        formattedValue = "£" + (value / 1e9).toFixed(1) + "b";
      } else if (value >= 1e6) {
        formattedValue = "£" + (value / 1e6).toFixed(1) + "m";
      } else if (value >= 1e3) {
        formattedValue = "£" + (value / 1e3).toFixed(1) + "k";
      } else {
        formattedValue = "£" + value.toFixed(1);
      }

      return formattedValue + "\n" + truncatedExtraLabel + "...";
    });

    label.adapters.add("x", function(x, target) {
      return -5;
    });

    return am5.Bullet.new(root, {
      locationX: 0,
      sprite: label
    });
  }); 

  // Set data
  var portfolio_impact_chart_02_data = [
    {
      "network": "NLE\nTarget",
      "value": 78400000,
      "extraLabel": "Tesco Distribution Centre 1"
    },
    {
      "network": "PML\nTarget",
      "value": 219300000,
      "extraLabel": "Tesco Distribution Centre 2"
    },
    {
      "network": "MAS\nTarget",
      "value": 231100000,
      "extraLabel": "Tesco Distribution Centre 1"
    }
  ];

  portfolio_impact_chart_02_yAxis.data.setAll(portfolio_impact_chart_02_data);
  portfolio_impact_chart_02_series.data.setAll(portfolio_impact_chart_02_data);

  portfolio_impact_chart_02_series.appear(1000);
  portfolio_impact_chart_02_chart.appear(1000, 100);
}

function loadPortfolioImpactChart03() {
  var portfolio_impact_chart_03_element = document.getElementById("portfolio_impact_chart_03");
  if (!portfolio_impact_chart_03_element) return;

  var portfolio_impact_chart_03_root = am5.Root.new("portfolio_impact_chart_03");
  portfolio_impact_chart_03_root.setThemes([am5themes_Animated.new(portfolio_impact_chart_03_root)]);

  var portfolio_impact_chart_03_chart = portfolio_impact_chart_03_root.container.children.push(am5xy.XYChart.new(portfolio_impact_chart_03_root, {
    panX: false,
    panY: false,
    wheelX: "none",
    wheelY: "none",
    paddingLeft: 0
  }));

  portfolio_impact_chart_03_chart.zoomOutButton.set("forceHidden", true);

  var colorSet = am5.ColorSet.new(portfolio_impact_chart_03_root, {
    colors: [
      am5.color(0xF57B03),
      am5.color(0x0CBFD6),
      am5.color(0x9575CD),
    ]
  });

  portfolio_impact_chart_03_chart.set("colors", colorSet);

  var portfolio_impact_chart_03_yRenderer = am5xy.AxisRendererY.new(portfolio_impact_chart_03_root, {
    minGridDistance: 30,
    minorGridEnabled: true,
  });

  portfolio_impact_chart_03_yRenderer.grid.template.set("location", 1);

  var portfolio_impact_chart_03_yAxis = portfolio_impact_chart_03_chart.yAxes.push(am5xy.CategoryAxis.new(portfolio_impact_chart_03_root, {
    maxDeviation: 0,
    categoryField: "network",
    renderer: portfolio_impact_chart_03_yRenderer,
  }));

  portfolio_impact_chart_03_yAxis.get("renderer").labels.template.setAll({
    fontSize: 12,
    textAlign: "right"
  });

  portfolio_impact_chart_03_yAxis.get("renderer").grid.template.setAll({
    visible: false
  });

  portfolio_impact_chart_03_yAxis.get("renderer").labels.template.setAll({
    visible: false
  });

  var portfolio_impact_chart_03_xAxis = portfolio_impact_chart_03_chart.xAxes.push(am5xy.ValueAxis.new(portfolio_impact_chart_03_root, {
    maxDeviation: 0,
    min: 0,
    numberFormatter: am5.NumberFormatter.new(portfolio_impact_chart_03_root, {
      "numberFormat": "#,###a"
    }),
    extraMax: 0.1,
    renderer: am5xy.AxisRendererX.new(portfolio_impact_chart_03_root, {
      strokeOpacity: 0.1,
      minGridDistance: 20
    })
  }));

  portfolio_impact_chart_03_xAxis.get("renderer").grid.template.setAll({
    visible: false
  });

  portfolio_impact_chart_03_xAxis.get("renderer").labels.template.setAll({
    visible: false
  });

  var portfolio_impact_chart_03_series = portfolio_impact_chart_03_chart.series.push(am5xy.ColumnSeries.new(portfolio_impact_chart_03_root, {
    name: "Series 1",
    xAxis: portfolio_impact_chart_03_xAxis,
    yAxis: portfolio_impact_chart_03_yAxis,
    valueXField: "value",
    categoryYField: "network",
  }));

  portfolio_impact_chart_03_series.columns.template.setAll({
    maxHeight: 36,
    cornerRadiusBR: 4,
    cornerRadiusTR: 4,
    cornerRadiusBL: 4,
    cornerRadiusTL: 4,
    stroke: am5.color(0x000000),
    strokeWidth: 2,
    fillOpacity: 0.3,
    fillPattern: am5.LinePattern.new(portfolio_impact_chart_03_root, {
      rotation: -45,
      strokeWidth: 8,
      width: 3000,
      height: 3000,
      gap: 1,
      color: am5.color(0xffffff),
    }),
    tooltipText: "[#ffffff][bold]£{valueX.formatNumber('#,###.#a')}[/]\n[#ffffff][fontWeight: 500]{extraLabel}[/]",
    tooltipBackground: am5.Rectangle.new(portfolio_impact_chart_03_root, {
      fill: am5.color(0x000000),
      fillOpacity: 0.8
    })
  });

  portfolio_impact_chart_03_series.columns.template.adapters.add("stroke", function (stroke, target) {
    return colorSet.getIndex(portfolio_impact_chart_03_series.columns.indexOf(target));
  });

  portfolio_impact_chart_03_series.columns.template.adapters.add("fill", function (stroke, target) {
    return colorSet.getIndex(portfolio_impact_chart_03_series.columns.indexOf(target));
  });

  portfolio_impact_chart_03_series.bullets.push(function(root, series, dataItem) {
    var label = am5.Label.new(root, {
      text: "£{valueX}\n{extraLabel}",
      fontSize: 12,
      fontWeight: "500",
      fill: am5.color(0x000000),
      populateText: true,
      centerY: am5.p50,
      centerX: am5.p0,
      paddingLeft: 15
    });

    label.adapters.add("text", function(text, target) {
      var value = dataItem.get("valueX");
      var extraLabel = dataItem.dataContext.extraLabel;
      var truncatedExtraLabel = extraLabel.split('\n')[0].substring(0, 6); // Get first 6 characters of the first line

      var formattedValue;
      if (value >= 1e9) {
        formattedValue = "£" + (value / 1e9).toFixed(1) + "b";
      } else if (value >= 1e6) {
        formattedValue = "£" + (value / 1e6).toFixed(1) + "m";
      } else if (value >= 1e3) {
        formattedValue = "£" + (value / 1e3).toFixed(1) + "k";
      } else {
        formattedValue = "£" + value.toFixed(1);
      }

      return formattedValue + "\n" + truncatedExtraLabel + "...";
    });

    label.adapters.add("x", function(x, target) {
      return -5;
    });

    return am5.Bullet.new(root, {
      locationX: 0,
      sprite: label
    });
  });

  var portfolio_impact_chart_03_data = [
    {
      "network": "NLE\nTarget",
      "value": 78400000,
      "extraLabel": "Tesco Distribution Centre 1"
    },
    {
      "network": "PML\nTarget",
      "value": 219300000,
      "extraLabel": "Tesco Distribution Centre 2"
    },
    {
      "network": "MAS\nTarget",
      "value": 231100000,
      "extraLabel": "Tesco Distribution Centre 1"
    }
  ];

  portfolio_impact_chart_03_yAxis.data.setAll(portfolio_impact_chart_03_data);
  portfolio_impact_chart_03_series.data.setAll(portfolio_impact_chart_03_data);

  portfolio_impact_chart_03_series.appear(1000);
  portfolio_impact_chart_03_chart.appear(1000, 100);
}