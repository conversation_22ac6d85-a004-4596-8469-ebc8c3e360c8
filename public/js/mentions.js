$(document).ready(function () {
    var wysiwyg_ta;
    var pos;

    $('.mention-item').on('click', function (event) {
        event.preventDefault();
        let userEmail = $(this).data('email'),
            userId = $(this).data('id'),
            userName = $(this).data('name'),
            mdBody = `@<${mention.tag} id="${userId}">${userName}</${mention.tag}>${mention.space}`,
            text_cm = wysiwyg_ta.codemirror;
        $mentionModal.modal('hide');
        text_cm.setSelection(pos, {line: pos.line, ch: (pos.ch + 1)});
        text_cm.replaceSelection(mdBody);
        text_cm.setCursor({line: pos.line, ch: (pos.ch + mdBody.length)});
        text_cm.focus();
        return false;
    });

    if ($('.wysiwyg-content').length > 0) {
        $('.wysiwyg-content').each(function () {
            wysiwyg_ta = new SimpleMDE({
                element: $(this)[0],
                toolbar: ["bold", "unordered-list", "link", "|", "preview"]
            });
            wysiwyg_ta.codemirror.on("keypress", function (cm, event) {
                var keyPressed = String.fromCharCode(event.keyCode);
                if (keyPressed === mention.trigger) {
                    pos = wysiwyg_ta.codemirror.getCursor(),
                        lineContent = cm.getLine(pos.line),
                        chBefore = lineContent.charAt(pos.ch - 1),
                        chAfter = lineContent.charAt(pos.ch);
                    if ((lineContent.length === 0)                                                      // beginning of content
                        || (pos.ch === 0 && chAfter === mention.space)                                  // beginning of content but with space after
                        || (mention.space === chBefore && mention.space === chAfter)                    // in between spaces
                        || (pos.ch === lineContent.length && lineContent.slice(-1) === mention.space))  //end of the line with space before
                    {
                        $mentionModal.modal('show');
                    }
                }
            });
        });
    }

    $mentionModal.on('shown.bs.modal', function (e) {
        $mentionInput.focus();
    });

    $mentionModal.on('hidden.bs.modal', function (e) {
        wysiwyg_ta.codemirror.focus();
        $mentionInput.val('');
        $('.mention-item').show()
    });

//filter the name while typing
    $mentionInput.on('keyup', function () {
        $('.mention-item')
            .removeClass('hidden')
            .filter(function () {
                $(this).toggle($(this).text().toLowerCase().indexOf($mentionInput.val()) > -1)
            });
    });
});
