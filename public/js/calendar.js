$(function() {
	var m = padleft(new Date().getMonth() + 1,2);

	$('.calendar').fullCalendar({
		dayPopoverFormat: 'dddd Do',
		editable: 0,
		eventLimit: 4,
		eventRender: function(ev,el) {
			var icon,
				content,
				title = '';

			switch(ev.type) {
				case 'rereview':
					icon = 'list-alt';
					if(ev.organisation != '') {
						content = ev.organisation + ' - ' + (ev.surveyor ? ev.surveyor : 'Unassigned');
					} else {
						content = (ev.surveyor ? ev.surveyor : 'Unassigned');
					}
					break;

				case 'survey':
					icon = 'list-alt';
					if(ev.organisation != '') {
						content = ev.organisation + ' - ' + (ev.surveyor ? ev.surveyor : 'Unassigned');
					} else {
						content = (ev.surveyor ? ev.surveyor : 'Unassigned');
					}
					break;

				case 'holiday':
					icon = 'plane';
					content = ev.risk_engineer;
					break;

				case 're-admin':
					icon = 'pencil';
					content = (ev.title ? ev.title + ' - ' : '') + ev.risk_engineer;
					title = 'RE Admin Task: ' + content;
					break;

				default:
					icon = 'star';
					if (ev.title) {
						content = ev.title;
					} else {
						content = ucwords(ev.type.replace('-',' '));
						title = content;
					}
					break;
			}

			title = (!title)
				? ucwords(ev.type.replace('-',' ')) + ': ' + content
				: title;

			$(el).attr(
				'title',
				title
			).addClass(
				'calendar--' + ev.type
			).find('.fc-content').html(
				'<i class="fa fa-' + icon + '"></i> ' + content
			);
		},
		events: {
			url: '/calendar',
			cache: true
		},
		firstDay:1,
		header: {
			left: 'prev',
			center: 'title',
			right: 'today next'
		},
		loading: function(status,view) {
			if (status) {
				$(view.el).addClass('fc-loading').parent().append('<div class="fc-loading-progress"><i class="fa fa-refresh fa-spin"></i></div>');
			} else {
				$(view.el).removeClass('fc-loading').parent().find('.fc-loading-progress').remove();
			}
		}
	});
});
