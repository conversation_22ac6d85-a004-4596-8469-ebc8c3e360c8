/*globals jQuery, ResponsiveBootstrapToolkit, bootbox*/
(function($, viewport) {
    var cache = {
        $window: $(window),
        $sortable: $('.js--sortable'),
        $library: $('.library'),
        $librarySection: $('.library__section'),
        $libraryItemBody: $('.library-item__body'),
        $dataTablesPagination: $('.js--data-table--pagination'),
        $dataTablesTableControls: $('.js--data-table--table-controls'),
        $modalNameLink: $('.js--modal-name'),
        $assignTo: $('.js--assign-to'),
        $formValidate: $('.js--form-validate'),
        $tableOpenChild: $('.js--table--open-child'),
        $filterDataTable: $('.js--filter-data-table'),
        $filterDataTableReports: $('.js--filter-data-table-reports'),
        $searchDataTable: $('.js--search-data-table'),
        $courseToggleVisibility: $('.js--course-toggle-visibility'),
        $courseFormLibertySectors: $('.course-edit__form-liberty-sectors'),
        $courseLessonAddCopy: $('.js--course-lesson-add, .js--course-lesson-copy'),
        $modalCoursesLibertyNew: $('.js--modal-liberty-new-course'),
        $filterReportButton: $('.js--filter-report-data-table'),
        $reminderButton: $('.js--reminder_button')
    };

    var viewportReadyTimer = null;

    /**
     * Initialises the module
     * @function _init
     */

    var _init = function() {
        // Sortable: drag and drop reordering
        
        cache.$sortable.sortable({
            onSort: function(event){
                var oldOrder = 0;
                var lesson = new Object();
                var lessons = new Array();
                var seq = 0;
                //alert('aeeoo');
                cache.$sortable.children().each(function(){
                    oldOrder = $(this).data('order');
                    seq++;
                    //alert(oldOrder+' '+seq);
                    $(this).data('order', seq);
                    lesson = new Object();
                    lesson.id = $(this).data('lesson');
                    lesson.seq = seq;
                    lessons.push(lesson);
                });

                url = '/learning/course/' +course_id + '/lessons/sort';
                $.ajax({
                    type: 'PUT',
                    url : url,
                    data: {lessons : lessons},
                    success: function(response){

                        if(response.response != undefined && response.response == 'success'){
                            show_success_notify('Lessons list updated.');
                            //return true;
                        }else{
                            show_error_notify('There was an error updating your lessons list.')
                        }

                    }
                });
            }
        });

        // add events
        _attachEvents();

        // Setup any JS-based layout
        _layout();

        // Data Tables and striped layout
        _initTables();

        // Form validation
        _initFormValidation(cache.$formValidate);

    };
    /**
     * Attach all events
     * @function _attachEvents
     */

    var _attachEvents = function() {
        cache.$window.on('resize', _resize);
        cache.$modalNameLink.on('click', _modalNameClick);
        cache.$assignTo.on('click', _assignToClick);
        cache.$tableOpenChild.on('click', _tableOpenChild);
        cache.$filterDataTable.on('change', _filterDataTable);
        cache.$filterDataTableReports.on('change', _filterDataTableReports);
        cache.$searchDataTable.on('keyup', _searchDataTable);
        cache.$courseToggleVisibility.on('change', _courseToggleVisibility);
        cache.$courseLessonAddCopy.on('click', _modalCourseLessonAddCopy);
        cache.$modalCoursesLibertyNew.on('click', _modalCoursesLibertyNew);
        cache.$filterReportButton.on('click', _filterReport);
        cache.$reminderButton.on('click', _reminder);
    };

    /**
     * Called on browser resize
     * @function _resize
     */

    var _resize = function() {
        // reset library equal heights
        cache.$libraryItemBody.css('height', 'auto');
        _libraryEqualHeights();
    };

    /**
     * Setup any JS-based layout
     * @function _layout
     */

    var _layout = function() {
        // If ResponsiveBootstrapToolkit not ready, poll until it is
        if (viewport.current() === 'unrecognized') {
            viewportReadyTimer = viewportReadyTimer || window.setInterval(_layout, 100);
            return;
        }

        window.clearTimeout(viewportReadyTimer);

        // All layout methods below
        _libraryEqualHeights();
    };

    /**
     * Set equal heights on content in library items
     * so they line up nicely
     * @function _libraryEqualHeights
     */

    var _libraryEqualHeights = function() {
        // Library equal heights
        if (cache.$library.length) {
            cache.$librarySection.each(_libraryEqualHeightsSection);
        }
    };

    /**
     * Set equal heights on content in library items
     * Divide content into rows of 2 or 3 (depending on breakpoint)
     * at set heights with their adjacent neighbours
     * @function _libraryEqualHeightsSection
     * @param {number} index
     * @param {element} item
     */

    var _libraryEqualHeightsSection = function(index, item) {
        var $section = $(item),
            $sectionItems = $section.find('.library-item'),
            sectionItemGroups = [],
            sectionItemGroupsCounter = 0,
            groupSize = (viewport.is('>sm')) ? 3 : 2;

        for (var i = 0; i < $sectionItems.length; i++) {

            if (i % groupSize === 0) {
                // Increment counter after first group
                sectionItemGroupsCounter = (i === 0) ? sectionItemGroupsCounter : (sectionItemGroupsCounter + 1);
                sectionItemGroups[sectionItemGroupsCounter] = $sectionItems.eq(i);
            } else {
                sectionItemGroups[sectionItemGroupsCounter] = $(sectionItemGroups[sectionItemGroupsCounter]).add($sectionItems.eq(i));
            }
        }
        // Set equal heights on the groups
        $.each(sectionItemGroups, _libraryItemSetEqualHeight);
    };

    /**
     * Set equal heights on individual groups of 2/3 library items
     * @function _libraryItemSetEqualHeight
     * @param {number} index
     * @param {jQuery} $items
     */

    var _libraryItemSetEqualHeight = function(index, $items) {
        // @TODO sort out: temp fix for carousel
        if (!$items.parents('.item:not(.active)').length) {
            $items.find('.library-item__body').equalHeights();
        }
    };

    /**
     * Show a modal that allows user to name the lesson/course they want to create
     * @function _modalNameClick
     * @param {event} e
     */

    var _modalNameClick = function(e) {
        e.preventDefault();

        var $link = $(e.currentTarget),
            href = $link.attr('href'),
            field = $link.data('modal-field'),
            fieldLower = field.toLowerCase();

        bootbox.prompt(field + ' name', function(result) {
            if (result !== null && result !== '') {
                window.location = href + '?' + fieldLower + '-name=' + encodeURIComponent(result);
            }
        });
    };

    /**
     * "Assign to" button for assigning course to a users
     * shows a modal
     * @function _assignToClick
     * @param {event} e
     */

    var _assignToClick = function(e) {
        e.preventDefault();

        var modal = bootbox.dialog({
            title: 'Assign course',
            message: $('.assign-users').html(),
            className: 'page--lms', // pick up page styles
            size: 'large',
            buttons: {
                danger: {
                    label: 'Cancel',
                    className: 'btn-danger'
                },
                success: {
                    label: 'Save',
                    className: 'btn-primary',
                    callback: _assignUsersSubmit
                }
            }
        });

        modal.on('shown.bs.modal', _assignUsersListInit);
    };

    /**
     * Initialise assign users JS
     * called after modal shows, as bootbox creates the elements after domready
     * @function _assignUsersListInit
     * @param {event} e
     */

    var _assignUsersListInit = function(e) {
        var $parent = $(e.currentTarget),
            $listItems = $parent.find('.assign-users__list-item'),
            $table = $parent.find('.assign-users__table'),
            $search = $parent.find('.assign-users__search'),
            $filter = $parent.find('.assign-users__filter'),
            table;

        $listItems.on('click', _assignUsersClick);

        table = $table.DataTable({
            info: false,
            searching: true,
            lengthChange: false,
            dom: '<"row"<"col-sm-12"tr>>' +
            '<"row"<"col-md-4"l><"col-md-4"i><"col-md-4"p>>',
        });

        // Wrap in responsive wrapper
        $table.wrap('<div class="table-responsive"></div>');

        $search.on('keyup', function() {
            table.search($(this).val()).draw();
        });

        $filter.on('change', function() {

            var searchTerm = $(this).find('option:selected').attr('value'),
                column = table.column($(this).data('column'));

            if ( column.search() !== searchTerm ) {
                column
                    .search( searchTerm, false, false )
                    .draw();
            }
        });
    };

    /**
     * Click event for assign users list items
     * @function _assignUsersClick
     * @param {event} e
     */

    var _assignUsersClick = function(e) {
        e.preventDefault();
        var $item = $(e.currentTarget);

        $item.parents('tr').eq(0).toggleClass('active');
    };

    /**
     * Called when submiting the assign users modal
     * Build active DOM elements into a list that can be sent via AJAX
     * @function _assignUsersSubmit
     * @param {event} e
     */

    var _assignUsersSubmit = function(e) {

        e.preventDefault();

        var $modal = this,
            $listGroup = $modal.find('.assign-users__list'),
            $activeItems = $listGroup.find('tr.active'),
            activeItems = [];

        // Build active DOM elements into a list
        $activeItems.each(function(index, item) {
            activeItems.push($(item).text());
        });

        // Submit via ajax
        $.ajax('/', {
            data: {
                users: activeItems.join(',')
            },
            success: function() {
                $modal.modal('hide');
                var $message = $('.assign-users__alert--success').clone();
                $message.find('.alert__message').text('Course assigned to ' + activeItems.length + ' users');

                var modal = bootbox.dialog({
                    title: 'Success',
                    message: $message.html(),
                    className: 'page--lms', // pick up page styles
                    buttons: {
                        success: {
                            label: 'OK',
                            className: 'btn-primary',
                        }
                    }
                });

            },
            error: function() {
                var $message = $('.assign-users__alert--error').clone();
                $message.find('.alert__message').text('Sorry, there was a connection issue');

                var modal = bootbox.dialog({
                    title: 'Error',
                    message: $message.html(),
                    className: 'page--lms', // pick up page styles
                    buttons: {
                        success: {
                            label: 'OK',
                            className: 'btn-primary',
                        }
                    }
                });
            }
        });

        return false; // required to stop modal closing
    };

    /**
     * Set up jQuery validation on forms
     * @function _initFormValidation
     * @param {jQuery} $selector elements to init
     */

    var _initFormValidation = function($selector) {

        $selector.validate({
            // Move error message to top of section
            errorPlacement: function(error, element) {
                error.appendTo(element.parents('.page-builder-view__page-item--question').find('.form__validation-message'));
            },
            // Submit form on succesful validation
            submitHandler: function(form) {
                form.submit();
            },
            // Highlight incorrect fields
            highlight: function(element) {
                var $formGroup = $(element).parents('.form-group').eq(0);
                $formGroup.addClass('has-error');
            },
            // Unhighlight incorrect fields
            unhighlight: function(element) {
                var $formGroup = $(element).parents('.form-group').eq(0);
                $formGroup.removeClass('has-error');
            }
        });
    };

    /**
     * Set up datatables and styling
     * @function _initTables
     */

    var _initTables = function() {

        // Setup data Tables
        cache.$dataTablesPagination.dataTable({
            info: false,
            searching: false,
            ordering: false,
            lengthChange: false
        });

        // Wrap in responsive wrapper
        cache.$dataTablesPagination.wrap('<div class="table-responsive"></div>');

        // init tables with table controls (search / filters)
        _initDataTableTableControls(cache.$dataTablesTableControls.eq(0));

    };

    /**
     * Data tables as seen on progress and reports page
     * can be filtered by controls above table
     * @function _initDataTableTableControls
     * @param {jQuery} $dataTable
     */

    var _initDataTableTableControls = function($dataTable) {
        var table = $dataTable.DataTable({
            dom:
              "<'row align-items-center'<'col-sm-12 col-md-6'><'col-sm-12 col-md-6'l>>" +
              "<'row'<'col-sm-12'tr>>" +
              "<'row'<'col-sm-12 col-md-7'p><'col-sm-12 col-md-5'i>>",
            renderer: 'bootstrap',
            columnDefs: [{
                sortable : false,
                targets : [ 'th--no-sort' ]
            }],
            oLanguage: {
                sSearch: '',
                sSearchPlaceholder: 'Search',
                oPaginate: {
                    sNext: '<i class="icon icon-chevron-right"></i>',
                    sPrevious: '<i class="icon icon-chevron-left"></i>'
                }
            },
            fnDrawCallback: function (oSettings) {
                // TODO: finish hide pagination when only 1 page is available?

                // var tblInstance = '#' + oSettings.sInstance + '_wrapper';

                // if ($(tblInstance).length) {
                //     if ($(tblInstance).find('#dataTables_paginate span span.paginate_button').length) {
                //         $(tblInstance).find('#dataTables_paginate').css('display', 'block');
                //     } else {
                //         $(tblInstance).find('#dataTables_paginate').css('display', 'none !important');
                //     }
                // }
            }
        });

        // Add event listener for opening and closing details
        $dataTable.on('click', '.js--table--open-child', _dataTableProgressOpenChild.bind(null, table));

        // Wrap in responsive wrapper
        $dataTable.wrap('<div class="table-responsive"></div>');
    };

    /**
     * Take the HTML in hidden last <td> of row to display lessons
     * in a child row
     * @function _dataTableProgressFormatChild
     * @param {array} data row data from datatable
     * @param {boolean} isParentOdd is parent row odd? used for styling
     */

    var _dataTableProgressFormatChild = function(data, isParentOdd) {
        var $tr = $(data[data.length - 1]).find('tr');
        if (isParentOdd) {
            $tr.addClass('tr--child odd');
        } else {
            $tr.addClass('tr--child even');
        }
        return $tr;
    };

    /**
     * On clicking "expand", show child table row with lesson info
     * @function _dataTableProgressOpenChild
     * @param {object} table DataTable object
     * @param {event} e
     */

    var _dataTableProgressOpenChild = function(table, e) {

        var tr = $(e.currentTarget).closest('tr'),
            isOdd = tr.hasClass('odd'),
            row = table.row(tr);

        if (row.child.isShown()) {
            // This row is already open - close it
            row.child.hide();
            tr.removeClass('shown');
        } else {
            // Open this row
            row.child(_dataTableProgressFormatChild(row.data(), isOdd)).show();
            tr.addClass('shown');
        }
    };





    /**
     * Change icon when opening / closing child rows
     * open child row is handled by DataTables _initTables
     * @function _tableOpenChild
     * @param {event} e
     */

    var _tableOpenChild = function(e) {
        var $btn = $(e.currentTarget);

        // Update button icon
        $btn
            .find('.icon-chevron-down, .icon-chevron-up')
            .toggleClass('icon-chevron-down')
            .toggleClass('icon-chevron-up');


    };

    /**
     * Change contents of data-table based on dropdown option selected
     * @function _filterDataTable
     * @param {event} e
     */

    var _filterDataTable = function(e) {
        var $select = $(e.currentTarget),
            $table = $($select.data('table')),
            columnIndex = $table.find($select.data('column')).index(),
            dataTable = $table.DataTable(),
            searchTerm = $select.find('option:selected').attr('value'),
            column = dataTable.column(columnIndex);

        if($select.prop('id') == 'organisation'){
            var selectedOrg = $select.find('option:selected').data('organisation');
            var branches = $("#branch");
            if($.trim(searchTerm) != ''){
                //branches.prop('disabled', false);
                branches.val('');
                branches.find('option').addClass('hidden');
                branches.find('option[data-organisation="0"]').removeClass('hidden');
                branches.find('option[data-organisation="'+selectedOrg+'"]').removeClass('hidden');
            }else{
                branches.find('option').removeClass('hidden');
                /*branches.val('');
                var intColumnIndex = $table.find(branches.data('column')).index();
                var intColumn = dataTable.column(intColumnIndex);
                if ( intColumn.search() !== branches.val() ) {
                    intColumn
                        .search( branches.val(), false, false )
                        .draw();
                }
                branches.prop('disabled', true);*/
            }
        }

        if ( column.search() !== searchTerm ) {
            column
                .search( searchTerm, false, false )
                .draw();
        }
    };

    /**
     * Change contents of data-table based on dropdown option selected
     * @function _filterDataTableReports
     * @param {event} e
     */
    var _filterDataTableReports = function(e) {
        var $select = $(e.currentTarget),
            org = $select.find('option:selected').data('organisation'),
            mainUrl = $select.data('url');
        var url = mainUrl+'?org='+org;
        if(org == 0){
            url = mainUrl;
        }
        //$(location).attr('href',url);
    };

    var _filterReport = function(e) {

        var $button = $(e.currentTarget),
            filters = $button.closest('.table-controls').find('.form-control'),
            params = new Object(),
            url = $button.data('url');
        if(filters.length > 0){
            filters.each(function(){
                if($(this).prop('name') != 'search' && $.trim($(this).val()) != ''){
                    params[$(this).prop('name')] = $(this).val();
                }
            });
            if(Object.keys(params).length > 0){
                $(location).attr('href',url+'?'+$.param(params));
            }
        }
    };

    /**
     * Search data table from external search field
     * @function _searchDataTable
     * @param {event} e
     */

    var _searchDataTable = function(e) {
        var $input = $(e.currentTarget),
            $table = $($input.data('table')),
            dataTable = $table.DataTable(),
            searchTerm = $input.val();

        dataTable
            .search(searchTerm)
            .draw();
    };

    /**
     * Course edit: changing the Visibility (published/Unpublished)
     * hides and shows additional section
     * @function _courseToggleVisibility
     * @param {event} e
     */

    var _courseToggleVisibility = function(e) {
        var $select = $(e.currentTarget),
            selected = $select.find('option:selected').val();

        if (selected === 'published') {
            cache.$courseFormLibertySectors.removeClass('hidden');
        } else {
            cache.$courseFormLibertySectors.addClass('hidden');
        }
    };

    /**
     * Course edit: add or copy course
     * show popup allow name input or copy from another course
     * @function _modalCourseLessonAddCopy
     * @param {event} e
     */

    var _modalCourseLessonAddCopy = function(e) {
        var $btn = $(e.currentTarget),
            isCopy = $btn.hasClass('js--course-lesson-copy'),
            title = isCopy ? 'Copy lesson' : 'Add lesson',
            $message = isCopy ? $('.modal-copy-lesson') : $('.modal-add-lesson'),
            lessonTitle = isCopy ? ' copy' : '';

        e.preventDefault();

        var modal = bootbox.dialog({
            title: title,
            message: $message.html(),
            className: 'page--lms', // pick up page styles
            buttons: {
                danger: {
                    label: 'Cancel',
                    className: 'btn-danger'
                },
                success: {
                    label: 'Add',
                    className: 'btn-success',
                    callback: function(e) {

                        var $form = $(e.currentTarget).parents('.modal').eq(0).find('.js--form-validate');
                        if ($form.valid()) {
                            var lessonName = $form.find('input[name="lesson-title"]').val();
                            window.location = '/lessons/edit-lesson-new.html?lesson-title=' + lessonName;
                        }

                        return false;

                    }
                }
            }
        });

        if (isCopy) {
            lessonTitle = $btn
                    .closest('.lesson-list__lesson')
                    .find('.lesson-list__title')
                    .text() + lessonTitle;
        }

        modal.on('shown.bs.modal', _courseLessonAddInit.bind(null, lessonTitle));

    };

    /**
     * Course edit: add lesson modal
     * called when modal is displayed, as content is added dynamically
     * @function _courseLessonAddInit
     * @param {string} lessonTitle prefill the lesson title field
     * @param {event} e
     */

    var _courseLessonAddInit = function(lessonTitle, e) {
        var $parent = $(e.currentTarget),
            $lessonTitle = $parent.find('.modal-add-lesson__lesson-title'),
            $radios = $parent.find('input[type=radio]'),
            $hiddenForm = $parent.find('.modal-add-lesson__course-form'),
            $validateForm = $parent.find('.js--form-validate');

        $lessonTitle.val(lessonTitle);

        _initFormValidation($validateForm);

        $validateForm.on('submit', function() { return false; }); // Buttons are outside of form

        $radios.on('change', _courseLessonAddRadioChange.bind(null, $hiddenForm));

    };

    /**
     * Course edit: add course modal - radio button change
     * show / hide form below
     * @function _courseLessonAddRadioChange
     * @param {jQuery} $hiddenForm form to show
     * @param {event} e
     */

    var _courseLessonAddRadioChange = function($hiddenForm, e) {
        if ($(e.currentTarget).val() === 'import') {
            $hiddenForm.removeClass('hidden');
        } else {
            $hiddenForm.addClass('hidden');
        }
    };

    /**
     * Courses: new course for liberty admin
     * show popup with name input, choose type of liberty or organisation
     * @function _modalCoursesLibertyNew
     * @param {event} e
     */

    var _modalCoursesLibertyNew = function(e) {
        var $btn = $(e.currentTarget),
            title = 'New course',
            $message = $('.modal-new-course');

        e.preventDefault();

        var modal = bootbox.dialog({
            title: title,
            message: $message.html(),
            className: 'page--lms', // pick up page styles
            buttons: {
                danger: {
                    label: 'Cancel',
                    className: 'btn-danger'
                },
                success: {
                    label: 'Add',
                    className: 'btn-success',
                    callback: function(e) {

                        var $form = $(e.currentTarget).parents('.modal').eq(0).find('.js--form-validate');
                        if ($form.valid()) {
                            var courseName = $form.find('input[name="course-title"]').val();
                            window.location = '/courses/edit-course-new-org.html?course-title=' + courseName;
                        }

                        return false;

                    }
                }
            }
        });

        modal.on('shown.bs.modal', _coursesAddInit);

    };

    /**
     * Courses: new course modal
     * called when modal is displayed, as content is added dynamically
     * @function _coursesAddInit
     * @param {event} e
     */

    var _coursesAddInit = function(e) {
        var $parent = $(e.currentTarget),
            $radios = $parent.find('input[type=radio]'),
            $hiddenForm = $parent.find('.modal-add-course__org-form'),
            $validateForm = $parent.find('.js--form-validate');

        _initFormValidation($validateForm);

        $validateForm.on('submit', function() { return false; }); // Buttons are outside of form

        $radios.on('change', _coursesAddRadioChange.bind(null, $hiddenForm));

    };

    /**
     * Courses: new course modal - radio button change
     * show / hide form below
     * @function _coursesAddRadioChange
     * @param {jQuery} $hiddenForm form to show
     * @param {event} e
     */

    var _coursesAddRadioChange = function($hiddenForm, e) {
        if ($(e.currentTarget).val() === 'organisation') {
            $hiddenForm.removeClass('hidden');
        } else {
            $hiddenForm.addClass('hidden');
        }
    };
    
    function show_success_notify(message){

        $.notify({
            message : message
        },{
            delay: 2000,
            placement: {
                from: 'bottom',
                align: 'right'
            },
            type: 'success'
        })

    }
    function show_error_notify(message){

        $.notify({
            message: message
        },{
            delay: 2000,
            placement: {
                from: 'bottom',
                align : 'right'
            },
            type: 'danger'
        })

    }
    var _reminder = function(){
        var user = $(this).closest('tr').data('user');
        var url = $(this).closest('tr').data('course');
        var data = {};
        data.user_id = user;
        $.ajax({
                'type' : 'POST',
                'url'  : url,
                'data' : data,
                success: function (response) {
                    if(response.response == 'success'){
                        show_success_notify('Reminder sent');
                    }else{
                        show_error_notify('Error to send the reminder');
                    }
                }
            })
    }
    _init();
    // alert('hi');

}(jQuery, ResponsiveBootstrapToolkit));
$(document).ready(function(){
    function show_success_notify(message){

        $.notify({
            message : message
        },{
            delay: 2000,
            placement: {
                from: 'bottom',
                align: 'right'
            },
            type: 'success'
        })

    }
    function show_error_notify(message){

        $.notify({
            message: message
        },{
            delay: 2000,
            placement: {
                from: 'bottom',
                align : 'right'
            },
            type: 'danger'
        })

    }

    $(document).on('click', '.frmAssign .btn', function(e){
        e.preventDefault();
        var url = $(this).parent().prop('action');
        var users = $(this).parent().find('input[name="users"]').val();
        var data = {};
        data.users = users;
        $.ajax({
            'type' : 'POST',
            'url'  : url,
            'data' : data,
            success: function (response) {
                if(response.response == 'success'){
                    show_success_notify('Course assigned successfully');
                    $('.bootbox-close-button').click();
                }else{
                    show_error_notify('Error to Assign');
                }
            }
        })
    });

    $(document).on('click', '.all-opts', function () {
        var element      = $(this);
        var identefier   = element.data('identifier');
        var target_table = identefier === 'organisation' ? '.assign-organisations__table' : '.assign-sectors__table';
        $(target_table).find('.assign-users__list-item').each(function () {
            if (element.data('act') == 1) {
                if ($(this).parent().parent().hasClass('active') == false) {
                    $(this).click();
                }
            } else {
                if ($(this).parent().parent().hasClass('active') == true) {
                    $(this).click();
                }
            }
        });

        if (element.data('act') == 1) {
            element.data('act', 2);
        } else {
            element.data('act', 1);
        }
    });

    $(document).on('click', '.top-submit', function () {
        var element = $(this);
        if ($('.assign-orgs__submit').length > 0) {
            $('.assign-orgs__submit').click();
        }
    });

    $(document).on('keyup', 'input[name="search"]', function () {
        $('.data-table').DataTable()
            .search($(this)
            .val())
            .draw();
    });
});
