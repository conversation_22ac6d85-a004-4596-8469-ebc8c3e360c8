$(function () {

    reorderAttributes();
    toggleColumn();
    onConfirmSendToClient();
    cautionIconDetector();
    preview();
    markAsComplete();
    generateModalValue();
    modalRecoStatus();
    submitMessage();
    sortGrading();
    ellipsisTextLogic();
    toggleReadMore();
    toggleReadLess();
    handleAttachment();

    let attachments = [];
    let attachmentLinks = [];

    function ellipsisTextLogic() {
        $('.item-wrap').on('click', '.toggle-assesment .toggle-arrow', function() {
            var dataActiveValue = $(this).parent().data('toggle');
            $(`.toggle1-${dataActiveValue}`).toggle();
            $(`.toggle2-${dataActiveValue}`).toggle();
        });
    }

    function sortGrading() {
        // if (typeof nested == 'undefined') {
        //     return false;
        // }

        new Sortable(nested, {
            animation: 150,
            fallbackOnBody: false,
            swapThreshold: 0.65
        });

        // Child
        var nestedSortables = [].slice.call(document.querySelectorAll('.nested-sortable'));
        for (var i = 0; i < nestedSortables.length; i++) {
            new Sortable(nestedSortables[i], {
                animation: 150,
                fallbackOnBody: false,
                swapThreshold: 0.65
            });
        }
    }

    function modalRecoStatus()
    {
        $(document).on('click', '.show_message_thread', function(){
            let currentStatusId = $(this).attr('data-refclass');
            let status = $('#status-'+currentStatusId).text();
            let bgButton = 'bg-success'
            if (status=="Pending") {
                bgButton = 'bg-primary';
            }

            if (status=="Closed") {
                bgButton = 'bg-primary';
            }

            $('.modal-status').html(`
                <div class="mt-1 d-flex justify-content-center align-items-center ${bgButton} text-white" style="width:90px; border-radius:8px">
                    <span><b>${status}</b></span>
                </div>
            `);
        });
    }

    function generateModalValue() {
        $('.readmore').on('click', function() {
            let heading = document.querySelector('h3#modal-sub-attr-name');
            let paragraph = document.querySelector('p#text-area-id');
            heading.textContent = '';
            paragraph.textContent = '';
    
            let textAreaString = $(this).attr('data-string');
            let dotModalColor = $(this).attr('data-color');
            let subAttrName = $(this).attr('data-sub-attr-name');
            heading.textContent = subAttrName;
            paragraph.innerHTML = textAreaString.replace(/\r\n|\n|\r/g, '<br>');
            document.getElementById('modal-dot-color-div').style.cssText = dotModalColor;
        });
    }

    function submitMessage() {
        $("#submit-message").on('click', function() {

            const role = $('#user-role').val();
            let rrRef = $(this).attr('data-refclass');
            let surveyId = $("#submit-message").attr('data-survey-id');
            var threadMessage = $("input#thread-message").val();

            if (!threadMessage.length && !attachments.length) {
                return;
            }

            var payload = {
                rr_ref: rrRef,
                survey_id: surveyId,
                message: threadMessage
            }

            var attachment_text = "";

            if(attachments) {
                attachment_text = '<div class="message-list__item-files">';
                for (i = 0; i < attachments.length; i++) {
                    attachment_text += '<div class="message-list__item-file"><i class="icon icon-paperclip"></i> <a href="'+attachmentLinks[i]+'">'+attachments[i]+'</a></div>';
                }
                attachment_text += '</div>';
            }
    
            payload['attachments'] = attachment_text;

            $(this).prop('disabled', true);
            $(this).html('<div class="spinner-border text-center" role="status" style="width: 1rem;height: 1rem;"><span class="sr-only">Loading...</span></div>');

            let statusText = role !== 'broker-user' ? 'Closed' : 'Pending';

            $.ajax({
                type: 'POST',
                url: '/surveys/messaging/send',
                data: payload,
                success: function (data) {
                    const searchString    = "status-"+refIssue.replace("_issue_closed","_message");
                    const $outside_status = $('a#'+searchString);
                    const $action_btn     = $('.mark-as-complete[data-refissue="'+refIssue+'"]');

                    $('.close-thread').modal('hide');

                    if (role === 'broker-user') {
                        $outside_status
                            .removeClass('btn-outline')
                            .addClass('btn-pending')
                            .text(statusText);
                        $action_btn
                            .html(`<img src="/img/icon-check-not-complete.svg" /><span>Pending Review</span`);
                    } else {
                        $outside_status
                            .removeClass("btn-outline btn-pending")
                            .addClass("btn-primary")
                            .text(statusText);
                        $action_btn.addClass("disabled");
                    }
                },
                error: function (xhr, status, error) {
                    console.error('Error:', status, error);
                }
            });

            let submissionId = $(this).attr('data-submission-id');
            var closeField = $(this).attr('data-refissue');

            var payload = {
                survey_id: surveyId,
            }

            payload[closeField] = role !== 'broker-user' ? 1 : 2;

            $.ajax({
                type: 'POST',
                url: '/risk-improvement/form/submission/'+submissionId+'/close',
                data: payload,
                success: function (data) {
                    //$('.close-thread').modal('hide');
                    location.reload();
                },
                error: function (xhr, status, error) {
                    console.error('Error:', status, error);
                }
            });

        });
    }

    function handleAttachment() {
        $('.js--messenger-mark-as-complete__attach').on('click', function(e){
            var $modal = $('.close-thread');
            var $plupload = $modal.find('.js--messenger__plupload2')
                $button = $modal.find('button[type="submit"]')
                endpoint = '/surveys/messaging/upload'
                $plupload.toggleClass('hidden')
                survey_id = $('body').attr('id');
    
            var token = $('.messenger__input input[name="_token"]').val();
    
            $plupload.plupload({
                // General settings
                runtimes: 'html5,flash,silverlight,html4',
                url: endpoint,
    
                // Maximum file size
                max_file_size: '2mb',
    
                // Resize images on clientside if we can
                resize: {
                    width: 200,
                    height: 200,
                    quality: 90,
                    crop: true // crop to exact dimensions
                },
    
                // Specify what files to browse for
                filters: [{
                    title: "Image files",
                    extensions: "jpg,gif,png,jpeg,bmp"
                }, {
                    title: "Zip files",
                    extensions: "zip,avi"
                }, {
                    title: "Custom files",
                    extensions: "doc,docx,ppt,pptx,xls,xlsx,txt,rtf,pdf,msg"
                }],
    
                // Rename files by clicking on their titles
                rename: true,
    
                // Sort files
                sortable: true,
    
                // Enable ability to drag'n'drop files onto the widget (currently only HTML5 supports that)
                dragdrop: true,
    
                // Flash settings
                flash_swf_url: '/plupload/js/Moxie.swf',
    
                // Silverlight settings
                silverlight_xap_url: '/plupload/js/Moxie.xap',
    
                multipart_params : {
                    "survey_id": survey_id,
                    "_token" : token,
                },
    
                init: {
                    PostInit: function(e) {
                        $plupload.find('.plupload_button').addClass('btn').addClass('btn-xs').addClass('btn-primary');
                    },
                    FilesAdded: function(uploader, files) {
                        $plupload.addClass('file-added');
                        uploader.start();
                        $button.addClass('disabled');
                        $button.find('i.fa').removeClass('hidden');
                        pluploadFiles = files;
    
                    },
                    UploadComplete: function(uploader, files) {
                        $button.removeClass('disabled');
                        $button.find('i.fa').addClass('hidden');
                    },
                    FileUploaded: function(uploader, files, object) {
    
                        var response = JSON.parse(object.response);
    
                        if(response.response == "success") {
                            var url = "/surveys/messaging/attachment/" + survey_id + '/' + response.uploaded_filename + '/' + response.name;
                            attachmentLinks.push(url);
                            attachments.push(response.name);
                        }
    
                        if(response.response == 'error'){
                            bootbox.alert(response.message);
                        }
                    }
                },
    
            });
    
            pluploadInited = true;
        });
    }

    function markAsComplete() {

        $('#close-thread, button.bootbox-close-button.close').on('click', function(e) {
            $('.close-thread').modal('hide');
        });

        $('.mark-as-complete').on('click', function(e) {
            e.preventDefault();
            $('.close-thread').modal('show');
            let currentStatusId = $(this).attr('data-refclass');
            let surveyId = $(this).attr('data-survey-id');
            let submissionId = $(this).attr('data-submission-id');
            var closeField = $(this).attr('data-refissue');
            $("#submit-message").attr('data-refclass', currentStatusId);
            $("#submit-message").attr('data-survey-id', surveyId);
            $("#submit-message").attr('data-submission-id', submissionId);
            $("#submit-message").attr('data-refissue', closeField);
        });
    }

    function preview() {
        let path = window.location.pathname;
        let pathSegments = path.split("/");
        let isPreview = path.includes('preview');

        if (isPreview) {
            $('div.item').trigger('click').off('click');
            $('.head-tier-1-toggle').trigger('click').off('click');
            $('.head-tier-2-toggle').trigger('click').off('click');
            $('i.toggle-arrow').trigger('click').off('click');
            $('i.icon-eye').remove();
            $('.disable-box, .reminder').remove();
            $('#preview-btn, #send-to-client-btn').remove();
            $('.report-preview').show();
            $('#print-preview').show();
            $('span.show_message_thread').off('click');
        }

        $('a.back-editor').on('click', function() {
            let editorPath = path.replace("/preview", "");
            window.location = editorPath;
        });
    }

    function cautionIconDetector() {

        var rec = [];
        $(".rec-item").each(function (index, element) {
            let actives = $(element).attr('data-active');
            rec.push(actives)
        });

        const uniqueCategories = [...new Set(rec)];
        $(".grading-item").each(function (index, element) {
            let hover = $(element).attr('data-hover');
            for (const item of uniqueCategories) {
                console.log(hover+'~'+item);
                if (hover === item || (item === 'management-safe-systems-of-work' && hover === 'safe-systems') 
                    || (hover === 'financial-business-risk-exposure' && item === 'financial-and-business-risk')
                    || (hover === 'management-safe-systems-of-work' && item === 'safe-systems')
                    || (hover === 'public-and-products' && item === 'public-and-products-liability' )) {

                    const riskRecCount = $(`[data-active="${hover}"]`).length;
                    const hazaradCount = riskRecCount ? riskRecCount : $(`[data-active="${item}"]`).length;

                    if (hazaradCount <= 8) {
                        $(`.${hover}`).after(`<div class="reminder"><img src="/img/hazard-icons/hazard-${hazaradCount}.svg" alt=""></div>`);
                    } else {
                        $(`.${hover}`).after(`<div class="reminder"><img src="/img/hazard-icons/hazard-9.svg" alt=""></div>`);
                    }

                    if (hover !== item) {
                        $(element).attr('data-hover', item);
                    }
                }
            }
        });
    }

    function onConfirmSendToClient() {
        $('#confirm-and-send-to-client-button').on('click', function () {

            const path     = window.location.pathname.replace(/^\/|\/$/g, '');
            const segments = path.split('/');
            const currentPage = segments.slice(0, 3).join('/');

            var emails = [];
            let lastEnteredEmail = $('#multi-field').val();

            if (lastEnteredEmail) {
                $('#multiemail').append('<div class="email-text">' + lastEnteredEmail + '<a href="#" class="close-email-list"> [x] </a></div>'); // append the value to the div
                $('#multi-field').val("");
            }

            const surveyId = $("#survey_id").val();
            const orginisationName = $("#organisation_name").val();
            const city = $("#city").val();
            const adminUserId = $("#admin-user-id").val();
            const orgId = $('#organisation_id').val();
            const sector = $('#sector').val();

            $(".email-text").each(function () {
                emails.push($(this).text().split(" ")[0]);
            });

            $.ajax({
                type: 'POST',
                url: '/microsite/send-to-client',
                data: {
                    emails: emails,
                    survey_id: surveyId,
                    organisation_name: orginisationName,
                    city: city,
                    created_by_admin_user_id: adminUserId,
                    is_dtr: currentPage.includes('dtr') ? 1 : 0,
                    org_id: orgId,
                    sector,
                },
                success: function (data) {
                    console.log('Success', data);
                    $('div#sendtoclient').modal('hide');
                    $(".success-display").append(
                        `<div class="alert alert-success alert-dismissible" role="alert">
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <i class="icon icon-x-circle"></i>
                            </button>
                            <strong class="alert-heading"><i class="icon icon-check-circle icon-md mr-2"></i>Success:</strong>
                            <div class="mb-0">
                                Message sent.
                            </div>
                        </div>`);
                },
                error: function (xhr, status, error) {
                    console.error('Error:', status, error);
                }
            });
        });
    }

    function reorderAttributes() {

        var riskGradingAttributeOrder = {};
        var riskGradingSubAttributeOrder = {};
        var surveyId = $("body.survey-id").attr('id');

        $("#nested").on("change", function () {

            var order = 0;

            $('#nested').find('div.nested-1').each(function () {
                $.each(this.attributes, function (index, attr) {
                    if (attr.name === 'data-attribute-id') {
                        riskGradingAttributeOrder[attr.value] = order++;
                    }
                });
            });

            $('#nested').find('div.nested-2').each(function () {
                $.each(this.attributes, function (index, attr) {
                    if (attr.name === 'data-sub-attribute-id') {
                        riskGradingSubAttributeOrder[attr.value] = order++;
                    }
                });
            });

            let postData = {
                survey_id: surveyId,
                risk_grading_attribute_order: riskGradingAttributeOrder,
                risk_grading_sub_attribute_order: riskGradingSubAttributeOrder
            };

            $.ajax({
                type: 'POST',
                url: '/microsite/risk-grading-re-order',
                data: JSON.stringify(postData),
                dataType: 'json',
                headers: {
                    'Content-Type': 'application/json',
                },
                success: function (data) {
                    console.log('Success:', data);
                },
                error: function (xhr, status, error) {
                    console.error('Error:', status, error);
                }
            });

        });
    }

    function toggleColumn() {
        $('.microsite-column').on('click', function (e) {
            e.preventDefault();
            const path     = window.location.pathname.replace(/^\/|\/$/g, '');
            const segments = path.split('/');
            const currentPage = segments.slice(0, 3).join('/');
            const field = $(this).data('column');
            const value = $(this).data('value');
            const key   = currentPage === 'risk-improvement/form/show' ? $(this).data('key') : $(this).data('srg-id');
            const attrId = $(this).data('attr-id') ?? null;
            const survey_id = $('input[name="survey_id"]').val();

            // AJAX Request for column toggle
            $(function () {
                const postData = {
                    survey_id: survey_id,
                    field: field,
                    value: value,
                    key: key,
                    attrId,
                };

                $.ajax({
                    type: 'POST',
                    url: '/microsite/toggle-column',
                    data: JSON.stringify(postData),
                    dataType: 'json',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    success: function (data) {
                        console.log('Success:', data);
                    },
                    error: function (xhr, status, error) {
                        console.error('Error:', status, error);
                    }
                });
            });
        });
    }

    function toggleReadMore() {
        $(document).on('click', '.toggle-read-more', function (e) {
            e.preventDefault();

            let content = $(this).parent().next().val();
            content = content + " <a href='#' class='toggle-read-less'>Read Less</a>"
            $(this).parent().html(content);
        });
    }

    function toggleReadLess() {
        $(document).on('click', '.toggle-read-less', function (e) {
            e.preventDefault();
            
            const summaryContent = $(this).parent().next().val();
            const countNewLine   = (summaryContent.match(/\n/g)||[]).length;
            const strLimit       = countNewLine < 5 ? 798 : 398;
            const truncatedText  = summaryContent.substring(0, strLimit) + "... <a href='#' class='toggle-read-more'>Read More</a>";
            $(this).parent().html(truncatedText);
        });
    }

    // Survey Details Toggle
    $('.survey-details .listings .item').each(function(){
        var slideContent = $(this).find('.toggle-details');
        $(this).on('click',function(){
            $(this).toggleClass('active');
            $(slideContent).slideToggle();
        });
    });

    // Risk Grading Toggle
    $('.risk-grading .listings > .item').each(function(){
        var toggleTitle = $(this).find('.head-tier-1-toggle');
        var toggleContent = $(this).find('.item-tier-toggle');
        $(toggleTitle).on('click',function(){
            $(toggleTitle).toggleClass('active');
            $(toggleContent).slideToggle();
        });
    });

    $('.risk-grading .item-tier-2 .item').each(function(){
        var toggleTitle2 = $(this).find('.head-tier-2-toggle');
        var toggleContent2 = $(this).find('.toggle-content');
        $(toggleTitle2).on('click',function(){
            $(toggleTitle2).toggleClass('active');
            $(toggleContent2).slideToggle();
        });
    });


    // Show - Hide
    $('.submission-gradings > .show-hide-toggle-parent, .show-hide-toggle-tier1, .show-hide-toggle-tier2').on('click',function(e){
      e.preventDefault();

      const key = $(this).attr('data-key');
      const $element = $(this).hasClass('show-hide-toggle-tier1') ? $(`#submission-gradings-${key}, #submission-gradings-${key} > .grading-item`) : $(`#sub-attr-${key}-wrapper`);
      $element.addClass('disable-box');

      const attrName = $(this).data('attr-name') || $(this).data('subattr-name');
      $(`input[name="${attrName}-hidden"]`).val("1");

      $(this).hide();
    });

    $('.submission-gradings > .show-toggle, .show-toggle-tier1, .show-toggle-tier2').each(function() {
        $(this).on('click',function(e) {
            e.preventDefault();
            
            const key = $(this).attr('data-key');
            const $element = $(this).hasClass('show-toggle-tier1') ? $(`#submission-gradings-${key}, #submission-gradings-${key} > .grading-item`) : $(`#sub-attr-${key}-wrapper`);
            $element
              .removeClass('disable-box');

            const attrName = $(this).data('attr-name');
            $(`input[name="${attrName}-hidden"]`).val("0");

            $(`#eye-attr-${key}, #eye-subattr-${key}`).show();
        });
    });

    $('#commentary-hide').click(function (e) {
      e.preventDefault();

      const surveyId = $('input[name="survey_id"]').val();
      const value = $(this).data('value');
      $('.commentary-wrapper').addClass('disable-box');
      toggleCommentary(surveyId, value);
    });

    $('#commentary-show').click(function (e) {
      e.preventDefault();

      const surveyId = $('input[name="survey_id"]').val();
      const value = $(this).data('value');
      $('.commentary-wrapper').removeClass('disable-box');
      toggleCommentary(surveyId, value);
    });


    function toggleCommentary(surveyId, val) {
      $.ajax({
        type: 'POST',
        url: '/microsite/toggle-commentary',
        data: JSON.stringify({
          survey_id: surveyId,
          value: val,
        }),
        dataType: 'json',
        headers: {
            'Content-Type': 'application/json',
        },
        success: function (response) {
          console.log('Success:', response);
        },
        error: function (xhr, status, err) {
          console.error('Error:', status, err);
        },
      });
    }


    // Show - Hide
    $('.show-hide-toggle-parent,.show-hide-toggle-tier1,.show-hide-toggle-tier2').on('click',function(e){
        e.preventDefault();
        $(this).parent().addClass('disable-box');
    });

    $('.show-toggle,.show-toggle-tier1,.show-toggle-tier2').each(function(){
        $(this).on('click',function(e){
            e.preventDefault();
            $(this).parent().removeClass('disable-box');
        });
    });

    // Recommendations Toggle
    $('.recomm-list .item-wrap').each(function(){
        var toggle = $(this).find('.toggle-arrow');
        var toggleContent = $(this).find('.recomm-toggle');
        $(toggle).on('click',function(){
            $(toggle).toggleClass('active');
            $(toggleContent).slideToggle();
        });
    });

    // Print Show/Hide
    $('.print-recomm').on('click',function(e){
        e.preventDefault();
        $('.risk-grading-and-recomm').addClass('disable-box-recom');
        $('.microsite-dashboard-flex').addClass('disable-box-recom');
    });

    $('.print-risk-grading').on('click',function(e){
        e.preventDefault();
        $('.risk-grading-and-recomm').addClass('disable-box-risk-grading');
        $('.microsite-dashboard-flex').addClass('disable-box-risk-grading');
    });
    
    $('.print-survey-details').on('click',function(e){
        e.preventDefault();
        $('.microsite-dashboard-flex').addClass('disable-box-survey-details');
    });

    $('.print-enable-recom').on('click',function(e){
        e.preventDefault();
        $('.risk-grading-and-recomm').removeClass('disable-box-recom');
        $('.microsite-dashboard-flex').removeClass('disable-box-recom');
    });

    $('.print-enable-risk-grading').on('click',function(e){
        e.preventDefault();
        $('.risk-grading-and-recomm').removeClass('disable-box-risk-grading');
        $('.microsite-dashboard-flex').removeClass('disable-box-risk-grading');
    });
    
    $('.print-enable-survey').on('click',function(e){
        e.preventDefault();
        $('.microsite-dashboard-flex').removeClass('disable-box-survey-details');
    });

    // On hover Risk grading and recommendation
    let highlightELements;
    $('.risk-grading .nested-1').hover(function() { 
        const targetItem = $(this).data('hover');
        highlightELements = $(`.rec-item[data-active="${targetItem}"]`);
        if (highlightELements) {
          highlightELements.each(function () {
            const color = $(this).data('hover-color');
            $(this)
              .removeClass('active')
              .removeClass(`hover-${color}`)
              .addClass('active')
              .addClass(`hover-${color}`);
          });
        }
    }, function() {
        highlightELements.each(function () {
          const color = $(this).data('hover-color');
            $(this)
              .removeClass('active')
              .removeClass(`hover-${color}`);
        });
    });

    // Email
    $('#multi-field').on('keypress', function(e) {
      if (e.which === 13 || e.which === 44) {
          const value = $(this).val().trim(); // get the value from the input field
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (value && emailRegex.test(value)) {
              $('#multiemail').append('<div class="email-text">' + value + '<a href="#" class="close-email-list"> [x] </a></div>'); // append the value to the div
              $(this).val(''); // clear the input field
          }
          e.preventDefault(); // prevent the default action of the Enter key
      } else if (e.which === 44) {
          e.preventDefault(); // prevent the comma from being added
      }
    });

    // Remove email on click
    $('div.email-field').on('click', '.close-email-list', function() {
      $(this).parent('.email-text').remove();
    });
    // Mobile toggle
    $('.micro-landing-page .heading').each(function(){
        var mobileToggle = $(this).find('.mobile-toggle');
        $(mobileToggle).on('click',function(){
            $(this).parent().next().slideToggle();
            $(this).toggleClass('active');
        });
    });

    //Images Preview

    $(function () {
        $('div.custom-image-box a, div.custom-attachments-item a').on('click', function () {
            $('div.marker-app').addClass('hidden');
            setTimeout(function () {
                $('div.fancybox-toolbar button.fancybox-button--close').on('click', function () {
                    $('div.marker-app').removeClass('hidden');
                });
            }, 500);
        });

        $('.rg-gradings-tooltip').tooltip({
            sanitize: false
        });
    })
});