!function(a){"use strict";"function"==typeof define&&define.amd?define(["jquery"],a):a(jQuery)}(function(a){"use strict";a.fn.sortable=function(b){var c,d=arguments;return this.each(function(){var e=a(this),f=e.data("sortable");if(f||!(b instanceof Object)&&b||(f=new Sortable(this,b),e.data("sortable",f)),f){if("widget"===b)return f;"destroy"===b?(f.destroy(),e.removeData("sortable")):"function"==typeof f[b]?c=f[b].apply(f,[].slice.call(d,1)):b in f.options&&(c=f.option.apply(f,d))}}),void 0===c?this:c}});