var args = require('system').args;
var page = require('webpage').create();
page.customHeaders = {
	"Cookie" : args[3]
  };
//page.open('http://gl.client.riskreduce.com/learning/course/24/lesson/12/test/result?user_id=131', function() {
page.open(decodeURIComponent(args[1]), function() {
	page.viewportSize = { width: 1024, height: 768 };
	if(args[4] !== undefined){
		var clipRect = page.evaluate(function (args) {
							return document.querySelector(args[4]).getBoundingClientRect();
						},args);
		page.clipRect = {
			top:    clipRect.top,
			left:   clipRect.left,
			width:  clipRect.width,
			height: clipRect.height
		};
	}
	if(args[2] !== undefined){
		page.render(args[2]);
		console.log('{"response":"success"}');
	}else{
		console.log('{"response":"error", "message": "you need to provide a name"}');
	}
	//console.log(args[4]);
	phantom.exit();
});