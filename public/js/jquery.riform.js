(function ( $ ) {
  
  var ffForm ;
  var sectionPrefix = 'sec_';
  var methods = {
    init : function( options ){ 
      
      if( !options ) options = {} ;
      
      // default field types (to be extended)
      var fieldsObj = {
        Standard_fields: {
          text: {
            label:        { type: 'text', placeholder: 'Label', validate: true },
            name:         { type: 'name', placeholder: 'Name' },
            placeholder:  { type: 'text', placeholder: 'Placeholder' },
            Class: {
              type: 'text', 
              value: ( 'class' in options ? options.Class : 'form-control'),
              placeholder: 'Class'
            },
            Required: { type: 'checkbox' },
            Condition: { type: 'checkboxDepends' },
            dependsOn: { type: 'depends' },
            Admin_only: { type: 'checkbox' },
            width: { type: 'select', options: { 'col-md-12': 'full', 'col-md-6': 'half', 'col-md-4': 'third', 'col-md-3': 'quarter'} },
            section:      { type: 'text', placeholder: 'Section' },
            CSR_Field: { type: 'checkbox' },
            PML_Property: { type: 'checkbox' },
            PML_Business_Interruption: { type: 'checkbox' }
          },
          textarea: {
            label:        { type: 'text', placeholder: 'Label', validate: true },
            name:         { type: 'name', placeholder: 'Name'}, 
            placeholder:  { type: 'text', placeholder: 'Placeholder' },
            Class: {
              type: 'text', 
              value: ( 'class' in options ? options.Class : 'form-control'),
              placeholder: 'Class'
            },
            Required: { type: 'checkbox' },
            Condition: { type: 'checkboxDepends' },
            dependsOn: { type: 'depends' },
            Admin_only: { type: 'checkbox' },
            width: { type: 'select', options: { 'col-md-12': 'full', 'col-md-6': 'half', 'col-md-4': 'third', 'col-md-3': 'quarter'} },
            section:      { type: 'text', placeholder: 'Section' },
            CSR_Field: { type: 'checkbox' }
          },
          email: {
            label:        { type: 'text', placeholder: 'Label', validate: true },
            name:         { type: 'name', placeholder: 'Name' },
            placeholder:  { type: 'text', placeholder: 'Placeholder' },
            Class: {
              type: 'text', 
              value: ( 'class' in options ? options.Class : 'form-control'),
              placeholder: 'Class'
            },
            Required: { type: 'checkbox' },
            Condition: { type: 'checkboxDepends' },
            dependsOn: { type: 'depends' },
            Admin_only: { type: 'checkbox' },
            width: { type: 'select', options: { 'col-md-12': 'full', 'col-md-6': 'half', 'col-md-4': 'third', 'col-md-3': 'quarter'} },
            section:      { type: 'text', placeholder: 'Section' },
            CSR_Field: { type: 'checkbox' }
          },
          select: {
            label:        { type: 'text', placeholder: 'Label', validate: true },
            name:         { type: 'name', placeholder: 'Name' },
            options:      { type: 'textarea', placeholder: 'one, two, three', validate: true },
            Class: {
              type: 'text', 
              value: ( 'class' in options ? options.Class : 'form-control'),
              placeholder: 'Class'
            },
            Required: { type: 'checkbox' },
            Condition: { type: 'checkboxDepends' },
            dependsOn: { type: 'depends' },
            Admin_Only: { type: 'checkbox' },
            Multi_Select: { type: 'checkbox' },
            width: { type: 'select', options: { 'col-md-12': 'full', 'col-md-6': 'half', 'col-md-4': 'third', 'col-md-3': 'quarter'} },
            section:      { type: 'text', placeholder: 'Section' },
            CSR_Field: { type: 'checkbox' }
          },
          select_risk_control: { // @note: Hide for SRG feature
            label:        { type: 'text', placeholder: 'Label', validate: true },
            name:         { type: 'name', placeholder: 'Name' },
            Class: {
              type: 'text',
              value: ( 'class' in options ? options.Class : 'form-control'),
              placeholder: 'Class'
            },
            Required: { type: 'checkbox' },
            Condition: { type: 'checkboxDepends' },
            dependsOn: { type: 'depends' },
            Admin_Only: { type: 'checkbox' },
            width: { type: 'select', options: { 'col-md-12': 'full', 'col-md-6': 'half', 'col-md-4': 'third', 'col-md-3': 'quarter'} },
            section:      { type: 'text', placeholder: 'Section' }
          },
          select_risk_control_breakdown: { // @note: Hide for SRG feature
            label:        { type: 'text', placeholder: 'Label', validate: true },
            name:         { type: 'name', placeholder: 'Name' },
            Class: {
              type: 'text',
              value: ( 'class' in options ? options.Class : 'form-control'),
              placeholder: 'Class'
            },
            // Required: { type: 'checkbox' },
            // Condition: { type: 'checkboxDepends' },
            // dependsOn: { type: 'depends' },
            // Admin_Only: { type: 'checkbox' },
            // width: { type: 'select', options: { 'col-md-12': 'full', 'col-md-6': 'half', 'col-md-4': 'third', 'col-md-3': 'quarter'} },
            // section:      { type: 'text', placeholder: 'Section' },
            // CSR_Field: { type: 'checkbox' }
          },
          checkbox: {
            label:        { type: 'text', placeholder: 'Label', validate: true },
            name:         { type: 'name', placeholder: 'Name' },
            Class: {
              type: 'text', 
              value: ( 'class' in options ? options.Class : 'form-control'),
              placeholder: 'Class'
            },
            Required: { type: 'checkbox' },
            Condition: { type: 'checkboxDepends' },
            dependsOn: { type: 'depends' },
            adminOnly: { type: 'checkbox' },
            width: { type: 'select', options: { 'col-md-12': 'full', 'col-md-6': 'half', 'col-md-4': 'third', 'col-md-3': 'quarter'} },
            section:      { type: 'text', placeholder: 'Section' },
            CSR_Field: { type: 'checkbox' }
          },
          radios: {
            label:        { type: 'text', placeholder: 'Label', validate: true },
            name:         { type: 'name', placeholder: 'Name' },
            options:      { type: 'textarea', placeholder: 'one, two, three', validate: true },
            Class: {
              type: 'text', 
              value: '',
              placeholder: 'Class'
            },
            Required: { type: 'checkbox' },
            Condition: { type: 'checkboxDepends' },
            dependsOn: { type: 'depends' },
            adminOnly: { type: 'checkbox' },
            width: { type: 'select', options: { 'col-md-12': 'full', 'col-md-6': 'half', 'col-md-4': 'third', 'col-md-3': 'quarter'} },
            section:      { type: 'text', placeholder: 'Section' },
            CSR_Field: { type: 'checkbox' }
          },
          date: {
            label:        { type: 'text', placeholder: 'Label', validate: true },
            name:         { type: 'name', placeholder: 'Name' },
            placeholder:  { type: 'text', placeholder: 'Placeholder' },
            Class: {
              type: 'text', 
              value: ( 'class' in options ? options.Class : 'form-control'),
              placeholder: 'Class'
            },
            format: {
              type: 'text', 
              value: ( 'dd/mm/yy'),
              placeholder: 'dd/mm/yy',
              validate: true
            },
            Required: { type: 'checkbox' },
            Condition: { type: 'checkboxDepends' },
            dependsOn: { type: 'depends' },
            adminOnly: { type: 'checkbox' },
            width: { type: 'select', options: { 'col-md-12': 'full', 'col-md-6': 'half', 'col-md-4': 'third', 'col-md-3': 'quarter'} },
            section:      { type: 'text', placeholder: 'Section' },
            CSR_Field: { type: 'checkbox' }
          },
          date_of_visit: {
            label:        { type: 'text', placeholder: 'Label', validate: true },
            name:         { type: 'name', placeholder: 'Name' },
            placeholder:  { type: 'text', placeholder: 'Placeholder' },
            Class: {
              type: 'text', 
              value: ( 'class' in options ? options.Class : 'form-control'),
              placeholder: 'Class'
            },
            format: {
              type: 'text', 
              value: ( 'dd/mm/yy'),
              placeholder: 'dd/mm/yy',
              validate: true
            },
            Required: { type: 'checkbox' },
            Condition: { type: 'checkboxDepends' },
            dependsOn: { type: 'depends' },
            adminOnly: { type: 'checkbox' },
            width: { type: 'select', options: { 'col-md-12': 'full', 'col-md-6': 'half', 'col-md-4': 'third', 'col-md-3': 'quarter'} },
            section:      { type: 'text', placeholder: 'Section' },
            CSR_Field: { type: 'checkbox' }
          },
          proposed_next_survey: {
            label:        { type: 'text', placeholder: 'Label', validate: true },
            name:         { type: 'name', placeholder: 'Name' },
            placeholder:  { type: 'text', placeholder: 'Placeholder' },
            Class: {
              type: 'text', 
              value: ( 'class' in options ? options.Class : 'form-control'),
              placeholder: 'Class'
            },
            format: {
              type: 'text', 
              value: ( 'dd/mm/yy'),
              placeholder: 'dd/mm/yy',
              validate: true
            },
            Required: { type: 'checkbox' },
            Condition: { type: 'checkboxDepends' },
            dependsOn: { type: 'depends' },
            adminOnly: { type: 'checkbox' },
            width: { type: 'select', options: { 'col-md-12': 'full', 'col-md-6': 'half', 'col-md-4': 'third', 'col-md-3': 'quarter'} },
            section:      { type: 'text', placeholder: 'Section' },
            CSR_Field: { type: 'checkbox' }
          },
          header: {
            label: { type: 'text', placeholder: 'Header'},
            width: { type: 'select', options: { 'col-md-12': 'full', 'col-md-6': 'half', 'col-md-4': 'third', 'col-md-3': 'quarter'} },
            section:      { type: 'text', placeholder: 'Section' },
            CSR_Field: { type: 'checkbox' }
          },
          linebreak: {
            label: { type: 'text', placeholder: '<br>'},
            section:      { type: 'text', placeholder: 'Section' },
            CSR_Field: { type: 'checkbox' }
          },
          freetext: {
            label: { type: 'textarea', placeholder: 'freetext', },
            width: { type: 'select', options: { 'col-md-12': 'full', 'col-md-6': 'half', 'col-md-4': 'third', 'col-md-3': 'quarter'} },
            section:      { type: 'text', placeholder: 'Section' },
            CSR_Field: { type: 'checkbox' }
          },
          fileprompt: {
            label: { type: 'textarea', placeholder: 'freetext', value: 'You can upload file(s) at the end of this form, after saving.' },
            width: { type: 'select', options: { 'col-md-12': 'full', 'col-md-6': 'half', 'col-md-4': 'third', 'col-md-3': 'quarter'} },
            section: { type: 'text', placeholder: 'Section' },
            CSR_Field: { type: 'checkbox' }
          },
          file: {
            label:        { type: 'text', placeholder: 'Label', validate: true },
            name:         { type: 'name', placeholder: 'Name' },
            Class: {
              type: 'text', 
              value: ( 'class' in options ? options.Class : 'form-control disabled'),
              placeholder: 'Class'
            },
            Required: { type: 'text' },
            section:      { type: 'text', placeholder: 'Section' },
            CSR_Field: { type: 'checkbox' }
          },
          sum: {
            label:        { type: 'text', placeholder: 'Label', validate: true },
            name:         { type: 'name', placeholder: 'Name' },
            placeholder:  { type: 'text', placeholder: 'Placeholder' },
            Class: {
              type: 'text', 
              value: ( 'class' in options ? options.Class : 'form-control disabled'),
              placeholder: 'Class'
            },
            Required: { type: 'checkbox' },
            sum: { type: 'checkboxSum' },
            sumOn: { type: 'sum' },
            adminOnly: { type: 'checkbox' },
            width: { type: 'select', options: { 'col-md-12': 'full', 'col-md-6': 'half', 'col-md-4': 'third', 'col-md-3': 'quarter'} },
            conditionalText: { type: 'checkboxConditionalText' },
            conditionalTextOn: { type: 'conditionalText' },
            section:      { type: 'text', placeholder: 'Section' },
            CSR_Field: { type: 'checkbox' }
          },
          percentage_calculator: {
            label:        { type: 'text', placeholder: 'Label', validate: true },
            name:         { type: 'name', placeholder: 'Name' },
            percentValue:      { type: 'text', placeholder: 'percent eg: [50]', validate: true },
            placeholder:  { type: 'text', placeholder: 'Placeholder' },
            Class: {
              type: 'text', 
              value: ( 'class' in options ? options.Class : 'form-control disabled'),
              placeholder: 'Class'
            },
            Required: { type: 'checkbox' },
            percentage: { type: 'checkboxPercentage' },
            percentageOn: { type: 'percentage' },
            adminOnly: { type: 'checkbox' },
            width: { type: 'select', options: { 'col-md-12': 'full', 'col-md-6': 'half', 'col-md-4': 'third', 'col-md-3': 'quarter'} },
            conditionalText: { type: 'checkboxConditionalText' },
            conditionalTextOn: { type: 'conditionalText' },
            section:      { type: 'text', placeholder: 'Section' },
            CSR_Field: { type: 'checkbox' }
          },
          divide: {
            label:        { type: 'text', placeholder: 'Label', validate: true },
            name:         { type: 'name', placeholder: 'Name' },
            divideValue:      { type: 'text', placeholder: 'divide by eg: [5]', validate: true },
            placeholder:  { type: 'text', placeholder: 'Placeholder' },
            Class: {
              type: 'text', 
              value: ( 'class' in options ? options.Class : 'form-control disabled'),
              placeholder: 'Class'
            },
            Required: { type: 'checkbox' },
            divide: { type: 'checkboxDivide' },
            divideOn: { type: 'divide' },
            adminOnly: { type: 'checkbox' },
            width: { type: 'select', options: { 'col-md-12': 'full', 'col-md-6': 'half', 'col-md-4': 'third', 'col-md-3': 'quarter'} },
            conditionalText: { type: 'checkboxConditionalText' },
            conditionalTextOn: { type: 'conditionalText' },
            section:      { type: 'text', placeholder: 'Section' },
            CSR_Field: { type: 'checkbox' }
          },
          multiply: {
            label:        { type: 'text', placeholder: 'Label', validate: true },
            name:         { type: 'name', placeholder: 'Name' },
            placeholder:  { type: 'text', placeholder: 'Placeholder' },
            Class: {
              type: 'text', 
              value: ( 'class' in options ? options.Class : 'form-control disabled'),
              placeholder: 'Class'
            },
            Required: { type: 'checkbox' },
            multiply: { type: 'checkboxMultiply' },
            multiplyOn: { type: 'multiply' },
            adminOnly: { type: 'checkbox' },
            width: { type: 'select', options: { 'col-md-12': 'full', 'col-md-6': 'half', 'col-md-4': 'third', 'col-md-3': 'quarter'} },
            conditionalText: { type: 'checkboxConditionalText' },
            conditionalTextOn: { type: 'conditionalText' },
            section:      { type: 'text', placeholder: 'Section' },
            CSR_Field: { type: 'checkbox' },
            PML_Property: { type: 'checkbox' },
            PML_Business_Interruption: { type: 'checkbox' }
          },
          subtract: {
            label:        { type: 'text', placeholder: 'Label', validate: true },
            name:         { type: 'name', placeholder: 'Name' },
            placeholder:  { type: 'text', placeholder: 'Placeholder' },
            Class: {
              type: 'text', 
              value: ( 'class' in options ? options.Class : 'form-control disabled'),
              placeholder: 'Class'
            },
            Required: { type: 'checkbox' },
            subtract: { type: 'checkboxSubtract' },
            subtractOn: { type: 'subtract' },
            adminOnly: { type: 'checkbox' },
            width: { type: 'select', options: { 'col-md-12': 'full', 'col-md-6': 'half', 'col-md-4': 'third', 'col-md-3': 'quarter'} },
            conditionalText: { type: 'checkboxConditionalText' },
            conditionalTextOn: { type: 'conditionalText' },
            section:      { type: 'text', placeholder: 'Section' },
            CSR_Field: { type: 'checkbox' }
          },
          average: {
            label:        { type: 'text', placeholder: 'Label', validate: true },
            name:         { type: 'name', placeholder: 'Name' },
            placeholder:  { type: 'text', placeholder: 'Placeholder' },
            Class: {
              type: 'text', 
              value: ( 'class' in options ? options.Class : 'form-control'),
              placeholder: 'Class'
            },
            Required: { type: 'checkbox' },
            average: { type: 'checkboxAverage' },
            averageOn: { type: 'average' },
            adminOnly: { type: 'checkbox' },
            width: { type: 'select', options: { 'col-md-12': 'full', 'col-md-6': 'half', 'col-md-4': 'third', 'col-md-3': 'quarter'} },
            type: { type: 'select', options: { 'mean': 'mean', 'mode': 'mode', 'median': 'median'} },
            conditionalText: { type: 'checkboxConditionalText' },
            conditionalTextOn: { type: 'conditionalText' },
            section:      { type: 'text', placeholder: 'Section' },
            CSR_Field: { type: 'checkbox' }
          },
          number: {
            label:        { type: 'text', placeholder: 'Label', validate: true },
            name:         { type: 'name', placeholder: 'Name' },
            placeholder:  { type: 'text', placeholder: 'Placeholder' },
            Class: {
              type: 'text', 
              value: ( 'class' in options ? options.Class : 'form-control'),
              placeholder: 'Class'
            },
            Required: { type: 'checkbox' },
            Condition: { type: 'checkboxDepends' },
            dependsOn: { type: 'depends' },
            adminOnly: { type: 'checkbox' },
            width: { type: 'select', options: { 'col-md-12': 'full', 'col-md-6': 'half', 'col-md-4': 'third', 'col-md-3': 'quarter'} },
            section:      { type: 'text', placeholder: 'Section' },
            CSR_Field: { type: 'checkbox' },
            PML_Property: { type: 'checkbox' },
            PML_Business_Interruption: { type: 'checkbox' }
          },
          country: {
            label:        { type: 'text', placeholder: 'Label', validate: true },
            name:         { type: 'name', placeholder: 'Name' },
            options:      { type: 'hidden', value: "United Kingdom, United States, Afghanistan, Albania, Algeria, Andorra, Angola, Antigua & Deps, Argentina, Armenia, Australia, Austria, Azerbaijan, Bahamas, Bahrain, Bangladesh, Barbados, Belarus, Belgium, Belize, Benin, Bhutan, Bolivia, Bosnia Herzegovina, Botswana, Brazil, Brunei, Bulgaria, Burkina, Burma, Burundi, Cambodia, Cameroon, Canada, Cape Verde, Central African Rep, Chad, Chile, People's Republic of China, Republic of China, Colombia, Comoros, Democratic Republic of the Congo, Republic of the Congo, Costa Rica,, Croatia, Cuba, Cyprus, Czech Republic, Danzig, Denmark, Djibouti, Dominica, Dominican Republic, East Timor, Ecuador, Egypt, El Salvador, Equatorial Guinea, Eritrea, Estonia, Ethiopia, Fiji, Finland, France, Gabon, Gaza Strip, The Gambia, Georgia, Germany, Ghana, Greece, Grenada, Guatemala, Guinea, Guinea-Bissau, Guyana, Haiti, Holy Roman Empire, Honduras, Hungary, Iceland, India, Indonesia, Iran, Iraq, Republic of Ireland, Israel, Italy, Ivory Coast, Jamaica, Japan, Jonathanland, Jordan, Kazakhstan, Kenya, Kiribati, North Korea, South Korea, Kosovo, Kuwait, Kyrgyzstan, Laos, Latvia, Lebanon, Lesotho, Liberia, Libya, Liechtenstein, Lithuania, Luxembourg, Macedonia, Madagascar, Malawi, Malaysia, Maldives, Mali, Malta, Marshall Islands, Mauritania, Mauritius, Mexico, Micronesia, Moldova, Monaco, Mongolia, Montenegro, Morocco, Mount Athos, Mozambique, Namibia, Nauru, Nepal, Newfoundland, Netherlands, New Zealand, Nicaragua, Niger, Nigeria, Norway, Oman, Ottoman Empire, Pakistan, Palau, Panama, Papua New Guinea, Paraguay, Peru, Philippines, Poland, Portugal, Prussia, Qatar, Romania, Rome, Russian Federation, Rwanda, St Kitts & Nevis, St Lucia, Saint Vincent & the, Grenadines, Samoa, San Marino, Sao Tome & Principe, Saudi Arabia, Senegal, Serbia, Seychelles, Sierra Leone, Singapore, Slovakia, Slovenia, Solomon Islands, Somalia, South Africa, Spain, Sri Lanka, Sudan, Suriname, Swaziland, Sweden, Switzerland, Syria, Tajikistan, Tanzania, Thailand, Togo, Tonga, Trinidad & Tobago, Tunisia, Turkey, Turkmenistan, Tuvalu, Uganda, Ukraine, United Arab Emirates, Uruguay, Uzbekistan, Vanuatu, Vatican City, Venezuela, Vietnam, Yemen, Zambia, Zimbabwe", validate: true },
            Class: {
              type: 'text', 
              value: ( 'class' in options ? options.Class : 'form-control'),
              placeholder: 'Class'
            },
            Required: { type: 'checkbox' },
            Condition: { type: 'checkboxDepends' },
            dependsOn: { type: 'depends' },
            Admin_Only: { type: 'checkbox' },
            Multi_Select: { type: 'checkbox' },
            width: { type: 'select', options: { 'col-md-12': 'full', 'col-md-6': 'half', 'col-md-4': 'third', 'col-md-3': 'quarter'} },
            section:      { type: 'text', placeholder: 'Section' },
            CSR_Field: { type: 'checkbox' }
          },
          risk_recommendation_summary: {
            label:        { type: 'text', placeholder: 'Label', validate: true },
            name:         { type: 'name', placeholder: 'Name' },
            Class: {
              type: 'text', 
              value: ( 'class' in options ? options.Class : 'form-control'),
              placeholder: 'Class'
            },
            Required: { type: 'checkbox' },
            Condition: { type: 'checkboxDepends' },
            dependsOn: { type: 'depends' },
            Admin_Only: { type: 'checkbox' },
            width: { type: 'select', options: { 'col-md-12': 'full', 'col-md-6': 'half', 'col-md-4': 'third', 'col-md-3': 'quarter'} },
            section:      { type: 'text', placeholder: 'Section' },
            prefix:       { type: 'text', placeholder: 'prefix' }
          },
          risk_recommendation: {
            label:        { type: 'text', placeholder: 'Label', validate: true },
            name:         { type: 'name', placeholder: 'Name' },
            Class: {
              type: 'text', 
              value: ( 'class' in options ? options.Class : 'form-control'),
              placeholder: 'Class'
            },
            Required: { type: 'checkbox' },
            Condition: { type: 'checkboxDepends' },
            dependsOn: { type: 'depends' },
            Admin_Only: { type: 'checkbox' },
            width: { type: 'select', options: { 'col-md-12': 'full', 'col-md-6': 'half', 'col-md-4': 'third', 'col-md-3': 'quarter'} },
            section:      { type: 'text', placeholder: 'Section' },
            prefix:       { type: 'text', placeholder: 'prefix' }
          },
          special_instructions: {
            label:        { type: 'text', placeholder: 'Label', validate: true },
            name:         { type: 'name', placeholder: 'Name'},
            placeholder:  { type: 'text', placeholder: 'Placeholder' },
            Class: {
              type: 'text',
              value: ( 'class' in options ? options.Class : 'form-control'),
              placeholder: 'Class'
            },
            Required: { type: 'checkbox' },
            Condition: { type: 'checkboxDepends' },
            dependsOn: { type: 'depends' },
            Admin_only: { type: 'checkbox' },
            width: { type: 'select', options: { 'col-md-12': 'full', 'col-md-6': 'half', 'col-md-4': 'third', 'col-md-3': 'quarter'} },
            section:      { type: 'text', placeholder: 'Section' },
            CSR_Field: { type: 'checkbox' }
          },
          re_review: {
            label:        { type: 'text', placeholder: 'Label', validate: true },
            name:         { type: 'name', placeholder: 'Name'},
            placeholder:  { type: 'text', placeholder: 'Placeholder' },
            Class: {
              type: 'text',
              value: ( 'class' in options ? options.Class : 'form-control'),
              placeholder: 'Class'
            },
            Required: { type: 'checkbox' },
            Condition: { type: 'checkboxDepends' },
            dependsOn: { type: 'depends' },
            Admin_only: { type: 'checkbox' },
            width: { type: 'select', options: { 'col-md-12': 'full', 'col-md-6': 'half', 'col-md-4': 'third', 'col-md-3': 'quarter'} },
            section:      { type: 'text', placeholder: 'Section' }
          },
          risk_reduce_access: {
            label:        { type: 'hidden', placeholder: 'Label', validate: true, value: 'Risk Reduce Access - Contact details' },
            name:         { type: 'name', placeholder: 'Name', value: 'risk-reduce-access' },
            Class: {
              type: 'text', 
              value: ( 'class' in options ? options.Class : 'form-control'),
              placeholder: 'Class'
            },
            Required: { type: 'checkbox' },
            Condition: { type: 'checkboxDepends' },
            dependsOn: { type: 'depends' },
            Admin_Only: { type: 'checkbox' },
            width: { type: 'select', options: { 'col-md-12': 'full', 'col-md-6': 'half', 'col-md-4': 'third', 'col-md-3': 'quarter'} },
            section:      { type: 'text', placeholder: 'Section' },
            prefix:       { type: 'text', placeholder: 'prefix' },
            CSR_Field: { type: 'checkbox' }
          },
        },
        Chartable_accident_reporting_fields: {
          date_of_accident: {
            label:        { type: 'hidden', placeholder: 'Label', validate: true, value: 'Date of accident' },
            name:         { type: 'name', placeholder: 'Name', value: 'date-of-accident' },
            placeholder:  { type: 'text', placeholder: 'Placeholder' },
            Class: {
              type: 'text', 
              value: ( 'class' in options ? options.Class : 'form-control'),
              placeholder: 'Class'
            },
            format: {
              type: 'text', 
              value: ( 'dd/mm/yy'),
              placeholder: 'dd/mm/yy',
              validate: true
            },
            Required: { type: 'checkbox' },
            Condition: { type: 'checkboxDepends' },
            dependsOn: { type: 'depends' },
            adminOnly: { type: 'checkbox' },
            width: { type: 'select', options: { 'col-md-12': 'full', 'col-md-6': 'half', 'col-md-4': 'third', 'col-md-3': 'quarter'} }
          },
          time_period_of_accident: {
            label:        { type: 'hidden', placeholder: 'Label', validate: true, value: 'Time Period of Accident' },
            name:         { type: 'name', placeholder: 'Name', value: 'time-period-of-accident' },
            options:      { type: 'textarea', placeholder: 'one, two, three', validate: true },
            Class: {
              type: 'text', 
              value: ( 'class' in options ? options.Class : 'form-control'),
              placeholder: 'Class'
            },
            Required: { type: 'checkbox' },
            Condition: { type: 'checkboxDepends' },
            dependsOn: { type: 'depends' },
            Admin_Only: { type: 'checkbox' },
            Multi_Select: { type: 'checkbox' },
            width: { type: 'select', options: { 'col-md-12': 'full', 'col-md-6': 'half', 'col-md-4': 'third', 'col-md-3': 'quarter'} }
          },
          where_did_the_accident_happen: {
            label:        { type: 'hidden', placeholder: 'Label', validate: true, value: 'Where did the accident happen?' },
            name:         { type: 'name', placeholder: 'Name', value: 'where-did-the-accident-happen' },
            options:      { type: 'textarea', placeholder: 'one, two, three', validate: true },
            Class: {
              type: 'text', 
              value: ( 'class' in options ? options.Class : 'form-control'),
              placeholder: 'Class'
            },
            Required: { type: 'checkbox' },
            Condition: { type: 'checkboxDepends' },
            dependsOn: { type: 'depends' },
            Admin_Only: { type: 'checkbox' },
            Multi_Select: { type: 'checkbox' },
            width: { type: 'select', options: { 'col-md-12': 'full', 'col-md-6': 'half', 'col-md-4': 'third', 'col-md-3': 'quarter'} }
          },
          branch: {
            label:        { type: 'hidden', placeholder: 'Label', validate: true, value: 'Branch where accident happened?' },
            name:         { type: 'name', placeholder: 'Name', value: 'branch' },
            options:      { type: 'hidden', placeholder: 'additional options, eg. one, two, three', validate: false },
            Class: {
              type: 'text', 
              value: ( 'class' in options ? options.Class : 'form-control'),
              placeholder: 'Class'
            },
            Required: { type: 'checkbox' },
            Condition: { type: 'checkboxDepends' },
            dependsOn: { type: 'depends' },
            Admin_Only: { type: 'checkbox' },
            Multi_Select: { type: 'checkbox' },
            width: { type: 'select', options: { 'col-md-12': 'full', 'col-md-6': 'half', 'col-md-4': 'third', 'col-md-3': 'quarter'} }
          },
          injured_person_type: {
            label:        { type: 'hidden', placeholder: 'Label', validate: true, value: 'Injured Person Type' },
            name:         { type: 'name', placeholder: 'Name', value: 'injured-person-type' },
            options:      { type: 'textarea', placeholder: 'one, two, three', validate: true },
            Class: {
              type: 'text', 
              value: ( 'class' in options ? options.Class : 'form-control'),
              placeholder: 'Class'
            },
            Required: { type: 'checkbox' },
            Condition: { type: 'checkboxDepends' },
            dependsOn: { type: 'depends' },
            Admin_Only: { type: 'checkbox' },
            Multi_Select: { type: 'checkbox' },
            width: { type: 'select', options: { 'col-md-12': 'full', 'col-md-6': 'half', 'col-md-4': 'third', 'col-md-3': 'quarter'} }
          },
          injured_person_name: {
            label:        { type: 'hidden', placeholder: 'Label', validate: true, value: 'Injured Person Name' },
            name:         { type: 'name', placeholder: 'Name' },
            placeholder:  { type: 'text', placeholder: 'Placeholder' },
            Class: {
              type: 'text', 
              value: ( 'class' in options ? options.Class : 'form-control'),
              placeholder: 'Class'
            },
            Required: { type: 'checkbox' },
            Condition: { type: 'checkboxDepends' },
            dependsOn: { type: 'depends' },
            Admin_only: { type: 'checkbox' },
            width: { type: 'select', options: { 'col-md-12': 'full', 'col-md-6': 'half', 'col-md-4': 'third', 'col-md-3': 'quarter'} }
          },
          severity_of_the_injury: {
            label:        { type: 'hidden', placeholder: 'Label', validate: true, value: 'Severity of the injury' },
            name:         { type: 'name', placeholder: 'Name', value: 'severity-of-the-injury' },
            options:      { type: 'textarea', placeholder: 'one, two, three', validate: true },
            Class: {
              type: 'text', 
              value: ( 'class' in options ? options.Class : 'form-control'),
              placeholder: 'Class'
            },
            Required: { type: 'checkbox' },
            Condition: { type: 'checkboxDepends' },
            dependsOn: { type: 'depends' },
            Admin_Only: { type: 'checkbox' },
            Multi_Select: { type: 'checkbox' },
            width: { type: 'select', options: { 'col-md-12': 'full', 'col-md-6': 'half', 'col-md-4': 'third', 'col-md-3': 'quarter'} }
          },
          accident_category: {
            label:        { type: 'hidden', placeholder: 'Label', validate: true, value: 'Accident Category' },
            name:         { type: 'name', placeholder: 'Name', value: 'accident-category' },
            options:      { type: 'textarea', placeholder: 'one, two, three', validate: true },
            Class: {
              type: 'text', 
              value: ( 'class' in options ? options.Class : 'form-control'),
              placeholder: 'Class'
            },
            Required: { type: 'checkbox' },
            Condition: { type: 'checkboxDepends' },
            dependsOn: { type: 'depends' },
            Admin_Only: { type: 'checkbox' },
            Multi_Select: { type: 'checkbox' },
            width: { type: 'select', options: { 'col-md-12': 'full', 'col-md-6': 'half', 'col-md-4': 'third', 'col-md-3': 'quarter'} }
          },
          action_taken: {
            label:        { type: 'hidden', placeholder: 'Label', validate: true, value: 'Action taken' },
            name:         { type: 'name', placeholder: 'Name', value: 'action-taken' },
            options:      { type: 'textarea', placeholder: 'one, two, three', validate: true },
            Class: {
              type: 'text', 
              value: ( 'class' in options ? options.Class : 'form-control'),
              placeholder: 'Class'
            },
            Required: { type: 'checkbox' },
            Condition: { type: 'checkboxDepends' },
            dependsOn: { type: 'depends' },
            Admin_Only: { type: 'checkbox' },
            Multi_Select: { type: 'checkbox' },
            width: { type: 'select', options: { 'col-md-12': 'full', 'col-md-6': 'half', 'col-md-4': 'third', 'col-md-3': 'quarter'} }
          },
          nature_of_injury: {
            label:        { type: 'hidden', placeholder: 'Label', validate: true, value: 'Nature of injury' },
            name:         { type: 'name', placeholder: 'Name', value: 'nature-of-injury' },
            options:      { type: 'textarea', placeholder: 'one, two, three', validate: true },
            Class: {
              type: 'text', 
              value: ( 'class' in options ? options.Class : 'form-control'),
              placeholder: 'Class'
            },
            Required: { type: 'checkbox' },
            Condition: { type: 'checkboxDepends' },
            dependsOn: { type: 'depends' },
            Admin_Only: { type: 'checkbox' },
            Multi_Select: { type: 'checkbox' },
            width: { type: 'select', options: { 'col-md-12': 'full', 'col-md-6': 'half', 'col-md-4': 'third', 'col-md-3': 'quarter'} }
          },
          area_details: {
            label:        { type: 'hidden', placeholder: 'Label', validate: true, value: 'Area details' },
            name:         { type: 'name', placeholder: 'Name', value: 'area-details' },
            options:      { type: 'textarea', placeholder: 'one, two, three', validate: true },
            Class: {
              type: 'text', 
              value: ( 'class' in options ? options.Class : 'form-control'),
              placeholder: 'Class'
            },
            Required: { type: 'checkbox' },
            Condition: { type: 'checkboxDepends' },
            dependsOn: { type: 'depends' },
            Admin_Only: { type: 'checkbox' },
            Multi_Select: { type: 'checkbox' },
            width: { type: 'select', options: { 'col-md-12': 'full', 'col-md-6': 'half', 'col-md-4': 'third', 'col-md-3': 'quarter'} }
          },
        }
      } ;
      
      // defaults
      settings = $.extend({
        Class:        'form-control',
        form:         '',
        admin:        false,
        dragIcon:     '+',
        closeIcon:    'X',
        minIcon:      '&ddarr;',
        maxIcon:      '&rrarr;',
        validate:     true,
        before : function( fieldsObj ) { return fieldsObj ; },
        complete : function( json ) {},
      }, options);
      
      // before callback
      fieldsObj = settings.before.call( this, fieldsObj );

      // are we only outputting?
      if( !$('#ffForm').length ) return ;

      // output field actions
      // output field config boxes 
      methods.html( settings, fieldsObj ) ;
      
      // hide config boxes
      $('#ffForm #ffFields').hide() ;
      
      // hide name fields - removed because of ie8
      //$('input[data-field-name=name]').attr('type', 'hidden') ;
      
      // which form are we using
      ffForm = typeof(settings.form) == 'object'
        ? settings.form
        : $('form').first() ;
      
      // add class to form
      ffForm.addClass('ffForm-target') ;
      
      //check to see if we have prepop content
      var prePopContent = ffForm.html().trim() ;
      if(prePopContent) {
        ffForm.html('') ; //empty
        methods.load( prePopContent ) ; //build
      }
      
      // add generate button to form
      ffForm.append('<button id="ffGenerate" class="btn btn-blue">Save</button>') ;
      
      // sortable
      if (jQuery.ui) {
        ffForm.sortable({
          handle: "h2",
          items: "> div",
          receive: function( event, ui ) { 
            var pos = ui.item.index() ;
            var type = $('a', ui.item).data('field-type') ;
            $(ui.sender).sortable('cancel'); 
            methods.add(type, pos) ;
          }
        }).disableSelection();
        $('#ffForm ul').sortable({
          connectWith: ".ffForm-target"
        }).disableSelection();
      }
      
      // generate click handler
      $('#ffGenerate').click( function() { 

        // var selObj = document.getElementById('exclude_org_select'),
      //         txtTextObj = document.getElementById('exclude_ids'),
      //         org_selected = [];

      //         for(var i = 0, l = selObj.options.length; i < l; i++){
      //             //Check if the option is selected
      //             if(selObj.options[i].selected){
      //                 org_selected.push(selObj.options[i].value);
      //             }
      //         } 
      //         txtTextObj.value = org_selected.join(',');
      //         $("#exclude_org_select").remove();
              
        methods.generate( ffForm ) 
      }) ;
      
      // depends click handler
      $(document).on('click', 'input[data-field-type=checkboxDepends]', function(){
        $("#depends").remove() ;
        if( $(this).is(':checked') ) {
          // insert a form to get value
          var dependForm = methods.dependForm() ;
          $(this).closest('li').after(dependForm) ;
        } else {
          $('.depends', $(this).closest('div')).val('') ;
          $('label strong', $(this).closest('div')).remove() ;
        }
      }) ;
      $(document).on('click', '#dependApply', function(e){
        e.preventDefault() ;
        var el = $('#dependSelect').val() ;
        var val = $('#dependValue').val() ;
        $('.depends', $(this).closest('div')).val(el+':'+val) ;
        $('.checkboxDepends label span', $(this).closest('div')).after(' <strong>('+el+':'+val+')</strong>') ;
        $("#depends").remove() ;
      }) ;
      
      // sum click handler
      $(document).on('click', 'input[data-field-type=checkboxSum]', function(){
        $("#sum").remove() ;
        if( $(this).is(':checked') ) {
          // insert a form to get value
          var sumForm = methods.sumForm() ;
          $(this).closest('li').after(sumForm) ;
        } else {
          $('.sum', $(this).closest('div')).val('') ;
          $('label strong', $(this).closest('div')).remove() ;
        }
      }) ;
      $(document).on('click', '#sumApply', function(e){
        e.preventDefault() ;
        var el = $('#sumSelect').val() ;
        $('.sum', $(this).closest('div')).val(el) ;
        $('.checkboxSum label span', $(this).closest('div')).after(' <strong>('+el+')</strong>') ;
        $("#sum").remove() ;
      }) ;
      
      // conditional Text click handler
      $(document).on('click', 'input[data-field-type=checkboxConditionalText]', function(){
        $("#conditionalText").remove() ;
        if( $(this).is(':checked') ) {
          // insert a form to get value
          var conditionalTextForm = methods.conditionalTextForm() ;
          $(this).closest('li').after(conditionalTextForm) ;
        } else {
          $('.conditionalText', $(this).closest('div')).val('') ;
          $('label strong', $(this).closest('div')).remove() ;
        }
      }) ;
      $(document).on('click', '#conditionalTextApply', function(e){
        e.preventDefault() ;
        var el = [] ;
        $.each($('#conditionalText li'), function(i,v){
          var from = $('[name=from]', v).val() ;
          var to = $('[name=to]', v).val() ;
          var text = $('[name=text]', v).val() ;
          el.push(from+':'+to+':'+text) ;
        }) ;
        el = el.join();
        $('.conditionalText', $(this).closest('div')).val(el) ;
        $('.checkboxConditionalText label span', $(this).closest('div')).after(' <strong>('+el+')</strong>') ;
        $("#conditionalText").remove() ;
      }) ;
      $(document).on('click', '#conditionalText .add', function(e){
        e.preventDefault() ;
        $(this).parent().after( methods.conditionalTextFormRow() ) ;
        $(this).hide() ;
      }) ;
      $(document).on('click', '#conditionalText .rm', function(e){
        e.preventDefault() ;
        $('.add', $(this).parent().prev()).show() ;
        $(this).parent().remove() ;
      }) ;

      
      // generate click handler
      $('#ffGenerate').click( function() { 
        methods.generate( ffForm ) 
      }) ;
      
      // change checkbox value on select
      $(document).on('click', '.ffForm-target input[type=checkbox]', function(e) { 
        $(this).val( $(this).is(':checked') ? 1: 0 ) ;
      }) ;

      
      // delete field handler
      $(document).on('click', '.delField', function(e) { 
        e.preventDefault() ;
        methods.deleter( $(this) ) ;
      }) ;
      
      // dynamic name handler
      $(document).on('input', '[data-field-name="label"]', function(e) { 
        e.preventDefault() ;
        methods.title( $(this) ) ;
      }) ;
      
      // slideToggle
      $(document).on('click', '.minField', function(e) { 
        e.preventDefault() ;
        methods.minMax( $(this) )
      }) ;
      
      // every field is an action
      return this.each(function(){
        $('a', this).click( function(e) {
          e.preventDefault() ;
          methods.add( $(this) )
        });
      }) ;
      
    },
    dependForm: function() {
      
      // list of all fields and a text box for value
      var html = '<p id="depends"><select id="dependSelect" class="form-control">' ;
      var fields = $('ul', ffForm) ;
      $.each(fields, function( index, value){
        var name = $('input[data-field-name="name"]', value).val() ;
        var label = $('input[data-field-name="label"]', value).val() ;
        html += '<option value="'+name+'">'+label+'</option>' ;
      });           
      html += '</select><br><input type="text" class="form-control" id="dependValue" placeholder="enter a trigger value"><br><a id="dependApply" href="#" class="btn btn-success">Apply</a></p>' ;
      return html ;
      
    },
    sumForm: function() {
      
      // list of all fields and a text box for value
      var html = '<p id="sum"><select id="sumSelect" multiple>' ;
      var fields = $('ul', ffForm) ;
      $.each(fields, function( index, value){
        
        var name = $('input[data-field-name="name"]', value).val() ;
        var label = $('input[data-field-name="label"]', value).val() ;
        var type = $(this).data('field-type') ;
        if(type=='number' || type=='select' || type=='sum' || type=='percentage_calculator' || type=='multiply' || type=='divide' || type=='subtract')
          html += '<option value="'+name+'">'+label+'</option>' ;
      });           
      html += '</select><a id="sumApply" class="text-primary" href="#">Apply</a></p>' ;
      return html ;
      
    },
    conditionalTextForm: function() {
      
      var html = '<ul id="conditionalText">' ;
      html += methods.conditionalTextFormRow(true) ;
      html += '<a id="conditionalTextApply" href="#">Apply</a></ul>' ;
      return html ;
      
    },
    conditionalTextFormRow: function(first) {
      
      var html = '<li class="condition">' ;
      html += '<input type="text" name="from" placeholder="From (e.g 13)">' ;
      html += '<input type="text" name="to" placeholder="To (e.g 15)">' ;
      html += '<input type="text" name="text" placeholder="text value">' ;
      if(first!==true) html += '<a href="#" class="rm">-</a>' ;
      html += '<a href="#" class="add">+</a></li>' ;
      return html ;
      
    },
    add: function( el, pos ) {

      // if(el.data) {
      //   alert(el.data('field-type'));
        
        
      // }

      if(el.data) {
        var name_of_field = el.data('field-type').substr(2);
        if(name_of_field.length >= 2 && name_of_field.substr(0, 3) == 'sum' && $('#liberty-form').find('ul[data-field-type=number]').length <= 1) {
          // alert('Please add two or more number fields to add the SUM field to this form.');
          // return;
        }
      }

      

      if($('#liberty-form').find('ul[data-field-type='+name_of_field+']').length != 0 && $('#ui-id-6').find('a[data-field-type=ff'+name_of_field+']').length != 0) {
        alert('Chartable accident reporting fields can only be added once in a form.');
        return;
      }
      
      var field = typeof el == 'string' ? el : el.data('field-type') ;
      //var unq = Date.now() ;
      $.each($('#ffFields #'+field+' input, #ffFields #'+field+' select'), function(k,v) {
        $(this).attr('name', $(this).attr('name')) ; 
        //alert($(this));
      }) ;
      if(!pos){
        if(el.data !== undefined) {
          //console.log(el.data);
          $('<div>'+$('#ffFields #'+field).html()+'</div>').insertBefore('#ffGenerate');
        } else {
          //console.log($('#ffFields #'+field).html());
          ffForm.prepend( '<div>'+$('#ffFields #'+field).html()+'</div>' ) ;
        }
        // var inserted = $('div:eq(0) [data-field-name="label"]', ffForm) ;
        var inserted = $('div:last [data-field-name="label"]', ffForm) ;
        
      } else {
        // get div with index and insert after
        if( $('div:eq('+pos+')', ffForm).length ) {
          $( '<div>'+$('#ffFields #'+field).html()+'</div>' ).insertBefore( $('div:eq('+pos+')', ffForm) );
          // var inserted = $('div:eq('+pos+') [data-field-name="label"]', ffForm) ;
          var inserted = $('div:last [data-field-name="label"]', ffForm) ;
        } else {
          $('<div>'+$('#ffFields #'+field).html()+'</div>').insertBefore( $('button',ffForm) ) ;
          var inserted = $('div:last [data-field-name="label"]', ffForm) ;
        }
      }
      
      // already have a name?
      methods.title( inserted ) ;
      
      // placeholders not supported?
      var _test = document.createElement('input');
      if( ! ('placeholder' in _test) ){
        $('*[placeholder]').each(function () {
          var $self = $(this);
          //First check to make sure the value is nothing
          if ( $self.val() === '' ) {
            $self.val($self.attr('placeholder'));
          }
        });
      }
    },
    load: function( jsonString ) {
      
      var formElements = JSON.parse(jsonString);
      $.each( formElements.reverse(), function( index, value )  {
        $.each( value, function( field , fields) {
          //console.log('ff'+field);
          methods.add( 'ff'+field );
          
          if(fields[0])
            $('div:eq(0) h2 strong', ffForm).html( fields[0].value ) ;
          
          // get each type and change name and value
          $.each( fields, function( i , v) {
            var input = $('div:eq(0)', ffForm) ;
            if(v.name == 'section') {
              input.removeAttr('class');
              input.addClass(sectionPrefix+v.value.split(/[ ,\/]+/).join('_'));
            }
            
            $('li:eq('+i+') input, li:eq('+i+') textarea, li:eq('+i+') select', input)
              .attr('name', v.name)
              .val(v.value) ;

            // checkbox
            if(v.value==1) {
              $('li:eq('+i+') input[type=checkbox]', input).prop('checked', true) ;     
            }
            
            // depends on label
            if(v.name.indexOf("dependsOn") > -1 && v.value) {
              $('li:eq('+(i-1)+') label span', input).after(' <strong>('+v.value+')</strong>') ;
            }
            
            // sum label
            if(v.name.indexOf("sum") > -1 && v.value) {
              $('li:eq('+(i-1)+') label span', input).after(' <strong>('+v.value+')</strong>') ;
            }

            // percentage label
            if(v.name.indexOf("percentage") > -1 && v.value) {
              $('li:eq('+(i-1)+') label span', input).after(' <strong>('+v.value+')</strong>') ;
            }

            // divide label
            if(v.name.indexOf("divide") > -1 && v.value) {
              $('li:eq('+(i-1)+') label span', input).after(' <strong>('+v.value+')</strong>') ;
            }

            // Multiply label
            if(v.name.indexOf("multiply") > -1 && v.value) {
              $('li:eq('+(i-1)+') label span', input).after(' <strong>('+v.value+')</strong>') ;
            }

            // Subtract label
            if(v.name.indexOf("subtract") > -1 && v.value) {
              $('li:eq('+(i-1)+') label span', input).after(' <strong>('+v.value+')</strong>') ;
            }
            
            // average label
            if(v.name.indexOf("average") > -1 && v.value) {
              $('li:eq('+(i-1)+') label span', input).after(' <strong>('+v.value+')</strong>') ;
            }
            
            // conditional text label
            if(v.name.indexOf("conditional") > -1 && v.value) {
              $('li:eq('+(i-1)+') label span', input).after(' <strong>('+v.value+')</strong>') ;
            }
            
          }) ;
          
        }) ;
      }) ;
      
    },
    deleter: function ( el ) {
      
      el.closest('div').remove() ;
      
    },
    title: function ( el ) {
      
      var val = el.val() ;
      //el.closest('li').parent().find('li [name="section"]').val($("#section_header_list").val());
      if($("#section_header_list").val() != '' && !el.closest('div').hasClass()) {
        var section_field = el.closest('div:visible').find('li [name="section"]');
        //el.closest('div:visible').css('background', '#900');
   
        
        if(section_field.val() == '') {
          el.closest('div:visible').addClass(sectionPrefix+$("#section_header_list").val().split(/[ ,\/]+/).join('_'));
          section_field.val($("#section_header_list").val());
        } else {
                    el.closest('div:visible').addClass(sectionPrefix+section_field.val().split(/[ ,\/]+/).join('_'));
          //section_field.val($("#section_header_list").val());
        }
      }
      if( el.closest('div').find('h2 strong').length ){
        el.closest('div').find('h2 strong').html( val == '' ? '' : val ) ;
        if(val == 'Date of accident' || val == 'Time Period of Accident' || val == 'Where did the accident happen?' || val == 'Branch where accident happened?' || val == 'Injured Person Type' || val == 'Severity of the injury' || val == 'Accident Category' || val == 'Action taken' || val == 'Nature of injury' || val == 'Area details' || val == 'Injured Person Name') {
          el.parent().next().find('input').val( val.replace(/ +/g, '-').replace(/[^a-z0-9\-]/gmi, "").toLowerCase() ) ;
        } else {
          el.parent().next().find('input').val( val.replace(/ +/g, '-').replace(/[^a-z0-9\-]/gmi, "").toLowerCase()+'-'+Math.floor(Date.now() / 1000) ) ;
        }
      }
      
    },
    minMax: function ( el ) {
      
      el.parent().next('ul').slideToggle('fast', function(){
        if( $(this).is(':visible') ) {
          $(el).html(settings.minIcon) ;
        } else {
          $(el).html(settings.maxIcon) ;
        }
      }) ;
      
    },
    generate: function( form ) {
      


      // validate and generate
      $.validator.setDefaults({
        invalidHandler: function(event, validator) {
          $('div', form).removeClass('error')
          $.each(validator.errorList, function(k, v){
            $('input[name='+v.element.name+']').closest('div').addClass('error');
              $('#liberty-form div.error').find('ul').show();
              if(v.element.name == 'label') {
                $('div.tabs').tabs('option','active',0);
              }
          }) ;
          $("html, body").animate({ scrollTop: 0 }, "slow");
        },
        submitHandler: function() {
          // form is valid remove errors
          $('div', form).removeClass('error')
          
          // generate json from form content - this needs cleaned up
          var fields = $('ul', form) ;
          var fieldArr = [];

          $.each(fields, function( index, value){
            //var field = $('input[data-field-name=name]', value).attr('name') ;
            var field = $(value).data('field-type') ;
            var obj = [];
            var f = {} ;
            $.each( $('li', value), function( i, v) {
              obj.push({
                name: $('input, textarea, select', $(v)).attr('name'),
                value: $('input, textarea, select', $(v)).val()
              }) ;
            }) ;
            f[field] = obj ;
            fieldArr.push(f);
          });           
          
          // callback
          settings.complete.call( this, JSON.stringify(fieldArr) );

          
        },
        errorPlacement: function(error, element) {
          // show which boxes have errors
          error.insertAfter(element);
        }
      });
      //$('div', form).removeClass('error') ;
      form.validate({ ignore: "" });
      
    },
    html: function( settings, fieldsObj ) {

      // build fields
      var html = '<div class="ffaccordion">' ;
      $.each( fieldsObj, function( index, value ) {
        html += '<h3>'+index.replace(/_/gi, ' ')+'</h3><div><ul>' ;
        $.each( Object.keys(value), function( i, v )  {
          html += '<li><a href="#" data-field-type="ff'+v+'">'+v.toUpperCase().replace('_', ' ')+'</a></li>' ;
        }) ;
        html += '</ul></div>' ;
      }) ;
      html += '</div>' ;
      
      // build field config boxes
      html += '<ul id="ffFields">' ;
      $.each( fieldsObj, function( i, v ) {
        $.each( Object.keys(v), function( index, value )  {
          html += '<li id="ff'+value+'"><h2 class="d-flex justify-content-between"><div class="d-flex"><span class="handle">'+settings.dragIcon+'</span><p><strong></strong> ('+value+')</p></div><a href="#" class="minField">'+settings.maxIcon+'</a> <a href="#" class="delField">'+settings.closeIcon+'</a></h2><ul data-field-type="'+value+'">' ;
          $.each(fieldsObj[i][value], function( name, field ) {
            html += '<li>' ;
            html += methods[field.type](name, field) ;
            html += '</li>' ;
          }) ;
          html += '</ul></li>' ;
        }) ;
      }) ;
      html += '</ul>' ;

      // output 
      $('#ffForm').html( html ) ;
      // $( ".ffaccordion" ).accordion({
      //   collapsible: true,
      //   heightStyle: "content"
      // });
    },
    populate: function( json ) {
      
      var data = JSON.parse( json ) ;
      for (var index in data) {
          //if (data.hasOwnProperty(index)) {
              value = data[index]
          //}
      
      //$.each( data, function( index, value )  {
        
        //index = index.replace(/_pre__/g,"");
        //console.log(index);
        // multi select may need to expand for other 
        if( $('[name="'+index+'[]"]').length && value != '') {
          switch($('[name="'+index+'[]"]').prop('tagName')) {
            case 'SELECT': {
              $('[name="'+index+'[]"] option').each( function(i, v){
                var opt = $(this) ;
                $.each(value, function(x,y){
                  if( opt.val() == y) {
                    opt.attr('selected', 'selected') ;
                  }
                }) ;
              }) ;
              break ;
            }
          }
        }
        
        // single fields
        if( $('[name='+index+']').length && value != '') {
          switch($('[name='+index+']').prop('tagName')){
            case 'INPUT': {
              switch($('[name='+index+']').attr('type')){
                case 'text':
                case 'email':
                {
                  $('[name='+index+']').val(value) ;
                  break ;
                }
                case 'radio':
                {
                  $('[name='+index+']').each( function(i, v){
                    if( $(this).val() == value) {
                      $(this).prop('checked', true) ;
                    }
                  }) ;
                  break ;
                }
                case 'checkbox':
                {
                  if(value != 0)
                    $('[name='+index+']').prop('checked', true) ;
                  break ;
                }
              }
              break ;
            }
            case 'SELECT': {
              
              var objMapping = {
                'Contact U/W within 24 hours'  : 'Requires Improvement',
                'Multiple Requirements identified - monthly updates required'  : 'Below Average',
                'Single Requirement - monitor progress'  : 'Average',
                'Recommendations Only -generally reasonable controls'  : 'Good',
                'Satisfactory'  : 'Good',
                'Not Applicable'  : 'Not Applicable / Not Assessed'
              };

              if(typeof(objMapping[value]) !== "undefined"){
                 value = objMapping[value];
              }
              
              $('[name='+index+'] option').each( function(i, v){
                if( $(this).val() == value) {
                  $(this).prop('selected', true) ;
                }
              }) ;
              break ;
            }

            case 'TEXTAREA': {
              $('[name='+index+']').val(value.replace(/\[br\]/g, '\r\n')) ;
              break ;
            }

          }

        }
        
      }
      
      // hide any dependant fields
      $("[data-depends-on!='0'][data-depends-on!=''][data-depends-on]").each( function(i,v){
        if($(this).val() != '') {
          $(this).closest('p').show() ;
        }
        var y = $(this).attr('data-depends-on');
        var target = y.split(':') ;

        var val = $('[name='+target[0]+']').val() ;
        var ignore = false ;
        if($(this).is(':checkbox')){
          if( $(this).prop('checked') ) {
            val = target[1] ;
          } else {
            ignore = true ;
          }
        }
        if( target[1].indexOf('~') !== -1 && !ignore ) {
          if(val != target[1].replace("~", "")) {
            $(this).closest('p').show() ;
          } else {
            $(this).closest('p').hide() ;
          }
        } else if( (target[1].indexOf('>') !== -1 || target[1].indexOf('&gt;') !== -1) && !ignore ){
          if( parseFloat(val) > parseFloat(target[1].replace(">", "")) || parseFloat(val) > parseFloat(target[1].replace("&gt;", "")) ) {
            $(this).closest('p').show() ;
          } else {
            $(this).closest('p').hide() ;
          }
        } else if( (target[1].indexOf('<') !== -1 || target[1].indexOf('&lt;') !== -1) && !ignore ){
          if( parseFloat(val) < parseFloat(target[1].replace("<", "")) || parseFloat(val) < parseFloat(target[1].replace("&lt;", "")) ) {
            $(this).closest('p').show() ;
          } else {
            $(this).closest('p').hide() ;
          }
        } else if( val == target[1] && !ignore){
          //show field
          $(this).closest('p').show() ;
        } else {
          $(this).closest('p').hide() ;
        }
      
     
      }) ;
      // Date fields
      $(".datepicker").each( function(i,v){
        if($(this).val() != '') {}
          var timestamp = new Date($(this).val()*1000);
          $(this).val(timestamp.getDate()+'/'+parseInt(timestamp.getMonth()+1)+'/'+timestamp.getFullYear()) ;
      }) ;
    },
    cachedbuild: function( fields, form ) {
      
      // build from json
      fields = JSON.parse(fields);
      //form.html('') ;
      var rules = {} ;
      $.each( fields, function( index, value )  {
        $.each( value , function( i, v )  {
          
          // width
          // var colwidth = 'col-md-12' ;
          // $.each( v , function( x, y )  {
          //   if(y.name.indexOf("width") > -1 && y.value) {
          //     colwidth = y.value ;
          //   }
          // }) ;
          
          // var container = i =='header' ? 'div' : 'p' ;
          
          // var html = '<'+container+' class="'+colwidth+'">' ;
          var method = '__'+i ;
          // html += methods[method](v);
          // html += '</'+container+'>' ;
          // form.append( html ) ;

          // if a number lets make sure its validated
          if(method=='__number'){
            rules[v[1].value] = { required: true, number: true}
          }
          

          // if percentage event handler
          $.each( v , function( x, y )  {

            if( y.name.indexOf("percentage") > -1 && y.value) {
              var targets = y.value.split(',') ;
              // console.log(y.value);
              $.each( targets, function(i, t) {
                $(document).on('change', '[name='+t+']', function(){
                  var total = 0 ; 
                  $.each( targets, function(a, b) {
                    var val = $('[name='+b+']').val() ;
                    total = parseFloat(total) + parseFloat(val) ;  
                  }) ;
                  var percent = v[2].value;
                  total = total*(percent/100);
                  if( !isNaN(parseFloat(total)) && isFinite(total) ) {
                    $('[name='+v[1].value+']', form).val(total.toFixed(2)) ;
                    $('[name='+v[1].value+']', form).trigger('change');
                  }
                    
                  // check for conditional text
                  var conditionalText = $('[name='+v[1].value+']', form).data( 'text' ) ;
                  if(conditionalText){
                    conditionalText = conditionalText.split(',') ;
                    $.each(conditionalText, function(q,r){
                      var condition = r.split(':') ;
                      if(total >= condition[0] && total <= condition[1]){
                        $('[name='+v[1].value+']', form).val( condition[2] ) ;
                      }
                    }) ;
                  }
                  
                }) ;
              }) ;
            }

            if( y.name.indexOf("sum") > -1 && y.value) {
              
              var targets = y.value.split(',') ;
              //console.log(targets);
              $.each( targets, function(i, t) {
                $(document).on('change', '[name='+t+']', function(){
                  // console.log('sum');
                  // console.log(t);
                  var total = 0 ; 
                  $.each( targets, function(a, b) {
                    // console.log(b);
                    var val = $('[name='+b+']').val() ;
                    total = parseFloat(total) + parseFloat(val) ;  
                  }) ;
                  if( !isNaN(parseFloat(total)) && isFinite(total) ) {
                    $('[name='+v[1].value+']', form).val(total) ;
                    $('[name='+v[1].value+']', form).trigger('change');
                  }
                    
                  // check for conditional text
                  var conditionalText = $('[name='+v[1].value+']', form).data( 'text' ) ;
                  if(conditionalText){
                    conditionalText = conditionalText.split(',') ;
                    $.each(conditionalText, function(q,r){
                      var condition = r.split(':') ;
                      if(total >= condition[0] && total <= condition[1]){
                        $('[name='+v[1].value+']', form).val( condition[2] ) ;
                      }
                    }) ;
                  }
                  
                }) ;
              }) ;
            }

            if( y.name.indexOf("divide") > -1 && y.value) {
              var targets = y.value.split(',') ;
              //console.log(y.value);
              $.each( targets, function(i, t) {
                $(document).on('change', '[name='+t+']', function(){
                  var total = 0 ; 
                  $.each( targets, function(a, b) {
                    var val = $('[name='+b+']').val() ;
                    total = parseFloat(total) + parseFloat(val) ;  
                  }) ;
                  var divisor = v[2].value;
                  total = total/divisor;
                  if( !isNaN(parseFloat(total)) && isFinite(total) ) {
                    $('[name='+v[1].value+']', form).val(total.toFixed(2)) ;
                    $('[name='+v[1].value+']', form).trigger('change');
                  }
                    
                  // check for conditional text
                  var conditionalText = $('[name='+v[1].value+']', form).data( 'text' ) ;
                  if(conditionalText){
                    conditionalText = conditionalText.split(',') ;
                    $.each(conditionalText, function(q,r){
                      var condition = r.split(':') ;
                      if(total >= condition[0] && total <= condition[1]){
                        $('[name='+v[1].value+']', form).val( condition[2] ) ;
                      }
                    }) ;
                  }
                  
                }) ;
              }) ;
            }

            if( y.name.indexOf("multiply") > -1 && y.value) {
              var targets = y.value.split(',') ;
              //console.log(y.value);
              $.each( targets, function(i, t) {
                $(document).on('change', '[name='+t+']', function(){
                  var total = 1 ; 
                  $.each( targets, function(a, b) {
                    var val = $('[name='+b+']').val() ;
                    total = parseFloat(total) * parseFloat(val) ;  
                  }) ;

                  if( !isNaN(parseInt(total)) && isFinite(total) ) {
                    $('[name='+v[1].value+']', form).val(parseInt(total)) ;
                    $('[name='+v[1].value+']', form).trigger('change');
                  }
                    
                  // check for conditional text
                  var conditionalText = $('[name='+v[1].value+']', form).data( 'text' ) ;
                  if(conditionalText){
                    conditionalText = conditionalText.split(',') ;
                    $.each(conditionalText, function(q,r){
                      var condition = r.split(':') ;
                      if(total >= condition[0] && total <= condition[1]){
                        $('[name='+v[1].value+']', form).val( condition[2] ) ;
                      }
                    }) ;
                  }
                  
                }) ;
              }) ;
            }

            if( y.name.indexOf("subtract") > -1 && y.value) {
              var targets = y.value.split(',') ;

              $.each( targets, function(i, t) {
                $(document).on('change', '[name='+t+']', function(){
                  var total = 0 ; 
                  $.each( targets, function(a, b) {
                    var val = $('[name='+b+']').val() ;
                    if(total == 0) {
                      total = parseInt(val);
                    } else {
                      total = total - parseInt(val) ;  
                    }
                                  
                  }) ;

                  if( !isNaN(parseInt(total)) && isFinite(total) ) {
                    $('[name='+v[1].value+']', form).val(total) ;
                    $('[name='+v[1].value+']', form).trigger('change');
                  }
                    
                  // check for conditional text
                  var conditionalText = $('[name='+v[1].value+']', form).data( 'text' ) ;
                  if(conditionalText){
                    conditionalText = conditionalText.split(',') ;
                    $.each(conditionalText, function(q,r){
                      var condition = r.split(':') ;
                      if(total >= condition[0] && total <= condition[1]){
                        $('[name='+v[1].value+']', form).val( condition[2] ) ;
                      }
                    }) ;
                  }
                  
                }) ;
              }) ;
            }

            if( y.name.indexOf("average") > -1 && y.value) {
              //console.log(y.value);
              var targets = y.value.split(',') ;
              var action = $('[name='+v[1].value+']', form).data( 'calc' ) ;
              $.each( targets, function(i, t) {
                $(document).on('change', '[name='+t+']', function(){
                  var total = 0 ; 
                  var avArr = [] ;
                  $.each( targets, function(a, b) {
                    var val = $('[name='+b+']').val() ; 
                    avArr.push(parseFloat(val)) ;
                  }) ;
                  total = window[action](avArr) ;
                  if( !isNaN(parseFloat(total)) && isFinite(total) ) {
                    $('[name='+v[1].value+']', form).val( total ) ;
                    $('[name='+v[1].value+']', form).trigger('change');
                  }
                    
                  // check for conditional text
                  var conditionalText = $('[name='+v[1].value+']', form).data( 'text' ) ;
                  if(conditionalText){
                    conditionalText = conditionalText.split(',') ;
                    $.each(conditionalText, function(q,r){
                      var condition = r.split(':') ;
                      if(total >= condition[0] && total <= condition[1]){
                        $('[name='+v[1].value+']', form).val( condition[2] ) ;
                      }
                    }) ;
                  }
                }) ;
              }) ;
            }

            if( y.name.indexOf("dependsOn") > -1 && y.value) {
              var target = y.value.split(':') ;
              $(document).on('change keyup', '[name='+target[0]+']', function(){
                var val = $(this).val() ;
                var ignore = false ;
                if($(this).is(':checkbox')){
                  if( $(this).prop('checked') ) {
                    val = target[1] ;
                  } else {
                    ignore = true ;
                  }
                }
                if( target[1].indexOf('~') !== -1 && !ignore ) {
                  if(val != target[1].replace("~", "")) {
                    $('[name='+v[1].value+']', form).closest('p').show() ;
                  } else {
                    $('[name='+v[1].value+']', form).closest('p').hide() ;
                  }
                } else if( (target[1].indexOf('>') !== -1 || target[1].indexOf('&gt;') !== -1) && !ignore ){
                  if( parseFloat(val) > parseFloat(target[1].replace(">", "")) || parseFloat(val) > parseFloat(target[1].replace("&gt;", "")) ) {
                    $('[name='+v[1].value+']', form).closest('p').show() ;
                  } else {
                    $('[name='+v[1].value+']', form).closest('p').hide() ;
                  }
                } else if( (target[1].indexOf('<') !== -1 || target[1].indexOf('&lt;') !== -1) && !ignore ){
                  if( parseFloat(val) < parseFloat(target[1].replace("<", "")) || parseFloat(val) < parseFloat(target[1].replace("&lt;", "")) ) {
                    $('[name='+v[1].value+']', form).closest('p').show() ;
                  } else {
                    $('[name='+v[1].value+']', form).closest('p').hide() ;
                  }
                } else if( val == target[1] && !ignore){
                  //show field
                  $('[name='+v[1].value+']', form).closest('p').show() ;
                } else {
                  $('[name='+v[1].value+']', form).closest('p').hide() ;
                }
              }) ;
            }

          }) ;
          
        }) ;
      }) ;
      
      
      // validate the form once built
      // if(settings.validate == true) {
      //   if( form.get(0).tagName == 'FORM') {
      //     form.validate({
      //       rules: rules
      //     });
      //   } else {
      //     form.closest('form').validate({
      //       rules: rules
      //     });
      //   }
      // }
      
      // datepicker fields
      $( ".datepicker" ).each( function() {
        var format = $(this).data('date-format') ;
        $(this).datepicker({
          changeMonth: true,
          changeYear: true,
          dateFormat: 'dd/mm/yy',
          onSelect: function( dateText, inst ) {
            var name = $(this).attr('name') ;
            dateText=dateText.split("/");
            var timestamp = new Date(dateText[1]+"/"+dateText[0]+"/"+dateText[2]).getTime();
            $('[name='+name+'].alt').val( timestamp / 1000 );
          }
        });
      }) ;
      
      // hide any dependant fields
      $("[data-depends-on!='0'][data-depends-on!=''][data-depends-on]", form).closest('p').hide() ;
      
    },
    build: function( fields, form ) {
      
      // build from json
      fields = JSON.parse(fields);
      //form.html('') ;
      var rules = {} ;
      $.each( fields, function( index, value )  {
        $.each( value , function( i, v )  {
          
          // width
          var colwidth = 'col-md-12' ;
          $.each( v , function( x, y )  {
            if(y.name.indexOf("width") > -1 && y.value) {
              colwidth = y.value ;
            }
          }) ;
          
          var container = i =='header' ? 'div' : 'p' ;
          
          var html = '<'+container+' class="'+colwidth+'">' ;
          var method = '__'+i ;
          html += methods[method](v);
          html += '</'+container+'>' ;
          form.append( html ) ;

          // if a number lets make sure its validated
          if(method=='__number'){
            rules[v[1].value] = { required: true, number: true}
          }
          

          // if percentage event handler
          $.each( v , function( x, y )  {

            if( y.name.indexOf("percentage") > -1 && y.value) {
              var targets = y.value.split(',') ;
              // console.log(y.value);
              $.each( targets, function(i, t) {
                $(document).on('change', '[name='+t+']', function(){
                  var total = 0 ; 
                  $.each( targets, function(a, b) {
                    var val = $('[name='+b+']').val() ;
                    total = parseFloat(total) + parseFloat(val) ;  
                  }) ;
                  var percent = v[2].value;
                  total = total*(percent/100);
                  if( !isNaN(parseFloat(total)) && isFinite(total) ) {
                    $('[name='+v[1].value+']', form).val(total.toFixed(2)) ;
                    $('[name='+v[1].value+']', form).trigger('change');
                  }
                    
                  // check for conditional text
                  var conditionalText = $('[name='+v[1].value+']', form).data( 'text' ) ;
                  if(conditionalText){
                    conditionalText = conditionalText.split(',') ;
                    $.each(conditionalText, function(q,r){
                      var condition = r.split(':') ;
                      if(total >= condition[0] && total <= condition[1]){
                        $('[name='+v[1].value+']', form).val( condition[2] ) ;
                      }
                    }) ;
                  }
                  
                }) ;
              }) ;
            }

            if( y.name.indexOf("sum") > -1 && y.value) {
              
              var targets = y.value.split(',') ;
              //console.log(targets);
              $.each( targets, function(i, t) {
                $(document).on('change', '[name='+t+']', function(){
                  // console.log('sum');
                  // console.log(t);
                  var total = 0 ; 
                  $.each( targets, function(a, b) {
                    // console.log(b);
                    var val = $('[name='+b+']').val() ;
                    total = parseFloat(total) + parseFloat(val) ;  
                  }) ;
                  if( !isNaN(parseFloat(total)) && isFinite(total) ) {
                    $('[name='+v[1].value+']', form).val(total) ;
                    $('[name='+v[1].value+']', form).trigger('change');
                  }
                    
                  // check for conditional text
                  var conditionalText = $('[name='+v[1].value+']', form).data( 'text' ) ;
                  if(conditionalText){
                    conditionalText = conditionalText.split(',') ;
                    $.each(conditionalText, function(q,r){
                      var condition = r.split(':') ;
                      if(total >= condition[0] && total <= condition[1]){
                        $('[name='+v[1].value+']', form).val( condition[2] ) ;
                      }
                    }) ;
                  }
                  
                }) ;
              }) ;
            }

            if( y.name.indexOf("divide") > -1 && y.value) {
              var targets = y.value.split(',') ;
              //console.log(y.value);
              $.each( targets, function(i, t) {
                $(document).on('change', '[name='+t+']', function(){
                  var total = 0 ; 
                  $.each( targets, function(a, b) {
                    var val = $('[name='+b+']').val() ;
                    total = parseFloat(total) + parseFloat(val) ;  
                  }) ;
                  var divisor = v[2].value;
                  total = total/divisor;
                  if( !isNaN(parseFloat(total)) && isFinite(total) ) {
                    $('[name='+v[1].value+']', form).val(total.toFixed(2)) ;
                    $('[name='+v[1].value+']', form).trigger('change');
                  }
                    
                  // check for conditional text
                  var conditionalText = $('[name='+v[1].value+']', form).data( 'text' ) ;
                  if(conditionalText){
                    conditionalText = conditionalText.split(',') ;
                    $.each(conditionalText, function(q,r){
                      var condition = r.split(':') ;
                      if(total >= condition[0] && total <= condition[1]){
                        $('[name='+v[1].value+']', form).val( condition[2] ) ;
                      }
                    }) ;
                  }
                  
                }) ;
              }) ;
            }

            if( y.name.indexOf("multiply") > -1 && y.value) {
              var targets = y.value.split(',') ;
              //console.log(y.value);
              $.each( targets, function(i, t) {
                $(document).on('change', '[name='+t+']', function(){
                  var total = 1 ; 
                  $.each( targets, function(a, b) {
                    var val = $('[name='+b+']').val() ;
                    total = parseFloat(total) * parseFloat(val) ;  
                  }) ;

                  if( !isNaN(parseInt(total)) && isFinite(total) ) {
                    $('[name='+v[1].value+']', form).val(parseInt(total)) ;
                    $('[name='+v[1].value+']', form).trigger('change');
                  }
                    
                  // check for conditional text
                  var conditionalText = $('[name='+v[1].value+']', form).data( 'text' ) ;
                  if(conditionalText){
                    conditionalText = conditionalText.split(',') ;
                    $.each(conditionalText, function(q,r){
                      var condition = r.split(':') ;
                      if(total >= condition[0] && total <= condition[1]){
                        $('[name='+v[1].value+']', form).val( condition[2] ) ;
                      }
                    }) ;
                  }
                  
                }) ;
              }) ;
            }

            if( y.name.indexOf("subtract") > -1 && y.value) {
              var targets = y.value.split(',') ;

              $.each( targets, function(i, t) {
                $(document).on('change', '[name='+t+']', function(){
                  var total = 0 ; 
                  $.each( targets, function(a, b) {
                    var val = $('[name='+b+']').val() ;
                    if(total == 0) {
                      total = parseInt(val);
                    } else {
                      total = total - parseInt(val) ;  
                    }
                                  
                  }) ;

                  if( !isNaN(parseInt(total)) && isFinite(total) ) {
                    $('[name='+v[1].value+']', form).val(total) ;
                    $('[name='+v[1].value+']', form).trigger('change');
                  }
                    
                  // check for conditional text
                  var conditionalText = $('[name='+v[1].value+']', form).data( 'text' ) ;
                  if(conditionalText){
                    conditionalText = conditionalText.split(',') ;
                    $.each(conditionalText, function(q,r){
                      var condition = r.split(':') ;
                      if(total >= condition[0] && total <= condition[1]){
                        $('[name='+v[1].value+']', form).val( condition[2] ) ;
                      }
                    }) ;
                  }
                  
                }) ;
              }) ;
            }

            if( y.name.indexOf("average") > -1 && y.value) {
              //console.log(y.value);
              var targets = y.value.split(',') ;
              var action = $('[name='+v[1].value+']', form).data( 'calc' ) ;
              $.each( targets, function(i, t) {
                $(document).on('change', '[name='+t+']', function(){
                  var total = 0 ; 
                  var avArr = [] ;
                  $.each( targets, function(a, b) {
                    var val = $('[name='+b+']').val() ; 
                    avArr.push(parseFloat(val)) ;
                  }) ;
                  total = window[action](avArr) ;
                  if( !isNaN(parseFloat(total)) && isFinite(total) ) {
                    $('[name='+v[1].value+']', form).val( total ) ;
                    $('[name='+v[1].value+']', form).trigger('change');
                  }
                    
                  // check for conditional text
                  var conditionalText = $('[name='+v[1].value+']', form).data( 'text' ) ;
                  if(conditionalText){
                    conditionalText = conditionalText.split(',') ;
                    $.each(conditionalText, function(q,r){
                      var condition = r.split(':') ;
                      if(total >= condition[0] && total <= condition[1]){
                        $('[name='+v[1].value+']', form).val( condition[2] ) ;
                      }
                    }) ;
                  }
                }) ;
              }) ;
            }

            if( y.name.indexOf("dependsOn") > -1 && y.value) {
              var target = y.value.split(':') ;
              $(document).on('change keyup', '[name='+target[0]+']', function(){
                var val = $(this).val() ;
                var ignore = false ;
                if($(this).is(':checkbox')){
                  if( $(this).prop('checked') ) {
                    val = target[1] ;
                  } else {
                    ignore = true ;
                  }
                }
                if( target[1].indexOf('~') !== -1 && !ignore ) {
                  if(val != target[1].replace("~", "")) {
                    $('[name='+v[1].value+']', form).closest('p').show() ;
                  } else {
                    $('[name='+v[1].value+']', form).closest('p').hide() ;
                  }
                } else if( (target[1].indexOf('>') !== -1 || target[1].indexOf('&gt;') !== -1) && !ignore ){
                  if( parseFloat(val) > parseFloat(target[1].replace(">", "")) || parseFloat(val) > parseFloat(target[1].replace("&gt;", "")) ) {
                    $('[name='+v[1].value+']', form).closest('p').show() ;
                  } else {
                    $('[name='+v[1].value+']', form).closest('p').hide() ;
                  }
                } else if( (target[1].indexOf('<') !== -1 || target[1].indexOf('&lt;') !== -1) && !ignore ){
                  if( parseFloat(val) < parseFloat(target[1].replace("<", "")) || parseFloat(val) < parseFloat(target[1].replace("&lt;", "")) ) {
                    $('[name='+v[1].value+']', form).closest('p').show() ;
                  } else {
                    $('[name='+v[1].value+']', form).closest('p').hide() ;
                  }
                } else if( val == target[1] && !ignore){
                  //show field
                  $('[name='+v[1].value+']', form).closest('p').show() ;
                } else {
                  $('[name='+v[1].value+']', form).closest('p').hide() ;
                }
              }) ;
            }

          }) ;
          
        }) ;
      }) ;
      form.append('<p class="col-md-12 last"><button class="btn btn-default save_btn">Save</button> <button class="btn btn-default save_submit_btn">Save &amp; Submit</button></p>') ;
      
      // validate the form once built
      if(settings.validate == true) {
        if( form.get(0).tagName == 'FORM') {
          form.validate({
            rules: rules
          });
        } else {
          form.closest('form').validate({
            rules: rules
          });
        }
      }
      
      // datepicker fields
      $( ".datepicker" ).each( function() {
        var format = $(this).data('date-format') ;
        $(this).datepicker({
          changeMonth: true,
          changeYear: true,
          dateFormat: 'dd/mm/yy',
          onSelect: function( dateText, inst ) {
            var name = $(this).attr('name') ;
            dateText=dateText.split("/");
            var timestamp = new Date(dateText[1]+"/"+dateText[0]+"/"+dateText[2]).getTime();
            $('[name='+name+'].alt').val( timestamp / 1000 );
          }
        });
      }) ;
      
      // hide any dependant fields
      $("[data-depends-on!='0'][data-depends-on!=''][data-depends-on]", form).closest('p').hide() ;
      
    },
    
    text: function( name, obj ) {
      var class_label = name=='Class'?'Class (Space separated)<br>':'';
      return class_label+'<input type="text" data-field-type="text" data-field-name="'+name+'" name="'+name+'" value="'+(obj.value ? obj.value : '')+'" class="form-control" placeholder="'+(obj.placeholder ? obj.placeholder : '')+'" '+(obj.validate ? 'required' : '')+'>' ;
    },
    hidden: function( name, obj ) {
      return '<input type="hidden" data-field-type="text" data-field-name="'+name+'" name="'+name+'" value="'+(obj.value ? obj.value : '')+'" class="form-control" placeholder="'+(obj.placeholder ? obj.placeholder : '')+'" '+(obj.validate ? 'required' : '')+'>' ;
    },
    name: function( name, obj ) {
      return '<input type="hidden" data-field-type="text" data-field-name="'+name+'" name="'+name+'" value="'+(obj.value ? obj.value : '')+'" class="form-control" placeholder="'+(obj.placeholder ? obj.placeholder : '')+'">' ;
    },
    depends: function( name, obj ) {
      return '<input type="hidden" data-field-type="depends" data-field-name="'+name+'" name="'+name+'" value="'+(obj.value ? obj.value : '')+'" class="depends form-control" placeholder="'+(obj.placeholder ? obj.placeholder : '')+'" '+(obj.validate ? 'required' : '')+' disabled>' ;
      
    },
    sum: function( name, obj ) {
      return '<input type="hidden" data-field-type="sum" data-field-name="'+name+'" name="'+name+'" value="'+(obj.value ? obj.value : '')+'" class="sum form-control" placeholder="'+(obj.placeholder ? obj.placeholder : '')+'" '+(obj.validate ? 'required' : '')+' disabled>' ;  
    },
    percentage: function( name, obj ) {
      return '<input type="hidden" data-field-type="sum" data-field-name="'+name+'" name="'+name+'" value="'+(obj.value ? obj.value : '')+'" class="sum form-control" placeholder="'+(obj.placeholder ? obj.placeholder : '')+'" '+(obj.validate ? 'required' : '')+' disabled>' ;  
    },
    divide: function( name, obj ) {
      return '<input type="hidden" data-field-type="sum" data-field-name="'+name+'" name="'+name+'" value="'+(obj.value ? obj.value : '')+'" class="sum form-control" placeholder="'+(obj.placeholder ? obj.placeholder : '')+'" '+(obj.validate ? 'required' : '')+' disabled>' ;  
    },
    multiply: function( name, obj ) {
      return '<input type="hidden" data-field-type="sum" data-field-name="'+name+'" name="'+name+'" value="'+(obj.value ? obj.value : '')+'" class="sum form-control" placeholder="'+(obj.placeholder ? obj.placeholder : '')+'" '+(obj.validate ? 'required' : '')+' disabled>' ;  
    },
    subtract: function( name, obj ) {
      return '<input type="hidden" data-field-type="sum" data-field-name="'+name+'" name="'+name+'" value="'+(obj.value ? obj.value : '')+'" class="sum form-control" placeholder="'+(obj.placeholder ? obj.placeholder : '')+'" '+(obj.validate ? 'required' : '')+' disabled>' ;  
    },
    average: function( name, obj ) {
      return '<input type="hidden" data-field-type="sum" data-field-name="'+name+'" name="'+name+'" value="'+(obj.value ? obj.value : '')+'" class="sum form-control" placeholder="'+(obj.placeholder ? obj.placeholder : '')+'" '+(obj.validate ? 'required' : '')+' disabled>' ;  
    },
    conditionalText: function( name, obj ) {
      return '<input type="hidden" data-field-type="conditionalText" data-field-name="'+name+'" name="'+name+'" value="'+(obj.value ? obj.value : '')+'" class="conditionalText form-control" placeholder="'+(obj.placeholder ? obj.placeholder : '')+'" '+(obj.validate ? 'required' : '')+' disabled>' ;  
    },
    checkbox: function( name, obj ) {
      return '<p class="checkbox"><label><input type="checkbox" data-field-type="checkbox" data-field-name="'+name+'" name="'+name+'" value="0"> '+name.replace("_", " ")+'</label></p>' ;
    },
    checkboxDepends: function( name, obj ) {
      return '<p class="checkbox checkboxDepends"><label><input type="checkbox" data-field-type="checkboxDepends" data-field-name="'+name+'" name="'+name+'" value="0"> <span>'+name+'</span></label></p>' ;
    },
    checkboxSum: function( name, obj ) {
      return '<p class="checkbox checkboxSum"><label><input type="checkbox" data-field-type="checkboxSum" data-field-name="'+name+'" name="'+name+'" value="0"> <span>Fields to use</span></label></p>' ;
    },
    checkboxPercentage: function( name, obj ) {
      return '<p class="checkbox checkboxSum"><label><input type="checkbox" data-field-type="checkboxSum" data-field-name="'+name+'" name="'+name+'" value="0"> <span>Fields to use</span></label></p>' ;
    },
    checkboxDivide: function( name, obj ) {
      return '<p class="checkbox checkboxSum"><label><input type="checkbox" data-field-type="checkboxSum" data-field-name="'+name+'" name="'+name+'" value="0"> <span>Fields to use</span></label></p>' ;
    },
    checkboxMultiply: function( name, obj ) {
      return '<p class="checkbox checkboxSum"><label><input type="checkbox" data-field-type="checkboxSum" data-field-name="'+name+'" name="'+name+'" value="0"> <span>Fields to use</span></label></p>' ;
    },
    checkboxSubtract: function( name, obj ) {
      return '<p class="checkbox checkboxSum"><label><input type="checkbox" data-field-type="checkboxSum" data-field-name="'+name+'" name="'+name+'" value="0"> <span>Fields to use</span></label></p>' ;
    },
    checkboxAverage: function( name, obj ) {
      return '<p class="checkbox checkboxSum"><label><input type="checkbox" data-field-type="checkboxSum" data-field-name="'+name+'" name="'+name+'" value="0"> <span>Fields to use</span></label></p>' ;
    },
    checkboxConditionalText: function( name, obj ) {
      return '<p class="checkbox checkboxConditionalText"><label><input type="checkbox" data-field-type="checkboxConditionalText" data-field-name="'+name+'" name="'+name+'" value="0"> <span>Conditional Text</span></label></p>' ;
    },
    textarea: function( name, obj ) {
      return '<textarea type="text" data-field-type="textarea" data-field-name="'+name+'" name="'+name+'" class="form-control" placeholder="'+(obj.placeholder ? obj.placeholder : '')+'" '+(obj.validate ? 'required' : '')+'>'+(obj.value ? obj.value : '')+'</textarea>' ;
    },
    select: function( name, obj ) {
      var html = name.charAt(0).toUpperCase()+ (name.replace("_", " ")).slice(1)+' <select class="form-control" size="1" name="'+name+'" data-field-type="select" data-field-name="'+name+'">' ;
      $.each(obj.options, function(i,v){
        html +='<option value="'+i+'">'+v+'</option>' ;
      }) ;
      html += '</select>' ;
      return html ;
    },
    __text: function( obj ) {
      if(typeof obj[10] !== 'undefined' && obj[10].value == "1") {
        var fType = "csr_field";
      } else {
        var fType = "uwr_field";
      }

      var html = '';
      var adminonly = settings.admin==false && obj[7].value == 1 ;
      if(adminonly) html += '<span style="display: none">' ; 
      html +=  '<label class="hidden_field '+fType+' sec_'+obj[9].value.split(/[ ,\/]+/).join('_')+'">'+obj[0].value+' '+(obj[4].value == 1 ? '*' : '')+'</label><input type="text" name="'+obj[1].value+'" placeholder="'+obj[2].value+'" class="'+obj[3].value+' hidden_field '+fType+' sec_'+obj[9].value.split(/[ ,\/]+/).join('_')+'" '+(obj[4].value == 1  && settings.validate==true ? 'required' : '')+' data-depends-on="'+(obj[6].value ? obj[6].value : '')+'">' ;
      if(adminonly) html += '</span>' ;
      return html;
    },
    __number: function( obj ) {
      if(typeof obj[10] !== 'undefined' && obj[10].value == "1") {
        var fType = "csr_field";
      } else {
        var fType = "uwr_field";
      }

      var html = '';
      var adminonly = settings.admin==false && obj[7].value == 1 ;
      if(adminonly) html += '<span style="display: none">' ;
      html += '<label class="hidden_field '+fType+' sec_'+obj[9].value.split(/[ ,\/]+/).join('_')+'">'+obj[0].value+' '+(obj[4].value == 1 ? '*' : '')+'</label><input type="text" name="'+obj[1].value+'" placeholder="'+obj[2].value+'" class="'+obj[3].value+' hidden_field '+fType+' sec_'+obj[9].value.split(/[ ,\/]+/).join('_')+'" '+(obj[4].value == 1  && settings.validate==true ? 'required' : '')+' data-depends-on="'+(obj[6].value ? obj[6].value : '')+'">' ;
      if(adminonly) html += '</span>' ;
      return html;
    },
    __file: function( obj ) {
      if(typeof obj[5] !== 'undefined' && obj[5].value == "1") {
        var fType = "csr_field";
      } else {
        var fType = "uwr_field";
      }

      //console.log(obj);
      //var html = '<label class="hidden_field uwr_field sec_'+obj[4].value.split(/[ ,\/]+/).join('_')+'">'+obj[0].value+' '+(obj[3].value == 1 ? '*' : '')+'</label>';
      var html = '';
      // console.log($("#id"+obj[1].value+"div"));
      $("#id"+obj[1].value+"div").appendTo('#ffgenerated');

      $("#id"+obj[1].value+"div").show();

      $("#id"+obj[1].value+"div").addClass(fType);
      $("#id"+obj[1].value+"div label").first().addClass(fType);
      //html += $("#id"+obj[1].value+"div").html();
      //html += '<label>'+obj[0].value+' '+(obj[4].value == 1 ? '*' : '')+'</label><input type="text" name="'+obj[1].value+'" placeholder="'+obj[2].value+'" class="'+obj[3].value+'" '+(obj[4].value == 1  && settings.validate==true ? 'required' : '')+' data-depends-on="'+(obj[6].value ? obj[6].value : '')+'">' ;
      //html += '@include("ri_form.partials.file-uploader", [\'id\' => \'id1\', \'name\' => \'file1[]\']) ';
      return html;
    },
    __sum: function( obj ) {
      if(typeof obj[12] !== 'undefined' && obj[12].value == "1") {
        var fType = "csr_field";
      } else {
        var fType = "uwr_field";
      }

      var html = '';
      var adminonly = settings.admin==false && obj[7].value == 1 ;
      if(adminonly) html += '<span style="display: none">' ;
      html += '<label class="hidden_field '+fType+' sec_'+obj[11].value.split(/[ ,\/]+/).join('_')+'">'+obj[0].value+' '+(obj[4].value == 1 ? '*' : '')+'</label><input type="text" name="'+obj[1].value+'" placeholder="'+obj[2].value+'" class="'+obj[3].value+' hidden_field '+fType+' sec_'+obj[11].value.split(/[ ,\/]+/).join('_')+'" '+(obj[4].value == 1  && settings.validate==true ? 'required' : '')+' data-sum="'+(obj[6].value ? obj[6].value : '')+'"  data-text="'+(obj[10].value ? obj[10].value : '')+'">' ;
      if(adminonly) html += '</span>' ;
      return html;
    },
    __multiply: function( obj ) {
      if(typeof obj[12] !== 'undefined' && obj[12].value == "1") {
        var fType = "csr_field";
      } else {
        var fType = "uwr_field";
      }

      var html = '';
      var adminonly = settings.admin==false && obj[7].value == 1 ;
      if(adminonly) html += '<span style="display: none">' ;
      html += '<label class="hidden_field '+fType+' sec_'+obj[11].value.split(/[ ,\/]+/).join('_')+'">'+obj[0].value+' '+(obj[4].value == 1 ? '*' : '')+'</label><input type="text" name="'+obj[1].value+'" placeholder="'+obj[2].value+'" class="'+obj[3].value+' hidden_field '+fType+' sec_'+obj[11].value.split(/[ ,\/]+/).join('_')+'" '+(obj[4].value == 1  && settings.validate==true ? 'required' : '')+' data-sum="'+(obj[6].value ? obj[6].value : '')+'"  data-text="'+(obj[10].value ? obj[10].value : '')+'">' ;
      if(adminonly) html += '</span>' ;
      return html;
    },
    __percentage_calculator: function( obj ) {
      if(typeof obj[13] !== 'undefined' && obj[13].value == "1") {
        var fType = "csr_field";
      } else {
        var fType = "uwr_field";
      }

      var html = '';
      var adminonly = settings.admin==false && obj[8].value == 1 ;
      if(adminonly) html += '<span style="display: none">' ;
      html += '<label class="hidden_field '+fType+' sec_'+obj[12].value.split(/[ ,\/]+/).join('_')+'">'+obj[0].value+' '+(obj[5].value == 1 ? '*' : '')+'</label><input type="text" name="'+obj[1].value+'" placeholder="'+obj[3].value+'" class="'+obj[4].value+' hidden_field '+fType+' sec_'+obj[12].value.split(/[ ,\/]+/).join('_')+'" '+(obj[5].value == 1  && settings.validate==true ? 'required' : '')+' data-sum="'+(obj[7].value ? obj[7].value : '')+'"  data-text="'+(obj[11].value ? obj[11].value : '')+'">' ;
      if(adminonly) html += '</span>' ;
      return html;
    },
    __divide: function( obj ) {
      if(typeof obj[13] !== 'undefined' && obj[13].value == "1") {
        var fType = "csr_field";
      } else {
        var fType = "uwr_field";
      }

      var html = '';
      var adminonly = settings.admin==false && obj[8].value == 1 ;
      if(adminonly) html += '<span style="display: none">' ;
      html += '<label class="hidden_field '+fType+' sec_'+obj[12].value.split(/[ ,\/]+/).join('_')+'">'+obj[0].value+' '+(obj[5].value == 1 ? '*' : '')+'</label><input type="text" name="'+obj[1].value+'" placeholder="'+obj[3].value+'" class="'+obj[4].value+' hidden_field '+fType+' sec_'+obj[12].value.split(/[ ,\/]+/).join('_')+'" '+(obj[5].value == 1  && settings.validate==true ? 'required' : '')+' data-sum="'+(obj[7].value ? obj[7].value : '')+'"  data-text="'+(obj[11].value ? obj[11].value : '')+'">' ;
      if(adminonly) html += '</span>' ;
      return html;
    },
    __subtract: function( obj ) {
      if(typeof obj[12] !== 'undefined' && obj[12].value == "1") {
        var fType = "csr_field";
      } else {
        var fType = "uwr_field";
      }

      var html = '';
      var adminonly = settings.admin==false && obj[7].value == 1 ;
      if(adminonly) html += '<span style="display: none">' ;
      html += '<label class="hidden_field '+fType+' sec_'+obj[11].value.split(/[ ,\/]+/).join('_')+'">'+obj[0].value+' '+(obj[4].value == 1 ? '*' : '')+'</label><input type="text" name="'+obj[1].value+'" placeholder="'+obj[2].value+'" class="'+obj[3].value+' hidden_field '+fType+' sec_'+obj[11].value.split(/[ ,\/]+/).join('_')+'" '+(obj[4].value == 1  && settings.validate==true ? 'required' : '')+' data-sum="'+(obj[6].value ? obj[6].value : '')+'"  data-text="'+(obj[10].value ? obj[10].value : '')+'">' ;
      if(adminonly) html += '</span>' ;
      return html;
    },
    __average: function( obj ) {
      if(typeof obj[13] !== 'undefined' && obj[13].value == "1") {
        var fType = "csr_field";
      } else {
        var fType = "uwr_field";
      }

      var html = '';
      var adminonly = settings.admin==false && obj[7].value == 1 ;
      if(adminonly) html += '<span style="display: none">' ;
      html += '<label class="hidden_field '+fType+' sec_'+obj[12].value.split(/[ ,\/]+/).join('_')+'">'+obj[0].value+' '+(obj[4].value == 1 ? '*' : '')+'</label><input type="text" name="'+obj[1].value+'" placeholder="'+obj[2].value+'" class="'+obj[3].value+' hidden_field '+fType+' sec_'+obj[12].value.split(/[ ,\/]+/).join('_')+'" '+(obj[4].value == 1  && settings.validate==true ? 'required' : '')+' data-sum="'+(obj[6].value ? obj[6].value : '')+'" data-calc="'+(obj[9].value ? obj[9].value : 'mean')+'" data-text="'+(obj[11].value ? obj[11].value : '')+'">' ;
      if(adminonly) html += '</span>' ;
      return html;
    },
    __textarea: function( obj ) {
      if(typeof obj[10] !== 'undefined' && obj[10].value == "1") {
        var fType = "csr_field";
      } else {
        var fType = "uwr_field";
      }

      var html = '';
      var adminonly = settings.admin==false && obj[7].value == 1 ;
      if(adminonly) html += '<span style="display: none">' ;
      html += '<label class="hidden_field '+fType+' sec_'+obj[9].value.split(/[ ,\/]+/).join('_')+'">'+obj[0].value+' '+(obj[4].value == 1 ? '*' : '')+'</label><textarea name="'+obj[1].value+'" placeholder="'+obj[2].value+'" class="autoExpand '+obj[3].value+' hidden_field '+fType+' sec_'+obj[9].value.split(/[ ,\/]+/).join('_')+'" '+(obj[4].value == 1  && settings.validate==true ? 'required' : '')+' data-depends-on="'+(obj[6].value ? obj[6].value : '')+'"></textarea>' ;
      if(adminonly) html += '</span>' ;
      return html;
    },
    __email: function( obj ) {
      if(typeof obj[10] !== 'undefined' && obj[10].value == "1") {
        var fType = "csr_field";
      } else {
        var fType = "uwr_field";
      }

      var html = '';
      var adminonly = settings.admin==false && obj[7].value == 1 ;
      if(adminonly) html += '<span style="display: none">' ;
      html += '<label class="hidden_field '+fType+' sec_'+obj[9].value.split(/[ ,\/]+/).join('_')+'">'+obj[0].value+' '+(obj[4].value == 1 ? '*' : '')+'</label><input type="email" name="'+obj[1].value+'" placeholder="'+obj[2].value+'" class="'+obj[3].value+' hidden_field '+fType+' sec_'+obj[9].value.split(/[ ,\/]+/).join('_')+'" '+(obj[4].value == 1  && settings.validate==true ? 'required' : '')+' data-depends-on="'+(obj[6].value ? obj[6].value : '')+'">' ;
      if(adminonly) html += '</span>' ;
      return html;
    },
    __select: function( obj ) {
      if(typeof obj[11] !== 'undefined' && obj[11].value == "1") {
        var fType = "csr_field";
      } else {
        var fType = "uwr_field";
      }

      var html = '';
      var adminonly = settings.admin==false && obj[7].value == 1 ;
      if(adminonly) html += '<span style="display: none">' ;
      html += '<label class="hidden_field '+fType+' sec_'+obj[10].value.split(/[ ,\/]+/).join('_')+'">'+obj[0].value+' '+(obj[4].value == 1 ? '*' : '')+'</label><select name="'+obj[1].value+(obj[8].value == 1 ? '[]' : '')+'" class="'+obj[3].value+' hidden_field '+fType+' sec_'+obj[10].value.split(/[ ,\/]+/).join('_')+'" '+(obj[4].value == 1  && settings.validate==true ? 'required' : '')+' data-depends-on="'+(obj[6].value ? obj[6].value : '')+'" '+(obj[8].value == 1 ? 'multiple' : '')+'>' ;
      html += '<option>Please Select</option>' ;
      $.each(obj[2].value.split(','), function(i,v) {
        html += '<option value="'+v.trim()+'">'+v.trim()+'</option>' ;
      }) ;
      html += '</select>' ;
      if(adminonly) html += '</span>' ;
      return html ;
    },
    __select_risk_control: function( obj ) {
      //console.log(JSON.stringify(obj));
      var html = '';
      var adminonly = settings.admin==false && obj[7].value == 1 ;
      if(adminonly) html += '<span style="display: none">' ;
      html += '<label class="hidden_field csr_field sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'">'+obj[0].value+' '+(obj[3].value == 1 ? '*' : '')+'</label><select name="'+obj[1].value+'" class="form-control rc_dd hidden_field csr_field sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'" '+(obj[4].value == 1  && settings.validate==true ? 'required' : '')+' data-depends-on="'+(obj[6].value ? obj[6].value : '')+'" '+'>' ;
      $.each(obj[2].value.split(','), function(i,v) {
        html += '<option value="Requires Improvement">Requires Improvement</option>' ;
        html += '<option value="Below Average">Below Average</option>' ;
        html += '<option value="Average">Average</option>' ;
        html += '<option value="Good">Good</option>' ;
        html += '<option value="Superior">Superior</option>' ;
        html += '<option value="Not Applicable / Not Assessed" selected>Not Applicable / Not Assessed</option>' ;
      }) ;
      html += '</select><input type="text" class="colorcode hidden_field csr_field sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'" disabled>' ;
      html += '';
      if(adminonly) html += '</span>' ;
      return html ;
    },
    __select_risk_control_breakdown: function( obj ) {
      if(typeof obj[9] !== 'undefined' && obj[9].value == "1") {
        var fType = "csr_field";
      } else {
        var fType = "uwr_field";
      }

      var html = '';
      var adminonly = settings.admin==false && obj[7].value == 1 ;
      if(adminonly) html += '<span style="display: none">' ;
      html += '<label class="hidden_field '+fType+' sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'">'+obj[0].value+' '+(obj[3].value == 1 ? '*' : '')+'</label><select name="'+obj[1].value+'" class="form-control rcb_dd hidden_field '+fType+' sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'" '+(obj[4].value == 1  && settings.validate==true ? 'required' : '')+' data-depends-on="'+(obj[6].value ? obj[6].value : '')+'" '+'>' ;
      $.each(obj[2].value.split(','), function(i,v) {
        html += '<option value="Good with documentation evidenced">Good with documentation evidenced</option>' ;
        html += '<option value="Satisfactory standard evidenced">Satisfactory standard evidenced</option>' ;
        html += '<option value="Generally satisfactory with minor issue identified">Generally satisfactory with minor issue identified</option>' ;
        html += '<option value="Requires attention, Recommendation raised">Requires attention, Recommendation raised</option>' ;
        html += '<option value="Poor - escalation raised with UW">Poor - escalation raised with UW</option>' ;
        html += '<option value="Not reviewed">Not reviewed</option>' ;
        html += '<option value="N/A">N/A</option>' ;
      }) ;
      html += '</select><input type="text" class="colorcode hidden_field '+fType+' sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'" disabled>' ;
      html += '<textarea name="'+obj[1].value+'_ta" class="autoExpand hidden_field form-control '+fType+' sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'"></textarea>';
      if(adminonly) html += '</span>' ;
      return html ;
    },
    __risk_recommendation_summary: function( obj ) {
      var html = '';
      for(i = 1; i <= 15; i++) {
        html += '<span class="rrs_wrapper rrs_wrapper_'+i+'">';
        var adminonly = settings.admin==false && obj[7].value == 1 ;
        if(adminonly) html += '<span style="display: none">' ;
        html += '<p class="col-md-3"><label class="hidden_field csr_field sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'">Ref:</label><input type="text" name="'+obj[1].value+'_'+i+'_ref" class="hidden_field form-control csr_field sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'" value="'+obj[9].value+':'+i+'"></p>' ;
        html += '<p class="col-md-9"><label class="hidden_field csr_field sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'">Summary:</label><input type="text" name="'+obj[1].value+'_'+i+'_summary" class="form-control rrs hidden_field csr_field sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'"></p>' ;
        html += '<p class="col-md-12 add_rr_p hidden_field csr_field sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'"><a href="#" class="btn btn-info add_rrs" data-display="rrs_wrapper_'+(i+1)+'">+</a> <a href="#" class="btn btn-danger remove_rrs" data-hide="rrs_wrapper_'+i+'">x</a></p>';
        if(adminonly) html += '</span>' ;
        html += '</span>';
      }
       return html ;
    },

    __special_instructions: function(obj){
      if(typeof obj[10] !== 'undefined' && obj[10].value == "1") {
        var fType = "csr_field";
      } else {
        var fType = "uwr_field";
      }

      var html = '';
      var adminonly = settings.admin==false && obj[7].value == 1 ;
      if(adminonly) html += '<span style="display: none">' ;
      html += '<label class="hidden_field '+fType+' sec_'+obj[9].value.split(/[ ,\/]+/).join('_')+'">'+obj[0].value+' '+(obj[4].value == 1 ? '*' : '')+'</label><textarea name="'+obj[1].value+'" placeholder="'+obj[2].value+'" class="autoExpand '+obj[3].value+' hidden_field '+fType+' sec_'+obj[9].value.split(/[ ,\/]+/).join('_')+'" '+(obj[4].value == 1  && settings.validate==true ? 'required' : '')+' data-depends-on="'+(obj[6].value ? obj[6].value : '')+'"></textarea>' ;
      if(adminonly) html += '</span>' ;
      return html;
    },
    __risk_recommendation: function( obj ) {
      var html = '';
      for(i = 1; i <= 15; i++) {
        if(i == 1) {
          html += '<p class="col-md-12 hidden_field csr_field sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'" style="float: left;"><strong>Risk Recommendation - '+obj[0].value+' '+(obj[4].value == 1 ? '*' : '')+'</strong></p>';
          html += '<p class="col-md-12 add_rr_p first_add hidden_field csr_field sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+' '+obj[1].value+'_first_add"><a href="#" class="btn csr_field btn-info add_rr sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'" data-display="rr_wrapper_'+i+'" rr_wrapper="'+obj[1].value+'">Add</a></p>';
        }
        html += '<span class="rr_wrapper rr_wrapper_'+i+' '+obj[1].value+'_wrapper" rr_wrapper="'+obj[1].value+'_wrapper"><div class="row mx-0">';
        var adminonly = settings.admin==false && obj[7].value == 1 ;
        if(adminonly) html += '<span style="display: none">' ;
        html += '<p class="col-md-2"><label class="hidden_field csr_field sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'">Ref:</label><input type="text" name="'+obj[1].value+'_'+i+'_ref" class="hidden_field form-control csr_field sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'" value="'+obj[9].value+':'+i+'"></p>' ;
        html += '<p class="col-md-2"><label class="hidden_field csr_field sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'">Classification:</label><select name="'+obj[1].value+'_'+i+'_classification" class="rr_dd rrt_dd rr_classification hidden_field csr_field sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+' form-control rr_classification" data-depends-on="'+(obj[6].value ? obj[6].value : '')+'" '+'>' ;
        $.each(obj[2].value.split(','), function(i,v) {
          html += '<option value="">-</option>' ;
          html += '<option value="R1">R1</option>' ;
          html += '<option value="R2">R2</option>' ;
          html += '<option value="R3">R3</option>' ;
          html += '<option value="R4">R4</option>' ;
        }) ;
        html += '</select></p>';
        html += '<p class="col-md-3"><label class="hidden_field csr_field sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'">Title:</label><select name="'+obj[1].value+'_'+i+'_title" section_name="sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'" class="rr_dd hidden_field rrt_dd csr_field sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+' form-control select2 rr_title" style="display:none !important; position:absolute; left:-10000px;" data-depends-on="'+(obj[6].value ? obj[6].value : '')+'" '+'>' ;
        $.each(obj[2].value.split(','), function(i,v) {
          html += '<option value="">-</option>' ;
        }) ;
        html += '</select></p>';

        html += '<p class="col-md-2"><label class="hidden_field csr_field sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'">Indicative Costs:</label><input type="text" name="'+obj[1].value+'_'+i+'_cba" class="hidden_field form-control csr_field sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'"></p>' ;

        html += '<p class="col-md-3"><label class="hidden_field csr_field sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'">Required by:</label><input type="text" onkeydown="return false" name="'+obj[1].value+'_'+i+'_required_by" class="form-control hidden_field csr_field datepickers sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'"></p>' ;
        html += '<p class="col-md-12"><label class="hidden_field csr_field sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'">Description:</label><textarea rows="6" name="'+obj[1].value+'_'+i+'_description" class="autoExpand rr_desc hidden_field form-control csr_field sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'"></textarea></p>';
        html += '<p class="col-md-12 '+obj[1].value+'_'+i+'_action_container"><label class="hidden_field csr_field sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'">Action &amp; Photographs:</label><textarea rows="6" name="'+obj[1].value+'_'+i+'_action" class="autoExpand rr_action hidden_field form-control csr_field sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'"></textarea></p>';
        html += '<p class="col-md-12 add_rr_p hidden_field csr_field sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'"><a href="#" class="btn btn-info add_rr" data-display="rr_wrapper_'+(i+1)+'" rr_wrapper="'+obj[1].value+'">+</a> <a href="#" class="btn btn-danger remove_rr" data-hide="rr_wrapper_'+i+'" rr_wrapper="'+obj[1].value+'">x</a></p><p><hr class="csr_field"/></p>';
        if(adminonly) html += '</span>' ;
        html += '</div></span>';
      }
      return html ;
    },
    __re_review: function( obj ) {
      var html = '';
      for(i = 1; i <= 10; i++) {
        html += '<span class="rr_wrapper '+obj[1].value+'_wrapper rr_wrapper_'+i+'" rr_wrapper="'+obj[1].value+'_wrapper">';
        if(i == 1) {
          html += '<p class="col-md-12 hidden_field csr_field '+obj[1].value+'_wrapper" rr_wrapper="'+obj[1].value+'_wrapper"><strong>RE Review</strong></p>';
        }
        var adminonly = settings.admin==false && obj[7].value == 1 ;
        if(adminonly) html += '<span style="display: none">' ;
        html += '<p class="col-md-9"><label class="hidden_field csr_field sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'">Action to be completed</label><input type="text" name="'+obj[1].value+'_'+i+'_action" class="hidden_field re_review_field form-control csr_field sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'"></p>' ;
        html += '<p class="col-md-3"><label class="hidden_field csr_field sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'">When by:</label><input type="text" onkeydown="return false" name="'+obj[1].value+'_'+i+'_required_by" class="form-control re_review_field hidden_field csr_field datepickers sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'"></p>' ;
        html += '<p class="col-md-12 add_rr_p hidden_field csr_field sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'"><a href="#" class="btn btn-info add_rr" rr_wrapper="'+obj[1].value+'" data-display="rr_wrapper_'+(i+1)+'">+</a> <a href="#" class="btn btn-danger remove_rr" rr_wrapper="'+obj[1].value+'" data-hide="rr_wrapper_'+i+'">x</a></p><p><hr/></p>';
        if(adminonly) html += '</span>' ;
        html += '</span>';
      }
      return html ;
    },
    __risk_reduce_access: function(obj){
      if(typeof obj[10] !== 'undefined' && obj[10].value == "1") {
        var fType = "csr_field";
      } else {
        // var fType = "uwr_field";
        // Hardcode
        var fType = "csr_field";
      }
      var html = '';
      var adminonly = settings.admin==false && obj[6].value == 1 ;
      if(adminonly) html += '<span style="display: none">' ;
      html += '<div class="col-md-12"><h2 class="hidden_field ffheader '+fType+' sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'">'+obj[0].value+' '+(obj[4].value == 1 ? '*' : '')+'</h2>';
      html += '<hr class="hidden_field '+fType+' sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'"></div>';
      html += '<p class="col-md-3"><label class="hidden_field '+fType+' sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'">First Name</label><input type="text" name="risk_reduce_access_first_name" class="hidden_field form-control '+fType+' sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'" data-depends-on="'+(obj[5].value ? obj[5].value : '')+'"></p>' ;
      html += '<p class="col-md-3"><label class="hidden_field '+fType+' sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'">Last Name</label><input type="text" name="risk_reduce_access_last_name" class="hidden_field form-control '+fType+' sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'" data-depends-on="'+(obj[5].value ? obj[5].value : '')+'"></p>' ;
      html += '<p class="col-md-3"><label class="hidden_field '+fType+' sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'">Email</label><input type="text" name="risk_reduce_access_email" class="hidden_field form-control '+fType+' sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'" data-depends-on="'+(obj[5].value ? obj[5].value : '')+'"></p>' ;
      html += '<p class="col-md-3"><label class="hidden_field '+fType+' sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'">Services of interest</label><select name="risk_reduce_access_services[]" class="form-control hidden_field '+fType+' sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'" data-depends-on="'+(obj[5].value ? obj[5].value : '')+'" multiple>' ;
      html += '<option value="safety_media">Safety Media</option>' ;
      html += '<option value="croner">Croner</option>' ;
      html += '<option value="cq_live">CQ-Live</option>' ;
      html += '</select></p>' ;
      if(adminonly) html += '</span>' ;
      return html;
    },
    __country: function( obj ) {
      if(typeof obj[12] !== 'undefined' && obj[12].value == "1") {
        var fType = "csr_field";
      } else {
        var fType = "uwr_field";
      }

      var html = '';
      var adminonly = settings.admin==false && obj[7].value == 1 ;
      if(adminonly) html += '<span style="display: none">' ;
      html += '<label class="hidden_field '+fType+' sec_'+obj[10].value.split(/[ ,\/]+/).join('_')+'">'+obj[0].value+' '+(obj[4].value == 1 ? '*' : '')+'</label><select name="'+obj[1].value+(obj[8].value == 1 ? '[]' : '')+'" class="'+obj[3].value+' hidden_field '+fType+' sec_'+obj[10].value.split(/[ ,\/]+/).join('_')+'" '+(obj[4].value == 1  && settings.validate==true ? 'required' : '')+' data-depends-on="'+(obj[6].value ? obj[6].value : '')+'" '+(obj[8].value == 1 ? 'multiple' : '')+'>' ;
      html += '<option>Please Select</option>' ;
      $.each(obj[2].value.split(','), function(i,v) {
        html += '<option value="'+v.trim()+'">'+v.trim()+'</option>' ;
      }) ;
      html += '</select>' ;
      if(adminonly) html += '</span>' ;
      return html ;
    },
    __time_period_of_accident: function( obj ) {
      var html = '';
      var adminonly = settings.admin==false && obj[7].value == 1 ;
      if(adminonly) html += '<span style="display: none">' ;
      html += '<label>'+obj[0].value+' '+(obj[4].value == 1 ? '*' : '')+'</label><select name="'+obj[1].value+(obj[8].value == 1 ? '[]' : '')+'" class="'+obj[3].value+'" '+(obj[4].value == 1  && settings.validate==true ? 'required' : '')+' data-depends-on="'+(obj[6].value ? obj[6].value : '')+'" '+(obj[8].value == 1 ? 'multiple' : '')+'>' ;
      html += '<option>Please Select</option>' ;
      $.each(obj[2].value.split(','), function(i,v) {
        html += '<option value="'+v.trim()+'">'+v.trim()+'</option>' ;
      }) ;
      html += '</select>' ;
      if(adminonly) html += '</span>' ;
      return html ;
    },
    __where_did_the_accident_happen: function( obj ) {
      var html = '';
      var adminonly = settings.admin==false && obj[7].value == 1 ;
      if(adminonly) html += '<span style="display: none">' ;
      html += '<label>'+obj[0].value+' '+(obj[4].value == 1 ? '*' : '')+'</label><select name="'+obj[1].value+(obj[8].value == 1 ? '[]' : '')+'" class="'+obj[3].value+'" '+(obj[4].value == 1  && settings.validate==true ? 'required' : '')+' data-depends-on="'+(obj[6].value ? obj[6].value : '')+'" '+(obj[8].value == 1 ? 'multiple' : '')+'>' ;
      html += '<option>Please Select</option>' ;
      $.each(obj[2].value.split(','), function(i,v) {
        html += '<option value="'+v.trim()+'">'+v.trim()+'</option>' ;
      }) ;
      html += '</select>' ;
      if(adminonly) html += '</span>' ;
      return html ;
    },
    __branch: function( obj ) {
      var html = '';
      var adminonly = settings.admin==false && obj[7].value == 1 ;
      if(adminonly) html += '<span style="display: none">' ;
      html += '<label>'+obj[0].value+' '+(obj[4].value == 1 ? '*' : '')+'</label><select name="'+obj[1].value+(obj[8].value == 1 ? '[]' : '')+'" class="'+obj[3].value+'" '+(obj[4].value == 1  && settings.validate==true ? 'required' : '')+' data-depends-on="'+(obj[6].value ? obj[6].value : '')+'" '+(obj[8].value == 1 ? 'multiple' : '')+'>' ;
      html += '<option>Please Select</option>' ;
      html += '</select>' ;
      if(adminonly) html += '</span>' ;
      return html ;
    },
    __injured_person_type: function( obj ) {
      var html = '';
      var adminonly = settings.admin==false && obj[7].value == 1 ;
      if(adminonly) html += '<span style="display: none">' ;
      html += '<label>'+obj[0].value+' '+(obj[4].value == 1 ? '*' : '')+'</label><select name="'+obj[1].value+(obj[8].value == 1 ? '[]' : '')+'" class="'+obj[3].value+'" '+(obj[4].value == 1  && settings.validate==true ? 'required' : '')+' data-depends-on="'+(obj[6].value ? obj[6].value : '')+'" '+(obj[8].value == 1 ? 'multiple' : '')+'>' ;
      html += '<option>Please Select</option>' ;
      $.each(obj[2].value.split(','), function(i,v) {
        html += '<option value="'+v.trim()+'">'+v.trim()+'</option>' ;
      }) ;
      html += '</select>' ;
      if(adminonly) html += '</span>' ;
      return html ;
    },
    __injured_person_name: function( obj ) {
      var html = '';
      var adminonly = settings.admin==false && obj[7].value == 1 ;
      if(adminonly) html += '<span style="display: none">' ; 
      html +=  '<label>'+obj[0].value+' '+(obj[4].value == 1 ? '*' : '')+'</label><input type="text" name="'+obj[1].value+'" placeholder="'+obj[2].value+'" class="'+obj[3].value+'" '+(obj[4].value == 1  && settings.validate==true ? 'required' : '')+' data-depends-on="'+(obj[6].value ? obj[6].value : '')+'">' ;
      if(adminonly) html += '</span>' ;
      return html;
    },
    __severity_of_the_injury: function( obj ) {
      var html = '';
      var adminonly = settings.admin==false && obj[7].value == 1 ;
      if(adminonly) html += '<span style="display: none">' ;
      html += '<label>'+obj[0].value+' '+(obj[4].value == 1 ? '*' : '')+'</label><select name="'+obj[1].value+(obj[8].value == 1 ? '[]' : '')+'" class="'+obj[3].value+'" '+(obj[4].value == 1  && settings.validate==true ? 'required' : '')+' data-depends-on="'+(obj[6].value ? obj[6].value : '')+'" '+(obj[8].value == 1 ? 'multiple' : '')+'>' ;
      html += '<option>Please Select</option>' ;
      $.each(obj[2].value.split(','), function(i,v) {
        html += '<option value="'+v.trim()+'">'+v.trim()+'</option>' ;
      }) ;
      html += '</select>' ;
      if(adminonly) html += '</span>' ;
      return html ;
    },
    __accident_category: function( obj ) {
      var html = '';
      var adminonly = settings.admin==false && obj[7].value == 1 ;
      if(adminonly) html += '<span style="display: none">' ;
      html += '<label>'+obj[0].value+' '+(obj[4].value == 1 ? '*' : '')+'</label><select name="'+obj[1].value+(obj[8].value == 1 ? '[]' : '')+'" class="'+obj[3].value+'" '+(obj[4].value == 1  && settings.validate==true ? 'required' : '')+' data-depends-on="'+(obj[6].value ? obj[6].value : '')+'" '+(obj[8].value == 1 ? 'multiple' : '')+'>' ;
      html += '<option>Please Select</option>' ;
      $.each(obj[2].value.split(','), function(i,v) {
        html += '<option value="'+v.trim()+'">'+v.trim()+'</option>' ;
      }) ;
      html += '</select>' ;
      if(adminonly) html += '</span>' ;
      return html ;
    },
    __action_taken: function( obj ) {
      var html = '';
      var adminonly = settings.admin==false && obj[7].value == 1 ;
      if(adminonly) html += '<span style="display: none">' ;
      html += '<label>'+obj[0].value+' '+(obj[4].value == 1 ? '*' : '')+'</label><select name="'+obj[1].value+(obj[8].value == 1 ? '[]' : '')+'" class="'+obj[3].value+'" '+(obj[4].value == 1  && settings.validate==true ? 'required' : '')+' data-depends-on="'+(obj[6].value ? obj[6].value : '')+'" '+(obj[8].value == 1 ? 'multiple' : '')+'>' ;
      html += '<option>Please Select</option>' ;
      $.each(obj[2].value.split(','), function(i,v) {
        html += '<option value="'+v.trim()+'">'+v.trim()+'</option>' ;
      }) ;
      html += '</select>' ;
      if(adminonly) html += '</span>' ;
      return html ;
    },
    __nature_of_injury: function( obj ) {
      var html = '';
      var adminonly = settings.admin==false && obj[7].value == 1 ;
      if(adminonly) html += '<span style="display: none">' ;
      html += '<label>'+obj[0].value+' '+(obj[4].value == 1 ? '*' : '')+'</label><select name="'+obj[1].value+(obj[8].value == 1 ? '[]' : '')+'" class="'+obj[3].value+'" '+(obj[4].value == 1  && settings.validate==true ? 'required' : '')+' data-depends-on="'+(obj[6].value ? obj[6].value : '')+'" '+(obj[8].value == 1 ? 'multiple' : '')+'>' ;
      html += '<option>Please Select</option>' ;
      $.each(obj[2].value.split(','), function(i,v) {
        html += '<option value="'+v.trim()+'">'+v.trim()+'</option>' ;
      }) ;
      html += '</select>' ;
      if(adminonly) html += '</span>' ;
      return html ;
    },
    __area_details: function( obj ) {
      var html = '';
      var adminonly = settings.admin==false && obj[7].value == 1 ;
      if(adminonly) html += '<span style="display: none">' ;
      html += '<label>'+obj[0].value+' '+(obj[4].value == 1 ? '*' : '')+'</label><select name="'+obj[1].value+(obj[8].value == 1 ? '[]' : '')+'" class="'+obj[3].value+'" '+(obj[4].value == 1  && settings.validate==true ? 'required' : '')+' data-depends-on="'+(obj[6].value ? obj[6].value : '')+'" '+(obj[8].value == 1 ? 'multiple' : '')+'>' ;
      html += '<option>Please Select</option>' ;
      $.each(obj[2].value.split(','), function(i,v) {
        html += '<option value="'+v.trim()+'">'+v.trim()+'</option>' ;
      }) ;
      html += '</select>' ;
      if(adminonly) html += '</span>' ;
      return html ;
    },
    __radios: function( obj ) {
      if(typeof obj[10] !== 'undefined' && obj[10].value == "1") {
        var fType = "csr_field";
      } else {
        var fType = "uwr_field";
      }

      var html = '';
      var adminonly = settings.admin==false && obj[7].value == 1 ;
      if(adminonly) html += '<span style="display: none">' ;
      html += '<label class="hidden_field '+fType+' sec_'+obj[9].value.split(/[ ,\/]+/).join('_')+'">'+obj[0].value+' '+(obj[4].value == 1 ? '*' : '')+'</label>';
      $.each(obj[2].value.split(','), function(i,v) {
        html += '<label class="radio-inline hidden_field sec_'+obj[9].value.split(/[ ,\/]+/).join('_')+'"><input type="radio" name="'+obj[1].value+'" value="'+v.trim()+'" '+(obj[4].value == 1  && settings.validate==true ? 'required' : '')+' data-depends-on="'+(obj[6].value ? obj[6].value : '')+'" class="hidden_field '+fType+' sec_'+obj[9].value.split(/[ ,\/]+/).join('_')+'"> '+v.trim()+'</label>' ;
      }) ;
      html += '<label for="'+obj[1].value+'" class="error" style="display:none">* Please pick an option above</label>' ;
      if(adminonly) html += '</span>' ;
      return html;
    },
    __checkbox: function( obj ) {
      if(typeof obj[9] !== 'undefined' && obj[9].value == "1") {
        var fType = "csr_field";
      } else {
        var fType = "uwr_field";
      }

      var html = '';
      var adminonly = settings.admin==false && obj[6].value == 1 ;
      if(adminonly) html += '<span style="display: none">' ;
      html += '<label class="hidden_field '+fType+' sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'">'+obj[0].value+' '+(obj[3].value == 1 ? '*' : '')+'</label><input type="checkbox" name="'+obj[1].value+'" class="'+obj[2].value+' hidden_field '+fType+' sec_'+obj[8].value.split(/[ ,\/]+/).join('_')+'" '+(obj[3].value == 1 && settings.validate==true ? 'required' : '')+' data-depends-on="'+(obj[5].value ? obj[5].value : '')+'" value="1">' ;
      if(adminonly) html += '</span>' ;
      return html;
    },
    __date: function( obj ) {
      if(typeof obj[11] !== 'undefined' && obj[11].value == "1") {
        var fType = "csr_field";
      } else {
        var fType = "uwr_field";
      }

      var html = '';
      var adminonly = settings.admin==false && obj[8].value == 1 ;
      if(adminonly) html += '<span style="display: none">' ;
      html += '<label class="hidden_field '+fType+' sec_'+obj[10].value.split(/[ ,\/]+/).join('_')+'">'+obj[0].value+' '+(obj[5].value == 1 ? '*' : '')+'</label><input type="text" onkeydown="return false" placeholder="'+obj[2].value+'" name="'+obj[1].value+'" '+(obj[5].value == 1 && settings.validate==true ? 'required' : '')+' data-depends-on="'+(obj[7].value ? obj[7].value : '')+'" class="form-control hidden_field '+fType+' datepickers sec_'+obj[10].value.split(/[ ,\/]+/).join('_')+'">' ;
      if(adminonly) html += '</span>' ;
      return html;
    },
    __date_of_visit: function( obj ) {
      if(typeof obj[11] !== 'undefined' && obj[11].value == "1") {
        var fType = "csr_field";
      } else {
        var fType = "uwr_field";
      }

      var html = '';
      var adminonly = settings.admin==false && obj[8].value == 1 ;
      if(adminonly) html += '<span style="display: none">' ;
      html += '<label class="hidden_field '+fType+' sec_'+obj[10].value.split(/[ ,\/]+/).join('_')+'">'+obj[0].value+' '+(obj[5].value == 1 ? '*' : '')+'</label><input type="text" name="'+obj[1].value+'" placeholder="'+obj[2].value+'" class="datepicker '+obj[3].value+' hidden_field '+fType+' sec_'+obj[10].value.split(/[ ,\/]+/).join('_')+'" data-date-format="'+obj[4].value+'" '+(obj[5].value == 1 && settings.validate==true ? 'required' : '')+' data-depends-on="'+(obj[7].value ? obj[7].value : '')+'">' ;
      html += '<input type="hidden" name="'+obj[1].value+'" class="'+obj[3].value+' alt">'
      if(adminonly) html += '</span>' ;
      return html;
    },
    __proposed_next_survey: function( obj ) {
      if(typeof obj[11] !== 'undefined' && obj[11].value == "1") {
        var fType = "csr_field";
      } else {
        var fType = "uwr_field";
      }

      var html = '';
      var adminonly = settings.admin==false && obj[8].value == 1 ;
      if(adminonly) html += '<span style="display: none">' ;
      html += '<label class="hidden_field '+fType+' sec_'+obj[10].value.split(/[ ,\/]+/).join('_')+'">'+obj[0].value+' '+(obj[5].value == 1 ? '*' : '')+'</label><input type="text" onkeydown="return false" name="'+obj[1].value+'" '+(obj[5].value == 1 && settings.validate==true ? 'required' : '')+' data-depends-on="'+(obj[7].value ? obj[7].value : '')+'" class="form-control hidden_field '+fType+' datepickers sec_'+obj[10].value.split(/[ ,\/]+/).join('_')+'">' ;

      // html += '<input type="hidden" name="'+obj[1].value+'" class="'+obj[3].value+' alt">';
      if(adminonly) html += '</span>' ;
      return html;
    },
    __date_of_accident: function( obj ) {
      var html = '';
      var adminonly = settings.admin==false && obj[8].value == 1 ;
      if(adminonly) html += '<span style="display: none">' ;
      html += '<label>'+obj[0].value+' '+(obj[5].value == 1 ? '*' : '')+'</label><input type="text" name="'+obj[1].value+'" placeholder="'+obj[2].value+'" class="datepicker '+obj[3].value+'" data-date-format="'+obj[4].value+'" '+(obj[5].value == 1 && settings.validate==true ? 'required' : '')+' data-depends-on="'+(obj[7].value ? obj[7].value : '')+'">' ;
      html += '<input type="hidden" name="'+obj[1].value+'" class="'+obj[3].value+' alt">'
      if(adminonly) html += '</span>' ;
      return html;
    },
    __linebreak: function( obj ) {
      if(typeof obj[2] !== 'undefined' && obj[2].value == "1") {
        var fType = "csr_field";
      } else {
        var fType = "uwr_field";
      }

      return '<hr class="hidden_field '+fType+' sec_'+obj[1].value.split(/[ ,\/]+/).join('_')+'">' ;
    },
    __freetext: function( obj ) {
      if(typeof obj[3] !== 'undefined' && obj[3].value == "1") {
        var fType = "csr_field";
      } else {
        var fType = "uwr_field";
      }
      return '<p class="hidden_field col-md-12 '+fType+' sec_'+obj[2].value.split(/[ ,\/]+/).join('_')+'">'+obj[0].value+'</p>' ;
    },
    __fileprompt: function( obj ) {
      if(typeof obj[3] !== 'undefined' && obj[3].value == "1") {
        var fType = "csr_field";
      } else {
        var fType = "uwr_field";
      }

      $("#id"+obj[1].value+"div").addClass(fType);

      return '<p class="hidden_field col-md-12 '+fType+' sec_'+obj[2].value.split(/[ ,\/]+/).join('_')+'">'+obj[0].value+'</p>' ;
      // return obj[0].value ;
    },
    __header: function( obj ) {
      if(typeof obj[3] !== 'undefined' && obj[3].value == "1") {
        var fType = "csr_field";
      } else {
        var fType = "uwr_field";
      }
      return '<h2 class="hidden_field ffheader hidden_field '+fType+' sec_'+obj[2].value.split(/[ ,\/]+/).join('_')+'">'+obj[0].value+'</h2><hr class="hidden_field '+fType+' sec_'+obj[2].value.split(/[ ,\/]+/).join('_')+'">' ;
    }
  }
  
  $.fn.ffForm = function( method ) {
    $.fn.ffForm.methods = methods;
    if ( methods[method] ) {
      return methods[method].apply( this, Array.prototype.slice.call( arguments, 1 ));
    } else if ( typeof method === 'object' || ! method ) {
      return methods.init.apply( this, arguments );
    } else {
      $.error( 'Method ' +  method + ' does not exist' );
    } 
  };
  
}( jQuery ));
/*! jQuery Validation Plugin - v1.13.1 - 10/14/2014
 * http://jqueryvalidation.org/
 * Copyright (c) 2014 Jörn Zaefferer; Licensed MIT */
!function(a){"function"==typeof define&&define.amd?define(["jquery"],a):a(jQuery)}(function(a){a.extend(a.fn,{validate:function(b){if(!this.length)return void(b&&b.debug&&window.console&&console.warn("Nothing selected, can't validate, returning nothing."));var c=a.data(this[0],"validator");return c?c:(this.attr("novalidate","novalidate"),c=new a.validator(b,this[0]),a.data(this[0],"validator",c),c.settings.onsubmit&&(this.validateDelegate(":submit","click",function(b){c.settings.submitHandler&&(c.submitButton=b.target),a(b.target).hasClass("cancel")&&(c.cancelSubmit=!0),void 0!==a(b.target).attr("formnovalidate")&&(c.cancelSubmit=!0)}),this.submit(function(b){function d(){var d,e;return c.settings.submitHandler?(c.submitButton&&(d=a("<input type='hidden'/>").attr("name",c.submitButton.name).val(a(c.submitButton).val()).appendTo(c.currentForm)),e=c.settings.submitHandler.call(c,c.currentForm,b),c.submitButton&&d.remove(),void 0!==e?e:!1):!0}return c.settings.debug&&b.preventDefault(),c.cancelSubmit?(c.cancelSubmit=!1,d()):c.form()?c.pendingRequest?(c.formSubmitted=!0,!1):d():(c.focusInvalid(),!1)})),c)},valid:function(){var b,c;return a(this[0]).is("form")?b=this.validate().form():(b=!0,c=a(this[0].form).validate(),this.each(function(){b=c.element(this)&&b})),b},removeAttrs:function(b){var c={},d=this;return a.each(b.split(/\s/),function(a,b){c[b]=d.attr(b),d.removeAttr(b)}),c},rules:function(b,c){var d,e,f,g,h,i,j=this[0];if(b)switch(d=a.data(j.form,"validator").settings,e=d.rules,f=a.validator.staticRules(j),b){case"add":a.extend(f,a.validator.normalizeRule(c)),delete f.messages,e[j.name]=f,c.messages&&(d.messages[j.name]=a.extend(d.messages[j.name],c.messages));break;case"remove":return c?(i={},a.each(c.split(/\s/),function(b,c){i[c]=f[c],delete f[c],"required"===c&&a(j).removeAttr("aria-required")}),i):(delete e[j.name],f)}return g=a.validator.normalizeRules(a.extend({},a.validator.classRules(j),a.validator.attributeRules(j),a.validator.dataRules(j),a.validator.staticRules(j)),j),g.required&&(h=g.required,delete g.required,g=a.extend({required:h},g),a(j).attr("aria-required","true")),g.remote&&(h=g.remote,delete g.remote,g=a.extend(g,{remote:h})),g}}),a.extend(a.expr[":"],{blank:function(b){return!a.trim(""+a(b).val())},filled:function(b){return!!a.trim(""+a(b).val())},unchecked:function(b){return!a(b).prop("checked")}}),a.validator=function(b,c){this.settings=a.extend(!0,{},a.validator.defaults,b),this.currentForm=c,this.init()},a.validator.format=function(b,c){return 1===arguments.length?function(){var c=a.makeArray(arguments);return c.unshift(b),a.validator.format.apply(this,c)}:(arguments.length>2&&c.constructor!==Array&&(c=a.makeArray(arguments).slice(1)),c.constructor!==Array&&(c=[c]),a.each(c,function(a,c){b=b.replace(new RegExp("\\{"+a+"\\}","g"),function(){return c})}),b)},a.extend(a.validator,{defaults:{messages:{},groups:{},rules:{},errorClass:"error",validClass:"valid",errorElement:"label",focusCleanup:!1,focusInvalid:!0,errorContainer:a([]),errorLabelContainer:a([]),onsubmit:!0,ignore:":hidden",ignoreTitle:!1,onfocusin:function(a){this.lastActive=a,this.settings.focusCleanup&&(this.settings.unhighlight&&this.settings.unhighlight.call(this,a,this.settings.errorClass,this.settings.validClass),this.hideThese(this.errorsFor(a)))},onfocusout:function(a){this.checkable(a)||!(a.name in this.submitted)&&this.optional(a)||this.element(a)},onkeyup:function(a,b){(9!==b.which||""!==this.elementValue(a))&&(a.name in this.submitted||a===this.lastElement)&&this.element(a)},onclick:function(a){a.name in this.submitted?this.element(a):a.parentNode.name in this.submitted&&this.element(a.parentNode)},highlight:function(b,c,d){"radio"===b.type?this.findByName(b.name).addClass(c).removeClass(d):a(b).addClass(c).removeClass(d)},unhighlight:function(b,c,d){"radio"===b.type?this.findByName(b.name).removeClass(c).addClass(d):a(b).removeClass(c).addClass(d)}},setDefaults:function(b){a.extend(a.validator.defaults,b)},messages:{required:"This field is required.",remote:"Please fix this field.",email:"Please enter a valid email address.",url:"Please enter a valid URL.",date:"Please enter a valid date.",dateISO:"Please enter a valid date ( ISO ).",number:"Please enter a valid number.",digits:"Please enter only digits.",creditcard:"Please enter a valid credit card number.",equalTo:"Please enter the same value again.",maxlength:a.validator.format("Please enter no more than {0} characters."),minlength:a.validator.format("Please enter at least {0} characters."),rangelength:a.validator.format("Please enter a value between {0} and {1} characters long."),range:a.validator.format("Please enter a value between {0} and {1}."),max:a.validator.format("Please enter a value less than or equal to {0}."),min:a.validator.format("Please enter a value greater than or equal to {0}.")},autoCreateRanges:!1,prototype:{init:function(){function b(b){var c=a.data(this[0].form,"validator"),d="on"+b.type.replace(/^validate/,""),e=c.settings;e[d]&&!this.is(e.ignore)&&e[d].call(c,this[0],b)}this.labelContainer=a(this.settings.errorLabelContainer),this.errorContext=this.labelContainer.length&&this.labelContainer||a(this.currentForm),this.containers=a(this.settings.errorContainer).add(this.settings.errorLabelContainer),this.submitted={},this.valueCache={},this.pendingRequest=0,this.pending={},this.invalid={},this.reset();var c,d=this.groups={};a.each(this.settings.groups,function(b,c){"string"==typeof c&&(c=c.split(/\s/)),a.each(c,function(a,c){d[c]=b})}),c=this.settings.rules,a.each(c,function(b,d){c[b]=a.validator.normalizeRule(d)}),a(this.currentForm).validateDelegate(":text, [type='password'], [type='file'], select, textarea, [type='number'], [type='search'] ,[type='tel'], [type='url'], [type='email'], [type='datetime'], [type='date'], [type='month'], [type='week'], [type='time'], [type='datetime-local'], [type='range'], [type='color'], [type='radio'], [type='checkbox']","focusin focusout keyup",b).validateDelegate("select, option, [type='radio'], [type='checkbox']","click",b),this.settings.invalidHandler&&a(this.currentForm).bind("invalid-form.validate",this.settings.invalidHandler),a(this.currentForm).find("[required], [data-rule-required], .required").attr("aria-required","true")},form:function(){return this.checkForm(),a.extend(this.submitted,this.errorMap),this.invalid=a.extend({},this.errorMap),this.valid()||a(this.currentForm).triggerHandler("invalid-form",[this]),this.showErrors(),this.valid()},checkForm:function(){this.prepareForm();for(var a=0,b=this.currentElements=this.elements();b[a];a++)this.check(b[a]);return this.valid()},element:function(b){var c=this.clean(b),d=this.validationTargetFor(c),e=!0;return this.lastElement=d,void 0===d?delete this.invalid[c.name]:(this.prepareElement(d),this.currentElements=a(d),e=this.check(d)!==!1,e?delete this.invalid[d.name]:this.invalid[d.name]=!0),a(b).attr("aria-invalid",!e),this.numberOfInvalids()||(this.toHide=this.toHide.add(this.containers)),this.showErrors(),e},showErrors:function(b){if(b){a.extend(this.errorMap,b),this.errorList=[];for(var c in b)this.errorList.push({message:b[c],element:this.findByName(c)[0]});this.successList=a.grep(this.successList,function(a){return!(a.name in b)})}this.settings.showErrors?this.settings.showErrors.call(this,this.errorMap,this.errorList):this.defaultShowErrors()},resetForm:function(){a.fn.resetForm&&a(this.currentForm).resetForm(),this.submitted={},this.lastElement=null,this.prepareForm(),this.hideErrors(),this.elements().removeClass(this.settings.errorClass).removeData("previousValue").removeAttr("aria-invalid")},numberOfInvalids:function(){return this.objectLength(this.invalid)},objectLength:function(a){var b,c=0;for(b in a)c++;return c},hideErrors:function(){this.hideThese(this.toHide)},hideThese:function(a){a.not(this.containers).text(""),this.addWrapper(a).hide()},valid:function(){return 0===this.size()},size:function(){return this.errorList.length},focusInvalid:function(){if(this.settings.focusInvalid)try{a(this.findLastActive()||this.errorList.length&&this.errorList[0].element||[]).filter(":visible").focus().trigger("focusin")}catch(b){}},findLastActive:function(){var b=this.lastActive;return b&&1===a.grep(this.errorList,function(a){return a.element.name===b.name}).length&&b},elements:function(){var b=this,c={};return a(this.currentForm).find("input, select, textarea").not(":submit, :reset, :image, [disabled], [readonly]").not(this.settings.ignore).filter(function(){return!this.name&&b.settings.debug&&window.console&&console.error("%o has no name assigned",this),this.name in c||!b.objectLength(a(this).rules())?!1:(c[this.name]=!0,!0)})},clean:function(b){return a(b)[0]},errors:function(){var b=this.settings.errorClass.split(" ").join(".");return a(this.settings.errorElement+"."+b,this.errorContext)},reset:function(){this.successList=[],this.errorList=[],this.errorMap={},this.toShow=a([]),this.toHide=a([]),this.currentElements=a([])},prepareForm:function(){this.reset(),this.toHide=this.errors().add(this.containers)},prepareElement:function(a){this.reset(),this.toHide=this.errorsFor(a)},elementValue:function(b){var c,d=a(b),e=b.type;return"radio"===e||"checkbox"===e?a("input[name='"+b.name+"']:checked").val():"number"===e&&"undefined"!=typeof b.validity?b.validity.badInput?!1:d.val():(c=d.val(),"string"==typeof c?c.replace(/\r/g,""):c)},check:function(b){b=this.validationTargetFor(this.clean(b));var c,d,e,f=a(b).rules(),g=a.map(f,function(a,b){return b}).length,h=!1,i=this.elementValue(b);for(d in f){e={method:d,parameters:f[d]};try{if(c=a.validator.methods[d].call(this,i,b,e.parameters),"dependency-mismatch"===c&&1===g){h=!0;continue}if(h=!1,"pending"===c)return void(this.toHide=this.toHide.not(this.errorsFor(b)));if(!c)return this.formatAndAdd(b,e),!1}catch(j){throw this.settings.debug&&window.console&&console.log("Exception occurred when checking element "+b.id+", check the '"+e.method+"' method.",j),j}}if(!h)return this.objectLength(f)&&this.successList.push(b),!0},customDataMessage:function(b,c){return a(b).data("msg"+c.charAt(0).toUpperCase()+c.substring(1).toLowerCase())||a(b).data("msg")},customMessage:function(a,b){var c=this.settings.messages[a];return c&&(c.constructor===String?c:c[b])},findDefined:function(){for(var a=0;a<arguments.length;a++)if(void 0!==arguments[a])return arguments[a];return void 0},defaultMessage:function(b,c){return this.findDefined(this.customMessage(b.name,c),this.customDataMessage(b,c),!this.settings.ignoreTitle&&b.title||void 0,a.validator.messages[c],"<strong>Warning: No message defined for "+b.name+"</strong>")},formatAndAdd:function(b,c){var d=this.defaultMessage(b,c.method),e=/\$?\{(\d+)\}/g;"function"==typeof d?d=d.call(this,c.parameters,b):e.test(d)&&(d=a.validator.format(d.replace(e,"{$1}"),c.parameters)),this.errorList.push({message:d,element:b,method:c.method}),this.errorMap[b.name]=d,this.submitted[b.name]=d},addWrapper:function(a){return this.settings.wrapper&&(a=a.add(a.parent(this.settings.wrapper))),a},defaultShowErrors:function(){var a,b,c;for(a=0;this.errorList[a];a++)c=this.errorList[a],this.settings.highlight&&this.settings.highlight.call(this,c.element,this.settings.errorClass,this.settings.validClass),this.showLabel(c.element,c.message);if(this.errorList.length&&(this.toShow=this.toShow.add(this.containers)),this.settings.success)for(a=0;this.successList[a];a++)this.showLabel(this.successList[a]);if(this.settings.unhighlight)for(a=0,b=this.validElements();b[a];a++)this.settings.unhighlight.call(this,b[a],this.settings.errorClass,this.settings.validClass);this.toHide=this.toHide.not(this.toShow),this.hideErrors(),this.addWrapper(this.toShow).show()},validElements:function(){return this.currentElements.not(this.invalidElements())},invalidElements:function(){return a(this.errorList).map(function(){return this.element})},showLabel:function(b,c){var d,e,f,g=this.errorsFor(b),h=this.idOrName(b),i=a(b).attr("aria-describedby");g.length?(g.removeClass(this.settings.validClass).addClass(this.settings.errorClass),g.html(c)):(g=a("<"+this.settings.errorElement+">").attr("id",h+"-error").addClass(this.settings.errorClass).html(c||""),d=g,this.settings.wrapper&&(d=g.hide().show().wrap("<"+this.settings.wrapper+"/>").parent()),this.labelContainer.length?this.labelContainer.append(d):this.settings.errorPlacement?this.settings.errorPlacement(d,a(b)):d.insertAfter(b),g.is("label")?g.attr("for",h):0===g.parents("label[for='"+h+"']").length&&(f=g.attr("id").replace(/(:|\.|\[|\])/g,"\\$1"),i?i.match(new RegExp("\\b"+f+"\\b"))||(i+=" "+f):i=f,a(b).attr("aria-describedby",i),e=this.groups[b.name],e&&a.each(this.groups,function(b,c){c===e&&a("[name='"+b+"']",this.currentForm).attr("aria-describedby",g.attr("id"))}))),!c&&this.settings.success&&(g.text(""),"string"==typeof this.settings.success?g.addClass(this.settings.success):this.settings.success(g,b)),this.toShow=this.toShow.add(g)},errorsFor:function(b){var c=this.idOrName(b),d=a(b).attr("aria-describedby"),e="label[for='"+c+"'], label[for='"+c+"'] *";return d&&(e=e+", #"+d.replace(/\s+/g,", #")),this.errors().filter(e)},idOrName:function(a){return this.groups[a.name]||(this.checkable(a)?a.name:a.id||a.name)},validationTargetFor:function(b){return this.checkable(b)&&(b=this.findByName(b.name)),a(b).not(this.settings.ignore)[0]},checkable:function(a){return/radio|checkbox/i.test(a.type)},findByName:function(b){return a(this.currentForm).find("[name='"+b+"']")},getLength:function(b,c){switch(c.nodeName.toLowerCase()){case"select":return a("option:selected",c).length;case"input":if(this.checkable(c))return this.findByName(c.name).filter(":checked").length}return b.length},depend:function(a,b){return this.dependTypes[typeof a]?this.dependTypes[typeof a](a,b):!0},dependTypes:{"boolean":function(a){return a},string:function(b,c){return!!a(b,c.form).length},"function":function(a,b){return a(b)}},optional:function(b){var c=this.elementValue(b);return!a.validator.methods.required.call(this,c,b)&&"dependency-mismatch"},startRequest:function(a){this.pending[a.name]||(this.pendingRequest++,this.pending[a.name]=!0)},stopRequest:function(b,c){this.pendingRequest--,this.pendingRequest<0&&(this.pendingRequest=0),delete this.pending[b.name],c&&0===this.pendingRequest&&this.formSubmitted&&this.form()?(a(this.currentForm).submit(),this.formSubmitted=!1):!c&&0===this.pendingRequest&&this.formSubmitted&&(a(this.currentForm).triggerHandler("invalid-form",[this]),this.formSubmitted=!1)},previousValue:function(b){return a.data(b,"previousValue")||a.data(b,"previousValue",{old:null,valid:!0,message:this.defaultMessage(b,"remote")})}},classRuleSettings:{required:{required:!0},email:{email:!0},url:{url:!0},date:{date:!0},dateISO:{dateISO:!0},number:{number:!0},digits:{digits:!0},creditcard:{creditcard:!0}},addClassRules:function(b,c){b.constructor===String?this.classRuleSettings[b]=c:a.extend(this.classRuleSettings,b)},classRules:function(b){var c={},d=a(b).attr("class");return d&&a.each(d.split(" "),function(){this in a.validator.classRuleSettings&&a.extend(c,a.validator.classRuleSettings[this])}),c},attributeRules:function(b){var c,d,e={},f=a(b),g=b.getAttribute("type");for(c in a.validator.methods)"required"===c?(d=b.getAttribute(c),""===d&&(d=!0),d=!!d):d=f.attr(c),/min|max/.test(c)&&(null===g||/number|range|text/.test(g))&&(d=Number(d)),d||0===d?e[c]=d:g===c&&"range"!==g&&(e[c]=!0);return e.maxlength&&/-1|2147483647|524288/.test(e.maxlength)&&delete e.maxlength,e},dataRules:function(b){var c,d,e={},f=a(b);for(c in a.validator.methods)d=f.data("rule"+c.charAt(0).toUpperCase()+c.substring(1).toLowerCase()),void 0!==d&&(e[c]=d);return e},staticRules:function(b){var c={},d=a.data(b.form,"validator");return d.settings.rules&&(c=a.validator.normalizeRule(d.settings.rules[b.name])||{}),c},normalizeRules:function(b,c){return a.each(b,function(d,e){if(e===!1)return void delete b[d];if(e.param||e.depends){var f=!0;switch(typeof e.depends){case"string":f=!!a(e.depends,c.form).length;break;case"function":f=e.depends.call(c,c)}f?b[d]=void 0!==e.param?e.param:!0:delete b[d]}}),a.each(b,function(d,e){b[d]=a.isFunction(e)?e(c):e}),a.each(["minlength","maxlength"],function(){b[this]&&(b[this]=Number(b[this]))}),a.each(["rangelength","range"],function(){var c;b[this]&&(a.isArray(b[this])?b[this]=[Number(b[this][0]),Number(b[this][1])]:"string"==typeof b[this]&&(c=b[this].replace(/[\[\]]/g,"").split(/[\s,]+/),b[this]=[Number(c[0]),Number(c[1])]))}),a.validator.autoCreateRanges&&(null!=b.min&&null!=b.max&&(b.range=[b.min,b.max],delete b.min,delete b.max),null!=b.minlength&&null!=b.maxlength&&(b.rangelength=[b.minlength,b.maxlength],delete b.minlength,delete b.maxlength)),b},normalizeRule:function(b){if("string"==typeof b){var c={};a.each(b.split(/\s/),function(){c[this]=!0}),b=c}return b},addMethod:function(b,c,d){a.validator.methods[b]=c,a.validator.messages[b]=void 0!==d?d:a.validator.messages[b],c.length<3&&a.validator.addClassRules(b,a.validator.normalizeRule(b))},methods:{required:function(b,c,d){if(!this.depend(d,c))return"dependency-mismatch";if("select"===c.nodeName.toLowerCase()){var e=a(c).val();return e&&e.length>0}return this.checkable(c)?this.getLength(b,c)>0:a.trim(b).length>0},email:function(a,b){return this.optional(b)||/^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(a)},url:function(a,b){return this.optional(b)||/^(https?|s?ftp):\/\/(((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:)*@)?(((\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5]))|((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.?)(:\d*)?)(\/((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)+(\/(([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)*)*)?)?(\?((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|[\uE000-\uF8FF]|\/|\?)*)?(#((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|\/|\?)*)?$/i.test(a)},date:function(a,b){return this.optional(b)||!/Invalid|NaN/.test(new Date(a).toString())},dateISO:function(a,b){return this.optional(b)||/^\d{4}[\/\-](0?[1-9]|1[012])[\/\-](0?[1-9]|[12][0-9]|3[01])$/.test(a)},number:function(a,b){return this.optional(b)||/^-?(?:\d+|\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/.test(a)},digits:function(a,b){return this.optional(b)||/^\d+$/.test(a)},creditcard:function(a,b){if(this.optional(b))return"dependency-mismatch";if(/[^0-9 \-]+/.test(a))return!1;var c,d,e=0,f=0,g=!1;if(a=a.replace(/\D/g,""),a.length<13||a.length>19)return!1;for(c=a.length-1;c>=0;c--)d=a.charAt(c),f=parseInt(d,10),g&&(f*=2)>9&&(f-=9),e+=f,g=!g;return e%10===0},minlength:function(b,c,d){var e=a.isArray(b)?b.length:this.getLength(b,c);return this.optional(c)||e>=d},maxlength:function(b,c,d){var e=a.isArray(b)?b.length:this.getLength(b,c);return this.optional(c)||d>=e},rangelength:function(b,c,d){var e=a.isArray(b)?b.length:this.getLength(b,c);return this.optional(c)||e>=d[0]&&e<=d[1]},min:function(a,b,c){return this.optional(b)||a>=c},max:function(a,b,c){return this.optional(b)||c>=a},range:function(a,b,c){return this.optional(b)||a>=c[0]&&a<=c[1]},equalTo:function(b,c,d){var e=a(d);return this.settings.onfocusout&&e.unbind(".validate-equalTo").bind("blur.validate-equalTo",function(){a(c).valid()}),b===e.val()},remote:function(b,c,d){if(this.optional(c))return"dependency-mismatch";var e,f,g=this.previousValue(c);return this.settings.messages[c.name]||(this.settings.messages[c.name]={}),g.originalMessage=this.settings.messages[c.name].remote,this.settings.messages[c.name].remote=g.message,d="string"==typeof d&&{url:d}||d,g.old===b?g.valid:(g.old=b,e=this,this.startRequest(c),f={},f[c.name]=b,a.ajax(a.extend(!0,{url:d,mode:"abort",port:"validate"+c.name,dataType:"json",data:f,context:e.currentForm,success:function(d){var f,h,i,j=d===!0||"true"===d;e.settings.messages[c.name].remote=g.originalMessage,j?(i=e.formSubmitted,e.prepareElement(c),e.formSubmitted=i,e.successList.push(c),delete e.invalid[c.name],e.showErrors()):(f={},h=d||e.defaultMessage(c,"remote"),f[c.name]=g.message=a.isFunction(h)?h(b):h,e.invalid[c.name]=!0,e.showErrors(f)),g.valid=j,e.stopRequest(c,j)}},d)),"pending")}}}),a.format=function(){throw"$.format has been deprecated. Please use $.validator.format instead."};var b,c={};a.ajaxPrefilter?a.ajaxPrefilter(function(a,b,d){var e=a.port;"abort"===a.mode&&(c[e]&&c[e].abort(),c[e]=d)}):(b=a.ajax,a.ajax=function(d){var e=("mode"in d?d:a.ajaxSettings).mode,f=("port"in d?d:a.ajaxSettings).port;return"abort"===e?(c[f]&&c[f].abort(),c[f]=b.apply(this,arguments),c[f]):b.apply(this,arguments)}),a.extend(a.fn,{validateDelegate:function(b,c,d){return this.bind(c,function(c){var e=a(c.target);return e.is(b)?d.apply(e,arguments):void 0})}})});
// From https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/keys
if (!Object.keys) {
  Object.keys = (function () {
    'use strict';
    var hasOwnProperty = Object.prototype.hasOwnProperty,
        hasDontEnumBug = !({toString: null}).propertyIsEnumerable('toString'),
        dontEnums = [
          'toString',
          'toLocaleString',
          'valueOf',
          'hasOwnProperty',
          'isPrototypeOf',
          'propertyIsEnumerable',
          'constructor'
        ],
        dontEnumsLength = dontEnums.length;

    return function (obj) {
      if (typeof obj !== 'object' && (typeof obj !== 'function' || obj === null)) {
        throw new TypeError('Object.keys called on non-object');
      }

      var result = [], prop, i;

      for (prop in obj) {
        if (hasOwnProperty.call(obj, prop)) {
          result.push(prop);
        }
      }

      if (hasDontEnumBug) {
        for (i = 0; i < dontEnumsLength; i++) {
          if (hasOwnProperty.call(obj, dontEnums[i])) {
            result.push(dontEnums[i]);
          }
        }
      }
      return result;
    };
  }());
}
if(typeof String.prototype.trim !== 'function') {
  String.prototype.trim = function() {
    return this.replace(/^\s+|\s+$/g, ''); 
  }
}
Date.now = Date.now || function() { return +new Date; };
/* mean mode median */
function mean(m) {
    var sum = 0;
    for (i = 0; i < m.length; i++) {
        sum += m[i];
    }
    return sum / m.length;
}
function median(m) {
    m.sort() ;
    var middle = Math.floor(m.length/2);
    if (m.length%2 == 1) {
        return m[middle];
    } else {
        console.log((m[middle-1] + m[middle]) / 2.0) ;
        return (m[middle-1] + m[middle]) / 2.0;
    }
}
function mode(ary) {
    var counter = {};
    var mode = [];
    var max = 0;
    for (var i in ary) {
        if (!(ary[i] in counter))
            counter[ary[i]] = 0;
        counter[ary[i]]++;
 
        if (counter[ary[i]] == max) 
            mode.push(ary[i]);
        else if (counter[ary[i]] > max) {
            max = counter[ary[i]];
            mode = [ary[i]];
        }
    }
    return mode; 
}
