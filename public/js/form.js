$(function() {
  

    

    $('.attachment_type').on('change', function(e) {
        let attachmentType = $(this).val();
        if (attachmentType == 'sector') {
            $('#exclude_org_ids').hide();
        }

        if (attachmentType == 'organisation') {
            $('.sector-div').hide();
            $('span.select2-container').show();
        } else {
            $('.sector-div').show();
            $('span.select2-container').hide();
        }
    });


    // ui tabs
    var tabs = $('.tabs').tab();

    // document action
    $('*[name=formType]').change(function() {
        $('.form-document-type').slideToggle();
        if ($(this).val() == 'categorised') {
            $('.levels').attr('required', '');
        } else {
            $('.levels').removeAttr('required');
        }
    });

    // enable form builder
    $('#ffForm').ffForm({
        dragIcon: '<i class="fa fa-arrows"></i>',
        closeIcon: '<i class="fa fa-times"></i>',
        minIcon: '<i class="fa fa-caret-down"></i>',
        maxIcon: '<i class="fa fa-caret-right"></i>',
        form: $('#liberty-form'),

        complete: function(json) {

            if (JSON.parse(json).length == 0) {
                alert('You must add at least one field');
                $('a.nav-link.fields').tab('show');
                //tabs.tab('option','active', 0);
            } else {
                // first check we have form options fields
                // validate other form
                // if fail go to it
                // tabs.tab('option','active', 1);
                //$('.tab2').tab('show');
                
                if ($("#formOptions").valid()) {

                    var is_category = $('input[name="formType"]:checked').val();
                    var level_1 = $('#level1').val();
                    var formName = $('#formName').val();
                    var errorFound = 0;

                    if (formName=="") {
                        $('#formName').addClass("border-danger");
                        alert('Form name is required inside form options');
                        errorFound++;
                    }

                    if (is_category=="categorised" && level_1==null) {
                        $('#level1').addClass("border-danger");
                        alert('Please select a Form Type Level inside form options');
                        errorFound++;
                    }

                    if(errorFound>0){
                        return false;
                    }

                    

                    var selObj = document.getElementById('exclude_org_select'),
                        txtTextObj = document.getElementById('exclude_ids'),
                        org_selected = [];

                    for (var i = 0, l = selObj.options.length; i < l; i++) {
                        //Check if the option is selected
                        if (selObj.options[i].selected) {
                            org_selected.push(selObj.options[i].value);
                        }
                    }
                    txtTextObj.value = org_selected.join(',');
                    $("#exclude_org_select").remove();

                    //tabs.tab('option','active', 0);
                    var options = $("#formOptions").serializeArray();
                    var o = {};
                    var formId = '';
                    $.each(options, function(i, v) {
                        if (v.name == 'ID')
                            formId = v.value;
                        else
                            o[v.name] = v.value;
                    })
                    json = JSON.parse(json);
                    o['fields'] = json;

                    // fileuploads status
                    if (!o['fileUploads'])
                        o['fileUploads'] = 'disallow';

                    // notify settings
                    if (!o['notify_group_user'])
                        o['notify_group_user'] = 0;
                    if (!o['notify_liberty_admin'])
                        o['notify_liberty_admin'] = 0;

                    let pathname = location.pathname.split('/');
                    let url = ($('input[name="is_public"]').val() === '1' || pathname[pathname.length - 1] === 'public') ? '/public-forms' : '/forms';

                    let selectedArray = [];
                    $('.sectors-selected').each(function(e) {
                        selectedArray.push($(this).val());
                    });

                    o.selected_sectors = selectedArray;

                    if ($('input[name="attachment_type"]:checked').val() == 'organisation') {
                        if (o.organisation > 0) {
                            o.exclude_ids = '';
                        }

                        if (o.organisation >= 0) {
                            o.sector = '';
                            o.selected_sectors = [];
                        }
                    } else if (o.selected_sectors != undefined && o.selected_sectors.length > 0) {
                        o.organisation = -1;
                        o.exclude_ids = '';
                    }

                    // check if its an existing form
                    if (formId) {
                        $.ajax({
                            type: "PUT",
                            url: url + "/" + formId,
                            data: { form: JSON.stringify(o) }
                        }).done(function(data) {
                            window.location.href = "/forms/updated";
                        });
                    } else {
                        $.post(url, { form: JSON.stringify(o) })
                            .done(function(data) {
                                window.location.href = "/forms/success";
                            });
                    }
                }
            }

        }
    });


    $('#ffGenerate2').on('click', function(e) {
        e.preventDefault();
        $('#ffGenerate').trigger('click');
    });


});
