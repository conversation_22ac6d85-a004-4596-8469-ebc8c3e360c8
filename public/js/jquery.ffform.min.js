
(function($){var ffForm;var methods={init:function(options){if(!options)options={};var fieldsObj={Standard_fields:{text:{label:{type:'text',placeholder:'Label',validate:true},name:{type:'name',placeholder:'Name'},placeholder:{type:'text',placeholder:'Placeholder'},Class:{type:'text',value:('class'in options?options.Class:'form-control'),placeholder:'Class'},Required:{type:'checkbox'},Condition:{type:'checkboxDepends'},dependsOn:{type:'depends'},Admin_only:{type:'checkbox'},width:{type:'select',options:{'col-md-12':'full','col-md-6':'half','col-md-4':'third','col-md-3':'quarter'}}},textarea:{label:{type:'text',placeholder:'Label',validate:true},name:{type:'name',placeholder:'Name'},placeholder:{type:'text',placeholder:'Placeholder'},Class:{type:'text',value:('class'in options?options.Class:'form-control'),placeholder:'Class'},Required:{type:'checkbox'},Condition:{type:'checkboxDepends'},dependsOn:{type:'depends'},Admin_only:{type:'checkbox'},width:{type:'select',options:{'col-md-12':'full','col-md-6':'half','col-md-4':'third','col-md-3':'quarter'}}},email:{label:{type:'text',placeholder:'Label',validate:true},name:{type:'name',placeholder:'Name'},placeholder:{type:'text',placeholder:'Placeholder'},Class:{type:'text',value:('class'in options?options.Class:'form-control'),placeholder:'Class'},Required:{type:'checkbox'},Condition:{type:'checkboxDepends'},dependsOn:{type:'depends'},Admin_only:{type:'checkbox'},width:{type:'select',options:{'col-md-12':'full','col-md-6':'half','col-md-4':'third','col-md-3':'quarter'}}},select:{label:{type:'text',placeholder:'Label',validate:true},name:{type:'name',placeholder:'Name'},options:{type:'textarea',placeholder:'one, two, three',validate:true},Class:{type:'text',value:('class'in options?options.Class:'form-control'),placeholder:'Class'},Required:{type:'checkbox'},Condition:{type:'checkboxDepends'},dependsOn:{type:'depends'},Admin_Only:{type:'checkbox'},Multi_Select:{type:'checkbox'},width:{type:'select',options:{'col-md-12':'full','col-md-6':'half','col-md-4':'third','col-md-3':'quarter'}}},checkbox:{label:{type:'text',placeholder:'Label',validate:true},name:{type:'name',placeholder:'Name'},Class:{type:'text',value:('class'in options?options.Class:''),placeholder:'Class'},Required:{type:'checkbox'},Condition:{type:'checkboxDepends'},dependsOn:{type:'depends'},adminOnly:{type:'checkbox'},width:{type:'select',options:{'col-md-12':'full','col-md-6':'half','col-md-4':'third','col-md-3':'quarter'}}},radios:{label:{type:'text',placeholder:'Label',validate:true},name:{type:'name',placeholder:'Name'},options:{type:'textarea',placeholder:'one, two, three',validate:true},Class:{type:'text',value:'',placeholder:'Class'},Required:{type:'checkbox'},Condition:{type:'checkboxDepends'},dependsOn:{type:'depends'},adminOnly:{type:'checkbox'},width:{type:'select',options:{'col-md-12':'full','col-md-6':'half','col-md-4':'third','col-md-3':'quarter'}}},date:{label:{type:'text',placeholder:'Label',validate:true},name:{type:'name',placeholder:'Name'},placeholder:{type:'text',placeholder:'Placeholder'},Class:{type:'text',value:('class'in options?options.Class:'form-control'),placeholder:'Class'},format:{type:'text',value:('dd/mm/yy'),placeholder:'dd/mm/yy',validate:true},Required:{type:'checkbox'},Condition:{type:'checkboxDepends'},dependsOn:{type:'depends'},adminOnly:{type:'checkbox'},width:{type:'select',options:{'col-md-12':'full','col-md-6':'half','col-md-4':'third','col-md-3':'quarter'}}},header:{label:{type:'text',placeholder:'Header'},width:{type:'select',options:{'col-md-12':'full','col-md-6':'half','col-md-4':'third','col-md-3':'quarter'}}},linebreak:{},freetext:{label:{type:'textarea',placeholder:'freetext',},width:{type:'select',options:{'col-md-12':'full','col-md-6':'half','col-md-4':'third','col-md-3':'quarter'}}},fileprompt:{label:{type:'textarea',placeholder:'freetext',value:'You can upload file(s) at the end of this form, after saving.'},width:{type:'select',options:{'col-md-12':'full','col-md-6':'half','col-md-4':'third','col-md-3':'quarter'}}},sum:{label:{type:'text',placeholder:'Label',validate:true},name:{type:'name',placeholder:'Name'},placeholder:{type:'text',placeholder:'Placeholder'},Class:{type:'text',value:('class'in options?options.Class:'form-control disabled'),placeholder:'Class'},Required:{type:'checkbox'},sum:{type:'checkboxSum'},sumOn:{type:'sum'},adminOnly:{type:'checkbox'},width:{type:'select',options:{'col-md-12':'full','col-md-6':'half','col-md-4':'third','col-md-3':'quarter'}},conditionalText:{type:'checkboxConditionalText'},conditionalTextOn:{type:'conditionalText'},},average:{label:{type:'text',placeholder:'Label',validate:true},name:{type:'name',placeholder:'Name'},placeholder:{type:'text',placeholder:'Placeholder'},Class:{type:'text',value:('class'in options?options.Class:'form-control'),placeholder:'Class'},Required:{type:'checkbox'},average:{type:'checkboxAverage'},averageOn:{type:'average'},adminOnly:{type:'checkbox'},width:{type:'select',options:{'col-md-12':'full','col-md-6':'half','col-md-4':'third','col-md-3':'quarter'}},type:{type:'select',options:{'mean':'mean','mode':'mode','median':'median'}},conditionalText:{type:'checkboxConditionalText'},conditionalTextOn:{type:'conditionalText'},},number:{label:{type:'text',placeholder:'Label',validate:true},name:{type:'name',placeholder:'Name'},placeholder:{type:'text',placeholder:'Placeholder'},Class:{type:'text',value:('class'in options?options.Class:'form-control'),placeholder:'Class'},Required:{type:'checkbox'},Condition:{type:'checkboxDepends'},dependsOn:{type:'depends'},adminOnly:{type:'checkbox'},width:{type:'select',options:{'col-md-12':'full','col-md-6':'half','col-md-4':'third','col-md-3':'quarter'}}},country:{label:{type:'text',placeholder:'Label',validate:true},name:{type:'name',placeholder:'Name'},options:{type:'hidden',value:"United Kingdom, United States, Afghanistan, Albania, Algeria, Andorra, Angola, Antigua & Deps, Argentina, Armenia, Australia, Austria, Azerbaijan, Bahamas, Bahrain, Bangladesh, Barbados, Belarus, Belgium, Belize, Benin, Bhutan, Bolivia, Bosnia Herzegovina, Botswana, Brazil, Brunei, Bulgaria, Burkina, Burma, Burundi, Cambodia, Cameroon, Canada, Cape Verde, Central African Rep, Chad, Chile, People's Republic of China, Republic of China, Colombia, Comoros, Democratic Republic of the Congo, Republic of the Congo, Costa Rica,, Croatia, Cuba, Cyprus, Czech Republic, Danzig, Denmark, Djibouti, Dominica, Dominican Republic, East Timor, Ecuador, Egypt, El Salvador, Equatorial Guinea, Eritrea, Estonia, Ethiopia, Fiji, Finland, France, Gabon, Gaza Strip, The Gambia, Georgia, Germany, Ghana, Greece, Grenada, Guatemala, Guinea, Guinea-Bissau, Guyana, Haiti, Holy Roman Empire, Honduras, Hungary, Iceland, India, Indonesia, Iran, Iraq, Republic of Ireland, Israel, Italy, Ivory Coast, Jamaica, Japan, Jonathanland, Jordan, Kazakhstan, Kenya, Kiribati, North Korea, South Korea, Kosovo, Kuwait, Kyrgyzstan, Laos, Latvia, Lebanon, Lesotho, Liberia, Libya, Liechtenstein, Lithuania, Luxembourg, Macedonia, Madagascar, Malawi, Malaysia, Maldives, Mali, Malta, Marshall Islands, Mauritania, Mauritius, Mexico, Micronesia, Moldova, Monaco, Mongolia, Montenegro, Morocco, Mount Athos, Mozambique, Namibia, Nauru, Nepal, Newfoundland, Netherlands, New Zealand, Nicaragua, Niger, Nigeria, Norway, Oman, Ottoman Empire, Pakistan, Palau, Panama, Papua New Guinea, Paraguay, Peru, Philippines, Poland, Portugal, Prussia, Qatar, Romania, Rome, Russian Federation, Rwanda, St Kitts & Nevis, St Lucia, Saint Vincent & the, Grenadines, Samoa, San Marino, Sao Tome & Principe, Saudi Arabia, Senegal, Serbia, Seychelles, Sierra Leone, Singapore, Slovakia, Slovenia, Solomon Islands, Somalia, South Africa, Spain, Sri Lanka, Sudan, Suriname, Swaziland, Sweden, Switzerland, Syria, Tajikistan, Tanzania, Thailand, Togo, Tonga, Trinidad & Tobago, Tunisia, Turkey, Turkmenistan, Tuvalu, Uganda, Ukraine, United Arab Emirates, Uruguay, Uzbekistan, Vanuatu, Vatican City, Venezuela, Vietnam, Yemen, Zambia, Zimbabwe",validate:true},Class:{type:'text',value:('class'in options?options.Class:'form-control'),placeholder:'Class'},Required:{type:'checkbox'},Condition:{type:'checkboxDepends'},dependsOn:{type:'depends'},Admin_Only:{type:'checkbox'},Multi_Select:{type:'checkbox'},width:{type:'select',options:{'col-md-12':'full','col-md-6':'half','col-md-4':'third','col-md-3':'quarter'}}}},Chartable_accident_reporting_fields:{date_of_accident:{label:{type:'hidden',placeholder:'Label',validate:true,value:'Date of accident'},name:{type:'name',placeholder:'Name',value:'date-of-accident'},placeholder:{type:'text',placeholder:'Placeholder'},Class:{type:'text',value:('class'in options?options.Class:'form-control'),placeholder:'Class'},format:{type:'text',value:('dd/mm/yy'),placeholder:'dd/mm/yy',validate:true},Required:{type:'checkbox'},Condition:{type:'checkboxDepends'},dependsOn:{type:'depends'},adminOnly:{type:'checkbox'},width:{type:'select',options:{'col-md-12':'full','col-md-6':'half','col-md-4':'third','col-md-3':'quarter'}}},time_period_of_accident:{label:{type:'hidden',placeholder:'Label',validate:true,value:'Time Period of Accident'},name:{type:'name',placeholder:'Name',value:'time-period-of-accident'},options:{type:'textarea',placeholder:'one, two, three',validate:true},Class:{type:'text',value:('class'in options?options.Class:'form-control'),placeholder:'Class'},Required:{type:'checkbox'},Condition:{type:'checkboxDepends'},dependsOn:{type:'depends'},Admin_Only:{type:'checkbox'},Multi_Select:{type:'checkbox'},width:{type:'select',options:{'col-md-12':'full','col-md-6':'half','col-md-4':'third','col-md-3':'quarter'}}},where_did_the_accident_happen:{label:{type:'hidden',placeholder:'Label',validate:true,value:'Where did the accident happen?'},name:{type:'name',placeholder:'Name',value:'where-did-the-accident-happen'},options:{type:'textarea',placeholder:'one, two, three',validate:true},Class:{type:'text',value:('class'in options?options.Class:'form-control'),placeholder:'Class'},Required:{type:'checkbox'},Condition:{type:'checkboxDepends'},dependsOn:{type:'depends'},Admin_Only:{type:'checkbox'},Multi_Select:{type:'checkbox'},width:{type:'select',options:{'col-md-12':'full','col-md-6':'half','col-md-4':'third','col-md-3':'quarter'}}},branch:{label:{type:'hidden',placeholder:'Label',validate:true,value:'Branch where accident happened?'},name:{type:'name',placeholder:'Name',value:'branch'},options:{type:'hidden',placeholder:'additional options, eg. one, two, three',validate:false},Class:{type:'text',value:('class'in options?options.Class:'form-control'),placeholder:'Class'},Required:{type:'checkbox'},Condition:{type:'checkboxDepends'},dependsOn:{type:'depends'},Admin_Only:{type:'checkbox'},Multi_Select:{type:'checkbox'},width:{type:'select',options:{'col-md-12':'full','col-md-6':'half','col-md-4':'third','col-md-3':'quarter'}}},injured_person_type:{label:{type:'hidden',placeholder:'Label',validate:true,value:'Injured Person Type'},name:{type:'name',placeholder:'Name',value:'injured-person-type'},options:{type:'textarea',placeholder:'one, two, three',validate:true},Class:{type:'text',value:('class'in options?options.Class:'form-control'),placeholder:'Class'},Required:{type:'checkbox'},Condition:{type:'checkboxDepends'},dependsOn:{type:'depends'},Admin_Only:{type:'checkbox'},Multi_Select:{type:'checkbox'},width:{type:'select',options:{'col-md-12':'full','col-md-6':'half','col-md-4':'third','col-md-3':'quarter'}}},injured_person_name:{label:{type:'hidden',placeholder:'Label',validate:true,value:'Injured Person Name'},name:{type:'name',placeholder:'Name',value:'injured-person-name'},placeholder:{type:'text',placeholder:'Placeholder'},Class:{type:'text',value:('class'in options?options.Class:'form-control'),placeholder:'Class'},Required:{type:'checkbox'},Condition:{type:'checkboxDepends'},dependsOn:{type:'depends'},Admin_only:{type:'checkbox'},width:{type:'select',options:{'col-md-12':'full','col-md-6':'half','col-md-4':'third','col-md-3':'quarter'}}},severity_of_the_injury:{label:{type:'hidden',placeholder:'Label',validate:true,value:'Severity of the injury'},name:{type:'name',placeholder:'Name',value:'severity-of-the-injury'},options:{type:'textarea',placeholder:'one, two, three',validate:true},Class:{type:'text',value:('class'in options?options.Class:'form-control'),placeholder:'Class'},Required:{type:'checkbox'},Condition:{type:'checkboxDepends'},dependsOn:{type:'depends'},Admin_Only:{type:'checkbox'},Multi_Select:{type:'checkbox'},width:{type:'select',options:{'col-md-12':'full','col-md-6':'half','col-md-4':'third','col-md-3':'quarter'}}},accident_category:{label:{type:'hidden',placeholder:'Label',validate:true,value:'Accident Category'},name:{type:'name',placeholder:'Name',value:'accident-category'},options:{type:'textarea',placeholder:'one, two, three',validate:true},Class:{type:'text',value:('class'in options?options.Class:'form-control'),placeholder:'Class'},Required:{type:'checkbox'},Condition:{type:'checkboxDepends'},dependsOn:{type:'depends'},Admin_Only:{type:'checkbox'},Multi_Select:{type:'checkbox'},width:{type:'select',options:{'col-md-12':'full','col-md-6':'half','col-md-4':'third','col-md-3':'quarter'}}},action_taken:{label:{type:'hidden',placeholder:'Label',validate:true,value:'Action taken'},name:{type:'name',placeholder:'Name',value:'action-taken'},options:{type:'textarea',placeholder:'one, two, three',validate:true},Class:{type:'text',value:('class'in options?options.Class:'form-control'),placeholder:'Class'},Required:{type:'checkbox'},Condition:{type:'checkboxDepends'},dependsOn:{type:'depends'},Admin_Only:{type:'checkbox'},Multi_Select:{type:'checkbox'},width:{type:'select',options:{'col-md-12':'full','col-md-6':'half','col-md-4':'third','col-md-3':'quarter'}}},nature_of_injury:{label:{type:'hidden',placeholder:'Label',validate:true,value:'Nature of injury'},name:{type:'name',placeholder:'Name',value:'nature-of-injury'},options:{type:'textarea',placeholder:'one, two, three',validate:true},Class:{type:'text',value:('class'in options?options.Class:'form-control'),placeholder:'Class'},Required:{type:'checkbox'},Condition:{type:'checkboxDepends'},dependsOn:{type:'depends'},Admin_Only:{type:'checkbox'},Multi_Select:{type:'checkbox'},width:{type:'select',options:{'col-md-12':'full','col-md-6':'half','col-md-4':'third','col-md-3':'quarter'}}},area_details:{label:{type:'hidden',placeholder:'Label',validate:true,value:'Area details'},name:{type:'name',placeholder:'Name',value:'area-details'},options:{type:'textarea',placeholder:'one, two, three',validate:true},Class:{type:'text',value:('class'in options?options.Class:'form-control'),placeholder:'Class'},Required:{type:'checkbox'},Condition:{type:'checkboxDepends'},dependsOn:{type:'depends'},Admin_Only:{type:'checkbox'},Multi_Select:{type:'checkbox'},width:{type:'select',options:{'col-md-12':'full','col-md-6':'half','col-md-4':'third','col-md-3':'quarter'}}},signature_pad:{name:{type:'hidden',placeholder:'Name',value:'signature-pad'},Class:{type:'hidden',value:('class'in options?options.Class:'form-control'),placeholder:'Class'},Required:{type:'checkbox'}}}};settings=$.extend({Class:'form-control',form:'',admin:false,dragIcon:'+',closeIcon:'X',minIcon:'&ddarr;',maxIcon:'&rrarr;',validate:true,before:function(fieldsObj){return fieldsObj;},complete:function(json){},},options);fieldsObj=settings.before.call(this,fieldsObj);if(!$('#ffForm').length)return;methods.html(settings,fieldsObj);$('#ffForm #ffFields').hide();ffForm=typeof(settings.form)=='object'?settings.form:$('form').first();ffForm.addClass('ffForm-target');var prePopContent=ffForm.html().trim();if(prePopContent){ffForm.html('');methods.load(prePopContent);}
ffForm.append('<button id="ffGenerate" class="btn btn-blue">Save</button>');if(jQuery.ui){ffForm.sortable({handle:"h2",items:"> div",receive:function(event,ui){var pos=ui.item.index();var type=$('a',ui.item).data('field-type');$(ui.sender).sortable('cancel');methods.add(type,pos);}}).disableSelection();$('#ffForm ul').sortable({connectWith:".ffForm-target"}).disableSelection();}
$('#ffGenerate').click(function(){methods.generate(ffForm)});$(document).on('click','input[data-field-type=checkboxDepends]',function(){$("#depends").remove();if($(this).is(':checked')){var dependForm=methods.dependForm();$(this).closest('li').after(dependForm);}else{$('.depends',$(this).closest('div')).val('');$('label strong',$(this).closest('div')).remove();}});$(document).on('click','#dependApply',function(e){e.preventDefault();var el=$('#dependSelect').val();var val=$('#dependValue').val();$('.depends',$(this).closest('div')).val(el+':'+val);$('.checkboxDepends label span',$(this).closest('div')).after(' <strong>('+el+':'+val+')</strong>');$("#depends").remove();});$(document).on('click','input[data-field-type=checkboxSum]',function(){$("#sum").remove();if($(this).is(':checked')){var sumForm=methods.sumForm();$(this).closest('li').after(sumForm);}else{$('.sum',$(this).closest('div')).val('');$('label strong',$(this).closest('div')).remove();}});$(document).on('click','#sumApply',function(e){e.preventDefault();var el=$('#sumSelect').val();$('.sum',$(this).closest('div')).val(el);$('.checkboxSum label span',$(this).closest('div')).after(' <strong>('+el+')</strong>');$("#sum").remove();});$(document).on('click','input[data-field-type=checkboxConditionalText]',function(){$("#conditionalText").remove();if($(this).is(':checked')){var conditionalTextForm=methods.conditionalTextForm();$(this).closest('li').after(conditionalTextForm);}else{$('.conditionalText',$(this).closest('div')).val('');$('label strong',$(this).closest('div')).remove();}});$(document).on('click','#conditionalTextApply',function(e){e.preventDefault();var el=[];$.each($('#conditionalText li'),function(i,v){var from=$('[name=from]',v).val();var to=$('[name=to]',v).val();var text=$('[name=text]',v).val();el.push(from+':'+to+':'+text);});el=el.join();$('.conditionalText',$(this).closest('div')).val(el);$('.checkboxConditionalText label span',$(this).closest('div')).after(' <strong>('+el+')</strong>');$("#conditionalText").remove();});$(document).on('click','#conditionalText .add',function(e){e.preventDefault();$(this).parent().after(methods.conditionalTextFormRow());$(this).hide();});$(document).on('click','#conditionalText .rm',function(e){e.preventDefault();$('.add',$(this).parent().prev()).show();$(this).parent().remove();});$('#ffGenerate').click(function(){methods.generate(ffForm)});$(document).on('click','.ffForm-target input[type=checkbox]',function(e){$(this).val($(this).is(':checked')?1:0);});$(document).on('click','.delField',function(e){e.preventDefault();methods.deleter($(this));});$(document).on('input','*[data-field-name="label"]',function(e){e.preventDefault();methods.title($(this));});$(document).on('click','.minField',function(e){e.preventDefault();methods.minMax($(this))});return this.each(function(){$('a',this).click(function(e){e.preventDefault();methods.add($(this))});});},dependForm:function(){var html='<p id="depends"><select id="dependSelect" class="form-control">';var fields=$('ul',ffForm);$.each(fields,function(index,value){var name=$('input[data-field-name="name"]',value).val();var label=$('input[data-field-name="label"]',value).val();html+='<option value="'+name+'">'+label+'</option>';});html+='</select><br><input type="text" class="form-control" id="dependValue" placeholder="enter a trigger value"><br><a id="dependApply" href="#" class="btn btn-success">Apply</a></p>';return html;},sumForm:function(){var html='<p id="sum"><select id="sumSelect" multiple>';var fields=$('ul',ffForm);$.each(fields,function(index,value){var name=$('input[data-field-name="name"]',value).val();var label=$('input[data-field-name="label"]',value).val();var type=$(this).data('field-type');if(type=='number'||type=='select')
html+='<option value="'+name+'">'+label+'</option>';});html+='</select><a id="sumApply" class="text-primary" href="#">Apply</a></p>';return html;},conditionalTextForm:function(){var html='<ul id="conditionalText">';html+=methods.conditionalTextFormRow(true);html+='<a id="conditionalTextApply" href="#">Apply</a></ul>';return html;},conditionalTextFormRow:function(first){var html='<li class="condition">';html+='<input type="text" name="from" placeholder="From (e.g 13)">';html+='<input type="text" name="to" placeholder="To (e.g 15)">';html+='<input type="text" name="text" placeholder="text value">';if(first!==true)html+='<a href="#" class="rm">-</a>';html+='<a href="#" class="add">+</a></li>';return html;},add:function(el,pos){if(el.data){var name_of_field=el.data('field-type').substr(2);if(name_of_field.length>=2&&name_of_field.substr(0,3)=='sum'&&$('#liberty-form').find('ul[data-field-type=number]').length<=1){}}
if($('#liberty-form').find('ul[data-field-type='+name_of_field+']').length!=0&&$('#ui-id-6').find('a[data-field-type=ff'+name_of_field+']').length!=0){alert('Chartable accident reporting fields can only be added once in a form.');return;}
var field=typeof el=='string'?el:el.data('field-type');$.each($('#ffFields #'+field+' input, #ffFields #'+field+' select'),function(k,v){$(this).attr('name',$(this).attr('name'));});if(!pos){if(el.data!==undefined){$('<div>'+$('#ffFields #'+field).html()+'</div>').insertBefore('#ffGenerate');}else{ffForm.prepend('<div>'+$('#ffFields #'+field).html()+'</div>');}
var inserted=$('div:eq(0) [data-field-name="label"]',ffForm);}else{if($('div:eq('+pos+')',ffForm).length){$('<div>'+$('#ffFields #'+field).html()+'</div>').insertBefore($('div:eq('+pos+')',ffForm));var inserted=$('div:eq('+pos+') [data-field-name="label"]',ffForm);}else{$('<div>'+$('#ffFields #'+field).html()+'</div>').insertBefore($('button',ffForm));var inserted=$('div:last [data-field-name="label"]',ffForm);}}
methods.title(inserted);var _test=document.createElement('input');if(!('placeholder'in _test)){$('*[placeholder]').each(function(){var $self=$(this);if($self.val()===''){$self.val($self.attr('placeholder'));}});}},load:function(jsonString){var formElements=JSON.parse(jsonString);$.each(formElements.reverse(),function(index,value){$.each(value,function(field,fields){methods.add('ff'+field);if(fields[0])
$('div:eq(0) h2 strong',ffForm).html(fields[0].value);$.each(fields,function(i,v){var input=$('div:eq(0)',ffForm);$('li:eq('+i+') input, li:eq('+i+') textarea, li:eq('+i+') select',input).attr('name',v.name).val(v.value);if(v.value==1){$('li:eq('+i+') input[type=checkbox]',input).prop('checked',true);}
if(v.name.indexOf("dependsOn")>-1&&v.value){$('li:eq('+(i-1)+') label span',input).after(' <strong>('+v.value+')</strong>');}
if(v.name.indexOf("sum")>-1&&v.value){$('li:eq('+(i-1)+') label span',input).after(' <strong>('+v.value+')</strong>');}
if(v.name.indexOf("average")>-1&&v.value){$('li:eq('+(i-1)+') label span',input).after(' <strong>('+v.value+')</strong>');}
if(v.name.indexOf("conditional")>-1&&v.value){$('li:eq('+(i-1)+') label span',input).after(' <strong>('+v.value+')</strong>');}});});});},deleter:function(el){el.closest('div').remove();},title:function(el){var val=el.val();if(el.closest('div').find('h2 strong').length){el.closest('div').find('h2 strong').html(val==''?'':val);if(val=='Date of accident'||val=='Time Period of Accident'||val=='Where did the accident happen?'||val=='Branch where accident happened?'||val=='Injured Person Type'||val=='Severity of the injury'||val=='Accident Category'||val=='Action taken'||val=='Nature of injury'||val=='Area details'||val=='Injured Person Name'){el.parent().next().find('input').val(val.replace(/ +/g,'-').replace(/[^a-z0-9\-]/gmi,"").toLowerCase());}else{el.parent().next().find('input').val(val.replace(/ +/g,'-').replace(/[^a-z0-9\-]/gmi,"").toLowerCase()+'-'+Math.floor(Date.now()/1000));}}},minMax:function(el){el.parent().next('ul').slideToggle('fast',function(){if($(this).is(':visible')){$(el).html(settings.minIcon);}else{$(el).html(settings.maxIcon);}});},generate:function(form){$.validator.setDefaults({invalidHandler:function(event,validator){alert("Some of the required fields have not been filled. Form has NOT been saved");$.each(validator.errorList,function(k,v){$('input[name='+v.element.name+']').closest('div').addClass('error');$('textarea[name='+v.element.name+']').closest('div').addClass('error');$('#liberty-form div.error').find('ul').show();if(v.element.name=='label'||v.element.name=='options'){$('div.tabs').tabs('option','active',0);}});$("html, body").animate({scrollTop:0},"slow");},submitHandler:function(){$('div',form).removeClass('error')
var fields=$('ul',form);var fieldArr=[];$.each(fields,function(index,value){var field=$(value).data('field-type');var obj=[];var f={};$.each($('li',value),function(i,v){obj.push({name:$('input, textarea, select',$(v)).attr('name'),value:$('input, textarea, select',$(v)).val()});});f[field]=obj;fieldArr.push(f);});settings.complete.call(this,JSON.stringify(fieldArr));},errorPlacement:function(error,element){error.insertAfter(element);}});form.validate({ignore:""});},html:function(settings,fieldsObj){var html='<div class="ffaccordion">';$.each(fieldsObj,function(index,value){html+='<h3>'+index.replace(/_/gi,' ')+'</h3><div><ul>';$.each(Object.keys(value),function(i,v){html+='<li><a href="#" data-field-type="ff'+v+'">'+v.toUpperCase().replace('_',' ')+'</a></li>';});html+='</ul></div>';});html+='</div>';html+='<ul id="ffFields">';$.each(fieldsObj,function(i,v){$.each(Object.keys(v),function(index,value){html+='<li id="ff'+value+'"><h2><span class="handle col-xs-1">'+settings.dragIcon+'</span><p class="col-xs-10"><strong></strong> ('+value+')</p> <a href="#" class="minField">'+settings.maxIcon+'</a> <a href="#" class="delField">'+settings.closeIcon+'</a></h2><ul data-field-type="'+value+'">';$.each(fieldsObj[i][value],function(name,field){html+='<li>';html+=methods[field.type](name,field);html+='</li>';});html+='</ul></li>';});});html+='</ul>';$('#ffForm').html(html);$(".ffaccordion").accordion({collapsible:true,heightStyle:"content"});},populate:function(json){var data=JSON.parse(json);$.each(data,function(index,value){if($('[name="'+index+'[]"]').length){switch($('[name="'+index+'[]"]').prop('tagName')){case'SELECT':{$('[name="'+index+'[]"] option').each(function(i,v){var opt=$(this);$.each(value,function(x,y){if(opt.val()==y){opt.attr('selected','selected');}});});break;}}}
if($('[name='+index+']').length){switch($('[name='+index+']').prop('tagName')){case'INPUT':{switch($('[name='+index+']').attr('type')){case'text':case'email':{$('[name='+index+']').val(value);break;}
case'radio':{$('[name='+index+']').each(function(i,v){if($(this).val()==value){$(this).prop('checked',true);}});break;}
case'checkbox':{if(value!=0)
$('[name='+index+']').prop('checked',true);break;}}
break;}
case'SELECT':{$('[name='+index+'] option').each(function(i,v){if($(this).val()==value){$(this).attr('selected','selected');}});break;}
case'TEXTAREA':{$('[name='+index+']').val(value);break;}
case'time_period_of_accident':{$('[name='+index+'] option').each(function(i,v){if($(this).val()==value){$(this).attr('selected','selected');}});break;}}}});$("[data-depends-on!='0'][data-depends-on!=''][data-depends-on]").each(function(i,v){if($(this).val()!='')
$(this).closest('p').show();});$(".datepicker").each(function(i,v){if($(this).val()!=''){}
var timestamp=new Date($(this).val()*1000);$(this).val(timestamp.getDate()+'/'+parseInt(timestamp.getMonth()+1)+'/'+timestamp.getFullYear());});},build:function(fields,form){fields=JSON.parse(fields);form.html('');var rules={};$.each(fields,function(index,value){$.each(value,function(i,v){var colwidth='col-md-12';$.each(v,function(x,y){if(y.name.indexOf("width")>-1&&y.value){colwidth=y.value;}});var container=i=='header'?'div':'p';var html='<'+container+' class="'+colwidth+'">';var method='__'+i;html+=methods[method](v);html+='</'+container+'>';form.append(html);if(method=='__number'){rules[v[1].value]={required:true,number:true}}
$.each(v,function(x,y){if(y.name.indexOf("dependsOn")>-1&&y.value){var target=y.value.split(':');$(document).on('change keyup','[name='+target[0]+']',function(){var val=$(this).val();var ignore=false;if($(this).is(':checkbox')){if($(this).prop('checked')){val=target[1];}else{ignore=true;}}
if(val==target[1]&&!ignore){$('[name='+v[1].value+']',form).closest('p').show();}else{$('[name='+v[1].value+']',form).closest('p').hide();}});}});$.each(v,function(x,y){if(y.name.indexOf("sum")>-1&&y.value){var targets=y.value.split(',');$.each(targets,function(i,t){$(document).on('change','[name='+t+']',function(){var total=0;$.each(targets,function(a,b){var val=$('[name='+b+']').val();total=parseFloat(total)+parseFloat(val);});if(!isNaN(parseFloat(total))&&isFinite(total))
$('[name='+v[1].value+']',form).val(total);var conditionalText=$('[name='+v[1].value+']',form).data('text');if(conditionalText){conditionalText=conditionalText.split(',');$.each(conditionalText,function(q,r){var condition=r.split(':');if(total>=condition[0]&&total<=condition[1]){$('[name='+v[1].value+']',form).val(condition[2]);}});}});});}});$.each(v,function(x,y){if(y.name.indexOf("average")>-1&&y.value){var targets=y.value.split(',');var action=$('[name='+v[1].value+']',form).data('calc');$.each(targets,function(i,t){$(document).on('change','[name='+t+']',function(){var total=0;var avArr=[];$.each(targets,function(a,b){var val=$('[name='+b+']').val();avArr.push(parseFloat(val));});total=window[action](avArr);if(!isNaN(parseFloat(total))&&isFinite(total))
$('[name='+v[1].value+']',form).val(total);var conditionalText=$('[name='+v[1].value+']',form).data('text');if(conditionalText){conditionalText=conditionalText.split(',');$.each(conditionalText,function(q,r){var condition=r.split(':');if(total>=condition[0]&&total<=condition[1]){$('[name='+v[1].value+']',form).val(condition[2]);}});}});});}});});});form.append('<p class="col-md-12"><button class="btn btn-default save_btn">Save</button></p>');if(settings.validate==true){if(form.get(0).tagName=='FORM'){form.validate({rules:rules});}else{form.closest('form').validate({rules:rules});}}
$(".datepicker").each(function(){var format=$(this).data('date-format');$(this).datepicker({changeMonth:true,changeYear:true,dateFormat:'dd/mm/yy',onSelect:function(dateText,inst){var name=$(this).attr('name');dateText=dateText.split("/");var timestamp=new Date(dateText[1]+"/"+dateText[0]+"/"+dateText[2]).getTime();$('[name='+name+'].alt').val(timestamp/1000);}});});$("[data-depends-on!='0'][data-depends-on!=''][data-depends-on]",form).closest('p').hide();},text:function(name,obj){var class_label=name=='Class'?'Class (Space separated)<br>':'';return class_label+'<input type="text" data-field-type="text" data-field-name="'+name+'" name="'+name+'" value="'+(obj.value?obj.value:'')+'" class="form-control" placeholder="'+(obj.placeholder?obj.placeholder:'')+'" '+(obj.validate?'required':'')+'>';},hidden:function(name,obj){return'<input type="hidden" data-field-type="text" data-field-name="'+name+'" name="'+name+'" value="'+(obj.value?obj.value:'')+'" class="form-control" placeholder="'+(obj.placeholder?obj.placeholder:'')+'" '+(obj.validate?'required':'')+'>';},name:function(name,obj){return'<input type="hidden" data-field-type="text" data-field-name="'+name+'" name="'+name+'" value="'+(obj.value?obj.value:'')+'" class="form-control" placeholder="'+(obj.placeholder?obj.placeholder:'')+'">';},depends:function(name,obj){return'<input type="hidden" data-field-type="depends" data-field-name="'+name+'" name="'+name+'" value="'+(obj.value?obj.value:'')+'" class="depends form-control" placeholder="'+(obj.placeholder?obj.placeholder:'')+'" '+(obj.validate?'required':'')+' disabled>';},sum:function(name,obj){return'<input type="hidden" data-field-type="sum" data-field-name="'+name+'" name="'+name+'" value="'+(obj.value?obj.value:'')+'" class="sum form-control" placeholder="'+(obj.placeholder?obj.placeholder:'')+'" '+(obj.validate?'required':'')+' disabled>';},average:function(name,obj){return'<input type="hidden" data-field-type="sum" data-field-name="'+name+'" name="'+name+'" value="'+(obj.value?obj.value:'')+'" class="sum form-control" placeholder="'+(obj.placeholder?obj.placeholder:'')+'" '+(obj.validate?'required':'')+' disabled>';},conditionalText:function(name,obj){return'<input type="hidden" data-field-type="conditionalText" data-field-name="'+name+'" name="'+name+'" value="'+(obj.value?obj.value:'')+'" class="conditionalText form-control" placeholder="'+(obj.placeholder?obj.placeholder:'')+'" '+(obj.validate?'required':'')+' disabled>';},checkbox:function(name,obj){return'<p class="checkbox"><label><input type="checkbox" data-field-type="checkbox" data-field-name="'+name+'" name="'+name+'" value="0"> '+name.replace("_"," ")+'</label></p>';},checkboxDepends:function(name,obj){return'<p class="checkbox checkboxDepends"><label><input type="checkbox" data-field-type="checkboxDepends" data-field-name="'+name+'" name="'+name+'" value="0"> <span>'+name+'</span></label></p>';},checkboxSum:function(name,obj){return'<p class="checkbox checkboxSum"><label><input type="checkbox" data-field-type="checkboxSum" data-field-name="'+name+'" name="'+name+'" value="0"> <span>Fields to use</span></label></p>';},checkboxAverage:function(name,obj){return'<p class="checkbox checkboxSum"><label><input type="checkbox" data-field-type="checkboxSum" data-field-name="'+name+'" name="'+name+'" value="0"> <span>Fields to use</span></label></p>';},checkboxConditionalText:function(name,obj){return'<p class="checkbox checkboxConditionalText"><label><input type="checkbox" data-field-type="checkboxConditionalText" data-field-name="'+name+'" name="'+name+'" value="0"> <span>Conditional Text</span></label></p>';},textarea:function(name,obj){return'<textarea type="text" data-field-type="textarea" data-field-name="'+name+'" name="'+name+'" class="form-control" placeholder="'+(obj.placeholder?obj.placeholder:'')+'" '+(obj.validate?'required':'')+'>'+(obj.value?obj.value:'')+'</textarea>';},select:function(name,obj){var html=name.charAt(0).toUpperCase()+(name.replace("_"," ")).slice(1)+' <select class="form-control" size="1" name="'+name+'" data-field-type="select" data-field-name="'+name+'">';$.each(obj.options,function(i,v){html+='<option value="'+i+'">'+v+'</option>';});html+='</select>';return html;},__text:function(obj){var html='';var adminonly=settings.admin==false&&obj[7].value==1;if(adminonly)html+='<span style="display: none">';html+='<label>'+obj[0].value+' '+(obj[4].value==1?'*':'')+'</label><input type="text" name="'+obj[1].value+'" placeholder="'+obj[2].value+'" class="'+obj[3].value+'" '+(obj[4].value==1&&settings.validate==true?'required':'')+' data-depends-on="'+(obj[6].value?obj[6].value:'')+'">';if(adminonly)html+='</span>';return html;},__number:function(obj){var html='';var adminonly=settings.admin==false&&obj[7].value==1;if(adminonly)html+='<span style="display: none">';html+='<label>'+obj[0].value+' '+(obj[4].value==1?'*':'')+'</label><input type="text" name="'+obj[1].value+'" placeholder="'+obj[2].value+'" class="'+obj[3].value+'" '+(obj[4].value==1&&settings.validate==true?'required':'')+' data-depends-on="'+(obj[6].value?obj[6].value:'')+'">';if(adminonly)html+='</span>';return html;},__sum:function(obj){var html='';var adminonly=settings.admin==false&&obj[7].value==1;if(adminonly)html+='<span style="display: none">';html+='<label>'+obj[0].value+' '+(obj[4].value==1?'*':'')+'</label><input type="text" name="'+obj[1].value+'" placeholder="'+obj[2].value+'" class="'+obj[3].value+'" '+(obj[4].value==1&&settings.validate==true?'required':'')+' data-sum="'+(obj[6].value?obj[6].value:'')+'"  data-text="'+(obj[10].value?obj[10].value:'')+'">';if(adminonly)html+='</span>';return html;},__average:function(obj){var html='';var adminonly=settings.admin==false&&obj[7].value==1;if(adminonly)html+='<span style="display: none">';html+='<label>'+obj[0].value+' '+(obj[4].value==1?'*':'')+'</label><input type="text" name="'+obj[1].value+'" placeholder="'+obj[2].value+'" class="'+obj[3].value+'" '+(obj[4].value==1&&settings.validate==true?'required':'')+' data-sum="'+(obj[6].value?obj[6].value:'')+'" data-calc="'+(obj[9].value?obj[9].value:'mean')+'" data-text="'+(obj[11].value?obj[11].value:'')+'">';if(adminonly)html+='</span>';return html;},__textarea:function(obj){var html='';var adminonly=settings.admin==false&&obj[7].value==1;if(adminonly)html+='<span style="display: none">';html+='<label>'+obj[0].value+' '+(obj[4].value==1?'*':'')+'</label><textarea name="'+obj[1].value+'" placeholder="'+obj[2].value+'" class="'+obj[3].value+'" '+(obj[4].value==1&&settings.validate==true?'required':'')+' data-depends-on="'+(obj[6].value?obj[6].value:'')+'"></textarea>';if(adminonly)html+='</span>';return html;},__email:function(obj){var html='';var adminonly=settings.admin==false&&obj[7].value==1;if(adminonly)html+='<span style="display: none">';html+='<label>'+obj[0].value+' '+(obj[4].value==1?'*':'')+'</label><input type="email" name="'+obj[1].value+'" placeholder="'+obj[2].value+'" class="'+obj[3].value+'" '+(obj[4].value==1&&settings.validate==true?'required':'')+' data-depends-on="'+(obj[6].value?obj[6].value:'')+'">';if(adminonly)html+='</span>';return html;},__select:function(obj){var html='';var adminonly=settings.admin==false&&obj[7].value==1;if(adminonly)html+='<span style="display: none">';html+='<label>'+obj[0].value+' '+(obj[4].value==1?'*':'')+'</label><select name="'+obj[1].value+(obj[8].value==1?'[]':'')+'" class="'+obj[3].value+'" '+(obj[4].value==1&&settings.validate==true?'required':'')+' data-depends-on="'+(obj[6].value?obj[6].value:'')+'" '+(obj[8].value==1?'multiple':'')+'>';html+='<option>Please Select</option>';$.each(obj[2].value.split(','),function(i,v){html+='<option value="'+v.trim()+'">'+v.trim()+'</option>';});html+='</select>';if(adminonly)html+='</span>';return html;},__country:function(obj){var html='';var adminonly=settings.admin==false&&obj[7].value==1;if(adminonly)html+='<span style="display: none">';html+='<label>'+obj[0].value+' '+(obj[4].value==1?'*':'')+'</label><select name="'+obj[1].value+(obj[8].value==1?'[]':'')+'" class="'+obj[3].value+'" '+(obj[4].value==1&&settings.validate==true?'required':'')+' data-depends-on="'+(obj[6].value?obj[6].value:'')+'" '+(obj[8].value==1?'multiple':'')+'>';html+='<option>Please Select</option>';$.each(obj[2].value.split(','),function(i,v){html+='<option value="'+v.trim()+'">'+v.trim()+'</option>';});html+='</select>';if(adminonly)html+='</span>';return html;},__time_period_of_accident:function(obj){var html='';var adminonly=settings.admin==false&&obj[7].value==1;if(adminonly)html+='<span style="display: none">';html+='<label>'+obj[0].value+' '+(obj[4].value==1?'*':'')+'</label><select name="'+obj[1].value+(obj[8].value==1?'[]':'')+'" class="'+obj[3].value+'" '+(obj[4].value==1&&settings.validate==true?'required':'')+' data-depends-on="'+(obj[6].value?obj[6].value:'')+'" '+(obj[8].value==1?'multiple':'')+'>';html+='<option>Please Select</option>';$.each(obj[2].value.split(','),function(i,v){html+='<option value="'+v.trim()+'">'+v.trim()+'</option>';});html+='</select>';if(adminonly)html+='</span>';return html;},__where_did_the_accident_happen:function(obj){var html='';var adminonly=settings.admin==false&&obj[7].value==1;if(adminonly)html+='<span style="display: none">';html+='<label>'+obj[0].value+' '+(obj[4].value==1?'*':'')+'</label><select name="'+obj[1].value+(obj[8].value==1?'[]':'')+'" class="'+obj[3].value+'" '+(obj[4].value==1&&settings.validate==true?'required':'')+' data-depends-on="'+(obj[6].value?obj[6].value:'')+'" '+(obj[8].value==1?'multiple':'')+'>';html+='<option>Please Select</option>';$.each(obj[2].value.split(','),function(i,v){html+='<option value="'+v.trim()+'">'+v.trim()+'</option>';});html+='</select>';if(adminonly)html+='</span>';return html;},__branch:function(obj){var html='';var adminonly=settings.admin==false&&obj[7].value==1;if(adminonly)html+='<span style="display: none">';html+='<label>'+obj[0].value+' '+(obj[4].value==1?'*':'')+'</label><select name="'+obj[1].value+(obj[8].value==1?'[]':'')+'" class="'+obj[3].value+'" '+(obj[4].value==1&&settings.validate==true?'required':'')+' data-depends-on="'+(obj[6].value?obj[6].value:'')+'" '+(obj[8].value==1?'multiple':'')+'>';html+='<option>Please Select</option>';html+='</select>';if(adminonly)html+='</span>';return html;},__injured_person_type:function(obj){var html='';var adminonly=settings.admin==false&&obj[7].value==1;if(adminonly)html+='<span style="display: none">';html+='<label>'+obj[0].value+' '+(obj[4].value==1?'*':'')+'</label><select name="'+obj[1].value+(obj[8].value==1?'[]':'')+'" class="'+obj[3].value+'" '+(obj[4].value==1&&settings.validate==true?'required':'')+' data-depends-on="'+(obj[6].value?obj[6].value:'')+'" '+(obj[8].value==1?'multiple':'')+'>';html+='<option>Please Select</option>';$.each(obj[2].value.split(','),function(i,v){html+='<option value="'+v.trim()+'">'+v.trim()+'</option>';});html+='</select>';if(adminonly)html+='</span>';return html;},__injured_person_name:function(obj){var html='';var adminonly=settings.admin==false&&obj[7].value==1;if(adminonly)html+='<span style="display: none">';html+='<label>'+obj[0].value+' '+(obj[4].value==1?'*':'')+'</label><input type="text" name="'+obj[1].value+'" placeholder="'+obj[2].value+'" class="'+obj[3].value+'" '+(obj[4].value==1&&settings.validate==true?'required':'')+' data-depends-on="'+(obj[6].value?obj[6].value:'')+'">';if(adminonly)html+='</span>';return html;},__severity_of_the_injury:function(obj){var html='';var adminonly=settings.admin==false&&obj[7].value==1;if(adminonly)html+='<span style="display: none">';html+='<label>'+obj[0].value+' '+(obj[4].value==1?'*':'')+'</label><select name="'+obj[1].value+(obj[8].value==1?'[]':'')+'" class="'+obj[3].value+'" '+(obj[4].value==1&&settings.validate==true?'required':'')+' data-depends-on="'+(obj[6].value?obj[6].value:'')+'" '+(obj[8].value==1?'multiple':'')+'>';html+='<option>Please Select</option>';$.each(obj[2].value.split(','),function(i,v){html+='<option value="'+v.trim()+'">'+v.trim()+'</option>';});html+='</select>';if(adminonly)html+='</span>';return html;},__accident_category:function(obj){var html='';var adminonly=settings.admin==false&&obj[7].value==1;if(adminonly)html+='<span style="display: none">';html+='<label>'+obj[0].value+' '+(obj[4].value==1?'*':'')+'</label><select name="'+obj[1].value+(obj[8].value==1?'[]':'')+'" class="'+obj[3].value+'" '+(obj[4].value==1&&settings.validate==true?'required':'')+' data-depends-on="'+(obj[6].value?obj[6].value:'')+'" '+(obj[8].value==1?'multiple':'')+'>';html+='<option>Please Select</option>';$.each(obj[2].value.split(','),function(i,v){html+='<option value="'+v.trim()+'">'+v.trim()+'</option>';});html+='</select>';if(adminonly)html+='</span>';return html;},__action_taken:function(obj){var html='';var adminonly=settings.admin==false&&obj[7].value==1;if(adminonly)html+='<span style="display: none">';html+='<label>'+obj[0].value+' '+(obj[4].value==1?'*':'')+'</label><select name="'+obj[1].value+(obj[8].value==1?'[]':'')+'" class="'+obj[3].value+'" '+(obj[4].value==1&&settings.validate==true?'required':'')+' data-depends-on="'+(obj[6].value?obj[6].value:'')+'" '+(obj[8].value==1?'multiple':'')+'>';html+='<option>Please Select</option>';$.each(obj[2].value.split(','),function(i,v){html+='<option value="'+v.trim()+'">'+v.trim()+'</option>';});html+='</select>';if(adminonly)html+='</span>';return html;},__nature_of_injury:function(obj){var html='';var adminonly=settings.admin==false&&obj[7].value==1;if(adminonly)html+='<span style="display: none">';html+='<label>'+obj[0].value+' '+(obj[4].value==1?'*':'')+'</label><select name="'+obj[1].value+(obj[8].value==1?'[]':'')+'" class="'+obj[3].value+'" '+(obj[4].value==1&&settings.validate==true?'required':'')+' data-depends-on="'+(obj[6].value?obj[6].value:'')+'" '+(obj[8].value==1?'multiple':'')+'>';html+='<option>Please Select</option>';$.each(obj[2].value.split(','),function(i,v){html+='<option value="'+v.trim()+'">'+v.trim()+'</option>';});html+='</select>';if(adminonly)html+='</span>';return html;},__area_details:function(obj){var html='';var adminonly=settings.admin==false&&obj[7].value==1;if(adminonly)html+='<span style="display: none">';html+='<label>'+obj[0].value+' '+(obj[4].value==1?'*':'')+'</label><select name="'+obj[1].value+(obj[8].value==1?'[]':'')+'" class="'+obj[3].value+'" '+(obj[4].value==1&&settings.validate==true?'required':'')+' data-depends-on="'+(obj[6].value?obj[6].value:'')+'" '+(obj[8].value==1?'multiple':'')+'>';html+='<option>Please Select</option>';$.each(obj[2].value.split(','),function(i,v){html+='<option value="'+v.trim()+'">'+v.trim()+'</option>';});html+='</select>';if(adminonly)html+='</span>';return html;},__radios:function(obj){var html='';var adminonly=settings.admin==false&&obj[7].value==1;if(adminonly)html+='<span style="display: none">';html+='<label>'+obj[0].value+' '+(obj[4].value==1?'*':'')+'</label>';$.each(obj[2].value.split(','),function(i,v){html+='<label class="radio-inline"><input type="radio" name="'+obj[1].value+'" value="'+v.trim()+'" '+(obj[4].value==1&&settings.validate==true?'required':'')+' data-depends-on="'+(obj[6].value?obj[6].value:'')+'"> '+v.trim()+'</label>';});html+='<label for="'+obj[1].value+'" class="error" style="display:none">* Please pick an option above</label>';if(adminonly)html+='</span>';return html;},__checkbox:function(obj){var html='';var adminonly=settings.admin==false&&obj[6].value==1;if(adminonly)html+='<span style="display: none">';html+='<label>'+obj[0].value+' '+(obj[3].value==1?'*':'')+'</label><input type="checkbox" name="'+obj[1].value+'" class="'+obj[2].value+'" '+(obj[3].value==1&&settings.validate==true?'required':'')+' data-depends-on="'+(obj[5].value?obj[5].value:'')+'" value="1">';if(adminonly)html+='</span>';return html;},__date:function(obj){var html='';var adminonly=settings.admin==false&&obj[8].value==1;if(adminonly)html+='<span style="display: none">';html+='<label>'+obj[0].value+' '+(obj[5].value==1?'*':'')+'</label><input type="text" name="'+obj[1].value+'" placeholder="'+obj[2].value+'" class="datepicker '+obj[3].value+'" data-date-format="'+obj[4].value+'" '+(obj[5].value==1&&settings.validate==true?'required':'')+' data-depends-on="'+(obj[7].value?obj[7].value:'')+'">';html+='<input type="hidden" name="'+obj[1].value+'" class="'+obj[3].value+' alt">'
if(adminonly)html+='</span>';return html;},__date_of_accident:function(obj){var html='';var adminonly=settings.admin==false&&obj[8].value==1;if(adminonly)html+='<span style="display: none">';html+='<label>'+obj[0].value+' '+(obj[5].value==1?'*':'')+'</label><input type="text" name="'+obj[1].value+'" placeholder="'+obj[2].value+'" class="datepicker '+obj[3].value+'" data-date-format="'+obj[4].value+'" '+(obj[5].value==1&&settings.validate==true?'required':'')+' data-depends-on="'+(obj[7].value?obj[7].value:'')+'">';html+='<input type="hidden" name="'+obj[1].value+'" class="'+obj[3].value+' alt">'
if(adminonly)html+='</span>';return html;},__linebreak:function(obj){return'<hr>';},__freetext:function(obj){return obj[0].value;},__fileprompt:function(obj){return obj[0].value;},__header:function(obj){return'<h2 class="ffheader">'+obj[0].value+'</h2><hr>';},__signature_pad:function(obj){var html='';var adminonly=settings.admin==false&&(obj[8]!==undefined&&obj[8].value==1);if(adminonly)html+='<span style="display: none">';html+='<label class="sig-pad">Signature</label>';if(adminonly)html+='</span>';return html;}}
$.fn.ffForm=function(method){$.fn.ffForm.methods=methods;if(methods[method]){return methods[method].apply(this,Array.prototype.slice.call(arguments,1));}else if(typeof method==='object'||!method){return methods.init.apply(this,arguments);}else{$.error('Method '+method+' does not exist');}};}(jQuery));!function(a){"function"==typeof define&&define.amd?define(["jquery"],a):a(jQuery)}(function(a){a.extend(a.fn,{validate:function(b){if(!this.length)return void(b&&b.debug&&window.console&&console.warn("Nothing selected, can't validate, returning nothing."));var c=a.data(this[0],"validator");return c?c:(this.attr("novalidate","novalidate"),c=new a.validator(b,this[0]),a.data(this[0],"validator",c),c.settings.onsubmit&&(this.validateDelegate(":submit","click",function(b){c.settings.submitHandler&&(c.submitButton=b.target),a(b.target).hasClass("cancel")&&(c.cancelSubmit=!0),void 0!==a(b.target).attr("formnovalidate")&&(c.cancelSubmit=!0)}),this.submit(function(b){function d(){var d,e;return c.settings.submitHandler?(c.submitButton&&(d=a("<input type='hidden'/>").attr("name",c.submitButton.name).val(a(c.submitButton).val()).appendTo(c.currentForm)),e=c.settings.submitHandler.call(c,c.currentForm,b),c.submitButton&&d.remove(),void 0!==e?e:!1):!0}return c.settings.debug&&b.preventDefault(),c.cancelSubmit?(c.cancelSubmit=!1,d()):c.form()?c.pendingRequest?(c.formSubmitted=!0,!1):d():(c.focusInvalid(),!1)})),c)},valid:function(){var b,c;return a(this[0]).is("form")?b=this.validate().form():(b=!0,c=a(this[0].form).validate(),this.each(function(){b=c.element(this)&&b})),b},removeAttrs:function(b){var c={},d=this;return a.each(b.split(/\s/),function(a,b){c[b]=d.attr(b),d.removeAttr(b)}),c},rules:function(b,c){var d,e,f,g,h,i,j=this[0];if(b)switch(d=a.data(j.form,"validator").settings,e=d.rules,f=a.validator.staticRules(j),b){case"add":a.extend(f,a.validator.normalizeRule(c)),delete f.messages,e[j.name]=f,c.messages&&(d.messages[j.name]=a.extend(d.messages[j.name],c.messages));break;case"remove":return c?(i={},a.each(c.split(/\s/),function(b,c){i[c]=f[c],delete f[c],"required"===c&&a(j).removeAttr("aria-required")}),i):(delete e[j.name],f)}return g=a.validator.normalizeRules(a.extend({},a.validator.classRules(j),a.validator.attributeRules(j),a.validator.dataRules(j),a.validator.staticRules(j)),j),g.required&&(h=g.required,delete g.required,g=a.extend({required:h},g),a(j).attr("aria-required","true")),g.remote&&(h=g.remote,delete g.remote,g=a.extend(g,{remote:h})),g}}),a.extend(a.expr[":"],{blank:function(b){return!a.trim(""+a(b).val())},filled:function(b){return!!a.trim(""+a(b).val())},unchecked:function(b){return!a(b).prop("checked")}}),a.validator=function(b,c){this.settings=a.extend(!0,{},a.validator.defaults,b),this.currentForm=c,this.init()},a.validator.format=function(b,c){return 1===arguments.length?function(){var c=a.makeArray(arguments);return c.unshift(b),a.validator.format.apply(this,c)}:(arguments.length>2&&c.constructor!==Array&&(c=a.makeArray(arguments).slice(1)),c.constructor!==Array&&(c=[c]),a.each(c,function(a,c){b=b.replace(new RegExp("\\{"+a+"\\}","g"),function(){return c})}),b)},a.extend(a.validator,{defaults:{messages:{},groups:{},rules:{},errorClass:"error",validClass:"valid",errorElement:"label",focusCleanup:!1,focusInvalid:!0,errorContainer:a([]),errorLabelContainer:a([]),onsubmit:!0,ignore:":hidden",ignoreTitle:!1,onfocusin:function(a){this.lastActive=a,this.settings.focusCleanup&&(this.settings.unhighlight&&this.settings.unhighlight.call(this,a,this.settings.errorClass,this.settings.validClass),this.hideThese(this.errorsFor(a)))},onfocusout:function(a){this.checkable(a)||!(a.name in this.submitted)&&this.optional(a)||this.element(a)},onkeyup:function(a,b){(9!==b.which||""!==this.elementValue(a))&&(a.name in this.submitted||a===this.lastElement)&&this.element(a)},onclick:function(a){a.name in this.submitted?this.element(a):a.parentNode.name in this.submitted&&this.element(a.parentNode)},highlight:function(b,c,d){"radio"===b.type?this.findByName(b.name).addClass(c).removeClass(d):a(b).addClass(c).removeClass(d)},unhighlight:function(b,c,d){"radio"===b.type?this.findByName(b.name).removeClass(c).addClass(d):a(b).removeClass(c).addClass(d)}},setDefaults:function(b){a.extend(a.validator.defaults,b)},messages:{required:"This field is required.",remote:"Please fix this field.",email:"Please enter a valid email address.",url:"Please enter a valid URL.",date:"Please enter a valid date.",dateISO:"Please enter a valid date ( ISO ).",number:"Please enter a valid number.",digits:"Please enter only digits.",creditcard:"Please enter a valid credit card number.",equalTo:"Please enter the same value again.",maxlength:a.validator.format("Please enter no more than {0} characters."),minlength:a.validator.format("Please enter at least {0} characters."),rangelength:a.validator.format("Please enter a value between {0} and {1} characters long."),range:a.validator.format("Please enter a value between {0} and {1}."),max:a.validator.format("Please enter a value less than or equal to {0}."),min:a.validator.format("Please enter a value greater than or equal to {0}.")},autoCreateRanges:!1,prototype:{init:function(){function b(b){var c=a.data(this[0].form,"validator"),d="on"+b.type.replace(/^validate/,""),e=c.settings;e[d]&&!this.is(e.ignore)&&e[d].call(c,this[0],b)}this.labelContainer=a(this.settings.errorLabelContainer),this.errorContext=this.labelContainer.length&&this.labelContainer||a(this.currentForm),this.containers=a(this.settings.errorContainer).add(this.settings.errorLabelContainer),this.submitted={},this.valueCache={},this.pendingRequest=0,this.pending={},this.invalid={},this.reset();var c,d=this.groups={};a.each(this.settings.groups,function(b,c){"string"==typeof c&&(c=c.split(/\s/)),a.each(c,function(a,c){d[c]=b})}),c=this.settings.rules,a.each(c,function(b,d){c[b]=a.validator.normalizeRule(d)}),a(this.currentForm).validateDelegate(":text, [type='password'], [type='file'], select, textarea, [type='number'], [type='search'] ,[type='tel'], [type='url'], [type='email'], [type='datetime'], [type='date'], [type='month'], [type='week'], [type='time'], [type='datetime-local'], [type='range'], [type='color'], [type='radio'], [type='checkbox']","focusin focusout keyup",b).validateDelegate("select, option, [type='radio'], [type='checkbox']","click",b),this.settings.invalidHandler&&a(this.currentForm).bind("invalid-form.validate",this.settings.invalidHandler),a(this.currentForm).find("[required], [data-rule-required], .required").attr("aria-required","true")},form:function(){return this.checkForm(),a.extend(this.submitted,this.errorMap),this.invalid=a.extend({},this.errorMap),this.valid()||a(this.currentForm).triggerHandler("invalid-form",[this]),this.showErrors(),this.valid()},checkForm:function(){this.prepareForm();for(var a=0,b=this.currentElements=this.elements();b[a];a++)this.check(b[a]);return this.valid()},element:function(b){var c=this.clean(b),d=this.validationTargetFor(c),e=!0;return this.lastElement=d,void 0===d?delete this.invalid[c.name]:(this.prepareElement(d),this.currentElements=a(d),e=this.check(d)!==!1,e?delete this.invalid[d.name]:this.invalid[d.name]=!0),a(b).attr("aria-invalid",!e),this.numberOfInvalids()||(this.toHide=this.toHide.add(this.containers)),this.showErrors(),e},showErrors:function(b){if(b){a.extend(this.errorMap,b),this.errorList=[];for(var c in b)this.errorList.push({message:b[c],element:this.findByName(c)[0]});this.successList=a.grep(this.successList,function(a){return!(a.name in b)})}this.settings.showErrors?this.settings.showErrors.call(this,this.errorMap,this.errorList):this.defaultShowErrors()},resetForm:function(){a.fn.resetForm&&a(this.currentForm).resetForm(),this.submitted={},this.lastElement=null,this.prepareForm(),this.hideErrors(),this.elements().removeClass(this.settings.errorClass).removeData("previousValue").removeAttr("aria-invalid")},numberOfInvalids:function(){return this.objectLength(this.invalid)},objectLength:function(a){var b,c=0;for(b in a)c++;return c},hideErrors:function(){this.hideThese(this.toHide)},hideThese:function(a){a.not(this.containers).text(""),this.addWrapper(a).hide()},valid:function(){return 0===this.size()},size:function(){return this.errorList.length},focusInvalid:function(){if(this.settings.focusInvalid)try{a(this.findLastActive()||this.errorList.length&&this.errorList[0].element||[]).filter(":visible").focus().trigger("focusin")}catch(b){}},findLastActive:function(){var b=this.lastActive;return b&&1===a.grep(this.errorList,function(a){return a.element.name===b.name}).length&&b},elements:function(){var b=this,c={};return a(this.currentForm).find("input, select, textarea").not(":submit, :reset, :image, [disabled], [readonly]").not(this.settings.ignore).filter(function(){return!this.name&&b.settings.debug&&window.console&&console.error("%o has no name assigned",this),this.name in c||!b.objectLength(a(this).rules())?!1:(c[this.name]=!0,!0)})},clean:function(b){return a(b)[0]},errors:function(){var b=this.settings.errorClass.split(" ").join(".");return a(this.settings.errorElement+"."+b,this.errorContext)},reset:function(){this.successList=[],this.errorList=[],this.errorMap={},this.toShow=a([]),this.toHide=a([]),this.currentElements=a([])},prepareForm:function(){this.reset(),this.toHide=this.errors().add(this.containers)},prepareElement:function(a){this.reset(),this.toHide=this.errorsFor(a)},elementValue:function(b){var c,d=a(b),e=b.type;return"radio"===e||"checkbox"===e?a("input[name='"+b.name+"']:checked").val():"number"===e&&"undefined"!=typeof b.validity?b.validity.badInput?!1:d.val():(c=d.val(),"string"==typeof c?c.replace(/\r/g,""):c)},check:function(b){b=this.validationTargetFor(this.clean(b));var c,d,e,f=a(b).rules(),g=a.map(f,function(a,b){return b}).length,h=!1,i=this.elementValue(b);for(d in f){e={method:d,parameters:f[d]};try{if(c=a.validator.methods[d].call(this,i,b,e.parameters),"dependency-mismatch"===c&&1===g){h=!0;continue}if(h=!1,"pending"===c)return void(this.toHide=this.toHide.not(this.errorsFor(b)));if(!c)return this.formatAndAdd(b,e),!1}catch(j){throw this.settings.debug&&window.console&&console.log("Exception occurred when checking element "+b.id+", check the '"+e.method+"' method.",j),j}}if(!h)return this.objectLength(f)&&this.successList.push(b),!0},customDataMessage:function(b,c){return a(b).data("msg"+c.charAt(0).toUpperCase()+c.substring(1).toLowerCase())||a(b).data("msg")},customMessage:function(a,b){var c=this.settings.messages[a];return c&&(c.constructor===String?c:c[b])},findDefined:function(){for(var a=0;a<arguments.length;a++)if(void 0!==arguments[a])return arguments[a];return void 0},defaultMessage:function(b,c){return this.findDefined(this.customMessage(b.name,c),this.customDataMessage(b,c),!this.settings.ignoreTitle&&b.title||void 0,a.validator.messages[c],"<strong>Warning: No message defined for "+b.name+"</strong>")},formatAndAdd:function(b,c){var d=this.defaultMessage(b,c.method),e=/\$?\{(\d+)\}/g;"function"==typeof d?d=d.call(this,c.parameters,b):e.test(d)&&(d=a.validator.format(d.replace(e,"{$1}"),c.parameters)),this.errorList.push({message:d,element:b,method:c.method}),this.errorMap[b.name]=d,this.submitted[b.name]=d},addWrapper:function(a){return this.settings.wrapper&&(a=a.add(a.parent(this.settings.wrapper))),a},defaultShowErrors:function(){var a,b,c;for(a=0;this.errorList[a];a++)c=this.errorList[a],this.settings.highlight&&this.settings.highlight.call(this,c.element,this.settings.errorClass,this.settings.validClass),this.showLabel(c.element,c.message);if(this.errorList.length&&(this.toShow=this.toShow.add(this.containers)),this.settings.success)for(a=0;this.successList[a];a++)this.showLabel(this.successList[a]);if(this.settings.unhighlight)for(a=0,b=this.validElements();b[a];a++)this.settings.unhighlight.call(this,b[a],this.settings.errorClass,this.settings.validClass);this.toHide=this.toHide.not(this.toShow),this.hideErrors(),this.addWrapper(this.toShow).show()},validElements:function(){return this.currentElements.not(this.invalidElements())},invalidElements:function(){return a(this.errorList).map(function(){return this.element})},showLabel:function(b,c){var d,e,f,g=this.errorsFor(b),h=this.idOrName(b),i=a(b).attr("aria-describedby");g.length?(g.removeClass(this.settings.validClass).addClass(this.settings.errorClass),g.html(c)):(g=a("<"+this.settings.errorElement+">").attr("id",h+"-error").addClass(this.settings.errorClass).html(c||""),d=g,this.settings.wrapper&&(d=g.hide().show().wrap("<"+this.settings.wrapper+"/>").parent()),this.labelContainer.length?this.labelContainer.append(d):this.settings.errorPlacement?this.settings.errorPlacement(d,a(b)):d.insertAfter(b),g.is("label")?g.attr("for",h):0===g.parents("label[for='"+h+"']").length&&(f=g.attr("id").replace(/(:|\.|\[|\])/g,"\\$1"),i?i.match(new RegExp("\\b"+f+"\\b"))||(i+=" "+f):i=f,a(b).attr("aria-describedby",i),e=this.groups[b.name],e&&a.each(this.groups,function(b,c){c===e&&a("[name='"+b+"']",this.currentForm).attr("aria-describedby",g.attr("id"))}))),!c&&this.settings.success&&(g.text(""),"string"==typeof this.settings.success?g.addClass(this.settings.success):this.settings.success(g,b)),this.toShow=this.toShow.add(g)},errorsFor:function(b){var c=this.idOrName(b),d=a(b).attr("aria-describedby"),e="label[for='"+c+"'], label[for='"+c+"'] *";return d&&(e=e+", #"+d.replace(/\s+/g,", #")),this.errors().filter(e)},idOrName:function(a){return this.groups[a.name]||(this.checkable(a)?a.name:a.id||a.name)},validationTargetFor:function(b){return this.checkable(b)&&(b=this.findByName(b.name)),a(b).not(this.settings.ignore)[0]},checkable:function(a){return/radio|checkbox/i.test(a.type)},findByName:function(b){return a(this.currentForm).find("[name='"+b+"']")},getLength:function(b,c){switch(c.nodeName.toLowerCase()){case"select":return a("option:selected",c).length;case"input":if(this.checkable(c))return this.findByName(c.name).filter(":checked").length}return b.length},depend:function(a,b){return this.dependTypes[typeof a]?this.dependTypes[typeof a](a,b):!0},dependTypes:{"boolean":function(a){return a},string:function(b,c){return!!a(b,c.form).length},"function":function(a,b){return a(b)}},optional:function(b){var c=this.elementValue(b);return!a.validator.methods.required.call(this,c,b)&&"dependency-mismatch"},startRequest:function(a){this.pending[a.name]||(this.pendingRequest++,this.pending[a.name]=!0)},stopRequest:function(b,c){this.pendingRequest--,this.pendingRequest<0&&(this.pendingRequest=0),delete this.pending[b.name],c&&0===this.pendingRequest&&this.formSubmitted&&this.form()?(a(this.currentForm).submit(),this.formSubmitted=!1):!c&&0===this.pendingRequest&&this.formSubmitted&&(a(this.currentForm).triggerHandler("invalid-form",[this]),this.formSubmitted=!1)},previousValue:function(b){return a.data(b,"previousValue")||a.data(b,"previousValue",{old:null,valid:!0,message:this.defaultMessage(b,"remote")})}},classRuleSettings:{required:{required:!0},email:{email:!0},url:{url:!0},date:{date:!0},dateISO:{dateISO:!0},number:{number:!0},digits:{digits:!0},creditcard:{creditcard:!0}},addClassRules:function(b,c){b.constructor===String?this.classRuleSettings[b]=c:a.extend(this.classRuleSettings,b)},classRules:function(b){var c={},d=a(b).attr("class");return d&&a.each(d.split(" "),function(){this in a.validator.classRuleSettings&&a.extend(c,a.validator.classRuleSettings[this])}),c},attributeRules:function(b){var c,d,e={},f=a(b),g=b.getAttribute("type");for(c in a.validator.methods)"required"===c?(d=b.getAttribute(c),""===d&&(d=!0),d=!!d):d=f.attr(c),/min|max/.test(c)&&(null===g||/number|range|text/.test(g))&&(d=Number(d)),d||0===d?e[c]=d:g===c&&"range"!==g&&(e[c]=!0);return e.maxlength&&/-1|2147483647|524288/.test(e.maxlength)&&delete e.maxlength,e},dataRules:function(b){var c,d,e={},f=a(b);for(c in a.validator.methods)d=f.data("rule"+c.charAt(0).toUpperCase()+c.substring(1).toLowerCase()),void 0!==d&&(e[c]=d);return e},staticRules:function(b){var c={},d=a.data(b.form,"validator");return d.settings.rules&&(c=a.validator.normalizeRule(d.settings.rules[b.name])||{}),c},normalizeRules:function(b,c){return a.each(b,function(d,e){if(e===!1)return void delete b[d];if(e.param||e.depends){var f=!0;switch(typeof e.depends){case"string":f=!!a(e.depends,c.form).length;break;case"function":f=e.depends.call(c,c)}f?b[d]=void 0!==e.param?e.param:!0:delete b[d]}}),a.each(b,function(d,e){b[d]=a.isFunction(e)?e(c):e}),a.each(["minlength","maxlength"],function(){b[this]&&(b[this]=Number(b[this]))}),a.each(["rangelength","range"],function(){var c;b[this]&&(a.isArray(b[this])?b[this]=[Number(b[this][0]),Number(b[this][1])]:"string"==typeof b[this]&&(c=b[this].replace(/[\[\]]/g,"").split(/[\s,]+/),b[this]=[Number(c[0]),Number(c[1])]))}),a.validator.autoCreateRanges&&(null!=b.min&&null!=b.max&&(b.range=[b.min,b.max],delete b.min,delete b.max),null!=b.minlength&&null!=b.maxlength&&(b.rangelength=[b.minlength,b.maxlength],delete b.minlength,delete b.maxlength)),b},normalizeRule:function(b){if("string"==typeof b){var c={};a.each(b.split(/\s/),function(){c[this]=!0}),b=c}return b},addMethod:function(b,c,d){a.validator.methods[b]=c,a.validator.messages[b]=void 0!==d?d:a.validator.messages[b],c.length<3&&a.validator.addClassRules(b,a.validator.normalizeRule(b))},methods:{required:function(b,c,d){if(!this.depend(d,c))return"dependency-mismatch";if("select"===c.nodeName.toLowerCase()){var e=a(c).val();return e&&e.length>0}return this.checkable(c)?this.getLength(b,c)>0:a.trim(b).length>0},email:function(a,b){return this.optional(b)||/^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(a)},url:function(a,b){return this.optional(b)||/^(https?|s?ftp):\/\/(((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:)*@)?(((\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5]))|((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.?)(:\d*)?)(\/((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)+(\/(([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)*)*)?)?(\?((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|[\uE000-\uF8FF]|\/|\?)*)?(#((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|\/|\?)*)?$/i.test(a)},date:function(a,b){return this.optional(b)||!/Invalid|NaN/.test(new Date(a).toString())},dateISO:function(a,b){return this.optional(b)||/^\d{4}[\/\-](0?[1-9]|1[012])[\/\-](0?[1-9]|[12][0-9]|3[01])$/.test(a)},number:function(a,b){return this.optional(b)||/^-?(?:\d+|\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/.test(a)},digits:function(a,b){return this.optional(b)||/^\d+$/.test(a)},creditcard:function(a,b){if(this.optional(b))return"dependency-mismatch";if(/[^0-9 \-]+/.test(a))return!1;var c,d,e=0,f=0,g=!1;if(a=a.replace(/\D/g,""),a.length<13||a.length>19)return!1;for(c=a.length-1;c>=0;c--)d=a.charAt(c),f=parseInt(d,10),g&&(f*=2)>9&&(f-=9),e+=f,g=!g;return e%10===0},minlength:function(b,c,d){var e=a.isArray(b)?b.length:this.getLength(b,c);return this.optional(c)||e>=d},maxlength:function(b,c,d){var e=a.isArray(b)?b.length:this.getLength(b,c);return this.optional(c)||d>=e},rangelength:function(b,c,d){var e=a.isArray(b)?b.length:this.getLength(b,c);return this.optional(c)||e>=d[0]&&e<=d[1]},min:function(a,b,c){return this.optional(b)||a>=c},max:function(a,b,c){return this.optional(b)||c>=a},range:function(a,b,c){return this.optional(b)||a>=c[0]&&a<=c[1]},equalTo:function(b,c,d){var e=a(d);return this.settings.onfocusout&&e.unbind(".validate-equalTo").bind("blur.validate-equalTo",function(){a(c).valid()}),b===e.val()},remote:function(b,c,d){if(this.optional(c))return"dependency-mismatch";var e,f,g=this.previousValue(c);return this.settings.messages[c.name]||(this.settings.messages[c.name]={}),g.originalMessage=this.settings.messages[c.name].remote,this.settings.messages[c.name].remote=g.message,d="string"==typeof d&&{url:d}||d,g.old===b?g.valid:(g.old=b,e=this,this.startRequest(c),f={},f[c.name]=b,a.ajax(a.extend(!0,{url:d,mode:"abort",port:"validate"+c.name,dataType:"json",data:f,context:e.currentForm,success:function(d){var f,h,i,j=d===!0||"true"===d;e.settings.messages[c.name].remote=g.originalMessage,j?(i=e.formSubmitted,e.prepareElement(c),e.formSubmitted=i,e.successList.push(c),delete e.invalid[c.name],e.showErrors()):(f={},h=d||e.defaultMessage(c,"remote"),f[c.name]=g.message=a.isFunction(h)?h(b):h,e.invalid[c.name]=!0,e.showErrors(f)),g.valid=j,e.stopRequest(c,j)}},d)),"pending")}}}),a.format=function(){throw"$.format has been deprecated. Please use $.validator.format instead."};var b,c={};a.ajaxPrefilter?a.ajaxPrefilter(function(a,b,d){var e=a.port;"abort"===a.mode&&(c[e]&&c[e].abort(),c[e]=d)}):(b=a.ajax,a.ajax=function(d){var e=("mode"in d?d:a.ajaxSettings).mode,f=("port"in d?d:a.ajaxSettings).port;return"abort"===e?(c[f]&&c[f].abort(),c[f]=b.apply(this,arguments),c[f]):b.apply(this,arguments)}),a.extend(a.fn,{validateDelegate:function(b,c,d){return this.bind(c,function(c){var e=a(c.target);return e.is(b)?d.apply(e,arguments):void 0})}})});if(!Object.keys){Object.keys=(function(){'use strict';var hasOwnProperty=Object.prototype.hasOwnProperty,hasDontEnumBug=!({toString:null}).propertyIsEnumerable('toString'),dontEnums=['toString','toLocaleString','valueOf','hasOwnProperty','isPrototypeOf','propertyIsEnumerable','constructor'],dontEnumsLength=dontEnums.length;return function(obj){if(typeof obj!=='object'&&(typeof obj!=='function'||obj===null)){throw new TypeError('Object.keys called on non-object');}
var result=[],prop,i;for(prop in obj){if(hasOwnProperty.call(obj,prop)){result.push(prop);}}
if(hasDontEnumBug){for(i=0;i<dontEnumsLength;i++){if(hasOwnProperty.call(obj,dontEnums[i])){result.push(dontEnums[i]);}}}
return result;};}());}
if(typeof String.prototype.trim!=='function'){String.prototype.trim=function(){return this.replace(/^\s+|\s+$/g,'');}}
Date.now=Date.now||function(){return+new Date;};function mean(m){var sum=0;for(i=0;i<m.length;i++){sum+=m[i];}
return sum/m.length;}
function median(m){m.sort();var middle=Math.floor(m.length/2);if(m.length%2==1){return m[middle];}else{console.log((m[middle-1]+m[middle])/2.0);return(m[middle-1]+m[middle])/2.0;}}
function mode(ary){var counter={};var mode=[];var max=0;for(var i in ary){if(!(ary[i]in counter))
counter[ary[i]]=0;counter[ary[i]]++;if(counter[ary[i]]==max)
mode.push(ary[i]);else if(counter[ary[i]]>max){max=counter[ary[i]];mode=[ary[i]];}}
return mode;}