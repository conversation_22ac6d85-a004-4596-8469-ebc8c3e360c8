am4core.useTheme(am4themes_animated);
am4core.options.commercialLicense = true;

var colorSet = new am4core.ColorSet();
colorSet.step = 4;

if ($('#pieChart').length) {
    // Risk Recommendation tyre piechart
    var piechart = am4core.create("pieChart", am4charts.PieChart);
    piechart.data = pieChart_data; //data from json
    var pieSeries = piechart.series.push(new am4charts.PieSeries());
    pieSeries.dataFields.value = "value";
    pieSeries.dataFields.category = "recommendation";
    pieSeries.labels.template.disabled = true;
    pieSeries.ticks.template.disabled = true;
    pieSeries.slices.template.tooltipText = "";
    pieSeries.colors = colorSet;
    let hs = pieSeries.slices.template.states.getKey("hover");
    hs.properties.scale = 1;
    let as = pieSeries.slices.template.states.getKey("active");
    as.properties.shiftRadius = 0;
    piechart.innerRadius = am4core.percent(65);
    piechart.responsive.enabled = true;
    piechart.legend = new am4charts.Legend();
    piechart.legend.labels.template.text = "{name}\n{value}%";
    piechart.legend.labels.template.fill = am4core.color("#002663");
    piechart.legend.valueLabels.template.text = "";
    piechart.legend.itemContainers.template.paddingTop = 8;
    piechart.legend.itemContainers.template.paddingBottom = 8;
    let pieMarkers = piechart.legend.markers.template.children.getIndex(0);
    pieMarkers.cornerRadius(10, 10, 10, 10);
    pieMarkers.width = 20;
    pieMarkers.height = 20;
    var label = piechart.seriesContainer.createChild(am4core.Label);
    label.text = "";
    label.horizontalCenter = "middle";
    label.verticalCenter = "middle";
    label.fontSize = 30;
    label.fill = '#337ab7';
    let piechartLegend = am4core.create("pieChartLegend", am4core.Container);
    piechartLegend.width = am4core.percent(100);
    piechartLegend.height = am4core.percent(100);
    piechartLegend.fontSize = 10;
    piechartLegend.fontWeight = "500";
    piechart.legend.parent = piechartLegend;
    piechart.ColorSet = 2;
}

if ($('#columnChart').length) {
    let org_id = window.location.pathname.substring(window.location.pathname.lastIndexOf('/') + 1);
    // Risk Recommendations by status
    var columnchart = am4core.create("columnChart", am4charts.XYChart);
    columnchart.data = columnChart_data; //data from json
    columnchart.colors.step = 2;
    columnchart.fontSize = 10;
    columnchart.responsive.enabled = true;
    // columnchart.scrollbarX = new am4core.Scrollbar();
    // columnchart.scrollbarX.parent = columnchart.bottomAxesContainer;
    let columnCategoryAxis = columnchart.xAxes.push(new am4charts.CategoryAxis());
    columnCategoryAxis.dataFields.category = "category";
    columnCategoryAxis.renderer.grid.template.location = 0;
    columnCategoryAxis.renderer.minGridDistance = 20;
    columnCategoryAxis.renderer.labels.template.fill = am4core.color("#002663");
    columnCategoryAxis.renderer.labels.template.fontWeight = "500";
    columnCategoryAxis.renderer.labels.template.textAlign = "middle";
    columnCategoryAxis.renderer.labels.template.rotation = -90;
    columnCategoryAxis.renderer.labels.template.horizontalCenter = "right";
    columnCategoryAxis.renderer.labels.template.verticalCenter = "middle";
    let columnValueAxis = columnchart.yAxes.push(new am4charts.ValueAxis());
    columnValueAxis.min = 0;
    columnValueAxis.renderer.labels.template.fill = am4core.color("#002663");
    columnValueAxis.renderer.labels.template.fontWeight = "500";
    let columnSeries1 = columnchart.series.push(new am4charts.ColumnSeries());
    columnSeries1.columns.template.tooltipText = "";
    columnSeries1.name = "Open";
    columnSeries1.dataFields.categoryX = "category";
    columnSeries1.dataFields.valueY = "value1";
    columnSeries1.dataItems.template.locations.categoryX = 0.5;
    columnSeries1.stacked = true;
    columnSeries1.tooltip.pointerOrientation = "vertical";
    columnSeries1.columns.template.url = "/tracker/risk-recommendations?mga_scheme=All&organisation=" + org_id + "&lob=All&location={category.urlEncode()}";
    let columnSeries2 = columnchart.series.push(new am4charts.ColumnSeries());
    columnSeries2.columns.template.tooltipText = "";
    columnSeries2.name = "Closed";
    columnSeries2.dataFields.categoryX = "category";
    columnSeries2.dataFields.valueY = "value2";
    columnSeries2.dataItems.template.locations.categoryX = 0.5;
    columnSeries2.stacked = true;
    columnSeries2.tooltip.pointerOrientation = "vertical";
    columnSeries2.columns.template.url = "/tracker/risk-recommendations?mga_scheme=All&organisation=" + org_id + "&lob=All&location={category.urlEncode()}";
    columnchart.legend = new am4charts.Legend();
    columnchart.legend.labels.template.fill = am4core.color("#002663");
    columnchart.legend.useDefaultMarker = true;
    columnchart.legend.valueLabels.template.text = "";
    let columnMarkers = columnchart.legend.markers.template.children.getIndex(0);
    columnMarkers.cornerRadius(10, 10, 10, 10);
    columnMarkers.width = 20;
    columnMarkers.height = 20;
    let columnnLegendContainer = am4core.create("columnChartLegend", am4core.Container);
    columnnLegendContainer.width = am4core.percent(100);
    columnnLegendContainer.height = am4core.percent(100);
    columnnLegendContainer.fontSize = 10;
    columnnLegendContainer.fontWeight = "500";
    columnchart.legend.parent = columnnLegendContainer;
    columnchart.legend.parent = columnnLegendContainer;

    if (columnChart_data.length > 6) {
        var tempcol = 6 / columnChart_data.length;
        columnchart.scrollbarX = new am4core.Scrollbar();
        columnchart.scrollbarX.parent = columnchart.bottomAxesContainer;
        columnchart.scrollbarX.startGrip.hide();
        columnchart.scrollbarX.endGrip.hide();
        columnchart.scrollbarX.start = 0;
        columnchart.scrollbarX.end = tempcol;
        columnchart.zoomOutButton = new am4core.ZoomOutButton();
        columnchart.zoomOutButton.hide();
    }
}

if ($('#columnChartLossEstimate').length) {
    let org_id = window.location.pathname.substring(window.location.pathname.lastIndexOf('/') + 1);
    // Risk Recommendations by status
    var columnchart = am4core.create("columnChartLossEstimate", am4charts.XYChart);
    columnchart.data = columnChart_data2; //data from json
    columnchart.colors.step = 2;
    columnchart.fontSize = 10;
    columnchart.responsive.enabled = true;
    // columnchart.scrollbarX = new am4core.Scrollbar();
    // columnchart.scrollbarX.parent = columnchart.bottomAxesContainer;
    let columnCategoryAxis = columnchart.xAxes.push(new am4charts.CategoryAxis());
    columnCategoryAxis.dataFields.category = "category2";
    columnCategoryAxis.renderer.grid.template.location = 0;
    columnCategoryAxis.renderer.minGridDistance = 20;
    columnCategoryAxis.renderer.labels.template.fill = am4core.color("#002663");
    columnCategoryAxis.renderer.labels.template.fontWeight = "500";
    columnCategoryAxis.renderer.labels.template.textAlign = "middle";
    columnCategoryAxis.renderer.labels.template.rotation = -90;
    columnCategoryAxis.renderer.labels.template.horizontalCenter = "right";
    columnCategoryAxis.renderer.labels.template.verticalCenter = "middle";

    let columnValueAxis = columnchart.yAxes.push(new am4charts.ValueAxis());
    columnValueAxis.min = 0;
    columnValueAxis.renderer.labels.template.fill = am4core.color("#002663");
    columnValueAxis.renderer.labels.template.fontWeight = "500";
    columnValueAxis.numberFormatter = new am4core.NumberFormatter();
    columnValueAxis.numberFormatter.numberFormat = '£#,###.##';

    let columnSeries1 = columnchart.series.push(new am4charts.ColumnSeries());
    columnSeries1.columns.template.tooltipText = "";
    columnSeries1.name = "Property Damage";
    columnSeries1.dataFields.categoryX = "category2";
    columnSeries1.dataFields.valueY = "value12";
    columnSeries1.dataItems.template.locations.categoryX = 0.5;
    columnSeries1.stacked = true;
    columnSeries1.tooltip.pointerOrientation = "vertical";
    //columnSeries1.columns.template.url = "/tracker/risk-recommendations?mga_scheme=All&organisation=" + org_id + "&lob=All&srf={category.urlEncode()}";
    let columnSeries2 = columnchart.series.push(new am4charts.ColumnSeries());
    columnSeries2.columns.template.tooltipText = "";
    columnSeries2.name = "Business Interruption";
    columnSeries2.dataFields.categoryX = "category2";
    columnSeries2.dataFields.valueY = "value22";
    columnSeries2.dataItems.template.locations.categoryX = 0.5;
    columnSeries2.stacked = true;
    columnSeries2.tooltip.pointerOrientation = "vertical";
    //columnSeries2.columns.template.url = "/tracker/risk-recommendations?mga_scheme=All&organisation=" + org_id + "&lob=All&srf={category.urlEncode()}";
    columnchart.legend = new am4charts.Legend();
    columnchart.legend.labels.template.fill = am4core.color("#002663");
    columnchart.legend.useDefaultMarker = true;
    columnchart.legend.valueLabels.template.text = "";
    let columnMarkers = columnchart.legend.markers.template.children.getIndex(0);
    columnMarkers.cornerRadius(10, 10, 10, 10);
    columnMarkers.width = 20;
    columnMarkers.height = 20;
    let columnnLegendContainer = am4core.create("columnChartLegend2", am4core.Container);
    columnnLegendContainer.width = am4core.percent(100);
    columnnLegendContainer.height = am4core.percent(100);
    columnnLegendContainer.fontSize = 10;
    columnnLegendContainer.fontWeight = "500";
    columnchart.legend.parent = columnnLegendContainer;
    columnchart.legend.parent = columnnLegendContainer;

    if (columnChart_data2.length > 6) {
        var tempcol = 6 / columnChart_data2.length;
        columnchart.scrollbarX = new am4core.Scrollbar();
        columnchart.scrollbarX.parent = columnchart.bottomAxesContainer;
        columnchart.scrollbarX.startGrip.hide();
        columnchart.scrollbarX.endGrip.hide();
        columnchart.scrollbarX.start = 0;
        columnchart.scrollbarX.end = tempcol;
        columnchart.zoomOutButton = new am4core.ZoomOutButton();
        columnchart.zoomOutButton.hide();
    }
}

if ($('#columnChartCompany').length) {
    // Risk Recommendations by company
    var columnchartCompany = am4core.create("columnChartCompany", am4charts.XYChart);
    columnchartCompany.data = columnChartCompany_data; //data from json
    columnchartCompany.colors.step = 2;
    columnchartCompany.fontSize = 10;
    columnchartCompany.responsive.enabled = true;
    let columnCompanyCategoryAxis = columnchartCompany.xAxes.push(new am4charts.CategoryAxis());
    columnCompanyCategoryAxis.dataFields.category = "category";
    columnCompanyCategoryAxis.renderer.grid.template.location = 0;
    columnCompanyCategoryAxis.renderer.minGridDistance = 20;
    columnCompanyCategoryAxis.renderer.labels.template.fill = am4core.color("#002663");
    columnCompanyCategoryAxis.renderer.labels.template.fontWeight = "500";
    columnCompanyCategoryAxis.renderer.labels.template.textAlign = "middle";
    columnCompanyCategoryAxis.renderer.labels.template.horizontalCenter = "right";
    columnCompanyCategoryAxis.renderer.labels.template.verticalCenter = "middle";
    columnCompanyCategoryAxis.renderer.labels.template.rotation = 270;

    let columnCompanyValueAxis = columnchartCompany.yAxes.push(new am4charts.ValueAxis());
    columnCompanyValueAxis.min = 0;
    columnCompanyValueAxis.renderer.labels.template.fill = am4core.color("#002663");
    columnCompanyValueAxis.renderer.labels.template.fontWeight = "500";
    let columnCompanySeries1 = columnchartCompany.series.push(new am4charts.ColumnSeries());
    columnCompanySeries1.columns.template.tooltipText = "";
    columnCompanySeries1.name = "Open";
    columnCompanySeries1.dataFields.categoryX = "category";
    columnCompanySeries1.dataFields.valueY = "value1";
    columnCompanySeries1.dataItems.template.locations.categoryX = 0.5;
    columnCompanySeries1.stacked = true;
    columnCompanySeries1.tooltip.pointerOrientation = "vertical";
    let columnCompanySeries2 = columnchartCompany.series.push(new am4charts.ColumnSeries());
    columnCompanySeries2.columns.template.tooltipText = "";
    columnCompanySeries2.name = "Closed";
    columnCompanySeries2.dataFields.categoryX = "category";
    columnCompanySeries2.dataFields.valueY = "value2";
    columnCompanySeries2.dataItems.template.locations.categoryX = 0.5;
    columnCompanySeries2.stacked = true;
    columnCompanySeries2.tooltip.pointerOrientation = "vertical";
    columnchartCompany.legend = new am4charts.Legend();
    columnchartCompany.legend.labels.template.fill = am4core.color("#002663");
    columnchartCompany.legend.useDefaultMarker = true;
    columnchartCompany.legend.valueLabels.template.text = "";
    let columnCompanyMarkers = columnchartCompany.legend.markers.template.children.getIndex(0);
    columnCompanyMarkers.cornerRadius(10, 10, 10, 10);
    columnCompanyMarkers.width = 20;
    columnCompanyMarkers.height = 20;
    let columnCompanyLegendContainer = am4core.create("columnChartCompanyLegend", am4core.Container);
    columnCompanyLegendContainer.width = am4core.percent(100);
    columnCompanyLegendContainer.height = am4core.percent(100);
    columnCompanyLegendContainer.fontSize = 10;
    columnCompanyLegendContainer.fontWeight = "500";
    columnchartCompany.legend.parent = columnCompanyLegendContainer;

    if (columnChartCompany_data.length > 6) {
        var tempcol = 6 / columnChart_data.length;
        columnchartCompany.scrollbarX = new am4core.Scrollbar();
        columnchartCompany.scrollbarX.parent = columnchartCompany.bottomAxesContainer;
        columnchartCompany.scrollbarX.startGrip.hide();
        columnchartCompany.scrollbarX.endGrip.hide();
        columnchartCompany.scrollbarX.start = 0;
        columnchartCompany.scrollbarX.end = tempcol;
        columnchartCompany.zoomOutButton = new am4core.ZoomOutButton();
        columnchartCompany.zoomOutButton.hide();
    }

}

if ($('#horizontalColumnChart').length) {
    // Top 5 Recommendations by title
    var horizontalColumnChart = am4core.create("horizontalColumnChart", am4charts.XYChart); // because of the colour
    horizontalColumnChart.data = horizontalColumnChart_data; //data from json
    horizontalColumnChart.fontSize = 10;
    horizontalColumnChart.responsive.enabled = true;
    var horizontalCategoryAxis = horizontalColumnChart.yAxes.push(new am4charts.CategoryAxis());
    horizontalCategoryAxis.dataFields.category = "title";
    horizontalCategoryAxis.numberFormatter.numberFormat = "#";
    horizontalCategoryAxis.renderer.inversed = true;
    horizontalCategoryAxis.renderer.grid.template.location = 0;
    horizontalCategoryAxis.renderer.labels.template.fill = am4core.color("#002663");
    horizontalCategoryAxis.renderer.labels.template.fontWeight = "500";
    horizontalCategoryAxis.renderer.labels.template.wrap = true;
    horizontalCategoryAxis.renderer.labels.template.maxWidth = 200;
    horizontalCategoryAxis.renderer.labels.template.textAlign = "end";
    var horizontalValueAxis = horizontalColumnChart.xAxes.push(new am4charts.ValueAxis());
    horizontalValueAxis.renderer.labels.template.fill = am4core.color("#002663");
    horizontalValueAxis.renderer.labels.template.fontWeight = "500";
    var horizontalSeries = horizontalColumnChart.series.push(new am4charts.ColumnSeries());
    horizontalSeries.dataFields.valueX = "recommendation";
    horizontalSeries.dataFields.categoryY = "title";
    horizontalSeries.name = "Recommendation";
    horizontalSeries.columns.template.tooltipText = "";
    horizontalSeries.columns.template.adapter.add("fill", function(fill, target) {
        return horizontalColumnChart.colors.getIndex(target.dataItem.index);
    });
    horizontalSeries.columns.template.adapter.add("stroke", function(stroke, target) {
        return horizontalColumnChart.colors.getIndex(target.dataItem.index);
    });
}