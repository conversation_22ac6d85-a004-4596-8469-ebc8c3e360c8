/*globals bootbox*/

// All JS in here is for mock interactions that won't be required
// after front-end templates are finished
(function() {
    var cache = {},
        cacheRef = {
            $lessonNameField: '.page--edit-lesson #field-lesson-title',
            $lessonAddTestSelect: '.mock-js--add-test-select',
            $lessonAddContentBtn: '.mock-js--lesson-add-content-btn',
            $lessonPageBuilderItems: '.page-builder__items',
            $lessonItemDuplicate: '.mock-js--lesson-item-duplicate, .mock-js--test-item-duplicate',
            $lessonItemDelete: '.mock-js--lesson-item-delete, .mock-js--test-item-delete',
            $lessonPageChangeBtn: '.mock-js--lesson-page-btn',
            $lessonPage: '.page-builder__page',
            $lessonPageAddBtn: '.mock-js--lesson-add-page-btn',
            $lessonPageEditNameBtn: '.mock-js--lesson-page-edit-name',
            $lessonPageSaveNameBtn: '.mock-js--lesson-page-save-name',
            $lessonPagesSortable: '.mock-js--sortable-pages',
            $lessonPageCopy: '.mock-js--lesson-page-copy',
            $lessonPageDelete: '.mock-js--lesson-page-delete',
            $testQuestionType: '.mock-js--test-question-type',
            $testQuestionAddAnswer: '.mock-js--test-add-answer',
            $testAddContentBtn: '.mock-js--test-add-content',
            $testRemoveAnswer: '.mock-js--test-remove-answer',
            $testSubmit: '.mock-js--test-submit',
            $courseLessonDelete: '.mock-js--course-lesson-delete',
            $courseNameField: '.page--edit-course #field-course-title',
            $courseCertificateField: '.mock-js--show-certificate',
            $deleteContent: '.mock-js--delete-content',
            $deleteInputFile: '.mock-js--delete-input-file',
            $tableClearFilters: '.mock-js--table-controls-filter-clear',
            $tableLoadContent: '.mock-js--load-table'
        };

    var init = function() {
        refreshSelectors();
        attachEvents();
        initLessonPagesSortable();
        initLessonNameFields();
        initCourseNameFields();
    };

    // http://stackoverflow.com/questions/523266/how-can-i-get-a-specific-parameter-from-location-search
    var parseQueryString = function() {

        var str = window.location.search;
        var objURL = {};

        str.replace(
            new RegExp("([^?=&]+)(=([^&]*))?", "g"),
            function($0, $1, $2, $3) {
                objURL[$1] = $3;
            }
        );
        return objURL;
    };

    var attachEvents = function() {
        cache.$lessonAddTestSelect.off('change', lessonAddTest).on('change', lessonAddTest);
        cache.$lessonAddContentBtn.off('click', lessonAddContent).on('click', lessonAddContent);
        cache.$lessonItemDuplicate.off('click', lessonItemDuplicate).on('click', lessonItemDuplicate);
        cache.$lessonItemDelete.off('click', lessonItemDelete).on('click', lessonItemDelete);
        cache.$lessonPageChangeBtn.off('click', lessonPageChange).on('click', lessonPageChange);
        cache.$lessonPageAddBtn.off('click', lessonPageAdd).on('click', lessonPageAdd);
        cache.$lessonPageEditNameBtn.off('click', lessonEditName).on('click', lessonEditName);
        cache.$lessonPageSaveNameBtn.off('click', lessonSaveName).on('click', lessonSaveName);
        cache.$lessonPageCopy.off('click', lessonPageCopy).on('click', lessonPageCopy);
        cache.$lessonPageDelete.off('click', lessonPageDelete).on('click', lessonPageDelete);
        cache.$testQuestionType.off('change', testQuestionType).on('change', testQuestionType);
        cache.$testQuestionAddAnswer.off('click', testQuestionAddAnswer).on('click', testQuestionAddAnswer);
        cache.$testAddContentBtn.off('click', testAddContent).on('click', testAddContent);
        cache.$testRemoveAnswer.off('click', testRemoveAnswer).on('click', testRemoveAnswer);
        cache.$testSubmit.off('click', testSubmit).on('click', testSubmit);
        cache.$courseLessonDelete.off('click', courseLessonDelete).on('click', courseLessonDelete);
        cache.$deleteContent.off('click', deleteContent).on('click', deleteContent);
        cache.$deleteInputFile.off('click', deleteInputFile).on('click', deleteInputFile);
        cache.$courseCertificateField.off('change', courseShowAccreditation).on('change', courseShowAccreditation);
        cache.$tableClearFilters.off('click', clearFilters).on('click', clearFilters);
        cache.$tableLoadContent.off('change', tableLoadContent).on('change', tableLoadContent);
    };

    var refreshSelectors = function() {
        $.each(cacheRef, function(index, item) {
            cache[index] = $(item);
        });
    };

    var contentChanged = function() {
        refreshSelectors();
        attachEvents();
        $('.js--sortable').sortable();
    };


    var initLessonPagesSortable = function() {
        cache.$lessonPagesSortable.sortable({
            onUpdate: function() {
                var $items = cache.$lessonPagesSortable.find('.page-list__item-name');
                $items.each(reorderLessonPages);
            }
        });
    };

    // if Lesson title is in query string, set it in the DOM
    var initLessonNameFields = function() {
        if (cache.$lessonNameField.length) {
            var params = parseQueryString(),
                lessonName = params['lesson-title'];
            if (lessonName) {
                cache.$lessonNameField.attr('value', decodeURIComponent(lessonName));
            }

        }
    };

    // if Course title is in query string, set it in the DOM
    var initCourseNameFields = function() {
        if (cache.$courseNameField.length) {
            var params = parseQueryString(),
                courseName = params['course-title'];

            if (courseName) {
                cache.$courseNameField.attr('value', decodeURIComponent(courseName));
            }

        }
    };


    // show test button after changing dropdown
    var lessonAddTest = function() {
        $('.js--make-test-button').toggleClass('hidden');
    };


    var reorderLessonPages = function(index, item) {
        $(item).css('opacity', 0.5);
        window.setTimeout(function() {
            $(item).text('Page ' + (index + 1));
            $(item).css('opacity', 1);
        }, 300);
    };

    // Test: submit and show validation
    var testSubmit = function(e) {
        var $btn = $(e.currentTarget);
        if ($btn.parents('.js--form-validate').valid()) {
            e.preventDefault();
            $btn
                .find('.icon')
                .removeClass('fa-check')
                .addClass('fa-refresh')
                .addClass('icon-spinning');
            window.setTimeout(function() {

                // Randomly show either failed or passed message
                if (Math.random() < 0.5) {
                    $('.page-builder-view__page-results__failed, .page-builder-view__page-buttons__failed').removeClass('hidden');
                } else {
                    $('.page-builder-view__page-results__passed, .page-builder-view__page-buttons__passed').removeClass('hidden');
                }


                $('.page-builder-view__page-buttons__submit').addClass('hidden');
            }, 1000);
        }
    };


    // Test: remove answer
    var testRemoveAnswer = function(e) {
        e.preventDefault();
        var $btn = $(e.currentTarget),
            $parent = $btn.parents('.test-question__answer');
        $parent.remove();
    };


    // Test: Add answer
    var testQuestionAddAnswer = function(e) {
        var $btn = $(e.currentTarget),
            $parent = $btn.parents('.page-builder__item--question'),
            $radios = $parent.find('.test-question__radios'),
            $checkboxes = $parent.find('.test-question__checkboxes'),
            $lastRadio = $radios.find('.test-question__radio').last(),
            $lastCheckbox = $checkboxes.find('.test-question__checkbox').last();

        if ($radios.hasClass('hidden')) {
            $lastCheckbox.parent().append($lastCheckbox.clone());
        } else {
            $lastRadio.parent().append($lastRadio.clone());
        }
        contentChanged();
    };

    // Test: change question type
    var testQuestionType = function(e) {
        var $select = $(e.currentTarget),
            $parent = $select.parents('.page-builder__item--question');

        $parent
            .find('.test-question__radios, .test-question__checkboxes, .test-question__radios, .test-question__radios-correct-answers')
            .toggleClass('hidden');
    };

    // Lesson: rename
    var lessonEditName = function(e) {
        e.preventDefault();
        e.stopPropagation();

        var $btn = $(e.currentTarget),
            $inputGroup = $btn.parents('.page-list__item').find('.page-list__item-input-group'),
            $input = $inputGroup.find('input');


        $inputGroup.slideToggle(function() {
            $input.get(0).select();
        });
    };

    // Lesson: save
    var lessonSaveName = function(e) {
        e.preventDefault();
        e.stopPropagation();

        var $btn = $(e.currentTarget),
            $parentText = $btn.parents('.page-list__item').find('.page-list__item-name'),
            inputValue = $btn.parents('.page-list__item').find('input').val(),
            $inputGroup = $btn.parents('.page-list__item').find('.page-list__item-input-group');

        $parentText.text(inputValue);
        $inputGroup.slideToggle();
    };

    // Lesson page copy from pages sidebar
    var lessonPageCopy = function(e) {
        var $item = $(this).parents('.page-list__item'),
            $list = $item.parents('.list-group').eq(0),
            $clone = $item.clone(),
            cloneName = 'Page ';

        $list.append($clone);

        cloneName += ($clone.index() + 1);
        $clone
            .removeClass('active')
            .find('.page-list__item-name').text(cloneName);
        contentChanged();
    };

    // Lesson page copy from pages sidebar
    var lessonPageDelete = function() {
        var $item = $(this).parents('.page-list__item'),
            $list = $item.parents('.list-group').eq(0);

        bootbox.confirm('Are you sure you want to delete this?', function(result) {
            if (result) {
                $item.remove();

                // reindex each item
                $list.find('.page-list__item').each(function(index, item) {
                    $(item).find('.page-list__item-name').text('Page ' + ($(item).index() + 1));
                });
            }
        });
    };

    // Lesson: add new
    var lessonPageAdd = function(e) {
        e.preventDefault();
        var $lastPageItem = $(e.currentTarget).parent().find('.page-list__item').last(),
            $lastPageItemClone = $lastPageItem.clone(),
            $inputGroup = $lastPageItemClone.find('page-list__item-input-group'),
            $input = $lastPageItemClone.find('input'),
            pageName = 'Page ';

        $lastPageItem.parent().append($lastPageItemClone);

        pageName += ($lastPageItemClone.index() + 1);

        $lastPageItemClone.removeClass('active');

        $lastPageItemClone.find('.page-list__item-name').text(pageName);
        $input.val(pageName);

        $inputGroup.slideToggle(function() {
            $input.get(0).select();
        });

        contentChanged();
    };

    // Lesson: hide all pages and show new page
    var lessonPageChange = function(e) {
        e.preventDefault();
        var link = $(e.currentTarget).attr('href'),
            $item = $(e.currentTarget).parents('.page-list__item'),
            $list = $item.parents('.list-group').eq(0);

        cache.$lessonPage.addClass('hidden');
        $(link).hide().removeClass('hidden').fadeIn();

        // indicate active item in the list-group
        $list.find('.page-list__item.active').removeClass('active');
        $item.addClass('active');
    };

    // Add content item buttons: show hidden content items
    var testAddContent = function(e) {
        e.preventDefault();
        var dataLink = $(e.currentTarget).data('link'),
            $parent = $('.page-builder__items'),
            $item = $parent.find('.page-builder__item--' + dataLink);

        if (dataLink === 'question') {
            $item = $item.last().clone();
        }

        $parent.append($item);

        if (dataLink === 'question') {
            $item.find('.collapse').collapse();
        }

        $item.removeClass('hidden');
        contentChanged();
    };

    // Add content item buttons: show hidden content items
    var lessonAddContent = function(e) {
        e.preventDefault();
        var dataLink = $(e.currentTarget).data('link'),
            $parent = $('.page-builder__page:not(.hidden) .page-builder__items'),
            $item = $parent.find('.page-builder__item--' + dataLink);

        $parent.append($item);
        $item.removeClass('hidden');
        contentChanged();
    };

    // Lesson Content items buttons: duplicate
    var lessonItemDuplicate = function(e) {
        e.preventDefault();
        e.stopPropagation();
        var $btn = $(e.currentTarget),
            $parent = $btn.parents('.page-builder__item');
        cache.$lessonPageBuilderItems.append($parent.clone());
        contentChanged();
    };

    // Lesson Content items buttons: delete
    var lessonItemDelete = function(e) {
        e.stopPropagation();
        e.preventDefault();
        var $btn = $(e.currentTarget),
            $parent = $btn.parents('.page-builder__item');


        bootbox.confirm('Are you sure you want to delete this?', function(result) {
            if (result) {
                $parent.remove();
                contentChanged();
            }
        });

    };

    // course edit - delete lesson
    var courseLessonDelete = function(e) {
        e.preventDefault();
        var $btn = $(e.currentTarget),
            $td = $btn.parents('li').eq(0);
        bootbox.confirm('Are you sure you want to delete this lesson?', function(result) {
            if (result) {
                $td.remove();
            }
        });
    };

    // delete lesson, course or test
    var deleteContent = function(e) {
        e.preventDefault();
        var $btn = $(e.currentTarget),
            contentType = $btn.data('content-type') ? $btn.data('content-type') : '',
            url = $btn.attr('href');
        bootbox.confirm('Are you sure you want to delete this ' + contentType + '?', function(result) {
            if (result) {
                window.location = url;
            }
        });
    };

    // delete input file (e.g. uploaded iamge)
    var deleteInputFile = function(e) {
        e.preventDefault();
        var $btn = $(e.currentTarget),
            $inputFile = $btn.parents('.input__file').eq(0);

        bootbox.confirm('Are you sure you want to delete this?', function(result) {
            if (result) {
                $inputFile.remove();
            }
        });
    };

    // show accreditation form on showing certificate
    var courseShowAccreditation = function() {
        $('.course-edit__form-accreditation-logos').toggleClass('hidden');
    };

    // clear table control filters
    var clearFilters = function(e) {
        e.preventDefault();
        var $controls = $(this)
                            .parents('.table-controls')
                            .find('select, input');
        $controls
            .val('')
            .trigger('change');
    };

    // pretend to load new table content
    var tableLoadContent = function(e) {
        var $table = $($(this).data('table'));
        $table
            .fadeTo(400, 0.5, function() {
                window.setTimeout(function() {
                    $table.fadeTo(400, 1);
                }, 400);
            });
    };





    init();

}());
