$(document).ready(function(){

    var pluploadInited = false,
        pluploadFiles;
    attachments = new Array();
    attachmentLinks = new Array();

    $('#page-builder-list .page-builder__page').each(function(e){
        initiate_image_upload($(this));
        initiate_document_upload($(this));
        initiate_video_upload($(this));
        initiate_audio_upload($(this));
    })


    /** Tests **/
    $('select[name="add-test"]').on('change', function(e){
        var url = '';
        var element = $(this);
        if($(this).val() == 1){
            url = $(this).closest('div').data('url');
            $.ajax({
                type : 'POST',
                url : url,
                data: {},
                success: function(response){
                    if(response.response != undefined && response.response == 'success'){
                        //item.fadeOut();
                        show_success_notify('Test created');
                        $('#lesson-test-buttons').removeClass('hidden');
                    }
                }
            })
        }else{
            url = $(this).closest('div').data('delete');
            bootbox.confirm('Are you sure you want to remove the test?', function(result){

                if(result === true){
                    $.ajax({
                        type : 'DELETE',
                        url : url,
                        data: {},
                        success: function(response){
                            if(response.response != undefined && response.response == 'success'){
                                //item.fadeOut();
                                show_success_notify('Test deleted');
                                $('#lesson-test-buttons').addClass('hidden');
                            }
                        }
                    })
                }else{
                    element.val(1).attr('selectd', true)
                }

            })            
        }
    })


    /** PAGES **/
    $('.page-list').on('click','.page-list__item', function(e){
        e.preventDefault();
        $(this).closest('.list-group').find('li').removeClass('active');
        $(this).addClass('active');

        //Find Page and show
        var id = $(this).find('a').attr('href');
        $('.page-builder__page:not(".active")').hide().addClass('hidden');
        $('.page-builder__page.active').fadeOut().addClass('hidden');
        $('.page-builder__page'+id).removeClass('hidden').fadeIn();
    });

    var sortable_pages = null;
     function setup_sortable_pages(){
        var page_list_items = document.getElementById('sortable-pages');
        console.log(page_list_items);
        sortable_pages = Sortable.create(page_list_items, {
          handle: ".icon-sortable",
            onSort: function(event){

                var ids = [];
                $('#sortable-pages').find('li').each(function(){
                    lesson_id = $(this).data('lesson-id');
                    course_id = $(this).data('course-id');
                    ids.push($(this).data('id'));
                });

                url = '/learning/course/' +course_id + '/lessons/' + lesson_id + '/page/sort';
                $.ajax({
                    type: 'PUT',
                    url : url,
                    data: {page_ids : ids},
                    success: function(response){


                        if(response.response != undefined && response.response == 'success'){
                            show_success_notify('Page list updated.');
                            return true;
                        }else{
                            show_error_notify('There was an error updating your page list.')
                        }

                    }
                });


            }

        });
    }

    function reset_sortable_pages(){
        sortable_pages.destroy();
        setup_sortable_pages();
    }

    setup_sortable_pages();


    var sortable = [];
    function setup_sortable_page_items(){

        $('#page-builder-list').find('.page-builder__page').each(function(){
            
            var page = document.getElementById('page-builder-items-'+$(this).data('id'));

            var sorted = Sortable.create(page, {
                handle: ".page-builder__item-title",
                onSort: function(event){
                    if((event.item.className).indexOf('page-builder__item--text-rich') > -1){
                        tinymce.get($(event.item).find('.field-rich-text-editor').prop('id')).remove();
                    }
                    show_loading_notify();

                    var page_items = $('#'+event.target.id);
                    var course_id = $('.list-group-item.page-list__item.active').data('course-id');
                    var lesson_id = $('.list-group-item.page-list__item.active').data('lesson-id');
                    var page_id = $('.list-group-item.page-list__item.active').data('id');
                    var page_item_ids = [];


                    page_items.children('.page-builder__item').each(function(){
                        page_item_ids.push($(this).data('id'));
                    })

                    url = '/learning/course/' + course_id + '/lessons/' + lesson_id + '/page/' + page_id + '/sort';

                    $.ajax({
                        type: 'PUT',
                        url : url,
                        data: {section_ids : page_item_ids},
                        success: function(response){
                            if(response.response != undefined && response.response == 'success'){
                                show_success_notify('Page order updated.');
                            }else{
                                show_error_notify('There was an error updating your page order.');
                            }
                            richtexteditor();
                        }
                    })


                }

            });

            sortable.push(sorted);
        })
    }

    function reset_sortable_page_items(){


        setup_sortable_page_items();
    }
    setup_sortable_page_items();
    



    //DELETE
    $('.page-list').on('click','.page-list__item .lesson-page-delete', function(e){

        e.stopPropagation();
        var item = $(this);

        bootbox.confirm('Are you sure you want to delete this page?', function(result){

            if(result === true){
                var id = item.closest('li').data('id');
                var lesson_id = item.closest('li').data('lesson-id');
                var course_id = item.closest('li').data('course-id');
                delete_page(course_id,lesson_id, id);
            }

        })

    });

    //DUPLICATE
    $('.page-list').on('click','.page-list__item .lesson-page-copy',function(e){
        e.stopPropagation();
        var item = $(this);

        bootbox.confirm('Are you sure you want to duplicate this page?', function(result){

            if(result === true){

                var id = item.closest('li').data('id');
                var lesson_id = item.closest('li').data('lesson-id');
                var course_id = item.closest('li').data('course-id');
                duplicate_page(course_id, lesson_id, id);

            }

        })
    })


    //SAVE
    $('.lesson-add-page-btn').on('click', function(e){
        
        e.preventDefault();
        $('.begin').remove() ;
        bootbox.prompt('Page Name', function(result){

            if(result !== null){

                save_page(result);

            }

        })

    });




    /** COMPONENTS **/

    //DELETE
    $('.page-builder__page').on('click', '.page-builder__item .icon-delete-section', function(e){
        e.stopPropagation();
        e.preventDefault();
        var item = $(this).closest('.page-builder__item ');
        item.addClass('in');
        bootbox.confirm('Are you sure you want to delete this section?', function(result){

            if(result === true){

                delete_section(item);
            }

        })


    });

    //SAVE
    $('#page-builder-list').on('click', '.page-builder__page .page-builder__item .page-builder__item-buttons .btn-success', function(e){

        e.preventDefault();
        var pageName = $(this).closest('.page-builder__page').find('.panel-title').html();
        var item = $(this).closest('.page-builder__item');
        item.addClass('in');
        var data = {};
        data.id = item.data('id');
        item.find('input,select,textarea').each(function(input){
            var name = $(this).attr('name');
            data[name] = $(this).val();
        });

        //RICH TEXT EDITOR
        item.find('.field-rich-text-editor').each(function(input){
            data.content = $(this).html();
        });

        var url = item.data('url');

        $.ajax({
            type : 'PUT',
            data: data,
            url : url,
            success: function(response){
                if(response != undefined){

                    if(response.response == 'success'){
                        //show_success_notify('Page saved.')
                        show_success_notify('Section on ' + pageName + ' saved.');
                        reset_sortable_page_items();
                        reset_sortable_pages();
                    }

                }
            }
        })


    });

    //Duplicate Component
    $('.page-builder__page').on('click','.page-builder__item .icon-duplicate-section', function(e){
        e.preventDefault();
        e.stopPropagation();
        var item = $(this);

        bootbox.confirm('Are you sure you want to duplicate this item?', function(result){

            if(result === true){

                var section_id = item.closest('.page-builder__item').data('id');


                var url = item.closest('.page-builder__item').data('url') + '/duplicate';

                $.ajax({
                    type: 'POST',
                    url : url,
                    data  : {},
                    success : function(response){
                        console.log(response);
                        if(response.response != undefined && response.response == 'success'){


                            var url = item.closest('.page-builder__page').data('url') + '/'+ response.data.id;

                            $.ajax({
                                type: 'GET',
                                url : url,
                                success: function(response){
                                    item.closest('.page-builder__item').last().after(response);

                                    $('#page-builder-list .page-builder__page').each(function(e){
                                        initiate_image_upload($(this));
                                        initiate_document_upload($(this));
                                        initiate_video_upload($(this));
                                        initiate_audio_upload($(this));
                                    })
                                }
                            })

                            reset_sortable_page_items();
                            reset_sortable_pages();
                            show_success_notify('Item duplicated successfully');

                        }else{

                            show_error_notify('There was an error duplicating the section.');

                        }

                    }
                });


            }

        })
    })


    //Add new component
    $('.page-builder__add-content').on('click', '.add-content__item-button', function(e){
        e.preventDefault();
        var type = $(this).data('link');
        var url = $('#page-builder-list').find('.page-builder__page:not(".hidden")').data('url');

        if(url != undefined){

            var section_url = url + '/' + type;


            $.ajax({
                'type' : 'POST',
                'url'   : section_url,
                success: function (response) {
                    if(response != null){
                        $('textarea').removeClass('new_ta');
                        if($('.page-builder__page:not(".hidden")').find('.page-builder__items .page-builder__item').length == 0){
                            $('.page-builder__page:not(".hidden")').find('.page-builder__items').last()
                                .prepend(response);
                        }else{
                            $('.page-builder__page:not(".hidden")').find('.page-builder__items .page-builder__item').last()
                                .after(response);
                        }

                        reset_sortable_page_items();
                        reset_sortable_pages();

                        var page = $('.page-builder__page:not(".hidden")');
                        initiate_image_upload(page);
                        initiate_document_upload(page);
                        initiate_video_upload(page);
                        initiate_audio_upload(page);
                        
                        tinymce.init({ 
                            selector: 'textarea.new_ta',
                            plugins: "paste",
                            menubar: "edit",
                            toolbar: "paste",
                            paste_as_text: true,
                            setup : function(ed) {
                                ed.on('change', function(e) {
                                    console.log(ed.id);
                                    //console.log(e);
                                    $("#"+ed.id).html(ed.getContent());
                                });
                                ed.on('keyup', function(e) {
                                    console.log(ed.id);
                                    //console.log(e);
                                    $("#"+ed.id).html(ed.getContent());
                                });
                            }
                        });

                    }
                }
            })

        }
    });

    /**
     * Check for page and alert if none exist
     */
     $('.add-content__item-button').click(function(){
       if(!$('.page-builder__page').length){
         show_error_notify('Please add a page to continue') ;
       }
     }) ;


    /** RICH TEXT EDITOR **/
    richtexteditor();
    function richtexteditor(){
        tinymce.init({ 
            selector:'.field-rich-text-editor',
            plugins: "paste",
            paste_as_text: true,
            setup : function(ed) {
                    ed.on('change', function(e) {
                        $("#"+ed.id).html(decodeURIComponent(encodeURI(ed.getContent())));
                    });
                    ed.on('keyup', function(e) {
                        $("#"+ed.id).html(decodeURIComponent(encodeURI(ed.getContent())));
                    });
                }
        });
    }
    // $('.field-rich-text-editor').wysiwyg();


    /** PLUPLOAD - DOCUMENT **/
    function initiate_document_upload($page){

        var id = $page.data('id');
        var $plupload = $page.find('.document-input-plupload').each(function(e){
            var uploader = $(this);
            var section = $(this).closest('.page-builder__item--document-upload');
            var section_id = section.data('id');

            uploader.plupload({
                runtimes: 'html5,flash,silverlight,html4',
                url: "/learning/course/" + course_id + '/lessons/' + lesson_id + '/upload/document',

                // Maximum file size
                max_file_size: '20mb',

                // Enable ability to drag'n'drop files onto the widget (currently only HTML5 supports that)
                dragdrop: true,
                multipart_params : {
                    "lesson_id" : lesson_id,
                    "course_id" : course_id,
                    "page_id"   : id,
                    'section_id' : section_id
                },
                // Flash settings
                flash_swf_url: '/plupload/js/Moxie.swf',

                // Silverlight settings
                silverlight_xap_url: '/plupload/js/Moxie.xap',
                init: {
                    PostInit: function(uploader) {
                        // $plupload.find('.plupload_start').on('click', function(){
                        //     uploader.start();
                        // })

                    },
                    FilesAdded: function(uploader, files) {
                        uploader.start();
                        pluploadFiles = files;
                    },
                    UploadComplete: function(uploader, files) {
                    },
                    FileUploaded: function(uploader, files, object) {
                        var response = JSON.parse(object.response);
                        section.find('li.document-downloads__item').last().clone();
                        // section.find('li.document-downloads__item').last().find('a.document-downloads__item-btn').attr('href',response.data.url)
                            //.find('.document-downloads__item-icon').after(' ' + response.data.name)

                    }
                }
            })
        })


    }


    $('.page-builder__page').on('click','.document-downloads__btn-delete', function(e){
        e.preventDefault();
        var id = $(this).closest('li').data('id');
        var item = $(this).closest('li');
        var section = $(this).closest('.page-builder__item--document-upload');
        bootbox.confirm('Are you sure you want to delete this document?', function(result){
            if(result === true){

                $.ajax({
                    type : 'DELETE',
                    url : section.data('url') + '/document/' + id,
                    data: {},
                    success: function(response){
                        if(response.response != undefined && response.response == 'success'){
                            item.fadeOut();
                            show_success_notify('Document Deleted');
                        }
                    }
                })

            }


        })


    });

    /** PLUPLOAD - IMAGE **/
    function initiate_image_upload($page){
        var id = $page.data('id');
        var $plupload = $page.find('.page-builder__item--image .js__plupload').each(function(e){
            var uploader = $(this);
            var section = $(this).closest('.page-builder__item--image');
            var section_id = section.data('id');
            uploader.plupload({
                // General settings
                runtimes: 'html5,flash,silverlight,html4',
                url: "/learning/course/" + course_id + '/lessons/' + lesson_id + '/upload/image',

                // Maximum file size
                max_file_size: '20mb',

                // Specify what files to browse for.
                filters: [{
                    title: "Image files",
                    extensions: "jpg,gif,png"
                }],

                // Enable ability to drag'n'drop files onto the widget (currently only HTML5 supports that)
                dragdrop: true,

                multi_selection:false,


                // Flash settings
                flash_swf_url: '/plupload/js/Moxie.swf',

                // Silverlight settings
                silverlight_xap_url: '/plupload/js/Moxie.xap',

                multipart_params : {
                    "lesson_id" : lesson_id,
                    "course_id" : course_id,
                    "page_id"   : id,
                    'section_id' : section_id
                },

                init: {
                    PostInit: function(e) {
                    },
                    FilesAdded: function(uploader, files) {
                        uploader.start();
                        pluploadFiles = files;

                    },
                    UploadComplete: function(uploader, files) {
                        uploader.splice();
                        uploader.refresh();
                    },
                    FileUploaded: function(uploader, files, object) {
                        var response = JSON.parse(object.response);
                        if(response.response == "success") {
                            $plupload.addClass('hidden');
                            section.find('.messenger__image-placeholder').removeClass('hidden').find('img').attr('src',response.data.url);
                            show_success_notify('Image uploaded successfully');
                            
                        }


                    }
                }

            });
        });

    }

    /** PLUPLOAD - AUDIO **/
    function initiate_audio_upload($page){
        var id = $page.data('id');
        var $plupload = $page.find('.page-builder__item--audio-upload .js__plupload').each(function(e){
            var uploader = $(this);
            var section = $(this).closest('.page-builder__item--audio-upload');
            var section_id = section.data('id');
            uploader.plupload({
                // General settings
                runtimes: 'html5,flash,silverlight,html4',
                url: "/learning/course/" + course_id + '/lessons/' + lesson_id + '/upload/audio',

                // Maximum file size
                max_file_size: '100mb',

                // Specify what files to browse for.
                filters: [{
                    title: "Audio files",
                    extensions: "pcm,wav,aiff,mp3,aac,ogg,wma,flac,alac,m4a"
                }],

                // Enable ability to drag'n'drop files onto the widget (currently only HTML5 supports that)
                dragdrop: true,

                multi_selection:false,


                // Flash settings
                flash_swf_url: '/plupload/js/Moxie.swf',

                // Silverlight settings
                silverlight_xap_url: '/plupload/js/Moxie.xap',

                multipart_params : {
                    "lesson_id" : lesson_id,
                    "course_id" : course_id,
                    "page_id"   : id,
                    'section_id' : section_id
                },

                init: {
                    PostInit: function(e) {
                    },
                    FilesAdded: function(uploader, files) {
                        uploader.start();
                        pluploadFiles = files;

                    },
                    UploadComplete: function(uploader, files) {
                        uploader.splice();
                        uploader.refresh();
                    },
                    FileUploaded: function(uploader, files, object) {
                        var response = JSON.parse(object.response);
                        if(response.response == "success") {
                            $plupload.addClass('hidden');
                            var audio = section.find('.audio-upload-placeholder').removeClass('hidden').find('audio');//.find('source');//.attr('src',response.data.url);
                            audio.find('source').attr('src', response.data.url);

                            audio[0].pause();
                            audio[0].load();//suspends and restores all audio element

                            show_success_notify('Audio uploaded successfully');
                            
                        }


                    }
                }

            });
        });

    }

    /** PLUPLOAD - VIDEO **/
    function initiate_video_upload($page){
        var id = $page.data('id');
        var $plupload = $page.find('.page-builder__item--video-upload .js__plupload').each(function(e){
            var uploader = $(this);
            var section = $(this).closest('.page-builder__item--video-upload');
            var section_id = section.data('id');
            
            uploader.plupload({
                // General settings
                runtimes: 'html5,flash,silverlight,html4',
                url: "/learning/course/" + course_id + '/lessons/' + lesson_id + '/upload/video',

                // Maximum file size
                max_file_size: '200mb',

                // Specify what files to browse for.
                filters: [{
                    title: "video files",
                    extensions: "mp4,mov,ogg,webm,avi,mpg,mpeg,3gp,wmv"
                }],

                // Enable ability to drag'n'drop files onto the widget (currently only HTML5 supports that)
                dragdrop: true,

                multi_selection:false,
                // Flash settings
                flash_swf_url: '/plupload/js/Moxie.swf',

                // Silverlight settings
                silverlight_xap_url: '/plupload/js/Moxie.xap',

                multipart_params : {
                    "lesson_id" : lesson_id,
                    "course_id" : course_id,
                    "page_id"   : id,
                    'section_id' : section_id
                },

                init: {
                    PostInit: function(e) {
                    },
                    FilesAdded: function(uploader, files) {
                        
                        if(files.length > 1){
                            
                            $(files).each(function(i, file){
                                uploader.removeFile(file);    
                            });
                            uploader.refresh();
                            alert('Upload element accepts only 1 file at a time.');
                        }else{
                        
                            uploader.start();
                            pluploadFiles = files;
                        }
                    },
                    UploadComplete: function(uploader, files) {
                        uploader.splice();
                        uploader.refresh();
                    },
                    FileUploaded: function(uploader, files, object) {
                        var response = JSON.parse(object.response);
                        if(response.response == "success") {

                            show_success_notify('video uploaded successfully');
                            
                            $plupload.addClass('hidden');
                            section.find('label').addClass('hidden');
                            section.find('p').removeClass('hidden');
                            
                            $.post( "/learning/course/" + response.data.course_id + '/lessons/' + response.data.lesson_id + '/uuid/' + response.data.uuid + '/filecheck', function( fcResponse ) {
                                
                                if(fcResponse.response == 'success'){
                                    
                                    section.find('p').addClass('hidden');
                                    section.find('label').removeClass('hidden');
                                    $plupload.addClass('hidden');
                                    
                                    var video = section.find('.messenger__video-upload-placeholder').find('video');

                                    //alert(video.attr('id'));
                                    //alert(fcResponse.url);
                                    videojs(video.attr('id'), {}, function () {
                                        this.reset();
                                        this.poster(fcResponse.posterUrl);
                                        this.src(fcResponse.url);
                                        this.load();
                                        section.find('.messenger__video-upload-placeholder').removeClass('hidden');
                                    });
                                    
                                }
                            });
                            
                        } 


                    }
                }

            });
        });

    }


    /** PRIVATE FUNCTIONS **/

    function delete_section(section){

        var url = section.data('url');

        if(url != undefined){

            $.ajax({
                type : 'DELETE',
                url : url ,
                data: {},
                success: function(response){

                    if(response.response == 'success'){
                        show_success_notify('Deleted');
                        section.fadeOut(1000, function(){
                        })

                    }

                }
            })

        }
    }

    function duplicate_page(course_id, lesson_id, page_id){

        var url = '/learning/course/' + course_id + '/lessons/' + lesson_id + '/page/' + page_id + '/duplicate';

        $.ajax({
            type: 'POST',
            url : url,
            data: {},
            success: function(response){

                if(response.response == 'success'){
                    var page_id = response.data._id;
                    //Button
                    var new_url = '/learning/course/' + course_id + '/lessons/' + lesson_id + '/page/' + page_id + '/button';
                    $.ajax({
                        type : 'GET',
                        'url' : new_url,
                        success: function(response){

                            $('.page-list .list-group').append(response);

                        }
                    });


                    //Page Model
                    var new_url = '/learning/course/' + course_id + '/lessons/' + lesson_id + '/page/' + page_id + '/edit';
                    $.ajax({
                        type : 'GET',
                        'url' : new_url,
                        success: function(response){
                            $('#page-builder-list .page-builder__page').last().after(response)
                            $('#page-builder-list #lesson-page-' + page_id).addClass('hidden');

                        }
                    });

                    show_success_notify('Page duplicated.');
                    reset_sortable_page_items();
                    reset_sortable_pages();

                }else{
                    show_error_notify('There was a problem duplicating the page.');
                }

            }
        })

    }

    function save_page(page_name){
        var url = $('.lesson-add-page-btn').data('url');
        $.ajax({
            type: 'POST',
            url : url,
            data : { 'title' : page_name},
            success: function(response){

                if(response.response == 'success'){
                    var page_id = response.data._id;
                    //Button
                    var new_url = url + '/' + page_id + '/button';
                    $.ajax({
                        type : 'GET',
                        'url' : new_url,
                        success: function(response){

                            $('.page-list .list-group').append(response);

                        }
                    });


                    //Page Model
                    var new_url = url + '/' + page_id + '/edit';
                    $.ajax({
                        type : 'GET',
                        'url' : new_url,
                        success: function(response){
                            if($('#page-builder-list .page-builder__page').length){
                                $('#page-builder-list .page-builder__page').last().after(response)
                                $('#page-builder-list #lesson-page-' + page_id).addClass('hidden');
                            }else{
                                $('#page-builder-list').prepend(response);
                            }

                            reset_sortable_page_items();
                            reset_sortable_pages();

                        }
                    });

                }else{

                    show_error_notify('There was an error adding page. Please try again later');
                }
            }

        })

    }

    function delete_page(course_id, lesson_id, page_id){

        var url = '/learning/course/' + course_id + '/lessons/' + lesson_id + '/page/' + page_id;

        $.ajax({
            type : 'DELETE',
            data: {},
            url : url,
            success: function(response){

                if(response.response != undefined && response.response == 'success'){
                    show_success_notify('Page deleted');
                    $('.page-list').find('li[data-id="'+page_id+'"]').fadeOut().remove();
                    $('#page-builder-list #lesson-page-'+page_id).fadeOut().remove();
                    reset_sortable_page_items();
                    reset_sortable_pages();
                }else{
                    show_error_notify('There was an error deleting the page.');
                }

            }
        })

        reset_sortable_pages();


    }


    function show_success_notify(message){

        $.notify({
            message : message
        },{
            delay: 2000,
            placement: {
                from: 'bottom',
                align: 'right'
            },
            type: 'success'
        })

    }

    function show_loading_notify(){

        $.notify({
            message: 'Updating...',
        },{
            delay: 2000,
            placement: {
                from: 'bottom',
                align: 'right'
            },
        })


    }

    function show_error_notify(message){

        $.notify({
            message: message
        },{
            delay: 2000,
            placement: {
                from: 'bottom',
                align : 'right'
            },
            type: 'danger'
        })

    }

});