$(document).ready(function(){
	$(document).on('change', 'select[name="pass_mark"], select[name="amount_questions"]', function(){
		var element = $(this);
		var url = element.closest('.panel').data('url');
		var data = {};
        //if($('select[name="pass_mark"]').val() <= $('select[name="amount_questions"]').val()){
    		data['option'] = element.attr('name');
    		data['value'] = element.val();
    		/*$.ajax({
                'type': 'POST',
                'data': data,
                'url' : url,
                success: function (response) {
                	
                }
            })
        }else{
            $('select[name="pass_mark"]').val($('select[name="amount_questions"]').val());
            alert("Pass mark couldn't be higher than amount of questions");
        }*/

	});
	//Add new component
    $('.page-builder__add-content').on('click', '.add-content__item-button', function(e){
        
        e.preventDefault();
        var type = $(this).data('link');
        var url = $('#test-builder-list').find('.page-builder__page:not(".hidden")').data('url');
  
        if(url != undefined){

            var section_url = url + '/' + type;

            $.ajax({
                'type' : 'GET',
                'url'   : section_url,
                success: function (response) {
                	
                    if(response != null){
                        $('textarea').removeClass('new_ta');
                        if($('.page-builder__page:not(".hidden")').find('.page-builder__items .page-builder__item').length == 0){
                            $('.page-builder__page:not(".hidden")').find('.page-builder__items').last()
                                .prepend(response);
                        }else{
                            $('.page-builder__page:not(".hidden")').find('.page-builder__items .page-builder__item').last()
                                .after(response);
                        }

                        reset_sortable_page_items();
                        reset_sortable_pages();
                        var page = $('.page-builder__page:not(".hidden")');
                        tinymce.init({ 
                            selector: 'textarea.new_ta',
                            setup : function(ed) {
                                ed.on('change', function(e) {
                                    //console.log(ed.id);
                                    //console.log(e);
                                    $("#"+ed.id).html(ed.getContent());
                                });
                                ed.on('keyup', function(e) {
                                    //console.log(ed.id);
                                    //console.log(e);
                                    $("#"+ed.id).html(ed.getContent());
                                });
                            }
                        });
                        if(type == 'question-test'){
                            increase();
                        }
                    }
                }
            })

        }
    });
    var increase = function(){
        var valueInt = parseInt($('select[name="pass_mark"]').children().last().html())+1;
        if(valueInt>1){
            $('select[name="pass_mark"]').append('<option value="'+valueInt+'">'+valueInt+'</option>');
            $('select[name="amount_questions"]').append('<option value="'+valueInt+'">'+valueInt+'</option>');
        }else{
            $('select[name="pass_mark"]').children().last().html(valueInt);
            $('select[name="amount_questions"]').children().last().html(valueInt);
            $('select[name="pass_mark"]').children().last().val(valueInt);
            $('select[name="amount_questions"]').children().last().val(valueInt);
        }
        $('.nQuestions').html(valueInt);
    };
    var decrease = function(){
        var valueInt = parseInt($('select[name="pass_mark"]').children().last().html())-1;
        if(valueInt>0){
            $('select[name="pass_mark"]').children().last().remove();
            $('select[name="amount_questions"]').children().last().remove();
        }else{
            $('select[name="pass_mark"]').children().last().html(valueInt);
            $('select[name="amount_questions"]').children().last().html(valueInt);
            $('select[name="pass_mark"]').children().last().val(valueInt);
            $('select[name="amount_questions"]').children().last().val(valueInt);
        }
        $('.nQuestions').html(valueInt);
    };
    richtexteditor();
    function richtexteditor(){
        tinymce.init({ 
            selector:'.field-rich-text-editor',
            setup : function(ed) {
                    ed.on('change', function(e) {
                        $("#"+ed.id).html(decodeURIComponent(ed.getContent()));
                    });
                    ed.on('keyup', function(e) {
                        $("#"+ed.id).html(decodeURIComponent(ed.getContent()));
                    });
                }
        });
    }

	//DELETE
    $('.page-builder__page').on('click', '.page-builder__item .icon-delete-section', function(e){
    	var div = $(this).closest('.page-builder__item');
        e.stopPropagation();
        e.preventDefault();
        var item = $(this).closest('.page-builder__item ');
        item.addClass('in');
        bootbox.confirm('Are you sure you want to delete this section?', function(result){

            if(result === true){

                delete_section(item);
                if(div.hasClass('page-builder__item--question')){
                    decrease();
                }
            }

        })


    });
//SAVE
    $(document).on('click', '.page-builder__page .page-builder__item .page-builder__item-buttons .btn-success', function(e){

        e.preventDefault();

        var item = $(this).closest('.page-builder__item');
        item.addClass('in');
        var data = {};
        data.id = item.data('id');
        item.find('.frmField').each(function(input){
            var name = $(this).attr('name');
            data[name] = $(this).val();
        });
        var error = false;
        if(item.find('.inpOpt').length > 0){
            if(item.find('.inpOpt').closest('.form-horizontal').find('input[name="option-answer"]:checked').length == 0 ){
                error = true;
            }
        	var options = Array();
        	item.find('.inpOpt').each(function(input){
        		var opt = {};
        		opt['id'] = $(this).closest('.form-group').data('id');
        		opt['value'] = $(this).val();
        		opt['correct'] = $(this).closest('.form-horizontal').find('input[name="option-answer"]').is(':checked');
            	options.push(opt);
        	});
        	data['options'] = options;
        }
        //RICH TEXT EDITOR
        item.find('.field-rich-text-editor').each(function(input){
            data.content = $(this).html();
        });
        alertSave(item, 'hidden');
        var url = item.data('url');
        if(error == false){
            $.ajax({
                type : 'PUT',
                data: data,
                url : url,
                success: function(response){
                    if(response != undefined){

                        if(response.response == 'success'){
                            show_success_notify('Page saved.')
                           // reset_sortable_page_items();
                            //reset_sortable_pages();
                            
                        }

                    }
                }
            })
        }else{
            show_error_notify('You need to indicate at least one correct option for this question.');
        }
    });
    $(document).on('change', '.frmField', function(){
        var item = $(this).closest('.page-builder__item');
        alertSave(item, 'show');
    });
    function alertSave(element, action){
        if(element.find('.test_save_alert').length > 0){
            if(action == 'hidden'){
                element.find('.test_save_alert').addClass('hidden');
            }else if(action == 'show'){
                element.find('.test_save_alert').removeClass('hidden');
            }
        }
    }
    //Add new option
    $(document).on('click', '.addOption', function(e){
        var element = $(this);
        e.preventDefault();
        var question_type = element.closest('.page-builder__item').find('select[name="question_type"]').val();
        var type = $(this).parent().parent().data('id');
        var url = $(this).parent().parent().data('url')+'/'+question_type;
        if(url != undefined){
            var section_url = url ;
            $.ajax({
                'type' : 'POST',
                'url'   : section_url,
                success: function (response) {
                	
                    if(response != null){
						element.parent().parent().parent().parent().find('.questions-wrapper').append(response);

                    }
                }
            })
        }
    });
    //remove option
    $(document).on('click', '.removeOption', function(e){
        var element = $(this);
        var url = element.closest('.questions-options').data('url');
        var optionid = element.closest('.form-group').data('id');
        e.preventDefault();
        if(url != undefined){
            var section_url = url + '/' + optionid;
            $.ajax({
                'type' : 'DELETE',
                'url'   : section_url,
                success: function (response) {
                    if(response != null && response.response == 'success'){
						element.closest('.form-horizontal').fadeOut(1000, function(){
                        	$(this).remove();
                        });


                    }
                }
            })

        }
    });
    //change question type
    $(document).on('change', 'select[name="question_type"]', function(e){
        var element = $(this);
        var url = element.closest('.form-group').data('url')+'/'+element.val()
        e.preventDefault();
        var inputs = element.closest('.page-builder__item-content').find('input[name="option-answer"]');
        
        if(url != undefined){
            var section_url = url ;
            $.ajax({
                'type' : 'POST',
                'url'   : section_url,
                success: function (response) {
                    if(response != null && response.response == 'success'){
						var newType = '';
				        if(element.val() == 0){
				        	newType = "radio";
				        }else if(element.val() == 1){
				        	newType = "checkbox";
				        }

				        inputs.each(function(){
				        	$(this).prop('type', newType);
				        });
                    }
                }
            })
        }
    });
        /** PRIVATE FUNCTIONS **/

    function delete_section(section){

        var url = section.data('url');

        if(url != undefined){

            $.ajax({
                type : 'DELETE',
                url : url ,
                data: {},
                success: function(response){

                    if(response.response == 'success'){
                        show_success_notify('Deleted');
                        section.fadeOut(1000, function(){
                        })

                    }

                }
            })

        }
    }
        //Duplicate Component
    $('.page-builder__page').on('click','.page-builder__item .icon-duplicate-section', function(e){
        e.preventDefault();
        e.stopPropagation();
        var item = $(this);

        bootbox.confirm('Are you sure you want to duplicate this item?', function(result){

            if(result === true){

                var section_id = item.closest('.page-builder__item').data('id');

                var url = item.closest('.page-builder__item').data('url') + '/duplicate';

                $.ajax({
                    type: 'POST',
                    url : url,
                    data  : {},
                    success : function(response){

                        if(response != undefined){
                            item.closest('.page-builder__item').last().after(response);
                            if(item.closest('.page-builder__item').hasClass('page-builder__item--question')){
                                increase();
                            }
                            //reset_sortable_page_items();
                            //reset_sortable_pages();
                            show_success_notify('Item duplicated successfully');

                        }else{

                            show_error_notify('There was an error duplicating the question.');

                        }

                    }
                });


            }

        })
    })
    var sortable = [];
    function setup_sortable_page_items(){

            var page = document.getElementById('page-builder-items');

            var sorted = Sortable.create(page, {
                handle: ".page-builder__item-title",
                onSort: function(event){
                    if((event.item.className).indexOf('page-builder__item--text-rich') > -1){
                        tinymce.get($(event.item).find('.field-rich-text-editor').prop('id')).remove();
                    }
                    show_loading_notify();

                    var page_items = $('#'+event.target.id);
                    var page_item_ids = [];

                    page_items.children('.page-builder__item').each(function(){
                        page_item_ids.push($(this).data('id'));
                    })

                    url = '/learning/course/' + course_id + '/lessons/' + lesson_id + '/test/edit/section/sort';

                    $.ajax({
                        type: 'PUT',
                        url : url,
                        data: {section_ids : page_item_ids},
                        success: function(response){
                            //alert(response.response);
                            if(response.response != undefined && response.response == 'success'){
                                show_success_notify('Sections order updated.');
                            }else{
                                show_error_notify('There was an error updating your sections order.');
                            }
                            richtexteditor();
                        }
                    })


                }

            });

            sortable.push(sorted);
    }

    function reset_sortable_page_items(){


        setup_sortable_page_items();
    }
    var sortable = [];
    var sortable_pages = null;

    setup_sortable_page_items();
    function reset_sortable_pages(){
        //sortable_pages.destroy();
        //setup_sortable_pages();
    }


    function show_success_notify(message){

        $.notify({
            message : message
        },{
            delay: 2000,
            placement: {
                from: 'bottom',
                align: 'right'
            },
            type: 'success'
        })

    }

    function show_loading_notify(){

        $.notify({
            message: 'Updating...',
        },{
            delay: 2000,
            placement: {
                from: 'bottom',
                align: 'right'
            },
        })


    }

    function show_error_notify(message){

        $.notify({
            message: message
        },{
            delay: 2000,
            placement: {
                from: 'bottom',
                align : 'right'
            },
            type: 'danger'
        })

    }

});