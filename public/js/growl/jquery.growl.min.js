var _createClass=function(){function defineProperties(target,props){for(var i=0;i<props.length;i++){var descriptor=props[i];descriptor.enumerable=descriptor.enumerable||!1,descriptor.configurable=!0,"value"in descriptor&&(descriptor.writable=!0),Object.defineProperty(target,descriptor.key,descriptor)}}return function(Constructor,protoProps,staticProps){return protoProps&&defineProperties(Constructor.prototype,protoProps),staticProps&&defineProperties(Constructor,staticProps),Constructor}}();function _classCallCheck(instance,Constructor){if(!(instance instanceof Constructor))throw new TypeError("Cannot call a class as a function")}(function(){"use strict";var $,Animation,Growl;$=jQuery,Animation=function(){var Animation=function(){function Animation(){_classCallCheck(this,Animation)}return _createClass(Animation,null,[{key:"transition",value:function transition($el){var el,ref,result,type;for(type in el=$el[0],ref=this.transitions)if(result=ref[type],null!=el.style[type])return result}}]),Animation}();return Animation.transitions={webkitTransition:"webkitTransitionEnd",mozTransition:"mozTransitionEnd",oTransition:"oTransitionEnd",transition:"transitionend"},Animation}(),Growl=function(){var Growl=function(){function Growl(){var settings=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};_classCallCheck(this,Growl),this.render=this.render.bind(this),this.bind=this.bind.bind(this),this.unbind=this.unbind.bind(this),this.mouseEnter=this.mouseEnter.bind(this),this.mouseLeave=this.mouseLeave.bind(this),this.click=this.click.bind(this),this.close=this.close.bind(this),this.cycle=this.cycle.bind(this),this.waitAndDismiss=this.waitAndDismiss.bind(this),this.present=this.present.bind(this),this.dismiss=this.dismiss.bind(this),this.remove=this.remove.bind(this),this.animate=this.animate.bind(this),this.$growls=this.$growls.bind(this),this.$growl=this.$growl.bind(this),this.html=this.html.bind(this),this.content=this.content.bind(this),this.container=this.container.bind(this),this.settings=$.extend({},Growl.settings,settings),this.initialize(this.settings.location),this.render()}return _createClass(Growl,null,[{key:"growl",value:function growl(){var settings=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return new Growl(settings)}}]),_createClass(Growl,[{key:"initialize",value:function initialize(location){var id;return $("body:not(:has(#"+(id="growls-"+location)+"))").append('<div id="'+id+'" />')}},{key:"render",value:function render(){var $growl;$growl=this.$growl(),this.$growls(this.settings.location).append($growl),this.settings.fixed?this.present():this.cycle()}},{key:"bind",value:function bind(){var $growl=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.$growl();return $growl.on("click",this.click),this.settings.delayOnHover&&($growl.on("mouseenter",this.mouseEnter),$growl.on("mouseleave",this.mouseLeave)),$growl.on("contextmenu",this.close).find("."+this.settings.namespace+"-close").on("click",this.close)}},{key:"unbind",value:function unbind(){var $growl=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.$growl();return $growl.off("click",this.click),this.settings.delayOnHover&&($growl.off("mouseenter",this.mouseEnter),$growl.off("mouseleave",this.mouseLeave)),$growl.off("contextmenu",this.close).find("."+this.settings.namespace+"-close").off("click",this.close)}},{key:"mouseEnter",value:function mouseEnter(event){var $growl;return($growl=this.$growl()).stop(!0,!0)}},{key:"mouseLeave",value:function mouseLeave(event){return this.waitAndDismiss()}},{key:"click",value:function click(event){if(null!=this.settings.url)return event.preventDefault(),event.stopPropagation(),window.open(this.settings.url)}},{key:"close",value:function close(event){var $growl;return event.preventDefault(),event.stopPropagation(),($growl=this.$growl()).stop().queue(this.dismiss).queue(this.remove)}},{key:"cycle",value:function cycle(){var $growl;return($growl=this.$growl()).queue(this.present).queue(this.waitAndDismiss())}},{key:"waitAndDismiss",value:function waitAndDismiss(){var $growl;return($growl=this.$growl()).delay(this.settings.duration).queue(this.dismiss).queue(this.remove)}},{key:"present",value:function present(callback){var $growl;return $growl=this.$growl(),this.bind($growl),this.animate($growl,this.settings.namespace+"-incoming","out",callback)}},{key:"dismiss",value:function dismiss(callback){var $growl;return $growl=this.$growl(),this.unbind($growl),this.animate($growl,this.settings.namespace+"-outgoing","in",callback)}},{key:"remove",value:function remove(callback){return this.$growl().remove(),"function"==typeof callback?callback():void 0}},{key:"animate",value:function animate($element,name){var direction=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"in",callback=arguments[3],transition;transition=Animation.transition($element),$element["in"===direction?"removeClass":"addClass"](name),$element.offset().position,$element["in"===direction?"addClass":"removeClass"](name),null!=callback&&(null!=transition?$element.one(transition,callback):callback())}},{key:"$growls",value:function $growls(location){var base;return null==this.$_growls&&(this.$_growls=[]),null!=(base=this.$_growls)[location]?base[location]:base[location]=$("#growls-"+location)}},{key:"$growl",value:function $growl(){return null!=this.$_growl?this.$_growl:this.$_growl=$(this.html())}},{key:"html",value:function html(){return this.container(this.content())}},{key:"content",value:function content(){return"<div class='"+this.settings.namespace+"-close'>"+this.settings.close+"</div>\n<div class='"+this.settings.namespace+"-title'>"+this.settings.title+"</div>\n<div class='"+this.settings.namespace+"-message'>"+this.settings.message+"</div>"}},{key:"container",value:function container(content){return"<div class='"+this.settings.namespace+" "+this.settings.namespace+"-"+this.settings.style+" "+this.settings.namespace+"-"+this.settings.size+"'>\n  "+content+"\n</div>"}}]),Growl}();return Growl.settings={namespace:"growl",duration:3200,close:"&#215;",location:"default",style:"default",size:"medium",delayOnHover:!0},Growl}(),this.Growl=Growl,$.growl=function(){var options=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Growl.growl(options)},$.growl.error=function(){var options=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},settings;return settings={title:"Error!",style:"error"},$.growl($.extend(settings,options))},$.growl.notice=function(){var options=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},settings;return settings={title:"Notice!",style:"notice"},$.growl($.extend(settings,options))},$.growl.warning=function(){var options=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},settings;return settings={title:"Warning!",style:"warning"},$.growl($.extend(settings,options))}}).call(this);