
$(document).ready(function() {
    // when a policy is changed
    $('select[name="policy_id"]').on('change', function(e) {
        e.preventDefault();

        // policy types
        var policy_type = [
            'Property',
            'Casualty',
            'Commercial Combined',
            'Construction-All-Risk'
        ];

        // get the current policy selected text
        var current = $(this).find(":selected").text();
        current = current.replace(/\s+/g, '-');
        // loop through policy types
        $.each(policy_type, function(index, value) {
            // if the current checked policy exists
            if (current.indexOf(value) != -1) {
                // show the policy
                $('#' + value).show();

                // if the value is commercial combined (array 2)
                if (value == policy_type[2]) {
                    // loop through the types again
                    $.each(policy_type, function(index, value) {
                        // show them all
                        $('#' + value).show();
                    });
                }
            }else{
                const pathname = window.location.pathname;
                console.log(pathname);
                if(current != '' && pathname == "/surveys/create") {
                    $('#' + value).hide();
                } else if(pathname == "/surveys/edit") {
                    $('#' + value).remove();
                }
            }
        });
    });

    var counters        = [];
    var container       = $("#table-fields tbody");

    var key, name, limit;

    // for adding input rows
    $(container).on('click', '.add-option', function (e) {
        e.preventDefault();

        // store the data info as variables
        key = $(this).data('id');
        name = $(this).data('name');
        limit = $(this).data('limit');

        // check to see if the row counter for specified key exists
        if (counters[key] == undefined) {
            // key didn't exist so it is added to the array
            counters[key] = 1;
        }

        var newRowHtml = '<tr class="field-data"><td class="col-xs-8"><input type="text" class="form-control" name="srf_key[]" data-field="srf"></td><td class="col-xs-8"><input type="text" class="form-control" name="srf_value[]" data-field="srf"><input type="hidden" name="srf_type[]" value="' + name + '" data-type="srf"></td><td class="col-xs-8"><a href="#" data-id="' + key + '" class="btn btn-danger remove-option pull-right" tabindex="-1">-</a></td></tr>';

        // make sure the rows haven't hit their limit
        if (counters[key] < limit) {
            // gets the closest tr until the next add option row
            var current = $(this).closest('tr').nextUntil('.add-option-row-a');

            // each row has a first element
            if (current.length == 0) {
                $(this).closest('tr').after(newRowHtml);
            } else {
                current.last().after(newRowHtml);
            }

            // increase the counter
            counters[key]++;
        }
    });

    // for removing input rows
    $(container).on('click', '.remove-option', function (e) {
        e.preventDefault();

        // remove the row
        $(this).parents('.field-data').remove();

        // decrease the counter
        counters[key]--;
    });

    // triger a change initially
    $('select[name="policy_id"]').trigger('change');

});
