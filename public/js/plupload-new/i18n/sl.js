// Slovenian (sl)
plupload.addI18n({"Stop Upload":"Ustavi prenos","Upload URL might be wrong or doesn't exist.":"URL za nalaganje je napačen ali ne obstaja.","tb":"tb","Size":"Velikost","Close":"<PERSON>ap<PERSON>","You must specify either browse_button or drop_element.":"","Init error.":"Napaka pri inicializaciji.","Add files to the upload queue and click the start button.":"Dodaj datoteke na seznam in klikni na gumb začni","List":"Seznam","Filename":"Ime datoteke","%s specified, but cannot be found.":"","Image format either wrong or not supported.":"Format slike je napačen ali ni podrpt.","Status":"Status","HTTP Error.":"Neznana HTTP napaka.","Start Upload":"Začni prenos","Error: File too large:":"Napaka: datoteka je prevelika:","kb":"kb","Duplicate file error.":"Datoteka je že na seznamu.","File size error.":"Datoteka je prevelika.","N/A":"Ni na voljo","gb":"gb","Error: Invalid file extension:":"Napaka: napačen tip datoteke:","Select files":"Izberi datoteke","%s already present in the queue.":"%s je že na seznamu.","Resoultion out of boundaries! <b>%s</b> runtime supports images only up to %wx%hpx.":"","File: %s":"Datoteka: %s","b":"b","Uploaded %d/%d files":"Naloženo %d/%d datotek","Upload element accepts only %d file(s) at a time. Extra files were stripped.":"Nalagalnik sprejme samo %d datotek na enkrat. Višek datotek je odstranjen iz seznama.","%d files queued":"%d datotek na seznamu","File: %s, size: %d, max file size: %d":"Datoteka: %s, velikost: %d, največja dovoljena velikost: %d","Thumbnails":"Sličice","Drag files here.":"Potegni datoteke sem.","Runtime ran out of available memory.":"Zmanjkalo je pomnilnika.","File count error.":"Napačno število datotek.","File extension error.":"Napačen tip datoteke.","mb":"mb","Add Files":"Dodaj datoteke"});