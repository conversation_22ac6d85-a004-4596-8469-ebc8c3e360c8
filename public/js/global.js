$(function() {
    $('#sector').change(function () {
        let sector = $(this).find('option:selected').text();
        if (sector === 'Responsible Business') {
            $('#bound-wrapper, #client-org-attachment, #bursary-wrapper, #loss-ratio-wrapper, #risk-grading-wrapper, #policy-numbers').hide();
            return;
        }
        $('#bound-wrapper, #client-org-attachment, #bursary-wrapper, #loss-ratio-wrapper, #risk-grading-wrapper, #policy-numbers').show();
    });

    $('form.limit-search input[type=text]').keypress(function(e) {
        if (e.which == 13) { // enter key
            $('form.limit-search').submit();
            return false;
        }
    });

    // modals
    $('.use-modal').on('click', use_modal);
    $(document).on('click', '.assign-users__submit', btn_modal);
    // select2
    if ($('select.select2').length > 0) {
        $('select.select2').select2();
    }


    $('a.btn').on('click', function(e) {
        if ($(this).hasClass('disabled')) {
            e.preventDefault();
        }
    });

    // main menu toggle
    $("#menu-toggle").click(function(e) {
        e.preventDefault();
        $("#wrapper").toggleClass("toggled");
    });

    // search nav
    $('.xs-nav').click(function(e) {
        e.preventDefault();
        $('header .search').slideToggle();
    });

    // nav action
    $('nav .parent > a').click(function(e) {
        e.preventDefault();
        $(this).next().slideToggle();
        $(this).parent().toggleClass('active')
    });

    $('.parent.active').each(function() {
        $('ul', this).slideToggle();
    });

    // pagination limits
    $('#limit').change(function() {
        $('.limit-search').submit();
    });

    // accordions
    if ($('.accordion').length) {
        $('.accordion').accordion({
            collapsible: true,
            active: false,
            animate: 100,
            activate: function(event, ui) {
                $('.accordion .fa-stack-1x').removeClass('fa-minus').addClass('fa-plus');
                var h = $(ui.newHeader[0]);
                var i = $('.fa-stack-1x', ui.newHeader[0]);
                if (h.next().hasClass('ui-accordion-content-active')) {
                    i.removeClass('fa-plus');
                    i.addClass('fa-minus');
                } else {
                    i.removeClass('fa-minus');
                    i.addClass('fa-plus');
                }
            }
        });
    }
    // delete button confirmations
    $('.btn-delete,.icon-delete').on('click', function(e) {
        var self = $(this),
            item = self.data('item'),
            msg = 'Are you sure_?'.replace(
                '_',
                (typeof item !== 'undefined' ? ' you want to delete this ' + item : '')
            );
        bootbox.confirm(msg, function(result) {
            if (result) {
                switch (self.get(0).tagName.toLowerCase()) {
                    case 'input':
                    case 'button':
                        self.closest('form').submit();
                        break;

                    case 'a':
                        self.unbind('click').get(0).click();
                        break;
                    case 'label':
                        if ($('.form_delete').length >= 1) {
                            $('.form_delete').submit();
                        }
                        break;
                }
            }
        });

        e.preventDefault();
    });
    $('.btn-duplicate').on('click', function(e) {
        var self = $(this),
            item = self.data('item'),
            msg = 'New course name:';

        bootbox.prompt(msg, function(result) {
            if (result) {
                switch (self.get(0).tagName.toLowerCase()) {
                    case 'label':
                        if ($('.form_duplicate').length >= 1) {
                            $('.form_duplicate').find('input[name="title"]').val(result);
                            $('.form_duplicate').submit();
                        }
                        break;
                    case 'a':
                        jQuery.ajax({
                            url: self.attr('href'),
                            type: 'POST',
                            //dataType: 'json',
                            data: { title: result },
                            complete: function(xhr, textStatus) {
                                //called when complete
                            },
                            success: function(data, textStatus, xhr) {
                                if (data.response == 'success') {
                                    window.location.href = data.url;
                                } else {
                                    show_error_notify(data.message);
                                }
                            },
                            error: function(xhr, textStatus, errorThrown) {
                                //called when there is an error
                            }
                        });
                        break;
                }
            }
        });

        e.preventDefault();
    });
    // textareas with a maximum length
    $('textarea[maxlength]').each(function() {
        var self = $(this),
            id = self.attr('id'),
            text = '(remaining characters: _)',
            initial_value = self.attr('maxlength') - self.val().length;

        self.on('keyup keydown keypress', function() {
            var counter = self.next('.chrs'),
                value = this.maxLength - this.value.length;

            if (counter) {
                counter.html(text.replace('_', (value ? value : 0)));
            }
        }).after(
            '<span class="chrs">' + text.replace('_', initial_value) + '</span>'
        );
    });

    // date pickers
    $('input.datepickers').datepicker({
        'format': 'dd/mm/yyyy',
        autoclose: true,
        weekStart: 1,
        todayHighlight: true
    });

    //carousel
    var carousel = {};
    var count = 0;
    $('.carousel').each(function() {
        var h = $(this).prev();
        carousel['owl' + count] = $(this).owlCarousel({
            itemsCustom: [
                [320, 1],
                [768, 2],
                [992, 3]
            ],
            pagination: false,
            navigation: false
        });
        h.append('<a href="#" class="owl_next" data-carousel="' + count + '"><i class="fa fa-chevron-right"></i></a> <a href="#" class="owl_prev" data-carousel="' + count + '"><i class="fa fa-chevron-left"></i></a>');
        $(".owl_next", h).click(function(e) {
            e.preventDefault();
            var c = $(this).data('carousel');
            console.log(c);
            carousel['owl' + c].trigger('owl.next');
        });
        $(".owl_prev", h).click(function(e) {
            e.preventDefault();
            var c = $(this).data('carousel');
            carousel['owl' + c].trigger('owl.prev');
        });
        count++;
    });

});

// croner
function initiateSSO(url) {
    window.location = url;
}

function initiateSSO() {
    url = document.getElementById('PartnerSpId').value;
    if (document.getElementById('TargetResource') != null) {
        if (document.getElementById('TargetResource').value != "") {
            url += "&TargetResource=";
            url += urlencode(document.getElementById('TargetResource').value);
        }
    }
    if (document.getElementById('Binding') != null) {
        if (document.getElementById('Binding').value != "") {
            url += "&Binding=";
            url += document.getElementById('Binding').value;
        }
    }
    if (document.getElementById('RequestedFormat') != null) {
        if (document.getElementById('RequestedFormat').value != "") {
            url += "&RequestedFormat=";
            url += document.getElementById('RequestedFormat').value;
        }
    }
    window.location = url;
}

function createSSOLink() {
    url = document.getElementById('PartnerSpId').value;
    if (document.getElementById('TargetResource') != null) {
        if (document.getElementById('TargetResource').value != "") {
            url += "&TargetResource=";
            url += document.getElementById('TargetResource').value;
        }
    }
    if (document.getElementById('Binding') != null) {
        if (document.getElementById('Binding').value != "") {
            url += "&Binding=";
            url += document.getElementById('Binding').value;
        }
    }
    if (document.getElementById('RequestedFormat') != null) {
        if (document.getElementById('RequestedFormat').value != "") {
            url += "&RequestedFormat=";
            url += document.getElementById('RequestedFormat').value;
        }
    }
    document.getElementById('ssolink').value = url;
    document.getElementById('ssolink').select();
    copyToClipboard(document.getElementById('ssolink').value);
}

function initiateSLO() {
    url = document.getElementById('hiddenslolink').value;
    window.location = url;
}

function createSLOLink() {
    document.getElementById('slolink').value = document.getElementById('hiddenslolink').value;
    document.getElementById('slolink').select();
    copyToClipboard(document.getElementById('slolink').value);
}

function toggleBoolean(elementIdName) {
    var element = document.getElementById(elementIdName);
    if (element.value == "true") {
        element.value = "false";
    } else {
        element.value = "true";
    }
}
// left padding helper (eg. 1 -> 001 etc)
var padleft = function(value, padding, chr) {
    return (Array(++padding).join(chr || '0') + value).slice(-padding);
};

// ucwords helper
var ucwords = function(str) {
    return (str + '').replace(/^([a-z\u00E0-\u00FC])|\s+([a-z\u00E0-\u00FC])/g, function($1) {
        return $1.toUpperCase();
    });
};

var field_filter_init = function(el) {
    if (el) {
        var input = el.find(':input'),
            checked = input.filter(':checked'),
            selects = input.filter('select');

        input.on('change', function(e) {
            field_filter($(this));
        });

        if (checked.length) {
            checked.trigger('change');
        }

        if (selects.length) {
            selects.trigger('change');
        }
    }
};

// filtering form fields by data type
var field_filter = function(e) {
    var value = e.val(),
        form = e.closest('form'),
        filtered_fields = form.find('*[data-filter="' + e.attr('name') + '"][data-filter-value]');

    filtered_fields.each(function(index, el) {
        el = $(el);

        if (new RegExp("(\\s|^)" + value + "(\\s|$)").test(el.data('filter-value'))) {
            el.find(':input').removeAttr('disabled');
            el.show();
        } else {
            el.find(':input').attr('disabled', 'disabled');
            el.hide();
        }
    });
};

// load an anchor's URL in a bootbox modal
var use_modal = function(e) {
    var self = $(this);

    $.blockUI({
        css: {
            border: 'none',
            padding: '15px',
            backgroundColor: '#000',
            '-webkit-border-radius': '10px',
            '-moz-border-radius': '10px',
            opacity: .5,
            color: '#fff'
        }
    });

    switch (self[0].nodeName.toLowerCase()) {
        case 'a':
            $.get(self.attr('href'), function(data) {
                setup_modal(self, data);
            });
            break;
    }

    e.preventDefault();
};

var btn_modal = function(e) {
    $(this).addClass('hidden');
    $(this).parent().append('<img src="/img/owl/AjaxLoader.gif">');
};




// load an anchor's URL in a bootbox modal
var select_users = function(id, el) {
    if ($(".user_list").val() != '') {
        var selected_users = $(".user_list").val();
        var selected_users_array = selected_users.split(',');
    } else {
        var selected_users_array = new Array();
    }
    $(el).parents('tr').eq(0).toggleClass('active');
    if ($(el).parents('tr').eq(0).hasClass('active')) {
        selected_users_array.push(id);
    } else {
        //console.log(selected_users_array);
        var index = selected_users_array.indexOf(id);
        selected_users_array.splice(index, 1);
        //console.log(index);
        //console.log(id);
    }
    $(".user_list").val(selected_users_array.join());
    console.log(selected_users_array);
    return false;
};
var select_orgs = function(id, el) {
    if ($(".organisation_list").val() != '') {
        var selected_users = $(".organisation_list").val();
        var selected_users_array = selected_users.split(',');
    } else {
        var selected_users_array = new Array();
    }
    $(el).parents('tr').eq(0).toggleClass('active');
    if ($(el).parents('tr').eq(0).hasClass('active')) {
        $(el).find('span').eq(0).removeClass('hidden');
        selected_users_array.push(id);
    } else {
        $(el).find('span').eq(0).addClass('hidden');
        //console.log(selected_users_array);
        var index = selected_users_array.indexOf(id);
        selected_users_array.splice(index, 1);
        //console.log(index);
        //console.log(id);
    }
    $(".organisation_list").val(selected_users_array.join());
    //console.log(selected_users_array);
    return false;
};
var setup_modal = function(el, data) {
    var dom = $(data),
        modal = {
            show: false,
            title: dom.filter('h1').html(),
            message: dom.filter('.modal')
        },
        form = modal.message.find('form'),
        btns = 0;

    // redefine any form buttons as bootbox button objects
    if (form) {
        btns = form.find(' > input[type="submit"][class*="btn-"]');

        // solo submit button
        if (btns.length === 1 && btns.find('.btn-submit')) {
            // add a cancel button as well
            modal.buttons = {
                danger: {
                    label: 'Cancel',
                    className: 'btn-danger'
                },
                success: {
                    label: btns.first().val(),
                    className: 'btn-success',
                    callback: function() {
                        var error = 0;
                        if ($(this).find('.modal-footer .btn-success').html() == 'Add') {
                            if ($(this).find('.bootbox-body .field-filter input[name="method"]:checked').val() == 'new') {
                                $(this).find('.bootbox-body .form-new-course').each(function() {
                                    if ($(this).val() == null) {
                                        error++;
                                    }
                                });
                            } else {
                                if ($(this).find('.bootbox-body input[name="title"]').val() == null) {
                                    error++;
                                }
                            }
                            console.log($.trim($(this).find('.bootbox-body #source').val()) == '');
                            if ((error <= 1 && $(this).find('.bootbox-body .field-filter input[name="method"]:checked').val() == 'new') ||
                                (error < 1 && $(this).find('.bootbox-body .field-filter input[name="method"]:checked').val() == 'duplicate') && $.trim($(this).find('.bootbox-body #source').val()) != '') {
                                $(this).find('.bootbox-body > form').submit();
                            } else {
                                if ($(this).find('.bootbox-body .field-filter input[name="method"]:checked').val() == 'duplicate') {
                                    show_error_notify('Please complete Course Title and select one course to duplicate');
                                } else {
                                    show_error_notify('Please complete Course Title and either Categories or Organisations');
                                }
                            }
                        } else if ($(this).find('.modal-footer .btn-success').html() == 'Add Lesson') {
                            if ($.trim($(this).find('.bootbox-body #title').val()) != '') {
                                $(this).find('.bootbox-body > form').submit();
                            } else {
                                show_error_notify('Please complete Lesson Title');
                            }
                        }
                        return false;
                    },
                }
            };

            btns.remove();
        } else {
            modal.buttons = {};
        }
    }

    if (modal.message) {
        modal.message = modal.message.html();
    }

    bootbox.dialog(modal).on('show.bs.modal', function(e) {
        $(".assign-users__filter--branch").prop("disabled", true);
        $.unblockUI();
        // $.getScript( '/js/bootstrap-toolkit.min.js', function() {});
        // $.getScript( '/js/select2/select2.min.js', function() {});
        // $.getScript( '/js/interactions/app.9adb9510.js', function() {});
        // $.getScript( '/js/interactions/mockup-interactions.7e12aa83.js', function() {});
        // $.getScript( '/js/interactions/vendor.fa77d764.js', function() {});


        $(".assign-users__list").hide();
        var user_response;
        var bootbox = $(this);
        form = $(this).find('.bootbox-body > form');
        $('select').select2();

        $('#field-organisation').change(function() {
            $(".assign-users__list").hide();
            $('.assign-users__table tbody').html('');
            $(".assign-users__filter--branch").html('<option value="">All branches</option>');
            jQuery.ajax({
                url: '/organisation/' + $('#field-organisation').val() + '/user',
                type: 'GET',
                dataType: 'json',
                complete: function(xhr, textStatus) {
                    //called when complete
                },
                success: function(data, textStatus, xhr) {
                    $('select').select2();
                    var i = 0;
                    user_response = data;
                    var selected_users = $(".user_list").val();
                    for (i = 0; i < data.length; i++) {
                        var selected = '0';
                        if (selected_users.match(new RegExp("(?:^|,)" + data[i].id + "(?:,|$)"))) {
                            selected = '1';
                        }
                        if (data[i].branch == 1) {
                            $('.assign-users__table tbody').append('<tr' + (selected == 1 ? ' class="active"' : '') + '><td><a href="#" onclick="select_users(\'' + data[i].id + '\', this)" class="assign-users__list-item"><span class="assign-users__list-item__icon fa fa-close" aria-hidden="true" title="Unassign user"></span>' + data[i].branch_name + '</a></td><td>' + data[i].parent_branch + '</td></tr>');
                            $('.assign-users__filter--branch').append('<option value="' + data[i].id + '">' + data[i].branch_name + '</option>');
                        } else {
                            $('.assign-users__table tbody').append('<tr' + (selected == 1 ? ' class="active"' : '') + '><td><a href="#" onclick="select_users(\'' + data[i].id + '\', this)" class="assign-users__list-item"><span class="assign-users__list-item__icon fa fa-close" aria-hidden="true" title="Unassign user"></span>' + data[i].last_name + ', ' + data[i].first_name + '</a></td><td>' + data[i].parent_branch + '</td></tr>');
                        }
                    }
                    $(".assign-users__filter--branch").prop("disabled", false);
                    $(".assign-users__list").show();
                    // $('.data-table').dataTable(
                    // {
                    //     responsive: true
                    // });
                    // $.getScript( '/js/learning.js', function() {});
                },
                error: function(xhr, textStatus, errorThrown) {
                    //called when there is an error
                }
            });
        });

        $('#field-branch').change(function() {
            $(".assign-users__list").hide();
            $('.assign-users__table tbody').html('');
            var selected_users = $(".user_list").val();
            for (i = 0; i < user_response.length; i++) {
                var selected = '0';
                if (selected_users.match(new RegExp("(?:^|,)" + user_response[i].id + "(?:,|$)"))) {
                    selected = '1';
                }
                if (user_response[i].branch_id == $('#field-branch').val() || (user_response[i].id == $('#field-branch').val() && user_response[i].branch == 1) || $('#field-branch').val() == '')
                    if (user_response[i].branch == 1) {
                        $('.assign-users__table tbody').append('<tr' + (selected == 1 ? ' class="active"' : '') + '><td><a href="#"onclick="select_users(\'' + user_response[i].id + '\', this)" class="assign-users__list-item"><span class="assign-users__list-item__icon fa fa-close" aria-hidden="true" title="Unassign user"></span>' + user_response[i].branch_name + '</a></td><td>' + user_response[i].parent_branch + '</td></tr>');
                    } else {
                        $('.assign-users__table tbody').append('<tr' + (selected == 1 ? ' class="active"' : '') + '><td><a href="#"onclick="select_users(\'' + user_response[i].id + '\', this)" class="assign-users__list-item"><span class="assign-users__list-item__icon fa fa-close" aria-hidden="true" title="Unassign user"></span>' + user_response[i].last_name + ', ' + user_response[i].first_name + '</a></td><td>' + user_response[i].parent_branch + '</td></tr>');
                    }
            }
            $(".assign-users__list").show();
        });


        // add a form validation/submit handler if found
        if (form) {
            form.validate({
                errorPlacement: function(error, el) {

                },

                submitHandler: function(form) {
                    var self = $(form);

                    $.ajax({
                        url: self.attr('action'),
                        type: self.attr('method'),
                        data: self.serializeArray(),
                        success: function(data, type, xhr) {
                            bootbox.modal('hide');
                            location.reload();
                        },
                        error: function(xhr, type, exception) {
                            self.validate().showErrors(xhr.responseJSON.invalid);
                        }
                    });
                },

                highlight: function(el) {
                    $(el).parents('.form-group').eq(0).addClass('has-error');
                },

                unhighlight: function(el) {
                    $(el).parents('.form-group').eq(0).removeClass('has-error');
                }
            });

            // field filter
            field_filter_init(
                form.find('> .field-filter')
            );
        }
        if ($('.data-table').length > 0) {
            $('.data-table').DataTable({
                searching: true
            });
            $('.dataTables_filter').addClass('hidden');
        }
    }).modal('show');

    function show_error_notify(message) {

        $.notify({
            message: message
        }, {
            delay: 2000,
            placement: {
                from: 'bottom',
                align: 'right'
            },
            type: 'danger'
        })

    }
    $(document).on('change', 'select[name="organisation[]"]', function() {
        // console.log($(this).val());
        if ($(this).val() != null && $(this).val().length == 1) {
            $('input[name="owner"]').prop('disabled', '');
            //console.log('opt1:'+$('input[name="owner"]').length);
        } else {
            $('input[name="owner"]').attr('checked', false);
            $('input[name="owner"]').prop('disabled', 'disabled');
            //console.log('opt2:'+$('input[name="owner"]').length);
        }
    });
};