/*jslint browser: true*/
/*jslint jquery: true*/

/*
 * jQ<PERSON>y Hotkeys Plugin
 * Copyright 2010, <PERSON>
 * Dual licensed under the MIT or GPL Version 2 licenses.
 *
 * Based upon the plugin by <PERSON><PERSON><PERSON>:
 * https://github.com/tzuryby/jquery.hotkeys
 *
 * Original idea by:
 * Binny V A, http://www.openjs.com/scripts/events/keyboard_shortcuts/
 */

/*
 * One small change is: now keys are passed by object { keys: '...' }
 * Might be useful, when you want to pass some other data to your handler
 */

(function(jQuery) {

    jQuery.hotkeys = {
        version: "0.2.0",

        specialKeys: {
            8: "backspace",
            9: "tab",
            10: "return",
            13: "return",
            16: "shift",
            17: "ctrl",
            18: "alt",
            19: "pause",
            20: "capslock",
            27: "esc",
            32: "space",
            33: "pageup",
            34: "pagedown",
            35: "end",
            36: "home",
            37: "left",
            38: "up",
            39: "right",
            40: "down",
            45: "insert",
            46: "del",
            59: ";",
            61: "=",
            96: "0",
            97: "1",
            98: "2",
            99: "3",
            100: "4",
            101: "5",
            102: "6",
            103: "7",
            104: "8",
            105: "9",
            106: "*",
            107: "+",
            109: "-",
            110: ".",
            111: "/",
            112: "f1",
            113: "f2",
            114: "f3",
            115: "f4",
            116: "f5",
            117: "f6",
            118: "f7",
            119: "f8",
            120: "f9",
            121: "f10",
            122: "f11",
            123: "f12",
            144: "numlock",
            145: "scroll",
            173: "-",
            186: ";",
            187: "=",
            188: ",",
            189: "-",
            190: ".",
            191: "/",
            192: "`",
            219: "[",
            220: "\\",
            221: "]",
            222: "'"
        },

        shiftNums: {
            "`": "~",
            "1": "!",
            "2": "@",
            "3": "#",
            "4": "$",
            "5": "%",
            "6": "^",
            "7": "&",
            "8": "*",
            "9": "(",
            "0": ")",
            "-": "_",
            "=": "+",
            ";": ": ",
            "'": "\"",
            ",": "<",
            ".": ">",
            "/": "?",
            "\\": "|"
        },

        // excludes: button, checkbox, file, hidden, image, password, radio, reset, search, submit, url
        textAcceptingInputTypes: [
            "text", "password", "number", "email", "url", "range", "date", "month", "week", "time", "datetime",
            "datetime-local", "search", "color", "tel"],

        // default input types not to bind to unless bound directly
        textInputTypes: /textarea|input|select/i,

        options: {
            filterInputAcceptingElements: true,
            filterTextInputs: true,
            filterContentEditable: true
        }
    };

    function keyHandler(handleObj) {
        if (typeof handleObj.data === "string") {
            handleObj.data = {
                keys: handleObj.data
            };
        }

        // Only care when a possible input has been specified
        if (!handleObj.data || !handleObj.data.keys || typeof handleObj.data.keys !== "string") {
            return;
        }

        var origHandler = handleObj.handler,
            keys = handleObj.data.keys.toLowerCase().split(" ");

        handleObj.handler = function(event) {
            //      Don't fire in text-accepting inputs that we didn't directly bind to
            if (this !== event.target &&
                (jQuery.hotkeys.options.filterInputAcceptingElements &&
                jQuery.hotkeys.textInputTypes.test(event.target.nodeName) ||
                (jQuery.hotkeys.options.filterContentEditable && jQuery(event.target).attr('contenteditable')) ||
                (jQuery.hotkeys.options.filterTextInputs &&
                jQuery.inArray(event.target.type, jQuery.hotkeys.textAcceptingInputTypes) > -1))) {
                return;
            }

            var special = event.type !== "keypress" && jQuery.hotkeys.specialKeys[event.which],
                character = String.fromCharCode(event.which).toLowerCase(),
                modif = "",
                possible = {};

            jQuery.each(["alt", "ctrl", "shift"], function(index, specialKey) {

                if (event[specialKey + 'Key'] && special !== specialKey) {
                    modif += specialKey + '+';
                }
            });

            // metaKey is triggered off ctrlKey erronously
            if (event.metaKey && !event.ctrlKey && special !== "meta") {
                modif += "meta+";
            }

            if (event.metaKey && special !== "meta" && modif.indexOf("alt+ctrl+shift+") > -1) {
                modif = modif.replace("alt+ctrl+shift+", "hyper+");
            }

            if (special) {
                possible[modif + special] = true;
            }
            else {
                possible[modif + character] = true;
                possible[modif + jQuery.hotkeys.shiftNums[character]] = true;

                // "$" can be triggered as "Shift+4" or "Shift+$" or just "$"
                if (modif === "shift+") {
                    possible[jQuery.hotkeys.shiftNums[character]] = true;
                }
            }

            for (var i = 0, l = keys.length; i < l; i++) {
                if (possible[keys[i]]) {
                    return origHandler.apply(this, arguments);
                }
            }
        };
    }

    jQuery.each(["keydown", "keyup", "keypress"], function() {
        jQuery.event.special[this] = {
            add: keyHandler
        };
    });

})(jQuery || this.jQuery || window.jQuery);



!function(a,b){"use strict";function c(c,d){this.selectedRange=null,this.editor=b(c);var e=b(c),f={hotKeys:{"Ctrl+b meta+b":"bold","Ctrl+i meta+i":"italic","Ctrl+u meta+u":"underline","Ctrl+z":"undo","Ctrl+y meta+y meta+shift+z":"redo","Ctrl+l meta+l":"justifyleft","Ctrl+r meta+r":"justifyright","Ctrl+e meta+e":"justifycenter","Ctrl+j meta+j":"justifyfull","Shift+tab":"outdent",tab:"indent"},toolbarSelector:"[data-role=editor-toolbar]",commandRole:"edit",activeToolbarClass:"btn-info",selectionMarker:"edit-focus-marker",selectionColor:"darkgrey",dragAndDropImages:!0,keypressTimeout:200,fileUploadError:function(a,b){console.log("File upload error",a,b)}},g=b.extend(!0,{},f,d),h="a[data-"+g.commandRole+"],button[data-"+g.commandRole+"],input[type=button][data-"+g.commandRole+"]";this.bindHotkeys(e,g,h),g.dragAndDropImages&&this.initFileDrops(e,g,h),this.bindToolbar(e,b(g.toolbarSelector),g,h),e.attr("contenteditable",!0).on("mouseup keyup mouseout",function(){this.saveSelection(),this.updateToolbar(e,h,g)}.bind(this)),b(a).bind("touchend",function(a){var b=e.is(a.target)||e.has(a.target).length>0,c=this.getCurrentRange(),d=c&&c.startContainer===c.endContainer&&c.startOffset===c.endOffset;(!d||b)&&(this.saveSelection(),this.updateToolbar(e,h,g))})}c.prototype.readFileIntoDataUrl=function(a){var c=b.Deferred(),d=new FileReader;return d.onload=function(a){c.resolve(a.target.result)},d.onerror=c.reject,d.onprogress=c.notify,d.readAsDataURL(a),c.promise()},c.prototype.cleanHtml=function(a){var c=this;if(b(c).data("wysiwyg-html-mode")===!0&&(b(c).html(b(c).text()),b(c).attr("contenteditable",!0),b(c).data("wysiwyg-html-mode",!1)),a===!0&&b(c).parent().is("form")){var d=b(c).html;if(b(d).has("img").length){var e=b("img",b(d)),f=[],g=b(c).parent();b.each(e,function(a,c){b(c).attr("src").match(/^data:image\/.*$/)&&(f.push(e[a]),b(g).prepend("<input value='"+b(c).attr("src")+"' type='hidden' name='postedimage/"+a+"' />"),b(c).attr("src","postedimage/"+a))})}}var h=b(c).html();return h&&h.replace(/(<br>|\s|<div><br><\/div>|&nbsp;)*$/,"")},c.prototype.updateToolbar=function(a,c,d){d.activeToolbarClass&&b(d.toolbarSelector).find(c).each(function(){var a=b(this),c=a.data(d.commandRole).split(" "),e=c[0];c.length>1&&document.queryCommandEnabled(e)&&document.queryCommandValue(e)===c[1]?a.addClass(d.activeToolbarClass):1===c.length&&document.queryCommandEnabled(e)&&document.queryCommandState(e)?a.addClass(d.activeToolbarClass):a.removeClass(d.activeToolbarClass)})},c.prototype.execCommand=function(a,b,c,d,e){var f=a.split(" "),g=f.shift(),h=f.join(" ")+(b||""),i=a.split("-");1===i.length?document.execCommand(g,!1,h):"format"===i[0]&&2===i.length&&document.execCommand("formatBlock",!1,i[1]),c.trigger("change"),this.updateToolbar(c,e,d)},c.prototype.bindHotkeys=function(a,c,d){var e=this;b.each(c.hotKeys,function(f,g){b(a).keydown(f,function(f){a.attr("contenteditable")&&b(a).is(":visible")&&(f.preventDefault(),f.stopPropagation(),e.execCommand(g,null,a,c,d))}).keyup(f,function(c){a.attr("contenteditable")&&b(a).is(":visible")&&(c.preventDefault(),c.stopPropagation())})}),a.keyup(function(){a.trigger("change")})},c.prototype.getCurrentRange=function(){var b,c;return a.getSelection?(b=a.getSelection(),b.getRangeAt&&b.rangeCount&&(c=b.getRangeAt(0))):document.selection&&(c=document.selection.createRange()),c},c.prototype.saveSelection=function(){this.selectedRange=this.getCurrentRange()},c.prototype.restoreSelection=function(){var b;if(a.getSelection||document.createRange){if(b=a.getSelection(),this.selectedRange){try{b.removeAllRanges()}catch(c){document.body.createTextRange().select(),document.selection.empty()}b.addRange(this.selectedRange)}}else document.selection&&this.selectedRange&&this.selectedRange.select()},c.prototype.toggleHtmlEdit=function(a){if(a.data("wysiwyg-html-mode")!==!0){var c=a.html(),d=b("<pre />");b(d).append(document.createTextNode(c)),b(d).attr("contenteditable",!0),b(a).html(" "),b(a).append(b(d)),b(a).attr("contenteditable",!1),b(a).data("wysiwyg-html-mode",!0),b(d).focus()}else b(a).html(b(a).text()),b(a).attr("contenteditable",!0),b(a).data("wysiwyg-html-mode",!1),b(a).focus()},c.prototype.insertFiles=function(a,c,d,e){var f=this;d.focus(),b.each(a,function(a,g){/^image\//.test(g.type)?b.when(f.readFileIntoDataUrl(g)).done(function(a){f.execCommand("insertimage",a,d,c,e),d.trigger("image-inserted")}).fail(function(a){c.fileUploadError("file-reader",a)}):c.fileUploadError("unsupported-file-type",g.type)})},c.prototype.markSelection=function(a,b,c){this.restoreSelection(),document.queryCommandSupported("hiliteColor")&&document.execCommand("hiliteColor",!1,b||"transparent"),this.saveSelection(),a.data(c.selectionMarker,b)},c.prototype.bindToolbar=function(a,c,d,e){var f=this;c.find(e).click(function(){f.restoreSelection(),a.focus(),"html"===a.data(d.commandRole)?f.toggleHtmlEdit(a):f.execCommand(b(this).data(d.commandRole),null,a,d,e),f.saveSelection()}),c.find("[data-toggle=dropdown]").click(this.restoreSelection()),c.find("input[type=text][data-"+d.commandRole+"]").on("webkitspeechchange change",function(){var c=this.value;this.value="",f.restoreSelection(),c&&(a.focus(),f.execCommand(b(this).data(d.commandRole),c,a,d,e)),f.saveSelection()}).on("focus",function(){var a=b(this);a.data(d.selectionMarker)||(f.markSelection(a,d.selectionColor,d),a.focus())}).on("blur",function(){var a=b(this);a.data(d.selectionMarker)&&f.markSelection(a,!1,d)}),c.find("input[type=file][data-"+d.commandRole+"]").change(function(){f.restoreSelection(),"file"===this.type&&this.files&&this.files.length>0&&f.insertFiles(this.files,d,a,e),f.saveSelection(),this.value=""})},c.prototype.initFileDrops=function(a,b,c){var d=this;a.on("dragenter dragover",!1).on("drop",function(e){var f=e.originalEvent.dataTransfer;e.stopPropagation(),e.preventDefault(),f&&f.files&&f.files.length>0&&d.insertFiles(f.files,b,a,c)})},b.fn.wysiwyg=function(a){new c(this,a)}}(window,window.jQuery);