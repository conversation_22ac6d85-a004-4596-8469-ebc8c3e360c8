var quillMention=function(t){"use strict";function e(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var n=e(t);function i(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function o(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function s(t,e,n){return e&&o(t.prototype,e),n&&o(t,n),t}function r(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function a(){return a=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t},a.apply(this,arguments)}function l(t){return l=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},l(t)}function h(t,e){return h=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},h(t,e)}function u(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function c(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return u(t)}function d(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,i=l(t);if(e){var o=l(this).constructor;n=Reflect.construct(i,arguments,o)}else n=i.apply(this,arguments);return c(this,n)}}function f(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=l(t)););return t}function m(){return m="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){var i=f(t,e);if(i){var o=Object.getOwnPropertyDescriptor(i,e);return o.get?o.get.call(arguments.length<3?t:n):o.value}},m.apply(this,arguments)}function p(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}function v(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,e){if(t){if("string"==typeof t)return p(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?p(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var i=0,o=function(){};return{s:o,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,r=!0,a=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return r=t.done,t},e:function(t){a=!0,s=t},f:function(){try{r||null==n.return||n.return()}finally{if(a)throw s}}}}var g=9,C=13,y=27,b=38,x=40;function k(t,e,n){var i=t;return Object.keys(e).forEach((function(t){n.indexOf(t)>-1?i.dataset[t]=e[t]:delete i.dataset[t]})),i}var I=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&h(t,e)}(n,t);var e=d(n);function n(t,o){var s;return i(this,n),r(u(s=e.call(this,t,o)),"hoverHandler",void 0),r(u(s),"hoverHandler",void 0),s.clickHandler=null,s.hoverHandler=null,s.mounted=!1,s}return s(n,[{key:"attach",value:function(){m(l(n.prototype),"attach",this).call(this),this.mounted||(this.mounted=!0,this.clickHandler=this.getClickHandler(),this.hoverHandler=this.getHoverHandler(),this.domNode.addEventListener("click",this.clickHandler,!1),this.domNode.addEventListener("mouseenter",this.hoverHandler,!1))}},{key:"detach",value:function(){m(l(n.prototype),"detach",this).call(this),this.mounted=!1,this.clickHandler&&(this.domNode.removeEventListener("click",this.clickHandler),this.clickHandler=null)}},{key:"getClickHandler",value:function(){var t=this;return function(e){var n=t.buildEvent("mention-clicked",e);window.dispatchEvent(n),e.preventDefault()}}},{key:"getHoverHandler",value:function(){var t=this;return function(e){var n=t.buildEvent("mention-hovered",e);window.dispatchEvent(n),e.preventDefault()}}},{key:"buildEvent",value:function(t,e){var n=new Event(t,{bubbles:!0,cancelable:!0});return n.value=a({},this.domNode.dataset),n.event=e,n}}],[{key:"create",value:function(t){var e=m(l(n),"create",this).call(this),i=document.createElement("span");return i.className="ql-mention-denotation-char",i.innerHTML=t.denotationChar,e.appendChild(i),e.innerHTML+=t.value,n.setDataValues(e,t)}},{key:"setDataValues",value:function(t,e){var n=t;return Object.keys(e).forEach((function(t){n.dataset[t]=e[t]})),n}},{key:"value",value:function(t){return t.dataset}}]),n}(n.default.import("blots/embed"));I.blotName="mention",I.tagName="span",I.className="mention",n.default.register(I);var L=function(){function t(e,n){var o=this;i(this,t),this.isOpen=!1,this.itemIndex=0,this.mentionCharPos=null,this.cursorPos=null,this.values=[],this.suspendMouseEnter=!1,this.existingSourceExecutionToken=null,this.quill=e,this.options={source:null,renderItem:function(t){return"".concat(t.value)},renderLoading:function(){return null},onSelect:function(t,e){e(t)},mentionDenotationChars:["@"],showDenotationChar:!0,allowedChars:/^[a-zA-Z0-9_]*$/,minChars:0,maxChars:31,offsetTop:2,offsetLeft:0,isolateCharacter:!1,fixMentionsToQuill:!1,positioningStrategy:"normal",defaultMenuOrientation:"bottom",blotName:"mention",dataAttributes:["id","value","denotationChar","link","target","disabled"],linkTarget:"_blank",onOpen:function(){return!0},onBeforeClose:function(){return!0},onClose:function(){return!0},listItemClass:"ql-mention-list-item",mentionContainerClass:"ql-mention-list-container",mentionListClass:"ql-mention-list",spaceAfterInsert:!0,selectKeys:[C]},a(this.options,n,{dataAttributes:Array.isArray(n.dataAttributes)?this.options.dataAttributes.concat(n.dataAttributes):this.options.dataAttributes}),this.mentionContainer=document.createElement("div"),this.mentionContainer.className=this.options.mentionContainerClass?this.options.mentionContainerClass:"",this.mentionContainer.style.cssText="display: none; position: absolute;",this.mentionContainer.onmousemove=this.onContainerMouseMove.bind(this),this.options.fixMentionsToQuill&&(this.mentionContainer.style.width="auto"),this.mentionList=document.createElement("ul"),this.mentionList.id="quill-mention-list",e.root.setAttribute("aria-owns","quill-mention-list"),this.mentionList.className=this.options.mentionListClass?this.options.mentionListClass:"",this.mentionContainer.appendChild(this.mentionList),e.on("text-change",this.onTextChange.bind(this)),e.on("selection-change",this.onSelectionChange.bind(this)),e.container.addEventListener("paste",(function(){setTimeout((function(){var t=e.getSelection();o.onSelectionChange(t)}))})),e.keyboard.addBinding({key:g},this.selectHandler.bind(this)),e.keyboard.bindings[g].unshift(e.keyboard.bindings[g].pop());var s,r=v(this.options.selectKeys);try{for(r.s();!(s=r.n()).done;){var l=s.value;e.keyboard.addBinding({key:l},this.selectHandler.bind(this))}}catch(t){r.e(t)}finally{r.f()}e.keyboard.bindings[C].unshift(e.keyboard.bindings[C].pop()),e.keyboard.addBinding({key:y},this.escapeHandler.bind(this)),e.keyboard.addBinding({key:b},this.upHandler.bind(this)),e.keyboard.addBinding({key:x},this.downHandler.bind(this))}return s(t,[{key:"selectHandler",value:function(){return!(this.isOpen&&!this.existingSourceExecutionToken)||(this.selectItem(),!1)}},{key:"escapeHandler",value:function(){return!this.isOpen||(this.existingSourceExecutionToken&&(this.existingSourceExecutionToken.abandoned=!0),this.hideMentionList(),!1)}},{key:"upHandler",value:function(){return!(this.isOpen&&!this.existingSourceExecutionToken)||(this.prevItem(),!1)}},{key:"downHandler",value:function(){return!(this.isOpen&&!this.existingSourceExecutionToken)||(this.nextItem(),!1)}},{key:"showMentionList",value:function(){"fixed"===this.options.positioningStrategy?document.body.appendChild(this.mentionContainer):this.quill.container.appendChild(this.mentionContainer),this.mentionContainer.style.visibility="hidden",this.mentionContainer.style.display="",this.mentionContainer.scrollTop=0,this.setMentionContainerPosition(),this.setIsOpen(!0)}},{key:"hideMentionList",value:function(){this.options.onBeforeClose(),this.mentionContainer.style.display="none",this.mentionContainer.remove(),this.setIsOpen(!1),this.quill.root.removeAttribute("aria-activedescendant")}},{key:"highlightItem",value:function(){for(var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],e=0;e<this.mentionList.childNodes.length;e+=1)this.mentionList.childNodes[e].classList.remove("selected");if(-1!==this.itemIndex&&"true"!==this.mentionList.childNodes[this.itemIndex].dataset.disabled&&(this.mentionList.childNodes[this.itemIndex].classList.add("selected"),this.quill.root.setAttribute("aria-activedescendant",this.mentionList.childNodes[this.itemIndex].id),t)){var n=this.mentionList.childNodes[this.itemIndex].offsetHeight,i=this.mentionList.childNodes[this.itemIndex].offsetTop,o=this.mentionContainer.scrollTop,s=o+this.mentionContainer.offsetHeight;i<o?this.mentionContainer.scrollTop=i:i>s-n&&(this.mentionContainer.scrollTop+=i-s+n)}}},{key:"getItemData",value:function(){var t=this.mentionList.childNodes[this.itemIndex].dataset.link,e=void 0!==t,n=this.mentionList.childNodes[this.itemIndex].dataset.target;return e&&(this.mentionList.childNodes[this.itemIndex].dataset.value='<a href="'.concat(t,'" target=').concat(n||this.options.linkTarget,">").concat(this.mentionList.childNodes[this.itemIndex].dataset.value)),this.mentionList.childNodes[this.itemIndex].dataset}},{key:"onContainerMouseMove",value:function(){this.suspendMouseEnter=!1}},{key:"selectItem",value:function(){var t=this;if(-1!==this.itemIndex){var e=this.getItemData();e.disabled||(this.options.onSelect(e,(function(e){t.insertItem(e)})),this.hideMentionList())}}},{key:"insertItem",value:function(t,e){var i,o=t;null!==o&&(this.options.showDenotationChar||(o.denotationChar=""),e?i=this.cursorPos:(i=this.mentionCharPos,this.quill.deleteText(this.mentionCharPos,this.cursorPos-this.mentionCharPos,n.default.sources.USER)),this.quill.insertEmbed(i,this.options.blotName,o,n.default.sources.USER),this.options.spaceAfterInsert?(this.quill.insertText(i+1," ",n.default.sources.USER),this.quill.setSelection(i+2,n.default.sources.USER)):this.quill.setSelection(i+1,n.default.sources.USER),this.hideMentionList())}},{key:"onItemMouseEnter",value:function(t){if(!this.suspendMouseEnter){var e=Number(t.target.dataset.index);Number.isNaN(e)||e===this.itemIndex||(this.itemIndex=e,this.highlightItem(!1))}}},{key:"onDisabledItemMouseEnter",value:function(t){this.suspendMouseEnter||(this.itemIndex=-1,this.highlightItem(!1))}},{key:"onItemClick",value:function(t){0===t.button&&(t.preventDefault(),t.stopImmediatePropagation(),this.itemIndex=t.currentTarget.dataset.index,this.highlightItem(),this.selectItem())}},{key:"onItemMouseDown",value:function(t){t.preventDefault(),t.stopImmediatePropagation()}},{key:"renderLoading",value:function(){if(this.options.renderLoading())if(this.mentionContainer.getElementsByClassName("ql-mention-loading").length>0)this.showMentionList();else{this.mentionList.innerHTML="";var t=document.createElement("div");t.className="ql-mention-loading",t.innerHTML=this.options.renderLoading(),this.mentionContainer.append(t),this.showMentionList()}}},{key:"removeLoading",value:function(){var t=this.mentionContainer.getElementsByClassName("ql-mention-loading");t.length>0&&t[0].remove()}},{key:"renderList",value:function(t,e,n){if(e&&e.length>0){this.removeLoading(),this.values=e,this.mentionList.innerHTML="";for(var i=-1,o=0;o<e.length;o+=1){var s=document.createElement("li");s.id="quill-mention-item-"+o,s.className=this.options.listItemClass?this.options.listItemClass:"",e[o].disabled?(s.className+=" disabled",s.setAttribute("aria-hidden","true")):-1===i&&(i=o),s.dataset.index=o,s.innerHTML=this.options.renderItem(e[o],n),e[o].disabled?s.onmouseenter=this.onDisabledItemMouseEnter.bind(this):(s.onmouseenter=this.onItemMouseEnter.bind(this),s.onmouseup=this.onItemClick.bind(this),s.onmousedown=this.onItemMouseDown.bind(this)),s.dataset.denotationChar=t,this.mentionList.appendChild(k(s,e[o],this.options.dataAttributes))}this.itemIndex=i,this.highlightItem(),this.showMentionList()}else this.hideMentionList()}},{key:"nextItem",value:function(){var t,e=0;do{e++,t=(this.itemIndex+e)%this.values.length;var n="true"===this.mentionList.childNodes[t].dataset.disabled;if(e===this.values.length+1){t=-1;break}}while(n);this.itemIndex=t,this.suspendMouseEnter=!0,this.highlightItem()}},{key:"prevItem",value:function(){var t,e=0;do{e++,t=(this.itemIndex+this.values.length-e)%this.values.length;var n="true"===this.mentionList.childNodes[t].dataset.disabled;if(e===this.values.length+1){t=-1;break}}while(n);this.itemIndex=t,this.suspendMouseEnter=!0,this.highlightItem()}},{key:"containerBottomIsNotVisible",value:function(t,e){return t+this.mentionContainer.offsetHeight+e.top>window.pageYOffset+window.innerHeight}},{key:"containerRightIsNotVisible",value:function(t,e){return!this.options.fixMentionsToQuill&&t+this.mentionContainer.offsetWidth+e.left>window.pageXOffset+document.documentElement.clientWidth}},{key:"setIsOpen",value:function(t){this.isOpen!==t&&(t?this.options.onOpen():this.options.onClose(),this.isOpen=t)}},{key:"setMentionContainerPosition",value:function(){"fixed"===this.options.positioningStrategy?this.setMentionContainerPosition_Fixed():this.setMentionContainerPosition_Normal()}},{key:"setMentionContainerPosition_Normal",value:function(){var t=this,e=this.quill.container.getBoundingClientRect(),n=this.quill.getBounds(this.mentionCharPos),i=this.mentionContainer.offsetHeight,o=this.options.offsetTop,s=this.options.offsetLeft;if(this.options.fixMentionsToQuill){this.mentionContainer.style.right="".concat(0,"px")}else s+=n.left;if(this.containerRightIsNotVisible(s,e)){var r=this.mentionContainer.offsetWidth+this.options.offsetLeft;s=e.width-r}if("top"===this.options.defaultMenuOrientation){if((o=this.options.fixMentionsToQuill?-1*(i+this.options.offsetTop):n.top-(i+this.options.offsetTop))+e.top<=0){var a=this.options.offsetTop;this.options.fixMentionsToQuill?a+=e.height:a+=n.bottom,o=a}}else if(this.options.fixMentionsToQuill?o+=e.height:o+=n.bottom,this.containerBottomIsNotVisible(o,e)){var l=-1*this.options.offsetTop;this.options.fixMentionsToQuill||(l+=n.top),o=l-i}o>=0?this.options.mentionContainerClass.split(" ").forEach((function(e){t.mentionContainer.classList.add("".concat(e,"-bottom")),t.mentionContainer.classList.remove("".concat(e,"-top"))})):this.options.mentionContainerClass.split(" ").forEach((function(e){t.mentionContainer.classList.add("".concat(e,"-top")),t.mentionContainer.classList.remove("".concat(e,"-bottom"))})),this.mentionContainer.style.top="".concat(o,"px"),this.mentionContainer.style.left="".concat(s,"px"),this.mentionContainer.style.visibility="visible"}},{key:"setMentionContainerPosition_Fixed",value:function(){var t=this;this.mentionContainer.style.position="fixed",this.mentionContainer.style.height=null;var e=this.quill.container.getBoundingClientRect(),n=this.quill.getBounds(this.mentionCharPos),i={left:e.left+n.left,top:e.top+n.top,width:0,height:n.height},o=this.options.fixMentionsToQuill?e:i,s=this.options.offsetTop,r=this.options.offsetLeft;if(this.options.fixMentionsToQuill){var a=o.right;this.mentionContainer.style.right="".concat(a,"px")}else(r+=o.left)+this.mentionContainer.offsetWidth>document.documentElement.clientWidth&&(r-=r+this.mentionContainer.offsetWidth-document.documentElement.clientWidth);var l=o.top,h=document.documentElement.clientHeight-(o.top+o.height),u=this.mentionContainer.offsetHeight<=h,c=this.mentionContainer.offsetHeight<=l;"bottom"===("top"===this.options.defaultMenuOrientation&&c?"top":"bottom"===this.options.defaultMenuOrientation&&u||h>l?"bottom":"top")?(s=o.top+o.height,u||(this.mentionContainer.style.height=h-3+"px"),this.options.mentionContainerClass.split(" ").forEach((function(e){t.mentionContainer.classList.add("".concat(e,"-bottom")),t.mentionContainer.classList.remove("".concat(e,"-top"))}))):(s=o.top-this.mentionContainer.offsetHeight,c||(this.mentionContainer.style.height=l-3+"px",s=3),this.options.mentionContainerClass.split(" ").forEach((function(e){t.mentionContainer.classList.add("".concat(e,"-top")),t.mentionContainer.classList.remove("".concat(e,"-bottom"))}))),this.mentionContainer.style.top="".concat(s,"px"),this.mentionContainer.style.left="".concat(r,"px"),this.mentionContainer.style.visibility="visible"}},{key:"getTextBeforeCursor",value:function(){var t=Math.max(0,this.cursorPos-this.options.maxChars);return this.quill.getText(t,this.cursorPos-t)}},{key:"onSomethingChange",value:function(){var t=this,e=this.quill.getSelection();if(null!=e){this.cursorPos=e.index;var n,i=this.getTextBeforeCursor(),o=(n=i,this.options.mentionDenotationChars.reduce((function(t,e){var i=n.lastIndexOf(e);return i>t.mentionCharIndex?{mentionChar:e,mentionCharIndex:i}:{mentionChar:t.mentionChar,mentionCharIndex:t.mentionCharIndex}}),{mentionChar:null,mentionCharIndex:-1})),s=o.mentionChar,r=o.mentionCharIndex;if(function(t,e,n){return t>-1&&!(n&&0!==t&&!e[t-1].match(/\s/g))}(r,i,this.options.isolateCharacter)){var a=this.cursorPos-(i.length-r);this.mentionCharPos=a;var l=i.substring(r+s.length);if(l.length>=this.options.minChars&&function(t,e){return e.test(t)}(l,this.getAllowedCharsRegex(s))){this.existingSourceExecutionToken&&(this.existingSourceExecutionToken.abandoned=!0),this.renderLoading();var h={abandoned:!1};this.existingSourceExecutionToken=h,this.options.source(l,(function(e,n){h.abandoned||(t.existingSourceExecutionToken=null,t.renderList(s,e,n))}),s)}else this.existingSourceExecutionToken&&(this.existingSourceExecutionToken.abandoned=!0),this.hideMentionList()}else this.existingSourceExecutionToken&&(this.existingSourceExecutionToken.abandoned=!0),this.hideMentionList()}}},{key:"getAllowedCharsRegex",value:function(t){return this.options.allowedChars instanceof RegExp?this.options.allowedChars:this.options.allowedChars(t)}},{key:"onTextChange",value:function(t,e,n){"user"===n&&this.onSomethingChange()}},{key:"onSelectionChange",value:function(t){t&&0===t.length?this.onSomethingChange():this.hideMentionList()}},{key:"openMenu",value:function(t){var e=this.quill.getSelection(!0);this.quill.insertText(e.index,t),this.quill.blur(),this.quill.focus()}}]),t}();return n.default.register("modules/mention",L),L}(Quill);
