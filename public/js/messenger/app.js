(function () {

    /*globals moment*/


    var cache = {
        $messengerHTML: $('.js--modal-messenger'),
        $responseTemplate: $('.js--modal-messenger .js--messenger-response-template')
    };

    let pluploadInited = false;
    let pluploadFiles;
    let attachments = [];
    let attachmentLinks = [];

    let newMessages = {};
    let msgThreadRef;
    let msgCount = 0;

    /**
     * Initialise the module
     * @function init
     */

    var init = function (rr_ref, issuename, open_button, size = 'large') {
        /*globals bootbox*/
        // alert(rr_ref);
        if (rr_ref === undefined) {
            rr_ref = "0";
        }

        if (issuename === undefined) {
            issuename = "0";
        }

        msgThreadRef = rr_ref;
        var modal = bootbox.dialog({
            title: false,
            className: 'modal--messenger',
            message: cache.$messengerHTML.html(),
            size,
            show: false
        });

        if (rr_ref != "0") {
            $(".message-list__item").hide();
            $("." + rr_ref).show();
        }

        modal.on('hidden.bs.modal', messengerClose);

        modal.on('shown.bs.modal', messengerOpen);
        modal.modal('show');

        $("input.rrec_status").change(function () {
            var post_data = {};
            post_data['survey_id'] = survey_id;
            post_data['organisation_id'] = organisation_id;
            if ($(this).is(":checked")) {
                post_data[issuename] = "0";
                submitCloseStatus(post_data, open_button);
            } else {
                confirmMessageClose(survey_id, $(this), function (d) {
                    post_data[issuename] = d;

                    submitCloseStatus(post_data, open_button);
                });
            }
        });

    }

    const appendMissingMessages = function () {
        const $messenger = $('.modal--messenger.show');
        const $messageList = $messenger.find('.message-list');

        newMessages[msgThreadRef].forEach(function (item) {
            $messageList.append(item);
            $messageList.slimscroll({ scrollBy: '400px' });
            messengerPopovers(item);
        });
    }

    var submitCloseStatus = function (status_data, open_button) {
        jQuery.ajax({
            url: '/risk-improvement/form/submission/' + submission_id + '/close',
            type: 'POST',
            dataType: 'json',
            data: status_data,
            complete: function (xhr, textStatus) {
                open_button.attr('data-issueclosed', status_data);
            },
            success: function (data, textStatus, xhr) {
                open_button.attr('data-issueclosed', status_data);
                location.reload();
            },
            error: function (xhr, textStatus, errorThrown) {
                open_button.attr('data-issueclosed', status_data);
            }
        });
    }

    var confirmMessageClose = function (survey_id, statusObj, callBack) {
        bootbox.prompt("Close Thread", function (result) {
            if (result != null) {
                var formData = [];

                $form = $('form.js--messenger__input-form').serializeArray();
                console.log($form);

                formData.push({ name: "survey_id", value: survey_id.toString() });
                formData.push({ name: "message", value: result });
                formData.push({ name: "rr_ref", value: $("input[name='rr_ref']").val() });
                formData.push({ name: "attachment", value: '<div class="message-list__item-files"></div>' });

                if (result === "") {
                    $('input.bootbox-input.bootbox-input-text.form-control').css("border-color", "red");
                    return false;
                }

                $('input.bootbox-input.bootbox-input-text.form-control').css("border-color", "");

                var notify_data = $('.modal-body .colleagues_notify');


                notify_data.each(function (i, o) {
                    if ($(this).is(":checked")) {
                        formData.push({ name: $(o).attr("name"), value: $(o).val() });
                    }
                });

                jQuery.ajax({
                    url: '/surveys/messaging/send',
                    type: 'POST',
                    dataType: 'json',
                    data: formData,
                    complete: function (xhr, textStatus) {
                        //called when complete
                    },
                    success: function (data, textStatus, xhr) {

                    },
                    error: function (xhr, textStatus, errorThrown) {
                        //console.log(textStatus);
                    }
                });

                var close = "1";

                callBack(close);
            }
            else {
                statusObj.trigger('click');
            }
        });
    }

    /**
     * Set up all JS for elements within modal (as dynamically created)
     * @function messengerOpen
     * @param {event} e
     */

    var messengerOpen = function (e) {
        var $modal = $(e.currentTarget);


        // textarea autogrow
        $modal.find('textarea').autoGrow();
        $formClose = $("#re_close");
        messengerScroll($modal);
        messengerEvents($modal);
        messengerPopovers($modal);

        const url = window.location.href;
        if (Object.keys(newMessages).includes(msgThreadRef)) {
            appendMissingMessages();
        }

    };

    var messengerClose = function (e) {
        pluploadInited = false;
        msgCount = 0;
        msgThreadRef = null;

        // Check if there's a temp attachment
        if (attachments) {
            jQuery.ajax({
                url: '/surveys/messaging/temp/deleteAttachment',
                type: 'POST',
                data: {attachments},
                dataType: 'json',
                success: function(response) {
                    console.log('Response: ' + response.message);
                    attachments = []; // reset attachments
                },
            });
        }
    };

    /**
     * Sets up Messenger scroll
     * @param $modal
     */
    var messengerScroll = function ($modal) {

        var messenger_list = $modal.find('.message-list').slimScroll({
            height: '450px',
            start: 'bottom'
        });

    };



    /**
     * Add messenger events (form submit)
     * @function messengerEvents
     * @param {jQuery} $modal modal element
     */

    var messengerEvents = function ($modal) {
        var $form = $modal.find('.js--messenger__input-form'),
            $attachBtn = $modal.find('.js--messenger__attach');

        $form.on('submit', messengerSubmit);
        $attachBtn.on('click', messengerPluploadInit.bind(null, $modal));

    };

    /**
     * Add popover / tooltips for notification and visibility icons
     * @function messengerPopovers
     * @param {jQuery} $container element to find popovers within
     */

    var messengerPopovers = function ($container) {
        $container.find('[data-toggle="popover"]').popover({
            content: function () {

                var $this = $(this),
                    content = $this.data('popover-content'),
                    notificationNames = $this.data('notification-names'),
                    notificationNamesHTML;

                if (notificationNames) {

                    notificationNames = notificationNames.split(',');
                    content += '<ul class="popover-list text-white">';

                    for (var i = 0; i < notificationNames.length; i++) {
                        content += '<li>' + notificationNames[i] + '</li>';
                    }

                    content += '</ul>';
                }

                return content;
            },
            html: true
        });
    };

    /**
     * On submitting messenger form, add new message to message body
     * @function messengerSubmit
     * @param {event} e
     */

    var messengerSubmit = function (e) {
        e.preventDefault();
        var $form = $(e.currentTarget),
            $input = $form.find('.messenger__input-field'),
            $messenger = $form.closest('.modal--messenger'),
            $messageList = $messenger.find('.message-list'),
            $messengerBodyContext = $messenger.find('.messenger__body-context'),
            message = $input.val().replace(/\n/g, '<br/>'), // preserve line breaks
            $newMessage = messageCreate(message),
            $plupload = $messenger.find('.js--messenger__plupload'),
            $button = $form.find('button[type="submit"]'),
            scrollTop = 0;

        $button.addClass('disabled');
        $button.find('i.fa').removeClass('hidden');

        // no message - do nothing
        if (!message || message === '') {
            $button.removeClass('disabled');
            $button.find('i.fa').addClass('hidden');
            return;
        }

        var form_data = $($form).serializeArray();

        var attachment_text = "";
        if (attachments) {
            attachment_text = '<div class="message-list__item-files">';
            for (i = 0; i < attachments.length; i++) {

                attachment_text += '<div class="message-list__item-file"><i class="icon icon-paperclip"></i> <a href="' + attachmentLinks[i] + '">' + attachments[i] + '</a></div>';

            }
            attachment_text += '</div>';
        }
        
        form_data.push({ name: 'attachments', value: attachment_text });
        
        if (window.location.href.includes('microsite')) {
            form_data.push({ name: 'uploaded', value: JSON.stringify(attachments) });
        }

        // $($form+" :input").each(function(){
        //     var input = $(this);
        // });

        jQuery.ajax({
            url: '/surveys/messaging/send',
            type: 'POST',
            dataType: 'json',
            data: form_data,
            complete: function (xhr, textStatus) {
                //called when complete
                const url = window.location.href;
                if (!newMessages[msgThreadRef]) {
                    newMessages[msgThreadRef] = [];
                }
                newMessages[msgThreadRef].push($newMessage);

                msgCount++;
                $(`.show_message_thread[data-refclass="${msgThreadRef}"]`).attr('data-msg-count', msgCount);
                $(`.show_message_thread[data-refclass="${msgThreadRef}"] span.message-count`).text(msgCount);
            },
            success: function (data, textStatus, xhr) {
                if (data.response == 'success') {
                    // reset input field
                    $input
                        .val('')
                        .focus()
                        .css('height', '');

                    // Close plupload
                    $plupload.addClass('hidden');

                    // add new message to message list
                    $messageList.append($newMessage);
                    // initialise message JS
                    messengerPopovers($newMessage);
                    $("." + $("input[name='rr_ref']").val()).show();

                    $messageList.slimscroll({ scrollBy: '400px' });
                    attachments = []; // reset attachments
                    attachmentLinks = []; // reset attachmentLinks
                    // Reset files in uploader
                    if (pluploadInited) {
                        $plupload.plupload('getUploader').splice();
                    }
                }
                $button.removeClass('disabled');
                $button.find('i.fa').addClass('hidden');
            },
            error: function (xhr, textStatus, errorThrown) {
                //called when there is an error
                $button.removeClass('disabled');
                $button.find('i.fa').addClass('hidden');

                console.log(textStatus);
            }
        });



    };


    /**
     * Resize messenger's body height so messenger fills screen
     * with input form visible in viewport
     * @function messageCreate
     * @param {string} message message content
     * @returns {jQuery} $newMessage
     */

    var messageCreate = function (message) {
        var $newMessage = cache.$responseTemplate.clone(),
            //$files = $newMessage.find('.message-list__item-files'),
            //$fileName = $newMessage.find('.message-list__item-file'),
            $message = $newMessage.find('.message-list__item-message'),
            $timeDate = $newMessage.find('.message-list__item-time-date'),
            timeDate;

        // format date using moment.js
        if (typeof moment !== 'undefined') {
            timeDate = moment().calendar();
        }

        var attachment_text = "";
        if (attachments) {
            attachment_text = '<div class="message-list__item-files">';
            for (i = 0; i < attachments.length; i++) {

                attachment_text += '<div class="message-list__item-file"><i class="icon icon-paperclip"></i> <a href="' + attachmentLinks[i] + '">' + attachments[i] + '</a></div>';

            }
            attachment_text += '</div>';
        }
        // Set content of cloned element
        $message.html(message + attachment_text);
        $timeDate.text(timeDate);


        // if (pluploadFiles) {
        //     for (var i in pluploadFiles) {
        //         var $newFileName = $fileName.clone();
        //         $newFileName
        //             .removeClass('hidden')
        //             .find('a').text(pluploadFiles[i].name);

        //         $files.append($newFileName);

        //     }
        // }

        $newMessage.removeClass('hidden');
        $newMessage.addClass($("input[name='rr_ref']").val());


        return $newMessage;
    };

    /**
     * Init plupload html5 file uploader
     * @function messengerPluploadInit
     * @param {jQuery} $modal modal element
     */

    var messengerPluploadInit = function ($modal) {

        var $plupload = $modal.find('.js--messenger__plupload');
        var $button = $modal.find('button[type="submit"]');
        var survey_id = $('body').attr('id');

        if(survey_id === undefined){
            survey_id = $('input[name="survey_id"]').val();
        }
        
        $plupload.toggleClass('hidden');

        if (pluploadInited) {
            return;
        }

        var token = $('.messenger__input input[name="_token"]').val();
        // const url = window.location.href.includes('microsite') ? '/surveys/messaging/temp/upload' : '/surveys/messaging/upload';

        $plupload.plupload({
            // General settings
            runtimes: 'html5,flash,silverlight,html4',
            url: '/surveys/messaging/upload',

            // Maximum file size
            max_file_size: '20mb',

            // Resize images on clientside if we can
            // resize: {
            //     width: 200,
            //     height: 200,
            //     quality: 90,
            //     crop: true // crop to exact dimensions
            // },

            // Specify what files to browse for
            filters: [{
                title: "Image files",
                extensions: "jpg,gif,png,jpeg,bmp"
            }, {
                title: "Zip files",
                extensions: "zip,avi"
            }, {
                title: "Custom files",
                extensions: "doc,docx,ppt,pptx,xls,xlsx,txt,rtf,pdf,msg"
            }],

            // Rename files by clicking on their titles
            rename: true,

            // Sort files
            sortable: true,

            // Enable ability to drag'n'drop files onto the widget (currently only HTML5 supports that)
            dragdrop: true,


            // Flash settings
            flash_swf_url: '/plupload/js/Moxie.swf',

            // Silverlight settings
            silverlight_xap_url: '/plupload/js/Moxie.xap',

            multipart_params: {
                "survey_id": survey_id,
                "_token": token,
            },

            init: {
                PostInit: function (e) {
                    $plupload.find('.plupload_button').addClass('btn').addClass('btn-xs').addClass('btn-primary');
                },
                FilesAdded: function (uploader, files) {
                    $plupload.addClass('file-added');
                    uploader.start();
                    $button.addClass('disabled');
                    $button.find('i.fa').removeClass('hidden');
                    pluploadFiles = files;

                },
                UploadComplete: function (uploader, files) {
                    $button.removeClass('disabled');
                    $button.find('i.fa').addClass('hidden');
                },
                FileUploaded: function (uploader, files, object) {
                    var response = JSON.parse(object.response);
                    if (response.response == "success") {
                        var url = "/surveys/messaging/attachment/" + survey_id + '/' + response.uploaded_filename + '/' + response.name;
                        attachmentLinks.push(url);
                        attachments.push(response.name);
                    }

                    if (response.response == 'error') {
                        bootbox.alert(response.message);
                    }
                }
            },

        });

        pluploadInited = true;

    };

    $(document).on('click', ".show_message_thread", function () {
        var refclass = $(this).data("refclass");
        $("input[name='rr_ref']").val(refclass);
        // $('.rrec_status').attr('name', $(this).data("refissue"));
        var issuename = $(this).data("refissue");

        var issueclosed = $(this).attr("data-issueclosed");

        if (issueclosed == '1') {
            $('.rrec_status').attr('checked', false);
        } else {
            $('.rrec_status').attr('checked', true);
        }

        const url = window.location.href;
        const size = url.includes('microsite') ? 'medium' : 'large'; // to modify modal sizing

        if (url.includes('microsite')) {
            msgCount = $(this).data('msg-count');
        }

        init(refclass, issuename, $(this), size);
    });
}());
