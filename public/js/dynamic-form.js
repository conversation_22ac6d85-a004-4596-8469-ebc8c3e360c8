(function() {

    //add property generator
    var totalCols = 4;
    var lines = [];
    var inputBoxKey = "input_box_";

    var addBox = function(boxId, title_value, initFirstRow = false){

        var currentBlock = $("#grid_box").children(".block");

        boxId = boxId !== undefined ? boxId : "block_" + currentBlock.length;

        var box = document.createElement("div");
        box.setAttribute("class", "block");
        box.setAttribute("id", boxId);

        var title = document.createElement("input");
        title.type = "text";
        title.setAttribute("class", "form-control boxTitle");
        title.setAttribute("placeholder", "Section Title");

        if(title_value !== undefined)
        {
            title.setAttribute("value", title_value);
        }

        box.appendChild(title);

        var an = document.createElement("a");
        an.text = "+";
        an.setAttribute("class", "btn btn-success addline");
        an.addEventListener("click", function(e){
            addLine(this);
        });

        box.appendChild(an);

        if(initFirstRow)
        {
            var newInputContainer = inputBoxKey + 0;
            box.appendChild(newLine(newInputContainer));
        }

        box.addEventListener("input", textChangeEvent);

        return box;
    };

    var addLine = function(element){

        var tempNode = $(element).parent();

        var newInputContainers = tempNode.children(".form_box_container");

        var line = newLine(inputBoxKey + newInputContainers.length);

        tempNode.append(line.outerHTML);
    };

    var newLine = function(containerId, data){
        var forms = [];

        var result = "";

        for (var i = 0; i < totalCols; i++) {

            var ele = document.createElement("input");
            ele.type = "text";
            ele.setAttribute("class", "form-control");
            ele.setAttribute("id", containerId + "_" + i);
            ele.setAttribute("data-input_box", containerId);

            if(data !== undefined)
            {
                ele.setAttribute("value", data[i]);
            }

            forms.push(ele);
        }

        var container = document.createElement("div");

        container.setAttribute("class", "form_box_container");
        container.setAttribute("id", containerId);

        forms.forEach(function(el, index) {
            container.appendChild(el);
        });

        return container;
    };

    var textChangeEvent = function(e){

        if(e.target === e.currentTarget) {
            e.stopPropagation();
            return false;
        }

        $('.box_field input[name="additional_info"]').val(generateResult());
    };

    var generateResult = function(){

        var item = {};
        var result = [];

        $('.block').each(function(index, o){
            var currentBlock = $(o);
            var id = currentBlock.attr("id");
            var title = currentBlock.find(".boxTitle");

            item = {
                key: id,
                title: title.val(),
                data: []
            };

            currentBlock.children(".form_box_container").each(function(innI, innerObj){
                var inputBoxKey = $(innerObj).attr("id");
                var inputValues = [];

                $(innerObj).children('input[type="text"]').each(function(i, inputObj){
                  inputValues.push($(inputObj).val());
                });

                var containerBox = {
                    key: inputBoxKey,
                    data: inputValues
                };

                item.data.push(containerBox);

            });

            result.push(item);
        });

        return JSON.stringify(result);
    };

    var loadBox = function(grid_box){
      var data = $('.box_field input[name="additional_info"]').val();

      if(addInfo !== null || addInfo.length > 0){
          addInfo.forEach(function(o, index){
              grid_box.appendChild(addBox(o.key, o.title, false));

              var boxObj = document.getElementById(o.key);

              if(boxObj != null)
              {
                  o.data.forEach(function(ct, ct_index){
                      var status = isAnyWithValue(ct.data);
                      if(status){
                          boxObj.appendChild(newLine(ct.key, ct.data));
                      }
                  });
              }
          });
      }
    };

    var isAnyWithValue = function(data = []){
        var status = false;
        if(data.length > 0){
            for (var index = 0; index < data.length; index++) {
                var value = data[index];
                if(value !== "" || value.length > 0){
                    status = true;
                    break;
                }
            }
        }
        return status;
    };

    var init = function(){
        var grid_box = document.getElementById("grid_box");

        if(grid_box != null)
        {
            $('#add').click(function(){
                grid_box.appendChild(addBox(undefined, undefined, true));
            });

            loadBox(grid_box);
        }
    };

    init();

}());
