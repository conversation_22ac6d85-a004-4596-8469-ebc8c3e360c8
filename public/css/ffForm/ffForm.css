.ffForm-target > div {
  background: #f4f4f4;
  border: 1px solid #ccc;
  margin-bottom: 10px; }
  .ffForm-target > div.error {
    border: 1px solid #c30; }
  .ffForm-target > div h2 {
    position: relative;
    padding: 15px; }
    .ffForm-target > div h2 a {
      color: #333; }
    .ffForm-target > div h2 .handle {
      position: relative;
      z-index: 1;
      cursor: move;
      margin-right: 5px; }
    .ffForm-target > div h2 .minField {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      text-align: right;
      padding: 15px 40px 0 0; }
    .ffForm-target > div h2 .delField {
      float: right;
      position: relative;
      z-index: 1; }
  .ffForm-target > div > ul {
    padding: 0 15px 15px;
    display: none; }
    .ffForm-target > div > ul li:not(:first-of-type) {
      margin-top: 10px; }
    .ffForm-target > div > ul li .error {
      color: #c30;
      display: block;
      font-size: 80%; }
    .ffForm-target > div > ul li .checkbox input {
      position: relative;
      top: 1px; }
  .ffForm-target > div p select {
    color: #333 !important; }
  .ffForm-target > div p a {
    color: #fff; }

#ffForm ul li {
  margin-bottom: 10px; }
  #ffForm ul li a {
    display: block;
    padding: 15px;
    background: #f4f4f4;
    border: 1px solid #ccc;
    color: #333;
    text-decoration: none; }

#ffgenerated {
  background: #efefef;
  display: flex;
  flex-wrap: wrap;
  border: 1px solid #ccc;
  padding: 0 15px;
  margin-bottom: 20px; }
  #ffgenerated .rc_dd {
    float: left !important;
    width: 90% !important; }
  #ffgenerated .rcb_dd {
    float: left !important;
    width: 90% !important; }
  #ffgenerated .colorcode {
    width: 34px !important;
    height: 34px !important;
    float: right !important; }
  #ffgenerated p {
    margin: 15px 0; }
  #ffgenerated label {
    font-weight: bold;
    display: block;
    margin-bottom: 2px; }
    #ffgenerated label.radio-inline, #ffgenerated label.checkbox-inline {
      font-weight: normal;
      display: inline-block; }
  #ffgenerated h2.ffheader {
    font-weight: bolder;
    margin-top: 30px; }
  #ffgenerated hr {
    margin-top: 10px;
    margin-bottom: 10px;
    border-top: 1px solid #032862; }

/*# sourceMappingURL=ffForm.css.map */
