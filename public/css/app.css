/**
 * selectize.css (v0.12.6)
 * Copyright (c) 2013–2015 <PERSON> & contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this
 * file except in compliance with the License. You may obtain a copy of the License at:
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under
 * the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF
 * ANY KIND, either express or implied. See the License for the specific language
 * governing permissions and limitations under the License.
 *
 * <AUTHOR> <<EMAIL>>
 */

.selectize-control.plugin-drag_drop.multi > .selectize-input > div.ui-sortable-placeholder {
  visibility: visible !important;
  background: #f2f2f2 !important;
  background: rgba(0, 0, 0, 0.06) !important;
  border: 0 none !important;
  box-shadow: inset 0 0 12px 4px #fff;
}
.selectize-control.plugin-drag_drop .ui-sortable-placeholder::after {
  content: '!';
  visibility: hidden;
}
.selectize-control.plugin-drag_drop .ui-sortable-helper {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}
.selectize-dropdown-header {
  position: relative;
  padding: 5px 8px;
  border-bottom: 1px solid #d0d0d0;
  background: #f8f8f8;
  border-radius: 3px 3px 0 0;
}
.selectize-dropdown-header-close {
  position: absolute;
  right: 8px;
  top: 50%;
  color: #303030;
  opacity: 0.4;
  margin-top: -12px;
  line-height: 20px;
  font-size: 20px !important;
}
.selectize-dropdown-header-close:hover {
  color: #000000;
}
.selectize-dropdown.plugin-optgroup_columns .optgroup {
  border-right: 1px solid #f2f2f2;
  border-top: 0 none;
  float: left;
  box-sizing: border-box;
}
.selectize-dropdown.plugin-optgroup_columns .optgroup:last-child {
  border-right: 0 none;
}
.selectize-dropdown.plugin-optgroup_columns .optgroup:before {
  display: none;
}
.selectize-dropdown.plugin-optgroup_columns .optgroup-header {
  border-top: 0 none;
}
.selectize-control.plugin-remove_button [data-value] {
  position: relative;
  padding-right: 24px !important;
}
.selectize-control.plugin-remove_button [data-value] .remove {
  z-index: 1;
  /* fixes ie bug (see #392) */
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 17px;
  text-align: center;
  font-weight: bold;
  font-size: 12px;
  color: inherit;
  text-decoration: none;
  vertical-align: middle;
  display: inline-block;
  padding: 2px 0 0 0;
  border-left: 1px solid #d0d0d0;
  border-radius: 0 2px 2px 0;
  box-sizing: border-box;
}
.selectize-control.plugin-remove_button [data-value] .remove:hover {
  background: rgba(0, 0, 0, 0.05);
}
.selectize-control.plugin-remove_button [data-value].active .remove {
  border-left-color: #cacaca;
}
.selectize-control.plugin-remove_button .disabled [data-value] .remove:hover {
  background: none;
}
.selectize-control.plugin-remove_button .disabled [data-value] .remove {
  border-left-color: #ffffff;
}
.selectize-control.plugin-remove_button .remove-single {
  position: absolute;
  right: 0;
  top: 0;
  font-size: 23px;
}
.selectize-control {
  position: relative;
}
.selectize-dropdown,
.selectize-input,
.selectize-input input {
  color: #303030;
  font-family: inherit;
  font-size: 13px;
  line-height: 18px;
  -webkit-font-smoothing: inherit;
}
.selectize-input,
.selectize-control.single .selectize-input.input-active {
  background: #fff;
  cursor: text;
  display: inline-block;
}
.selectize-input {
  border: 1px solid #d0d0d0;
  padding: 8px 8px;
  display: inline-block;
  width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 1;
  box-sizing: border-box;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}
.selectize-control.multi .selectize-input.has-items {
  padding: 6px 8px 3px;
}
.selectize-input.full {
  background-color: #fff;
}
.selectize-input.disabled,
.selectize-input.disabled * {
  cursor: default !important;
}
.selectize-input.focus {
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.15);
}
.selectize-input.dropdown-active {
  border-radius: 3px 3px 0 0;
}
.selectize-input > * {
  vertical-align: baseline;
  display: -moz-inline-stack;
  display: inline-block;
  zoom: 1;
  *display: inline;
}
.selectize-control.multi .selectize-input > div {
  cursor: pointer;
  margin: 0 3px 3px 0;
  padding: 2px 6px;
  background: #f2f2f2;
  color: #303030;
  border: 0 solid #d0d0d0;
}
.selectize-control.multi .selectize-input > div.active {
  background: #e8e8e8;
  color: #303030;
  border: 0 solid #cacaca;
}
.selectize-control.multi .selectize-input.disabled > div,
.selectize-control.multi .selectize-input.disabled > div.active {
  color: #7d7d7d;
  background: #ffffff;
  border: 0 solid #ffffff;
}
.selectize-input > input {
  display: inline-block !important;
  padding: 0 !important;
  min-height: 0 !important;
  max-height: none !important;
  max-width: 100% !important;
  margin: 0 2px 0 0 !important;
  text-indent: 0 !important;
  border: 0 none !important;
  background: none !important;
  line-height: inherit !important;
  -webkit-user-select: auto !important;
  box-shadow: none !important;
}
.selectize-input > input::-ms-clear {
  display: none;
}
.selectize-input > input:focus {
  outline: none !important;
}
.selectize-input::after {
  content: ' ';
  display: block;
  clear: left;
}
.selectize-input.dropdown-active::before {
  content: ' ';
  display: block;
  position: absolute;
  background: #f0f0f0;
  height: 1px;
  bottom: 0;
  left: 0;
  right: 0;
}
.selectize-dropdown {
  position: absolute;
  z-index: 10;
  border: 1px solid #d0d0d0;
  background: #fff;
  margin: -1px 0 0 0;
  border-top: 0 none;
  box-sizing: border-box;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-radius: 0 0 3px 3px;
}
.selectize-dropdown [data-selectable] {
  cursor: pointer;
  overflow: hidden;
}
.selectize-dropdown [data-selectable] .highlight {
  background: rgba(125, 168, 208, 0.2);
  border-radius: 1px;
}
.selectize-dropdown .option,
.selectize-dropdown .optgroup-header {
  padding: 5px 8px;
}
.selectize-dropdown .option,
.selectize-dropdown [data-disabled],
.selectize-dropdown [data-disabled] [data-selectable].option {
  cursor: inherit;
  opacity: 0.5;
}
.selectize-dropdown [data-selectable].option {
  opacity: 1;
}
.selectize-dropdown .optgroup:first-child .optgroup-header {
  border-top: 0 none;
}
.selectize-dropdown .optgroup-header {
  color: #303030;
  background: #fff;
  cursor: default;
}
.selectize-dropdown .active {
  background-color: #f5fafd;
  color: #495c68;
}
.selectize-dropdown .active.create {
  color: #495c68;
}
.selectize-dropdown .create {
  color: rgba(48, 48, 48, 0.5);
}
.selectize-dropdown-content {
  overflow-y: auto;
  overflow-x: hidden;
  max-height: 200px;
  -webkit-overflow-scrolling: touch;
}
.selectize-control.single .selectize-input,
.selectize-control.single .selectize-input input {
  cursor: pointer;
}
.selectize-control.single .selectize-input.input-active,
.selectize-control.single .selectize-input.input-active input {
  cursor: text;
}
.selectize-control.single .selectize-input:after {
  content: ' ';
  display: block;
  position: absolute;
  top: 50%;
  right: 15px;
  margin-top: -3px;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 5px 5px 0 5px;
  border-color: #808080 transparent transparent transparent;
}
.selectize-control.single .selectize-input.dropdown-active:after {
  margin-top: -4px;
  border-width: 0 5px 5px 5px;
  border-color: transparent transparent #808080 transparent;
}
.selectize-control.rtl.single .selectize-input:after {
  left: 15px;
  right: auto;
}
.selectize-control.rtl .selectize-input > input {
  margin: 0 4px 0 -2px !important;
}
.selectize-control .selectize-input.disabled {
  opacity: 0.5;
  background-color: #fafafa;
}
@charset "UTF-8";
/* ROBOTO */
@font-face {
  font-family: "Roboto Web";
  src: url("/fonts/Roboto-Bold.woff2") format("woff2"), url("/fonts/Roboto-Bold.woff") format("woff");
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Roboto Web";
  src: url("/fonts/Roboto-Light.woff2") format("woff2"), url("/fonts/Roboto-Light.woff") format("woff");
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Roboto Web";
  src: url("/fonts/Roboto-Black.woff2") format("woff2"), url("/fonts/Roboto-Black.woff") format("woff");
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Roboto Web";
  src: url("/fonts/Roboto-Medium.woff2") format("woff2"), url("/fonts/Roboto-Medium.woff") format("woff");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Roboto Web";
  src: url("/fonts/Roboto-Regular.woff2") format("woff2"), url("/fonts/Roboto-Regular.woff") format("woff");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
/* Geist */
@font-face {
  font-family: "Geist";
  src: url("/fonts/Geist-Regular.woff2") format("woff2");
  font-weight: 400;
  font-style: normal;
}
@font-face {
  font-family: "Geist";
  src: url("/fonts/Geist-Light.woff2") format("woff2");
  font-weight: 300;
  font-style: normal;
}
@font-face {
  font-family: "Geist";
  src: url("/fonts/Geist-Medium.woff2") format("woff2");
  font-weight: 500;
  font-style: normal;
}
@font-face {
  font-family: "Geist";
  src: url("/fonts/Geist-Bold.woff2") format("woff2");
  font-weight: 700;
  font-style: normal;
}
@font-face {
  font-family: "Geist Black";
  src: url("/fonts/Geist-Black.woff2") format("woff2");
  font-weight: 900;
  font-style: normal;
}
@font-face {
  font-family: "Geist";
  src: url("/fonts/Geist-SemiBold.woff2") format("woff2");
  font-weight: 600;
  font-style: normal;
}
@font-face {
  font-family: "Roboto";
  src: url("/fonts/Roboto-Light.ttf");
  font-style: normal;
  font-weight: 300;
}
@font-face {
  font-family: "Roboto";
  src: url("/fonts/Roboto-Regular.ttf");
  font-style: normal;
  font-weight: 400;
}
@font-face {
  font-family: "Roboto";
  src: url("/fonts/Roboto-Medium.ttf");
  font-style: normal;
  font-weight: 500;
}
@font-face {
  font-family: "Roboto";
  src: url("/fonts/Roboto-Bold.ttf");
  font-style: normal;
  font-weight: 700;
}
@font-face {
  font-family: "Roboto";
  src: url("/fonts/Roboto-Black.ttf");
  font-style: normal;
  font-weight: 900;
}
@font-face {
  font-family: "lsm-icons";
  src: url("/fonts/lsm-icons.eot?9li7el#iefix") format("embedded-opentype"), url("/fonts/lsm-icons.ttf?9li7el") format("truetype"), url("/fonts/lsm-icons.woff?9li7el") format("woff"), url("/fonts/lsm-icons.svg?9li7el#lsm-icons") format("svg");
  font-weight: normal;
  font-style: normal;
}
.icon, #ffFormCreate .fa {
  font-family: "lsm-icons" !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.icon.icon-sm, #ffFormCreate .icon-sm.fa {
  font-size: 12px;
}
.icon.icon-md, #ffFormCreate .icon-md.fa {
  font-size: 16px;
}
.icon.icon-lg, #ffFormCreate .icon-lg.fa {
  font-size: 24px;
}
.icon.icon-xl, #ffFormCreate .icon-xl.fa {
  font-size: 30px;
}
.icon.icon-xxl, #ffFormCreate .icon-xxl.fa {
  font-size: 64px;
}
.icon.icon-rec, #ffFormCreate .icon-rec.fa {
  background-image: url("data:image/svg+xml,%3Csvg id='Component_10_1' data-name='Component 10 – 1' xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E%3Ccircle id='Ellipse_124' data-name='Ellipse 124' cx='5' cy='5' r='5' transform='translate(3 3)' fill='%23cc6565'/%3E%3Cg id='Ellipse_125' data-name='Ellipse 125' fill='none' stroke='%23cc6565' stroke-width='1'%3E%3Ccircle cx='8' cy='8' r='8' stroke='none'/%3E%3Ccircle cx='8' cy='8' r='7.5' fill='none'/%3E%3C/g%3E%3C/svg%3E%0A");
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  width: 12px;
  height: 12px;
  display: inline-block;
  position: relative;
  top: 1px;
  margin-right: 7px;
}

.icon-activity::before {
  content: "\E900";
}

.icon-airplay::before {
  content: "\E901";
}

.icon-alert-circle::before {
  content: "\E902";
}

.icon-alert-octagon::before {
  content: "\E903";
}

.icon-alert-triangle::before {
  content: "\E904";
}

.icon-align-center::before {
  content: "\E905";
}

.icon-align-justify::before {
  content: "\E906";
}

.icon-align-left::before {
  content: "\E907";
}

.icon-align-right::before {
  content: "\E908";
}

.icon-anchor::before {
  content: "\E909";
}

.icon-aperture::before {
  content: "\E90A";
}

.icon-archive::before {
  content: "\E90B";
}

.icon-arrow-down::before {
  content: "\E90C";
}

.icon-arrow-down-circle::before {
  content: "\E90D";
}

.icon-arrow-down-left::before {
  content: "\E90E";
}

.icon-arrow-down-right::before {
  content: "\E90F";
}

.icon-arrow-left::before {
  content: "\E910";
}

.icon-arrow-left-circle::before {
  content: "\E911";
}

.icon-arrow-right::before {
  content: "\E912";
}

.icon-arrow-right-circle::before {
  content: "\E913";
}

.icon-arrow-up::before {
  content: "\E914";
}

.icon-arrow-up-circle::before {
  content: "\E915";
}

.icon-arrow-up-left::before {
  content: "\E916";
}

.icon-arrow-up-right::before {
  content: "\E917";
}

.icon-at-sign::before {
  content: "\E918";
}

.icon-award::before {
  content: "\E919";
}

.icon-bar-chart::before {
  content: "\E91A";
}

.icon-bar-chart-2::before {
  content: "\E91B";
}

.icon-battery::before {
  content: "\E91C";
}

.icon-battery-charging::before {
  content: "\E91D";
}

.icon-bell::before {
  content: "\E91E";
}

.icon-bell-off::before {
  content: "\E91F";
}

.icon-bluetooth::before {
  content: "\E920";
}

.icon-bold::before {
  content: "\E921";
}

.icon-book::before {
  content: "\E922";
}

.icon-bookmark::before {
  content: "\E923";
}

.icon-book-open::before {
  content: "\E924";
}

.icon-box::before {
  content: "\E925";
}

.icon-briefcase::before {
  content: "\E926";
}

.icon-calendar::before {
  content: "\E927";
}

.icon-camera::before {
  content: "\E928";
}

.icon-camera-off::before {
  content: "\E929";
}

.icon-cast::before {
  content: "\E92A";
}

.icon-check::before {
  content: "\E92B";
}

.icon-check-circle::before {
  content: "\E92C";
}

.icon-check-square::before {
  content: "\E92D";
}

.icon-chevron-down::before, #ffFormCreate .fa.fa-caret-down::before {
  content: "\E92E";
}

.icon-chevron-left::before {
  content: "\E92F";
}

.icon-chevron-right::before, #ffFormCreate .fa.fa-caret-right::before {
  content: "\E930";
}

.icon-chevrons-down::before {
  content: "\E931";
}

.icon-chevrons-left::before {
  content: "\E932";
}

.icon-chevrons-right::before {
  content: "\E933";
}

.icon-chevrons-up::before {
  content: "\E934";
}

.icon-chevron-up::before {
  content: "\E935";
}

.icon-chrome::before {
  content: "\E936";
}

.icon-circle::before {
  content: "\E937";
}

.icon-clipboard::before {
  content: "\E938";
}

.icon-clock::before {
  content: "\E939";
}

.icon-cloud::before {
  content: "\E93A";
}

.icon-cloud-drizzle::before {
  content: "\E93B";
}

.icon-cloud-lightning::before {
  content: "\E93C";
}

.icon-cloud-off::before {
  content: "\E93D";
}

.icon-cloud-rain::before {
  content: "\E93E";
}

.icon-cloud-snow::before {
  content: "\E93F";
}

.icon-code::before {
  content: "\E940";
}

.icon-codepen::before {
  content: "\E941";
}

.icon-coffee::before {
  content: "\E942";
}

.icon-command::before {
  content: "\E943";
}

.icon-compass::before {
  content: "\E944";
}

.icon-copy::before {
  content: "\E945";
}

.icon-corner-down-left::before {
  content: "\E946";
}

.icon-corner-down-right::before {
  content: "\E947";
}

.icon-corner-left-down::before {
  content: "\E948";
}

.icon-corner-left-up::before {
  content: "\E949";
}

.icon-corner-right-down::before {
  content: "\E94A";
}

.icon-corner-right-up::before {
  content: "\E94B";
}

.icon-corner-up-left::before {
  content: "\E94C";
}

.icon-corner-up-right::before {
  content: "\E94D";
}

.icon-cpu::before {
  content: "\E94E";
}

.icon-credit-card::before {
  content: "\E94F";
}

.icon-crop::before {
  content: "\E950";
}

.icon-crosshair::before {
  content: "\E951";
}

.icon-database::before {
  content: "\E952";
}

.icon-delete::before {
  content: "\E953";
}

.icon-disc::before {
  content: "\E954";
}

.icon-dollar-sign::before {
  content: "\E955";
}

.icon-download::before {
  content: "\E956";
}

.icon-download-cloud::before {
  content: "\E957";
}

.icon-droplet::before {
  content: "\E958";
}

.icon-edit::before {
  content: "\E959";
}

.icon-edit-2::before {
  content: "\E95A";
}

.icon-edit-3::before {
  content: "\E95B";
}

.icon-external-link::before {
  content: "\E95C";
}

.icon-eye::before {
  content: "\E95D";
}

.icon-eye-off::before {
  content: "\E95E";
}

.icon-facebook::before {
  content: "\E95F";
}

.icon-fast-forward::before {
  content: "\E960";
}

.icon-feather::before {
  content: "\E961";
}

.icon-figma::before {
  content: "\E962";
}

.icon-file::before {
  content: "\E963";
}

.icon-file-minus::before {
  content: "\E964";
}

.icon-file-plus::before {
  content: "\E965";
}

.icon-file-text::before {
  content: "\E966";
}

.icon-film::before {
  content: "\E967";
}

.icon-filter::before {
  content: "\E968";
}

.icon-flag::before {
  content: "\E969";
}

.icon-folder::before {
  content: "\E96A";
}

.icon-folder-minus::before {
  content: "\E96B";
}

.icon-folder-plus::before {
  content: "\E96C";
}

.icon-frown::before {
  content: "\E96D";
}

.icon-gift::before {
  content: "\E96E";
}

.icon-git-branch::before {
  content: "\E96F";
}

.icon-git-commit::before {
  content: "\E970";
}

.icon-github::before {
  content: "\E971";
}

.icon-gitlab::before {
  content: "\E972";
}

.icon-git-merge::before {
  content: "\E973";
}

.icon-git-pull-request::before {
  content: "\E974";
}

.icon-globe::before {
  content: "\E975";
}

.icon-grid::before {
  content: "\E976";
}

.icon-hard-drive::before {
  content: "\E977";
}

.icon-hash::before {
  content: "\E978";
}

.icon-headphones::before {
  content: "\E979";
}

.icon-heart::before {
  content: "\E97A";
}

.icon-help-circle::before {
  content: "\E97B";
}

.icon-home::before {
  content: "\E97C";
}

.icon-image::before {
  content: "\E97D";
}

.icon-inbox::before {
  content: "\E97E";
}

.icon-info::before {
  content: "\E97F";
}

.icon-instagram::before {
  content: "\E980";
}

.icon-italic::before {
  content: "\E981";
}

.icon-key::before {
  content: "\E982";
}

.icon-layers::before {
  content: "\E983";
}

.icon-layout::before {
  content: "\E984";
}

.icon-life-buoy::before {
  content: "\E985";
}

.icon-link::before {
  content: "\E986";
}

.icon-link-2::before {
  content: "\E987";
}

.icon-linkedin::before {
  content: "\E988";
}

.icon-list::before {
  content: "\E989";
}

.icon-loader::before {
  content: "\E98A";
}

.icon-lock::before {
  content: "\E98B";
}

.icon-log-in::before {
  content: "\E98C";
}

.icon-log-out::before {
  content: "\E98D";
}

.icon-mail::before {
  content: "\E98E";
}

.icon-map::before {
  content: "\E98F";
}

.icon-map-pin::before {
  content: "\E990";
}

.icon-maximize::before {
  content: "\E991";
}

.icon-maximize-2::before {
  content: "\E992";
}

.icon-meh::before {
  content: "\E993";
}

.icon-menu::before {
  content: "\E994";
}

.icon-message-circle::before {
  content: "\E995";
}

.icon-message-square::before {
  content: "\E996";
}

.icon-mic::before {
  content: "\E997";
}

.icon-mic-off::before {
  content: "\E998";
}

.icon-minimize::before {
  content: "\E999";
}

.icon-minimize-2::before {
  content: "\E99A";
}

.icon-minus::before {
  content: "\E99B";
}

.icon-minus-circle::before {
  content: "\E99C";
}

.icon-minus-square::before {
  content: "\E99D";
}

.icon-monitor::before {
  content: "\E99E";
}

.icon-moon::before {
  content: "\E99F";
}

.icon-more-horizontal::before {
  content: "\E9A0";
}

.icon-more-vertical::before {
  content: "\E9A1";
}

.icon-mouse-pointer::before {
  content: "\E9A2";
}

.icon-move::before, #ffFormCreate .fa.fa-arrows::before {
  content: "\E9A3";
}

.icon-music::before {
  content: "\E9A4";
}

.icon-navigation::before {
  content: "\E9A5";
}

.icon-navigation-2::before {
  content: "\E9A6";
}

.icon-octagon::before {
  content: "\E9A7";
}

.icon-package::before {
  content: "\E9A8";
}

.icon-paperclip::before {
  content: "\E9A9";
}

.icon-pause::before {
  content: "\E9AA";
}

.icon-pause-circle::before {
  content: "\E9AB";
}

.icon-pen-tool::before {
  content: "\E9AC";
}

.icon-percent::before {
  content: "\E9AD";
}

.icon-phone::before {
  content: "\E9AE";
}

.icon-phone-call::before {
  content: "\E9AF";
}

.icon-phone-forwarded::before {
  content: "\E9B0";
}

.icon-phone-incoming::before {
  content: "\E9B1";
}

.icon-phone-missed::before {
  content: "\E9B2";
}

.icon-phone-off::before {
  content: "\E9B3";
}

.icon-phone-outgoing::before {
  content: "\E9B4";
}

.icon-pie-chart::before {
  content: "\E9B5";
}

.icon-play::before {
  content: "\E9B6";
}

.icon-play-circle::before {
  content: "\E9B7";
}

.icon-plus::before {
  content: "\E9B8";
}

.icon-plus-circle::before {
  content: "\E9B9";
}

.icon-plus-square::before {
  content: "\E9BA";
}

.icon-pocket::before {
  content: "\E9BB";
}

.icon-power::before {
  content: "\E9BC";
}

.icon-printer::before {
  content: "\E9BD";
}

.icon-radio::before {
  content: "\E9BE";
}

.icon-refresh-ccw::before {
  content: "\E9BF";
}

.icon-refresh-cw::before {
  content: "\E9C0";
}

.icon-repeat::before {
  content: "\E9C1";
}

.icon-rewind::before {
  content: "\E9C2";
}

.icon-rotate-ccw::before {
  content: "\E9C3";
}

.icon-rotate-cw::before {
  content: "\E9C4";
}

.icon-rss::before {
  content: "\E9C5";
}

.icon-save::before {
  content: "\E9C6";
}

.icon-scissors::before {
  content: "\E9C7";
}

.icon-search::before {
  content: "\E9C8";
}

.icon-send::before {
  content: "\E9C9";
}

.icon-server::before {
  content: "\E9CA";
}

.icon-settings::before {
  content: "\E9CB";
}

.icon-share::before {
  content: "\E9CC";
}

.icon-share-2::before {
  content: "\E9CD";
}

.icon-shield::before {
  content: "\E9CE";
}

.icon-shield-off::before {
  content: "\E9CF";
}

.icon-shopping-bag::before {
  content: "\E9D0";
}

.icon-shopping-cart::before {
  content: "\E9D1";
}

.icon-shuffle::before {
  content: "\E9D2";
}

.icon-sidebar::before {
  content: "\E9D3";
}

.icon-skip-back::before {
  content: "\E9D4";
}

.icon-skip-forward::before {
  content: "\E9D5";
}

.icon-slack::before {
  content: "\E9D6";
}

.icon-slash::before {
  content: "\E9D7";
}

.icon-sliders::before {
  content: "\E9D8";
}

.icon-smartphone::before {
  content: "\E9D9";
}

.icon-smile::before {
  content: "\E9DA";
}

.icon-speaker::before {
  content: "\E9DB";
}

.icon-square::before {
  content: "\E9DC";
}

.icon-star::before {
  content: "\E9DD";
}

.icon-stop-circle::before {
  content: "\E9DE";
}

.icon-sun::before {
  content: "\E9DF";
}

.icon-sunrise::before {
  content: "\E9E0";
}

.icon-sunset::before {
  content: "\E9E1";
}

.icon-tablet::before {
  content: "\E9E2";
}

.icon-tag::before {
  content: "\E9E3";
}

.icon-target::before {
  content: "\E9E4";
}

.icon-terminal::before {
  content: "\E9E5";
}

.icon-thermometer::before {
  content: "\E9E6";
}

.icon-thumbs-down::before {
  content: "\E9E7";
}

.icon-thumbs-up::before {
  content: "\E9E8";
}

.icon-toggle-left::before {
  content: "\E9E9";
}

.icon-toggle-right::before {
  content: "\E9EA";
}

.icon-trash::before {
  content: "\E9EB";
}

.icon-trash-2::before, #ffFormCreate .fa.fa-times::before {
  content: "\E9EC";
}

.icon-trello::before {
  content: "\E9ED";
}

.icon-trending-down::before {
  content: "\E9EE";
}

.icon-trending-up::before {
  content: "\E9EF";
}

.icon-triangle::before {
  content: "\E9F0";
}

.icon-truck::before {
  content: "\E9F1";
}

.icon-tv::before {
  content: "\E9F2";
}

.icon-twitter::before {
  content: "\E9F3";
}

.icon-type::before {
  content: "\E9F4";
}

.icon-umbrella::before {
  content: "\E9F5";
}

.icon-underline::before {
  content: "\E9F6";
}

.icon-unlock::before {
  content: "\E9F7";
}

.icon-upload::before {
  content: "\E9F8";
}

.icon-upload-cloud::before {
  content: "\E9F9";
}

.icon-user::before {
  content: "\E9FA";
}

.icon-user-check::before {
  content: "\E9FB";
}

.icon-user-minus::before {
  content: "\E9FC";
}

.icon-user-plus::before {
  content: "\E9FD";
}

.icon-users::before {
  content: "\E9FE";
}

.icon-user-x::before {
  content: "\E9FF";
}

.icon-video::before {
  content: "\EA00";
}

.icon-video-off::before {
  content: "\EA01";
}

.icon-voicemail::before {
  content: "\EA02";
}

.icon-volume::before {
  content: "\EA03";
}

.icon-volume-1::before {
  content: "\EA04";
}

.icon-volume-2::before {
  content: "\EA05";
}

.icon-volume-x::before {
  content: "\EA06";
}

.icon-watch::before {
  content: "\EA07";
}

.icon-wifi::before {
  content: "\EA08";
}

.icon-wifi-off::before {
  content: "\EA09";
}

.icon-wind::before {
  content: "\EA0A";
}

.icon-x::before {
  content: "\EA0B";
}

.icon-x-circle::before {
  content: "\EA0C";
}

.icon-x-octagon::before {
  content: "\EA0D";
}

.icon-x-square::before {
  content: "\EA0E";
}

.icon-youtube::before {
  content: "\EA0F";
}

.icon-zap::before {
  content: "\EA10";
}

.icon-zap-off::before {
  content: "\EA11";
}

.icon-zoom-in::before {
  content: "\EA12";
}

.icon-zoom-out::before {
  content: "\EA13";
}

.icon-locked {
  background-image: url('data:image/svg+xml,<svg width="28" height="35" viewBox="0 0 28 35" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M23.23 14.8398H4C1.795 14.8398 0 16.6348 0 18.8398V30.9298C0 33.1348 1.795 34.9298 4 34.9298H23.23C25.435 34.9298 27.23 33.1348 27.23 30.9298V18.8398C27.23 16.6348 25.435 14.8398 23.23 14.8398ZM25.23 30.9298C25.23 32.0348 24.335 32.9298 23.23 32.9298H4C2.895 32.9298 2 32.0348 2 30.9298V18.8398C2 17.7348 2.895 16.8398 4 16.8398H23.23C24.335 16.8398 25.23 17.7348 25.23 18.8398V30.9298Z" fill="%23306E9A"/><path d="M6.62 7.735C6.62 4.575 9.195 2 12.355 2H15.125C18.285 2 20.86 4.575 20.86 7.735V13.19H22.86V7.735C22.86 3.47 19.39 0 15.125 0H12.355C8.175 0 4.755 3.335 4.625 7.49V13.265H6.62V7.735Z" fill="%23306E9A"/><path d="M13.7401 20.2441C12.5601 20.2441 11.6001 21.2041 11.6001 22.3841C11.6001 23.2041 12.0651 23.9091 12.7451 24.2691V29.5291H14.7801V24.2441C15.4351 23.8791 15.8801 23.1891 15.8801 22.3841C15.8801 21.2041 14.9201 20.2441 13.7401 20.2441Z" fill="%23306E9A"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  width: 28px;
  height: 35px;
  display: inline-block;
}
.icon-locked.grey-circle {
  background-color: #f3f6fb;
  border-radius: 50%;
  width: 70px;
  height: 70px;
  background-size: 45%;
}

/*!
 * Bootstrap v4.6.2 (https://getbootstrap.com/)
 * Copyright 2011-2022 The Bootstrap Authors
 * Copyright 2011-2022 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
 */
:root {
  --blue: #337ab7;
  --indigo: #6610f2;
  --purple: #7A4883;
  --pink: #e83e8c;
  --red: #cc6565;
  --orange: #ef8c3f;
  --yellow: #ffc107;
  --green: #64d091;
  --teal: #20c997;
  --cyan: #17a2b8;
  --white: #fff;
  --gray: #6c757d;
  --gray-dark: #343a40;
  --primary: #337ab7;
  --secondary: #002663;
  --success: #64d091;
  --info: #388695;
  --warning: #DB9E5B;
  --danger: #cc6565;
  --light: #d8dce6;
  --dark: #5e6983;
  --lighter: #fafafc;
  --lighter-blue: #f4f8fc;
  --breakpoint-xs: 0;
  --breakpoint-sm: 600px;
  --breakpoint-md: 980px;
  --breakpoint-lg: 1200px;
  --breakpoint-xl: 1580px;
  --font-family-sans-serif: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --font-family-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  font-family: sans-serif;
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

article, aside, figcaption, figure, footer, header, hgroup, main, nav, section {
  display: block;
}

body {
  margin: 0;
  font-family: "Roboto", sans-serif, serif;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.3125;
  color: #5e6983;
  text-align: left;
  background-color: #fff;
}

[tabindex="-1"]:focus:not(:focus-visible) {
  outline: 0 !important;
}

hr {
  box-sizing: content-box;
  height: 0;
  overflow: visible;
}

h1, h2, h3, h4, h5, h6 {
  margin-top: 0;
  margin-bottom: 0.5rem;
}

p {
  margin-top: 0;
  margin-bottom: 1rem;
}

abbr[title],
abbr[data-original-title] {
  text-decoration: underline;
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
  cursor: help;
  border-bottom: 0;
  -webkit-text-decoration-skip-ink: none;
          text-decoration-skip-ink: none;
}

address {
  margin-bottom: 1rem;
  font-style: normal;
  line-height: inherit;
}

ol,
ul,
dl {
  margin-top: 0;
  margin-bottom: 1rem;
}

ol ol,
ul ul,
ol ul,
ul ol {
  margin-bottom: 0;
}

dt {
  font-weight: 700;
}

dd {
  margin-bottom: 0.5rem;
  margin-left: 0;
}

blockquote {
  margin: 0 0 1rem;
}

b,
strong {
  font-weight: 500;
}

small {
  font-size: 80%;
}

sub,
sup {
  position: relative;
  font-size: 75%;
  line-height: 0;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

a {
  color: #337ab7;
  text-decoration: none;
  background-color: transparent;
}
a:hover {
  color: #22527b;
  text-decoration: underline;
}

a:not([href]):not([class]) {
  color: inherit;
  text-decoration: none;
}
a:not([href]):not([class]):hover {
  color: inherit;
  text-decoration: none;
}

pre,
code,
kbd,
samp {
  font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  font-size: 1em;
}

pre {
  margin-top: 0;
  margin-bottom: 1rem;
  overflow: auto;
  -ms-overflow-style: scrollbar;
}

figure {
  margin: 0 0 1rem;
}

img {
  vertical-align: middle;
  border-style: none;
}

svg {
  overflow: hidden;
  vertical-align: middle;
}

table {
  border-collapse: collapse;
}

caption {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  color: #6c757d;
  text-align: left;
  caption-side: bottom;
}

th {
  text-align: inherit;
  text-align: -webkit-match-parent;
}

label {
  display: inline-block;
  margin-bottom: 0.5rem;
}

button {
  border-radius: 0;
}

button:focus:not(:focus-visible) {
  outline: 0;
}

input,
button,
select,
optgroup,
textarea {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

button,
input {
  overflow: visible;
}

button,
select {
  text-transform: none;
}

[role=button] {
  cursor: pointer;
}

select {
  word-wrap: normal;
}

button,
[type=button],
[type=reset],
[type=submit] {
  -webkit-appearance: button;
}

button:not(:disabled),
[type=button]:not(:disabled),
[type=reset]:not(:disabled),
[type=submit]:not(:disabled) {
  cursor: pointer;
}

button::-moz-focus-inner,
[type=button]::-moz-focus-inner,
[type=reset]::-moz-focus-inner,
[type=submit]::-moz-focus-inner {
  padding: 0;
  border-style: none;
}

input[type=radio],
input[type=checkbox] {
  box-sizing: border-box;
  padding: 0;
}

textarea {
  overflow: auto;
  resize: vertical;
}

fieldset {
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
}

legend {
  display: block;
  width: 100%;
  max-width: 100%;
  padding: 0;
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
  line-height: inherit;
  color: inherit;
  white-space: normal;
}

progress {
  vertical-align: baseline;
}

[type=number]::-webkit-inner-spin-button,
[type=number]::-webkit-outer-spin-button {
  height: auto;
}

[type=search] {
  outline-offset: -2px;
  -webkit-appearance: none;
}

[type=search]::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-file-upload-button {
  font: inherit;
  -webkit-appearance: button;
}

output {
  display: inline-block;
}

summary {
  display: list-item;
  cursor: pointer;
}

template {
  display: none;
}

[hidden] {
  display: none !important;
}

h1, h2, h3, h4, h5, h6,
.h1, .h2, .h3, .h4, .h5, .h6 {
  margin-bottom: 0.5rem;
  font-family: "Roboto", sans-serif, serif;
  font-weight: bold;
  line-height: 1.5;
  color: #002663;
}

h1, .h1 {
  font-size: 2.1875rem;
}

h2, .h2 {
  font-size: 1.5625rem;
}

h3, .h3 {
  font-size: 0.875rem;
}

h4, .h4 {
  font-size: 1.3125rem;
}

h5, .h5 {
  font-size: 1.09375rem;
}

h6, .h6 {
  font-size: 0.875rem;
}

.lead {
  font-size: 1.09375rem;
  font-weight: 300;
}

.display-1 {
  font-size: 2.1875rem;
  font-weight: 700;
  line-height: 1.5;
}

.display-2 {
  font-size: 1.5625rem;
  font-weight: 700;
  line-height: 1.5;
}

.display-3 {
  font-size: 0.875rem;
  font-weight: 700;
  line-height: 1.5;
}

.display-4 {
  font-size: 0.75rem;
  font-weight: 300;
  line-height: 1.5;
}

hr {
  margin-top: 1rem;
  margin-bottom: 1rem;
  border: 0;
  border-top: 1px solid #d8dce6;
}

small,
.small {
  font-size: 0.875em;
  font-weight: 400;
}

mark,
.mark {
  padding: 0.2em;
  background-color: #fcf8e3;
}

.list-unstyled, #ffFormCreate #ffForm div ul {
  padding-left: 0;
  list-style: none;
}

.list-inline {
  padding-left: 0;
  list-style: none;
}

.list-inline-item {
  display: inline-block;
}
.list-inline-item:not(:last-child) {
  margin-right: 0.5rem;
}

.initialism {
  font-size: 90%;
  text-transform: uppercase;
}

.blockquote {
  margin-bottom: 1rem;
  font-size: 1.09375rem;
}

.blockquote-footer {
  display: block;
  font-size: 0.875em;
  color: #6c757d;
}
.blockquote-footer::before {
  content: "\2014\A0";
}

.img-fluid, .img-responsive {
  max-width: 100%;
  height: auto;
}

.img-thumbnail {
  padding: 0.25rem;
  background-color: #fff;
  border: 1px solid #dee2e6;
  border-radius: 0.125rem;
  max-width: 100%;
  height: auto;
}

.figure {
  display: inline-block;
}

.figure-img {
  margin-bottom: 0.5rem;
  line-height: 1;
}

.figure-caption {
  font-size: 90%;
  color: #6c757d;
}

code {
  font-size: 87.5%;
  color: #e83e8c;
  word-wrap: break-word;
}
a > code {
  color: inherit;
}

kbd {
  padding: 0.2rem 0.4rem;
  font-size: 87.5%;
  color: #fff;
  background-color: #212529;
  border-radius: 0.2rem;
}
kbd kbd {
  padding: 0;
  font-size: 100%;
  font-weight: 700;
}

pre {
  display: block;
  font-size: 87.5%;
  color: #212529;
}
pre code {
  font-size: inherit;
  color: inherit;
  word-break: normal;
}

.pre-scrollable {
  max-height: 340px;
  overflow-y: scroll;
}

.container,
.container-fluid,
.container-xl,
.container-lg,
.container-md,
.container-sm {
  width: 100%;
  padding-right: 1.40625rem;
  padding-left: 1.40625rem;
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 600px) {
  .container-sm, .container {
    max-width: 100%;
  }
}
@media (min-width: 980px) {
  .container-md, .container-sm, .container {
    max-width: 95%;
  }
}
@media (min-width: 1200px) {
  .container-lg, .container-md, .container-sm, .container {
    max-width: 95%;
  }
}
@media (min-width: 1580px) {
  .container-xl, .container-lg, .container-md, .container-sm, .container {
    max-width: 1580px;
  }
}
.row, .learning .form-horizontal .form-group {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  flex-wrap: wrap;
  margin-right: -1.40625rem;
  margin-left: -1.40625rem;
}

.no-gutters {
  margin-right: 0;
  margin-left: 0;
}
.no-gutters > .col,
.no-gutters > [class*=col-] {
  padding-right: 0;
  padding-left: 0;
}

.col-xl,
.col-xl-auto, .col-xl-12, .col-xl-11, .col-xl-10, .col-xl-9, .col-xl-8, .col-xl-7, .col-xl-6, .col-xl-5, .col-xl-4, .col-xl-3, .col-xl-2, .col-xl-1, .col-lg,
.col-lg-auto, .col-lg-12, .col-lg-11, .col-lg-10, .col-lg-9, .col-lg-8, .col-lg-7, .col-lg-6, .col-lg-5, .col-lg-4, .col-lg-3, .col-lg-2, .col-lg-1, .col-md,
.col-md-auto, .col-md-12, .col-md-11, .col-md-10, .col-md-9, .col-md-8, .col-md-7, .col-md-6, .col-md-5, .col-md-4, .col-md-3, .col-md-2, .col-md-1, .col-sm,
.col-sm-auto, .col-sm-12, .col-sm-11, .col-sm-10, .col-sm-9, .col-sm-8, .col-sm-7, .col-sm-6, .col-sm-5, .col-sm-4, .col-sm-3, .col-sm-2, .col-sm-1, .col,
.col-auto, .col-12, .col-11, .col-10, .col-9, .col-8, .col-7, .col-6, .col-5, .col-4, .col-3, .col-2, .col-1 {
  position: relative;
  width: 100%;
  padding-right: 1.40625rem;
  padding-left: 1.40625rem;
}

.col {
  flex-basis: 0;
  flex-grow: 1;
  max-width: 100%;
}

.row-cols-1 > * {
  flex: 0 0 100%;
  max-width: 100%;
}

.row-cols-2 > * {
  flex: 0 0 50%;
  max-width: 50%;
}

.row-cols-3 > * {
  flex: 0 0 33.3333333333%;
  max-width: 33.3333333333%;
}

.row-cols-4 > * {
  flex: 0 0 25%;
  max-width: 25%;
}

.row-cols-5 > * {
  flex: 0 0 20%;
  max-width: 20%;
}

.row-cols-6 > * {
  flex: 0 0 16.6666666667%;
  max-width: 16.6666666667%;
}

.col-auto {
  flex: 0 0 auto;
  width: auto;
  max-width: 100%;
}

.col-1 {
  flex: 0 0 8.33333333%;
  max-width: 8.33333333%;
}

.col-2 {
  flex: 0 0 16.66666667%;
  max-width: 16.66666667%;
}

.col-3 {
  flex: 0 0 25%;
  max-width: 25%;
}

.col-4 {
  flex: 0 0 33.33333333%;
  max-width: 33.33333333%;
}

.col-5 {
  flex: 0 0 41.66666667%;
  max-width: 41.66666667%;
}

.col-6 {
  flex: 0 0 50%;
  max-width: 50%;
}

.col-7 {
  flex: 0 0 58.33333333%;
  max-width: 58.33333333%;
}

.col-8 {
  flex: 0 0 66.66666667%;
  max-width: 66.66666667%;
}

.col-9 {
  flex: 0 0 75%;
  max-width: 75%;
}

.col-10 {
  flex: 0 0 83.33333333%;
  max-width: 83.33333333%;
}

.col-11 {
  flex: 0 0 91.66666667%;
  max-width: 91.66666667%;
}

.col-12 {
  flex: 0 0 100%;
  max-width: 100%;
}

.order-first {
  order: -1;
}

.order-last {
  order: 13;
}

.order-0 {
  order: 0;
}

.order-1 {
  order: 1;
}

.order-2 {
  order: 2;
}

.order-3 {
  order: 3;
}

.order-4 {
  order: 4;
}

.order-5 {
  order: 5;
}

.order-6 {
  order: 6;
}

.order-7 {
  order: 7;
}

.order-8 {
  order: 8;
}

.order-9 {
  order: 9;
}

.order-10 {
  order: 10;
}

.order-11 {
  order: 11;
}

.order-12 {
  order: 12;
}

.offset-1 {
  margin-left: 8.33333333%;
}

.offset-2 {
  margin-left: 16.66666667%;
}

.offset-3 {
  margin-left: 25%;
}

.offset-4 {
  margin-left: 33.33333333%;
}

.offset-5 {
  margin-left: 41.66666667%;
}

.offset-6 {
  margin-left: 50%;
}

.offset-7 {
  margin-left: 58.33333333%;
}

.offset-8 {
  margin-left: 66.66666667%;
}

.offset-9 {
  margin-left: 75%;
}

.offset-10 {
  margin-left: 83.33333333%;
}

.offset-11 {
  margin-left: 91.66666667%;
}

@media (min-width: 600px) {
  .col-sm {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }

  .row-cols-sm-1 > * {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .row-cols-sm-2 > * {
    flex: 0 0 50%;
    max-width: 50%;
  }

  .row-cols-sm-3 > * {
    flex: 0 0 33.3333333333%;
    max-width: 33.3333333333%;
  }

  .row-cols-sm-4 > * {
    flex: 0 0 25%;
    max-width: 25%;
  }

  .row-cols-sm-5 > * {
    flex: 0 0 20%;
    max-width: 20%;
  }

  .row-cols-sm-6 > * {
    flex: 0 0 16.6666666667%;
    max-width: 16.6666666667%;
  }

  .col-sm-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }

  .col-sm-1 {
    flex: 0 0 8.33333333%;
    max-width: 8.33333333%;
  }

  .col-sm-2 {
    flex: 0 0 16.66666667%;
    max-width: 16.66666667%;
  }

  .col-sm-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }

  .col-sm-4 {
    flex: 0 0 33.33333333%;
    max-width: 33.33333333%;
  }

  .col-sm-5 {
    flex: 0 0 41.66666667%;
    max-width: 41.66666667%;
  }

  .col-sm-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }

  .col-sm-7 {
    flex: 0 0 58.33333333%;
    max-width: 58.33333333%;
  }

  .col-sm-8 {
    flex: 0 0 66.66666667%;
    max-width: 66.66666667%;
  }

  .col-sm-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }

  .col-sm-10 {
    flex: 0 0 83.33333333%;
    max-width: 83.33333333%;
  }

  .col-sm-11 {
    flex: 0 0 91.66666667%;
    max-width: 91.66666667%;
  }

  .col-sm-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .order-sm-first {
    order: -1;
  }

  .order-sm-last {
    order: 13;
  }

  .order-sm-0 {
    order: 0;
  }

  .order-sm-1 {
    order: 1;
  }

  .order-sm-2 {
    order: 2;
  }

  .order-sm-3 {
    order: 3;
  }

  .order-sm-4 {
    order: 4;
  }

  .order-sm-5 {
    order: 5;
  }

  .order-sm-6 {
    order: 6;
  }

  .order-sm-7 {
    order: 7;
  }

  .order-sm-8 {
    order: 8;
  }

  .order-sm-9 {
    order: 9;
  }

  .order-sm-10 {
    order: 10;
  }

  .order-sm-11 {
    order: 11;
  }

  .order-sm-12 {
    order: 12;
  }

  .offset-sm-0 {
    margin-left: 0;
  }

  .offset-sm-1 {
    margin-left: 8.33333333%;
  }

  .offset-sm-2 {
    margin-left: 16.66666667%;
  }

  .offset-sm-3 {
    margin-left: 25%;
  }

  .offset-sm-4 {
    margin-left: 33.33333333%;
  }

  .offset-sm-5 {
    margin-left: 41.66666667%;
  }

  .offset-sm-6 {
    margin-left: 50%;
  }

  .offset-sm-7 {
    margin-left: 58.33333333%;
  }

  .offset-sm-8 {
    margin-left: 66.66666667%;
  }

  .offset-sm-9 {
    margin-left: 75%;
  }

  .offset-sm-10 {
    margin-left: 83.33333333%;
  }

  .offset-sm-11 {
    margin-left: 91.66666667%;
  }
}
@media (min-width: 980px) {
  .col-md {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }

  .row-cols-md-1 > * {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .row-cols-md-2 > * {
    flex: 0 0 50%;
    max-width: 50%;
  }

  .row-cols-md-3 > * {
    flex: 0 0 33.3333333333%;
    max-width: 33.3333333333%;
  }

  .row-cols-md-4 > * {
    flex: 0 0 25%;
    max-width: 25%;
  }

  .row-cols-md-5 > * {
    flex: 0 0 20%;
    max-width: 20%;
  }

  .row-cols-md-6 > * {
    flex: 0 0 16.6666666667%;
    max-width: 16.6666666667%;
  }

  .col-md-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }

  .col-md-1 {
    flex: 0 0 8.33333333%;
    max-width: 8.33333333%;
  }

  .col-md-2 {
    flex: 0 0 16.66666667%;
    max-width: 16.66666667%;
  }

  .col-md-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }

  .col-md-4 {
    flex: 0 0 33.33333333%;
    max-width: 33.33333333%;
  }

  .col-md-5 {
    flex: 0 0 41.66666667%;
    max-width: 41.66666667%;
  }

  .col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }

  .col-md-7 {
    flex: 0 0 58.33333333%;
    max-width: 58.33333333%;
  }

  .col-md-8 {
    flex: 0 0 66.66666667%;
    max-width: 66.66666667%;
  }

  .col-md-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }

  .col-md-10 {
    flex: 0 0 83.33333333%;
    max-width: 83.33333333%;
  }

  .col-md-11 {
    flex: 0 0 91.66666667%;
    max-width: 91.66666667%;
  }

  .col-md-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .order-md-first {
    order: -1;
  }

  .order-md-last {
    order: 13;
  }

  .order-md-0 {
    order: 0;
  }

  .order-md-1 {
    order: 1;
  }

  .order-md-2 {
    order: 2;
  }

  .order-md-3 {
    order: 3;
  }

  .order-md-4 {
    order: 4;
  }

  .order-md-5 {
    order: 5;
  }

  .order-md-6 {
    order: 6;
  }

  .order-md-7 {
    order: 7;
  }

  .order-md-8 {
    order: 8;
  }

  .order-md-9 {
    order: 9;
  }

  .order-md-10 {
    order: 10;
  }

  .order-md-11 {
    order: 11;
  }

  .order-md-12 {
    order: 12;
  }

  .offset-md-0 {
    margin-left: 0;
  }

  .offset-md-1 {
    margin-left: 8.33333333%;
  }

  .offset-md-2 {
    margin-left: 16.66666667%;
  }

  .offset-md-3 {
    margin-left: 25%;
  }

  .offset-md-4 {
    margin-left: 33.33333333%;
  }

  .offset-md-5 {
    margin-left: 41.66666667%;
  }

  .offset-md-6 {
    margin-left: 50%;
  }

  .offset-md-7 {
    margin-left: 58.33333333%;
  }

  .offset-md-8 {
    margin-left: 66.66666667%;
  }

  .offset-md-9 {
    margin-left: 75%;
  }

  .offset-md-10 {
    margin-left: 83.33333333%;
  }

  .offset-md-11 {
    margin-left: 91.66666667%;
  }
}
@media (min-width: 1200px) {
  .col-lg {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }

  .row-cols-lg-1 > * {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .row-cols-lg-2 > * {
    flex: 0 0 50%;
    max-width: 50%;
  }

  .row-cols-lg-3 > * {
    flex: 0 0 33.3333333333%;
    max-width: 33.3333333333%;
  }

  .row-cols-lg-4 > * {
    flex: 0 0 25%;
    max-width: 25%;
  }

  .row-cols-lg-5 > * {
    flex: 0 0 20%;
    max-width: 20%;
  }

  .row-cols-lg-6 > * {
    flex: 0 0 16.6666666667%;
    max-width: 16.6666666667%;
  }

  .col-lg-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }

  .col-lg-1 {
    flex: 0 0 8.33333333%;
    max-width: 8.33333333%;
  }

  .col-lg-2 {
    flex: 0 0 16.66666667%;
    max-width: 16.66666667%;
  }

  .col-lg-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }

  .col-lg-4 {
    flex: 0 0 33.33333333%;
    max-width: 33.33333333%;
  }

  .col-lg-5 {
    flex: 0 0 41.66666667%;
    max-width: 41.66666667%;
  }

  .col-lg-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }

  .col-lg-7 {
    flex: 0 0 58.33333333%;
    max-width: 58.33333333%;
  }

  .col-lg-8 {
    flex: 0 0 66.66666667%;
    max-width: 66.66666667%;
  }

  .col-lg-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }

  .col-lg-10 {
    flex: 0 0 83.33333333%;
    max-width: 83.33333333%;
  }

  .col-lg-11 {
    flex: 0 0 91.66666667%;
    max-width: 91.66666667%;
  }

  .col-lg-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .order-lg-first {
    order: -1;
  }

  .order-lg-last {
    order: 13;
  }

  .order-lg-0 {
    order: 0;
  }

  .order-lg-1 {
    order: 1;
  }

  .order-lg-2 {
    order: 2;
  }

  .order-lg-3 {
    order: 3;
  }

  .order-lg-4 {
    order: 4;
  }

  .order-lg-5 {
    order: 5;
  }

  .order-lg-6 {
    order: 6;
  }

  .order-lg-7 {
    order: 7;
  }

  .order-lg-8 {
    order: 8;
  }

  .order-lg-9 {
    order: 9;
  }

  .order-lg-10 {
    order: 10;
  }

  .order-lg-11 {
    order: 11;
  }

  .order-lg-12 {
    order: 12;
  }

  .offset-lg-0 {
    margin-left: 0;
  }

  .offset-lg-1 {
    margin-left: 8.33333333%;
  }

  .offset-lg-2 {
    margin-left: 16.66666667%;
  }

  .offset-lg-3 {
    margin-left: 25%;
  }

  .offset-lg-4 {
    margin-left: 33.33333333%;
  }

  .offset-lg-5 {
    margin-left: 41.66666667%;
  }

  .offset-lg-6 {
    margin-left: 50%;
  }

  .offset-lg-7 {
    margin-left: 58.33333333%;
  }

  .offset-lg-8 {
    margin-left: 66.66666667%;
  }

  .offset-lg-9 {
    margin-left: 75%;
  }

  .offset-lg-10 {
    margin-left: 83.33333333%;
  }

  .offset-lg-11 {
    margin-left: 91.66666667%;
  }
}
@media (min-width: 1580px) {
  .col-xl {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }

  .row-cols-xl-1 > * {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .row-cols-xl-2 > * {
    flex: 0 0 50%;
    max-width: 50%;
  }

  .row-cols-xl-3 > * {
    flex: 0 0 33.3333333333%;
    max-width: 33.3333333333%;
  }

  .row-cols-xl-4 > * {
    flex: 0 0 25%;
    max-width: 25%;
  }

  .row-cols-xl-5 > * {
    flex: 0 0 20%;
    max-width: 20%;
  }

  .row-cols-xl-6 > * {
    flex: 0 0 16.6666666667%;
    max-width: 16.6666666667%;
  }

  .col-xl-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }

  .col-xl-1 {
    flex: 0 0 8.33333333%;
    max-width: 8.33333333%;
  }

  .col-xl-2 {
    flex: 0 0 16.66666667%;
    max-width: 16.66666667%;
  }

  .col-xl-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }

  .col-xl-4 {
    flex: 0 0 33.33333333%;
    max-width: 33.33333333%;
  }

  .col-xl-5 {
    flex: 0 0 41.66666667%;
    max-width: 41.66666667%;
  }

  .col-xl-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }

  .col-xl-7 {
    flex: 0 0 58.33333333%;
    max-width: 58.33333333%;
  }

  .col-xl-8 {
    flex: 0 0 66.66666667%;
    max-width: 66.66666667%;
  }

  .col-xl-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }

  .col-xl-10 {
    flex: 0 0 83.33333333%;
    max-width: 83.33333333%;
  }

  .col-xl-11 {
    flex: 0 0 91.66666667%;
    max-width: 91.66666667%;
  }

  .col-xl-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .order-xl-first {
    order: -1;
  }

  .order-xl-last {
    order: 13;
  }

  .order-xl-0 {
    order: 0;
  }

  .order-xl-1 {
    order: 1;
  }

  .order-xl-2 {
    order: 2;
  }

  .order-xl-3 {
    order: 3;
  }

  .order-xl-4 {
    order: 4;
  }

  .order-xl-5 {
    order: 5;
  }

  .order-xl-6 {
    order: 6;
  }

  .order-xl-7 {
    order: 7;
  }

  .order-xl-8 {
    order: 8;
  }

  .order-xl-9 {
    order: 9;
  }

  .order-xl-10 {
    order: 10;
  }

  .order-xl-11 {
    order: 11;
  }

  .order-xl-12 {
    order: 12;
  }

  .offset-xl-0 {
    margin-left: 0;
  }

  .offset-xl-1 {
    margin-left: 8.33333333%;
  }

  .offset-xl-2 {
    margin-left: 16.66666667%;
  }

  .offset-xl-3 {
    margin-left: 25%;
  }

  .offset-xl-4 {
    margin-left: 33.33333333%;
  }

  .offset-xl-5 {
    margin-left: 41.66666667%;
  }

  .offset-xl-6 {
    margin-left: 50%;
  }

  .offset-xl-7 {
    margin-left: 58.33333333%;
  }

  .offset-xl-8 {
    margin-left: 66.66666667%;
  }

  .offset-xl-9 {
    margin-left: 75%;
  }

  .offset-xl-10 {
    margin-left: 83.33333333%;
  }

  .offset-xl-11 {
    margin-left: 91.66666667%;
  }
}
.table {
  width: 100%;
  margin-bottom: 1rem;
  color: #5e6983;
  background-color: transparent;
}
.table th,
.table td {
  padding: 0.75rem;
  vertical-align: top;
  border-top: 1px solid #d8dce6;
}
.table thead th {
  vertical-align: bottom;
  border-bottom: 2px solid #d8dce6;
}
.table tbody + tbody {
  border-top: 2px solid #d8dce6;
}

.table-sm th,
.table-sm td {
  padding: 0.3rem;
}

.table-bordered {
  border: 1px solid #d8dce6;
}
.table-bordered th,
.table-bordered td {
  border: 1px solid #d8dce6;
}
.table-bordered thead th,
.table-bordered thead td {
  border-bottom-width: 2px;
}

.table-borderless th,
.table-borderless td,
.table-borderless thead th,
.table-borderless tbody + tbody {
  border: 0;
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: #fafafc;
}

.table-hover tbody tr:hover {
  color: #5e6983;
  background-color: rgba(0, 0, 0, 0.075);
}

.table-primary,
.table-primary > th,
.table-primary > td {
  background-color: #c6daeb;
}
.table-primary th,
.table-primary td,
.table-primary thead th,
.table-primary tbody + tbody {
  border-color: #95bada;
}

.table-hover .table-primary:hover {
  background-color: #b3cee4;
}
.table-hover .table-primary:hover > td,
.table-hover .table-primary:hover > th {
  background-color: #b3cee4;
}

.table-secondary,
.table-secondary > th,
.table-secondary > td {
  background-color: #b8c2d3;
}
.table-secondary th,
.table-secondary td,
.table-secondary thead th,
.table-secondary tbody + tbody {
  border-color: #7a8eae;
}

.table-hover .table-secondary:hover {
  background-color: #a8b4c9;
}
.table-hover .table-secondary:hover > td,
.table-hover .table-secondary:hover > th {
  background-color: #a8b4c9;
}

.table-success,
.table-success > th,
.table-success > td {
  background-color: #d4f2e0;
}
.table-success th,
.table-success td,
.table-success thead th,
.table-success tbody + tbody {
  border-color: #aee7c6;
}

.table-hover .table-success:hover {
  background-color: #c0ecd2;
}
.table-hover .table-success:hover > td,
.table-hover .table-success:hover > th {
  background-color: #c0ecd2;
}

.table-info,
.table-info > th,
.table-info > td {
  background-color: #c7dde1;
}
.table-info th,
.table-info td,
.table-info thead th,
.table-info tbody + tbody {
  border-color: #98c0c8;
}

.table-hover .table-info:hover {
  background-color: #b6d3d8;
}
.table-hover .table-info:hover > td,
.table-hover .table-info:hover > th {
  background-color: #b6d3d8;
}

.table-warning,
.table-warning > th,
.table-warning > td {
  background-color: #f5e4d1;
}
.table-warning th,
.table-warning td,
.table-warning thead th,
.table-warning tbody + tbody {
  border-color: #eccdaa;
}

.table-hover .table-warning:hover {
  background-color: #f0d8bc;
}
.table-hover .table-warning:hover > td,
.table-hover .table-warning:hover > th {
  background-color: #f0d8bc;
}

.table-danger,
.table-danger > th,
.table-danger > td {
  background-color: #f1d4d4;
}
.table-danger th,
.table-danger td,
.table-danger thead th,
.table-danger tbody + tbody {
  border-color: #e4afaf;
}

.table-hover .table-danger:hover {
  background-color: #ebc1c1;
}
.table-hover .table-danger:hover > td,
.table-hover .table-danger:hover > th {
  background-color: #ebc1c1;
}

.table-light,
.table-light > th,
.table-light > td {
  background-color: #f4f5f8;
}
.table-light th,
.table-light td,
.table-light thead th,
.table-light tbody + tbody {
  border-color: #ebedf2;
}

.table-hover .table-light:hover {
  background-color: #e4e7ee;
}
.table-hover .table-light:hover > td,
.table-hover .table-light:hover > th {
  background-color: #e4e7ee;
}

.table-dark,
.table-dark > th,
.table-dark > td {
  background-color: #d2d5dc;
}
.table-dark th,
.table-dark td,
.table-dark thead th,
.table-dark tbody + tbody {
  border-color: #abb1bf;
}

.table-hover .table-dark:hover {
  background-color: #c4c8d1;
}
.table-hover .table-dark:hover > td,
.table-hover .table-dark:hover > th {
  background-color: #c4c8d1;
}

.table-lighter,
.table-lighter > th,
.table-lighter > td {
  background-color: #fefefe;
}
.table-lighter th,
.table-lighter td,
.table-lighter thead th,
.table-lighter tbody + tbody {
  border-color: #fcfcfd;
}

.table-hover .table-lighter:hover {
  background-color: #f1f1f1;
}
.table-hover .table-lighter:hover > td,
.table-hover .table-lighter:hover > th {
  background-color: #f1f1f1;
}

.table-lighter-blue,
.table-lighter-blue > th,
.table-lighter-blue > td {
  background-color: #fcfdfe;
}
.table-lighter-blue th,
.table-lighter-blue td,
.table-lighter-blue thead th,
.table-lighter-blue tbody + tbody {
  border-color: #f9fbfd;
}

.table-hover .table-lighter-blue:hover {
  background-color: #e9f0f8;
}
.table-hover .table-lighter-blue:hover > td,
.table-hover .table-lighter-blue:hover > th {
  background-color: #e9f0f8;
}

.table-active,
.table-active > th,
.table-active > td {
  background-color: rgba(0, 0, 0, 0.075);
}

.table-hover .table-active:hover {
  background-color: rgba(0, 0, 0, 0.075);
}
.table-hover .table-active:hover > td,
.table-hover .table-active:hover > th {
  background-color: rgba(0, 0, 0, 0.075);
}

.table .thead-dark th {
  color: #fff;
  background-color: #343a40;
  border-color: #454d55;
}
.table .thead-light th {
  color: #495057;
  background-color: #e9ecef;
  border-color: #d8dce6;
}

.table-dark {
  color: #fff;
  background-color: #343a40;
}
.table-dark th,
.table-dark td,
.table-dark thead th {
  border-color: #454d55;
}
.table-dark.table-bordered {
  border: 0;
}
.table-dark.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(255, 255, 255, 0.05);
}
.table-dark.table-hover tbody tr:hover {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.075);
}

@media (max-width: 599.98px) {
  .table-responsive-sm {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  .table-responsive-sm > .table-bordered {
    border: 0;
  }
}
@media (max-width: 979.98px) {
  .table-responsive-md {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  .table-responsive-md > .table-bordered {
    border: 0;
  }
}
@media (max-width: 1199.98px) {
  .table-responsive-lg {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  .table-responsive-lg > .table-bordered {
    border: 0;
  }
}
@media (max-width: 1579.98px) {
  .table-responsive-xl {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  .table-responsive-xl > .table-bordered {
    border: 0;
  }
}
.table-responsive {
  display: block;
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}
.table-responsive > .table-bordered {
  border: 0;
}

.form-control {
  display: block;
  width: 100%;
  height: -webkit-calc(1.3125em + 1.375rem + 2px);
  height: calc(1.3125em + 1.375rem + 2px);
  padding: 0.6875rem 0.9375rem;
  font-family: "Roboto", sans-serif, serif;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.3125;
  color: #495057;
  background-color: #fafafc;
  background-clip: padding-box;
  border: 1px solid #d8dce6;
  border-radius: 0.125rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-control {
    transition: none;
  }
}
.form-control::-ms-expand {
  background-color: transparent;
  border: 0;
}
.form-control:focus {
  color: #002663;
  background-color: #fff;
  border-color: #2e6da4;
  outline: 0;
  box-shadow: none !important;
}
.form-control::-moz-placeholder {
  color: rgba(94, 105, 131, 0.6);
  opacity: 1;
}
.form-control::placeholder {
  color: rgba(94, 105, 131, 0.6);
  opacity: 1;
}
.form-control:disabled, .form-control[readonly] {
  background-color: #e9ecef;
  opacity: 1;
}

input[type=date].form-control,
input[type=time].form-control,
input[type=datetime-local].form-control,
input[type=month].form-control {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}

select.form-control:-moz-focusring {
  color: transparent;
  text-shadow: 0 0 0 #495057;
}
select.form-control:focus::-ms-value {
  color: #495057;
  background-color: #fafafc;
}

.form-control-file,
.form-control-range {
  display: block;
  width: 100%;
}

.col-form-label {
  padding-top: -webkit-calc(0.6875rem + 1px);
  padding-top: calc(0.6875rem + 1px);
  padding-bottom: -webkit-calc(0.6875rem + 1px);
  padding-bottom: calc(0.6875rem + 1px);
  margin-bottom: 0;
  font-size: inherit;
  line-height: 1.3125;
}

.col-form-label-lg {
  padding-top: -webkit-calc(0.5rem + 1px);
  padding-top: calc(0.5rem + 1px);
  padding-bottom: -webkit-calc(0.5rem + 1px);
  padding-bottom: calc(0.5rem + 1px);
  font-size: 1.09375rem;
  line-height: 1.5;
}

.col-form-label-sm {
  padding-top: -webkit-calc(0.25rem + 1px);
  padding-top: calc(0.25rem + 1px);
  padding-bottom: -webkit-calc(0.25rem + 1px);
  padding-bottom: calc(0.25rem + 1px);
  font-size: 0.765625rem;
  line-height: 1.5;
}

.form-control-plaintext {
  display: block;
  width: 100%;
  padding: 0.6875rem 0;
  margin-bottom: 0;
  font-size: 0.875rem;
  line-height: 1.3125;
  color: #5e6983;
  background-color: transparent;
  border: solid transparent;
  border-width: 1px 0;
}
.form-control-plaintext.form-control-sm, .form-control-plaintext.form-control-lg {
  padding-right: 0;
  padding-left: 0;
}

.form-control-sm {
  height: -webkit-calc(1.5em + 0.5rem + 2px);
  height: calc(1.5em + 0.5rem + 2px);
  padding: 0.25rem 0.5rem;
  font-size: 0.765625rem;
  line-height: 1.5;
  border-radius: 0.2rem;
}

.form-control-lg {
  height: -webkit-calc(1.5em + 1rem + 2px);
  height: calc(1.5em + 1rem + 2px);
  padding: 0.5rem 1rem;
  font-size: 1.09375rem;
  line-height: 1.5;
  border-radius: 0.3rem;
}

select.form-control[size], select.form-control[multiple] {
  height: auto;
}

textarea.form-control {
  height: auto;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-text {
  display: block;
  margin-top: 0.25rem;
}

.form-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  flex-wrap: wrap;
  margin-right: -5px;
  margin-left: -5px;
}
.form-row > .col,
.form-row > [class*=col-] {
  padding-right: 5px;
  padding-left: 5px;
}

.form-check {
  position: relative;
  display: block;
  padding-left: 1.25rem;
}

.form-check-input {
  position: absolute;
  margin-top: 0.3rem;
  margin-left: -1.25rem;
}
.form-check-input[disabled] ~ .form-check-label, .form-check-input:disabled ~ .form-check-label {
  color: #6c757d;
}

.form-check-label {
  margin-bottom: 0;
}

.form-check-inline {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  align-items: center;
  padding-left: 0;
  margin-right: 0.75rem;
}
.form-check-inline .form-check-input {
  position: static;
  margin-top: 0;
  margin-right: 0.3125rem;
  margin-left: 0;
}

.valid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: #64d091;
}

.valid-tooltip {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  margin-top: 0.1rem;
  font-size: 0.765625rem;
  line-height: 1.3125;
  color: #212529;
  background-color: rgba(100, 208, 145, 0.9);
  border-radius: 0.125rem;
}
.form-row > .col > .valid-tooltip, .form-row > [class*=col-] > .valid-tooltip {
  left: 5px;
}

.was-validated :valid ~ .valid-feedback,
.was-validated :valid ~ .valid-tooltip,
.is-valid ~ .valid-feedback,
.is-valid ~ .valid-tooltip {
  display: block;
}

.was-validated .form-control:valid, .form-control.is-valid {
  border-color: #64d091;
  padding-right: -webkit-calc(1.3125em + 1.375rem) !important;
  padding-right: calc(1.3125em + 1.375rem) !important;
  background-image: none;
  background-repeat: no-repeat;
  background-position: right -webkit-calc(0.328125em + 0.34375rem) center;
  background-position: right calc(0.328125em + 0.34375rem) center;
  background-size: -webkit-calc(0.65625em + 0.6875rem) -webkit-calc(0.65625em + 0.6875rem);
  background-size: calc(0.65625em + 0.6875rem) calc(0.65625em + 0.6875rem);
}
.was-validated .form-control:valid:focus, .form-control.is-valid:focus {
  border-color: #64d091;
  box-shadow: 0 0 0 0.2rem rgba(100, 208, 145, 0.25);
}

.was-validated select.form-control:valid, select.form-control.is-valid {
  padding-right: 3.75rem !important;
  background-position: right 1.875rem center;
}

.was-validated textarea.form-control:valid, textarea.form-control.is-valid {
  padding-right: -webkit-calc(1.3125em + 1.375rem);
  padding-right: calc(1.3125em + 1.375rem);
  background-position: top -webkit-calc(0.328125em + 0.34375rem) right -webkit-calc(0.328125em + 0.34375rem);
  background-position: top calc(0.328125em + 0.34375rem) right calc(0.328125em + 0.34375rem);
}

.was-validated .custom-select:valid, .custom-select.is-valid {
  border-color: #64d091;
  padding-right: -webkit-calc(0.75em + 2.96875rem) !important;
  padding-right: calc(0.75em + 2.96875rem) !important;
  background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='5' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") right 0.9375rem center/8px 10px no-repeat, #fafafc none center right 1.9375rem/calc(0.65625em + 0.6875rem) -webkit-calc(0.65625em + 0.6875rem) no-repeat;
  background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='5' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") right 0.9375rem center/8px 10px no-repeat, #fafafc none center right 1.9375rem/calc(0.65625em + 0.6875rem) calc(0.65625em + 0.6875rem) no-repeat;
}
.was-validated .custom-select:valid:focus, .custom-select.is-valid:focus {
  border-color: #64d091;
  box-shadow: 0 0 0 0.2rem rgba(100, 208, 145, 0.25);
}

.was-validated .form-check-input:valid ~ .form-check-label, .form-check-input.is-valid ~ .form-check-label {
  color: #64d091;
}
.was-validated .form-check-input:valid ~ .valid-feedback,
.was-validated .form-check-input:valid ~ .valid-tooltip, .form-check-input.is-valid ~ .valid-feedback,
.form-check-input.is-valid ~ .valid-tooltip {
  display: block;
}

.was-validated .custom-control-input:valid ~ .custom-control-label, .custom-control-input.is-valid ~ .custom-control-label {
  color: #64d091;
}
.was-validated .custom-control-input:valid ~ .custom-control-label::before, .custom-control-input.is-valid ~ .custom-control-label::before {
  border-color: #64d091;
}
.was-validated .custom-control-input:valid:checked ~ .custom-control-label::before, .custom-control-input.is-valid:checked ~ .custom-control-label::before {
  border-color: #8bdcad;
  background-color: #8bdcad;
}
.was-validated .custom-control-input:valid:focus ~ .custom-control-label::before, .custom-control-input.is-valid:focus ~ .custom-control-label::before {
  box-shadow: 0 0 0 0.2rem rgba(100, 208, 145, 0.25);
}
.was-validated .custom-control-input:valid:focus:not(:checked) ~ .custom-control-label::before, .custom-control-input.is-valid:focus:not(:checked) ~ .custom-control-label::before {
  border-color: #64d091;
}

.was-validated .custom-file-input:valid ~ .custom-file-label, .custom-file-input.is-valid ~ .custom-file-label {
  border-color: #64d091;
}
.was-validated .custom-file-input:valid:focus ~ .custom-file-label, .custom-file-input.is-valid:focus ~ .custom-file-label {
  border-color: #64d091;
  box-shadow: 0 0 0 0.2rem rgba(100, 208, 145, 0.25);
}

.invalid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: #cc6565;
}

.invalid-tooltip {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  margin-top: 0.1rem;
  font-size: 0.765625rem;
  line-height: 1.3125;
  color: #fff;
  background-color: rgba(204, 101, 101, 0.9);
  border-radius: 0.125rem;
}
.form-row > .col > .invalid-tooltip, .form-row > [class*=col-] > .invalid-tooltip {
  left: 5px;
}

.was-validated :invalid ~ .invalid-feedback,
.was-validated :invalid ~ .invalid-tooltip,
.is-invalid ~ .invalid-feedback,
.is-invalid ~ .invalid-tooltip {
  display: block;
}

.was-validated .form-control:invalid, .form-control.is-invalid {
  border-color: #cc6565;
  padding-right: -webkit-calc(1.3125em + 1.375rem) !important;
  padding-right: calc(1.3125em + 1.375rem) !important;
  background-image: none;
  background-repeat: no-repeat;
  background-position: right -webkit-calc(0.328125em + 0.34375rem) center;
  background-position: right calc(0.328125em + 0.34375rem) center;
  background-size: -webkit-calc(0.65625em + 0.6875rem) -webkit-calc(0.65625em + 0.6875rem);
  background-size: calc(0.65625em + 0.6875rem) calc(0.65625em + 0.6875rem);
}
.was-validated .form-control:invalid:focus, .form-control.is-invalid:focus {
  border-color: #cc6565;
  box-shadow: 0 0 0 0.2rem rgba(204, 101, 101, 0.25);
}

.was-validated select.form-control:invalid, select.form-control.is-invalid {
  padding-right: 3.75rem !important;
  background-position: right 1.875rem center;
}

.was-validated textarea.form-control:invalid, textarea.form-control.is-invalid {
  padding-right: -webkit-calc(1.3125em + 1.375rem);
  padding-right: calc(1.3125em + 1.375rem);
  background-position: top -webkit-calc(0.328125em + 0.34375rem) right -webkit-calc(0.328125em + 0.34375rem);
  background-position: top calc(0.328125em + 0.34375rem) right calc(0.328125em + 0.34375rem);
}

.was-validated .custom-select:invalid, .custom-select.is-invalid {
  border-color: #cc6565;
  padding-right: -webkit-calc(0.75em + 2.96875rem) !important;
  padding-right: calc(0.75em + 2.96875rem) !important;
  background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='5' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") right 0.9375rem center/8px 10px no-repeat, #fafafc none center right 1.9375rem/calc(0.65625em + 0.6875rem) -webkit-calc(0.65625em + 0.6875rem) no-repeat;
  background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='5' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") right 0.9375rem center/8px 10px no-repeat, #fafafc none center right 1.9375rem/calc(0.65625em + 0.6875rem) calc(0.65625em + 0.6875rem) no-repeat;
}
.was-validated .custom-select:invalid:focus, .custom-select.is-invalid:focus {
  border-color: #cc6565;
  box-shadow: 0 0 0 0.2rem rgba(204, 101, 101, 0.25);
}

.was-validated .form-check-input:invalid ~ .form-check-label, .form-check-input.is-invalid ~ .form-check-label {
  color: #cc6565;
}
.was-validated .form-check-input:invalid ~ .invalid-feedback,
.was-validated .form-check-input:invalid ~ .invalid-tooltip, .form-check-input.is-invalid ~ .invalid-feedback,
.form-check-input.is-invalid ~ .invalid-tooltip {
  display: block;
}

.was-validated .custom-control-input:invalid ~ .custom-control-label, .custom-control-input.is-invalid ~ .custom-control-label {
  color: #cc6565;
}
.was-validated .custom-control-input:invalid ~ .custom-control-label::before, .custom-control-input.is-invalid ~ .custom-control-label::before {
  border-color: #cc6565;
}
.was-validated .custom-control-input:invalid:checked ~ .custom-control-label::before, .custom-control-input.is-invalid:checked ~ .custom-control-label::before {
  border-color: #d98b8b;
  background-color: #d98b8b;
}
.was-validated .custom-control-input:invalid:focus ~ .custom-control-label::before, .custom-control-input.is-invalid:focus ~ .custom-control-label::before {
  box-shadow: 0 0 0 0.2rem rgba(204, 101, 101, 0.25);
}
.was-validated .custom-control-input:invalid:focus:not(:checked) ~ .custom-control-label::before, .custom-control-input.is-invalid:focus:not(:checked) ~ .custom-control-label::before {
  border-color: #cc6565;
}

.was-validated .custom-file-input:invalid ~ .custom-file-label, .custom-file-input.is-invalid ~ .custom-file-label {
  border-color: #cc6565;
}
.was-validated .custom-file-input:invalid:focus ~ .custom-file-label, .custom-file-input.is-invalid:focus ~ .custom-file-label {
  border-color: #cc6565;
  box-shadow: 0 0 0 0.2rem rgba(204, 101, 101, 0.25);
}

.form-inline {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  flex-flow: row wrap;
  align-items: center;
}
.form-inline .form-check {
  width: 100%;
}
@media (min-width: 600px) {
  .form-inline label {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0;
  }
  .form-inline .form-group {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    flex: 0 0 auto;
    flex-flow: row wrap;
    align-items: center;
    margin-bottom: 0;
  }
  .form-inline .form-control {
    display: inline-block;
    width: auto;
    vertical-align: middle;
  }
  .form-inline .form-control-plaintext {
    display: inline-block;
  }
  .form-inline .input-group,
.form-inline .custom-select {
    width: auto;
  }
  .form-inline .form-check {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    align-items: center;
    justify-content: center;
    width: auto;
    padding-left: 0;
  }
  .form-inline .form-check-input {
    position: relative;
    flex-shrink: 0;
    margin-top: 0;
    margin-right: 0.25rem;
    margin-left: 0;
  }
  .form-inline .custom-control {
    align-items: center;
    justify-content: center;
  }
  .form-inline .custom-control-label {
    margin-bottom: 0;
  }
}

.btn, #ffFormCreate #ffForm div ul li a {
  display: inline-block;
  font-weight: 500;
  color: #5e6983;
  text-align: center;
  vertical-align: middle;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  background-color: transparent;
  border: 1px solid transparent;
  padding: 14px 25px;
  font-size: 0.75rem;
  line-height: 1.3125;
  border-radius: 0.125rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .btn, #ffFormCreate #ffForm div ul li a {
    transition: none;
  }
}
.btn:hover, #ffFormCreate #ffForm div ul li a:hover {
  color: #5e6983;
  text-decoration: none;
}
.btn:focus, #ffFormCreate #ffForm div ul li a:focus, .btn.focus, #ffFormCreate #ffForm div ul li a.focus {
  outline: 0;
  box-shadow: none !important;
}
.btn.disabled, #ffFormCreate #ffForm div ul li a.disabled, .btn:disabled, #ffFormCreate #ffForm div ul li a:disabled {
  opacity: 1;
}
.btn:not(:disabled):not(.disabled), #ffFormCreate #ffForm div ul li a:not(:disabled):not(.disabled) {
  cursor: pointer;
}
a.btn.disabled, #ffFormCreate #ffForm div ul li a.disabled,
fieldset:disabled a.btn,
fieldset:disabled #ffFormCreate #ffForm div ul li a,
#ffFormCreate #ffForm div ul li fieldset:disabled a {
  pointer-events: none;
}

.btn-primary, .page--lms .btn-primary, #ffgenerated button.btn-default {
  color: #fff;
  background-color: #337ab7;
  border-color: #337ab7;
}
.btn-primary:hover, #ffgenerated button.btn-default:hover {
  color: #fff;
  background-color: #2b6699;
  border-color: #285f8f;
}
.btn-primary:focus, #ffgenerated button.btn-default:focus, .btn-primary.focus, #ffgenerated button.focus.btn-default {
  color: #fff;
  background-color: #2b6699;
  border-color: #285f8f;
  box-shadow: 0 0 0 0.2rem rgba(82, 142, 194, 0.5);
}
.btn-primary.disabled, #ffgenerated button.disabled.btn-default, .btn-primary:disabled, #ffgenerated button.btn-default:disabled {
  color: #fff;
  background-color: #337ab7;
  border-color: #337ab7;
}
.btn-primary:not(:disabled):not(.disabled):active, #ffgenerated button.btn-default:not(:disabled):not(.disabled):active, .btn-primary:not(:disabled):not(.disabled).active, #ffgenerated button.btn-default:not(:disabled):not(.disabled).active, .show > .btn-primary.dropdown-toggle, #ffgenerated .page--lms .show > button.dropdown-toggle.btn-default, #ffgenerated .show > button.dropdown-toggle.btn-default {
  color: #fff;
  background-color: #285f8f;
  border-color: #255985;
}
.btn-primary:not(:disabled):not(.disabled):active:focus, #ffgenerated button.btn-default:not(:disabled):not(.disabled):active:focus, .btn-primary:not(:disabled):not(.disabled).active:focus, #ffgenerated button.btn-default:not(:disabled):not(.disabled).active:focus, .show > .btn-primary.dropdown-toggle:focus, #ffgenerated .page--lms .show > button.dropdown-toggle.btn-default:focus, #ffgenerated .show > button.dropdown-toggle.btn-default:focus {
  box-shadow: 0 0 0 0.2rem rgba(82, 142, 194, 0.5);
}

.btn-secondary {
  color: #fff;
  background-color: #002663;
  border-color: #002663;
}
.btn-secondary:hover {
  color: #fff;
  background-color: #00173d;
  border-color: #001230;
}
.btn-secondary:focus, .btn-secondary.focus {
  color: #fff;
  background-color: #00173d;
  border-color: #001230;
  box-shadow: 0 0 0 0.2rem rgba(38, 71, 122, 0.5);
}
.btn-secondary.disabled, .btn-secondary:disabled {
  color: #fff;
  background-color: #002663;
  border-color: #002663;
}
.btn-secondary:not(:disabled):not(.disabled):active, .btn-secondary:not(:disabled):not(.disabled).active, .show > .btn-secondary.dropdown-toggle {
  color: #fff;
  background-color: #001230;
  border-color: #000e23;
}
.btn-secondary:not(:disabled):not(.disabled):active:focus, .btn-secondary:not(:disabled):not(.disabled).active:focus, .show > .btn-secondary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(38, 71, 122, 0.5);
}

.btn-success, .page--lms .btn-success, .page--lms #ffFormCreate .btn-blue, #ffFormCreate .page--lms .btn-blue, #ffgenerated button.btn-success, #ffgenerated #ffFormCreate button.btn-blue, #ffFormCreate #ffgenerated button.btn-blue, #ffFormCreate .btn-blue {
  color: #212529;
  background-color: #64d091;
  border-color: #64d091;
}
.btn-success:hover, .page--lms #ffFormCreate .btn-blue:hover, #ffFormCreate .page--lms .btn-blue:hover, #ffgenerated button.btn-success:hover, #ffgenerated #ffFormCreate button.btn-blue:hover, #ffFormCreate #ffgenerated button.btn-blue:hover, #ffFormCreate .btn-blue:hover {
  color: #212529;
  background-color: #47c77c;
  border-color: #3dc475;
}
.btn-success:focus, .page--lms #ffFormCreate .btn-blue:focus, #ffFormCreate .page--lms .btn-blue:focus, #ffgenerated button.btn-success:focus, #ffgenerated #ffFormCreate button.btn-blue:focus, #ffFormCreate #ffgenerated button.btn-blue:focus, #ffFormCreate .btn-blue:focus, .btn-success.focus, .page--lms #ffFormCreate .focus.btn-blue, #ffFormCreate .page--lms .focus.btn-blue, #ffgenerated button.focus.btn-success, #ffgenerated #ffFormCreate button.focus.btn-blue, #ffFormCreate #ffgenerated button.focus.btn-blue, #ffFormCreate .focus.btn-blue {
  color: #212529;
  background-color: #47c77c;
  border-color: #3dc475;
  box-shadow: 0 0 0 0.2rem rgba(90, 182, 129, 0.5);
}
.btn-success.disabled, .page--lms #ffFormCreate .disabled.btn-blue, #ffFormCreate .page--lms .disabled.btn-blue, #ffgenerated button.disabled.btn-success, #ffgenerated #ffFormCreate button.disabled.btn-blue, #ffFormCreate #ffgenerated button.disabled.btn-blue, #ffFormCreate .disabled.btn-blue, .btn-success:disabled, .page--lms #ffFormCreate .btn-blue:disabled, #ffFormCreate .page--lms .btn-blue:disabled, #ffgenerated button.btn-success:disabled, #ffgenerated #ffFormCreate button.btn-blue:disabled, #ffFormCreate #ffgenerated button.btn-blue:disabled, #ffFormCreate .btn-blue:disabled {
  color: #212529;
  background-color: #64d091;
  border-color: #64d091;
}
.btn-success:not(:disabled):not(.disabled):active, .page--lms #ffFormCreate .btn-blue:not(:disabled):not(.disabled):active, #ffFormCreate .page--lms .btn-blue:not(:disabled):not(.disabled):active, #ffgenerated button.btn-success:not(:disabled):not(.disabled):active, #ffgenerated #ffFormCreate button.btn-blue:not(:disabled):not(.disabled):active, #ffFormCreate #ffgenerated button.btn-blue:not(:disabled):not(.disabled):active, #ffFormCreate .btn-blue:not(:disabled):not(.disabled):active, .btn-success:not(:disabled):not(.disabled).active, .page--lms #ffFormCreate .btn-blue:not(:disabled):not(.disabled).active, #ffFormCreate .page--lms .btn-blue:not(:disabled):not(.disabled).active, #ffgenerated button.btn-success:not(:disabled):not(.disabled).active, #ffgenerated #ffFormCreate button.btn-blue:not(:disabled):not(.disabled).active, #ffFormCreate #ffgenerated button.btn-blue:not(:disabled):not(.disabled).active, #ffFormCreate .btn-blue:not(:disabled):not(.disabled).active, .show > .btn-success.dropdown-toggle, .page--lms #ffFormCreate .show > .dropdown-toggle.btn-blue, #ffFormCreate .page--lms .show > .dropdown-toggle.btn-blue, #ffgenerated .page--lms .show > button.dropdown-toggle.btn-success, #ffgenerated #ffFormCreate .page--lms .show > button.dropdown-toggle.btn-blue, #ffFormCreate #ffgenerated .page--lms .show > button.dropdown-toggle.btn-blue, #ffgenerated .show > button.dropdown-toggle.btn-success, #ffgenerated #ffFormCreate .show > button.dropdown-toggle.btn-blue, #ffFormCreate #ffgenerated .show > button.dropdown-toggle.btn-blue, #ffFormCreate .show > .dropdown-toggle.btn-blue {
  color: #fff;
  background-color: #3dc475;
  border-color: #39bb6f;
}
.btn-success:not(:disabled):not(.disabled):active:focus, .page--lms #ffFormCreate .btn-blue:not(:disabled):not(.disabled):active:focus, #ffFormCreate .page--lms .btn-blue:not(:disabled):not(.disabled):active:focus, #ffgenerated button.btn-success:not(:disabled):not(.disabled):active:focus, #ffgenerated #ffFormCreate button.btn-blue:not(:disabled):not(.disabled):active:focus, #ffFormCreate #ffgenerated button.btn-blue:not(:disabled):not(.disabled):active:focus, #ffFormCreate .btn-blue:not(:disabled):not(.disabled):active:focus, .btn-success:not(:disabled):not(.disabled).active:focus, .page--lms #ffFormCreate .btn-blue:not(:disabled):not(.disabled).active:focus, #ffFormCreate .page--lms .btn-blue:not(:disabled):not(.disabled).active:focus, #ffgenerated button.btn-success:not(:disabled):not(.disabled).active:focus, #ffgenerated #ffFormCreate button.btn-blue:not(:disabled):not(.disabled).active:focus, #ffFormCreate #ffgenerated button.btn-blue:not(:disabled):not(.disabled).active:focus, #ffFormCreate .btn-blue:not(:disabled):not(.disabled).active:focus, .show > .btn-success.dropdown-toggle:focus, .page--lms #ffFormCreate .show > .dropdown-toggle.btn-blue:focus, #ffFormCreate .page--lms .show > .dropdown-toggle.btn-blue:focus, #ffgenerated .page--lms .show > button.dropdown-toggle.btn-success:focus, #ffgenerated #ffFormCreate .page--lms .show > button.dropdown-toggle.btn-blue:focus, #ffFormCreate #ffgenerated .page--lms .show > button.dropdown-toggle.btn-blue:focus, #ffgenerated .show > button.dropdown-toggle.btn-success:focus, #ffgenerated #ffFormCreate .show > button.dropdown-toggle.btn-blue:focus, #ffFormCreate #ffgenerated .show > button.dropdown-toggle.btn-blue:focus, #ffFormCreate .show > .dropdown-toggle.btn-blue:focus {
  box-shadow: 0 0 0 0.2rem rgba(90, 182, 129, 0.5);
}

.btn-info {
  color: #fff;
  background-color: #388695;
  border-color: #388695;
}
.btn-info:hover {
  color: #fff;
  background-color: #2e6d79;
  border-color: #2a6570;
}
.btn-info:focus, .btn-info.focus {
  color: #fff;
  background-color: #2e6d79;
  border-color: #2a6570;
  box-shadow: 0 0 0 0.2rem rgba(86, 152, 165, 0.5);
}
.btn-info.disabled, .btn-info:disabled {
  color: #fff;
  background-color: #388695;
  border-color: #388695;
}
.btn-info:not(:disabled):not(.disabled):active, .btn-info:not(:disabled):not(.disabled).active, .show > .btn-info.dropdown-toggle {
  color: #fff;
  background-color: #2a6570;
  border-color: #275c67;
}
.btn-info:not(:disabled):not(.disabled):active:focus, .btn-info:not(:disabled):not(.disabled).active:focus, .show > .btn-info.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(86, 152, 165, 0.5);
}

.btn-warning {
  color: #212529;
  background-color: #DB9E5B;
  border-color: #DB9E5B;
}
.btn-warning:hover {
  color: #212529;
  background-color: #d48b3c;
  border-color: #d28531;
}
.btn-warning:focus, .btn-warning.focus {
  color: #212529;
  background-color: #d48b3c;
  border-color: #d28531;
  box-shadow: 0 0 0 0.2rem rgba(191, 140, 84, 0.5);
}
.btn-warning.disabled, .btn-warning:disabled {
  color: #212529;
  background-color: #DB9E5B;
  border-color: #DB9E5B;
}
.btn-warning:not(:disabled):not(.disabled):active, .btn-warning:not(:disabled):not(.disabled).active, .show > .btn-warning.dropdown-toggle {
  color: #fff;
  background-color: #d28531;
  border-color: #ca7f2c;
}
.btn-warning:not(:disabled):not(.disabled):active:focus, .btn-warning:not(:disabled):not(.disabled).active:focus, .show > .btn-warning.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(191, 140, 84, 0.5);
}

.btn-danger, .page--lms .btn-danger {
  color: #fff;
  background-color: #cc6565;
  border-color: #cc6565;
}
.btn-danger:hover {
  color: #fff;
  background-color: #c24848;
  border-color: #bf3f3f;
}
.btn-danger:focus, .btn-danger.focus {
  color: #fff;
  background-color: #c24848;
  border-color: #bf3f3f;
  box-shadow: 0 0 0 0.2rem rgba(212, 124, 124, 0.5);
}
.btn-danger.disabled, .btn-danger:disabled {
  color: #fff;
  background-color: #cc6565;
  border-color: #cc6565;
}
.btn-danger:not(:disabled):not(.disabled):active, .btn-danger:not(:disabled):not(.disabled).active, .show > .btn-danger.dropdown-toggle {
  color: #fff;
  background-color: #bf3f3f;
  border-color: #b53c3c;
}
.btn-danger:not(:disabled):not(.disabled):active:focus, .btn-danger:not(:disabled):not(.disabled).active:focus, .show > .btn-danger.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(212, 124, 124, 0.5);
}

.btn-light, .page--lms .btn-default, #ffgenerated button.btn-info, #ffFormCreate #ffForm div ul li a {
  color: #212529;
  background-color: #d8dce6;
  border-color: #d8dce6;
}
.btn-light:hover, .page--lms .btn-default:hover, #ffgenerated button.btn-info:hover, #ffFormCreate #ffForm div ul li a:hover {
  color: #212529;
  background-color: #c1c7d7;
  border-color: #b9c0d2;
}
.btn-light:focus, .page--lms .btn-default:focus, #ffgenerated button.btn-info:focus, #ffFormCreate #ffForm div ul li a:focus, .btn-light.focus, .page--lms .focus.btn-default, #ffgenerated button.focus.btn-info, #ffFormCreate #ffForm div ul li a.focus {
  color: #212529;
  background-color: #c1c7d7;
  border-color: #b9c0d2;
  box-shadow: 0 0 0 0.2rem rgba(189, 193, 202, 0.5);
}
.btn-light.disabled, .page--lms .disabled.btn-default, #ffgenerated button.disabled.btn-info, #ffFormCreate #ffForm div ul li a.disabled, .btn-light:disabled, .page--lms .btn-default:disabled, #ffgenerated button.btn-info:disabled, #ffFormCreate #ffForm div ul li a:disabled {
  color: #212529;
  background-color: #d8dce6;
  border-color: #d8dce6;
}
.btn-light:not(:disabled):not(.disabled):active, .page--lms .btn-default:not(:disabled):not(.disabled):active, #ffgenerated button.btn-info:not(:disabled):not(.disabled):active, #ffFormCreate #ffForm div ul li a:not(:disabled):not(.disabled):active, .btn-light:not(:disabled):not(.disabled).active, .page--lms .btn-default:not(:disabled):not(.disabled).active, #ffgenerated button.btn-info:not(:disabled):not(.disabled).active, #ffFormCreate #ffForm div ul li a:not(:disabled):not(.disabled).active, .show > .btn-light.dropdown-toggle, .page--lms .show > .dropdown-toggle.btn-default, #ffgenerated .show > button.dropdown-toggle.btn-info, #ffFormCreate #ffForm div ul li .show > a.dropdown-toggle {
  color: #212529;
  background-color: #b9c0d2;
  border-color: #b1b9cd;
}
.btn-light:not(:disabled):not(.disabled):active:focus, .page--lms .btn-default:not(:disabled):not(.disabled):active:focus, #ffgenerated button.btn-info:not(:disabled):not(.disabled):active:focus, #ffFormCreate #ffForm div ul li a:not(:disabled):not(.disabled):active:focus, .btn-light:not(:disabled):not(.disabled).active:focus, .page--lms .btn-default:not(:disabled):not(.disabled).active:focus, #ffgenerated button.btn-info:not(:disabled):not(.disabled).active:focus, #ffFormCreate #ffForm div ul li a:not(:disabled):not(.disabled).active:focus, .show > .btn-light.dropdown-toggle:focus, .page--lms .show > .dropdown-toggle.btn-default:focus, #ffgenerated .show > button.dropdown-toggle.btn-info:focus, #ffFormCreate #ffForm div ul li .show > a.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(189, 193, 202, 0.5);
}

.btn-dark {
  color: #fff;
  background-color: #5e6983;
  border-color: #5e6983;
}
.btn-dark:hover {
  color: #fff;
  background-color: #4e576d;
  border-color: #495165;
}
.btn-dark:focus, .btn-dark.focus {
  color: #fff;
  background-color: #4e576d;
  border-color: #495165;
  box-shadow: 0 0 0 0.2rem rgba(118, 128, 150, 0.5);
}
.btn-dark.disabled, .btn-dark:disabled {
  color: #fff;
  background-color: #5e6983;
  border-color: #5e6983;
}
.btn-dark:not(:disabled):not(.disabled):active, .btn-dark:not(:disabled):not(.disabled).active, .show > .btn-dark.dropdown-toggle {
  color: #fff;
  background-color: #495165;
  border-color: #434b5e;
}
.btn-dark:not(:disabled):not(.disabled):active:focus, .btn-dark:not(:disabled):not(.disabled).active:focus, .show > .btn-dark.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(118, 128, 150, 0.5);
}

.btn-lighter {
  color: #212529;
  background-color: #fafafc;
  border-color: #fafafc;
}
.btn-lighter:hover {
  color: #212529;
  background-color: #e2e2ee;
  border-color: #dadae9;
}
.btn-lighter:focus, .btn-lighter.focus {
  color: #212529;
  background-color: #e2e2ee;
  border-color: #dadae9;
  box-shadow: 0 0 0 0.2rem rgba(217, 218, 220, 0.5);
}
.btn-lighter.disabled, .btn-lighter:disabled {
  color: #212529;
  background-color: #fafafc;
  border-color: #fafafc;
}
.btn-lighter:not(:disabled):not(.disabled):active, .btn-lighter:not(:disabled):not(.disabled).active, .show > .btn-lighter.dropdown-toggle {
  color: #212529;
  background-color: #dadae9;
  border-color: #d2d2e4;
}
.btn-lighter:not(:disabled):not(.disabled):active:focus, .btn-lighter:not(:disabled):not(.disabled).active:focus, .show > .btn-lighter.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(217, 218, 220, 0.5);
}

.btn-lighter-blue {
  color: #212529;
  background-color: #f4f8fc;
  border-color: #f4f8fc;
}
.btn-lighter-blue:hover {
  color: #212529;
  background-color: #d6e5f4;
  border-color: #ccdff1;
}
.btn-lighter-blue:focus, .btn-lighter-blue.focus {
  color: #212529;
  background-color: #d6e5f4;
  border-color: #ccdff1;
  box-shadow: 0 0 0 0.2rem rgba(212, 216, 220, 0.5);
}
.btn-lighter-blue.disabled, .btn-lighter-blue:disabled {
  color: #212529;
  background-color: #f4f8fc;
  border-color: #f4f8fc;
}
.btn-lighter-blue:not(:disabled):not(.disabled):active, .btn-lighter-blue:not(:disabled):not(.disabled).active, .show > .btn-lighter-blue.dropdown-toggle {
  color: #212529;
  background-color: #ccdff1;
  border-color: #c2d8ee;
}
.btn-lighter-blue:not(:disabled):not(.disabled):active:focus, .btn-lighter-blue:not(:disabled):not(.disabled).active:focus, .show > .btn-lighter-blue.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(212, 216, 220, 0.5);
}

.btn-outline-primary {
  color: #337ab7;
  border-color: #337ab7;
}
.btn-outline-primary:hover {
  color: #fff;
  background-color: #337ab7;
  border-color: #337ab7;
}
.btn-outline-primary:focus, .btn-outline-primary.focus {
  box-shadow: 0 0 0 0.2rem rgba(51, 122, 183, 0.5);
}
.btn-outline-primary.disabled, .btn-outline-primary:disabled {
  color: #337ab7;
  background-color: transparent;
}
.btn-outline-primary:not(:disabled):not(.disabled):active, .btn-outline-primary:not(:disabled):not(.disabled).active, .show > .btn-outline-primary.dropdown-toggle {
  color: #fff;
  background-color: #337ab7;
  border-color: #337ab7;
}
.btn-outline-primary:not(:disabled):not(.disabled):active:focus, .btn-outline-primary:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-primary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(51, 122, 183, 0.5);
}

.btn-outline-secondary {
  color: #002663;
  border-color: #002663;
}
.btn-outline-secondary:hover {
  color: #fff;
  background-color: #002663;
  border-color: #002663;
}
.btn-outline-secondary:focus, .btn-outline-secondary.focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 38, 99, 0.5);
}
.btn-outline-secondary.disabled, .btn-outline-secondary:disabled {
  color: #002663;
  background-color: transparent;
}
.btn-outline-secondary:not(:disabled):not(.disabled):active, .btn-outline-secondary:not(:disabled):not(.disabled).active, .show > .btn-outline-secondary.dropdown-toggle {
  color: #fff;
  background-color: #002663;
  border-color: #002663;
}
.btn-outline-secondary:not(:disabled):not(.disabled):active:focus, .btn-outline-secondary:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-secondary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 38, 99, 0.5);
}

.btn-outline-success {
  color: #64d091;
  border-color: #64d091;
}
.btn-outline-success:hover {
  color: #212529;
  background-color: #64d091;
  border-color: #64d091;
}
.btn-outline-success:focus, .btn-outline-success.focus {
  box-shadow: 0 0 0 0.2rem rgba(100, 208, 145, 0.5);
}
.btn-outline-success.disabled, .btn-outline-success:disabled {
  color: #64d091;
  background-color: transparent;
}
.btn-outline-success:not(:disabled):not(.disabled):active, .btn-outline-success:not(:disabled):not(.disabled).active, .show > .btn-outline-success.dropdown-toggle {
  color: #212529;
  background-color: #64d091;
  border-color: #64d091;
}
.btn-outline-success:not(:disabled):not(.disabled):active:focus, .btn-outline-success:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-success.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(100, 208, 145, 0.5);
}

.btn-outline-info {
  color: #388695;
  border-color: #388695;
}
.btn-outline-info:hover {
  color: #fff;
  background-color: #388695;
  border-color: #388695;
}
.btn-outline-info:focus, .btn-outline-info.focus {
  box-shadow: 0 0 0 0.2rem rgba(56, 134, 149, 0.5);
}
.btn-outline-info.disabled, .btn-outline-info:disabled {
  color: #388695;
  background-color: transparent;
}
.btn-outline-info:not(:disabled):not(.disabled):active, .btn-outline-info:not(:disabled):not(.disabled).active, .show > .btn-outline-info.dropdown-toggle {
  color: #fff;
  background-color: #388695;
  border-color: #388695;
}
.btn-outline-info:not(:disabled):not(.disabled):active:focus, .btn-outline-info:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-info.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(56, 134, 149, 0.5);
}

.btn-outline-warning {
  color: #DB9E5B;
  border-color: #DB9E5B;
}
.btn-outline-warning:hover {
  color: #212529;
  background-color: #DB9E5B;
  border-color: #DB9E5B;
}
.btn-outline-warning:focus, .btn-outline-warning.focus {
  box-shadow: 0 0 0 0.2rem rgba(219, 158, 91, 0.5);
}
.btn-outline-warning.disabled, .btn-outline-warning:disabled {
  color: #DB9E5B;
  background-color: transparent;
}
.btn-outline-warning:not(:disabled):not(.disabled):active, .btn-outline-warning:not(:disabled):not(.disabled).active, .show > .btn-outline-warning.dropdown-toggle {
  color: #212529;
  background-color: #DB9E5B;
  border-color: #DB9E5B;
}
.btn-outline-warning:not(:disabled):not(.disabled):active:focus, .btn-outline-warning:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-warning.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(219, 158, 91, 0.5);
}

.btn-outline-danger {
  color: #cc6565;
  border-color: #cc6565;
}
.btn-outline-danger:hover {
  color: #fff;
  background-color: #cc6565;
  border-color: #cc6565;
}
.btn-outline-danger:focus, .btn-outline-danger.focus {
  box-shadow: 0 0 0 0.2rem rgba(204, 101, 101, 0.5);
}
.btn-outline-danger.disabled, .btn-outline-danger:disabled {
  color: #cc6565;
  background-color: transparent;
}
.btn-outline-danger:not(:disabled):not(.disabled):active, .btn-outline-danger:not(:disabled):not(.disabled).active, .show > .btn-outline-danger.dropdown-toggle {
  color: #fff;
  background-color: #cc6565;
  border-color: #cc6565;
}
.btn-outline-danger:not(:disabled):not(.disabled):active:focus, .btn-outline-danger:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-danger.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(204, 101, 101, 0.5);
}

.btn-outline-light {
  color: #d8dce6;
  border-color: #d8dce6;
}
.btn-outline-light:hover {
  color: #212529;
  background-color: #d8dce6;
  border-color: #d8dce6;
}
.btn-outline-light:focus, .btn-outline-light.focus {
  box-shadow: 0 0 0 0.2rem rgba(216, 220, 230, 0.5);
}
.btn-outline-light.disabled, .btn-outline-light:disabled {
  color: #d8dce6;
  background-color: transparent;
}
.btn-outline-light:not(:disabled):not(.disabled):active, .btn-outline-light:not(:disabled):not(.disabled).active, .show > .btn-outline-light.dropdown-toggle {
  color: #212529;
  background-color: #d8dce6;
  border-color: #d8dce6;
}
.btn-outline-light:not(:disabled):not(.disabled):active:focus, .btn-outline-light:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-light.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(216, 220, 230, 0.5);
}

.btn-outline-dark {
  color: #5e6983;
  border-color: #5e6983;
}
.btn-outline-dark:hover {
  color: #fff;
  background-color: #5e6983;
  border-color: #5e6983;
}
.btn-outline-dark:focus, .btn-outline-dark.focus {
  box-shadow: 0 0 0 0.2rem rgba(94, 105, 131, 0.5);
}
.btn-outline-dark.disabled, .btn-outline-dark:disabled {
  color: #5e6983;
  background-color: transparent;
}
.btn-outline-dark:not(:disabled):not(.disabled):active, .btn-outline-dark:not(:disabled):not(.disabled).active, .show > .btn-outline-dark.dropdown-toggle {
  color: #fff;
  background-color: #5e6983;
  border-color: #5e6983;
}
.btn-outline-dark:not(:disabled):not(.disabled):active:focus, .btn-outline-dark:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-dark.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(94, 105, 131, 0.5);
}

.btn-outline-lighter {
  color: #fafafc;
  border-color: #fafafc;
}
.btn-outline-lighter:hover {
  color: #212529;
  background-color: #fafafc;
  border-color: #fafafc;
}
.btn-outline-lighter:focus, .btn-outline-lighter.focus {
  box-shadow: 0 0 0 0.2rem rgba(250, 250, 252, 0.5);
}
.btn-outline-lighter.disabled, .btn-outline-lighter:disabled {
  color: #fafafc;
  background-color: transparent;
}
.btn-outline-lighter:not(:disabled):not(.disabled):active, .btn-outline-lighter:not(:disabled):not(.disabled).active, .show > .btn-outline-lighter.dropdown-toggle {
  color: #212529;
  background-color: #fafafc;
  border-color: #fafafc;
}
.btn-outline-lighter:not(:disabled):not(.disabled):active:focus, .btn-outline-lighter:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-lighter.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(250, 250, 252, 0.5);
}

.btn-outline-lighter-blue {
  color: #f4f8fc;
  border-color: #f4f8fc;
}
.btn-outline-lighter-blue:hover {
  color: #212529;
  background-color: #f4f8fc;
  border-color: #f4f8fc;
}
.btn-outline-lighter-blue:focus, .btn-outline-lighter-blue.focus {
  box-shadow: 0 0 0 0.2rem rgba(244, 248, 252, 0.5);
}
.btn-outline-lighter-blue.disabled, .btn-outline-lighter-blue:disabled {
  color: #f4f8fc;
  background-color: transparent;
}
.btn-outline-lighter-blue:not(:disabled):not(.disabled):active, .btn-outline-lighter-blue:not(:disabled):not(.disabled).active, .show > .btn-outline-lighter-blue.dropdown-toggle {
  color: #212529;
  background-color: #f4f8fc;
  border-color: #f4f8fc;
}
.btn-outline-lighter-blue:not(:disabled):not(.disabled):active:focus, .btn-outline-lighter-blue:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-lighter-blue.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(244, 248, 252, 0.5);
}

.btn-link {
  font-weight: 400;
  color: #337ab7;
  text-decoration: none;
}
.btn-link:hover {
  color: #22527b;
  text-decoration: underline;
}
.btn-link:focus, .btn-link.focus {
  text-decoration: underline;
}
.btn-link:disabled, .btn-link.disabled {
  color: #6c757d;
  pointer-events: none;
}

.btn-lg, .btn-group-lg > .btn, #ffFormCreate #ffForm div ul li .btn-group-lg > a {
  padding: 18px 25px;
  font-size: 0.75rem;
  line-height: 1.5;
  border-radius: 0.125rem;
}

.btn-sm, .btn-group-sm > .btn, #ffFormCreate #ffForm div ul li .btn-group-sm > a {
  padding: 7.5px 25px;
  font-size: 0.75rem;
  line-height: 1.5;
  border-radius: 0.125rem;
}

.btn-block {
  display: block;
  width: 100%;
}
.btn-block + .btn-block {
  margin-top: 0.5rem;
}

input[type=submit].btn-block,
input[type=reset].btn-block,
input[type=button].btn-block {
  width: 100%;
}

.fade {
  transition: opacity 0.15s linear;
}
@media (prefers-reduced-motion: reduce) {
  .fade {
    transition: none;
  }
}
.fade:not(.show) {
  opacity: 0;
}

.collapse:not(.show) {
  display: none;
}

.collapsing {
  position: relative;
  height: 0;
  overflow: hidden;
  transition: height 0.35s ease;
}
@media (prefers-reduced-motion: reduce) {
  .collapsing {
    transition: none;
  }
}
.collapsing.width {
  width: 0;
  height: auto;
  transition: width 0.35s ease;
}
@media (prefers-reduced-motion: reduce) {
  .collapsing.width {
    transition: none;
  }
}

.dropup,
.dropright,
.dropdown,
.dropleft {
  position: relative;
}

.dropdown-toggle {
  white-space: nowrap;
}
.dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid;
  border-right: 0.3em solid transparent;
  border-bottom: 0;
  border-left: 0.3em solid transparent;
}
.dropdown-toggle:empty::after {
  margin-left: 0;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 10rem;
  padding: 0.5rem 0;
  margin: 0.125rem 0 0;
  font-size: 0.875rem;
  color: #5e6983;
  text-align: left;
  list-style: none;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 0.125rem;
}

.dropdown-menu-left {
  right: auto;
  left: 0;
}

.dropdown-menu-right {
  right: 0;
  left: auto;
}

@media (min-width: 600px) {
  .dropdown-menu-sm-left {
    right: auto;
    left: 0;
  }

  .dropdown-menu-sm-right {
    right: 0;
    left: auto;
  }
}
@media (min-width: 980px) {
  .dropdown-menu-md-left {
    right: auto;
    left: 0;
  }

  .dropdown-menu-md-right {
    right: 0;
    left: auto;
  }
}
@media (min-width: 1200px) {
  .dropdown-menu-lg-left {
    right: auto;
    left: 0;
  }

  .dropdown-menu-lg-right {
    right: 0;
    left: auto;
  }
}
@media (min-width: 1580px) {
  .dropdown-menu-xl-left {
    right: auto;
    left: 0;
  }

  .dropdown-menu-xl-right {
    right: 0;
    left: auto;
  }
}
.dropup .dropdown-menu {
  top: auto;
  bottom: 100%;
  margin-top: 0;
  margin-bottom: 0.125rem;
}
.dropup .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0;
  border-right: 0.3em solid transparent;
  border-bottom: 0.3em solid;
  border-left: 0.3em solid transparent;
}
.dropup .dropdown-toggle:empty::after {
  margin-left: 0;
}

.dropright .dropdown-menu {
  top: 0;
  right: auto;
  left: 100%;
  margin-top: 0;
  margin-left: 0.125rem;
}
.dropright .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid transparent;
  border-right: 0;
  border-bottom: 0.3em solid transparent;
  border-left: 0.3em solid;
}
.dropright .dropdown-toggle:empty::after {
  margin-left: 0;
}
.dropright .dropdown-toggle::after {
  vertical-align: 0;
}

.dropleft .dropdown-menu {
  top: 0;
  right: 100%;
  left: auto;
  margin-top: 0;
  margin-right: 0.125rem;
}
.dropleft .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
}
.dropleft .dropdown-toggle::after {
  display: none;
}
.dropleft .dropdown-toggle::before {
  display: inline-block;
  margin-right: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid transparent;
  border-right: 0.3em solid;
  border-bottom: 0.3em solid transparent;
}
.dropleft .dropdown-toggle:empty::after {
  margin-left: 0;
}
.dropleft .dropdown-toggle::before {
  vertical-align: 0;
}

.dropdown-menu[x-placement^=top], .dropdown-menu[x-placement^=right], .dropdown-menu[x-placement^=bottom], .dropdown-menu[x-placement^=left] {
  right: auto;
  bottom: auto;
}

.dropdown-divider {
  height: 0;
  margin: 0.5rem 0;
  overflow: hidden;
  border-top: 1px solid #e9ecef;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: 0.25rem 1.5rem;
  clear: both;
  font-weight: 400;
  color: #212529;
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
}
.dropdown-item:hover, .dropdown-item:focus {
  color: #16181b;
  text-decoration: none;
  background-color: #e9ecef;
}
.dropdown-item.active, .dropdown-item:active {
  color: #fff;
  text-decoration: none;
  background-color: #337ab7;
}
.dropdown-item.disabled, .dropdown-item:disabled {
  color: #adb5bd;
  pointer-events: none;
  background-color: transparent;
}

.dropdown-menu.show {
  display: block;
}

.dropdown-header {
  display: block;
  padding: 0.5rem 1.5rem;
  margin-bottom: 0;
  font-size: 0.765625rem;
  color: #6c757d;
  white-space: nowrap;
}

.dropdown-item-text {
  display: block;
  padding: 0.25rem 1.5rem;
  color: #212529;
}

.btn-group,
.btn-group-vertical {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  vertical-align: middle;
}
.btn-group > .btn, #ffFormCreate #ffForm div ul li .btn-group > a,
.btn-group-vertical > .btn,
#ffFormCreate #ffForm div ul li .btn-group-vertical > a {
  position: relative;
  flex: 1 1 auto;
}
.btn-group > .btn:hover, #ffFormCreate #ffForm div ul li .btn-group > a:hover,
.btn-group-vertical > .btn:hover,
#ffFormCreate #ffForm div ul li .btn-group-vertical > a:hover {
  z-index: 1;
}
.btn-group > .btn:focus, #ffFormCreate #ffForm div ul li .btn-group > a:focus, .btn-group > .btn:active, #ffFormCreate #ffForm div ul li .btn-group > a:active, .btn-group > .btn.active, #ffFormCreate #ffForm div ul li .btn-group > a.active,
.btn-group-vertical > .btn:focus,
#ffFormCreate #ffForm div ul li .btn-group-vertical > a:focus,
.btn-group-vertical > .btn:active,
#ffFormCreate #ffForm div ul li .btn-group-vertical > a:active,
.btn-group-vertical > .btn.active,
#ffFormCreate #ffForm div ul li .btn-group-vertical > a.active {
  z-index: 1;
}

.btn-toolbar {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}
.btn-toolbar .input-group {
  width: auto;
}

.btn-group > .btn:not(:first-child), #ffFormCreate #ffForm div ul li .btn-group > a:not(:first-child),
.btn-group > .btn-group:not(:first-child) {
  margin-left: -1px;
}
.btn-group > .btn:not(:last-child):not(.dropdown-toggle), #ffFormCreate #ffForm div ul li .btn-group > a:not(:last-child):not(.dropdown-toggle),
.btn-group > .btn-group:not(:last-child) > .btn,
#ffFormCreate #ffForm div ul li .btn-group > .btn-group:not(:last-child) > a {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.btn-group > .btn:not(:first-child), #ffFormCreate #ffForm div ul li .btn-group > a:not(:first-child),
.btn-group > .btn-group:not(:first-child) > .btn,
#ffFormCreate #ffForm div ul li .btn-group > .btn-group:not(:first-child) > a {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.dropdown-toggle-split {
  padding-right: 18.75px;
  padding-left: 18.75px;
}
.dropdown-toggle-split::after, .dropup .dropdown-toggle-split::after, .dropright .dropdown-toggle-split::after {
  margin-left: 0;
}
.dropleft .dropdown-toggle-split::before {
  margin-right: 0;
}

.btn-sm + .dropdown-toggle-split, .btn-group-sm > .btn + .dropdown-toggle-split, #ffFormCreate #ffForm div ul li .btn-group-sm > a + .dropdown-toggle-split {
  padding-right: 18.75px;
  padding-left: 18.75px;
}

.btn-lg + .dropdown-toggle-split, .btn-group-lg > .btn + .dropdown-toggle-split, #ffFormCreate #ffForm div ul li .btn-group-lg > a + .dropdown-toggle-split {
  padding-right: 18.75px;
  padding-left: 18.75px;
}

.btn-group-vertical {
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
}
.btn-group-vertical > .btn, #ffFormCreate #ffForm div ul li .btn-group-vertical > a,
.btn-group-vertical > .btn-group {
  width: 100%;
}
.btn-group-vertical > .btn:not(:first-child), #ffFormCreate #ffForm div ul li .btn-group-vertical > a:not(:first-child),
.btn-group-vertical > .btn-group:not(:first-child) {
  margin-top: -1px;
}
.btn-group-vertical > .btn:not(:last-child):not(.dropdown-toggle), #ffFormCreate #ffForm div ul li .btn-group-vertical > a:not(:last-child):not(.dropdown-toggle),
.btn-group-vertical > .btn-group:not(:last-child) > .btn,
#ffFormCreate #ffForm div ul li .btn-group-vertical > .btn-group:not(:last-child) > a {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.btn-group-vertical > .btn:not(:first-child), #ffFormCreate #ffForm div ul li .btn-group-vertical > a:not(:first-child),
.btn-group-vertical > .btn-group:not(:first-child) > .btn,
#ffFormCreate #ffForm div ul li .btn-group-vertical > .btn-group:not(:first-child) > a {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.btn-group-toggle > .btn, #ffFormCreate #ffForm div ul li .btn-group-toggle > a,
.btn-group-toggle > .btn-group > .btn,
#ffFormCreate #ffForm div ul li .btn-group-toggle > .btn-group > a {
  margin-bottom: 0;
}
.btn-group-toggle > .btn input[type=radio], #ffFormCreate #ffForm div ul li .btn-group-toggle > a input[type=radio],
.btn-group-toggle > .btn input[type=checkbox],
#ffFormCreate #ffForm div ul li .btn-group-toggle > a input[type=checkbox],
.btn-group-toggle > .btn-group > .btn input[type=radio],
.btn-group-toggle > .btn-group > .btn input[type=checkbox] {
  position: absolute;
  clip: rect(0, 0, 0, 0);
  pointer-events: none;
}

.input-group {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  width: 100%;
}
.input-group > .form-control,
.input-group > .form-control-plaintext,
.input-group > .custom-select,
.input-group > .custom-file {
  position: relative;
  flex: 1 1 auto;
  width: 1%;
  min-width: 0;
  margin-bottom: 0;
}
.input-group > .form-control + .form-control,
.input-group > .form-control + .custom-select,
.input-group > .form-control + .custom-file,
.input-group > .form-control-plaintext + .form-control,
.input-group > .form-control-plaintext + .custom-select,
.input-group > .form-control-plaintext + .custom-file,
.input-group > .custom-select + .form-control,
.input-group > .custom-select + .custom-select,
.input-group > .custom-select + .custom-file,
.input-group > .custom-file + .form-control,
.input-group > .custom-file + .custom-select,
.input-group > .custom-file + .custom-file {
  margin-left: -1px;
}
.input-group > .form-control:focus,
.input-group > .custom-select:focus,
.input-group > .custom-file .custom-file-input:focus ~ .custom-file-label {
  z-index: 3;
}
.input-group > .custom-file .custom-file-input:focus {
  z-index: 4;
}
.input-group > .form-control:not(:first-child),
.input-group > .custom-select:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.input-group > .custom-file {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
}
.input-group > .custom-file:not(:last-child) .custom-file-label, .input-group > .custom-file:not(:last-child) .custom-file-label::after {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.input-group > .custom-file:not(:first-child) .custom-file-label {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.input-group:not(.has-validation) > .form-control:not(:last-child),
.input-group:not(.has-validation) > .custom-select:not(:last-child),
.input-group:not(.has-validation) > .custom-file:not(:last-child) .custom-file-label,
.input-group:not(.has-validation) > .custom-file:not(:last-child) .custom-file-label::after {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.input-group.has-validation > .form-control:nth-last-child(n+3),
.input-group.has-validation > .custom-select:nth-last-child(n+3),
.input-group.has-validation > .custom-file:nth-last-child(n+3) .custom-file-label,
.input-group.has-validation > .custom-file:nth-last-child(n+3) .custom-file-label::after {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.input-group-prepend,
.input-group-append {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}
.input-group-prepend .btn, .input-group-prepend #ffFormCreate #ffForm div ul li a, #ffFormCreate #ffForm div ul li .input-group-prepend a,
.input-group-append .btn,
.input-group-append #ffFormCreate #ffForm div ul li a,
#ffFormCreate #ffForm div ul li .input-group-append a {
  position: relative;
  z-index: 2;
}
.input-group-prepend .btn:focus, .input-group-prepend #ffFormCreate #ffForm div ul li a:focus, #ffFormCreate #ffForm div ul li .input-group-prepend a:focus,
.input-group-append .btn:focus,
.input-group-append #ffFormCreate #ffForm div ul li a:focus,
#ffFormCreate #ffForm div ul li .input-group-append a:focus {
  z-index: 3;
}
.input-group-prepend .btn + .btn, .input-group-prepend #ffFormCreate #ffForm div ul li a + .btn, #ffFormCreate #ffForm div ul li .input-group-prepend a + .btn, .input-group-prepend #ffFormCreate #ffForm div ul li .btn + a, #ffFormCreate #ffForm div ul li .input-group-prepend .btn + a, .input-group-prepend #ffFormCreate #ffForm div ul li a + a, #ffFormCreate #ffForm div ul li .input-group-prepend a + a,
.input-group-prepend .btn + .input-group-text,
.input-group-prepend #ffFormCreate #ffForm div ul li a + .input-group-text,
#ffFormCreate #ffForm div ul li .input-group-prepend a + .input-group-text,
.input-group-prepend .input-group-text + .input-group-text,
.input-group-prepend .input-group-text + .btn,
.input-group-prepend #ffFormCreate #ffForm div ul li .input-group-text + a,
#ffFormCreate #ffForm div ul li .input-group-prepend .input-group-text + a,
.input-group-append .btn + .btn,
.input-group-append #ffFormCreate #ffForm div ul li a + .btn,
#ffFormCreate #ffForm div ul li .input-group-append a + .btn,
.input-group-append #ffFormCreate #ffForm div ul li .btn + a,
#ffFormCreate #ffForm div ul li .input-group-append .btn + a,
.input-group-append #ffFormCreate #ffForm div ul li a + a,
#ffFormCreate #ffForm div ul li .input-group-append a + a,
.input-group-append .btn + .input-group-text,
.input-group-append #ffFormCreate #ffForm div ul li a + .input-group-text,
#ffFormCreate #ffForm div ul li .input-group-append a + .input-group-text,
.input-group-append .input-group-text + .input-group-text,
.input-group-append .input-group-text + .btn,
.input-group-append #ffFormCreate #ffForm div ul li .input-group-text + a,
#ffFormCreate #ffForm div ul li .input-group-append .input-group-text + a {
  margin-left: -1px;
}

.input-group-prepend {
  margin-right: -1px;
}

.input-group-append {
  margin-left: -1px;
}

.input-group-text {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  padding: 0.6875rem 0.9375rem;
  margin-bottom: 0;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.3125;
  color: #495057;
  text-align: center;
  white-space: nowrap;
  background-color: #fafafc;
  border: 1px solid #d8dce6;
  border-radius: 0.125rem;
}
.input-group-text input[type=radio],
.input-group-text input[type=checkbox] {
  margin-top: 0;
}

.input-group-lg > .form-control:not(textarea),
.input-group-lg > .custom-select {
  height: -webkit-calc(1.5em + 1rem + 2px);
  height: calc(1.5em + 1rem + 2px);
}

.input-group-lg > .form-control,
.input-group-lg > .custom-select,
.input-group-lg > .input-group-prepend > .input-group-text,
.input-group-lg > .input-group-append > .input-group-text,
.input-group-lg > .input-group-prepend > .btn,
#ffFormCreate #ffForm div ul li .input-group-lg > .input-group-prepend > a,
.input-group-lg > .input-group-append > .btn,
#ffFormCreate #ffForm div ul li .input-group-lg > .input-group-append > a {
  padding: 0.5rem 1rem;
  font-size: 1.09375rem;
  line-height: 1.5;
  border-radius: 0.3rem;
}

.input-group-sm > .form-control:not(textarea),
.input-group-sm > .custom-select {
  height: -webkit-calc(1.5em + 0.5rem + 2px);
  height: calc(1.5em + 0.5rem + 2px);
}

.input-group-sm > .form-control,
.input-group-sm > .custom-select,
.input-group-sm > .input-group-prepend > .input-group-text,
.input-group-sm > .input-group-append > .input-group-text,
.input-group-sm > .input-group-prepend > .btn,
#ffFormCreate #ffForm div ul li .input-group-sm > .input-group-prepend > a,
.input-group-sm > .input-group-append > .btn,
#ffFormCreate #ffForm div ul li .input-group-sm > .input-group-append > a {
  padding: 0.25rem 0.5rem;
  font-size: 0.765625rem;
  line-height: 1.5;
  border-radius: 0.2rem;
}

.input-group-lg > .custom-select,
.input-group-sm > .custom-select {
  padding-right: 1.9375rem;
}

.input-group > .input-group-prepend > .btn, #ffFormCreate #ffForm div ul li .input-group > .input-group-prepend > a,
.input-group > .input-group-prepend > .input-group-text,
.input-group:not(.has-validation) > .input-group-append:not(:last-child) > .btn,
#ffFormCreate #ffForm div ul li .input-group:not(.has-validation) > .input-group-append:not(:last-child) > a,
.input-group:not(.has-validation) > .input-group-append:not(:last-child) > .input-group-text,
.input-group.has-validation > .input-group-append:nth-last-child(n+3) > .btn,
#ffFormCreate #ffForm div ul li .input-group.has-validation > .input-group-append:nth-last-child(n+3) > a,
.input-group.has-validation > .input-group-append:nth-last-child(n+3) > .input-group-text,
.input-group > .input-group-append:last-child > .btn:not(:last-child):not(.dropdown-toggle),
#ffFormCreate #ffForm div ul li .input-group > .input-group-append:last-child > a:not(:last-child):not(.dropdown-toggle),
.input-group > .input-group-append:last-child > .input-group-text:not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.input-group > .input-group-append > .btn, #ffFormCreate #ffForm div ul li .input-group > .input-group-append > a,
.input-group > .input-group-append > .input-group-text,
.input-group > .input-group-prepend:not(:first-child) > .btn,
#ffFormCreate #ffForm div ul li .input-group > .input-group-prepend:not(:first-child) > a,
.input-group > .input-group-prepend:not(:first-child) > .input-group-text,
.input-group > .input-group-prepend:first-child > .btn:not(:first-child),
#ffFormCreate #ffForm div ul li .input-group > .input-group-prepend:first-child > a:not(:first-child),
.input-group > .input-group-prepend:first-child > .input-group-text:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.custom-control {
  position: relative;
  z-index: 1;
  display: block;
  min-height: 1.1484375rem;
  padding-left: 1.5rem;
  print-color-adjust: exact;
}

.custom-control-inline {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  margin-right: 1rem;
}

.custom-control-input {
  position: absolute;
  left: 0;
  z-index: -1;
  width: 1rem;
  height: 1.07421875rem;
  opacity: 0;
}
.custom-control-input:checked ~ .custom-control-label::before {
  color: #fff;
  border-color: #337ab7;
  background-color: #337ab7;
}
.custom-control-input:focus ~ .custom-control-label::before {
  box-shadow: none !important;
}
.custom-control-input:focus:not(:checked) ~ .custom-control-label::before {
  border-color: #2e6da4;
}
.custom-control-input:not(:disabled):active ~ .custom-control-label::before {
  color: #fff;
  background-color: #b3d0ea;
  border-color: #b3d0ea;
}
.custom-control-input[disabled] ~ .custom-control-label, .custom-control-input:disabled ~ .custom-control-label {
  color: #6c757d;
}
.custom-control-input[disabled] ~ .custom-control-label::before, .custom-control-input:disabled ~ .custom-control-label::before {
  background-color: #e9ecef;
}

.custom-control-label {
  position: relative;
  margin-bottom: 0;
  vertical-align: top;
}
.custom-control-label::before {
  position: absolute;
  top: 0.07421875rem;
  left: -1.5rem;
  display: block;
  width: 1rem;
  height: 1rem;
  pointer-events: none;
  content: "";
  background-color: #fafafc;
  border: 1px solid #adb5bd;
}
.custom-control-label::after {
  position: absolute;
  top: 0.07421875rem;
  left: -1.5rem;
  display: block;
  width: 1rem;
  height: 1rem;
  content: "";
  background: 50%/50% 50% no-repeat;
}

.custom-checkbox .custom-control-label::before {
  border-radius: 0.125rem;
}
.custom-checkbox .custom-control-input:checked ~ .custom-control-label::after {
  background-image: none;
}
.custom-checkbox .custom-control-input:indeterminate ~ .custom-control-label::before {
  border-color: #337ab7;
  background-color: #337ab7;
}
.custom-checkbox .custom-control-input:indeterminate ~ .custom-control-label::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='4' viewBox='0 0 4 4'%3e%3cpath stroke='%23fff' d='M0 2h4'/%3e%3c/svg%3e");
}
.custom-checkbox .custom-control-input:disabled:checked ~ .custom-control-label::before {
  background-color: rgba(51, 122, 183, 0.5);
}
.custom-checkbox .custom-control-input:disabled:indeterminate ~ .custom-control-label::before {
  background-color: rgba(51, 122, 183, 0.5);
}

.custom-radio .custom-control-label::before {
  border-radius: 50%;
}
.custom-radio .custom-control-input:checked ~ .custom-control-label::after {
  background-image: none;
}
.custom-radio .custom-control-input:disabled:checked ~ .custom-control-label::before {
  background-color: rgba(51, 122, 183, 0.5);
}

.custom-switch {
  padding-left: 2.25rem;
}
.custom-switch .custom-control-label::before {
  left: -2.25rem;
  width: 1.75rem;
  pointer-events: all;
  border-radius: 0.5rem;
}
.custom-switch .custom-control-label::after {
  top: -webkit-calc(0.07421875rem + 2px);
  top: calc(0.07421875rem + 2px);
  left: -webkit-calc(-2.25rem + 2px);
  left: calc(-2.25rem + 2px);
  width: -webkit-calc(1rem - 4px);
  width: calc(1rem - 4px);
  height: -webkit-calc(1rem - 4px);
  height: calc(1rem - 4px);
  background-color: #adb5bd;
  border-radius: 0.5rem;
  transition: transform 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .custom-switch .custom-control-label::after {
    transition: none;
  }
}
.custom-switch .custom-control-input:checked ~ .custom-control-label::after {
  background-color: #fafafc;
  transform: translateX(0.75rem);
}
.custom-switch .custom-control-input:disabled:checked ~ .custom-control-label::before {
  background-color: rgba(51, 122, 183, 0.5);
}

.custom-select {
  display: inline-block;
  width: 100%;
  height: -webkit-calc(1.3125em + 1.375rem + 2px);
  height: calc(1.3125em + 1.375rem + 2px);
  padding: 0.6875rem 1.9375rem 0.6875rem 0.9375rem;
  font-family: "Roboto", sans-serif, serif;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.3125;
  color: #495057;
  vertical-align: middle;
  background: #fafafc url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='5' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") right 0.9375rem center/8px 10px no-repeat;
  border: 1px solid #d8dce6;
  border-radius: 0.125rem;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.custom-select:focus {
  border-color: #2e6da4;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(51, 122, 183, 0.25);
}
.custom-select:focus::-ms-value {
  color: #495057;
  background-color: #fafafc;
}
.custom-select[multiple], .custom-select[size]:not([size="1"]) {
  height: auto;
  padding-right: 0.9375rem;
  background-image: none;
}
.custom-select:disabled {
  color: #6c757d;
  background-color: #e9ecef;
}
.custom-select::-ms-expand {
  display: none;
}
.custom-select:-moz-focusring {
  color: transparent;
  text-shadow: 0 0 0 #495057;
}

.custom-select-sm {
  height: -webkit-calc(1.5em + 0.5rem + 2px);
  height: calc(1.5em + 0.5rem + 2px);
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  padding-left: 0.5rem;
  font-size: 0.765625rem;
}

.custom-select-lg {
  height: -webkit-calc(1.5em + 1rem + 2px);
  height: calc(1.5em + 1rem + 2px);
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 1rem;
  font-size: 1.09375rem;
}

.custom-file {
  position: relative;
  display: inline-block;
  width: 100%;
  height: -webkit-calc(1.3125em + 1.375rem + 2px);
  height: calc(1.3125em + 1.375rem + 2px);
  margin-bottom: 0;
}

.custom-file-input {
  position: relative;
  z-index: 2;
  width: 100%;
  height: -webkit-calc(1.3125em + 1.375rem + 2px);
  height: calc(1.3125em + 1.375rem + 2px);
  margin: 0;
  overflow: hidden;
  opacity: 0;
}
.custom-file-input:focus ~ .custom-file-label {
  border-color: #2e6da4;
  box-shadow: none !important;
}
.custom-file-input[disabled] ~ .custom-file-label, .custom-file-input:disabled ~ .custom-file-label {
  background-color: #e9ecef;
}
.custom-file-input:lang(en) ~ .custom-file-label::after {
  content: "Browse";
}
.custom-file-input ~ .custom-file-label[data-browse]::after {
  content: attr(data-browse);
}

.custom-file-label {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1;
  height: -webkit-calc(1.3125em + 1.375rem + 2px);
  height: calc(1.3125em + 1.375rem + 2px);
  padding: 0.6875rem 0.9375rem;
  overflow: hidden;
  font-family: "Roboto", sans-serif, serif;
  font-weight: 400;
  line-height: 1.3125;
  color: #495057;
  background-color: #fafafc;
  border: 1px solid #d8dce6;
  border-radius: 0.125rem;
}
.custom-file-label::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 3;
  display: block;
  height: -webkit-calc(1.3125em + 1.375rem);
  height: calc(1.3125em + 1.375rem);
  padding: 0.6875rem 0.9375rem;
  line-height: 1.3125;
  color: #495057;
  content: "Browse";
  background-color: #fafafc;
  border-left: inherit;
  border-radius: 0 0.125rem 0.125rem 0;
}

.custom-range {
  width: 100%;
  height: 1.4rem;
  padding: 0;
  background-color: transparent;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.custom-range:focus {
  outline: 0;
}
.custom-range:focus::-webkit-slider-thumb {
  box-shadow: 0 0 0 1px #fff, none !important;
}
.custom-range:focus::-moz-range-thumb {
  box-shadow: 0 0 0 1px #fff, none !important;
}
.custom-range:focus::-ms-thumb {
  box-shadow: 0 0 0 1px #fff, none !important;
}
.custom-range::-moz-focus-outer {
  border: 0;
}
.custom-range::-webkit-slider-thumb {
  width: 1rem;
  height: 1rem;
  margin-top: -0.25rem;
  background-color: #337ab7;
  border: 0;
  border-radius: 1rem;
  -webkit-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  -webkit-appearance: none;
          appearance: none;
}
@media (prefers-reduced-motion: reduce) {
  .custom-range::-webkit-slider-thumb {
    -webkit-transition: none;
    transition: none;
  }
}
.custom-range::-webkit-slider-thumb:active {
  background-color: #b3d0ea;
}
.custom-range::-webkit-slider-runnable-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: #dee2e6;
  border-color: transparent;
  border-radius: 1rem;
}
.custom-range::-moz-range-thumb {
  width: 1rem;
  height: 1rem;
  background-color: #337ab7;
  border: 0;
  border-radius: 1rem;
  -moz-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  -moz-appearance: none;
       appearance: none;
}
@media (prefers-reduced-motion: reduce) {
  .custom-range::-moz-range-thumb {
    -moz-transition: none;
    transition: none;
  }
}
.custom-range::-moz-range-thumb:active {
  background-color: #b3d0ea;
}
.custom-range::-moz-range-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: #dee2e6;
  border-color: transparent;
  border-radius: 1rem;
}
.custom-range::-ms-thumb {
  width: 1rem;
  height: 1rem;
  margin-top: 0;
  margin-right: 0.2rem;
  margin-left: 0.2rem;
  background-color: #337ab7;
  border: 0;
  border-radius: 1rem;
  -ms-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  appearance: none;
}
@media (prefers-reduced-motion: reduce) {
  .custom-range::-ms-thumb {
    -ms-transition: none;
    transition: none;
  }
}
.custom-range::-ms-thumb:active {
  background-color: #b3d0ea;
}
.custom-range::-ms-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: transparent;
  border-color: transparent;
  border-width: 0.5rem;
}
.custom-range::-ms-fill-lower {
  background-color: #dee2e6;
  border-radius: 1rem;
}
.custom-range::-ms-fill-upper {
  margin-right: 15px;
  background-color: #dee2e6;
  border-radius: 1rem;
}
.custom-range:disabled::-webkit-slider-thumb {
  background-color: #adb5bd;
}
.custom-range:disabled::-webkit-slider-runnable-track {
  cursor: default;
}
.custom-range:disabled::-moz-range-thumb {
  background-color: #adb5bd;
}
.custom-range:disabled::-moz-range-track {
  cursor: default;
}
.custom-range:disabled::-ms-thumb {
  background-color: #adb5bd;
}

.custom-control-label::before,
.custom-file-label,
.custom-select {
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .custom-control-label::before,
.custom-file-label,
.custom-select {
    transition: none;
  }
}

.nav {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  flex-wrap: wrap;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}

.nav-link {
  display: block;
  padding: 0.65rem 1rem;
}
.nav-link:hover, .nav-link:focus {
  text-decoration: none;
}
.nav-link.disabled {
  color: #6c757d;
  pointer-events: none;
  cursor: default;
}

.nav-tabs {
  border-bottom: 1px solid #dee2e6;
}
.nav-tabs .nav-link {
  margin-bottom: -1px;
  background-color: transparent;
  border: 1px solid transparent;
  border-top-left-radius: 0.125rem;
  border-top-right-radius: 0.125rem;
}
.nav-tabs .nav-link:hover, .nav-tabs .nav-link:focus {
  isolation: isolate;
  border-color: #e9ecef #e9ecef #dee2e6;
}
.nav-tabs .nav-link.disabled {
  color: #6c757d;
  background-color: transparent;
  border-color: transparent;
}
.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
  color: #495057;
  background-color: #fff;
  border-color: #dee2e6 #dee2e6 #fff;
}
.nav-tabs .dropdown-menu {
  margin-top: -1px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.nav-pills .nav-link {
  background: none;
  border: 0;
  border-radius: 0;
}
.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  color: #fff;
  background-color: #337ab7;
}

.nav-fill > .nav-link,
.nav-fill .nav-item {
  flex: 1 1 auto;
  text-align: center;
}

.nav-justified > .nav-link,
.nav-justified .nav-item {
  flex-basis: 0;
  flex-grow: 1;
  text-align: center;
}

.tab-content > .tab-pane {
  display: none;
}
.tab-content > .active {
  display: block;
}

.navbar {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 1rem;
}
.navbar .container,
.navbar .container-fluid,
.navbar .container-sm,
.navbar .container-md,
.navbar .container-lg,
.navbar .container-xl {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
}
.navbar-brand {
  display: inline-block;
  padding-top: 0.5064453125rem;
  padding-bottom: 0.5064453125rem;
  margin-right: 1rem;
  font-size: 1.09375rem;
  line-height: inherit;
  white-space: nowrap;
}
.navbar-brand:hover, .navbar-brand:focus {
  text-decoration: none;
}

.navbar-nav {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  flex-direction: column;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}
.navbar-nav .nav-link {
  padding-right: 0;
  padding-left: 0;
}
.navbar-nav .dropdown-menu {
  position: static;
  float: none;
}

.navbar-text {
  display: inline-block;
  padding-top: 0.65rem;
  padding-bottom: 0.65rem;
}

.navbar-collapse {
  flex-basis: 100%;
  flex-grow: 1;
  align-items: center;
}

.navbar-toggler {
  padding: 0.25rem 0.75rem;
  font-size: 1.09375rem;
  line-height: 1;
  background-color: transparent;
  border: 1px solid transparent;
  border-radius: 0.125rem;
}
.navbar-toggler:hover, .navbar-toggler:focus {
  text-decoration: none;
}

.navbar-toggler-icon {
  display: inline-block;
  width: 1.5em;
  height: 1.5em;
  vertical-align: middle;
  content: "";
  background: 50%/100% 100% no-repeat;
}

.navbar-nav-scroll {
  max-height: 75vh;
  overflow-y: auto;
}

@media (max-width: 599.98px) {
  .navbar-expand-sm > .container,
.navbar-expand-sm > .container-fluid,
.navbar-expand-sm > .container-sm,
.navbar-expand-sm > .container-md,
.navbar-expand-sm > .container-lg,
.navbar-expand-sm > .container-xl {
    padding-right: 0;
    padding-left: 0;
  }
}
@media (min-width: 600px) {
  .navbar-expand-sm {
    flex-flow: row nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-sm .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-sm .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-sm .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-sm > .container,
.navbar-expand-sm > .container-fluid,
.navbar-expand-sm > .container-sm,
.navbar-expand-sm > .container-md,
.navbar-expand-sm > .container-lg,
.navbar-expand-sm > .container-xl {
    flex-wrap: nowrap;
  }
  .navbar-expand-sm .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-sm .navbar-collapse {
    display: -webkit-box !important;
    display: -webkit-flex !important;
    display: -ms-flexbox !important;
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-sm .navbar-toggler {
    display: none;
  }
}
@media (max-width: 979.98px) {
  .navbar-expand-md > .container,
.navbar-expand-md > .container-fluid,
.navbar-expand-md > .container-sm,
.navbar-expand-md > .container-md,
.navbar-expand-md > .container-lg,
.navbar-expand-md > .container-xl {
    padding-right: 0;
    padding-left: 0;
  }
}
@media (min-width: 980px) {
  .navbar-expand-md {
    flex-flow: row nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-md .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-md .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-md .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-md > .container,
.navbar-expand-md > .container-fluid,
.navbar-expand-md > .container-sm,
.navbar-expand-md > .container-md,
.navbar-expand-md > .container-lg,
.navbar-expand-md > .container-xl {
    flex-wrap: nowrap;
  }
  .navbar-expand-md .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-md .navbar-collapse {
    display: -webkit-box !important;
    display: -webkit-flex !important;
    display: -ms-flexbox !important;
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-md .navbar-toggler {
    display: none;
  }
}
@media (max-width: 1199.98px) {
  .navbar-expand-lg > .container,
.navbar-expand-lg > .container-fluid,
.navbar-expand-lg > .container-sm,
.navbar-expand-lg > .container-md,
.navbar-expand-lg > .container-lg,
.navbar-expand-lg > .container-xl {
    padding-right: 0;
    padding-left: 0;
  }
}
@media (min-width: 1200px) {
  .navbar-expand-lg {
    flex-flow: row nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-lg .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-lg .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-lg .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-lg > .container,
.navbar-expand-lg > .container-fluid,
.navbar-expand-lg > .container-sm,
.navbar-expand-lg > .container-md,
.navbar-expand-lg > .container-lg,
.navbar-expand-lg > .container-xl {
    flex-wrap: nowrap;
  }
  .navbar-expand-lg .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-lg .navbar-collapse {
    display: -webkit-box !important;
    display: -webkit-flex !important;
    display: -ms-flexbox !important;
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-lg .navbar-toggler {
    display: none;
  }
}
@media (max-width: 1579.98px) {
  .navbar-expand-xl > .container,
.navbar-expand-xl > .container-fluid,
.navbar-expand-xl > .container-sm,
.navbar-expand-xl > .container-md,
.navbar-expand-xl > .container-lg,
.navbar-expand-xl > .container-xl {
    padding-right: 0;
    padding-left: 0;
  }
}
@media (min-width: 1580px) {
  .navbar-expand-xl {
    flex-flow: row nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-xl .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-xl .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-xl .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-xl > .container,
.navbar-expand-xl > .container-fluid,
.navbar-expand-xl > .container-sm,
.navbar-expand-xl > .container-md,
.navbar-expand-xl > .container-lg,
.navbar-expand-xl > .container-xl {
    flex-wrap: nowrap;
  }
  .navbar-expand-xl .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-xl .navbar-collapse {
    display: -webkit-box !important;
    display: -webkit-flex !important;
    display: -ms-flexbox !important;
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-xl .navbar-toggler {
    display: none;
  }
}
.navbar-expand {
  flex-flow: row nowrap;
  justify-content: flex-start;
}
.navbar-expand > .container,
.navbar-expand > .container-fluid,
.navbar-expand > .container-sm,
.navbar-expand > .container-md,
.navbar-expand > .container-lg,
.navbar-expand > .container-xl {
  padding-right: 0;
  padding-left: 0;
}
.navbar-expand .navbar-nav {
  flex-direction: row;
}
.navbar-expand .navbar-nav .dropdown-menu {
  position: absolute;
}
.navbar-expand .navbar-nav .nav-link {
  padding-right: 0.5rem;
  padding-left: 0.5rem;
}
.navbar-expand > .container,
.navbar-expand > .container-fluid,
.navbar-expand > .container-sm,
.navbar-expand > .container-md,
.navbar-expand > .container-lg,
.navbar-expand > .container-xl {
  flex-wrap: nowrap;
}
.navbar-expand .navbar-nav-scroll {
  overflow: visible;
}
.navbar-expand .navbar-collapse {
  display: -webkit-box !important;
  display: -webkit-flex !important;
  display: -ms-flexbox !important;
  display: flex !important;
  flex-basis: auto;
}
.navbar-expand .navbar-toggler {
  display: none;
}

.navbar-light .navbar-brand {
  color: rgba(0, 0, 0, 0.9);
}
.navbar-light .navbar-brand:hover, .navbar-light .navbar-brand:focus {
  color: rgba(0, 0, 0, 0.9);
}
.navbar-light .navbar-nav .nav-link {
  color: rgba(0, 0, 0, 0.5);
}
.navbar-light .navbar-nav .nav-link:hover, .navbar-light .navbar-nav .nav-link:focus {
  color: rgba(0, 0, 0, 0.7);
}
.navbar-light .navbar-nav .nav-link.disabled {
  color: rgba(0, 0, 0, 0.3);
}
.navbar-light .navbar-nav .show > .nav-link,
.navbar-light .navbar-nav .active > .nav-link,
.navbar-light .navbar-nav .nav-link.show,
.navbar-light .navbar-nav .nav-link.active {
  color: rgba(0, 0, 0, 0.9);
}
.navbar-light .navbar-toggler {
  color: rgba(0, 0, 0, 0.5);
  border-color: rgba(0, 0, 0, 0.1);
}
.navbar-light .navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%280, 0, 0, 0.5%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}
.navbar-light .navbar-text {
  color: rgba(0, 0, 0, 0.5);
}
.navbar-light .navbar-text a {
  color: rgba(0, 0, 0, 0.9);
}
.navbar-light .navbar-text a:hover, .navbar-light .navbar-text a:focus {
  color: rgba(0, 0, 0, 0.9);
}

.navbar-dark .navbar-brand {
  color: #fff;
}
.navbar-dark .navbar-brand:hover, .navbar-dark .navbar-brand:focus {
  color: #fff;
}
.navbar-dark .navbar-nav .nav-link {
  color: rgba(255, 255, 255, 0.5);
}
.navbar-dark .navbar-nav .nav-link:hover, .navbar-dark .navbar-nav .nav-link:focus {
  color: rgba(255, 255, 255, 0.75);
}
.navbar-dark .navbar-nav .nav-link.disabled {
  color: rgba(255, 255, 255, 0.25);
}
.navbar-dark .navbar-nav .show > .nav-link,
.navbar-dark .navbar-nav .active > .nav-link,
.navbar-dark .navbar-nav .nav-link.show,
.navbar-dark .navbar-nav .nav-link.active {
  color: #fff;
}
.navbar-dark .navbar-toggler {
  color: rgba(255, 255, 255, 0.5);
  border-color: rgba(255, 255, 255, 0.1);
}
.navbar-dark .navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.5%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}
.navbar-dark .navbar-text {
  color: rgba(255, 255, 255, 0.5);
}
.navbar-dark .navbar-text a {
  color: #fff;
}
.navbar-dark .navbar-text a:hover, .navbar-dark .navbar-text a:focus {
  color: #fff;
}

.card {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: border-box;
  border: 1px solid rgba(0, 0, 0, 0.125);
  border-radius: 0.125rem;
}
.card > hr {
  margin-right: 0;
  margin-left: 0;
}
.card > .list-group {
  border-top: inherit;
  border-bottom: inherit;
}
.card > .list-group:first-child {
  border-top-width: 0;
  border-top-left-radius: -webkit-calc(0.125rem - 1px);
  border-top-left-radius: calc(0.125rem - 1px);
  border-top-right-radius: -webkit-calc(0.125rem - 1px);
  border-top-right-radius: calc(0.125rem - 1px);
}
.card > .list-group:last-child {
  border-bottom-width: 0;
  border-bottom-right-radius: -webkit-calc(0.125rem - 1px);
  border-bottom-right-radius: calc(0.125rem - 1px);
  border-bottom-left-radius: -webkit-calc(0.125rem - 1px);
  border-bottom-left-radius: calc(0.125rem - 1px);
}
.card > .card-header + .list-group,
.card > .list-group + .card-footer {
  border-top: 0;
}

.card-body {
  flex: 1 1 auto;
  min-height: 1px;
  padding: 1.25rem;
}

.card-title {
  margin-bottom: 0.75rem;
}

.card-subtitle {
  margin-top: -0.375rem;
  margin-bottom: 0;
}

.card-text:last-child {
  margin-bottom: 0;
}

.card-link:hover {
  text-decoration: none;
}
.card-link + .card-link {
  margin-left: 1.25rem;
}

.card-header {
  padding: 0.75rem 1.25rem;
  margin-bottom: 0;
  background-color: rgba(0, 0, 0, 0.03);
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}
.card-header:first-child {
  border-radius: -webkit-calc(0.125rem - 1px) -webkit-calc(0.125rem - 1px) 0 0;
  border-radius: calc(0.125rem - 1px) calc(0.125rem - 1px) 0 0;
}

.card-footer {
  padding: 0.75rem 1.25rem;
  background-color: rgba(0, 0, 0, 0.03);
  border-top: 1px solid rgba(0, 0, 0, 0.125);
}
.card-footer:last-child {
  border-radius: 0 0 -webkit-calc(0.125rem - 1px) -webkit-calc(0.125rem - 1px);
  border-radius: 0 0 calc(0.125rem - 1px) calc(0.125rem - 1px);
}

.card-header-tabs {
  margin-right: -0.625rem;
  margin-bottom: -0.75rem;
  margin-left: -0.625rem;
  border-bottom: 0;
}

.card-header-pills {
  margin-right: -0.625rem;
  margin-left: -0.625rem;
}

.card-img-overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 1.25rem;
  border-radius: -webkit-calc(0.125rem - 1px);
  border-radius: calc(0.125rem - 1px);
}

.card-img,
.card-img-top,
.card-img-bottom {
  flex-shrink: 0;
  width: 100%;
}

.card-img,
.card-img-top {
  border-top-left-radius: -webkit-calc(0.125rem - 1px);
  border-top-left-radius: calc(0.125rem - 1px);
  border-top-right-radius: -webkit-calc(0.125rem - 1px);
  border-top-right-radius: calc(0.125rem - 1px);
}

.card-img,
.card-img-bottom {
  border-bottom-right-radius: -webkit-calc(0.125rem - 1px);
  border-bottom-right-radius: calc(0.125rem - 1px);
  border-bottom-left-radius: -webkit-calc(0.125rem - 1px);
  border-bottom-left-radius: calc(0.125rem - 1px);
}

.card-deck .card {
  margin-bottom: 1.40625rem;
}
@media (min-width: 600px) {
  .card-deck {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    flex-flow: row wrap;
    margin-right: -1.40625rem;
    margin-left: -1.40625rem;
  }
  .card-deck .card {
    flex: 1 0 0%;
    margin-right: 1.40625rem;
    margin-bottom: 0;
    margin-left: 1.40625rem;
  }
}

.card-group > .card {
  margin-bottom: 1.40625rem;
}
@media (min-width: 600px) {
  .card-group {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    flex-flow: row wrap;
  }
  .card-group > .card {
    flex: 1 0 0%;
    margin-bottom: 0;
  }
  .card-group > .card + .card {
    margin-left: 0;
    border-left: 0;
  }
  .card-group > .card:not(:last-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
  .card-group > .card:not(:last-child) .card-img-top,
.card-group > .card:not(:last-child) .card-header {
    border-top-right-radius: 0;
  }
  .card-group > .card:not(:last-child) .card-img-bottom,
.card-group > .card:not(:last-child) .card-footer {
    border-bottom-right-radius: 0;
  }
  .card-group > .card:not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
  .card-group > .card:not(:first-child) .card-img-top,
.card-group > .card:not(:first-child) .card-header {
    border-top-left-radius: 0;
  }
  .card-group > .card:not(:first-child) .card-img-bottom,
.card-group > .card:not(:first-child) .card-footer {
    border-bottom-left-radius: 0;
  }
}

.card-columns .card {
  margin-bottom: 0.75rem;
}
@media (min-width: 600px) {
  .card-columns {
    -moz-column-count: 3;
         column-count: 3;
    -moz-column-gap: 1.25rem;
         column-gap: 1.25rem;
    orphans: 1;
    widows: 1;
  }
  .card-columns .card {
    display: inline-block;
    width: 100%;
  }
}

.accordion {
  overflow-anchor: none;
}
.accordion > .card {
  overflow: hidden;
}
.accordion > .card:not(:last-of-type) {
  border-bottom: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.accordion > .card:not(:first-of-type) {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.accordion > .card > .card-header {
  border-radius: 0;
  margin-bottom: -1px;
}

.breadcrumb {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  flex-wrap: wrap;
  padding: 0 0;
  margin-bottom: 0;
  list-style: none;
  background-color: none;
  border-radius: 0;
}

.breadcrumb-item + .breadcrumb-item {
  padding-left: 0.5rem;
}
.breadcrumb-item + .breadcrumb-item::before {
  float: left;
  padding-right: 0.5rem;
  color: #6c757d;
  content: "\E930";
}
.breadcrumb-item + .breadcrumb-item:hover::before {
  text-decoration: underline;
}
.breadcrumb-item + .breadcrumb-item:hover::before {
  text-decoration: none;
}
.breadcrumb-item.active {
  color: #5e6983;
}

.pagination {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding-left: 0;
  list-style: none;
  border-radius: 0.125rem;
}

.page-link {
  position: relative;
  display: block;
  padding: 0.5rem 0.75rem;
  margin-left: -1px;
  line-height: 1.3125;
  color: #5e6983;
  background-color: transparent;
  border: 1px solid transparent;
}
.page-link:hover {
  z-index: 2;
  color: rgba(94, 105, 131, 0.4);
  text-decoration: none;
  background-color: transparent;
  border-color: transparent;
}
.page-link:focus {
  z-index: 3;
  outline: 0;
  box-shadow: none;
}

.page-item:first-child .page-link {
  margin-left: 0;
  border-top-left-radius: 0.125rem;
  border-bottom-left-radius: 0.125rem;
}
.page-item:last-child .page-link {
  border-top-right-radius: 0.125rem;
  border-bottom-right-radius: 0.125rem;
}
.page-item.active .page-link {
  z-index: 3;
  color: #2e6da4;
  background-color: transparent;
  border-color: #d8dce6;
}
.page-item.disabled .page-link {
  color: #d8dce6;
  pointer-events: none;
  cursor: auto;
  background-color: transparent;
  border-color: transparent;
}

.pagination-lg .page-link {
  padding: 0.75rem 1.5rem;
  font-size: 1.09375rem;
  line-height: 1.5;
}
.pagination-lg .page-item:first-child .page-link {
  border-top-left-radius: 0.3rem;
  border-bottom-left-radius: 0.3rem;
}
.pagination-lg .page-item:last-child .page-link {
  border-top-right-radius: 0.3rem;
  border-bottom-right-radius: 0.3rem;
}

.pagination-sm .page-link {
  padding: 0.25rem 0.5rem;
  font-size: 0.765625rem;
  line-height: 1.5;
}
.pagination-sm .page-item:first-child .page-link {
  border-top-left-radius: 0.2rem;
  border-bottom-left-radius: 0.2rem;
}
.pagination-sm .page-item:last-child .page-link {
  border-top-right-radius: 0.2rem;
  border-bottom-right-radius: 0.2rem;
}

.badge, .select2-container--default .select2-selection--multiple .select2-selection__choice {
  display: inline-block;
  padding: 0.6em 0.4em;
  font-size: 0.625rem;
  font-weight: 700;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.125rem;
  transition: none;
}
a.badge:hover, .select2-container--default .select2-selection--multiple a.select2-selection__choice:hover, a.badge:focus, .select2-container--default .select2-selection--multiple a.select2-selection__choice:focus {
  text-decoration: none;
}

.badge:empty, .select2-container--default .select2-selection--multiple .select2-selection__choice:empty {
  display: none;
}

.btn .badge, .btn .select2-container--default .select2-selection--multiple .select2-selection__choice, .select2-container--default .select2-selection--multiple .btn .select2-selection__choice, #ffFormCreate #ffForm div ul li a .badge, #ffFormCreate #ffForm div ul li a .select2-container--default .select2-selection--multiple .select2-selection__choice, .select2-container--default .select2-selection--multiple #ffFormCreate #ffForm div ul li a .select2-selection__choice {
  position: relative;
  top: -1px;
}

.badge-pill, .select2-container--default .select2-selection--multiple .select2-selection__choice {
  padding-right: 1.5em;
  padding-left: 1.5em;
  border-radius: 10rem;
}

.badge-primary {
  color: #fff;
  background-color: #337ab7;
}
a.badge-primary:hover, a.badge-primary:focus {
  color: #fff;
  background-color: #285f8f;
}
a.badge-primary:focus, a.badge-primary.focus {
  outline: 0;
  box-shadow: 0 0 0 0 rgba(51, 122, 183, 0.5);
}

.badge-secondary {
  color: #fff;
  background-color: #002663;
}
a.badge-secondary:hover, a.badge-secondary:focus {
  color: #fff;
  background-color: #001230;
}
a.badge-secondary:focus, a.badge-secondary.focus {
  outline: 0;
  box-shadow: 0 0 0 0 rgba(0, 38, 99, 0.5);
}

.badge-success {
  color: #212529;
  background-color: #64d091;
}
a.badge-success:hover, a.badge-success:focus {
  color: #212529;
  background-color: #3dc475;
}
a.badge-success:focus, a.badge-success.focus {
  outline: 0;
  box-shadow: 0 0 0 0 rgba(100, 208, 145, 0.5);
}

.badge-info {
  color: #fff;
  background-color: #388695;
}
a.badge-info:hover, a.badge-info:focus {
  color: #fff;
  background-color: #2a6570;
}
a.badge-info:focus, a.badge-info.focus {
  outline: 0;
  box-shadow: 0 0 0 0 rgba(56, 134, 149, 0.5);
}

.badge-warning {
  color: #212529;
  background-color: #DB9E5B;
}
a.badge-warning:hover, a.badge-warning:focus {
  color: #212529;
  background-color: #d28531;
}
a.badge-warning:focus, a.badge-warning.focus {
  outline: 0;
  box-shadow: 0 0 0 0 rgba(219, 158, 91, 0.5);
}

.badge-danger {
  color: #fff;
  background-color: #cc6565;
}
a.badge-danger:hover, a.badge-danger:focus {
  color: #fff;
  background-color: #bf3f3f;
}
a.badge-danger:focus, a.badge-danger.focus {
  outline: 0;
  box-shadow: 0 0 0 0 rgba(204, 101, 101, 0.5);
}

.badge-light, .select2-container--default .select2-selection--multiple .select2-selection__choice {
  color: #212529;
  background-color: #d8dce6;
}
a.badge-light:hover, .select2-container--default .select2-selection--multiple a.select2-selection__choice:hover, a.badge-light:focus, .select2-container--default .select2-selection--multiple a.select2-selection__choice:focus {
  color: #212529;
  background-color: #b9c0d2;
}
a.badge-light:focus, .select2-container--default .select2-selection--multiple a.select2-selection__choice:focus, a.badge-light.focus, .select2-container--default .select2-selection--multiple a.focus.select2-selection__choice {
  outline: 0;
  box-shadow: 0 0 0 0 rgba(216, 220, 230, 0.5);
}

.badge-dark {
  color: #fff;
  background-color: #5e6983;
}
a.badge-dark:hover, a.badge-dark:focus {
  color: #fff;
  background-color: #495165;
}
a.badge-dark:focus, a.badge-dark.focus {
  outline: 0;
  box-shadow: 0 0 0 0 rgba(94, 105, 131, 0.5);
}

.badge-lighter {
  color: #212529;
  background-color: #fafafc;
}
a.badge-lighter:hover, a.badge-lighter:focus {
  color: #212529;
  background-color: #dadae9;
}
a.badge-lighter:focus, a.badge-lighter.focus {
  outline: 0;
  box-shadow: 0 0 0 0 rgba(250, 250, 252, 0.5);
}

.badge-lighter-blue {
  color: #212529;
  background-color: #f4f8fc;
}
a.badge-lighter-blue:hover, a.badge-lighter-blue:focus {
  color: #212529;
  background-color: #ccdff1;
}
a.badge-lighter-blue:focus, a.badge-lighter-blue.focus {
  outline: 0;
  box-shadow: 0 0 0 0 rgba(244, 248, 252, 0.5);
}

.jumbotron {
  padding: 2rem 1rem;
  margin-bottom: 2rem;
  background-color: #e9ecef;
  border-radius: 0.3rem;
}
@media (min-width: 600px) {
  .jumbotron {
    padding: 4rem 2rem;
  }
}

.jumbotron-fluid {
  padding-right: 0;
  padding-left: 0;
  border-radius: 0;
}

.alert {
  position: relative;
  padding: 1.5rem 1.5rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: 0.125rem;
}

.alert-heading {
  color: inherit;
}

.alert-link {
  font-weight: 700;
}

.alert-dismissible {
  padding-right: 4.3125rem;
}
.alert-dismissible .close {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 2;
  padding: 1.5rem 1.5rem;
  color: inherit;
}

.alert-primary {
  color: #1b3f5f;
  background-color: #d6e4f1;
  border-color: #c6daeb;
}
.alert-primary hr {
  border-top-color: #b3cee4;
}
.alert-primary .alert-link {
  color: #102537;
}

.alert-secondary {
  color: #001433;
  background-color: #ccd4e0;
  border-color: #b8c2d3;
}
.alert-secondary hr {
  border-top-color: #a8b4c9;
}
.alert-secondary .alert-link {
  color: black;
}

.alert-success {
  color: #346c4b;
  background-color: #e0f6e9;
  border-color: #d4f2e0;
}
.alert-success hr {
  border-top-color: #c0ecd2;
}
.alert-success .alert-link {
  color: #234a33;
}

.alert-info {
  color: #1d464d;
  background-color: #d7e7ea;
  border-color: #c7dde1;
}
.alert-info hr {
  border-top-color: #b6d3d8;
}
.alert-info .alert-link {
  color: #0f2428;
}

.alert-warning {
  color: #72522f;
  background-color: #f8ecde;
  border-color: #f5e4d1;
}
.alert-warning hr {
  border-top-color: #f0d8bc;
}
.alert-warning .alert-link {
  color: #4e3820;
}

.alert-danger {
  color: #6a3535;
  background-color: #f5e0e0;
  border-color: #f1d4d4;
}
.alert-danger hr {
  border-top-color: #ebc1c1;
}
.alert-danger .alert-link {
  color: #482424;
}

.alert-light {
  color: #707278;
  background-color: #f7f8fa;
  border-color: #f4f5f8;
}
.alert-light hr {
  border-top-color: #e4e7ee;
}
.alert-light .alert-link {
  color: #57595e;
}

.alert-dark {
  color: #313744;
  background-color: #dfe1e6;
  border-color: #d2d5dc;
}
.alert-dark hr {
  border-top-color: #c4c8d1;
}
.alert-dark .alert-link {
  color: #1c1f26;
}

.alert-lighter {
  color: #828283;
  background-color: #fefefe;
  border-color: #fefefe;
}
.alert-lighter hr {
  border-top-color: #f1f1f1;
}
.alert-lighter .alert-link {
  color: dimgray;
}

.alert-lighter-blue {
  color: #7f8183;
  background-color: #fdfefe;
  border-color: #fcfdfe;
}
.alert-lighter-blue hr {
  border-top-color: #e9f0f8;
}
.alert-lighter-blue .alert-link {
  color: #666869;
}

@keyframes progress-bar-stripes {
  from {
    background-position: 1rem 0;
  }
  to {
    background-position: 0 0;
  }
}
.progress {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  height: 1rem;
  overflow: hidden;
  line-height: 0;
  font-size: 0.65625rem;
  background-color: #e9ecef;
  border-radius: 0.125rem;
}

.progress-bar {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  background-color: #337ab7;
  transition: width 0.6s ease;
}
@media (prefers-reduced-motion: reduce) {
  .progress-bar {
    transition: none;
  }
}

.progress-bar-striped {
  background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 1rem 1rem;
}

.progress-bar-animated {
  animation: 1s linear infinite progress-bar-stripes;
}
@media (prefers-reduced-motion: reduce) {
  .progress-bar-animated {
    animation: none;
  }
}

.media {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: flex-start;
}

.media-body {
  flex: 1;
}

.list-group {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  flex-direction: column;
  padding-left: 0;
  margin-bottom: 0;
  border-radius: 0.125rem;
}

.list-group-item-action {
  width: 100%;
  color: #495057;
  text-align: inherit;
}
.list-group-item-action:hover, .list-group-item-action:focus {
  z-index: 1;
  color: #495057;
  text-decoration: none;
  background-color: #f8f9fa;
}
.list-group-item-action:active {
  color: #5e6983;
  background-color: #e9ecef;
}

.list-group-item {
  position: relative;
  display: block;
  padding: 0.75rem 1.25rem;
  background-color: #fff;
  border: 1px solid rgba(0, 0, 0, 0.125);
}
.list-group-item:first-child {
  border-top-left-radius: inherit;
  border-top-right-radius: inherit;
}
.list-group-item:last-child {
  border-bottom-right-radius: inherit;
  border-bottom-left-radius: inherit;
}
.list-group-item.disabled, .list-group-item:disabled {
  color: #6c757d;
  pointer-events: none;
  background-color: #fff;
}
.list-group-item.active {
  z-index: 2;
  color: #fff;
  background-color: #337ab7;
  border-color: #337ab7;
}
.list-group-item + .list-group-item {
  border-top-width: 0;
}
.list-group-item + .list-group-item.active {
  margin-top: -1px;
  border-top-width: 1px;
}

.list-group-horizontal {
  flex-direction: row;
}
.list-group-horizontal > .list-group-item:first-child {
  border-bottom-left-radius: 0.125rem;
  border-top-right-radius: 0;
}
.list-group-horizontal > .list-group-item:last-child {
  border-top-right-radius: 0.125rem;
  border-bottom-left-radius: 0;
}
.list-group-horizontal > .list-group-item.active {
  margin-top: 0;
}
.list-group-horizontal > .list-group-item + .list-group-item {
  border-top-width: 1px;
  border-left-width: 0;
}
.list-group-horizontal > .list-group-item + .list-group-item.active {
  margin-left: -1px;
  border-left-width: 1px;
}

@media (min-width: 600px) {
  .list-group-horizontal-sm {
    flex-direction: row;
  }
  .list-group-horizontal-sm > .list-group-item:first-child {
    border-bottom-left-radius: 0.125rem;
    border-top-right-radius: 0;
  }
  .list-group-horizontal-sm > .list-group-item:last-child {
    border-top-right-radius: 0.125rem;
    border-bottom-left-radius: 0;
  }
  .list-group-horizontal-sm > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-sm > .list-group-item + .list-group-item {
    border-top-width: 1px;
    border-left-width: 0;
  }
  .list-group-horizontal-sm > .list-group-item + .list-group-item.active {
    margin-left: -1px;
    border-left-width: 1px;
  }
}
@media (min-width: 980px) {
  .list-group-horizontal-md {
    flex-direction: row;
  }
  .list-group-horizontal-md > .list-group-item:first-child {
    border-bottom-left-radius: 0.125rem;
    border-top-right-radius: 0;
  }
  .list-group-horizontal-md > .list-group-item:last-child {
    border-top-right-radius: 0.125rem;
    border-bottom-left-radius: 0;
  }
  .list-group-horizontal-md > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-md > .list-group-item + .list-group-item {
    border-top-width: 1px;
    border-left-width: 0;
  }
  .list-group-horizontal-md > .list-group-item + .list-group-item.active {
    margin-left: -1px;
    border-left-width: 1px;
  }
}
@media (min-width: 1200px) {
  .list-group-horizontal-lg {
    flex-direction: row;
  }
  .list-group-horizontal-lg > .list-group-item:first-child {
    border-bottom-left-radius: 0.125rem;
    border-top-right-radius: 0;
  }
  .list-group-horizontal-lg > .list-group-item:last-child {
    border-top-right-radius: 0.125rem;
    border-bottom-left-radius: 0;
  }
  .list-group-horizontal-lg > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-lg > .list-group-item + .list-group-item {
    border-top-width: 1px;
    border-left-width: 0;
  }
  .list-group-horizontal-lg > .list-group-item + .list-group-item.active {
    margin-left: -1px;
    border-left-width: 1px;
  }
}
@media (min-width: 1580px) {
  .list-group-horizontal-xl {
    flex-direction: row;
  }
  .list-group-horizontal-xl > .list-group-item:first-child {
    border-bottom-left-radius: 0.125rem;
    border-top-right-radius: 0;
  }
  .list-group-horizontal-xl > .list-group-item:last-child {
    border-top-right-radius: 0.125rem;
    border-bottom-left-radius: 0;
  }
  .list-group-horizontal-xl > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-xl > .list-group-item + .list-group-item {
    border-top-width: 1px;
    border-left-width: 0;
  }
  .list-group-horizontal-xl > .list-group-item + .list-group-item.active {
    margin-left: -1px;
    border-left-width: 1px;
  }
}
.list-group-flush {
  border-radius: 0;
}
.list-group-flush > .list-group-item {
  border-width: 0 0 1px;
}
.list-group-flush > .list-group-item:last-child {
  border-bottom-width: 0;
}

.list-group-item-primary {
  color: #1b3f5f;
  background-color: #c6daeb;
}
.list-group-item-primary.list-group-item-action:hover, .list-group-item-primary.list-group-item-action:focus {
  color: #1b3f5f;
  background-color: #b3cee4;
}
.list-group-item-primary.list-group-item-action.active {
  color: #fff;
  background-color: #1b3f5f;
  border-color: #1b3f5f;
}

.list-group-item-secondary {
  color: #001433;
  background-color: #b8c2d3;
}
.list-group-item-secondary.list-group-item-action:hover, .list-group-item-secondary.list-group-item-action:focus {
  color: #001433;
  background-color: #a8b4c9;
}
.list-group-item-secondary.list-group-item-action.active {
  color: #fff;
  background-color: #001433;
  border-color: #001433;
}

.list-group-item-success {
  color: #346c4b;
  background-color: #d4f2e0;
}
.list-group-item-success.list-group-item-action:hover, .list-group-item-success.list-group-item-action:focus {
  color: #346c4b;
  background-color: #c0ecd2;
}
.list-group-item-success.list-group-item-action.active {
  color: #fff;
  background-color: #346c4b;
  border-color: #346c4b;
}

.list-group-item-info {
  color: #1d464d;
  background-color: #c7dde1;
}
.list-group-item-info.list-group-item-action:hover, .list-group-item-info.list-group-item-action:focus {
  color: #1d464d;
  background-color: #b6d3d8;
}
.list-group-item-info.list-group-item-action.active {
  color: #fff;
  background-color: #1d464d;
  border-color: #1d464d;
}

.list-group-item-warning {
  color: #72522f;
  background-color: #f5e4d1;
}
.list-group-item-warning.list-group-item-action:hover, .list-group-item-warning.list-group-item-action:focus {
  color: #72522f;
  background-color: #f0d8bc;
}
.list-group-item-warning.list-group-item-action.active {
  color: #fff;
  background-color: #72522f;
  border-color: #72522f;
}

.list-group-item-danger {
  color: #6a3535;
  background-color: #f1d4d4;
}
.list-group-item-danger.list-group-item-action:hover, .list-group-item-danger.list-group-item-action:focus {
  color: #6a3535;
  background-color: #ebc1c1;
}
.list-group-item-danger.list-group-item-action.active {
  color: #fff;
  background-color: #6a3535;
  border-color: #6a3535;
}

.list-group-item-light {
  color: #707278;
  background-color: #f4f5f8;
}
.list-group-item-light.list-group-item-action:hover, .list-group-item-light.list-group-item-action:focus {
  color: #707278;
  background-color: #e4e7ee;
}
.list-group-item-light.list-group-item-action.active {
  color: #fff;
  background-color: #707278;
  border-color: #707278;
}

.list-group-item-dark {
  color: #313744;
  background-color: #d2d5dc;
}
.list-group-item-dark.list-group-item-action:hover, .list-group-item-dark.list-group-item-action:focus {
  color: #313744;
  background-color: #c4c8d1;
}
.list-group-item-dark.list-group-item-action.active {
  color: #fff;
  background-color: #313744;
  border-color: #313744;
}

.list-group-item-lighter {
  color: #828283;
  background-color: #fefefe;
}
.list-group-item-lighter.list-group-item-action:hover, .list-group-item-lighter.list-group-item-action:focus {
  color: #828283;
  background-color: #f1f1f1;
}
.list-group-item-lighter.list-group-item-action.active {
  color: #fff;
  background-color: #828283;
  border-color: #828283;
}

.list-group-item-lighter-blue {
  color: #7f8183;
  background-color: #fcfdfe;
}
.list-group-item-lighter-blue.list-group-item-action:hover, .list-group-item-lighter-blue.list-group-item-action:focus {
  color: #7f8183;
  background-color: #e9f0f8;
}
.list-group-item-lighter-blue.list-group-item-action.active {
  color: #fff;
  background-color: #7f8183;
  border-color: #7f8183;
}

.close {
  float: right;
  font-size: 1.3125rem;
  font-weight: 700;
  line-height: 1;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  opacity: 0.5;
}
.close:hover {
  color: #000;
  text-decoration: none;
}
.close:not(:disabled):not(.disabled):hover, .close:not(:disabled):not(.disabled):focus {
  opacity: 0.75;
}

button.close {
  padding: 0;
  background-color: transparent;
  border: 0;
}

a.close.disabled {
  pointer-events: none;
}

.toast {
  flex-basis: 350px;
  max-width: 350px;
  font-size: 0.875rem;
  background-color: rgba(255, 255, 255, 0.85);
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
  opacity: 0;
  border-radius: 0.25rem;
}
.toast:not(:last-child) {
  margin-bottom: 0.75rem;
}
.toast.showing {
  opacity: 1;
}
.toast.show {
  display: block;
  opacity: 1;
}
.toast.hide {
  display: none;
}

.toast-header {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  color: #6c757d;
  background-color: rgba(255, 255, 255, 0.85);
  background-clip: padding-box;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  border-top-left-radius: -webkit-calc(0.25rem - 1px);
  border-top-left-radius: calc(0.25rem - 1px);
  border-top-right-radius: -webkit-calc(0.25rem - 1px);
  border-top-right-radius: calc(0.25rem - 1px);
}

.toast-body {
  padding: 0.75rem;
}

.modal-open {
  overflow: hidden;
}
.modal-open .modal {
  overflow-x: hidden;
  overflow-y: auto;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1050;
  display: none;
  width: 100%;
  height: 100%;
  overflow: hidden;
  outline: 0;
}

.modal-dialog {
  position: relative;
  width: auto;
  margin: 1.25rem;
  pointer-events: none;
}
.modal.fade .modal-dialog {
  transition: transform 0.3s ease-out;
  transform: translate(0, -50px);
}
@media (prefers-reduced-motion: reduce) {
  .modal.fade .modal-dialog {
    transition: none;
  }
}
.modal.show .modal-dialog {
  transform: none;
}
.modal.modal-static .modal-dialog {
  transform: scale(1.02);
}

.modal-dialog-scrollable {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  max-height: -webkit-calc(100% - 2.5rem);
  max-height: calc(100% - 2.5rem);
}
.modal-dialog-scrollable .modal-content {
  max-height: -webkit-calc(100vh - 2.5rem);
  max-height: calc(100vh - 2.5rem);
  overflow: hidden;
}
.modal-dialog-scrollable .modal-header,
.modal-dialog-scrollable .modal-footer {
  flex-shrink: 0;
}
.modal-dialog-scrollable .modal-body {
  overflow-y: auto;
}

.modal-dialog-centered {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  min-height: -webkit-calc(100% - 2.5rem);
  min-height: calc(100% - 2.5rem);
}
.modal-dialog-centered::before {
  display: block;
  height: -webkit-calc(100vh - 2.5rem);
  height: calc(100vh - 2.5rem);
  height: -moz-min-content;
  height: -webkit-min-content;
  height: min-content;
  content: "";
}
.modal-dialog-centered.modal-dialog-scrollable {
  flex-direction: column;
  justify-content: center;
  height: 100%;
}
.modal-dialog-centered.modal-dialog-scrollable .modal-content {
  max-height: none;
}
.modal-dialog-centered.modal-dialog-scrollable::before {
  content: none;
}

.modal-content {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  flex-direction: column;
  width: 100%;
  pointer-events: auto;
  background-color: #fff;
  background-clip: padding-box;
  border: 0 solid rgba(0, 0, 0, 0.2);
  border-radius: 0.3rem;
  outline: 0;
}

.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1040;
  width: 100vw;
  height: 100vh;
  background-color: #5e6983;
}
.modal-backdrop.fade {
  opacity: 0;
}
.modal-backdrop.show {
  opacity: 0.8;
}

.modal-header {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 1rem 1rem;
  border-bottom: 0 solid #d8dce6;
  border-top-left-radius: 0.3rem;
  border-top-right-radius: 0.3rem;
}
.modal-header .close {
  padding: 1rem 1rem;
  margin: -1rem -1rem -1rem auto;
}

.modal-title {
  margin-bottom: 0;
  line-height: 1.3125;
}

.modal-body {
  position: relative;
  flex: 1 1 auto;
  padding: 3.125rem;
}

.modal-footer {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-end;
  padding: 2.875rem;
  border-top: 0 solid #d8dce6;
  border-bottom-right-radius: 0.3rem;
  border-bottom-left-radius: 0.3rem;
}
.modal-footer > * {
  margin: 0.25rem;
}

.modal-scrollbar-measure {
  position: absolute;
  top: -9999px;
  width: 50px;
  height: 50px;
  overflow: scroll;
}

@media (min-width: 600px) {
  .modal-dialog {
    max-width: 465px;
    margin: 2.8125rem auto;
  }

  .modal-dialog-scrollable {
    max-height: -webkit-calc(100% - 5.625rem);
    max-height: calc(100% - 5.625rem);
  }
  .modal-dialog-scrollable .modal-content {
    max-height: -webkit-calc(100vh - 5.625rem);
    max-height: calc(100vh - 5.625rem);
  }

  .modal-dialog-centered {
    min-height: -webkit-calc(100% - 5.625rem);
    min-height: calc(100% - 5.625rem);
  }
  .modal-dialog-centered::before {
    height: -webkit-calc(100vh - 5.625rem);
    height: calc(100vh - 5.625rem);
    height: -moz-min-content;
    height: -webkit-min-content;
    height: min-content;
  }

  .modal-sm {
    max-width: 300px;
  }
}
@media (min-width: 1200px) {
  .modal-lg,
.modal-xl {
    max-width: 700px;
  }
}
@media (min-width: 1580px) {
  .modal-xl {
    max-width: 1140px;
  }
}
.tooltip {
  position: absolute;
  z-index: 1070;
  display: block;
  margin: 0;
  font-family: "Roboto", sans-serif, serif;
  font-style: normal;
  font-weight: 400;
  line-height: 1.3125;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  white-space: normal;
  word-spacing: normal;
  line-break: auto;
  font-size: 0.765625rem;
  word-wrap: break-word;
  opacity: 0;
}
.tooltip.show {
  opacity: 0.9;
}
.tooltip .arrow {
  position: absolute;
  display: block;
  width: 0.6rem;
  height: 0.3rem;
}
.tooltip .arrow::before {
  position: absolute;
  content: "";
  border-color: transparent;
  border-style: solid;
}

.bs-tooltip-top, .bs-tooltip-auto[x-placement^=top] {
  padding: 0.3rem 0;
}
.bs-tooltip-top .arrow, .bs-tooltip-auto[x-placement^=top] .arrow {
  bottom: 0;
}
.bs-tooltip-top .arrow::before, .bs-tooltip-auto[x-placement^=top] .arrow::before {
  top: 0;
  border-width: 0.3rem 0.3rem 0;
  border-top-color: #000;
}

.bs-tooltip-right, .bs-tooltip-auto[x-placement^=right] {
  padding: 0 0.3rem;
}
.bs-tooltip-right .arrow, .bs-tooltip-auto[x-placement^=right] .arrow {
  left: 0;
  width: 0.3rem;
  height: 0.6rem;
}
.bs-tooltip-right .arrow::before, .bs-tooltip-auto[x-placement^=right] .arrow::before {
  right: 0;
  border-width: 0.3rem 0.3rem 0.3rem 0;
  border-right-color: #000;
}

.bs-tooltip-bottom, .bs-tooltip-auto[x-placement^=bottom] {
  padding: 0.3rem 0;
}
.bs-tooltip-bottom .arrow, .bs-tooltip-auto[x-placement^=bottom] .arrow {
  top: 0;
}
.bs-tooltip-bottom .arrow::before, .bs-tooltip-auto[x-placement^=bottom] .arrow::before {
  bottom: 0;
  border-width: 0 0.3rem 0.3rem;
  border-bottom-color: #000;
}

.bs-tooltip-left, .bs-tooltip-auto[x-placement^=left] {
  padding: 0 0.3rem;
}
.bs-tooltip-left .arrow, .bs-tooltip-auto[x-placement^=left] .arrow {
  right: 0;
  width: 0.3rem;
  height: 0.6rem;
}
.bs-tooltip-left .arrow::before, .bs-tooltip-auto[x-placement^=left] .arrow::before {
  left: 0;
  border-width: 0.3rem 0 0.3rem 0.3rem;
  border-left-color: #000;
}

.tooltip-inner {
  max-width: 200px;
  padding: 0.25rem 0.5rem;
  color: #fff;
  text-align: center;
  background-color: #000;
  border-radius: 0.125rem;
}

.popover {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1060;
  display: block;
  max-width: 276px;
  font-family: "Roboto", sans-serif, serif;
  font-style: normal;
  font-weight: 400;
  line-height: 1.3125;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  white-space: normal;
  word-spacing: normal;
  line-break: auto;
  font-size: 0.765625rem;
  word-wrap: break-word;
  background-color: #061436;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 0.3rem;
}
.popover .arrow {
  position: absolute;
  display: block;
  width: 1rem;
  height: 0.5rem;
  margin: 0 0.3rem;
}
.popover .arrow::before, .popover .arrow::after {
  position: absolute;
  display: block;
  content: "";
  border-color: transparent;
  border-style: solid;
}

.bs-popover-top, .bs-popover-auto[x-placement^=top] {
  margin-bottom: 0.5rem;
}
.bs-popover-top > .arrow, .bs-popover-auto[x-placement^=top] > .arrow {
  bottom: -webkit-calc(-0.5rem - 1px);
  bottom: calc(-0.5rem - 1px);
}
.bs-popover-top > .arrow::before, .bs-popover-auto[x-placement^=top] > .arrow::before {
  bottom: 0;
  border-width: 0.5rem 0.5rem 0;
  border-top-color: rgba(0, 0, 0, 0.25);
}
.bs-popover-top > .arrow::after, .bs-popover-auto[x-placement^=top] > .arrow::after {
  bottom: 1px;
  border-width: 0.5rem 0.5rem 0;
  border-top-color: #061436;
}

.bs-popover-right, .bs-popover-auto[x-placement^=right] {
  margin-left: 0.5rem;
}
.bs-popover-right > .arrow, .bs-popover-auto[x-placement^=right] > .arrow {
  left: -webkit-calc(-0.5rem - 1px);
  left: calc(-0.5rem - 1px);
  width: 0.5rem;
  height: 1rem;
  margin: 0.3rem 0;
}
.bs-popover-right > .arrow::before, .bs-popover-auto[x-placement^=right] > .arrow::before {
  left: 0;
  border-width: 0.5rem 0.5rem 0.5rem 0;
  border-right-color: rgba(0, 0, 0, 0.25);
}
.bs-popover-right > .arrow::after, .bs-popover-auto[x-placement^=right] > .arrow::after {
  left: 1px;
  border-width: 0.5rem 0.5rem 0.5rem 0;
  border-right-color: #061436;
}

.bs-popover-bottom, .bs-popover-auto[x-placement^=bottom] {
  margin-top: 0.5rem;
}
.bs-popover-bottom > .arrow, .bs-popover-auto[x-placement^=bottom] > .arrow {
  top: -webkit-calc(-0.5rem - 1px);
  top: calc(-0.5rem - 1px);
}
.bs-popover-bottom > .arrow::before, .bs-popover-auto[x-placement^=bottom] > .arrow::before {
  top: 0;
  border-width: 0 0.5rem 0.5rem 0.5rem;
  border-bottom-color: rgba(0, 0, 0, 0.25);
}
.bs-popover-bottom > .arrow::after, .bs-popover-auto[x-placement^=bottom] > .arrow::after {
  top: 1px;
  border-width: 0 0.5rem 0.5rem 0.5rem;
  border-bottom-color: #061436;
}
.bs-popover-bottom .popover-header::before, .bs-popover-auto[x-placement^=bottom] .popover-header::before {
  position: absolute;
  top: 0;
  left: 50%;
  display: block;
  width: 1rem;
  margin-left: -0.5rem;
  content: "";
  border-bottom: 1px solid #040f28;
}

.bs-popover-left, .bs-popover-auto[x-placement^=left] {
  margin-right: 0.5rem;
}
.bs-popover-left > .arrow, .bs-popover-auto[x-placement^=left] > .arrow {
  right: -webkit-calc(-0.5rem - 1px);
  right: calc(-0.5rem - 1px);
  width: 0.5rem;
  height: 1rem;
  margin: 0.3rem 0;
}
.bs-popover-left > .arrow::before, .bs-popover-auto[x-placement^=left] > .arrow::before {
  right: 0;
  border-width: 0.5rem 0 0.5rem 0.5rem;
  border-left-color: rgba(0, 0, 0, 0.25);
}
.bs-popover-left > .arrow::after, .bs-popover-auto[x-placement^=left] > .arrow::after {
  right: 1px;
  border-width: 0.5rem 0 0.5rem 0.5rem;
  border-left-color: #061436;
}

.popover-header {
  padding: 0.5rem 0.75rem;
  margin-bottom: 0;
  font-size: 0.875rem;
  color: #002663;
  background-color: #040f28;
  border-bottom: 1px solid #020611;
  border-top-left-radius: -webkit-calc(0.3rem - 1px);
  border-top-left-radius: calc(0.3rem - 1px);
  border-top-right-radius: -webkit-calc(0.3rem - 1px);
  border-top-right-radius: calc(0.3rem - 1px);
}
.popover-header:empty {
  display: none;
}

.popover-body {
  padding: 0.5rem 0.75rem;
  color: #5e6983;
}

.carousel {
  position: relative;
}

.carousel.pointer-event {
  touch-action: pan-y;
}

.carousel-inner {
  position: relative;
  width: 100%;
  overflow: hidden;
}
.carousel-inner::after {
  display: block;
  clear: both;
  content: "";
}

.carousel-item {
  position: relative;
  display: none;
  float: left;
  width: 100%;
  margin-right: -100%;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  transition: transform 0.6s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-item {
    transition: none;
  }
}

.carousel-item.active,
.carousel-item-next,
.carousel-item-prev {
  display: block;
}

.carousel-item-next:not(.carousel-item-left),
.active.carousel-item-right {
  transform: translateX(100%);
}

.carousel-item-prev:not(.carousel-item-right),
.active.carousel-item-left {
  transform: translateX(-100%);
}

.carousel-fade .carousel-item {
  opacity: 0;
  transition-property: opacity;
  transform: none;
}
.carousel-fade .carousel-item.active,
.carousel-fade .carousel-item-next.carousel-item-left,
.carousel-fade .carousel-item-prev.carousel-item-right {
  z-index: 1;
  opacity: 1;
}
.carousel-fade .active.carousel-item-left,
.carousel-fade .active.carousel-item-right {
  z-index: 0;
  opacity: 0;
  transition: opacity 0s 0.6s;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-fade .active.carousel-item-left,
.carousel-fade .active.carousel-item-right {
    transition: none;
  }
}

.carousel-control-prev,
.carousel-control-next {
  position: absolute;
  top: 0;
  bottom: 0;
  z-index: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 15%;
  padding: 0;
  color: #fff;
  text-align: center;
  background: none;
  border: 0;
  opacity: 0.5;
  transition: opacity 0.15s ease;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-control-prev,
.carousel-control-next {
    transition: none;
  }
}
.carousel-control-prev:hover, .carousel-control-prev:focus,
.carousel-control-next:hover,
.carousel-control-next:focus {
  color: #fff;
  text-decoration: none;
  outline: 0;
  opacity: 0.9;
}

.carousel-control-prev {
  left: 0;
}

.carousel-control-next {
  right: 0;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  background: 50%/100% 100% no-repeat;
}

.carousel-control-prev-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath d='M5.25 0l-4 4 4 4 1.5-1.5L4.25 4l2.5-2.5L5.25 0z'/%3e%3c/svg%3e");
}

.carousel-control-next-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath d='M2.75 0l-1.5 1.5L3.75 4l-2.5 2.5L2.75 8l4-4-4-4z'/%3e%3c/svg%3e");
}

.carousel-indicators {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 15;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  justify-content: center;
  padding-left: 0;
  margin-right: 15%;
  margin-left: 15%;
  list-style: none;
}
.carousel-indicators li {
  box-sizing: content-box;
  flex: 0 1 auto;
  width: 30px;
  height: 3px;
  margin-right: 3px;
  margin-left: 3px;
  text-indent: -999px;
  cursor: pointer;
  background-color: #fff;
  background-clip: padding-box;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  opacity: 0.5;
  transition: opacity 0.6s ease;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-indicators li {
    transition: none;
  }
}
.carousel-indicators .active {
  opacity: 1;
}

.carousel-caption {
  position: absolute;
  right: 15%;
  bottom: 20px;
  left: 15%;
  z-index: 10;
  padding-top: 20px;
  padding-bottom: 20px;
  color: #fff;
  text-align: center;
}

@keyframes spinner-border {
  to {
    transform: rotate(360deg);
  }
}
.spinner-border {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  vertical-align: -0.125em;
  border: 0.25em solid currentcolor;
  border-right-color: transparent;
  border-radius: 50%;
  animation: 0.75s linear infinite spinner-border;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
  border-width: 0.2em;
}

@keyframes spinner-grow {
  0% {
    transform: scale(0);
  }
  50% {
    opacity: 1;
    transform: none;
  }
}
.spinner-grow {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  vertical-align: -0.125em;
  background-color: currentcolor;
  border-radius: 50%;
  opacity: 0;
  animation: 0.75s linear infinite spinner-grow;
}

.spinner-grow-sm {
  width: 1rem;
  height: 1rem;
}

@media (prefers-reduced-motion: reduce) {
  .spinner-border,
.spinner-grow {
    animation-duration: 1.5s;
  }
}
.align-baseline {
  vertical-align: baseline !important;
}

.align-top {
  vertical-align: top !important;
}

.align-middle {
  vertical-align: middle !important;
}

.align-bottom {
  vertical-align: bottom !important;
}

.align-text-bottom {
  vertical-align: text-bottom !important;
}

.align-text-top {
  vertical-align: text-top !important;
}

.bg-primary {
  background-color: #337ab7 !important;
}

a.bg-primary:hover, a.bg-primary:focus,
button.bg-primary:hover,
button.bg-primary:focus {
  background-color: #285f8f !important;
}

.bg-secondary {
  background-color: #002663 !important;
}

a.bg-secondary:hover, a.bg-secondary:focus,
button.bg-secondary:hover,
button.bg-secondary:focus {
  background-color: #001230 !important;
}

.bg-success {
  background-color: #64d091 !important;
}

a.bg-success:hover, a.bg-success:focus,
button.bg-success:hover,
button.bg-success:focus {
  background-color: #3dc475 !important;
}

.bg-info {
  background-color: #388695 !important;
}

a.bg-info:hover, a.bg-info:focus,
button.bg-info:hover,
button.bg-info:focus {
  background-color: #2a6570 !important;
}

.bg-warning {
  background-color: #DB9E5B !important;
}

a.bg-warning:hover, a.bg-warning:focus,
button.bg-warning:hover,
button.bg-warning:focus {
  background-color: #d28531 !important;
}

.bg-danger {
  background-color: #cc6565 !important;
}

a.bg-danger:hover, a.bg-danger:focus,
button.bg-danger:hover,
button.bg-danger:focus {
  background-color: #bf3f3f !important;
}

.bg-light {
  background-color: #d8dce6 !important;
}

a.bg-light:hover, a.bg-light:focus,
button.bg-light:hover,
button.bg-light:focus {
  background-color: #b9c0d2 !important;
}

.bg-dark {
  background-color: #5e6983 !important;
}

a.bg-dark:hover, a.bg-dark:focus,
button.bg-dark:hover,
button.bg-dark:focus {
  background-color: #495165 !important;
}

.bg-lighter {
  background-color: #fafafc !important;
}

a.bg-lighter:hover, a.bg-lighter:focus,
button.bg-lighter:hover,
button.bg-lighter:focus {
  background-color: #dadae9 !important;
}

.bg-lighter-blue {
  background-color: #f4f8fc !important;
}

a.bg-lighter-blue:hover, a.bg-lighter-blue:focus,
button.bg-lighter-blue:hover,
button.bg-lighter-blue:focus {
  background-color: #ccdff1 !important;
}

.bg-white, #ffgenerated {
  background-color: #fff !important;
}

.bg-transparent {
  background-color: transparent !important;
}

.border {
  border: 1px solid #d8dce6 !important;
}

.border-top {
  border-top: 1px solid #d8dce6 !important;
}

.border-right {
  border-right: 1px solid #d8dce6 !important;
}

.border-bottom {
  border-bottom: 1px solid #d8dce6 !important;
}

.border-left {
  border-left: 1px solid #d8dce6 !important;
}

.border-0 {
  border: 0 !important;
}

.border-top-0 {
  border-top: 0 !important;
}

.border-right-0 {
  border-right: 0 !important;
}

.border-bottom-0 {
  border-bottom: 0 !important;
}

.border-left-0 {
  border-left: 0 !important;
}

.border-primary {
  border-color: #337ab7 !important;
}

.border-secondary {
  border-color: #002663 !important;
}

.border-success {
  border-color: #64d091 !important;
}

.border-info {
  border-color: #388695 !important;
}

.border-warning {
  border-color: #DB9E5B !important;
}

.border-danger {
  border-color: #cc6565 !important;
}

.border-light {
  border-color: #d8dce6 !important;
}

.border-dark {
  border-color: #5e6983 !important;
}

.border-lighter {
  border-color: #fafafc !important;
}

.border-lighter-blue {
  border-color: #f4f8fc !important;
}

.border-white {
  border-color: #fff !important;
}

.rounded-sm {
  border-radius: 0.2rem !important;
}

.rounded {
  border-radius: 0.125rem !important;
}

.rounded-top {
  border-top-left-radius: 0.125rem !important;
  border-top-right-radius: 0.125rem !important;
}

.rounded-right {
  border-top-right-radius: 0.125rem !important;
  border-bottom-right-radius: 0.125rem !important;
}

.rounded-bottom {
  border-bottom-right-radius: 0.125rem !important;
  border-bottom-left-radius: 0.125rem !important;
}

.rounded-left {
  border-top-left-radius: 0.125rem !important;
  border-bottom-left-radius: 0.125rem !important;
}

.rounded-lg {
  border-radius: 0.3rem !important;
}

.rounded-circle {
  border-radius: 50% !important;
}

.rounded-pill {
  border-radius: 50rem !important;
}

.rounded-0 {
  border-radius: 0 !important;
}

.clearfix::after {
  display: block;
  clear: both;
  content: "";
}

.d-none {
  display: none !important;
}

.d-inline {
  display: inline !important;
}

.d-inline-block {
  display: inline-block !important;
}

.d-block {
  display: block !important;
}

.d-table {
  display: table !important;
}

.d-table-row {
  display: table-row !important;
}

.d-table-cell {
  display: table-cell !important;
}

.d-flex, .page-builder__item-title.panel-title {
  display: -webkit-box !important;
  display: -webkit-flex !important;
  display: -ms-flexbox !important;
  display: flex !important;
}

.d-inline-flex {
  display: -webkit-inline-box !important;
  display: -webkit-inline-flex !important;
  display: -ms-inline-flexbox !important;
  display: inline-flex !important;
}

@media (min-width: 600px) {
  .d-sm-none {
    display: none !important;
  }

  .d-sm-inline {
    display: inline !important;
  }

  .d-sm-inline-block {
    display: inline-block !important;
  }

  .d-sm-block {
    display: block !important;
  }

  .d-sm-table {
    display: table !important;
  }

  .d-sm-table-row {
    display: table-row !important;
  }

  .d-sm-table-cell {
    display: table-cell !important;
  }

  .d-sm-flex {
    display: -webkit-box !important;
    display: -webkit-flex !important;
    display: -ms-flexbox !important;
    display: flex !important;
  }

  .d-sm-inline-flex {
    display: -webkit-inline-box !important;
    display: -webkit-inline-flex !important;
    display: -ms-inline-flexbox !important;
    display: inline-flex !important;
  }
}
@media (min-width: 980px) {
  .d-md-none {
    display: none !important;
  }

  .d-md-inline {
    display: inline !important;
  }

  .d-md-inline-block {
    display: inline-block !important;
  }

  .d-md-block {
    display: block !important;
  }

  .d-md-table {
    display: table !important;
  }

  .d-md-table-row {
    display: table-row !important;
  }

  .d-md-table-cell {
    display: table-cell !important;
  }

  .d-md-flex {
    display: -webkit-box !important;
    display: -webkit-flex !important;
    display: -ms-flexbox !important;
    display: flex !important;
  }

  .d-md-inline-flex {
    display: -webkit-inline-box !important;
    display: -webkit-inline-flex !important;
    display: -ms-inline-flexbox !important;
    display: inline-flex !important;
  }
}
@media (min-width: 1200px) {
  .d-lg-none {
    display: none !important;
  }

  .d-lg-inline {
    display: inline !important;
  }

  .d-lg-inline-block {
    display: inline-block !important;
  }

  .d-lg-block {
    display: block !important;
  }

  .d-lg-table {
    display: table !important;
  }

  .d-lg-table-row {
    display: table-row !important;
  }

  .d-lg-table-cell {
    display: table-cell !important;
  }

  .d-lg-flex {
    display: -webkit-box !important;
    display: -webkit-flex !important;
    display: -ms-flexbox !important;
    display: flex !important;
  }

  .d-lg-inline-flex {
    display: -webkit-inline-box !important;
    display: -webkit-inline-flex !important;
    display: -ms-inline-flexbox !important;
    display: inline-flex !important;
  }
}
@media (min-width: 1580px) {
  .d-xl-none {
    display: none !important;
  }

  .d-xl-inline {
    display: inline !important;
  }

  .d-xl-inline-block {
    display: inline-block !important;
  }

  .d-xl-block {
    display: block !important;
  }

  .d-xl-table {
    display: table !important;
  }

  .d-xl-table-row {
    display: table-row !important;
  }

  .d-xl-table-cell {
    display: table-cell !important;
  }

  .d-xl-flex {
    display: -webkit-box !important;
    display: -webkit-flex !important;
    display: -ms-flexbox !important;
    display: flex !important;
  }

  .d-xl-inline-flex {
    display: -webkit-inline-box !important;
    display: -webkit-inline-flex !important;
    display: -ms-inline-flexbox !important;
    display: inline-flex !important;
  }
}
@media print {
  .d-print-none {
    display: none !important;
  }

  .d-print-inline {
    display: inline !important;
  }

  .d-print-inline-block {
    display: inline-block !important;
  }

  .d-print-block {
    display: block !important;
  }

  .d-print-table {
    display: table !important;
  }

  .d-print-table-row {
    display: table-row !important;
  }

  .d-print-table-cell {
    display: table-cell !important;
  }

  .d-print-flex {
    display: -webkit-box !important;
    display: -webkit-flex !important;
    display: -ms-flexbox !important;
    display: flex !important;
  }

  .d-print-inline-flex {
    display: -webkit-inline-box !important;
    display: -webkit-inline-flex !important;
    display: -ms-inline-flexbox !important;
    display: inline-flex !important;
  }
}
.embed-responsive {
  position: relative;
  display: block;
  width: 100%;
  padding: 0;
  overflow: hidden;
}
.embed-responsive::before {
  display: block;
  content: "";
}
.embed-responsive .embed-responsive-item,
.embed-responsive iframe,
.embed-responsive embed,
.embed-responsive object,
.embed-responsive video {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
}

.embed-responsive-21by9::before {
  padding-top: 42.85714286%;
}

.embed-responsive-16by9::before {
  padding-top: 56.25%;
}

.embed-responsive-4by3::before {
  padding-top: 75%;
}

.embed-responsive-1by1::before {
  padding-top: 100%;
}

.flex-row {
  flex-direction: row !important;
}

.flex-column {
  flex-direction: column !important;
}

.flex-row-reverse {
  flex-direction: row-reverse !important;
}

.flex-column-reverse {
  flex-direction: column-reverse !important;
}

.flex-wrap {
  flex-wrap: wrap !important;
}

.flex-nowrap {
  flex-wrap: nowrap !important;
}

.flex-wrap-reverse {
  flex-wrap: wrap-reverse !important;
}

.flex-fill {
  flex: 1 1 auto !important;
}

.flex-grow-0 {
  flex-grow: 0 !important;
}

.flex-grow-1 {
  flex-grow: 1 !important;
}

.flex-shrink-0 {
  flex-shrink: 0 !important;
}

.flex-shrink-1 {
  flex-shrink: 1 !important;
}

.justify-content-start {
  justify-content: flex-start !important;
}

.justify-content-end {
  justify-content: flex-end !important;
}

.justify-content-center {
  justify-content: center !important;
}

.justify-content-between, .page-builder__item-title.panel-title {
  justify-content: space-between !important;
}

.justify-content-around {
  justify-content: space-around !important;
}

.align-items-start {
  align-items: flex-start !important;
}

.align-items-end {
  align-items: flex-end !important;
}

.align-items-center, .page-builder__item-title.panel-title {
  align-items: center !important;
}

.align-items-baseline {
  align-items: baseline !important;
}

.align-items-stretch {
  align-items: stretch !important;
}

.align-content-start {
  align-content: flex-start !important;
}

.align-content-end {
  align-content: flex-end !important;
}

.align-content-center {
  align-content: center !important;
}

.align-content-between {
  align-content: space-between !important;
}

.align-content-around {
  align-content: space-around !important;
}

.align-content-stretch {
  align-content: stretch !important;
}

.align-self-auto {
  align-self: auto !important;
}

.align-self-start {
  align-self: flex-start !important;
}

.align-self-end {
  align-self: flex-end !important;
}

.align-self-center {
  align-self: center !important;
}

.align-self-baseline {
  align-self: baseline !important;
}

.align-self-stretch {
  align-self: stretch !important;
}

@media (min-width: 600px) {
  .flex-sm-row {
    flex-direction: row !important;
  }

  .flex-sm-column {
    flex-direction: column !important;
  }

  .flex-sm-row-reverse {
    flex-direction: row-reverse !important;
  }

  .flex-sm-column-reverse {
    flex-direction: column-reverse !important;
  }

  .flex-sm-wrap {
    flex-wrap: wrap !important;
  }

  .flex-sm-nowrap {
    flex-wrap: nowrap !important;
  }

  .flex-sm-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }

  .flex-sm-fill {
    flex: 1 1 auto !important;
  }

  .flex-sm-grow-0 {
    flex-grow: 0 !important;
  }

  .flex-sm-grow-1 {
    flex-grow: 1 !important;
  }

  .flex-sm-shrink-0 {
    flex-shrink: 0 !important;
  }

  .flex-sm-shrink-1 {
    flex-shrink: 1 !important;
  }

  .justify-content-sm-start {
    justify-content: flex-start !important;
  }

  .justify-content-sm-end {
    justify-content: flex-end !important;
  }

  .justify-content-sm-center {
    justify-content: center !important;
  }

  .justify-content-sm-between {
    justify-content: space-between !important;
  }

  .justify-content-sm-around {
    justify-content: space-around !important;
  }

  .align-items-sm-start {
    align-items: flex-start !important;
  }

  .align-items-sm-end {
    align-items: flex-end !important;
  }

  .align-items-sm-center {
    align-items: center !important;
  }

  .align-items-sm-baseline {
    align-items: baseline !important;
  }

  .align-items-sm-stretch {
    align-items: stretch !important;
  }

  .align-content-sm-start {
    align-content: flex-start !important;
  }

  .align-content-sm-end {
    align-content: flex-end !important;
  }

  .align-content-sm-center {
    align-content: center !important;
  }

  .align-content-sm-between {
    align-content: space-between !important;
  }

  .align-content-sm-around {
    align-content: space-around !important;
  }

  .align-content-sm-stretch {
    align-content: stretch !important;
  }

  .align-self-sm-auto {
    align-self: auto !important;
  }

  .align-self-sm-start {
    align-self: flex-start !important;
  }

  .align-self-sm-end {
    align-self: flex-end !important;
  }

  .align-self-sm-center {
    align-self: center !important;
  }

  .align-self-sm-baseline {
    align-self: baseline !important;
  }

  .align-self-sm-stretch {
    align-self: stretch !important;
  }
}
@media (min-width: 980px) {
  .flex-md-row {
    flex-direction: row !important;
  }

  .flex-md-column {
    flex-direction: column !important;
  }

  .flex-md-row-reverse {
    flex-direction: row-reverse !important;
  }

  .flex-md-column-reverse {
    flex-direction: column-reverse !important;
  }

  .flex-md-wrap {
    flex-wrap: wrap !important;
  }

  .flex-md-nowrap {
    flex-wrap: nowrap !important;
  }

  .flex-md-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }

  .flex-md-fill {
    flex: 1 1 auto !important;
  }

  .flex-md-grow-0 {
    flex-grow: 0 !important;
  }

  .flex-md-grow-1 {
    flex-grow: 1 !important;
  }

  .flex-md-shrink-0 {
    flex-shrink: 0 !important;
  }

  .flex-md-shrink-1 {
    flex-shrink: 1 !important;
  }

  .justify-content-md-start {
    justify-content: flex-start !important;
  }

  .justify-content-md-end {
    justify-content: flex-end !important;
  }

  .justify-content-md-center {
    justify-content: center !important;
  }

  .justify-content-md-between {
    justify-content: space-between !important;
  }

  .justify-content-md-around {
    justify-content: space-around !important;
  }

  .align-items-md-start {
    align-items: flex-start !important;
  }

  .align-items-md-end {
    align-items: flex-end !important;
  }

  .align-items-md-center {
    align-items: center !important;
  }

  .align-items-md-baseline {
    align-items: baseline !important;
  }

  .align-items-md-stretch {
    align-items: stretch !important;
  }

  .align-content-md-start {
    align-content: flex-start !important;
  }

  .align-content-md-end {
    align-content: flex-end !important;
  }

  .align-content-md-center {
    align-content: center !important;
  }

  .align-content-md-between {
    align-content: space-between !important;
  }

  .align-content-md-around {
    align-content: space-around !important;
  }

  .align-content-md-stretch {
    align-content: stretch !important;
  }

  .align-self-md-auto {
    align-self: auto !important;
  }

  .align-self-md-start {
    align-self: flex-start !important;
  }

  .align-self-md-end {
    align-self: flex-end !important;
  }

  .align-self-md-center {
    align-self: center !important;
  }

  .align-self-md-baseline {
    align-self: baseline !important;
  }

  .align-self-md-stretch {
    align-self: stretch !important;
  }
}
@media (min-width: 1200px) {
  .flex-lg-row {
    flex-direction: row !important;
  }

  .flex-lg-column {
    flex-direction: column !important;
  }

  .flex-lg-row-reverse {
    flex-direction: row-reverse !important;
  }

  .flex-lg-column-reverse {
    flex-direction: column-reverse !important;
  }

  .flex-lg-wrap {
    flex-wrap: wrap !important;
  }

  .flex-lg-nowrap {
    flex-wrap: nowrap !important;
  }

  .flex-lg-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }

  .flex-lg-fill {
    flex: 1 1 auto !important;
  }

  .flex-lg-grow-0 {
    flex-grow: 0 !important;
  }

  .flex-lg-grow-1 {
    flex-grow: 1 !important;
  }

  .flex-lg-shrink-0 {
    flex-shrink: 0 !important;
  }

  .flex-lg-shrink-1 {
    flex-shrink: 1 !important;
  }

  .justify-content-lg-start {
    justify-content: flex-start !important;
  }

  .justify-content-lg-end {
    justify-content: flex-end !important;
  }

  .justify-content-lg-center {
    justify-content: center !important;
  }

  .justify-content-lg-between {
    justify-content: space-between !important;
  }

  .justify-content-lg-around {
    justify-content: space-around !important;
  }

  .align-items-lg-start {
    align-items: flex-start !important;
  }

  .align-items-lg-end {
    align-items: flex-end !important;
  }

  .align-items-lg-center {
    align-items: center !important;
  }

  .align-items-lg-baseline {
    align-items: baseline !important;
  }

  .align-items-lg-stretch {
    align-items: stretch !important;
  }

  .align-content-lg-start {
    align-content: flex-start !important;
  }

  .align-content-lg-end {
    align-content: flex-end !important;
  }

  .align-content-lg-center {
    align-content: center !important;
  }

  .align-content-lg-between {
    align-content: space-between !important;
  }

  .align-content-lg-around {
    align-content: space-around !important;
  }

  .align-content-lg-stretch {
    align-content: stretch !important;
  }

  .align-self-lg-auto {
    align-self: auto !important;
  }

  .align-self-lg-start {
    align-self: flex-start !important;
  }

  .align-self-lg-end {
    align-self: flex-end !important;
  }

  .align-self-lg-center {
    align-self: center !important;
  }

  .align-self-lg-baseline {
    align-self: baseline !important;
  }

  .align-self-lg-stretch {
    align-self: stretch !important;
  }
}
@media (min-width: 1580px) {
  .flex-xl-row {
    flex-direction: row !important;
  }

  .flex-xl-column {
    flex-direction: column !important;
  }

  .flex-xl-row-reverse {
    flex-direction: row-reverse !important;
  }

  .flex-xl-column-reverse {
    flex-direction: column-reverse !important;
  }

  .flex-xl-wrap {
    flex-wrap: wrap !important;
  }

  .flex-xl-nowrap {
    flex-wrap: nowrap !important;
  }

  .flex-xl-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }

  .flex-xl-fill {
    flex: 1 1 auto !important;
  }

  .flex-xl-grow-0 {
    flex-grow: 0 !important;
  }

  .flex-xl-grow-1 {
    flex-grow: 1 !important;
  }

  .flex-xl-shrink-0 {
    flex-shrink: 0 !important;
  }

  .flex-xl-shrink-1 {
    flex-shrink: 1 !important;
  }

  .justify-content-xl-start {
    justify-content: flex-start !important;
  }

  .justify-content-xl-end {
    justify-content: flex-end !important;
  }

  .justify-content-xl-center {
    justify-content: center !important;
  }

  .justify-content-xl-between {
    justify-content: space-between !important;
  }

  .justify-content-xl-around {
    justify-content: space-around !important;
  }

  .align-items-xl-start {
    align-items: flex-start !important;
  }

  .align-items-xl-end {
    align-items: flex-end !important;
  }

  .align-items-xl-center {
    align-items: center !important;
  }

  .align-items-xl-baseline {
    align-items: baseline !important;
  }

  .align-items-xl-stretch {
    align-items: stretch !important;
  }

  .align-content-xl-start {
    align-content: flex-start !important;
  }

  .align-content-xl-end {
    align-content: flex-end !important;
  }

  .align-content-xl-center {
    align-content: center !important;
  }

  .align-content-xl-between {
    align-content: space-between !important;
  }

  .align-content-xl-around {
    align-content: space-around !important;
  }

  .align-content-xl-stretch {
    align-content: stretch !important;
  }

  .align-self-xl-auto {
    align-self: auto !important;
  }

  .align-self-xl-start {
    align-self: flex-start !important;
  }

  .align-self-xl-end {
    align-self: flex-end !important;
  }

  .align-self-xl-center {
    align-self: center !important;
  }

  .align-self-xl-baseline {
    align-self: baseline !important;
  }

  .align-self-xl-stretch {
    align-self: stretch !important;
  }
}
.float-left {
  float: left !important;
}

.float-right {
  float: right !important;
}

.float-none {
  float: none !important;
}

@media (min-width: 600px) {
  .float-sm-left {
    float: left !important;
  }

  .float-sm-right {
    float: right !important;
  }

  .float-sm-none {
    float: none !important;
  }
}
@media (min-width: 980px) {
  .float-md-left {
    float: left !important;
  }

  .float-md-right {
    float: right !important;
  }

  .float-md-none {
    float: none !important;
  }
}
@media (min-width: 1200px) {
  .float-lg-left {
    float: left !important;
  }

  .float-lg-right {
    float: right !important;
  }

  .float-lg-none {
    float: none !important;
  }
}
@media (min-width: 1580px) {
  .float-xl-left {
    float: left !important;
  }

  .float-xl-right {
    float: right !important;
  }

  .float-xl-none {
    float: none !important;
  }
}
.user-select-all {
  -webkit-user-select: all !important;
     -moz-user-select: all !important;
          user-select: all !important;
}

.user-select-auto {
  -webkit-user-select: auto !important;
     -moz-user-select: auto !important;
          user-select: auto !important;
}

.user-select-none {
  -webkit-user-select: none !important;
     -moz-user-select: none !important;
          user-select: none !important;
}

.overflow-auto {
  overflow: auto !important;
}

.overflow-hidden {
  overflow: hidden !important;
}

.position-static {
  position: static !important;
}

.position-relative {
  position: relative !important;
}

.position-absolute {
  position: absolute !important;
}

.position-fixed {
  position: fixed !important;
}

.position-sticky {
  position: -webkit-sticky !important;
  position: sticky !important;
}

.fixed-top {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1030;
}

.fixed-bottom {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1030;
}

@supports (position: sticky) {
  .sticky-top {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 1020;
  }
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.sr-only-focusable:active, .sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

.shadow-sm {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.shadow {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.shadow-lg {
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

.shadow-none {
  box-shadow: none !important;
}

.w-25 {
  width: 25% !important;
}

.w-50 {
  width: 50% !important;
}

.w-75 {
  width: 75% !important;
}

.w-100 {
  width: 100% !important;
}

.w-auto {
  width: auto !important;
}

.h-25 {
  height: 25% !important;
}

.h-50 {
  height: 50% !important;
}

.h-75 {
  height: 75% !important;
}

.h-100 {
  height: 100% !important;
}

.h-auto {
  height: auto !important;
}

.mw-100 {
  max-width: 100% !important;
}

.mh-100 {
  max-height: 100% !important;
}

.min-vw-100 {
  min-width: 100vw !important;
}

.min-vh-100 {
  min-height: 100vh !important;
}

.vw-100 {
  width: 100vw !important;
}

.vh-100 {
  height: 100vh !important;
}

.m-0 {
  margin: 0 !important;
}

.mt-0,
.my-0 {
  margin-top: 0 !important;
}

.mr-0,
.mx-0 {
  margin-right: 0 !important;
}

.mb-0,
.my-0 {
  margin-bottom: 0 !important;
}

.ml-0,
.mx-0 {
  margin-left: 0 !important;
}

.m-1 {
  margin: 0.25rem !important;
}

.mt-1,
.my-1 {
  margin-top: 0.25rem !important;
}

.mr-1,
.mx-1 {
  margin-right: 0.25rem !important;
}

.mb-1,
.my-1 {
  margin-bottom: 0.25rem !important;
}

.ml-1,
.mx-1 {
  margin-left: 0.25rem !important;
}

.m-2 {
  margin: 0.5rem !important;
}

.mt-2,
.my-2 {
  margin-top: 0.5rem !important;
}

.mr-2,
.mx-2 {
  margin-right: 0.5rem !important;
}

.mb-2,
.my-2 {
  margin-bottom: 0.5rem !important;
}

.ml-2,
.mx-2 {
  margin-left: 0.5rem !important;
}

.m-3 {
  margin: 1rem !important;
}

.mt-3,
.my-3 {
  margin-top: 1rem !important;
}

.mr-3,
.mx-3 {
  margin-right: 1rem !important;
}

.mb-3,
.my-3 {
  margin-bottom: 1rem !important;
}

.ml-3,
.mx-3 {
  margin-left: 1rem !important;
}

.m-4 {
  margin: 1.5rem !important;
}

.mt-4,
.my-4 {
  margin-top: 1.5rem !important;
}

.mr-4,
.mx-4 {
  margin-right: 1.5rem !important;
}

.mb-4,
.my-4 {
  margin-bottom: 1.5rem !important;
}

.ml-4,
.mx-4 {
  margin-left: 1.5rem !important;
}

.m-5 {
  margin: 3rem !important;
}

.mt-5,
.my-5 {
  margin-top: 3rem !important;
}

.mr-5,
.mx-5 {
  margin-right: 3rem !important;
}

.mb-5,
.my-5 {
  margin-bottom: 3rem !important;
}

.ml-5,
.mx-5 {
  margin-left: 3rem !important;
}

.p-0, #ffgenerated {
  padding: 0 !important;
}

.pt-0,
.py-0 {
  padding-top: 0 !important;
}

.pr-0,
.px-0 {
  padding-right: 0 !important;
}

.pb-0,
.py-0 {
  padding-bottom: 0 !important;
}

.pl-0,
.px-0 {
  padding-left: 0 !important;
}

.p-1 {
  padding: 0.25rem !important;
}

.pt-1,
.py-1 {
  padding-top: 0.25rem !important;
}

.pr-1,
.px-1 {
  padding-right: 0.25rem !important;
}

.pb-1,
.py-1 {
  padding-bottom: 0.25rem !important;
}

.pl-1,
.px-1 {
  padding-left: 0.25rem !important;
}

.p-2 {
  padding: 0.5rem !important;
}

.pt-2,
.py-2 {
  padding-top: 0.5rem !important;
}

.pr-2,
.px-2 {
  padding-right: 0.5rem !important;
}

.pb-2,
.py-2 {
  padding-bottom: 0.5rem !important;
}

.pl-2,
.px-2 {
  padding-left: 0.5rem !important;
}

.p-3 {
  padding: 1rem !important;
}

.pt-3,
.py-3 {
  padding-top: 1rem !important;
}

.pr-3,
.px-3 {
  padding-right: 1rem !important;
}

.pb-3,
.py-3 {
  padding-bottom: 1rem !important;
}

.pl-3,
.px-3 {
  padding-left: 1rem !important;
}

.p-4 {
  padding: 1.5rem !important;
}

.pt-4,
.py-4 {
  padding-top: 1.5rem !important;
}

.pr-4,
.px-4 {
  padding-right: 1.5rem !important;
}

.pb-4,
.py-4 {
  padding-bottom: 1.5rem !important;
}

.pl-4,
.px-4 {
  padding-left: 1.5rem !important;
}

.p-5 {
  padding: 3rem !important;
}

.pt-5,
.py-5 {
  padding-top: 3rem !important;
}

.pr-5,
.px-5 {
  padding-right: 3rem !important;
}

.pb-5,
.py-5 {
  padding-bottom: 3rem !important;
}

.pl-5,
.px-5 {
  padding-left: 3rem !important;
}

.m-n1 {
  margin: -0.25rem !important;
}

.mt-n1,
.my-n1 {
  margin-top: -0.25rem !important;
}

.mr-n1,
.mx-n1 {
  margin-right: -0.25rem !important;
}

.mb-n1,
.my-n1 {
  margin-bottom: -0.25rem !important;
}

.ml-n1,
.mx-n1 {
  margin-left: -0.25rem !important;
}

.m-n2 {
  margin: -0.5rem !important;
}

.mt-n2,
.my-n2 {
  margin-top: -0.5rem !important;
}

.mr-n2,
.mx-n2 {
  margin-right: -0.5rem !important;
}

.mb-n2,
.my-n2 {
  margin-bottom: -0.5rem !important;
}

.ml-n2,
.mx-n2 {
  margin-left: -0.5rem !important;
}

.m-n3 {
  margin: -1rem !important;
}

.mt-n3,
.my-n3 {
  margin-top: -1rem !important;
}

.mr-n3,
.mx-n3 {
  margin-right: -1rem !important;
}

.mb-n3,
.my-n3 {
  margin-bottom: -1rem !important;
}

.ml-n3,
.mx-n3 {
  margin-left: -1rem !important;
}

.m-n4 {
  margin: -1.5rem !important;
}

.mt-n4,
.my-n4 {
  margin-top: -1.5rem !important;
}

.mr-n4,
.mx-n4 {
  margin-right: -1.5rem !important;
}

.mb-n4,
.my-n4 {
  margin-bottom: -1.5rem !important;
}

.ml-n4,
.mx-n4 {
  margin-left: -1.5rem !important;
}

.m-n5 {
  margin: -3rem !important;
}

.mt-n5,
.my-n5 {
  margin-top: -3rem !important;
}

.mr-n5,
.mx-n5 {
  margin-right: -3rem !important;
}

.mb-n5,
.my-n5 {
  margin-bottom: -3rem !important;
}

.ml-n5,
.mx-n5 {
  margin-left: -3rem !important;
}

.m-auto {
  margin: auto !important;
}

.mt-auto,
.my-auto {
  margin-top: auto !important;
}

.mr-auto,
.mx-auto {
  margin-right: auto !important;
}

.mb-auto,
.my-auto {
  margin-bottom: auto !important;
}

.ml-auto,
.mx-auto {
  margin-left: auto !important;
}

@media (min-width: 600px) {
  .m-sm-0 {
    margin: 0 !important;
  }

  .mt-sm-0,
.my-sm-0 {
    margin-top: 0 !important;
  }

  .mr-sm-0,
.mx-sm-0 {
    margin-right: 0 !important;
  }

  .mb-sm-0,
.my-sm-0 {
    margin-bottom: 0 !important;
  }

  .ml-sm-0,
.mx-sm-0 {
    margin-left: 0 !important;
  }

  .m-sm-1 {
    margin: 0.25rem !important;
  }

  .mt-sm-1,
.my-sm-1 {
    margin-top: 0.25rem !important;
  }

  .mr-sm-1,
.mx-sm-1 {
    margin-right: 0.25rem !important;
  }

  .mb-sm-1,
.my-sm-1 {
    margin-bottom: 0.25rem !important;
  }

  .ml-sm-1,
.mx-sm-1 {
    margin-left: 0.25rem !important;
  }

  .m-sm-2 {
    margin: 0.5rem !important;
  }

  .mt-sm-2,
.my-sm-2 {
    margin-top: 0.5rem !important;
  }

  .mr-sm-2,
.mx-sm-2 {
    margin-right: 0.5rem !important;
  }

  .mb-sm-2,
.my-sm-2 {
    margin-bottom: 0.5rem !important;
  }

  .ml-sm-2,
.mx-sm-2 {
    margin-left: 0.5rem !important;
  }

  .m-sm-3 {
    margin: 1rem !important;
  }

  .mt-sm-3,
.my-sm-3 {
    margin-top: 1rem !important;
  }

  .mr-sm-3,
.mx-sm-3 {
    margin-right: 1rem !important;
  }

  .mb-sm-3,
.my-sm-3 {
    margin-bottom: 1rem !important;
  }

  .ml-sm-3,
.mx-sm-3 {
    margin-left: 1rem !important;
  }

  .m-sm-4 {
    margin: 1.5rem !important;
  }

  .mt-sm-4,
.my-sm-4 {
    margin-top: 1.5rem !important;
  }

  .mr-sm-4,
.mx-sm-4 {
    margin-right: 1.5rem !important;
  }

  .mb-sm-4,
.my-sm-4 {
    margin-bottom: 1.5rem !important;
  }

  .ml-sm-4,
.mx-sm-4 {
    margin-left: 1.5rem !important;
  }

  .m-sm-5 {
    margin: 3rem !important;
  }

  .mt-sm-5,
.my-sm-5 {
    margin-top: 3rem !important;
  }

  .mr-sm-5,
.mx-sm-5 {
    margin-right: 3rem !important;
  }

  .mb-sm-5,
.my-sm-5 {
    margin-bottom: 3rem !important;
  }

  .ml-sm-5,
.mx-sm-5 {
    margin-left: 3rem !important;
  }

  .p-sm-0 {
    padding: 0 !important;
  }

  .pt-sm-0,
.py-sm-0 {
    padding-top: 0 !important;
  }

  .pr-sm-0,
.px-sm-0 {
    padding-right: 0 !important;
  }

  .pb-sm-0,
.py-sm-0 {
    padding-bottom: 0 !important;
  }

  .pl-sm-0,
.px-sm-0 {
    padding-left: 0 !important;
  }

  .p-sm-1 {
    padding: 0.25rem !important;
  }

  .pt-sm-1,
.py-sm-1 {
    padding-top: 0.25rem !important;
  }

  .pr-sm-1,
.px-sm-1 {
    padding-right: 0.25rem !important;
  }

  .pb-sm-1,
.py-sm-1 {
    padding-bottom: 0.25rem !important;
  }

  .pl-sm-1,
.px-sm-1 {
    padding-left: 0.25rem !important;
  }

  .p-sm-2 {
    padding: 0.5rem !important;
  }

  .pt-sm-2,
.py-sm-2 {
    padding-top: 0.5rem !important;
  }

  .pr-sm-2,
.px-sm-2 {
    padding-right: 0.5rem !important;
  }

  .pb-sm-2,
.py-sm-2 {
    padding-bottom: 0.5rem !important;
  }

  .pl-sm-2,
.px-sm-2 {
    padding-left: 0.5rem !important;
  }

  .p-sm-3 {
    padding: 1rem !important;
  }

  .pt-sm-3,
.py-sm-3 {
    padding-top: 1rem !important;
  }

  .pr-sm-3,
.px-sm-3 {
    padding-right: 1rem !important;
  }

  .pb-sm-3,
.py-sm-3 {
    padding-bottom: 1rem !important;
  }

  .pl-sm-3,
.px-sm-3 {
    padding-left: 1rem !important;
  }

  .p-sm-4 {
    padding: 1.5rem !important;
  }

  .pt-sm-4,
.py-sm-4 {
    padding-top: 1.5rem !important;
  }

  .pr-sm-4,
.px-sm-4 {
    padding-right: 1.5rem !important;
  }

  .pb-sm-4,
.py-sm-4 {
    padding-bottom: 1.5rem !important;
  }

  .pl-sm-4,
.px-sm-4 {
    padding-left: 1.5rem !important;
  }

  .p-sm-5 {
    padding: 3rem !important;
  }

  .pt-sm-5,
.py-sm-5 {
    padding-top: 3rem !important;
  }

  .pr-sm-5,
.px-sm-5 {
    padding-right: 3rem !important;
  }

  .pb-sm-5,
.py-sm-5 {
    padding-bottom: 3rem !important;
  }

  .pl-sm-5,
.px-sm-5 {
    padding-left: 3rem !important;
  }

  .m-sm-n1 {
    margin: -0.25rem !important;
  }

  .mt-sm-n1,
.my-sm-n1 {
    margin-top: -0.25rem !important;
  }

  .mr-sm-n1,
.mx-sm-n1 {
    margin-right: -0.25rem !important;
  }

  .mb-sm-n1,
.my-sm-n1 {
    margin-bottom: -0.25rem !important;
  }

  .ml-sm-n1,
.mx-sm-n1 {
    margin-left: -0.25rem !important;
  }

  .m-sm-n2 {
    margin: -0.5rem !important;
  }

  .mt-sm-n2,
.my-sm-n2 {
    margin-top: -0.5rem !important;
  }

  .mr-sm-n2,
.mx-sm-n2 {
    margin-right: -0.5rem !important;
  }

  .mb-sm-n2,
.my-sm-n2 {
    margin-bottom: -0.5rem !important;
  }

  .ml-sm-n2,
.mx-sm-n2 {
    margin-left: -0.5rem !important;
  }

  .m-sm-n3 {
    margin: -1rem !important;
  }

  .mt-sm-n3,
.my-sm-n3 {
    margin-top: -1rem !important;
  }

  .mr-sm-n3,
.mx-sm-n3 {
    margin-right: -1rem !important;
  }

  .mb-sm-n3,
.my-sm-n3 {
    margin-bottom: -1rem !important;
  }

  .ml-sm-n3,
.mx-sm-n3 {
    margin-left: -1rem !important;
  }

  .m-sm-n4 {
    margin: -1.5rem !important;
  }

  .mt-sm-n4,
.my-sm-n4 {
    margin-top: -1.5rem !important;
  }

  .mr-sm-n4,
.mx-sm-n4 {
    margin-right: -1.5rem !important;
  }

  .mb-sm-n4,
.my-sm-n4 {
    margin-bottom: -1.5rem !important;
  }

  .ml-sm-n4,
.mx-sm-n4 {
    margin-left: -1.5rem !important;
  }

  .m-sm-n5 {
    margin: -3rem !important;
  }

  .mt-sm-n5,
.my-sm-n5 {
    margin-top: -3rem !important;
  }

  .mr-sm-n5,
.mx-sm-n5 {
    margin-right: -3rem !important;
  }

  .mb-sm-n5,
.my-sm-n5 {
    margin-bottom: -3rem !important;
  }

  .ml-sm-n5,
.mx-sm-n5 {
    margin-left: -3rem !important;
  }

  .m-sm-auto {
    margin: auto !important;
  }

  .mt-sm-auto,
.my-sm-auto {
    margin-top: auto !important;
  }

  .mr-sm-auto,
.mx-sm-auto {
    margin-right: auto !important;
  }

  .mb-sm-auto,
.my-sm-auto {
    margin-bottom: auto !important;
  }

  .ml-sm-auto,
.mx-sm-auto {
    margin-left: auto !important;
  }
}
@media (min-width: 980px) {
  .m-md-0 {
    margin: 0 !important;
  }

  .mt-md-0,
.my-md-0 {
    margin-top: 0 !important;
  }

  .mr-md-0,
.mx-md-0 {
    margin-right: 0 !important;
  }

  .mb-md-0,
.my-md-0 {
    margin-bottom: 0 !important;
  }

  .ml-md-0,
.mx-md-0 {
    margin-left: 0 !important;
  }

  .m-md-1 {
    margin: 0.25rem !important;
  }

  .mt-md-1,
.my-md-1 {
    margin-top: 0.25rem !important;
  }

  .mr-md-1,
.mx-md-1 {
    margin-right: 0.25rem !important;
  }

  .mb-md-1,
.my-md-1 {
    margin-bottom: 0.25rem !important;
  }

  .ml-md-1,
.mx-md-1 {
    margin-left: 0.25rem !important;
  }

  .m-md-2 {
    margin: 0.5rem !important;
  }

  .mt-md-2,
.my-md-2 {
    margin-top: 0.5rem !important;
  }

  .mr-md-2,
.mx-md-2 {
    margin-right: 0.5rem !important;
  }

  .mb-md-2,
.my-md-2 {
    margin-bottom: 0.5rem !important;
  }

  .ml-md-2,
.mx-md-2 {
    margin-left: 0.5rem !important;
  }

  .m-md-3 {
    margin: 1rem !important;
  }

  .mt-md-3,
.my-md-3 {
    margin-top: 1rem !important;
  }

  .mr-md-3,
.mx-md-3 {
    margin-right: 1rem !important;
  }

  .mb-md-3,
.my-md-3 {
    margin-bottom: 1rem !important;
  }

  .ml-md-3,
.mx-md-3 {
    margin-left: 1rem !important;
  }

  .m-md-4 {
    margin: 1.5rem !important;
  }

  .mt-md-4,
.my-md-4 {
    margin-top: 1.5rem !important;
  }

  .mr-md-4,
.mx-md-4 {
    margin-right: 1.5rem !important;
  }

  .mb-md-4,
.my-md-4 {
    margin-bottom: 1.5rem !important;
  }

  .ml-md-4,
.mx-md-4 {
    margin-left: 1.5rem !important;
  }

  .m-md-5 {
    margin: 3rem !important;
  }

  .mt-md-5,
.my-md-5 {
    margin-top: 3rem !important;
  }

  .mr-md-5,
.mx-md-5 {
    margin-right: 3rem !important;
  }

  .mb-md-5,
.my-md-5 {
    margin-bottom: 3rem !important;
  }

  .ml-md-5,
.mx-md-5 {
    margin-left: 3rem !important;
  }

  .p-md-0 {
    padding: 0 !important;
  }

  .pt-md-0,
.py-md-0 {
    padding-top: 0 !important;
  }

  .pr-md-0,
.px-md-0 {
    padding-right: 0 !important;
  }

  .pb-md-0,
.py-md-0 {
    padding-bottom: 0 !important;
  }

  .pl-md-0,
.px-md-0 {
    padding-left: 0 !important;
  }

  .p-md-1 {
    padding: 0.25rem !important;
  }

  .pt-md-1,
.py-md-1 {
    padding-top: 0.25rem !important;
  }

  .pr-md-1,
.px-md-1 {
    padding-right: 0.25rem !important;
  }

  .pb-md-1,
.py-md-1 {
    padding-bottom: 0.25rem !important;
  }

  .pl-md-1,
.px-md-1 {
    padding-left: 0.25rem !important;
  }

  .p-md-2 {
    padding: 0.5rem !important;
  }

  .pt-md-2,
.py-md-2 {
    padding-top: 0.5rem !important;
  }

  .pr-md-2,
.px-md-2 {
    padding-right: 0.5rem !important;
  }

  .pb-md-2,
.py-md-2 {
    padding-bottom: 0.5rem !important;
  }

  .pl-md-2,
.px-md-2 {
    padding-left: 0.5rem !important;
  }

  .p-md-3 {
    padding: 1rem !important;
  }

  .pt-md-3,
.py-md-3 {
    padding-top: 1rem !important;
  }

  .pr-md-3,
.px-md-3 {
    padding-right: 1rem !important;
  }

  .pb-md-3,
.py-md-3 {
    padding-bottom: 1rem !important;
  }

  .pl-md-3,
.px-md-3 {
    padding-left: 1rem !important;
  }

  .p-md-4 {
    padding: 1.5rem !important;
  }

  .pt-md-4,
.py-md-4 {
    padding-top: 1.5rem !important;
  }

  .pr-md-4,
.px-md-4 {
    padding-right: 1.5rem !important;
  }

  .pb-md-4,
.py-md-4 {
    padding-bottom: 1.5rem !important;
  }

  .pl-md-4,
.px-md-4 {
    padding-left: 1.5rem !important;
  }

  .p-md-5 {
    padding: 3rem !important;
  }

  .pt-md-5,
.py-md-5 {
    padding-top: 3rem !important;
  }

  .pr-md-5,
.px-md-5 {
    padding-right: 3rem !important;
  }

  .pb-md-5,
.py-md-5 {
    padding-bottom: 3rem !important;
  }

  .pl-md-5,
.px-md-5 {
    padding-left: 3rem !important;
  }

  .m-md-n1 {
    margin: -0.25rem !important;
  }

  .mt-md-n1,
.my-md-n1 {
    margin-top: -0.25rem !important;
  }

  .mr-md-n1,
.mx-md-n1 {
    margin-right: -0.25rem !important;
  }

  .mb-md-n1,
.my-md-n1 {
    margin-bottom: -0.25rem !important;
  }

  .ml-md-n1,
.mx-md-n1 {
    margin-left: -0.25rem !important;
  }

  .m-md-n2 {
    margin: -0.5rem !important;
  }

  .mt-md-n2,
.my-md-n2 {
    margin-top: -0.5rem !important;
  }

  .mr-md-n2,
.mx-md-n2 {
    margin-right: -0.5rem !important;
  }

  .mb-md-n2,
.my-md-n2 {
    margin-bottom: -0.5rem !important;
  }

  .ml-md-n2,
.mx-md-n2 {
    margin-left: -0.5rem !important;
  }

  .m-md-n3 {
    margin: -1rem !important;
  }

  .mt-md-n3,
.my-md-n3 {
    margin-top: -1rem !important;
  }

  .mr-md-n3,
.mx-md-n3 {
    margin-right: -1rem !important;
  }

  .mb-md-n3,
.my-md-n3 {
    margin-bottom: -1rem !important;
  }

  .ml-md-n3,
.mx-md-n3 {
    margin-left: -1rem !important;
  }

  .m-md-n4 {
    margin: -1.5rem !important;
  }

  .mt-md-n4,
.my-md-n4 {
    margin-top: -1.5rem !important;
  }

  .mr-md-n4,
.mx-md-n4 {
    margin-right: -1.5rem !important;
  }

  .mb-md-n4,
.my-md-n4 {
    margin-bottom: -1.5rem !important;
  }

  .ml-md-n4,
.mx-md-n4 {
    margin-left: -1.5rem !important;
  }

  .m-md-n5 {
    margin: -3rem !important;
  }

  .mt-md-n5,
.my-md-n5 {
    margin-top: -3rem !important;
  }

  .mr-md-n5,
.mx-md-n5 {
    margin-right: -3rem !important;
  }

  .mb-md-n5,
.my-md-n5 {
    margin-bottom: -3rem !important;
  }

  .ml-md-n5,
.mx-md-n5 {
    margin-left: -3rem !important;
  }

  .m-md-auto {
    margin: auto !important;
  }

  .mt-md-auto,
.my-md-auto {
    margin-top: auto !important;
  }

  .mr-md-auto,
.mx-md-auto {
    margin-right: auto !important;
  }

  .mb-md-auto,
.my-md-auto {
    margin-bottom: auto !important;
  }

  .ml-md-auto,
.mx-md-auto {
    margin-left: auto !important;
  }
}
@media (min-width: 1200px) {
  .m-lg-0 {
    margin: 0 !important;
  }

  .mt-lg-0,
.my-lg-0 {
    margin-top: 0 !important;
  }

  .mr-lg-0,
.mx-lg-0 {
    margin-right: 0 !important;
  }

  .mb-lg-0,
.my-lg-0 {
    margin-bottom: 0 !important;
  }

  .ml-lg-0,
.mx-lg-0 {
    margin-left: 0 !important;
  }

  .m-lg-1 {
    margin: 0.25rem !important;
  }

  .mt-lg-1,
.my-lg-1 {
    margin-top: 0.25rem !important;
  }

  .mr-lg-1,
.mx-lg-1 {
    margin-right: 0.25rem !important;
  }

  .mb-lg-1,
.my-lg-1 {
    margin-bottom: 0.25rem !important;
  }

  .ml-lg-1,
.mx-lg-1 {
    margin-left: 0.25rem !important;
  }

  .m-lg-2 {
    margin: 0.5rem !important;
  }

  .mt-lg-2,
.my-lg-2 {
    margin-top: 0.5rem !important;
  }

  .mr-lg-2,
.mx-lg-2 {
    margin-right: 0.5rem !important;
  }

  .mb-lg-2,
.my-lg-2 {
    margin-bottom: 0.5rem !important;
  }

  .ml-lg-2,
.mx-lg-2 {
    margin-left: 0.5rem !important;
  }

  .m-lg-3 {
    margin: 1rem !important;
  }

  .mt-lg-3,
.my-lg-3 {
    margin-top: 1rem !important;
  }

  .mr-lg-3,
.mx-lg-3 {
    margin-right: 1rem !important;
  }

  .mb-lg-3,
.my-lg-3 {
    margin-bottom: 1rem !important;
  }

  .ml-lg-3,
.mx-lg-3 {
    margin-left: 1rem !important;
  }

  .m-lg-4 {
    margin: 1.5rem !important;
  }

  .mt-lg-4,
.my-lg-4 {
    margin-top: 1.5rem !important;
  }

  .mr-lg-4,
.mx-lg-4 {
    margin-right: 1.5rem !important;
  }

  .mb-lg-4,
.my-lg-4 {
    margin-bottom: 1.5rem !important;
  }

  .ml-lg-4,
.mx-lg-4 {
    margin-left: 1.5rem !important;
  }

  .m-lg-5 {
    margin: 3rem !important;
  }

  .mt-lg-5,
.my-lg-5 {
    margin-top: 3rem !important;
  }

  .mr-lg-5,
.mx-lg-5 {
    margin-right: 3rem !important;
  }

  .mb-lg-5,
.my-lg-5 {
    margin-bottom: 3rem !important;
  }

  .ml-lg-5,
.mx-lg-5 {
    margin-left: 3rem !important;
  }

  .p-lg-0 {
    padding: 0 !important;
  }

  .pt-lg-0,
.py-lg-0 {
    padding-top: 0 !important;
  }

  .pr-lg-0,
.px-lg-0 {
    padding-right: 0 !important;
  }

  .pb-lg-0,
.py-lg-0 {
    padding-bottom: 0 !important;
  }

  .pl-lg-0,
.px-lg-0 {
    padding-left: 0 !important;
  }

  .p-lg-1 {
    padding: 0.25rem !important;
  }

  .pt-lg-1,
.py-lg-1 {
    padding-top: 0.25rem !important;
  }

  .pr-lg-1,
.px-lg-1 {
    padding-right: 0.25rem !important;
  }

  .pb-lg-1,
.py-lg-1 {
    padding-bottom: 0.25rem !important;
  }

  .pl-lg-1,
.px-lg-1 {
    padding-left: 0.25rem !important;
  }

  .p-lg-2 {
    padding: 0.5rem !important;
  }

  .pt-lg-2,
.py-lg-2 {
    padding-top: 0.5rem !important;
  }

  .pr-lg-2,
.px-lg-2 {
    padding-right: 0.5rem !important;
  }

  .pb-lg-2,
.py-lg-2 {
    padding-bottom: 0.5rem !important;
  }

  .pl-lg-2,
.px-lg-2 {
    padding-left: 0.5rem !important;
  }

  .p-lg-3 {
    padding: 1rem !important;
  }

  .pt-lg-3,
.py-lg-3 {
    padding-top: 1rem !important;
  }

  .pr-lg-3,
.px-lg-3 {
    padding-right: 1rem !important;
  }

  .pb-lg-3,
.py-lg-3 {
    padding-bottom: 1rem !important;
  }

  .pl-lg-3,
.px-lg-3 {
    padding-left: 1rem !important;
  }

  .p-lg-4 {
    padding: 1.5rem !important;
  }

  .pt-lg-4,
.py-lg-4 {
    padding-top: 1.5rem !important;
  }

  .pr-lg-4,
.px-lg-4 {
    padding-right: 1.5rem !important;
  }

  .pb-lg-4,
.py-lg-4 {
    padding-bottom: 1.5rem !important;
  }

  .pl-lg-4,
.px-lg-4 {
    padding-left: 1.5rem !important;
  }

  .p-lg-5 {
    padding: 3rem !important;
  }

  .pt-lg-5,
.py-lg-5 {
    padding-top: 3rem !important;
  }

  .pr-lg-5,
.px-lg-5 {
    padding-right: 3rem !important;
  }

  .pb-lg-5,
.py-lg-5 {
    padding-bottom: 3rem !important;
  }

  .pl-lg-5,
.px-lg-5 {
    padding-left: 3rem !important;
  }

  .m-lg-n1 {
    margin: -0.25rem !important;
  }

  .mt-lg-n1,
.my-lg-n1 {
    margin-top: -0.25rem !important;
  }

  .mr-lg-n1,
.mx-lg-n1 {
    margin-right: -0.25rem !important;
  }

  .mb-lg-n1,
.my-lg-n1 {
    margin-bottom: -0.25rem !important;
  }

  .ml-lg-n1,
.mx-lg-n1 {
    margin-left: -0.25rem !important;
  }

  .m-lg-n2 {
    margin: -0.5rem !important;
  }

  .mt-lg-n2,
.my-lg-n2 {
    margin-top: -0.5rem !important;
  }

  .mr-lg-n2,
.mx-lg-n2 {
    margin-right: -0.5rem !important;
  }

  .mb-lg-n2,
.my-lg-n2 {
    margin-bottom: -0.5rem !important;
  }

  .ml-lg-n2,
.mx-lg-n2 {
    margin-left: -0.5rem !important;
  }

  .m-lg-n3 {
    margin: -1rem !important;
  }

  .mt-lg-n3,
.my-lg-n3 {
    margin-top: -1rem !important;
  }

  .mr-lg-n3,
.mx-lg-n3 {
    margin-right: -1rem !important;
  }

  .mb-lg-n3,
.my-lg-n3 {
    margin-bottom: -1rem !important;
  }

  .ml-lg-n3,
.mx-lg-n3 {
    margin-left: -1rem !important;
  }

  .m-lg-n4 {
    margin: -1.5rem !important;
  }

  .mt-lg-n4,
.my-lg-n4 {
    margin-top: -1.5rem !important;
  }

  .mr-lg-n4,
.mx-lg-n4 {
    margin-right: -1.5rem !important;
  }

  .mb-lg-n4,
.my-lg-n4 {
    margin-bottom: -1.5rem !important;
  }

  .ml-lg-n4,
.mx-lg-n4 {
    margin-left: -1.5rem !important;
  }

  .m-lg-n5 {
    margin: -3rem !important;
  }

  .mt-lg-n5,
.my-lg-n5 {
    margin-top: -3rem !important;
  }

  .mr-lg-n5,
.mx-lg-n5 {
    margin-right: -3rem !important;
  }

  .mb-lg-n5,
.my-lg-n5 {
    margin-bottom: -3rem !important;
  }

  .ml-lg-n5,
.mx-lg-n5 {
    margin-left: -3rem !important;
  }

  .m-lg-auto {
    margin: auto !important;
  }

  .mt-lg-auto,
.my-lg-auto {
    margin-top: auto !important;
  }

  .mr-lg-auto,
.mx-lg-auto {
    margin-right: auto !important;
  }

  .mb-lg-auto,
.my-lg-auto {
    margin-bottom: auto !important;
  }

  .ml-lg-auto,
.mx-lg-auto {
    margin-left: auto !important;
  }
}
@media (min-width: 1580px) {
  .m-xl-0 {
    margin: 0 !important;
  }

  .mt-xl-0,
.my-xl-0 {
    margin-top: 0 !important;
  }

  .mr-xl-0,
.mx-xl-0 {
    margin-right: 0 !important;
  }

  .mb-xl-0,
.my-xl-0 {
    margin-bottom: 0 !important;
  }

  .ml-xl-0,
.mx-xl-0 {
    margin-left: 0 !important;
  }

  .m-xl-1 {
    margin: 0.25rem !important;
  }

  .mt-xl-1,
.my-xl-1 {
    margin-top: 0.25rem !important;
  }

  .mr-xl-1,
.mx-xl-1 {
    margin-right: 0.25rem !important;
  }

  .mb-xl-1,
.my-xl-1 {
    margin-bottom: 0.25rem !important;
  }

  .ml-xl-1,
.mx-xl-1 {
    margin-left: 0.25rem !important;
  }

  .m-xl-2 {
    margin: 0.5rem !important;
  }

  .mt-xl-2,
.my-xl-2 {
    margin-top: 0.5rem !important;
  }

  .mr-xl-2,
.mx-xl-2 {
    margin-right: 0.5rem !important;
  }

  .mb-xl-2,
.my-xl-2 {
    margin-bottom: 0.5rem !important;
  }

  .ml-xl-2,
.mx-xl-2 {
    margin-left: 0.5rem !important;
  }

  .m-xl-3 {
    margin: 1rem !important;
  }

  .mt-xl-3,
.my-xl-3 {
    margin-top: 1rem !important;
  }

  .mr-xl-3,
.mx-xl-3 {
    margin-right: 1rem !important;
  }

  .mb-xl-3,
.my-xl-3 {
    margin-bottom: 1rem !important;
  }

  .ml-xl-3,
.mx-xl-3 {
    margin-left: 1rem !important;
  }

  .m-xl-4 {
    margin: 1.5rem !important;
  }

  .mt-xl-4,
.my-xl-4 {
    margin-top: 1.5rem !important;
  }

  .mr-xl-4,
.mx-xl-4 {
    margin-right: 1.5rem !important;
  }

  .mb-xl-4,
.my-xl-4 {
    margin-bottom: 1.5rem !important;
  }

  .ml-xl-4,
.mx-xl-4 {
    margin-left: 1.5rem !important;
  }

  .m-xl-5 {
    margin: 3rem !important;
  }

  .mt-xl-5,
.my-xl-5 {
    margin-top: 3rem !important;
  }

  .mr-xl-5,
.mx-xl-5 {
    margin-right: 3rem !important;
  }

  .mb-xl-5,
.my-xl-5 {
    margin-bottom: 3rem !important;
  }

  .ml-xl-5,
.mx-xl-5 {
    margin-left: 3rem !important;
  }

  .p-xl-0 {
    padding: 0 !important;
  }

  .pt-xl-0,
.py-xl-0 {
    padding-top: 0 !important;
  }

  .pr-xl-0,
.px-xl-0 {
    padding-right: 0 !important;
  }

  .pb-xl-0,
.py-xl-0 {
    padding-bottom: 0 !important;
  }

  .pl-xl-0,
.px-xl-0 {
    padding-left: 0 !important;
  }

  .p-xl-1 {
    padding: 0.25rem !important;
  }

  .pt-xl-1,
.py-xl-1 {
    padding-top: 0.25rem !important;
  }

  .pr-xl-1,
.px-xl-1 {
    padding-right: 0.25rem !important;
  }

  .pb-xl-1,
.py-xl-1 {
    padding-bottom: 0.25rem !important;
  }

  .pl-xl-1,
.px-xl-1 {
    padding-left: 0.25rem !important;
  }

  .p-xl-2 {
    padding: 0.5rem !important;
  }

  .pt-xl-2,
.py-xl-2 {
    padding-top: 0.5rem !important;
  }

  .pr-xl-2,
.px-xl-2 {
    padding-right: 0.5rem !important;
  }

  .pb-xl-2,
.py-xl-2 {
    padding-bottom: 0.5rem !important;
  }

  .pl-xl-2,
.px-xl-2 {
    padding-left: 0.5rem !important;
  }

  .p-xl-3 {
    padding: 1rem !important;
  }

  .pt-xl-3,
.py-xl-3 {
    padding-top: 1rem !important;
  }

  .pr-xl-3,
.px-xl-3 {
    padding-right: 1rem !important;
  }

  .pb-xl-3,
.py-xl-3 {
    padding-bottom: 1rem !important;
  }

  .pl-xl-3,
.px-xl-3 {
    padding-left: 1rem !important;
  }

  .p-xl-4 {
    padding: 1.5rem !important;
  }

  .pt-xl-4,
.py-xl-4 {
    padding-top: 1.5rem !important;
  }

  .pr-xl-4,
.px-xl-4 {
    padding-right: 1.5rem !important;
  }

  .pb-xl-4,
.py-xl-4 {
    padding-bottom: 1.5rem !important;
  }

  .pl-xl-4,
.px-xl-4 {
    padding-left: 1.5rem !important;
  }

  .p-xl-5 {
    padding: 3rem !important;
  }

  .pt-xl-5,
.py-xl-5 {
    padding-top: 3rem !important;
  }

  .pr-xl-5,
.px-xl-5 {
    padding-right: 3rem !important;
  }

  .pb-xl-5,
.py-xl-5 {
    padding-bottom: 3rem !important;
  }

  .pl-xl-5,
.px-xl-5 {
    padding-left: 3rem !important;
  }

  .m-xl-n1 {
    margin: -0.25rem !important;
  }

  .mt-xl-n1,
.my-xl-n1 {
    margin-top: -0.25rem !important;
  }

  .mr-xl-n1,
.mx-xl-n1 {
    margin-right: -0.25rem !important;
  }

  .mb-xl-n1,
.my-xl-n1 {
    margin-bottom: -0.25rem !important;
  }

  .ml-xl-n1,
.mx-xl-n1 {
    margin-left: -0.25rem !important;
  }

  .m-xl-n2 {
    margin: -0.5rem !important;
  }

  .mt-xl-n2,
.my-xl-n2 {
    margin-top: -0.5rem !important;
  }

  .mr-xl-n2,
.mx-xl-n2 {
    margin-right: -0.5rem !important;
  }

  .mb-xl-n2,
.my-xl-n2 {
    margin-bottom: -0.5rem !important;
  }

  .ml-xl-n2,
.mx-xl-n2 {
    margin-left: -0.5rem !important;
  }

  .m-xl-n3 {
    margin: -1rem !important;
  }

  .mt-xl-n3,
.my-xl-n3 {
    margin-top: -1rem !important;
  }

  .mr-xl-n3,
.mx-xl-n3 {
    margin-right: -1rem !important;
  }

  .mb-xl-n3,
.my-xl-n3 {
    margin-bottom: -1rem !important;
  }

  .ml-xl-n3,
.mx-xl-n3 {
    margin-left: -1rem !important;
  }

  .m-xl-n4 {
    margin: -1.5rem !important;
  }

  .mt-xl-n4,
.my-xl-n4 {
    margin-top: -1.5rem !important;
  }

  .mr-xl-n4,
.mx-xl-n4 {
    margin-right: -1.5rem !important;
  }

  .mb-xl-n4,
.my-xl-n4 {
    margin-bottom: -1.5rem !important;
  }

  .ml-xl-n4,
.mx-xl-n4 {
    margin-left: -1.5rem !important;
  }

  .m-xl-n5 {
    margin: -3rem !important;
  }

  .mt-xl-n5,
.my-xl-n5 {
    margin-top: -3rem !important;
  }

  .mr-xl-n5,
.mx-xl-n5 {
    margin-right: -3rem !important;
  }

  .mb-xl-n5,
.my-xl-n5 {
    margin-bottom: -3rem !important;
  }

  .ml-xl-n5,
.mx-xl-n5 {
    margin-left: -3rem !important;
  }

  .m-xl-auto {
    margin: auto !important;
  }

  .mt-xl-auto,
.my-xl-auto {
    margin-top: auto !important;
  }

  .mr-xl-auto,
.mx-xl-auto {
    margin-right: auto !important;
  }

  .mb-xl-auto,
.my-xl-auto {
    margin-bottom: auto !important;
  }

  .ml-xl-auto,
.mx-xl-auto {
    margin-left: auto !important;
  }
}
.stretched-link::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  pointer-events: auto;
  content: "";
  background-color: rgba(0, 0, 0, 0);
}

.text-monospace {
  font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace !important;
}

.text-justify {
  text-align: justify !important;
}

.text-wrap {
  white-space: normal !important;
}

.text-nowrap {
  white-space: nowrap !important;
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-left {
  text-align: left !important;
}

.text-right {
  text-align: right !important;
}

.text-center {
  text-align: center !important;
}

@media (min-width: 600px) {
  .text-sm-left {
    text-align: left !important;
  }

  .text-sm-right {
    text-align: right !important;
  }

  .text-sm-center {
    text-align: center !important;
  }
}
@media (min-width: 980px) {
  .text-md-left {
    text-align: left !important;
  }

  .text-md-right {
    text-align: right !important;
  }

  .text-md-center {
    text-align: center !important;
  }
}
@media (min-width: 1200px) {
  .text-lg-left {
    text-align: left !important;
  }

  .text-lg-right {
    text-align: right !important;
  }

  .text-lg-center {
    text-align: center !important;
  }
}
@media (min-width: 1580px) {
  .text-xl-left {
    text-align: left !important;
  }

  .text-xl-right {
    text-align: right !important;
  }

  .text-xl-center {
    text-align: center !important;
  }
}
.text-lowercase {
  text-transform: lowercase !important;
}

.text-uppercase {
  text-transform: uppercase !important;
}

.text-capitalize {
  text-transform: capitalize !important;
}

.font-weight-light {
  font-weight: 300 !important;
}

.font-weight-lighter {
  font-weight: lighter !important;
}

.font-weight-normal {
  font-weight: 400 !important;
}

.font-weight-bold {
  font-weight: 700 !important;
}

.font-weight-bolder {
  font-weight: 500 !important;
}

.font-italic {
  font-style: italic !important;
}

.text-white {
  color: #fff !important;
}

.text-primary {
  color: #337ab7 !important;
}

a.text-primary:hover, a.text-primary:focus {
  color: #22527b !important;
}

.text-secondary {
  color: #002663 !important;
}

a.text-secondary:hover, a.text-secondary:focus {
  color: #000917 !important;
}

.text-success {
  color: #64d091 !important;
}

a.text-success:hover, a.text-success:focus {
  color: #36b269 !important;
}

.text-info {
  color: #388695 !important;
}

a.text-info:hover, a.text-info:focus {
  color: #23545d !important;
}

.text-warning {
  color: #DB9E5B !important;
}

a.text-warning:hover, a.text-warning:focus {
  color: #bf782a !important;
}

.text-danger {
  color: #cc6565 !important;
}

a.text-danger:hover, a.text-danger:focus {
  color: #ac3939 !important;
}

.text-light {
  color: #d8dce6 !important;
}

a.text-light:hover, a.text-light:focus {
  color: #a9b2c8 !important;
}

.text-dark {
  color: #5e6983 !important;
}

a.text-dark:hover, a.text-dark:focus {
  color: #3e4556 !important;
}

.text-lighter {
  color: #fafafc !important;
}

a.text-lighter:hover, a.text-lighter:focus {
  color: #cacadf !important;
}

.text-lighter-blue {
  color: #f4f8fc !important;
}

a.text-lighter-blue:hover, a.text-lighter-blue:focus {
  color: #b8d2ec !important;
}

.text-body {
  color: #5e6983 !important;
}

.text-muted {
  color: #6c757d !important;
}

.text-black-50 {
  color: rgba(0, 0, 0, 0.5) !important;
}

.text-white-50 {
  color: rgba(255, 255, 255, 0.5) !important;
}

.text-hide {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0;
}

.text-decoration-none {
  text-decoration: none !important;
}

.text-break {
  word-break: break-word !important;
  word-wrap: break-word !important;
}

.text-reset {
  color: inherit !important;
}

.visible {
  visibility: visible !important;
}

.invisible {
  visibility: hidden !important;
}

@media print {
  *,
*::before,
*::after {
    text-shadow: none !important;
    box-shadow: none !important;
  }

  a:not(.btn) {
    text-decoration: underline;
  }

  abbr[title]::after {
    content: " (" attr(title) ")";
  }

  pre {
    white-space: pre-wrap !important;
  }

  pre,
blockquote {
    border: 1px solid #adb5bd;
    page-break-inside: avoid;
  }

  tr,
img {
    page-break-inside: avoid;
  }

  p,
h2,
h3 {
    orphans: 3;
    widows: 3;
  }

  h2,
h3 {
    page-break-after: avoid;
  }

  @page {
    size: a3;
  }
  body {
    min-width: 1200px !important;
  }

  .container {
    min-width: 1200px !important;
  }

  .navbar {
    display: none;
  }

  .badge, .select2-container--default .select2-selection--multiple .select2-selection__choice {
    border: 1px solid #000;
  }

  .table {
    border-collapse: collapse !important;
  }
  .table td,
.table th {
    background-color: #fff !important;
  }

  .table-bordered th,
.table-bordered td {
    border: 1px solid #dee2e6 !important;
  }

  .table-dark {
    color: inherit;
  }
  .table-dark th,
.table-dark td,
.table-dark thead th,
.table-dark tbody + tbody {
    border-color: #d8dce6;
  }

  .table .thead-dark th {
    color: inherit;
    border-color: #d8dce6;
  }
}
.selectric-wrapper {
  position: relative;
  cursor: pointer;
}

.selectric-responsive {
  width: 100%;
}

.selectric {
  border: 1px solid #d8dce6;
  border-radius: 0.125rem;
  background: #fafafc;
  position: relative;
  overflow: hidden;
}
.selectric .label {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin: 0 40px 0 10px;
  font-size: 0.875rem;
  line-height: 40px;
  color: #5e6983;
  height: 40px;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.selectric .button {
  display: block;
  position: absolute;
  right: 0;
  top: 0;
  width: 40px;
  height: 40px;
  line-height: 40px;
  background-color: #F8f8f8;
  color: #5e6983;
  text-align: center;
  font: 0/0 a;
  *font: 20px/40px Lucida Sans Unicode, Arial Unicode MS, Arial;
}
.selectric .button:after {
  content: " ";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
  width: 0;
  height: 0;
  border: 4px solid transparent;
  border-top-color: #5e6983;
  border-bottom: none;
}

.selectric-focus .selectric {
  border-color: #9aa4be;
}

.selectric-hover .selectric {
  border-color: #b9c0d2;
}
.selectric-hover .selectric .button {
  color: #495165;
}
.selectric-hover .selectric .button:after {
  border-top-color: #495165;
}

.selectric-open {
  z-index: 9999;
}
.selectric-open .selectric {
  border-color: #b9c0d2;
}
.selectric-open .selectric-items {
  display: block;
}

.selectric-disabled {
  filter: alpha(opacity=50);
  opacity: 0.5;
  cursor: default;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

.selectric-hide-select {
  position: relative;
  overflow: hidden;
  width: 0;
  height: 0;
}
.selectric-hide-select select {
  position: absolute;
  left: -100%;
}
.selectric-hide-select.selectric-is-native {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 10;
}
.selectric-hide-select.selectric-is-native select {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  width: 100%;
  border: none;
  z-index: 1;
  box-sizing: border-box;
  opacity: 0;
}

.selectric-input {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  margin: 0 !important;
  padding: 0 !important;
  width: 1px !important;
  height: 1px !important;
  outline: none !important;
  border: none !important;
  *font: 0/0 a !important;
  background: none !important;
}

.selectric-temp-show {
  position: absolute !important;
  visibility: hidden !important;
  display: block !important;
}

/* Items box */
.selectric-items {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  background: #fafafc;
  border: 1px solid #b9c0d2;
  z-index: -1;
  box-shadow: 0 0 10px -6px;
}
.selectric-items .selectric-scroll {
  height: 100%;
  overflow: auto;
}
.selectric-above .selectric-items {
  top: auto;
  bottom: 100%;
}
.selectric-items ul, .selectric-items li {
  list-style: none;
  padding: 0;
  margin: 0;
  font-size: 0.875rem;
  line-height: 20px;
  min-height: 20px;
}
.selectric-items li {
  display: block;
  padding: 10px;
  color: #666;
  cursor: pointer;
}
.selectric-items li.selected {
  background: #E0E0E0;
  color: #444;
}
.selectric-items li.highlighted {
  background: #D0D0D0;
  color: #444;
}
.selectric-items li:hover {
  background: #D5D5D5;
  color: #444;
}
.selectric-items .disabled {
  filter: alpha(opacity=50);
  opacity: 0.5;
  cursor: default !important;
  background: none !important;
  color: #666 !important;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.selectric-items .selectric-group .selectric-group-label {
  font-weight: bold;
  padding-left: 10px;
  cursor: default;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  background: none;
  color: #444;
}
.selectric-items .selectric-group.disabled li {
  filter: alpha(opacity=100);
  opacity: 1;
}
.selectric-items .selectric-group li {
  padding-left: 25px;
}

/*!
 * Datepicker for Bootstrap v1.9.0 (https://github.com/uxsolutions/bootstrap-datepicker)
 *
 * Licensed under the Apache License v2.0 (http://www.apache.org/licenses/LICENSE-2.0)
 */
.datepicker {
  padding: 4px;
  border-radius: 4px;
  direction: ltr;
}

.datepicker-inline {
  width: 220px;
}

.datepicker-rtl {
  direction: rtl;
}

.datepicker-rtl.dropdown-menu {
  left: auto;
}

.datepicker-rtl table tr td span {
  float: right;
}

.datepicker-dropdown {
  top: 0;
  left: 0;
}

.datepicker-dropdown:before {
  content: "";
  display: inline-block;
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-bottom: 7px solid #999;
  border-top: 0;
  border-bottom-color: rgba(0, 0, 0, 0.2);
  position: absolute;
}

.datepicker-dropdown:after {
  content: "";
  display: inline-block;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 6px solid #fff;
  border-top: 0;
  position: absolute;
}

.datepicker-dropdown.datepicker-orient-left:before {
  left: 6px;
}

.datepicker-dropdown.datepicker-orient-left:after {
  left: 7px;
}

.datepicker-dropdown.datepicker-orient-right:before {
  right: 6px;
}

.datepicker-dropdown.datepicker-orient-right:after {
  right: 7px;
}

.datepicker-dropdown.datepicker-orient-bottom:before {
  top: -7px;
}

.datepicker-dropdown.datepicker-orient-bottom:after {
  top: -6px;
}

.datepicker-dropdown.datepicker-orient-top:before {
  bottom: -7px;
  border-bottom: 0;
  border-top: 7px solid #999;
}

.datepicker-dropdown.datepicker-orient-top:after {
  bottom: -6px;
  border-bottom: 0;
  border-top: 6px solid #fff;
}

.datepicker table {
  margin: 0;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

.datepicker td,
.datepicker th {
  text-align: center;
  width: 20px;
  height: 20px;
  border-radius: 4px;
  border: none;
}

.table-striped .datepicker table tr td,
.table-striped .datepicker table tr th {
  background-color: transparent;
}

.datepicker table tr td.day:hover,
.datepicker table tr td.day.focused {
  background: #eee;
  cursor: pointer;
}

.datepicker table tr td.old,
.datepicker table tr td.new {
  color: #999;
}

.datepicker table tr td.disabled,
.datepicker table tr td.disabled:hover {
  background: none;
  color: #999;
  cursor: default;
}

.datepicker table tr td.highlighted {
  background: #d9edf7;
  border-radius: 0;
}

.datepicker table tr td.today,
.datepicker table tr td.today:hover,
.datepicker table tr td.today.disabled,
.datepicker table tr td.today.disabled:hover {
  background-color: #fde19a;
  background-image: -webkit-gradient(linear, left top, left bottom, from(#fdd49a), to(#fdf59a));
  background-image: -o-linear-gradient(top, #fdd49a, #fdf59a);
  background-image: linear-gradient(to bottom, #fdd49a, #fdf59a);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#fdd49a", endColorstr="#fdf59a", GradientType=0);
  border-color: #fdf59a #fdf59a #fbed50;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
  color: #000;
}

.datepicker table tr td.today:hover,
.datepicker table tr td.today:hover:hover,
.datepicker table tr td.today.disabled:hover,
.datepicker table tr td.today.disabled:hover:hover,
.datepicker table tr td.today:active,
.datepicker table tr td.today:hover:active,
.datepicker table tr td.today.disabled:active,
.datepicker table tr td.today.disabled:hover:active,
.datepicker table tr td.today.active,
.datepicker table tr td.today:hover.active,
.datepicker table tr td.today.disabled.active,
.datepicker table tr td.today.disabled:hover.active,
.datepicker table tr td.today.disabled,
.datepicker table tr td.today:hover.disabled,
.datepicker table tr td.today.disabled.disabled,
.datepicker table tr td.today.disabled:hover.disabled,
.datepicker table tr td.today[disabled],
.datepicker table tr td.today:hover[disabled],
.datepicker table tr td.today.disabled[disabled],
.datepicker table tr td.today.disabled:hover[disabled] {
  background-color: #fdf59a;
}

.datepicker table tr td.today:active,
.datepicker table tr td.today:hover:active,
.datepicker table tr td.today.disabled:active,
.datepicker table tr td.today.disabled:hover:active,
.datepicker table tr td.today.active,
.datepicker table tr td.today:hover.active,
.datepicker table tr td.today.disabled.active,
.datepicker table tr td.today.disabled:hover.active {
  background-color: #fbf069 \9 ;
}

.datepicker table tr td.today:hover:hover {
  color: #000;
}

.datepicker table tr td.today.active:hover {
  color: #fff;
}

.datepicker table tr td.range,
.datepicker table tr td.range:hover,
.datepicker table tr td.range.disabled,
.datepicker table tr td.range.disabled:hover {
  background: #eee;
  border-radius: 0;
}

.datepicker table tr td.range.today,
.datepicker table tr td.range.today:hover,
.datepicker table tr td.range.today.disabled,
.datepicker table tr td.range.today.disabled:hover {
  background-color: #f3d17a;
  background-image: -webkit-gradient(linear, left top, left bottom, from(#f3c17a), to(#f3e97a));
  background-image: -o-linear-gradient(top, #f3c17a, #f3e97a);
  background-image: linear-gradient(to bottom, #f3c17a, #f3e97a);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#f3c17a", endColorstr="#f3e97a", GradientType=0);
  border-color: #f3e97a #f3e97a #edde34;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
  border-radius: 0;
}

.datepicker table tr td.range.today:hover,
.datepicker table tr td.range.today:hover:hover,
.datepicker table tr td.range.today.disabled:hover,
.datepicker table tr td.range.today.disabled:hover:hover,
.datepicker table tr td.range.today:active,
.datepicker table tr td.range.today:hover:active,
.datepicker table tr td.range.today.disabled:active,
.datepicker table tr td.range.today.disabled:hover:active,
.datepicker table tr td.range.today.active,
.datepicker table tr td.range.today:hover.active,
.datepicker table tr td.range.today.disabled.active,
.datepicker table tr td.range.today.disabled:hover.active,
.datepicker table tr td.range.today.disabled,
.datepicker table tr td.range.today:hover.disabled,
.datepicker table tr td.range.today.disabled.disabled,
.datepicker table tr td.range.today.disabled:hover.disabled,
.datepicker table tr td.range.today[disabled],
.datepicker table tr td.range.today:hover[disabled],
.datepicker table tr td.range.today.disabled[disabled],
.datepicker table tr td.range.today.disabled:hover[disabled] {
  background-color: #f3e97a;
}

.datepicker table tr td.range.today:active,
.datepicker table tr td.range.today:hover:active,
.datepicker table tr td.range.today.disabled:active,
.datepicker table tr td.range.today.disabled:hover:active,
.datepicker table tr td.range.today.active,
.datepicker table tr td.range.today:hover.active,
.datepicker table tr td.range.today.disabled.active,
.datepicker table tr td.range.today.disabled:hover.active {
  background-color: #efe24b \9 ;
}

.datepicker table tr td.selected,
.datepicker table tr td.selected:hover,
.datepicker table tr td.selected.disabled,
.datepicker table tr td.selected.disabled:hover {
  background-color: #9e9e9e;
  background-image: -webkit-gradient(linear, left top, left bottom, from(#b3b3b3), to(#808080));
  background-image: -o-linear-gradient(top, #b3b3b3, #808080);
  background-image: linear-gradient(to bottom, #b3b3b3, #808080);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#b3b3b3", endColorstr="#808080", GradientType=0);
  border-color: #808080 #808080 #595959;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
  color: #fff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}

.datepicker table tr td.selected:hover,
.datepicker table tr td.selected:hover:hover,
.datepicker table tr td.selected.disabled:hover,
.datepicker table tr td.selected.disabled:hover:hover,
.datepicker table tr td.selected:active,
.datepicker table tr td.selected:hover:active,
.datepicker table tr td.selected.disabled:active,
.datepicker table tr td.selected.disabled:hover:active,
.datepicker table tr td.selected.active,
.datepicker table tr td.selected:hover.active,
.datepicker table tr td.selected.disabled.active,
.datepicker table tr td.selected.disabled:hover.active,
.datepicker table tr td.selected.disabled,
.datepicker table tr td.selected:hover.disabled,
.datepicker table tr td.selected.disabled.disabled,
.datepicker table tr td.selected.disabled:hover.disabled,
.datepicker table tr td.selected[disabled],
.datepicker table tr td.selected:hover[disabled],
.datepicker table tr td.selected.disabled[disabled],
.datepicker table tr td.selected.disabled:hover[disabled] {
  background-color: #808080;
}

.datepicker table tr td.selected:active,
.datepicker table tr td.selected:hover:active,
.datepicker table tr td.selected.disabled:active,
.datepicker table tr td.selected.disabled:hover:active,
.datepicker table tr td.selected.active,
.datepicker table tr td.selected:hover.active,
.datepicker table tr td.selected.disabled.active,
.datepicker table tr td.selected.disabled:hover.active {
  background-color: #666666 \9 ;
}

.datepicker table tr td.active,
.datepicker table tr td.active:hover,
.datepicker table tr td.active.disabled,
.datepicker table tr td.active.disabled:hover {
  background-color: #006dcc;
  background-image: -webkit-gradient(linear, left top, left bottom, from(#08c), to(#0044cc));
  background-image: -o-linear-gradient(top, #08c, #0044cc);
  background-image: linear-gradient(to bottom, #08c, #0044cc);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#08c", endColorstr="#0044cc", GradientType=0);
  border-color: #0044cc #0044cc #002a80;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
  color: #fff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}

.datepicker table tr td.active:hover,
.datepicker table tr td.active:hover:hover,
.datepicker table tr td.active.disabled:hover,
.datepicker table tr td.active.disabled:hover:hover,
.datepicker table tr td.active:active,
.datepicker table tr td.active:hover:active,
.datepicker table tr td.active.disabled:active,
.datepicker table tr td.active.disabled:hover:active,
.datepicker table tr td.active.active,
.datepicker table tr td.active:hover.active,
.datepicker table tr td.active.disabled.active,
.datepicker table tr td.active.disabled:hover.active,
.datepicker table tr td.active.disabled,
.datepicker table tr td.active:hover.disabled,
.datepicker table tr td.active.disabled.disabled,
.datepicker table tr td.active.disabled:hover.disabled,
.datepicker table tr td.active[disabled],
.datepicker table tr td.active:hover[disabled],
.datepicker table tr td.active.disabled[disabled],
.datepicker table tr td.active.disabled:hover[disabled] {
  background-color: #0044cc;
}

.datepicker table tr td.active:active,
.datepicker table tr td.active:hover:active,
.datepicker table tr td.active.disabled:active,
.datepicker table tr td.active.disabled:hover:active,
.datepicker table tr td.active.active,
.datepicker table tr td.active:hover.active,
.datepicker table tr td.active.disabled.active,
.datepicker table tr td.active.disabled:hover.active {
  background-color: #003399 \9 ;
}

.datepicker table tr td span {
  display: block;
  width: 23%;
  height: 54px;
  line-height: 54px;
  float: left;
  margin: 1%;
  cursor: pointer;
  border-radius: 4px;
}

.datepicker table tr td span:hover,
.datepicker table tr td span.focused {
  background: #eee;
}

.datepicker table tr td span.disabled,
.datepicker table tr td span.disabled:hover {
  background: none;
  color: #999;
  cursor: default;
}

.datepicker table tr td span.active,
.datepicker table tr td span.active:hover,
.datepicker table tr td span.active.disabled,
.datepicker table tr td span.active.disabled:hover {
  background-color: #006dcc;
  background-image: -webkit-gradient(linear, left top, left bottom, from(#08c), to(#0044cc));
  background-image: -o-linear-gradient(top, #08c, #0044cc);
  background-image: linear-gradient(to bottom, #08c, #0044cc);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#08c", endColorstr="#0044cc", GradientType=0);
  border-color: #0044cc #0044cc #002a80;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
  color: #fff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}

.datepicker table tr td span.active:hover,
.datepicker table tr td span.active:hover:hover,
.datepicker table tr td span.active.disabled:hover,
.datepicker table tr td span.active.disabled:hover:hover,
.datepicker table tr td span.active:active,
.datepicker table tr td span.active:hover:active,
.datepicker table tr td span.active.disabled:active,
.datepicker table tr td span.active.disabled:hover:active,
.datepicker table tr td span.active.active,
.datepicker table tr td span.active:hover.active,
.datepicker table tr td span.active.disabled.active,
.datepicker table tr td span.active.disabled:hover.active,
.datepicker table tr td span.active.disabled,
.datepicker table tr td span.active:hover.disabled,
.datepicker table tr td span.active.disabled.disabled,
.datepicker table tr td span.active.disabled:hover.disabled,
.datepicker table tr td span.active[disabled],
.datepicker table tr td span.active:hover[disabled],
.datepicker table tr td span.active.disabled[disabled],
.datepicker table tr td span.active.disabled:hover[disabled] {
  background-color: #0044cc;
}

.datepicker table tr td span.active:active,
.datepicker table tr td span.active:hover:active,
.datepicker table tr td span.active.disabled:active,
.datepicker table tr td span.active.disabled:hover:active,
.datepicker table tr td span.active.active,
.datepicker table tr td span.active:hover.active,
.datepicker table tr td span.active.disabled.active,
.datepicker table tr td span.active.disabled:hover.active {
  background-color: #003399 \9 ;
}

.datepicker table tr td span.old,
.datepicker table tr td span.new {
  color: #999;
}

.datepicker .datepicker-switch {
  width: 145px;
}

.datepicker .datepicker-switch,
.datepicker .prev,
.datepicker .next,
.datepicker tfoot tr th {
  cursor: pointer;
}

.datepicker .datepicker-switch:hover,
.datepicker .prev:hover,
.datepicker .next:hover,
.datepicker tfoot tr th:hover {
  background: #eee;
}

.datepicker .prev.disabled,
.datepicker .next.disabled {
  visibility: hidden;
}

.datepicker .cw {
  font-size: 10px;
  width: 12px;
  padding: 0 2px 0 5px;
  vertical-align: middle;
}

.input-append.date .add-on,
.input-prepend.date .add-on {
  cursor: pointer;
}

.input-append.date .add-on i,
.input-prepend.date .add-on i {
  margin-top: 3px;
}

.input-daterange input {
  text-align: center;
}

.input-daterange input:first-child {
  border-radius: 3px 0 0 3px;
}

.input-daterange input:last-child {
  border-radius: 0 3px 3px 0;
}

.input-daterange .add-on {
  display: inline-block;
  width: auto;
  min-width: 16px;
  height: 18px;
  padding: 4px 5px;
  font-weight: normal;
  line-height: 18px;
  text-align: center;
  text-shadow: 0 1px 0 #fff;
  vertical-align: middle;
  background-color: #eee;
  border: 1px solid #ccc;
  margin-left: -5px;
  margin-right: -5px;
}

/*# sourceMappingURL=bootstrap-datepicker.css.map */
.pell {
  border: 1px solid rgba(10, 10, 10, 0.1);
  box-sizing: border-box;
}

.pell-content {
  box-sizing: border-box;
  height: 300px;
  outline: 0;
  overflow-y: auto;
  padding: 10px;
}

.pell-actionbar {
  background-color: #FFF;
  border-bottom: 1px solid rgba(10, 10, 10, 0.1);
}

.pell-button {
  background-color: transparent;
  border: none;
  cursor: pointer;
  height: 30px;
  outline: 0;
  width: 30px;
  vertical-align: bottom;
}

.pell-button-selected {
  background-color: #F0F0F0;
}

/* Functional styling;
 * These styles are required for noUiSlider to function.
 * You don't need to change these rules to apply your design.
 */
.noUi-target,
.noUi-target * {
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-user-select: none;
  touch-action: none;
  -moz-user-select: none;
  user-select: none;
  box-sizing: border-box;
}

.noUi-target {
  position: relative;
}

.noUi-base,
.noUi-connects {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 1;
}

/* Wrapper for all connect elements.
 */
.noUi-connects {
  overflow: hidden;
  z-index: 0;
}

.noUi-connect,
.noUi-origin {
  will-change: transform;
  position: absolute;
  z-index: 1;
  top: 0;
  right: 0;
  height: 100%;
  width: 100%;
  -ms-transform-origin: 0 0;
  -webkit-transform-origin: 0 0;
  -webkit-transform-style: preserve-3d;
  transform-origin: 0 0;
  transform-style: flat;
}

/* Offset direction
 */
.noUi-txt-dir-rtl.noUi-horizontal .noUi-origin {
  left: 0;
  right: auto;
}

/* Give origins 0 height/width so they don't interfere with clicking the
 * connect elements.
 */
.noUi-vertical .noUi-origin {
  top: -100%;
  width: 0;
}

.noUi-horizontal .noUi-origin {
  height: 0;
}

.noUi-handle {
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  position: absolute;
}

.noUi-touch-area {
  height: 100%;
  width: 100%;
}

.noUi-state-tap .noUi-connect,
.noUi-state-tap .noUi-origin {
  transition: transform 0.3s;
}

.noUi-state-drag * {
  cursor: inherit !important;
}

/* Slider size and handle placement;
 */
.noUi-horizontal {
  height: 18px;
}

.noUi-horizontal .noUi-handle {
  width: 34px;
  height: 28px;
  right: -17px;
  top: -6px;
}

.noUi-vertical {
  width: 18px;
}

.noUi-vertical .noUi-handle {
  width: 28px;
  height: 34px;
  right: -6px;
  bottom: -17px;
}

.noUi-txt-dir-rtl.noUi-horizontal .noUi-handle {
  left: -17px;
  right: auto;
}

/* Styling;
 * Giving the connect element a border radius causes issues with using transform: scale
 */
.noUi-target {
  background: #FAFAFA;
  border-radius: 4px;
  border: 1px solid #D3D3D3;
  box-shadow: inset 0 1px 1px #F0F0F0, 0 3px 6px -5px #BBB;
}

.noUi-connects {
  border-radius: 3px;
}

.noUi-connect {
  background: #3FB8AF;
}

/* Handles and cursors;
 */
.noUi-draggable {
  cursor: ew-resize;
}

.noUi-vertical .noUi-draggable {
  cursor: ns-resize;
}

.noUi-handle {
  border: 1px solid #D9D9D9;
  border-radius: 3px;
  background: #FFF;
  cursor: default;
  box-shadow: inset 0 0 1px #FFF, inset 0 1px 7px #EBEBEB, 0 3px 6px -3px #BBB;
}

.noUi-active {
  box-shadow: inset 0 0 1px #FFF, inset 0 1px 7px #DDD, 0 3px 6px -3px #BBB;
}

/* Handle stripes;
 */
.noUi-handle:before,
.noUi-handle:after {
  content: "";
  display: block;
  position: absolute;
  height: 14px;
  width: 1px;
  background: #E8E7E6;
  left: 14px;
  top: 6px;
}

.noUi-handle:after {
  left: 17px;
}

.noUi-vertical .noUi-handle:before,
.noUi-vertical .noUi-handle:after {
  width: 14px;
  height: 1px;
  left: 6px;
  top: 14px;
}

.noUi-vertical .noUi-handle:after {
  top: 17px;
}

/* Disabled state;
 */
[disabled] .noUi-connect {
  background: #B8B8B8;
}

[disabled].noUi-target,
[disabled].noUi-handle,
[disabled] .noUi-handle {
  cursor: not-allowed;
}

/* Base;
 *
 */
.noUi-pips,
.noUi-pips * {
  box-sizing: border-box;
}

.noUi-pips {
  position: absolute;
  color: #999;
}

/* Values;
 *
 */
.noUi-value {
  position: absolute;
  white-space: nowrap;
  text-align: center;
}

.noUi-value-sub {
  color: #ccc;
  font-size: 10px;
}

/* Markings;
 *
 */
.noUi-marker {
  position: absolute;
  background: #CCC;
}

.noUi-marker-sub {
  background: #AAA;
}

.noUi-marker-large {
  background: #AAA;
}

/* Horizontal layout;
 *
 */
.noUi-pips-horizontal {
  padding: 10px 0;
  height: 80px;
  top: 100%;
  left: 0;
  width: 100%;
}

.noUi-value-horizontal {
  transform: translate(-50%, 50%);
}

.noUi-rtl .noUi-value-horizontal {
  transform: translate(50%, 50%);
}

.noUi-marker-horizontal.noUi-marker {
  margin-left: -1px;
  width: 2px;
  height: 5px;
}

.noUi-marker-horizontal.noUi-marker-sub {
  height: 10px;
}

.noUi-marker-horizontal.noUi-marker-large {
  height: 15px;
}

/* Vertical layout;
 *
 */
.noUi-pips-vertical {
  padding: 0 10px;
  height: 100%;
  top: 0;
  left: 100%;
}

.noUi-value-vertical {
  transform: translate(0, -50%);
  padding-left: 25px;
}

.noUi-rtl .noUi-value-vertical {
  transform: translate(0, 50%);
}

.noUi-marker-vertical.noUi-marker {
  width: 5px;
  height: 2px;
  margin-top: -1px;
}

.noUi-marker-vertical.noUi-marker-sub {
  width: 10px;
}

.noUi-marker-vertical.noUi-marker-large {
  width: 15px;
}

.noUi-tooltip {
  display: block;
  position: absolute;
  border: 1px solid #D9D9D9;
  border-radius: 3px;
  background: #fff;
  color: #000;
  padding: 5px;
  text-align: center;
  white-space: nowrap;
}

.noUi-horizontal .noUi-tooltip {
  transform: translate(-50%, 0);
  left: 50%;
  bottom: 120%;
}

.noUi-vertical .noUi-tooltip {
  transform: translate(0, -50%);
  top: 50%;
  right: 120%;
}

.noUi-horizontal .noUi-origin > .noUi-tooltip {
  transform: translate(50%, 0);
  left: auto;
  bottom: 10px;
}

.noUi-vertical .noUi-origin > .noUi-tooltip {
  transform: translate(0, -18px);
  top: auto;
  right: 28px;
}

.accordion-tab {
  border-style: solid;
  border-color: #d8dce6;
  border-width: 1px 1px 0;
  border-radius: 0.125rem;
}
.accordion-tab-head {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #d8dce6;
}
.accordion-tab-head > span {
  padding: 0 28px;
}
.accordion-tab-toggle {
  cursor: pointer;
  padding: 22px 26px;
  border-left: 1px solid #d8dce6;
  position: relative;
}
.accordion-tab-toggle::after {
  font-family: "lsm-icons";
  content: "\E9B8";
  transform-origin: center;
  transition: transform 0.3s;
  display: block;
}
.accordion-tab-toggle:not(.collapsed)::before {
  content: "";
  width: 100%;
  height: 1px;
  background: #fff;
  position: absolute;
  bottom: -1px;
  right: 0;
  display: block;
}
.accordion-tab-toggle[aria-expanded=true]::after {
  transform: rotate(-45deg);
}
.accordion-tab-content {
  border-bottom: 1px solid #d8dce6;
}
.accordion-tab-content > div {
  padding: 28px;
}

.alert {
  font-size: 14px;
  line-height: 21px;
}
.alert .alert-heading {
  font-size: 14px;
  line-height: 21px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
}

.alert-dismissible .close {
  top: 50%;
  transform: translateY(-50%);
  margin-right: 1.25rem;
  padding: 0;
  opacity: 1;
}

.alert-info {
  background-color: #f4f8fc;
  border-color: #a7c6e1;
  color: #337ab7;
}

.alert-success {
  background-color: #f7fdf9;
  border-color: #bdebd0;
  color: #64d091;
}

.alert-warning {
  background-color: #fdf7f7;
  border-color: #f8cdae;
  color: #38332e;
}

.article-link {
  font-size: 0.75rem;
  color: #002663;
  line-height: 1.625rem;
  letter-spacing: 0.0625rem;
  font-weight: bold;
  text-transform: uppercase;
  overflow: hidden;
  display: inline-block;
  position: relative;
  padding-right: 1em;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}
.article-link::before, .article-link::after {
  font-size: 0.5rem;
  display: inline-block;
  font-family: "lsm-icons";
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
  transition: all 0.2s ease-in-out;
}
.article-link::before {
  content: "\E930";
  left: 0;
  transform: translate(-100%, -50%);
}
.article-link::after {
  content: "\E930";
  right: 0;
}
.article-link:hover {
  padding-left: 1em;
  padding-right: 0;
}
.article-link:hover::before {
  transform: translate(0%, -50%);
}
.article-link:hover::after {
  transform: translate(100%, -50%);
}

.badge.is-interactive, .select2-container--default .select2-selection--multiple .is-interactive.select2-selection__choice {
  padding-right: 1.9em;
}
.badge.is-interactive::after, .select2-container--default .select2-selection--multiple .is-interactive.select2-selection__choice::after {
  content: "\EA0B";
  font-family: "lsm-icons";
  font-size: 7px;
  right: -1.3em;
  position: relative;
  opacity: 0.25;
}
.badge.is-interactive:hover::after, .select2-container--default .select2-selection--multiple .is-interactive.select2-selection__choice:hover::after {
  opacity: 1;
}
.badge.badge-pill, .select2-container--default .select2-selection--multiple .select2-selection__choice {
  border-radius: 50rem;
}
.badge.badge-lg, .select2-container--default .select2-selection--multiple .badge-lg.select2-selection__choice {
  padding: 7px 12px;
}
.badge .badge-remove, .select2-container--default .select2-selection--multiple .select2-selection__choice .badge-remove {
  margin-left: 0.3rem;
  cursor: pointer;
  font-style: normal;
}
.badge .badge-remove:after, .select2-container--default .select2-selection--multiple .select2-selection__choice .badge-remove:after {
  font-family: "lsm-icons";
  content: "\EA0B";
  font-size: 0.4375rem;
}
.badge.badge-light, .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #f4f8fc;
  color: #586c95;
}
.badge.badge-light:hover, .select2-container--default .select2-selection--multiple .select2-selection__choice:hover {
  background-color: #f4f8fc;
  color: inherit;
}

.breadcrumb-back {
  font-size: 0.75rem;
  font-weight: bold;
  line-height: 0.9375rem;
  text-transform: uppercase;
  letter-spacing: 0.0625rem;
}
.breadcrumb-back::before {
  font-size: 0.5rem;
  content: "\E92F";
  display: inline-block;
  padding-right: 0.5rem;
  font-family: "lsm-icons";
  position: relative;
  top: -1px;
}

.breadcrumb-item + .breadcrumb-item::before {
  font-size: 0.4375rem;
  font-family: "lsm-icons";
  position: relative;
  top: -2px;
}
.breadcrumb-item.active {
  font-weight: bold;
}
.breadcrumb-item.is-interactive::before {
  content: "\E912";
}
.breadcrumb-item:not(.active) a {
  color: #5e6983;
}

.btn, #ffFormCreate #ffForm div ul li a {
  text-transform: uppercase;
  letter-spacing: 1.5px;
  min-width: 9.375rem;
  text-align: center;
  cursor: pointer;
}
.btn:disabled, #ffFormCreate #ffForm div ul li a:disabled, .btn.disabled, #ffFormCreate #ffForm div ul li a.disabled {
  background-color: #ebedf2 !important;
  border-color: #ebedf2 !important;
  color: #bdc0c7 !important;
}
.btn.btn-primary:hover, #ffgenerated button.btn.btn-default:hover, #ffFormCreate #ffForm div ul li a.btn-primary:hover {
  background: #246095;
}
.btn.btn-outline, #ffFormCreate #ffForm div ul li a.btn-outline {
  background: transparent;
  border: 1px solid #2E6DA4;
  color: #002663;
}
.btn.btn-outline:hover, #ffFormCreate #ffForm div ul li a.btn-outline:hover {
  box-shadow: 0px 0px 10px #2e6da461;
}
.btn.btn-pending, #ffFormCreate #ffForm div ul li a.btn-pending {
  background: #FAFAFC;
  border: 1px solid #D8DCE6;
  color: #5E6983;
}
.btn.btn-pending:hover, #ffFormCreate #ffForm div ul li a.btn-pending:hover {
  box-shadow: 0px 0px 10px #2e6da461;
}
.btn.btn-yellow, #ffFormCreate #ffForm div ul li a.btn-yellow {
  background: #FDCA41;
  border: 1px solid #D8AC35;
  color: #fff;
}
.btn.btn-yellow:hover, #ffFormCreate #ffForm div ul li a.btn-yellow:hover {
  background: #eebc36;
}
.btn.btn-success, .page--lms #ffFormCreate .btn.btn-blue, #ffFormCreate .page--lms .btn.btn-blue, #ffgenerated button.btn.btn-success, #ffgenerated #ffFormCreate button.btn.btn-blue, #ffFormCreate #ffgenerated button.btn.btn-blue, #ffFormCreate .btn.btn-blue, #ffFormCreate #ffForm div ul li a.btn-success, #ffFormCreate #ffForm div ul li a.btn-blue {
  color: #fff;
}
.btn.btn-success:hover, .page--lms #ffFormCreate .btn.btn-blue:hover, #ffFormCreate .page--lms .btn.btn-blue:hover, #ffgenerated button.btn.btn-success:hover, #ffgenerated #ffFormCreate button.btn.btn-blue:hover, #ffFormCreate #ffgenerated button.btn.btn-blue:hover, #ffFormCreate .btn.btn-blue:hover, #ffFormCreate #ffForm div ul li a.btn-success:hover, #ffFormCreate #ffForm div ul li a.btn-blue:hover {
  background: #4ca872;
}
.btn.btn-danger:hover, #ffFormCreate #ffForm div ul li a.btn-danger:hover {
  background: #af4d4d;
}
.btn.btn-light, .page--lms .btn.btn-default, #ffgenerated button.btn.btn-info, #ffFormCreate #ffForm div ul li a {
  background: transparent;
  border: 1px solid #d8dce6;
  color: #5e6983;
}
.btn.btn-light.text-primary, .page--lms .btn.text-primary.btn-default, #ffgenerated button.btn.text-primary.btn-info, #ffFormCreate #ffForm div ul li a.text-primary {
  border-color: #337ab7;
}
.btn.btn-light:hover, .page--lms .btn.btn-default:hover, #ffgenerated button.btn.btn-info:hover, #ffFormCreate #ffForm div ul li a:hover {
  border-color: #5e6983;
}
.btn.btn-link, #ffFormCreate #ffForm div ul li a.btn-link {
  font-size: 0.75rem;
  color: #002663;
  font-weight: bold;
  min-width: auto;
}
.btn.btn-link.btn-greyish, #ffFormCreate #ffForm div ul li a.btn-link.btn-greyish {
  color: #5e6983;
}
.btn.btn-link:hover, #ffFormCreate #ffForm div ul li a.btn-link:hover {
  text-decoration: none;
  color: #337ab7;
}
.btn.btn-sm, #ffFormCreate #ffForm div ul li a.btn-sm, .btn-group-sm > .btn, #ffFormCreate #ffForm div ul li .btn-group-sm > a {
  min-width: 7.5rem;
}
.btn.btn-icon, #ffFormCreate #ffForm div ul li a.btn-icon {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.btn.btn-icon i, #ffFormCreate #ffForm div ul li a.btn-icon i {
  font-size: 0.875rem;
  display: inline-block;
  margin-right: 0.3125rem;
  vertical-align: middle;
}
.btn.btn-icon.btn-sm i, #ffFormCreate #ffForm div ul li a.btn-icon.btn-sm i, .btn-group-sm > .btn.btn-icon i, #ffFormCreate #ffForm div ul li .btn-group-sm > a.btn-icon i {
  font-size: 1rem;
}
.btn.btn-icon.btn-link, #ffFormCreate #ffForm div ul li a.btn-icon.btn-link {
  padding: 0;
}
.btn.btn-icon.btn-link i, #ffFormCreate #ffForm div ul li a.btn-icon.btn-link i {
  font-size: 0.8125rem;
  color: #337ab7;
}
.btn.btn-icon.btn-link.btn-greyish i, #ffFormCreate #ffForm div ul li a.btn-icon.btn-link.btn-greyish i {
  color: #5e6983;
}
.btn.btn-stack, #ffFormCreate #ffForm div ul li a.btn-stack {
  background: none;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  flex-direction: column;
  border: 0;
  font-weight: 700;
  min-width: auto;
}
.btn.btn-stack:not(:disabled):hover, #ffFormCreate #ffForm div ul li a.btn-stack:not(:disabled):hover, .btn.btn-stack:not(:disabled):focus, #ffFormCreate #ffForm div ul li a.btn-stack:not(:disabled):focus, .btn.btn-stack:not(:disabled):active, #ffFormCreate #ffForm div ul li a.btn-stack:not(:disabled):active {
  background: transparent;
  color: inherit;
}
.btn.btn-stack i, #ffFormCreate #ffForm div ul li a.btn-stack i {
  font-size: 0.875rem;
  width: 1.8125rem;
  height: 1.8125rem;
  border-radius: 50%;
  color: #fff;
  margin: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.75rem;
}
.btn.btn-stack i::before, #ffFormCreate #ffForm div ul li a.btn-stack i::before {
  position: relative;
  left: 1px;
}
.btn.btn-stack.btn-primary, #ffgenerated button.btn.btn-stack.btn-default, #ffFormCreate #ffForm div ul li a.btn-stack.btn-primary {
  color: #337ab7;
}
.btn.btn-stack.btn-primary i, #ffgenerated button.btn.btn-stack.btn-default i, #ffFormCreate #ffForm div ul li a.btn-stack.btn-primary i {
  background: #337ab7;
}
.btn.btn-stack.btn-primary:hover, #ffgenerated button.btn.btn-stack.btn-default:hover, #ffFormCreate #ffForm div ul li a.btn-stack.btn-primary:hover {
  color: #246095;
}
.btn.btn-stack.btn-primary:hover i, #ffgenerated button.btn.btn-stack.btn-default:hover i, #ffFormCreate #ffForm div ul li a.btn-stack.btn-primary:hover i {
  background: #246095;
}
.btn.btn-stack.btn-success, .page--lms #ffFormCreate .btn.btn-stack.btn-blue, #ffFormCreate .page--lms .btn.btn-stack.btn-blue, #ffgenerated button.btn.btn-stack.btn-success, #ffgenerated #ffFormCreate button.btn.btn-stack.btn-blue, #ffFormCreate #ffgenerated button.btn.btn-stack.btn-blue, #ffFormCreate .btn.btn-stack.btn-blue, #ffFormCreate #ffForm div ul li a.btn-stack.btn-success, #ffFormCreate #ffForm div ul li a.btn-stack.btn-blue {
  color: #64d091;
}
.btn.btn-stack.btn-success i, .page--lms #ffFormCreate .btn.btn-stack.btn-blue i, #ffFormCreate .page--lms .btn.btn-stack.btn-blue i, #ffgenerated button.btn.btn-stack.btn-success i, #ffgenerated #ffFormCreate button.btn.btn-stack.btn-blue i, #ffFormCreate #ffgenerated button.btn.btn-stack.btn-blue i, #ffFormCreate .btn.btn-stack.btn-blue i, #ffFormCreate #ffForm div ul li a.btn-stack.btn-success i, #ffFormCreate #ffForm div ul li a.btn-stack.btn-blue i {
  background: #64d091;
}
.btn.btn-stack.btn-success:hover, .page--lms #ffFormCreate .btn.btn-stack.btn-blue:hover, #ffFormCreate .page--lms .btn.btn-stack.btn-blue:hover, #ffgenerated button.btn.btn-stack.btn-success:hover, #ffgenerated #ffFormCreate button.btn.btn-stack.btn-blue:hover, #ffFormCreate #ffgenerated button.btn.btn-stack.btn-blue:hover, #ffFormCreate .btn.btn-stack.btn-blue:hover, #ffFormCreate #ffForm div ul li a.btn-stack.btn-success:hover, #ffFormCreate #ffForm div ul li a.btn-stack.btn-blue:hover {
  color: #4ca872;
}
.btn.btn-stack.btn-success:hover i, .page--lms #ffFormCreate .btn.btn-stack.btn-blue:hover i, #ffFormCreate .page--lms .btn.btn-stack.btn-blue:hover i, #ffgenerated button.btn.btn-stack.btn-success:hover i, #ffgenerated #ffFormCreate button.btn.btn-stack.btn-blue:hover i, #ffFormCreate #ffgenerated button.btn.btn-stack.btn-blue:hover i, #ffFormCreate .btn.btn-stack.btn-blue:hover i, #ffFormCreate #ffForm div ul li a.btn-stack.btn-success:hover i, #ffFormCreate #ffForm div ul li a.btn-stack.btn-blue:hover i {
  background: #4ca872;
}
.btn.btn-stack.btn-danger, #ffFormCreate #ffForm div ul li a.btn-stack.btn-danger {
  color: #cc6565;
}
.btn.btn-stack.btn-danger i, #ffFormCreate #ffForm div ul li a.btn-stack.btn-danger i {
  background: #cc6565;
}
.btn.btn-stack.btn-danger:hover, #ffFormCreate #ffForm div ul li a.btn-stack.btn-danger:hover {
  color: #af4d4d;
}
.btn.btn-stack.btn-danger:hover i, #ffFormCreate #ffForm div ul li a.btn-stack.btn-danger:hover i {
  background: #af4d4d;
}
.btn.btn-stack.btn-sm, #ffFormCreate #ffForm div ul li a.btn-stack.btn-sm, .btn-group-sm > .btn.btn-stack, #ffFormCreate #ffForm div ul li .btn-group-sm > a.btn-stack {
  flex-direction: row;
  align-items: center;
}
.btn.btn-stack.btn-sm.btn-icon i, #ffFormCreate #ffForm div ul li a.btn-stack.btn-sm.btn-icon i, .btn-group-sm > .btn.btn-stack.btn-icon i, #ffFormCreate #ffForm div ul li .btn-group-sm > a.btn-stack.btn-icon i {
  font-size: 8px;
  width: 1.1875rem;
  height: 1.1875rem;
  margin: 0 0.4375rem 0 0;
}

[class*=btn-outline-]:not(.btn-outline-primary) {
  font-size: 16px;
  font-weight: bold;
  border-radius: 22px;
  padding: 8px 14px;
  text-transform: none;
  transition: all 0.3s ease-in-out;
}
[class*=btn-outline-]:not(.btn-outline-primary).btn-sm, .btn-group-sm > [class*=btn-outline-].btn:not(.btn-outline-primary), #ffFormCreate #ffForm div ul li .btn-group-sm > a[class*=btn-outline-]:not(.btn-outline-primary) {
  padding: 2px 14px;
}

.btn-outline-light-blue {
  color: #67B7DC;
  border: 2px solid #67b7dc;
  background: #67B7DC1A 0% 0% no-repeat padding-box;
}
.btn-outline-light-blue:hover {
  color: #fff;
  background-color: #67B7DC;
}
.btn-outline-light-green {
  color: #4FC5A2;
  border: 2px solid #4fc5a2;
  background: #4FC5A21A 0% 0% no-repeat padding-box;
}
.btn-outline-light-green:hover {
  color: #fff;
  background-color: #4FC5A2;
}
.btn-outline-lavender {
  color: #9777DC;
  border: 2px solid #9777DC;
  background: #9777DC1A 0% 0% no-repeat padding-box;
}
.btn-outline-lavender:hover {
  color: #fff;
  background-color: #9777DC;
}

.card-featured {
  flex-direction: row;
}
.card-featured .card-img {
  width: 70%;
  flex-shrink: 0;
}
.card-featured .card-title {
  font-size: 1.3125rem;
  font-weight: bold;
  margin-bottom: 0.625rem;
  display: block;
}
.card-featured .card-body {
  font-size: 1rem;
  padding: 2rem;
  line-height: 1.5625rem;
}
.card-featured .card-text {
  margin-bottom: 1.375rem;
}
@media (min-width: 980px) and (max-width: 1579.98px) {
  .card-featured .card-img {
    width: 60%;
  }
}
@media (max-width: 979.98px) {
  .card-featured {
    flex-direction: column;
  }
  .card-featured .card-img {
    width: 100%;
  }
  .card-featured .card-body {
    padding: 1rem 0;
  }
}

.card-link:hover .card-body {
  transform: translateY(-0.3125rem);
}
.card-link .card-title {
  font-size: 0.875rem;
  font-weight: bold;
  line-height: 1.5rem;
  color: #002663;
  margin-bottom: 0;
}
.card-link .card-body {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem;
  transition: transform 0.3s ease-in-out;
  background-color: #fff;
}
.card-link .card-body .card-actions a {
  transition: opacity 0.3s ease-in-out;
}
.card-link .card-body .card-actions a:not(:last-of-type) {
  margin-right: 0.75rem;
}
.card-link .card-body .card-actions a:hover {
  text-decoration: none;
  opacity: 0.75;
}
.card-link .card-body .card-actions .icon, .card-link .card-body .card-actions #ffFormCreate .fa, #ffFormCreate .card-link .card-body .card-actions .fa {
  color: #002663;
}
.card-link .card-meta {
  font-size: 0.75rem;
  color: #5e6983;
  line-height: 1.3125rem;
  position: relative;
  padding-left: 0.9375rem;
}
.card-link .card-meta::before {
  content: "";
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8.5' height='11.114' viewBox='0 0 8.5 11.114'%3E%3Cdefs%3E%3Cstyle%3E.a%7Bfill:none;stroke:%232e6da4;stroke-linecap:square;%7D%3C/style%3E%3C/defs%3E%3Cpath class='a' d='M12.5,12.643,8.75,9.964,5,12.643V4.071A1.071,1.071,0,0,1,6.071,3h5.357A1.071,1.071,0,0,1,12.5,4.071Z' transform='translate(-4.5 -2.5)'/%3E%3C/svg%3E");
  display: block;
  position: absolute;
  width: 0.5625rem;
  height: 0.6875rem;
  left: 0;
  top: 0.3125rem;
}

.card-list-item {
  flex-direction: row;
  align-items: flex-start;
}
.card-list-item .card-img {
  width: 40%;
  flex-shrink: 0;
}
.card-list-item .card-title {
  font-size: 1rem;
  line-height: 1.4375rem;
  font-weight: bold;
  margin-bottom: 0.3125rem;
  display: block;
}
.card-list-item .card-body {
  font-size: 0.875rem;
  padding: 0.5rem 0.5rem 0.5rem 1.25rem;
  line-height: 1.375rem;
}
.card-list-item .card-text {
  margin-bottom: 0.3125rem;
}
@media (max-width: 979.98px) {
  .card-list-item {
    flex-direction: column;
  }
  .card-list-item .card-img {
    width: 100%;
  }
  .card-list-item .card-body {
    padding: 1rem 0;
  }
}

.card {
  border: 0;
  border-radius: 0;
  background: #fff;
  width: 100%;
}
.card:hover {
  text-decoration: none;
}

.card-img {
  position: relative;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  width: 100%;
  border-radius: 0;
}
.card-img:before {
  display: block;
  content: " ";
  width: 100%;
  padding-top: 60%;
}
.card-img > .inner {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.card-title {
  font-size: 1.3125rem;
  color: #2e6da4;
}

.card-body {
  padding: 1rem 0;
}

.code {
  position: relative;
}
.code .clipboard {
  position: absolute;
  right: 0.9375rem;
  top: 0.9375rem;
  background-color: #ebedf2;
  color: #7987a8;
  border-radius: 50%;
  width: 1.8125rem;
  height: 1.8125rem;
  text-decoration: none;
  text-align: center;
  line-height: 1.75rem;
}
.code pre {
  border: 1px solid #cbdded;
  border-radius: 0.125rem;
  margin-bottom: 0;
  max-height: 43.3125rem;
}

.hljs {
  font-size: 0.875rem;
  display: block;
  overflow-x: auto;
  color: #002663;
  background-color: #fff;
  line-height: 2.0625rem;
}

.hljs-ln tr:first-of-type td {
  padding-top: 0.5rem;
}
.hljs-ln tr:last-of-type td {
  padding-bottom: 0.5rem;
}
.hljs-ln tr td {
  padding: 0.125rem 0.6875rem;
}

.hljs-ln-numbers {
  font-size: 0.875rem;
  text-align: center;
  border-right: 1px solid #cbdded;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  background-color: #f4f8fc;
  color: #7987a8;
  line-height: 1.1875rem;
}

.hljs-number,
.hljs-variable,
.hljs-template-variable,
.hljs-string,
.hljs-doctag {
  color: #40929b;
}

.hljs-literal {
  color: #ef8c3f;
}

.hljs-comment,
.hljs-quote {
  color: #ef533f;
  font-style: italic;
}

.hljs-subst {
  font-weight: normal;
}

.hljs-emphasis {
  font-style: italic;
}

.hljs-strong {
  font-weight: bold;
}

.hljs-tag,
.hljs-name,
.hljs-attribute {
  color: #ef533f;
  font-weight: normal;
}

.hljs-attr,
.hljs-keyword,
.hljs-selector-tag,
.hljs-subst,
.hljs-regexp,
.hljs-link,
.hljs-symbol,
.hljs-bullet,
.hljs-built_in,
.hljs-builtin-name,
.hljs-deletion,
.hljs-addition {
  color: #ef533f;
}

.hljs-title,
.hljs-section,
.hljs-selector-id,
.hljs-type,
.hljs-meta {
  color: #ef533f;
  font-weight: bold;
}

.hljs-class .hljs-title {
  color: #ef533f;
  font-weight: bold;
}

.datepicker {
  border-radius: 0.125rem;
  padding: 0.6875rem 0.9375rem;
}
.datepicker > * {
  padding: 0.625rem;
}
.datepicker .datepicker-switch,
.datepicker .prev,
.datepicker .next {
  color: #5e6983;
}
.datepicker table tr td,
.datepicker table tr th {
  width: 30px;
  height: 30px;
  letter-spacing: 0;
  color: #002663;
  font-weight: bold;
}
.datepicker table tr td {
  border-radius: 50%;
}
.datepicker table tr td.active {
  background: #2e6da4 !important;
}
.datepicker table tr td.old, .datepicker table tr td.new {
  color: rgba(94, 105, 131, 0.3);
}
.datepicker table tr td span {
  height: 44px;
  line-height: 44px;
}
.datepicker table tr td span.active {
  background: #2e6da4 !important;
}

.datepicker-dropdown::before, .datepicker-dropdown::after {
  content: unset;
}

.dropdown-toggle {
  background: #fff;
  width: 1.875rem;
  height: 1.875rem;
  border-radius: 50%;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  border: 0;
  position: relative;
  top: 3px;
}
.dropdown-toggle::after {
  display: none;
}
.dropdown-toggle span {
  margin: 0 auto;
  position: relative;
  transform-origin: center;
  transition: 0.3s ease-out;
}
.dropdown-toggle span, .dropdown-toggle span::before, .dropdown-toggle span::after {
  display: block;
  width: 0.1875rem;
  height: 0.1875rem;
  background: #5e6983;
  border-radius: 50%;
}
.dropdown-toggle span::before, .dropdown-toggle span::after {
  content: "";
  position: absolute;
}
.dropdown-toggle span::before {
  top: -6px;
}
.dropdown-toggle span::after {
  bottom: -6px;
}
.dropdown-toggle:focus {
  outline: none;
}
.dropdown-toggle:hover, .dropdown-toggle[aria-expanded=true] {
  background: #f7f7f9;
}
.dropdown-toggle[aria-expanded=true] span {
  transform: rotate(90deg);
}
.dropdown-menu:not(.datepicker) {
  border: 0;
  box-shadow: 0 5px 15px 1px rgba(0, 0, 0, 0.1);
  margin: 1rem 0 0;
  padding: 0.5rem 0;
  transition: all 0.3s;
  max-height: 0;
  overflow: hidden;
  opacity: 0;
  z-index: 3000;
}
.dropdown-menu:not(.datepicker).show {
  max-height: 300px;
  opacity: 1;
}
.dropdown-menu:not(.datepicker) > * {
  font-size: 0.75rem;
  text-transform: uppercase;
  color: #5e6983;
  padding: 0.875rem 1.25rem;
  font-weight: 500;
  letter-spacing: 1.5px;
}
.dropdown-menu:not(.datepicker) > *:hover {
  background: none;
}

.field-type {
  position: relative;
  border: 1px solid #d8dce6;
  background: #fff;
  transition: 0.3s;
  display: block;
  cursor: pointer;
}
.field-type:before {
  display: block;
  content: " ";
  width: 100%;
  padding-top: 100%;
}
.field-type > .inner {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.field-type .inner {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.field-type .inner i {
  color: #337ab7;
  font-size: 30px;
  margin-bottom: 1.125rem;
}
.field-type .inner p {
  color: #002663;
  text-transform: uppercase;
  font-size: 11px;
  letter-spacing: 1.5px;
  font-weight: bold;
}
.field-type:hover {
  background: #f7f7f9;
}

.floodalerts-index .client-logo .subtitle, .floodalerts-show .client-logo .subtitle {
  color: #5E6983;
  font-size: 22px;
}
.floodalerts-index .tooltip, .floodalerts-show .tooltip {
  opacity: 1 !important;
}
.floodalerts-index .tooltip .tooltip-inner, .floodalerts-show .tooltip .tooltip-inner {
  background: #fff;
  color: #5e6983;
  text-align: left;
  padding: 1.2rem;
  border: 1px solid #d8dce6;
  font-size: 15px;
  line-height: 1.5;
  max-width: 430px;
}
.floodalerts-index .tooltip .tooltip-inner .alert-card-severe .alert-recommendation-title, .floodalerts-show .tooltip .tooltip-inner .alert-card-severe .alert-recommendation-title {
  color: #D0555F;
}
.floodalerts-index .tooltip .tooltip-inner .alert-card-prepare .alert-recommendation-title, .floodalerts-show .tooltip .tooltip-inner .alert-card-prepare .alert-recommendation-title {
  color: #E2C466;
}
.floodalerts-index .tooltip .tooltip-inner .alert-card-act .alert-recommendation-title, .floodalerts-show .tooltip .tooltip-inner .alert-card-act .alert-recommendation-title {
  color: #D67B63;
}
.floodalerts-index .tooltip.bs-tooltip-bottom .arrow::before, .floodalerts-index .tooltip.bs-tooltip-auto[x-placement^=bottom] .arrow::before, .floodalerts-show .tooltip.bs-tooltip-bottom .arrow::before, .floodalerts-show .tooltip.bs-tooltip-auto[x-placement^=bottom] .arrow::before {
  border-bottom-color: #d8dce6;
}

.flood-alert.alert-card {
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}
.flood-alert.alert-card.alert-card-severe .card-header {
  background-color: #FAEDEF;
  border: 1px solid #E8ADB2;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  font-size: 16px;
  font-weight: bold;
  color: #D0555F;
  line-height: 1;
  min-height: 60px;
  padding: 0 1.25rem;
}
.flood-alert.alert-card.alert-card-severe .card-header .card-title {
  font-size: 16px;
  color: #D0555F;
}
.flood-alert.alert-card.alert-card-severe .card-header .recommendation {
  text-decoration: underline;
}
.flood-alert.alert-card.alert-card-severe .card-header .last-updated {
  font-weight: normal;
}
@media (max-width: 979.98px) {
  .flood-alert.alert-card.alert-card-severe .card-header {
    font-size: 12px;
  }
  .flood-alert.alert-card.alert-card-severe .card-header .card-title {
    font-size: 12px;
  }
}
.flood-alert.alert-card.alert-card-prepare .card-header {
  background-color: #FCF8EC;
  border: 1px solid #F2E6AD;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  font-size: 16px;
  font-weight: bold;
  color: #E2C466;
  line-height: 1;
  min-height: 60px;
  padding: 0 1.25rem;
}
.flood-alert.alert-card.alert-card-prepare .card-header .card-title {
  font-size: 16px;
  color: #E2C466;
}
.flood-alert.alert-card.alert-card-prepare .card-header .recommendation {
  text-decoration: underline;
}
.flood-alert.alert-card.alert-card-prepare .card-header .last-updated {
  font-weight: normal;
}
@media (max-width: 979.98px) {
  .flood-alert.alert-card.alert-card-prepare .card-header {
    font-size: 12px;
  }
  .flood-alert.alert-card.alert-card-prepare .card-header .card-title {
    font-size: 12px;
  }
}
.flood-alert.alert-card.alert-card-act .card-header {
  background-color: #FCF1ED;
  border: 1px solid #F7D8C2;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  font-size: 16px;
  font-weight: bold;
  color: #D67B63;
  line-height: 1;
  min-height: 60px;
  padding: 0 1.25rem;
}
.flood-alert.alert-card.alert-card-act .card-header .card-title {
  font-size: 16px;
  color: #D67B63;
}
.flood-alert.alert-card.alert-card-act .card-header .recommendation {
  text-decoration: underline;
}
.flood-alert.alert-card.alert-card-act .card-header .last-updated {
  font-weight: normal;
}
@media (max-width: 979.98px) {
  .flood-alert.alert-card.alert-card-act .card-header {
    font-size: 12px;
  }
  .flood-alert.alert-card.alert-card-act .card-header .card-title {
    font-size: 12px;
  }
}
.flood-alert.alert-card .card-body {
  font-size: 14px;
  color: #5E6983;
  border: 1px solid #D8DCE6;
  border-top: none;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  padding: 20px;
}
.flood-alert.alert-card .card-body .location-name {
  font-size: 16px;
  font-weight: 600;
  color: #002663;
}
.flood-alert.alert-card .card-body .chart-section {
  border-top: 1px solid #D8DCE6;
  border-bottom: 1px solid #D8DCE6;
}
.flood-alert.alert-card .card-body #chartscrollbar svg {
  height: 85px !important;
}
.flood-alert.alert-card .water-level-chart {
  width: 100%;
  height: 300px;
  font-size: 9px;
}
.flood-alert.alert-card .flood-map {
  min-height: 300px;
  width: 100%;
}
.flood-alert.alert-card .noUi-horizontal {
  height: 10px;
}
.flood-alert.alert-card .noUi-target {
  background: #CFD3DF;
}
.flood-alert.alert-card .noUi-handle {
  border-radius: 50%;
  border: 1px solid #307AB7;
  box-shadow: none;
}
.flood-alert.alert-card .noUi-handle:before,
.flood-alert.alert-card .noUi-handle:after {
  display: none;
}
.flood-alert.alert-card .noUi-horizontal .noUi-handle {
  width: 20px;
  height: 20px;
  right: -11px;
}
.flood-alert.alert-card .noUi-connect {
  background: #307AB7;
}
.flood-alert.alert-card .noUi-marker-horizontal.noUi-marker {
  top: -8px;
  z-index: 10;
  width: 1px;
  background: #fff;
}
.flood-alert.alert-card .noUi-marker-horizontal.noUi-marker-sub {
  height: 8px;
}
.flood-alert.alert-card .noUi-value, .flood-alert.alert-card .noUi-value-sub {
  font-size: 9px;
  color: #BFC0C4;
  transform: rotate(90deg);
  margin-left: -12px;
}
.flood-alert.alert-card .mapscrollbarContainer-wrapper {
  overflow-x: auto;
}
.flood-alert.alert-card .mapscrollbarContainer {
  padding: 20px 30px 40px;
  position: relative;
  min-width: 800px;
}
.flood-alert.alert-card .mapscrollbar_date {
  font-size: 11px;
  line-height: 1;
  font-weight: bold;
  color: #002663;
  padding-left: 5px;
  border-left: 1px solid #002663;
  position: absolute;
  top: 0;
}
.flood-alert.alert-card .mapscrollbar_date.date-1 {
  left: 30px;
}
.flood-alert.alert-card .mapscrollbar_date.mapscrollbar_last_date {
  border-left: 0;
  padding-left: 0;
  border-right: 1px solid #002663;
  padding-right: 5px;
}
.flood-alert.alert-card .noUi-pips-horizontal {
  height: auto;
}
@media screen and (max-width: 601px) {
  .flood-alert.alert-card .tooltip .tooltip-inner {
    font-size: 13px;
    max-width: 300px;
  }
}

.flood-notification {
  background: #D36B87;
  display: inline-block !important;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  padding: 0 !important;
  top: -5px;
  position: relative;
}

.alert-flood-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='61.2' height='61.2' viewBox='0 0 61.2 61.2'%3E%3Cg id='Group_1831' data-name='Group 1831' transform='translate(-140.712 -295.131)'%3E%3Cpath id='Path_825' data-name='Path 825' d='M141.312,387.731c5,0,5,4.8,10,4.8s5-4.8,10-4.8,5,4.8,10,4.8,5-4.8,10-4.8,5,4.8,10,4.8,5-4.8,10-4.8' transform='translate(0 -36.8)' fill='none' stroke='%23d0555f' stroke-linecap='round' stroke-miterlimit='10' stroke-width='1.2'/%3E%3Cpath id='Path_826' data-name='Path 826' d='M141.312,375.731c5,0,5,4.8,10,4.8s5-4.8,10-4.8,5,4.8,10,4.8,5-4.8,10-4.8,5,4.8,10,4.8,5-4.8,10-4.8' transform='translate(0 -32)' fill='none' stroke='%23d0555f' stroke-linecap='round' stroke-miterlimit='10' stroke-width='1.2'/%3E%3Cpath id='Path_827' data-name='Path 827' d='M141.312,363.731c5,0,5,4.8,10,4.8s5-4.8,10-4.8,5,4.8,10,4.8,5-4.8,10-4.8,5,4.8,10,4.8,5-4.8,10-4.8' transform='translate(0 -27.2)' fill='none' stroke='%23d0555f' stroke-linecap='round' stroke-miterlimit='10' stroke-width='1.2'/%3E%3Cpath id='Path_828' data-name='Path 828' d='M141.312,351.731c5,0,5,4.8,10,4.8s5-4.8,10-4.8,5,4.8,10,4.8,5-4.8,10-4.8,5,4.8,10,4.8,5-4.8,10-4.8' transform='translate(0 -22.4)' fill='none' stroke='%23d0555f' stroke-linecap='round' stroke-miterlimit='10' stroke-width='1.2'/%3E%3Cpath id='Path_829' data-name='Path 829' d='M188.812,333.966V295.731h21v38.383' transform='translate(-19)' fill='none' stroke='%23d0555f' stroke-linecap='round' stroke-miterlimit='10' stroke-width='1.2'/%3E%3Crect id='Rectangle_1409' data-name='Rectangle 1409' width='6' height='6' transform='translate(172.812 316.731)' fill='none' stroke='%23d0555f' stroke-linecap='round' stroke-miterlimit='10' stroke-width='1.2'/%3E%3Crect id='Rectangle_1410' data-name='Rectangle 1410' width='6' height='6' transform='translate(181.812 316.731)' fill='none' stroke='%23d0555f' stroke-linecap='round' stroke-miterlimit='10' stroke-width='1.2'/%3E%3Crect id='Rectangle_1411' data-name='Rectangle 1411' width='6' height='6' transform='translate(172.812 307.731)' fill='none' stroke='%23d0555f' stroke-linecap='round' stroke-miterlimit='10' stroke-width='1.2'/%3E%3Crect id='Rectangle_1412' data-name='Rectangle 1412' width='6' height='6' transform='translate(181.812 307.731)' fill='none' stroke='%23d0555f' stroke-linecap='round' stroke-miterlimit='10' stroke-width='1.2'/%3E%3Crect id='Rectangle_1413' data-name='Rectangle 1413' width='6' height='6' transform='translate(172.812 298.731)' fill='none' stroke='%23d0555f' stroke-linecap='round' stroke-miterlimit='10' stroke-width='1.2'/%3E%3Crect id='Rectangle_1414' data-name='Rectangle 1414' width='6' height='6' transform='translate(181.812 298.731)' fill='none' stroke='%23d0555f' stroke-linecap='round' stroke-miterlimit='10' stroke-width='1.2'/%3E%3Cpath id='Path_830' data-name='Path 830' d='M158.812,340.114V310.731h18v29.235' transform='translate(-7 -6)' fill='none' stroke='%23d0555f' stroke-linecap='round' stroke-miterlimit='10' stroke-width='1.2'/%3E%3Cline id='Line_445' data-name='Line 445' x2='18' transform='translate(151.812 309.531)' fill='none' stroke='%23d0555f' stroke-linecap='round' stroke-miterlimit='10' stroke-width='1.2'/%3E%3Cline id='Line_446' data-name='Line 446' x2='18' transform='translate(151.812 314.331)' fill='none' stroke='%23d0555f' stroke-linecap='round' stroke-miterlimit='10' stroke-width='1.2'/%3E%3Cline id='Line_447' data-name='Line 447' x2='18' transform='translate(151.812 319.131)' fill='none' stroke='%23d0555f' stroke-linecap='round' stroke-miterlimit='10' stroke-width='1.2'/%3E%3Cline id='Line_448' data-name='Line 448' x2='18' transform='translate(151.812 323.931)' fill='none' stroke='%23d0555f' stroke-linecap='round' stroke-miterlimit='10' stroke-width='1.2'/%3E%3C/g%3E%3C/svg%3E%0A");
  width: 60px;
  height: 60px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  margin-right: 1rem;
}
@media (max-width: 979.98px) {
  .alert-flood-icon {
    width: 30px;
    height: 30px;
  }
}

.form-container input[type=checkbox] {
  height: auto !important;
  width: auto !important;
}
.form-container .form-body {
  border: 1px solid #d8dce6;
  padding: 1.875rem 1.875rem 3.75rem;
  margin-bottom: -1.875rem;
  border-top-left-radius: 0.125rem;
  border-top-right-radius: 0.125rem;
}
.form-container .form-footer .btn, .form-container .form-footer #ffFormCreate #ffForm div ul li a, #ffFormCreate #ffForm div ul li .form-container .form-footer a {
  border-radius: 0 0 0.125rem 0.125rem;
}

label {
  font-size: 0.875rem;
  font-weight: bold;
  margin-bottom: 0.5625rem;
}
label.icon::before, #ffFormCreate label.fa::before {
  font-size: 0.75rem;
  position: absolute;
  z-index: 10;
  top: 1rem;
  right: 0.9375rem;
}

.input-group {
  position: relative;
}
.input-group.icon::before, #ffFormCreate .input-group.fa::before {
  font-size: 0.75rem;
  position: absolute;
  z-index: 10;
  top: 1rem;
  right: 0.9375rem;
}
.input-group.is-interactive {
  box-shadow: 0 5px 5px rgba(46, 109, 164, 0.06);
  transition: box-shadow 0.3s;
}
.input-group.is-interactive:hover {
  box-shadow: 0 5px 10px rgba(46, 109, 164, 0.2);
}
.input-group.is-interactive:hover input,
.input-group.is-interactive:hover button {
  border-color: #337ab7;
}
.input-group.is-interactive:hover input .icon, .input-group.is-interactive:hover input #ffFormCreate .fa, #ffFormCreate .input-group.is-interactive:hover input .fa,
.input-group.is-interactive:hover button .icon,
.input-group.is-interactive:hover button #ffFormCreate .fa,
#ffFormCreate .input-group.is-interactive:hover button .fa {
  color: #337ab7;
}
.input-group.is-interactive input {
  background-color: #fff;
  border-color: #cbdded;
  color: #002663;
  font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}
.input-group.is-interactive button {
  border-color: #cbdded;
  min-width: unset;
  padding: 0 1rem;
}
.input-group.is-interactive button .icon, .input-group.is-interactive button #ffFormCreate .fa, #ffFormCreate .input-group.is-interactive button .fa {
  color: #2e6da4;
}

.invalid-feedback {
  margin-top: 0.4375rem;
  text-align: right;
}

.form-control {
  height: 45px;
}
.form-control:disabled, .form-control.disabled {
  cursor: not-allowed;
}
.form-control.is-invalid {
  background-color: #fff;
}

.custom-control {
  padding-left: 2.25rem;
}

.custom-control-label {
  font-weight: 400;
}
.custom-control-label::before {
  width: 1.875rem;
  height: 1.875rem;
}

.custom-checkbox .custom-control-label::before {
  background-color: #fafafc;
  top: -0.35rem;
  left: -2.25rem;
}
.custom-checkbox .custom-control-label::after {
  width: 1.8rem;
  height: 1.8rem;
  top: 0.03rem;
  left: -1.75rem;
}
.custom-checkbox .custom-control-input:checked ~ .custom-control-label::after {
  content: "\E92B";
  font-family: "lsm-icons";
  color: #fff;
}

.custom-radio .custom-control-label::before {
  border-color: #d8dce6;
  background-color: #fff;
  top: -0.35rem;
  left: -2.25rem;
}
.custom-radio .custom-control-label::after {
  top: 0.25rem;
  left: -1.65rem;
}
.custom-radio .custom-control-input:checked ~ .custom-control-label::before {
  border-color: #d8dce6;
  background-color: #fff;
}
.custom-radio .custom-control-input:checked ~ .custom-control-label::after {
  background-color: #337ab7;
  border-radius: 50%;
  width: 0.75rem;
  height: 0.75rem;
  display: block;
}

.no-resize {
  resize: none;
}

.has-icon {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
}
.has-icon .icon, .has-icon #ffFormCreate .fa, #ffFormCreate .has-icon .fa {
  margin-left: 5px;
  color: #337ab7;
  font-size: 12px;
}

hr.with-content {
  position: relative;
  text-align: center;
  opacity: 0.5;
  width: 5.3125rem;
  margin: 0;
}
hr.with-content::after {
  font-size: 0.75rem;
  content: attr(data-content);
  font-weight: bold;
  display: inline-block;
  position: relative;
  top: -0.8em;
  background: #fff;
  padding: 0 0.625rem;
}

@media (min-width: 600px) {
  .hero.forum-hero {
    min-height: 350px;
  }
}
.hero.forum-hero .hero-content {
  max-width: 26.875rem;
}

.sectors-container {
  max-height: 50vh;
  overflow-y: auto;
}
.sectors-item {
  display: block;
  position: relative;
  width: 100%;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
  color: #5e6983;
  background: #fff;
  border: 1px solid #d8dce6;
  border-radius: 0.125rem;
}
.sectors-item:before {
  display: block;
  content: " ";
  width: 100%;
  padding-top: 28.5714285714%;
}
.sectors-item > .inner {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.sectors-item:hover {
  color: inherit;
  border-color: #5e6983;
}
.sectors-item.active {
  background: #337ab7;
  color: #fff;
}
.sectors-item.active:after {
  font-family: "lsm-icons";
  content: "\E92B";
  opacity: 0.7;
  position: absolute;
  bottom: 10px;
  right: 15px;
}
.sectors-item .inner {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  justify-content: center;
  align-items: center;
}
.sectors-item.loading {
  opacity: 0.5;
  pointer-events: none;
}
.sectors-item.loading .sectors-spinner {
  display: inline-block;
}
.sectors-spinner {
  display: none;
  z-index: 2;
  left: -webkit-calc(50% - 12.5px);
  left: calc(50% - 12.5px);
  top: -webkit-calc(50% - 12.5px);
  top: calc(50% - 12.5px);
}

.forum-dropdown-toggle {
  width: 45px;
  height: 45px;
  border: 1px solid #d8dce6;
  border-radius: 0.125rem;
  background: #fff;
  background-image: url("data:image/svg+xml,%3Csvg id='Layer_1' data-name='Layer 1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 18 12'%3E%3Cpath d='M7,12h4V10H7ZM0,0V2H18V0ZM3,7H15V5H3Z' style='fill:%23337ab7'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-size: 50%;
  background-position: center;
}

.forum-profile {
  width: 44px;
  height: 44px;
  background-color: #f7f7f9;
  background-position: center;
  background-size: contain;
  background-repeat: no-repeat;
  text-align: center;
  line-height: 44px;
  font-size: 20px;
  color: #337ab7;
}
.forum-profile + p {
  font-weight: 500;
}

.forum-title-bar {
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  padding: 12px 0;
}
.forum-title-bar i {
  font-size: 20px;
}
.forum-title-bar.bar-podcast {
  background-image: url("/img/forum/podcast.png");
}
.forum-title-bar.bar-events {
  background-image: url("/img/forum/events.png");
}
.forum-title-bar.bar-webinar {
  background-image: url("/img/forum/webinar.png");
}

.forum-cover {
  background-image: url("/img/forum/cover.png");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center right;
}

.forum-shadow {
  box-shadow: 0 3px 3px rgba(0, 0, 0, 0.03);
}
.forum-shadow:not(#comments) {
  transition: border-color 0.2s;
}
.forum-shadow:not(#comments):hover {
  border-color: #002663 !important;
}

/*
* forum components
*/
.forum-poll-results {
  line-height: 1.2;
  font-size: 14px;
}
.forum-poll-results-bar {
  margin: 6px 0 5px;
  height: 15px;
  border-radius: 2px;
  background: #002663;
  background: -webkit-gradient(linear, left top, right top, from(#002663), to(#337ab7));
  background: -o-linear-gradient(left, #002663 0%, #337ab7 100%);
  background: linear-gradient(to right, #002663 0%, #337ab7 100%);
}

.forum-calendar {
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  min-height: 200px;
  background-image: url("/img/forum/calendar_fallback.png");
}
.forum-calendar-icon {
  padding: 8px 8px 6px 7px;
  position: relative;
}
.forum-calendar-icon i {
  font-size: 36px;
  color: #002663;
}
.forum-calendar-icon span {
  font-size: 14px;
  position: absolute;
  top: 60%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-weight: 500;
}

.forum-box {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  font-size: 12px;
  line-height: 1.1;
  border: 1px solid #d8dce6;
  border-radius: 0.125rem;
  margin-bottom: 10px;
}
.forum-box-image {
  position: relative;
  width: 30%;
  flex-shrink: 0;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}
.forum-box-image:before {
  display: block;
  content: " ";
  width: 100%;
  padding-top: 75%;
}
.forum-box-image > .inner {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.forum-box-content {
  background: #fff;
  position: relative;
  font-weight: 500;
  color: #002663;
  padding: 7px 40px 7px 7px;
  min-height: 52px;
  flex-grow: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
}
.forum-box-content:before {
  content: "";
  background-image: url("/img/forum/box.png");
  background-position: center right;
  background-size: contain;
  background-repeat: no-repeat;
  height: 100%;
  width: 30px;
  position: absolute;
  left: -30px;
  top: 0;
  z-index: 1;
}
@media (max-width: 979.98px) {
  .forum-box-content:before {
    width: 10vw;
    left: -10vw;
  }
}
.forum-box-content:after {
  font-family: "lsm-icons";
  content: "\E930";
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #337ab7;
}
@media (min-width: 600px) and (max-width: 979.98px) {
  .forum-box-content {
    width: 48%;
  }
}

@media (min-width: 980px) {
  .forum-sticky {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 10;
  }
  .forum-sticky.sticky-shadow {
    border-bottom: 1px solid #d8dce6;
    box-shadow: 0 3px 5px -5px rgba(0, 0, 0, 0.7);
    margin-left: -10px;
    margin-right: -10px;
    padding: 0 10px;
  }
}
.ui-autocomplete-input {
  border: 1px solid #d8dce6;
}
.ui-autocomplete-input:focus {
  border-color: #d8dce6;
}

ul.ui-autocomplete {
  border: 1px solid #d8dce6;
  background: #fff;
  list-style-type: none;
  margin: -2px 0 0 0;
  padding: 0;
  max-height: 200px;
  overflow-y: auto;
}
ul.ui-autocomplete li {
  padding: 1rem;
  cursor: pointer;
}
ul.ui-autocomplete li:hover {
  background: #fafafc;
}

.mejs__container {
  background: none;
}
.mejs__container .mejs__controls:not([style*="display: none"]) {
  background: #002663;
  border-radius: 30px;
}
.mejs__container .mejs__time-rail {
  padding-top: 13px;
}
.mejs__container .mejs__time-handle-content {
  top: -7px;
}
.mejs__container .mejs__time-total,
.mejs__container .mejs__time-buffering,
.mejs__container .mejs__time-loaded,
.mejs__container .mejs__time-current,
.mejs__container .mejs__time-float,
.mejs__container .mejs__time-hovered,
.mejs__container .mejs__time-float-current,
.mejs__container .mejs__time-float-corner,
.mejs__container .mejs__time-marker {
  height: 5px;
}
.mejs__container .mejs__horizontal-volume-total {
  background: rgba(255, 255, 255, 0.3);
}

#comments .border-bottom:last-child {
  border: none !important;
}
#comments img {
  max-width: 100%;
  margin: 1rem 0;
}

.no-text-break {
  white-space: nowrap;
}

.letter-spacing {
  letter-spacing: 1.5px;
}

.font-12 {
  font-size: 12px;
  line-height: 1.1;
}

.font-16 {
  font-size: 16px;
  line-height: 1.3;
}

.font-18 {
  font-size: 18px;
  line-height: 1.3;
}

.font-20 {
  font-size: 20px;
  line-height: 1.2;
}

.opacity-50 {
  opacity: 0.5;
}

.forum-poll-confirm {
  border: 1px solid #d8dce6;
  text-transform: uppercase;
  padding: 18px 25px;
  font-size: 0.75rem;
  border-radius: 0.125rem;
  font-weight: 500;
}

/* text truncate */
.forum-box-truncate {
  width: 100%;
  padding: 2px 0;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  /* autoprefixer: off */
  -webkit-box-orient: vertical;
  /* autoprefixer: on */
}

.list-bordered {
  margin: 0;
  padding: 0;
  list-style-type: none;
  background: #fff;
  border: 1px solid #d8dce6;
  border-radius: 0.125rem;
}
.list-bordered li {
  position: relative;
  color: #5e6983;
  padding-right: 1.5rem;
}
.list-bordered li a,
.list-bordered li span {
  padding: 1.5rem 2.125rem 1.5rem 1.5rem;
  display: block;
  color: inherit;
}
.list-bordered li a:hover,
.list-bordered li span:hover {
  text-decoration: none;
}
.list-bordered li:not(:last-child) {
  border-bottom: 1px solid #d8dce6;
}
.list-bordered li::after {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 20px;
  font-size: 12px;
  margin-right: 12px;
  font-family: "lsm-icons";
  content: "\E930";
}
.list-bordered li:hover {
  background: #fff;
}
.list-bordered li:hover a {
  text-decoration: underline;
}
.list-bordered:hover {
  background: #fafafc;
}

.list-content {
  margin: 0;
  padding: 0;
  list-style-type: none;
  color: #002663;
}
.list-content li {
  font-size: 1rem;
  counter-increment: step-counter;
  position: relative;
  padding-left: 35px;
  line-height: 1.6875rem;
}
.list-content li:not(.active) {
  color: rgba(0, 38, 99, 0.5);
}
.list-content li:not(.active)::before {
  border-color: #d8dce6;
}
.list-content li:not(:last-child) {
  padding-bottom: 30px;
}
.list-content li:not(:last-child)::after {
  content: "";
  background-color: #d8dce6;
  width: 1px;
  height: 100%;
  display: block;
  position: absolute;
  left: 12px;
  top: 0;
  z-index: -1;
}
.list-content li::before {
  font-size: 0.75rem;
  content: counter(step-counter);
  background-color: #fff;
  position: absolute;
  left: 0;
  margin-right: 12px;
  border: 1px solid #2e6da4;
  border-radius: 50%;
  width: 1.6875rem;
  height: 1.6875rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  justify-content: center;
}

.list-simple {
  margin: 0;
  padding: 0;
  list-style-type: none;
  color: #337ab7;
}
.list-simple li {
  position: relative;
  padding-left: 25px;
}
.list-simple li:not(:last-child) {
  margin-bottom: 30px;
}
.list-simple li::before {
  position: absolute;
  top: 0;
  left: 0;
  font-size: 13px;
  margin-right: 12px;
  font-family: "lsm-icons";
  content: "\E912";
}

.list-sortable {
  padding: 0;
  margin: 0;
  list-style-type: none;
}
.list-sortable-handle {
  display: block;
  width: 0.5625rem;
  height: 0.875rem;
  background-image: url('data:image/svg+xml,%3Csvg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 9 14.01"%3E%3Ccircle cx="1" cy="1" r="1" style="fill: %235e6983"/%3E%3Ccircle cx="8" cy="1" r="1" style="fill: %235e6983"/%3E%3Ccircle cx="1" cy="7" r="1" style="fill: %235e6983"/%3E%3Ccircle cx="8" cy="7" r="1" style="fill: %235e6983"/%3E%3Ccircle cx="1" cy="13.01" r="1" style="fill: %235e6983"/%3E%3Ccircle cx="8" cy="13.01" r="1" style="fill: %235e6983"/%3E%3C/svg%3E%0A');
  background-size: contain;
  background-repeat: no-repeat;
  cursor: -webkit-grab;
  cursor: grab;
  margin-right: 0.75rem;
  flex-shrink: 0;
}
.list-sortable li {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  width: 100%;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #d8dce6;
  padding: 0.875rem 1.5rem;
  margin-bottom: 1.5625rem;
  background: #fff;
}
.list-sortable li p {
  font-size: 1rem;
  flex-grow: 1;
  flex-shrink: 0;
}
@media (max-width: 979.98px) {
  .list-sortable li p {
    max-width: 65%;
  }
}
.list-sortable li .btn, .list-sortable #ffFormCreate #ffForm div ul li a, #ffFormCreate #ffForm div ul .list-sortable li a {
  margin-right: 1.25rem;
}
.list-sortable li.slip-reordering {
  background: #fff;
  border: 1px solid #337ab7;
  box-shadow: 0 5px 15px 1px rgba(0, 0, 0, 0.1);
}

.mention-item.active, .mention-item:hover {
  color: #337ab7;
}

* {
  color-adjust: exact;
  -webkit-print-color-adjust: exact;
  print-color-adjust: exact;
}

@media print {
  footer,
.btn-print,
.disable-box,
.reminder,
.report-preview,
.show-hide-toggle,
.toggle-assesment,
.toggle-arrow {
    display: none !important;
    visibility: hidden;
  }

  header {
    display: none;
  }

  .header-bar {
    border-top: none;
  }
}
.microsite {
  border: 0px;
}
.microsite .login-page-bg {
  position: absolute;
  width: 50%;
  height: 100%;
  top: 0;
  right: 0;
  background-image: url("/img/login-lm.jpg");
  background-size: cover;
  background-repeat: no-repeat;
  background-position: top center;
}

.micro-welcome-page .content {
  margin-bottom: 51px;
  margin-top: 95px;
}
.micro-welcome-page .content h2 {
  font-size: 24px;
  font-weight: 700;
  color: #002663;
  margin-bottom: 17px;
}
.micro-welcome-page .content p {
  font-size: 16px;
  font-weight: 400;
  color: #5E6983;
}
.micro-welcome-page .form-container label {
  font-size: 16px;
  font-weight: 500;
  color: #002663;
}
.micro-welcome-page__otp .content {
  margin-bottom: 11px;
}

.micro-landing-page .container {
  width: 100%;
  max-width: 1432px;
  padding: 0 20px;
}
.micro-landing-page .show-hide-toggle {
  font-size: 22px;
  cursor: pointer;
}
.micro-landing-page .show-hide-toggle .icon-eye {
  display: block;
  position: relative;
  z-index: 1;
}
.micro-landing-page .show-hide-toggle .icon-eye:hover + .hide-tip {
  opacity: 1;
  visibility: visible;
}
.micro-landing-page .hide-tip {
  position: absolute;
  top: 32px;
  left: 50%;
  width: 125px;
  border-radius: 8px;
  background: #272727;
  padding: 12px 7px;
  font-size: 14px;
  font-weight: 400;
  color: #FFF;
  letter-spacing: 0.28px;
  text-align: center;
  z-index: 4;
  opacity: 0;
  visibility: hidden;
  transform: translateX(-50%);
  transition: opacity 0.4s ease-out, visibility 0.4s ease-out;
}
.micro-landing-page .show-toggle-hidden-content {
  position: absolute;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  justify-content: center;
  align-items: center;
  top: 28px;
  left: 50%;
  width: 223px;
  height: 36px;
  border-radius: 30px;
  background: #002663;
  z-index: 2;
  opacity: 0;
  visibility: hidden;
  transform: translateX(-50%);
  transition: opacity 0.4s ease-out, visibility 0.4s ease-out;
}
@media (prefers-reduced-motion: reduce) {
  .micro-landing-page .show-toggle-hidden-content {
    transition: none;
  }
}
.micro-landing-page .show-toggle-hidden-content:hover {
  text-decoration: none;
}
.micro-landing-page .show-toggle-hidden-content p {
  font-size: 14px;
  color: #C7DCEE;
  margin-left: 10px;
}
.micro-landing-page .show-toggle-hidden-content p span {
  color: #fff;
  text-decoration: underline;
}
.micro-landing-page .dot {
  display: block;
  width: 12px;
  height: 12px;
  min-width: 12px;
  min-height: 12px;
  border-radius: 100%;
  margin-right: 5px;
}
.micro-landing-page .dot-green {
  background-color: #49C993;
  border: 1px solid #40A87C;
}
.micro-landing-page .dot-blue {
  background: #337ab7;
  border: 1px solid #002663;
}
.micro-landing-page .dot-yellow {
  background: #FDCA41;
  border: 1px solid #D8AC35;
}
.micro-landing-page .dot-orange {
  background: #FF9D43;
  border: 1px solid #D98538;
}
.micro-landing-page .dot-red {
  background: #DC6788;
  border: 1px solid #AC4662;
}
.micro-landing-page .disable-box {
  position: relative;
}
.micro-landing-page .disable-box:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #fff;
  opacity: 0.85;
  z-index: 1;
}
.micro-landing-page .disable-box .show-hide-toggle {
  background: rgba(94, 105, 131, 0.1);
  opacity: 0;
}
.micro-landing-page .disable-box .show-toggle-parent {
  opacity: 1;
  visibility: visible;
}
.micro-landing-page .heading {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
}
.micro-landing-page .heading p {
  font-size: 24px;
  font-weight: 700;
  color: #002663;
  margin-left: 4px;
}
@media (max-width: 1135px) {
  .micro-landing-page .heading p {
    font-size: 19px;
  }
}
.micro-landing-page .heading img {
  display: block;
}
@media (max-width: 1135px) {
  .micro-landing-page .heading img {
    width: 30px;
    height: auto;
  }
}
.micro-landing-page .heading .icon, .micro-landing-page .heading #ffFormCreate .fa, #ffFormCreate .micro-landing-page .heading .fa {
  font-size: 36px;
  color: #2E6DA4;
}
.micro-landing-page .heading .mobile-toggle {
  display: none;
  position: relative;
  width: 24px;
  height: 24px;
  margin-right: 2px;
  cursor: pointer;
}
@media (max-width: 1140px) {
  .micro-landing-page .heading .mobile-toggle {
    display: block;
  }
}
.micro-landing-page .heading .mobile-toggle.active .icon, .micro-landing-page .heading .mobile-toggle.active #ffFormCreate .fa, #ffFormCreate .micro-landing-page .heading .mobile-toggle.active .fa {
  transform: rotate(-180deg);
}
.micro-landing-page .heading .mobile-toggle .icon, .micro-landing-page .heading .mobile-toggle #ffFormCreate .fa, #ffFormCreate .micro-landing-page .heading .mobile-toggle .fa {
  position: absolute;
  top: 6px;
  left: 0;
  font-size: 15px;
  transition: transform 0.4s ease-out;
}
@media (prefers-reduced-motion: reduce) {
  .micro-landing-page .heading .mobile-toggle .icon, .micro-landing-page .heading .mobile-toggle #ffFormCreate .fa, #ffFormCreate .micro-landing-page .heading .mobile-toggle .fa {
    transition: none;
  }
}
.micro-landing-page .mobile-toggle-content {
  display: block;
  margin-top: 27px;
}
@media not print {
  @media (max-width: 1140px) {
    .micro-landing-page .mobile-toggle-content {
      display: none;
    }
  }
}
.micro-landing-page .report-preview {
  background: #e4ebf1;
  padding: 20px 0;
}
.micro-landing-page .report-preview-flex {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.micro-landing-page .report-preview .container {
  position: relative;
}
.micro-landing-page .report-preview .back-editor {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 700;
  color: #002663;
}
.micro-landing-page .report-preview .back-editor span {
  display: block;
  margin-left: 12px;
}
.micro-landing-page .report-preview .center-title {
  font-size: 24px;
  color: rgba(0, 38, 99, 0.75);
  font-weight: 500;
  text-align: center;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.micro-landing-page .report-preview .btn, .micro-landing-page .report-preview #ffFormCreate #ffForm div ul li a, #ffFormCreate #ffForm div ul li .micro-landing-page .report-preview a {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 150px;
  font-size: 16px;
  font-weight: 700;
  text-transform: none;
  letter-spacing: inherit;
  padding: 8px 4px 7px;
  margin-left: 11px;
}
.micro-landing-page .report-preview .btn span, .micro-landing-page .report-preview #ffFormCreate #ffForm div ul li a span, #ffFormCreate #ffForm div ul li .micro-landing-page .report-preview a span {
  display: block;
  margin-left: 4px;
}
.micro-landing-page .header-bar {
  border-top: 1px solid #D8DCE6;
  border-bottom: 1px solid #D8DCE6;
  background: #fff;
  padding: 22px 0;
}
.micro-landing-page .header-bar__flex {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
@media not print {
  @media (max-width: 991px) {
    .micro-landing-page .header-bar__flex {
      flex-wrap: wrap;
    }
  }
  @media (max-width: 768px) {
    .micro-landing-page .header-bar__flex {
      flex-direction: column;
    }
  }
}
@media not print {
  @media (max-width: 768px) {
    .micro-landing-page .header-bar .reports {
      margin-bottom: 10px;
    }
  }
}
.micro-landing-page .header-bar .reports .details {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  font-size: 19px;
}
@media not print {
  @media (max-width: 768px) {
    .micro-landing-page .header-bar .reports .details {
      text-align: center;
    }
  }
}
.micro-landing-page .header-bar .reports .details span {
  display: block;
  font-weight: 700;
  color: #002663;
}
.micro-landing-page .header-bar .reports .details .date {
  font-weight: 400;
  color: #5e6983;
  margin-left: 4px;
}
.micro-landing-page .header-bar .reports .number-id {
  font-size: 12px;
  font-weight: 500;
  color: #5e6983;
}
@media not print {
  @media (max-width: 768px) {
    .micro-landing-page .header-bar .reports .number-id {
      text-align: center;
    }
  }
}
.micro-landing-page .header-bar .micro-logo {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
@media not print {
  @media (max-width: 991px) {
    .micro-landing-page .header-bar .micro-logo {
      position: relative;
      top: inherit;
      left: inherit;
      width: 100%;
      margin-bottom: 20px;
      text-align: center;
      transform: translate(0, 0);
    }
  }
}
.micro-landing-page .header-bar .micro-logo img {
  width: 70px;
}
.micro-landing-page .header-bar .btn-wrap {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}
@media not print {
  @media (max-width: 768px) {
    .micro-landing-page .header-bar .btn-wrap {
      width: 100%;
    }
  }
}
.micro-landing-page .header-bar .btn-wrap .btn, .micro-landing-page .header-bar .btn-wrap #ffFormCreate #ffForm div ul li a, #ffFormCreate #ffForm div ul li .micro-landing-page .header-bar .btn-wrap a {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 150px;
  font-size: 16px;
  font-weight: 700;
  text-transform: none;
  letter-spacing: inherit;
  padding: 8px 4px 7px;
  margin-left: 11px;
}
@media not print {
  @media (max-width: 768px) {
    .micro-landing-page .header-bar .btn-wrap .btn, .micro-landing-page .header-bar .btn-wrap #ffFormCreate #ffForm div ul li a, #ffFormCreate #ffForm div ul li .micro-landing-page .header-bar .btn-wrap a {
      width: -webkit-calc( 50% - 11px );
      width: calc( 50% - 11px );
      min-width: 1px;
      margin: 0 5px;
    }
  }
}
@media not print {
  @media (max-width: 768px) {
    .micro-landing-page .header-bar .btn-wrap .btn-print {
      width: 100%;
      min-width: 1px;
      margin: 0;
    }
  }
}
.micro-landing-page .header-bar .btn-wrap .btn span, .micro-landing-page .header-bar .btn-wrap #ffFormCreate #ffForm div ul li a span, #ffFormCreate #ffForm div ul li .micro-landing-page .header-bar .btn-wrap a span {
  display: block;
  margin-left: 4px;
}
.micro-landing-page .commentary {
  background: #f7f8fa;
  padding-top: 16px;
}
.micro-landing-page .commentary .commentary-box {
  position: relative;
  padding: 20px;
  border-radius: 4px;
  border: 1px solid #E5E7EB;
  background: #FFF;
}
.micro-landing-page .commentary .show-hide-toggle {
  position: absolute;
  top: 18px;
  right: 20px;
  width: 36px;
  height: 36px;
  border-radius: 30px;
  padding: 7px;
  transition: background 0.4s ease-out;
}
@media (prefers-reduced-motion: reduce) {
  .micro-landing-page .commentary .show-hide-toggle {
    transition: none;
  }
}
.micro-landing-page .commentary .show-hide-toggle:hover {
  background: rgba(94, 105, 131, 0.1);
}
.micro-landing-page .commentary .commentary-field {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  justify-content: space-between;
}
@media not print {
  @media (max-width: 768px) {
    .micro-landing-page .commentary .commentary-field {
      flex-direction: column;
    }
  }
}
.micro-landing-page .commentary .commentary-field .summary {
  width: 74%;
  margin-right: 27px;
}
@media not print {
  @media (max-width: 768px) {
    .micro-landing-page .commentary .commentary-field .summary {
      width: 100%;
      padding-right: 0;
      margin-bottom: 20px;
    }
  }
}
.micro-landing-page .commentary .commentary-field .summary .summary-content {
  border: 1px solid #E4E7EB;
  width: 100%;
  border-radius: 2px;
  font-size: 16px;
  background-color: #FAFAFC;
  min-height: 120px;
  white-space: pre-line;
}
.micro-landing-page .commentary .commentary-field .notes {
  width: 25.92%;
}
@media not print {
  @media (max-width: 768px) {
    .micro-landing-page .commentary .commentary-field .notes {
      width: 100%;
    }
  }
}
.micro-landing-page .commentary .commentary-field label {
  display: block;
  font-size: 16px;
  color: #002663;
  font-weight: 500;
}
.micro-landing-page .commentary .commentary-field textarea {
  width: 100%;
  min-height: 120px;
  font-size: 16px;
  font-weight: 400;
  color: #5E6983;
  padding: 16px;
  border-radius: 4px;
  border: 1px solid #E5E7EB;
  background: #FAFAFC;
  resize: none;
  outline: none;
}
.micro-landing-page .commentary-print .commentary-wrap {
  border-radius: 4px;
  border: 1px solid #E5E7EB;
}
.micro-landing-page .commentary-print .commentary-wrap label {
  display: block;
  font-size: 16px;
  color: #5E6983;
  font-weight: 400;
  line-height: normal;
  background: #FAFAFC;
  padding: 10px 16px;
  margin-bottom: 0;
}
.micro-landing-page .commentary-print .commentary-wrap .details {
  padding: 16px;
}
.micro-landing-page .commentary-print .commentary-wrap .details p {
  font-size: 16px;
  font-weight: 400;
  color: #5E6983;
  white-space: pre-line;
}

.microsite-dashboard {
  background: #f7f8fa;
  padding: 24px 0;
}
.microsite-dashboard .show-hide-toggle {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 36px;
  height: 36px;
  border-radius: 30px;
  padding: 7px;
  transition: background 0.4s ease-out;
}
@media (prefers-reduced-motion: reduce) {
  .microsite-dashboard .show-hide-toggle {
    transition: none;
  }
}
.microsite-dashboard .show-hide-toggle:hover {
  background: rgba(94, 105, 131, 0.1);
}
.microsite-dashboard .microsite-dashboard-flex,
.microsite-dashboard .risk-grading-and-recomm {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  justify-content: space-between;
}
@media not print {
  @media (max-width: 1140px) {
    .microsite-dashboard .microsite-dashboard-flex,
.microsite-dashboard .risk-grading-and-recomm {
      flex-direction: column;
    }
  }
}
@media print {
  .microsite-dashboard .microsite-dashboard-flex.disable-box-survey-details .risk-grading-and-recomm {
    width: 100%;
  }
}
@media print {
  .microsite-dashboard .microsite-dashboard-flex .disable-box-recom .risk-grading {
    width: 100%;
  }
}
@media print {
  .microsite-dashboard .microsite-dashboard-flex .disable-box-risk-grading .recommendations {
    width: 100%;
  }
}
@media print {
  .microsite-dashboard .microsite-dashboard-flex.disable-box-recom.disable-box-risk-grading .survey-details {
    width: 100%;
  }
}
@media print {
  .microsite-dashboard .microsite-dashboard-flex.disable-box-recom.disable-box-risk-grading .risk-grading-and-recomm {
    display: none;
  }
}
.microsite-dashboard .survey-details {
  position: relative;
  width: 23.93%;
  padding: 28px 20px;
  border-radius: 4px;
  border: 1px solid #E5E7EB;
  background: #FFF;
}
@media not print {
  @media (max-width: 1140px) {
    .microsite-dashboard .survey-details {
      width: 100%;
      margin-bottom: 16px;
    }
  }
}
.microsite-dashboard .survey-details .listings .item {
  position: relative;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #E5E7EB;
  background: #FFF;
  cursor: pointer;
  transition: box-shadow 0.4s ease-out;
}
@media (prefers-reduced-motion: reduce) {
  .microsite-dashboard .survey-details .listings .item {
    transition: none;
  }
}
.microsite-dashboard .survey-details .listings .item:hover {
  box-shadow: 0px 0px 10px #D8DCE6;
}
@media not print {
  @media (max-width: 768px) {
    .microsite-dashboard .survey-details .listings .item {
      padding: 16px 33px 16px 16px;
    }
  }
}
.microsite-dashboard .survey-details .listings .item.active .toggle-arrow {
  transform: rotate(180deg);
}
.microsite-dashboard .survey-details .listings .item:not(:last-of-type) {
  margin-bottom: 12px;
}
.microsite-dashboard .survey-details .listings .item .toggle-arrow {
  position: absolute;
  top: 26px;
  right: 18px;
  cursor: pointer;
  transition: transform 0.4s ease-out;
}
@media (prefers-reduced-motion: reduce) {
  .microsite-dashboard .survey-details .listings .item .toggle-arrow {
    transition: none;
  }
}
.microsite-dashboard .survey-details .listings .item .subtext {
  font-size: 14px;
  font-weight: 400;
  color: #5E6983;
}
.microsite-dashboard .survey-details .listings .item p {
  font-size: 16px;
  font-weight: 500;
  color: #002663;
}
@media not print {
  @media (max-width: 1135px) {
    .microsite-dashboard .survey-details .listings .item p {
      font-size: 14px;
    }
  }
}
.microsite-dashboard .survey-details .listings .item .toggle-details {
  display: none;
}
@media print {
  .microsite-dashboard .survey-details .listings .item .toggle-details {
    display: block;
  }
}
.microsite-dashboard .survey-details .listings .item .contact-details {
  margin-top: 12px;
}
.microsite-dashboard .survey-details .listings .item .contact-details p {
  font-size: 16px;
  font-weight: 400;
  color: #5E6983;
}
.microsite-dashboard .survey-details .listings .item .contact-details a {
  display: block;
  font-size: 16px;
  font-weight: 400;
  color: #5E6983;
}
.microsite-dashboard .survey-details .listings .item .contact-details a:not(:last-of-type) {
  margin-bottom: 5px;
}
.microsite-dashboard .survey-details .listings .item .contact-details a.email {
  color: #2E6DA4;
  text-decoration: underline;
}
.microsite-dashboard .survey-details .listings .item .item-cost {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}
.microsite-dashboard .survey-details .listings .item .item-cost .name {
  min-width: 102px;
  font-size: 16px;
  font-weight: 400;
  color: #5E6983;
  border-radius: 4px;
  border: 1px solid #D8DCE6;
  background: #FAFAFC;
  padding: 4px 12px;
  text-align: center;
}
.microsite-dashboard .survey-details .listings .item .item-cost .price {
  min-width: 102px;
  font-size: 16px;
  font-weight: 400;
  color: #5E6983;
  text-align: center;
}
.microsite-dashboard .photographs-and-notes {
  position: relative;
  width: 100%;
  padding: 28px 20px;
  border-radius: 4px;
  border: 1px solid #E5E7EB;
  background: #FFF;
}
.microsite-dashboard .photographs-and-notes .photos {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  margin: 0 -7px;
}
.microsite-dashboard .photographs-and-notes .photos .item {
  position: relative;
  width: -webkit-calc(50% - 10px);
  width: calc(50% - 10px);
  padding: 0 8px;
  border-radius: 8px;
  margin-top: 15px;
}
.microsite-dashboard .photographs-and-notes .photos .item a {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 234px;
  background: #000;
}
.microsite-dashboard .photographs-and-notes .photos .item .caption-print {
  color: #5E6983;
  font-size: 16px;
  font-weight: 400;
  line-height: 20px;
  letter-spacing: 0.32px;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  border-radius: 4px;
  background: #F7F8FA;
  padding: 15px 20px;
}
.microsite-dashboard .risk-grading-and-recomm {
  width: 74.72%;
  border-radius: 4px;
  border: 1px solid #E5E7EB;
  background: #FFF;
}
@media not print {
  @media (max-width: 1140px) {
    .microsite-dashboard .risk-grading-and-recomm {
      width: 100%;
      border: 0;
      background: transparent;
    }
  }
}
.microsite-dashboard .risk-grading-and-recomm .risk-grading,
.microsite-dashboard .risk-grading-and-recomm .recommendations {
  position: relative;
  padding: 28px 20px;
}
@media not print {
  @media (max-width: 1140px) {
    .microsite-dashboard .risk-grading-and-recomm .risk-grading,
.microsite-dashboard .risk-grading-and-recomm .recommendations {
      border-radius: 4px;
      border: 1px solid #E5E7EB;
      background: #FFF;
      margin-bottom: 16px;
    }
  }
}
.microsite-dashboard .risk-grading-and-recomm .risk-grading {
  width: 40%;
}
@media not print {
  @media (max-width: 1140px) {
    .microsite-dashboard .risk-grading-and-recomm .risk-grading {
      width: 100%;
    }
  }
}
.microsite-dashboard .risk-grading-and-recomm .recommendations {
  width: 60%;
  border-left: 1px solid #E5E7EB;
}
@media not print {
  @media (max-width: 1140px) {
    .microsite-dashboard .risk-grading-and-recomm .recommendations {
      width: 100%;
      border-left: 0;
    }
  }
}
.microsite-dashboard .risk-grading .legend-risk-grading, .microsite-dashboard .recommendations .legend-risk-grading {
  align-items: center;
  flex-wrap: wrap;
  padding: 10px 16px;
  margin-bottom: 12px;
  border-radius: 4px;
  border: 1px solid #D8DCE6;
  background: #FAFAFC;
}
.microsite-dashboard .risk-grading .legend-risk-grading .item, .microsite-dashboard .recommendations .legend-risk-grading .item {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  margin-right: 12px;
}
.microsite-dashboard .risk-grading .legend-risk-grading .item p, .microsite-dashboard .recommendations .legend-risk-grading .item p {
  font-size: 12px;
  color: #5E6983;
  font-weight: 500;
}
.microsite-dashboard .risk-grading .listings, .microsite-dashboard .recommendations .listings {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  flex-direction: column;
}
.microsite-dashboard .risk-grading .listings .policy-header, .microsite-dashboard .recommendations .listings .policy-header {
  background: #FAFAFC;
  padding: 5px 10px;
  border-radius: 5px;
}
.microsite-dashboard .risk-grading .listings .item, .microsite-dashboard .recommendations .listings .item {
  position: relative;
  margin-bottom: 10px;
  border-radius: 4px;
  border: 1px solid #D8DCE6;
}
.microsite-dashboard .risk-grading .listings .item .show-hide-toggle, .microsite-dashboard .recommendations .listings .item .show-hide-toggle {
  top: 3px;
  right: 45px;
}
.microsite-dashboard .risk-grading .listings .item.has-reminder > .show-hide-toggle, .microsite-dashboard .recommendations .listings .item.has-reminder > .show-hide-toggle {
  right: 80px;
}
.microsite-dashboard .risk-grading .listings .item.has-reminder .head-toggle, .microsite-dashboard .recommendations .listings .item.has-reminder .head-toggle {
  padding-right: 114px;
}
.microsite-dashboard .risk-grading .listings .item.disable-box .show-toggle-tier1-wrap, .microsite-dashboard .recommendations .listings .item.disable-box .show-toggle-tier1-wrap {
  opacity: 1;
  visibility: visible;
}
.microsite-dashboard .risk-grading .listings .item[data-color=green] .head-tier-1-toggle .dot, .microsite-dashboard .recommendations .listings .item[data-color=green] .head-tier-1-toggle .dot {
  background-color: #49C993;
  border: 1px solid #40A87C;
}
.microsite-dashboard .risk-grading .listings .item[data-color=blue] .head-tier-1-toggle .dot, .microsite-dashboard .recommendations .listings .item[data-color=blue] .head-tier-1-toggle .dot {
  background: #337ab7;
  border: 1px solid #002663;
}
.microsite-dashboard .risk-grading .listings .item[data-color=yellow] .head-tier-1-toggle .dot, .microsite-dashboard .recommendations .listings .item[data-color=yellow] .head-tier-1-toggle .dot {
  background: #FDCA41;
  border: 1px solid #D8AC35;
}
.microsite-dashboard .risk-grading .listings .item[data-color=orange] .head-tier-1-toggle .dot, .microsite-dashboard .recommendations .listings .item[data-color=orange] .head-tier-1-toggle .dot {
  background: #FF9D43;
  border: 1px solid #D98538;
}
.microsite-dashboard .risk-grading .listings .item[data-color=red] .head-tier-1-toggle .dot, .microsite-dashboard .recommendations .listings .item[data-color=red] .head-tier-1-toggle .dot {
  background: #DC6788;
  border: 1px solid #AC4662;
}
.microsite-dashboard .risk-grading .listings .item .show-toggle-hidden-content, .microsite-dashboard .recommendations .listings .item .show-toggle-hidden-content {
  width: 206px;
  height: 27px;
  top: 8px;
}
.microsite-dashboard .risk-grading .listings .item .show-toggle-hidden-content p, .microsite-dashboard .recommendations .listings .item .show-toggle-hidden-content p {
  font-size: 12px;
  margin-left: 5px;
}
.microsite-dashboard .risk-grading .listings .item .show-toggle-hidden-content img, .microsite-dashboard .recommendations .listings .item .show-toggle-hidden-content img {
  width: 16px;
  height: auto;
}
.microsite-dashboard .risk-grading .listings .item .item-tier-2, .microsite-dashboard .recommendations .listings .item .item-tier-2 {
  padding: 16px;
}
.microsite-dashboard .risk-grading .listings .item .item-tier-2 .item:last-of-type, .microsite-dashboard .recommendations .listings .item .item-tier-2 .item:last-of-type {
  margin-bottom: 0;
}
.microsite-dashboard .risk-grading .listings .item .item-tier-2 .item.disable-box .show-toggle-tier2-wrap, .microsite-dashboard .recommendations .listings .item .item-tier-2 .item.disable-box .show-toggle-tier2-wrap {
  opacity: 1;
  visibility: visible;
}
.microsite-dashboard .risk-grading .listings .item .head-toggle, .microsite-dashboard .recommendations .listings .item .head-toggle {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  background: #FAFAFC;
  padding: 10px 78px 10px 16px;
  cursor: pointer;
  transition: box-shadow 0.4s ease-out;
}
@media (prefers-reduced-motion: reduce) {
  .microsite-dashboard .risk-grading .listings .item .head-toggle, .microsite-dashboard .recommendations .listings .item .head-toggle {
    transition: none;
  }
}
.microsite-dashboard .risk-grading .listings .item .head-toggle:hover, .microsite-dashboard .recommendations .listings .item .head-toggle:hover {
  box-shadow: 0px 0px 10px #D8DCE6;
}
.microsite-dashboard .risk-grading .listings .item .head-toggle.active .toggle-arrow, .microsite-dashboard .recommendations .listings .item .head-toggle.active .toggle-arrow {
  transform: rotate(180deg);
}
.microsite-dashboard .risk-grading .listings .item .drag, .microsite-dashboard .recommendations .listings .item .drag {
  margin-right: 10px;
  cursor: move;
  cursor: -webkit-grabbing;
}
.microsite-dashboard .risk-grading .listings .item .drag img, .microsite-dashboard .recommendations .listings .item .drag img {
  display: block;
}
.microsite-dashboard .risk-grading .listings .item .head-title, .microsite-dashboard .recommendations .listings .item .head-title {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
}
.microsite-dashboard .risk-grading .listings .item .head-title p, .microsite-dashboard .recommendations .listings .item .head-title p {
  font-size: 16px;
}
@media not print {
  @media (max-width: 1135px) {
    .microsite-dashboard .risk-grading .listings .item .head-title p, .microsite-dashboard .recommendations .listings .item .head-title p {
      font-size: 14px;
    }
  }
}
.microsite-dashboard .risk-grading .listings .item .item-tier-toggle, .microsite-dashboard .recommendations .listings .item .item-tier-toggle {
  display: none;
}
@media print {
  .microsite-dashboard .risk-grading .listings .item .item-tier-toggle, .microsite-dashboard .recommendations .listings .item .item-tier-toggle {
    display: block;
  }
}
.microsite-dashboard .risk-grading .listings .item .toggle-content, .microsite-dashboard .recommendations .listings .item .toggle-content {
  display: none;
  background: #fff;
  padding: 16px;
}
@media print {
  .microsite-dashboard .risk-grading .listings .item .toggle-content, .microsite-dashboard .recommendations .listings .item .toggle-content {
    display: block;
  }
}
.microsite-dashboard .risk-grading .listings .item .toggle-content p, .microsite-dashboard .recommendations .listings .item .toggle-content p {
  font-size: 16px;
  margin-bottom: 10px;
}
.microsite-dashboard .risk-grading .listings .item .toggle-content .readmore, .microsite-dashboard .recommendations .listings .item .toggle-content .readmore {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  color: #337AB7;
  line-height: 20px;
  letter-spacing: 0.32px;
  -webkit-text-decoration-line: underline;
          text-decoration-line: underline;
}
.microsite-dashboard .risk-grading .listings .item .toggle-arrow, .microsite-dashboard .recommendations .listings .item .toggle-arrow {
  position: absolute;
  top: 13px;
  right: 22px;
  transition: transform 0.5s ease-out;
}
@media (prefers-reduced-motion: reduce) {
  .microsite-dashboard .risk-grading .listings .item .toggle-arrow, .microsite-dashboard .recommendations .listings .item .toggle-arrow {
    transition: none;
  }
}
.microsite-dashboard .risk-grading .listings .item .reminder, .microsite-dashboard .recommendations .listings .item .reminder {
  position: absolute;
  top: 10px;
  right: 51px;
}
.microsite-dashboard .recommendations .recomm-list .item-wrap {
  padding: 16px;
  margin-bottom: 12px;
  border-radius: 4px;
  border: 1px solid #D8DCE6;
  background: #FAFAFC;
}
.microsite-dashboard .recommendations .recomm-toggle {
  display: none;
}
@media print {
  .microsite-dashboard .recommendations .recomm-toggle {
    display: block;
  }
}
.microsite-dashboard .recommendations .recomm-assestment .item {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  margin: 0 -7px;
}
@media not print {
  @media (max-width: 480px) {
    .microsite-dashboard .recommendations .recomm-assestment .item {
      flex-wrap: wrap;
      font-size: 14px;
      margin: 0 -2px;
    }
  }
}
.microsite-dashboard .recommendations .recomm-assestment .item:not(:last-of-type) {
  margin-bottom: 12px;
}
.microsite-dashboard .recommendations .recomm-assestment .item .bx {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 5px;
  margin: 0 7px;
  border-radius: 4px;
  border: 1px solid #D8DCE6;
  background: #FAFAFC;
  text-align: center;
}
@media not print {
  @media (max-width: 480px) {
    .microsite-dashboard .recommendations .recomm-assestment .item .bx {
      margin: 0 2px;
    }
  }
}
.microsite-dashboard .recommendations .recomm-assestment .item .action {
  margin-left: 7px;
}
.microsite-dashboard .recommendations .recomm-assestment .item .action .btn, .microsite-dashboard .recommendations .recomm-assestment .item .action #ffFormCreate #ffForm div ul li a, #ffFormCreate #ffForm div ul li .microsite-dashboard .recommendations .recomm-assestment .item .action a {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 85px;
  min-width: 85px;
  font-size: 16px;
  font-weight: 700;
  text-transform: none;
  letter-spacing: inherit;
  padding: 11px 10px;
  border-radius: 54px;
}
.microsite-dashboard .recommendations .recomm-assestment .item .action .btn-primary, .microsite-dashboard .recommendations .recomm-assestment .item .action #ffgenerated button.btn-default, #ffgenerated .microsite-dashboard .recommendations .recomm-assestment .item .action button.btn-default {
  border: 1px solid #002663;
  background: #002663;
}
.microsite-dashboard .recommendations .recomm-assestment .item .category {
  width: 216px;
  min-width: 119px;
}
@media not print {
  @media (max-width: 480px) {
    .microsite-dashboard .recommendations .recomm-assestment .item .category {
      width: 100%;
      min-width: 1px;
      margin-bottom: 12px;
    }
  }
}
.microsite-dashboard .recommendations .recomm-assestment .item .tag {
  position: relative;
  min-width: 51px;
  width: 15%;
}
.microsite-dashboard .recommendations .recomm-assestment .item .tag span {
  display: block;
  margin-right: 7px;
}
.microsite-dashboard .recommendations .recomm-assestment .item .tag .definitions {
  position: absolute;
  bottom: 45px;
  left: 50%;
  width: 328px;
  border-radius: 8px;
  background: #272727;
  padding: 12px 10px;
  font-size: 14px;
  font-weight: 400;
  color: #FFF;
  letter-spacing: 0.28px;
  text-align: center;
  z-index: 2;
  opacity: 0;
  visibility: hidden;
  transform: translateX(-50%);
  transition: opacity 0.4s ease-out, visibility 0.4s ease-out;
}
@media (prefers-reduced-motion: reduce) {
  .microsite-dashboard .recommendations .recomm-assestment .item .tag .definitions {
    transition: none;
  }
}
.microsite-dashboard .recommendations .recomm-assestment .item .tag .info {
  display: block;
  cursor: pointer;
}
.microsite-dashboard .recommendations .recomm-assestment .item .tag .info:hover + .definitions {
  opacity: 1;
  visibility: visible;
}
@media not print {
  @media (max-width: 1140px) {
    .microsite-dashboard .recommendations .recomm-assestment .item .tag {
      width: 15%;
    }
  }
  @media (max-width: 480px) {
    .microsite-dashboard .recommendations .recomm-assestment .item .tag {
      min-width: 1px;
    }
  }
}
.microsite-dashboard .recommendations .recomm-assestment .item .code,
.microsite-dashboard .recommendations .recomm-assestment .item .date {
  min-width: 86px;
  width: 20%;
  transition: background 0.3s ease-out, border 0.3s ease-out, color 0.3s ease-out;
}
@media (prefers-reduced-motion: reduce) {
  .microsite-dashboard .recommendations .recomm-assestment .item .code,
.microsite-dashboard .recommendations .recomm-assestment .item .date {
    transition: none;
  }
}
@media not print {
  @media (max-width: 1140px) {
    .microsite-dashboard .recommendations .recomm-assestment .item .code,
.microsite-dashboard .recommendations .recomm-assestment .item .date {
      width: 24%;
    }
  }
  @media (max-width: 480px) {
    .microsite-dashboard .recommendations .recomm-assestment .item .code,
.microsite-dashboard .recommendations .recomm-assestment .item .date {
      width: 57%;
      min-width: 1px;
    }
  }
}
.microsite-dashboard .recommendations .recomm-assestment .item .color {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 51px;
}
@media not print {
  @media (max-width: 480px) {
    .microsite-dashboard .recommendations .recomm-assestment .item .color {
      width: 18%;
      min-width: 1px;
    }
  }
}
.microsite-dashboard .recommendations .recomm-assestment .item .toggle-assesment {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  min-width: 50px;
  cursor: pointer;
}
.microsite-dashboard .recommendations .recomm-assestment .item .toggle-assesment .toggle-arrow {
  transition: transform 0.5s ease-out;
}
@media (prefers-reduced-motion: reduce) {
  .microsite-dashboard .recommendations .recomm-assestment .item .toggle-assesment .toggle-arrow {
    transition: none;
  }
}
.microsite-dashboard .recommendations .recomm-assestment .item .toggle-assesment .toggle-arrow.active {
  transform: rotate(180deg);
}
.microsite-dashboard .recommendations .recomm-assestment .item.hover-green .date {
  color: #40A87C;
  background: rgba(74, 201, 148, 0.2);
  border: 1px solid #40A87C;
}
.microsite-dashboard .recommendations .recomm-assestment .item.hover-yellow .date {
  color: #806e3f;
  background: #f4f47a;
  border: 1px solid #caca21;
}
.microsite-dashboard .recommendations .recomm-assestment .item.hover-orange .date {
  color: #D98538;
  background: rgba(255, 158, 66, 0.2);
  border: 1px solid #D98538;
}
.microsite-dashboard .recommendations .recomm-assestment .item.hover-red .date {
  color: #AC4662;
  background: rgba(219, 102, 135, 0.2);
  border: 1px solid #AC4662;
}
.microsite-dashboard .recommendations .recomm-assestment .item.hover-blue .date {
  color: #002663;
  background: rgba(52, 122, 183, 0.2);
  border: 1px solid #002663;
}
.microsite-dashboard .recommendations .discussion {
  overflow-wrap: break-word;
  word-wrap: break-word;
  padding: 16px 16px 24px 16px;
  border-radius: 8px;
  border: 1px solid #E5E7EB;
  background: #FFF;
  margin-top: 16px;
  font-size: 16px;
}
.microsite-dashboard .recommendations .discussion h5 {
  font-size: 16px;
  font-weight: 700;
  color: #002663;
  margin-bottom: 3px;
}
.microsite-dashboard .recommendations .discussion p {
  font-size: 16px;
}
.microsite-dashboard .recommendations .btn-wrap {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  justify-content: space-between;
  margin: 16px 0;
}
@media not print {
  @media (max-width: 768px) {
    .microsite-dashboard .recommendations .btn-wrap {
      flex-direction: column;
    }
  }
}
.microsite-dashboard .recommendations .btn-wrap .btn, .microsite-dashboard .recommendations .btn-wrap #ffFormCreate #ffForm div ul li a, #ffFormCreate #ffForm div ul li .microsite-dashboard .recommendations .btn-wrap a {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  justify-content: center;
  align-items: center;
  width: -webkit-calc( 50% - 8px );
  width: calc( 50% - 8px );
  font-size: 16px;
  font-weight: 700;
  text-transform: none;
  letter-spacing: inherit;
  padding: 14px 10px;
}
@media not print {
  @media (max-width: 768px) {
    .microsite-dashboard .recommendations .btn-wrap .btn, .microsite-dashboard .recommendations .btn-wrap #ffFormCreate #ffForm div ul li a, #ffFormCreate #ffForm div ul li .microsite-dashboard .recommendations .btn-wrap a {
      width: 100%;
      margin-bottom: 16px;
    }
  }
}
.microsite-dashboard .recommendations .btn-wrap .btn span, .microsite-dashboard .recommendations .btn-wrap #ffFormCreate #ffForm div ul li a span, #ffFormCreate #ffForm div ul li .microsite-dashboard .recommendations .btn-wrap a span {
  display: block;
  margin-left: 4px;
}
.microsite-dashboard .recommendations .photographs {
  border-radius: 8px;
  border: 1px solid #E5E7EB;
  background: #FFF;
  padding: 16px 16px 24px 16px;
}
.microsite-dashboard .recommendations .photographs h4 {
  font-size: 18px;
  font-weight: 700;
  color: #002663;
  text-align: left;
  margin-bottom: 0;
}
@media not print {
  @media (max-width: 1135px) {
    .microsite-dashboard .recommendations .photographs h4 {
      font-size: 16px;
    }
  }
}
.microsite-dashboard .recommendations .photographs .photos {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  margin: 0 -7px;
}
.microsite-dashboard .recommendations .photographs .photos .caption-print {
  display: none;
  color: #5E6983;
  font-size: 16px;
  font-weight: 400;
  line-height: 20px;
  letter-spacing: 0.32px;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
@media print {
  .microsite-dashboard .recommendations .photographs .photos .caption-print {
    display: block;
  }
}
.microsite-dashboard .recommendations .photographs .photos .item {
  position: relative;
  width: 20%;
  padding: 0 8px;
  border-radius: 8px;
  margin-top: 15px;
}
@media print {
  .microsite-dashboard .recommendations .photographs .photos .item {
    width: 100%;
  }
}
@media not print {
  @media (max-width: 768px) {
    .microsite-dashboard .recommendations .photographs .photos .item {
      width: 33.33%;
    }
  }
  @media (max-width: 480px) {
    .microsite-dashboard .recommendations .photographs .photos .item {
      width: 50%;
    }
  }
}
.microsite-dashboard .recommendations .photographs .photos .item a {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  justify-content: center;
  align-items: center;
}
.microsite-dashboard .recommendations .photographs .photos .item a:hover:before {
  opacity: 0.75;
  visibility: visible;
}
.microsite-dashboard .recommendations .photographs .photos .item a:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #2E6DA4;
  border-radius: 8px;
  opacity: 0.75;
  z-index: 1;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.4s ease-out, visibility 0.4s ease-out;
}
@media (prefers-reduced-motion: reduce) {
  .microsite-dashboard .recommendations .photographs .photos .item a:before {
    transition: none;
  }
}
.microsite-dashboard .recommendations .photographs .photos .item a .view {
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: 2;
  opacity: 0;
  visibility: hidden;
  transform: translate(-50%, -50%);
  transition: opacity 0.4s ease-out, visibility 0.4s ease-out;
}
@media (prefers-reduced-motion: reduce) {
  .microsite-dashboard .recommendations .photographs .photos .item a .view {
    transition: none;
  }
}
.microsite-dashboard .recommendations .photographs .photos .item img {
  display: block;
  width: auto;
  -o-object-fit: contain;
     object-fit: contain;
  border-radius: 8px;
}
@media print {
  .microsite-dashboard .recommendations .photographs .photos .item img {
    width: 100%;
    height: auto;
  }
}
.microsite-dashboard .recommendations .photographs .photos .item .pdf-download {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 95px;
  height: 95px;
  border-radius: 8px;
  border: 1px solid #D8DCE6;
  background: #FFF;
  text-decoration: none;
}
@media print {
  .microsite-dashboard .recommendations .photographs .photos .item .pdf-download {
    width: 100%;
    height: 113px;
  }
}
.microsite-dashboard .recommendations .photographs .photos .item .pdf-download img {
  width: 24px;
  height: 24px;
}
.microsite-dashboard .recommendations .photographs .photos .item .pdf-download span {
  display: block;
  color: #002663;
  text-align: center;
  font-size: 13px;
  font-style: normal;
  font-weight: 700;
  line-height: 20px;
  letter-spacing: 0.26px;
}

.modal-rish-grading .modal-dialog {
  width: 430px;
}
.modal-rish-grading .modal-content {
  padding: 24px;
}
.modal-rish-grading .close {
  position: absolute;
  top: 29px;
  right: 22px;
}
.modal-rish-grading .dot {
  display: block;
  width: 12px;
  height: 12px;
  min-width: 12px;
  min-height: 12px;
  border-radius: 100%;
  margin-right: 12px;
}
.modal-rish-grading .dot-green {
  background-color: #49C993;
  border: 1px solid #40A87C;
}
.modal-rish-grading .dot-blue {
  background: #337ab7;
  border: 1px solid #002663;
}
.modal-rish-grading .dot-yellow {
  background: #FDCA41;
  border: 1px solid #D8AC35;
}
.modal-rish-grading .dot-orange {
  background: #FF9D43;
  border: 1px solid #D98538;
}
.modal-rish-grading .dot-red {
  background: #DC6788;
  border: 1px solid #AC4662;
}
.modal-rish-grading .heading {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}
.modal-rish-grading .heading p {
  font-size: 24px;
  font-style: normal;
  color: #002663;
  font-weight: 700;
  line-height: normal;
}
.modal-rish-grading .content {
  border-radius: 4px;
  border: 1px solid #E5E7EB;
  background: #FAFAFC;
  margin-bottom: 24px;
}
.modal-rish-grading .content textarea {
  width: 100%;
  height: 312px;
  color: #5E6983;
  font-size: 16px;
  font-weight: 400;
  padding: 16px;
  line-height: 20px;
  letter-spacing: 0.32px;
  border: 0px;
  background: none;
  resize: none;
  outline: none;
}
.modal-rish-grading .btn, .modal-rish-grading #ffFormCreate #ffForm div ul li a, #ffFormCreate #ffForm div ul li .modal-rish-grading a {
  height: 60px;
  color: #FFF;
  text-align: center;
  font-size: 16px;
  font-weight: 700;
  line-height: 20px;
  letter-spacing: 0.32px;
  padding: 19px 16px;
  border-radius: 4px;
  background: #2E6DA4;
}

.sendtoclient-modal .modal-dialog {
  max-width: 430px;
}
.sendtoclient-modal .modal-content {
  position: relative;
  padding: 24px;
}
.sendtoclient-modal .modal-content .close {
  position: absolute;
  top: 10px;
  right: 16px;
  margin-bottom: 36px;
}
.sendtoclient-modal .modal-content .btn, .sendtoclient-modal .modal-content #ffFormCreate #ffForm div ul li a, #ffFormCreate #ffForm div ul li .sendtoclient-modal .modal-content a {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 60px;
  font-size: 16px;
  font-weight: 700;
  text-transform: none;
  letter-spacing: inherit;
  padding: 8px 5px 7px;
  margin-top: 24px;
}
.sendtoclient-modal .modal-content .btn span, .sendtoclient-modal .modal-content #ffFormCreate #ffForm div ul li a span, #ffFormCreate #ffForm div ul li .sendtoclient-modal .modal-content a span {
  display: block;
  margin-left: 4px;
}
.sendtoclient-modal .modal-content .modal-title {
  font-size: 24px;
  font-weight: 700;
  color: #002663;
  text-align: center;
}
.sendtoclient-modal .modal-content .email-field label {
  font-size: 16px;
  color: #002663;
  font-weight: 500;
}
.sendtoclient-modal .modal-content .email-field .multiemail {
  width: 100%;
  height: 176px;
  padding: 16px 8px;
  border-radius: 4px;
  border: 1px solid #E5E7EB;
  background: #FAFAFC;
}
.sendtoclient-modal .modal-content .email-field .multiemail input {
  width: 100%;
  font-size: 16px;
  font-weight: 400;
  background: transparent;
  padding: 0 4px;
  border: none;
  outline: none;
}
.sendtoclient-modal .modal-content .email-field .multiemail input ::-moz-placeholder {
  color: rgba(94, 105, 131, 0.3);
  opacity: 0.3;
}
.sendtoclient-modal .modal-content .email-field .multiemail input ::placeholder {
  color: rgba(94, 105, 131, 0.3);
  opacity: 0.3;
}
.sendtoclient-modal .modal-content .email-field #multiemail {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
}
.sendtoclient-modal .modal-content .email-field #multiemail .email-text {
  font-size: 16px;
  font-weight: 400;
  color: #5E6983;
  padding: 4px 12px;
  border-radius: 8px;
  border: 1px solid #E5E7EB;
  background: #FFF;
  margin: 0 4px 8px;
}

.fancybox-overlay .fancybox-close {
  display: none;
}
.fancybox-overlay .fancybox-wrap {
  width: 698px !important;
}
.fancybox-overlay .fancybox-wrap .fancybox-skin {
  padding-top: 85px !important;
}
.fancybox-overlay .fancybox-wrap .fancybox-inner {
  width: 100% !important;
  height: auto !important;
}
.fancybox-overlay .fancybox-wrap .fancybox-inner img {
  width: 100%;
  height: auto;
}
.fancybox-overlay .fancybox-wrap .fancybox-prev span {
  left: -70px;
  visibility: visible;
  background: url("../img/icon-next.svg") no-repeat;
  transform: rotate(-180deg);
}
.fancybox-overlay .fancybox-wrap .fancybox-next span {
  right: -70px;
  visibility: visible;
  background: url("../img/icon-next.svg") no-repeat;
}
.fancybox-overlay .fancybox-wrap .desc {
  color: #5E6983;
  font-size: 16px;
  font-weight: 400;
  margin-top: 24px;
  letter-spacing: 0.32px;
  text-align: left;
  text-shadow: none;
}
.fancybox-overlay .fancybox-wrap .fancybox-title {
  position: absolute;
  right: 0;
  bottom: inherit;
  top: 28px;
  width: 100%;
  max-width: 730px;
  margin-bottom: 0;
}
.fancybox-overlay .fancybox-wrap .fancybox-title .child {
  margin-right: 0;
  max-width: 730px;
  background: transparent;
  padding: 0;
  white-space: inherit;
  color: #002663;
  text-align: center;
  font-size: 24px;
  font-weight: 700;
  text-shadow: none;
}

#page > footer > div.close-thread.bootbox.modal.fade.bootbox-prompt div.modal-header button {
  top: 0 !important;
  right: 0 !important;
}

.modal-close {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  width: 3.375rem;
  height: 3.375rem;
  border-radius: 50%;
  position: fixed;
  top: 2.8125rem;
  right: 2.8125rem;
  font-size: 18px;
  pointer-events: all;
  cursor: pointer;
}
.modal-close::after {
  font-family: "lsm-icons";
  content: "\EA0B";
}
.modal-title {
  margin-bottom: 2rem;
}
.modal-title h1,
.modal-title h2,
.modal-title h3,
.modal-title h4,
.modal-title h5 {
  font-size: 25px;
}
.modal-footer {
  padding: 0;
}
.modal-footer .btn, .modal-footer #ffFormCreate #ffForm div ul li a, #ffFormCreate #ffForm div ul li .modal-footer a {
  border-radius: 0;
}

.selectize-control .selectize-input {
  border: 1px solid #d8dce6;
  background: #f7f7f9;
  border-radius: 0.125rem;
  box-shadow: none;
  padding: 0.6875rem 2rem 0.6875rem 0.9375rem;
  height: 2.8125rem;
  color: #5e6983;
  position: relative;
}
.selectize-control .selectize-input:after {
  font-family: "lsm-icons";
  content: "\E9C8";
  height: 100%;
  border-left: 1px solid #d8dce6;
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  padding: 0 0.9375rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
}
.selectize-control .selectize-input .item {
  display: none;
}
.selectize-control [type=select-multiple] {
  top: 7px;
}
.selectize-control .selectize-dropdown {
  border: 1px solid #d8dce6;
  box-shadow: 0 4px 10px -5px #d8dce6;
  color: #5e6983;
}
.selectize-control .selectize-dropdown-scroll {
  overflow-x: hidden;
  max-height: 200px;
  overflow-y: auto;
}
.selectize-control .selectize-dropdown-scroll .simplebar-scrollbar:before {
  background: #5e6983;
}
.selectize-control .selectize-dropdown-scroll .simplebar-scrollbar.simplebar-visible:before {
  opacity: 1;
}
.selectize-control .selectize-dropdown-content {
  overflow: visible;
  -webkit-overflow-scrolling: unset;
  max-height: none;
}
.selectize-control .selectize-dropdown-content .option {
  padding: 1.1875rem 1.1rem 1.1875rem 1.0625rem;
  position: relative;
  cursor: pointer;
}
.selectize-control .selectize-dropdown-content .option.active {
  color: #002663;
  background: #f7f7f9;
}
.selectize-control .selectize-dropdown-content .option.selected:after {
  font-family: "lsm-icons";
  content: "\E92B";
  color: #337ab7;
  position: absolute;
  top: 50%;
  right: 1rem;
  transform: translateY(-50%);
  font-size: 18px;
}
.selectize-control.is-invalid .selectize-input {
  border-color: #cc6565 !important;
}

.multiselect-choices {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  flex-wrap: wrap;
  margin: 1rem 0;
}
.multiselect-choices .item {
  padding: 0.3rem 1rem;
  border: 1px solid #d8dce6;
  border-radius: 0.125rem;
  margin-right: 1rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-width: 9.375rem;
  margin-bottom: 1rem;
}
.multiselect-choices .item p {
  flex-grow: 1;
  padding-right: 0.625rem;
}

.nav-pills {
  border: 1px solid #337ab7;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  border-radius: 0.25rem;
}
.nav-pills .nav-item {
  min-width: 9.0625rem;
}
.nav-pills .nav-item .nav-link {
  font-weight: bold;
  line-height: 1.0625rem;
  text-align: center;
  cursor: pointer;
}
.nav-pills .nav-item .nav-link:not(.active) {
  color: #337ab7;
}
.nav-pills .nav-item .nav-link.active:hover {
  color: #fff;
}

nav.main-nav {
  font-size: 0.75rem;
}
nav.main-nav li a {
  color: #002663;
  font-weight: bold;
  padding: 1.0625rem 0;
  display: block;
  cursor: pointer;
  position: relative;
  z-index: 10;
}
nav.main-nav li a .icon, nav.main-nav li a #ffFormCreate .fa, #ffFormCreate nav.main-nav li a .fa {
  margin-right: 0.625rem;
  color: #337ab7;
}
nav.main-nav li a:hover {
  text-decoration: none;
  color: #337ab7;
}
nav.main-nav li.parent {
  position: relative;
}
nav.main-nav li.parent::after {
  font-family: "lsm-icons";
  content: "\E9B8";
  position: absolute;
  right: 0;
  top: 1.0625rem;
  font-size: 10px;
  color: #337ab7;
}
nav.main-nav li.parent.active::after {
  font-family: "lsm-icons";
  content: "\E99B";
}
nav.main-nav li.parent ul {
  display: none;
  list-style-type: none;
  padding: 0;
  margin: 0 0 0 1.375rem;
}
nav.main-nav li.parent ul li {
  border-bottom: 0;
}
nav.main-nav li.parent ul li:first-child a {
  padding-top: 0;
}
nav.main-nav li.parent ul li:last-child a {
  padding-bottom: 1.0625rem;
}
nav.main-nav li.parent ul li a {
  font-weight: normal;
  padding: 0.625rem 0;
}
nav.main-nav li:not(:last-child) {
  border-bottom: 1px solid #d8dce6;
}
@media (max-width: 1199.98px) {
  nav.main-nav {
    position: fixed;
    background: #fff;
    z-index: 1000;
    width: 80%;
    height: 100%;
    top: 0;
    left: -100%;
    transition: left 0.3s ease-in-out;
  }
  nav.main-nav > ul {
    position: absolute;
    top: 0;
    left: 20px;
    width: -webkit-calc(100% - 40px);
    width: calc(100% - 40px);
    height: 100%;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    z-index: 1001;
  }
  nav.main-nav.expanded {
    left: 0;
  }
}

.burger-menu-nav {
  display: none;
}

.nav-menu-wrapper nav.main-nav {
  position: fixed;
  background: #fff;
  z-index: 1000;
  width: 80%;
  height: 100%;
  top: 0;
  left: -100%;
  transition: left 0.3s ease-in-out;
}
.nav-menu-wrapper nav.main-nav > ul {
  position: absolute;
  top: 0;
  left: 20px;
  width: -webkit-calc(100% - 40px);
  width: calc(100% - 40px);
  height: 100%;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  z-index: 1001;
}
.nav-menu-wrapper nav.main-nav.expanded {
  left: 0;
}
.nav-menu-wrapper nav.main-nav.expanded .burger-menu-nav {
  display: block;
}

.page-item:nth-of-type(1) .page-link, .page-item:nth-of-type(2) .page-link, .page-item:nth-last-child(2) .page-link, .page-item:nth-last-child(1) .page-link {
  color: #2e6da4;
}

.page-link {
  border-radius: 50% !important;
  font-weight: bold;
  width: 3.5rem;
  height: 3.5rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #5e6983;
  transition: color 0.3s ease-in-out;
}

.placeholder-block {
  background-color: #fafafc;
  border-color: #e7e9f0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 380px;
  height: 380px;
  text-align: center;
  font-weight: bold;
  position: relative;
  flex-direction: column;
}
.placeholder-block::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: -o-linear-gradient(left, #d8dce6 100%, transparent 100%) top left no-repeat, -o-linear-gradient(left, transparent 4px, #d8dce6 4px, #d8dce6 12px, transparent 12px) top center repeat-x, -o-linear-gradient(left, #d8dce6 100%, transparent 100%) top right no-repeat, -o-linear-gradient(bottom, #d8dce6 100%, transparent 100%) top left no-repeat, -o-linear-gradient(bottom, transparent 4px, #d8dce6 4px, #d8dce6 12px, transparent 12px) center left repeat-y, -o-linear-gradient(bottom, #d8dce6 100%, transparent 100%) bottom left no-repeat, -o-linear-gradient(left, #fff 100%, transparent 100%) bottom left no-repeat, -o-linear-gradient(left, transparent 4px, #d8dce6 4px, #d8dce6 12px, transparent 12px) bottom center repeat-x, -o-linear-gradient(left, #d8dce6 100%, transparent 100%) bottom right no-repeat, -o-linear-gradient(bottom, #d8dce6 100%, transparent 100%) top right no-repeat, -o-linear-gradient(bottom, transparent 4px, #d8dce6 4px, #d8dce6 12px, transparent 12px) center right repeat-y, -o-linear-gradient(bottom, #d8dce6 100%, transparent 100%) bottom right no-repeat;
  background: linear-gradient(90deg, #d8dce6 100%, transparent 100%) top left no-repeat, linear-gradient(90deg, transparent 4px, #d8dce6 4px, #d8dce6 12px, transparent 12px) top center repeat-x, linear-gradient(90deg, #d8dce6 100%, transparent 100%) top right no-repeat, linear-gradient(0deg, #d8dce6 100%, transparent 100%) top left no-repeat, linear-gradient(0deg, transparent 4px, #d8dce6 4px, #d8dce6 12px, transparent 12px) center left repeat-y, linear-gradient(0deg, #d8dce6 100%, transparent 100%) bottom left no-repeat, linear-gradient(90deg, #fff 100%, transparent 100%) bottom left no-repeat, linear-gradient(90deg, transparent 4px, #d8dce6 4px, #d8dce6 12px, transparent 12px) bottom center repeat-x, linear-gradient(90deg, #d8dce6 100%, transparent 100%) bottom right no-repeat, linear-gradient(0deg, #d8dce6 100%, transparent 100%) top right no-repeat, linear-gradient(0deg, transparent 4px, #d8dce6 4px, #d8dce6 12px, transparent 12px) center right repeat-y, linear-gradient(0deg, #d8dce6 100%, transparent 100%) bottom right no-repeat;
  background-size: 8px 1px, 16px 1px, 8px 1px, 1px 8px, 1px 16px, 1px 8px;
}
.placeholder-block p {
  opacity: 0.6;
}

.repeater {
  border: 1px solid #d8dce6;
}
.repeater ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
  counter-reset: repeater;
}
.repeater ul li {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  border-bottom: 1px solid #d8dce6;
}
.repeater ul li .repeater-left > span {
  text-align: center;
  display: block;
  margin-bottom: 1rem;
}
.repeater ul li .repeater-left > span::before {
  counter-increment: repeater;
  content: counter(repeater);
  font-weight: bold;
}
.repeater ul li:last-child {
  display: none;
}
.repeater hr {
  margin: 1.5rem 0;
}
.repeater-left {
  background: #f4f8fc;
  padding: 1.5rem 0.5rem;
}
.repeater-right {
  padding: 1.5rem;
}
@media (min-width: 600px) {
  .repeater-right {
    flex-grow: 1;
  }
}
@media (max-width: 599.98px) {
  .repeater-right {
    max-width: 85%;
  }
}
.repeater-asset {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}
.repeater-asset-file {
  flex-grow: 1;
  padding: 0 1.5rem;
}
.repeater-asset-photo .photo {
  position: relative;
  border-radius: 0.125rem;
  width: 70px;
  overflow: hidden;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: top center;
}
.repeater-asset-photo .photo:before {
  display: block;
  content: " ";
  width: 100%;
  padding-top: 100%;
}
.repeater-asset-photo .photo > .inner {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.selectric {
  border-color: #d8dce6;
  background-color: #fff;
}
.selectric.is-invalid {
  border-color: #cc6565;
}
.selectric .label {
  margin-left: 0.9375rem;
}
.selectric .button {
  background-color: #fff;
  border-left: 1px solid #d8dce6;
  width: 48px;
  height: 45px;
  line-height: 45px;
}
.selectric .button::after {
  font-family: "lsm-icons";
  content: "\E92E";
  font-size: 12px;
  border: 0;
  right: 0.9375rem;
}

.selectric-open .selectric {
  border-color: #d8dce6;
}
.selectric-open .selectric-items {
  z-index: 10;
}
.selectric-open .selectric-items::before {
  content: "";
  background-color: #fff;
  width: 39px;
  height: 1px;
  display: block;
  position: absolute;
  top: -1px;
  right: 0;
}

.selectric-items {
  border-color: #d8dce6;
  background-color: #fff;
  box-shadow: 0 5px 5px rgba(0, 0, 0, 0.03);
  top: -webkit-calc(100% - 1px);
  top: calc(100% - 1px);
}
.selectric-items ul li {
  color: #5e6983;
  padding: 1.125rem;
}
.selectric-items ul li:hover {
  background-color: #fafafc;
}
.selectric-items ul li.highlighted {
  background-color: #fafafc;
  color: #002663;
}

.table thead th {
  font-size: 1rem;
}
.table th,
.table td {
  vertical-align: middle;
  padding: 0.75rem 1.5rem;
}
.table a:not(.btn):not(.dropdown-item) {
  color: #002663;
  text-decoration: underline;
  font-weight: normal;
}
.table .dropdown-toggle[aria-expanded=false] {
  background: transparent;
}

.tooltip.bs-tooltip-top, .tooltip.bs-tooltip-auto[x-placement^=top] {
  margin-bottom: 0.625rem;
}
.tooltip.bs-tooltip-top .arrow::before, .tooltip.bs-tooltip-auto[x-placement^=top] .arrow::before {
  border-top-color: #061436;
}
.tooltip.bs-tooltip-bottom, .tooltip.bs-tooltip-auto[x-placement^=bottom] {
  margin-top: 0.625rem;
}
.tooltip.bs-tooltip-bottom .arrow::before, .tooltip.bs-tooltip-auto[x-placement^=bottom] .arrow::before {
  border-bottom-color: #061436;
}
.tooltip.bs-tooltip-right, .tooltip.bs-tooltip-auto[x-placement^=right] {
  margin-left: 0.625rem;
}
.tooltip.bs-tooltip-right .arrow::before, .tooltip.bs-tooltip-auto[x-placement^=right] .arrow::before {
  border-right-color: #061436;
}
.tooltip.bs-tooltip-left, .tooltip.bs-tooltip-auto[x-placement^=left] {
  margin-right: 0.625rem;
}
.tooltip.bs-tooltip-left .arrow::before, .tooltip.bs-tooltip-auto[x-placement^=left] .arrow::before {
  border-left-color: #061436;
}
.tooltip .tooltip-inner {
  background-color: #061436;
  padding: 0.45rem 1rem;
  border-radius: 0.25rem;
}

.top-bar-title {
  font-size: 1.3125rem;
  line-height: 1;
  font-weight: 700;
}
.top-bar-title a {
  color: inherit;
}
.top-bar-title a:hover {
  text-decoration: none;
}
.top-bar .btn-link {
  text-transform: none;
  letter-spacing: 0;
}
.top-bar .logo {
  background-image: url("/img/lsm-logo.svg");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: top left;
  width: 154px;
  height: 60px;
}

.nav-trigger {
  font-size: 1.625rem;
  cursor: pointer;
}

h1[class*=display-],
h2[class*=display-],
h3[class*=display-],
h4[class*=display-],
h5[class*=display-],
h6[class*=display-] {
  color: rgba(94, 105, 131, 0.6);
}
h1 .icon, h1 #ffFormCreate .fa, #ffFormCreate h1 .fa,
h2 .icon,
h2 #ffFormCreate .fa,
#ffFormCreate h2 .fa,
h3 .icon,
h3 #ffFormCreate .fa,
#ffFormCreate h3 .fa,
h4 .icon,
h4 #ffFormCreate .fa,
#ffFormCreate h4 .fa,
h5 .icon,
h5 #ffFormCreate .fa,
#ffFormCreate h5 .fa,
h6 .icon,
h6 #ffFormCreate .fa,
#ffFormCreate h6 .fa {
  color: #2e6da4;
}

h1 .icon, h1 #ffFormCreate .fa, #ffFormCreate h1 .fa,
.display-1 .icon,
.display-1 #ffFormCreate .fa,
#ffFormCreate .display-1 .fa {
  margin-right: 20px;
}

h2 .icon, h2 #ffFormCreate .fa, #ffFormCreate h2 .fa,
.display-2 .icon,
.display-2 #ffFormCreate .fa,
#ffFormCreate .display-2 .fa {
  margin-right: 16px;
}

h3 .icon, h3 #ffFormCreate .fa, #ffFormCreate h3 .fa,
.display-3 .icon,
.display-3 #ffFormCreate .fa,
#ffFormCreate .display-3 .fa {
  margin-right: 12px;
}

a.workspace-banner {
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
a.workspace-banner:hover {
  text-decoration: none;
  border-color: #337ab7;
  box-shadow: 0 5px 5px rgba(0, 0, 0, 0.03);
  color: #5e6983;
}

.workspace-banner {
  border: 1px solid #d8dce6;
  border-radius: 0.125rem;
  padding: 1.875rem 3.125rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #5e6983;
}
@media (min-width: 980px) {
  .workspace-banner {
    flex-direction: row;
  }
}

.workspace-banner-img {
  max-width: 216px;
  margin-right: 3.125rem;
  border-radius: 50%;
}

.workspace-banner-body {
  min-height: 94px;
}

.workspace-banner-title {
  margin-bottom: 0.3125rem;
}

.workspace-banner-date {
  font-weight: bold;
  margin-bottom: 0.9375rem;
}

.workspace-banner-version {
  font-size: 0.625rem;
  font-weight: bold;
  margin-top: 3.125rem;
}

a.workspace-card {
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
a.workspace-card:hover {
  text-decoration: none;
  border-color: #337ab7;
  box-shadow: 0 5px 5px rgba(0, 0, 0, 0.03);
  color: #5e6983;
}

.workspace-card {
  font-size: 0.625rem;
  border: 1px solid #d8dce6;
  border-radius: 0.125rem;
  padding: 1.875rem 0.9375rem 0.9375rem;
  text-align: center;
  font-weight: bold;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  flex-direction: column;
  color: #5e6983;
}

.workspace-card-img {
  max-width: 114px;
  border-radius: 50%;
  margin: 0 auto 0.9375rem;
}

.workspace-card-body {
  min-height: 94px;
}

.workspace-card-title {
  font-size: 0.75rem;
  text-transform: uppercase;
  line-height: 0.9375rem;
  margin-bottom: 0.3125rem;
}

.workspace-card-footer {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pell {
  border: 1px solid #d8dce6;
  border-radius: 0.125rem;
}
.pell.is-invalid {
  border-color: #cc6565;
}

.pell-actionbar {
  padding: 0.375rem 0.8125rem;
}

.pell-button {
  border: 1px solid #d8dce6;
  width: 1.9375rem;
  height: 1.9375rem;
  border-radius: 0.125rem;
}
.pell-button:not(:last-of-type) {
  margin-right: 0.3125rem;
}
.pell-button:nth-child(3), .pell-button:nth-child(6), .pell-button:nth-child(8), .pell-button:nth-child(9) {
  margin-right: 1.25rem;
}
.pell-button .icon-bold,
.pell-button .icon-italic,
.pell-button .icon-underline,
.pell-button .icon-list,
.pell-button .icon-hash {
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  display: block;
  margin: 0 auto;
}
.pell-button .icon-bold {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 11 16'%3E%3Ctext fill='%235e6983' font-family='Arial-BoldMT, Arial' font-size='14' font-weight='700' transform='translate(0 13)'%3E%3Ctspan x='0' y='0'%3EB%3C/tspan%3E%3C/text%3E%3C/svg%3E");
  width: 11px;
  height: 16px;
}
.pell-button .icon-bold::before {
  content: "";
}
.pell-button .icon-italic {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 5 16'%3E%3Ctext fill='%235e6983' font-family='Arial-ItalicMT, Arial' font-size='14' font-style='italic' transform='translate(0 13)'%3E%3Ctspan x='0' y='0'%3EI%3C/tspan%3E%3C/text%3E%3C/svg%3E");
  width: 5px;
  height: 16px;
}
.pell-button .icon-italic::before {
  content: "";
}
.pell-button .icon-underline {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 11 16'%3E%3Ctext fill='%235e6983' font-family='ArialMT, Arial' font-size='14' text-decoration='underline' transform='translate(0 13)'%3E%3Ctspan x='0' y='0'%3EU%3C/tspan%3E%3C/text%3E%3C/svg%3E");
  width: 11px;
  height: 16px;
}
.pell-button .icon-underline::before {
  content: "";
}
.pell-button .icon-list {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 17.905 12.604'%3E%3Cg fill='none' stroke='%235e6983' stroke-linecap='square' stroke-linejoin='round'%3E%3Cpath d='M4.835.5h12.57M4.835 6.302h12.57M4.835 12.104h12.57M0 .5h0M0 6.302h0M0 12.104h0'/%3E%3C/g%3E%3C/svg%3E");
  width: 18px;
  height: 13px;
}
.pell-button .icon-list::before {
  content: "";
}
.pell-button .icon-hash {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 14.834 14.821'%3E%3Cg fill='none' stroke='%235e6983' stroke-linecap='square'%3E%3Cpath d='M6.034 8.102a3.457 3.457 0 005.213.373l2.074-2.074a3.457 3.457 0 10-4.887-4.888l-1.19 1.182'/%3E%3Cpath d='M8.805 6.719a3.457 3.457 0 00-5.213-.373L1.513 8.42a3.458 3.458 0 104.892 4.888l1.182-1.183'/%3E%3C/g%3E%3C/svg%3E");
  width: 15px;
  height: 15px;
}
.pell-button .icon-hash::before {
  content: "";
}

.pell-content {
  background-color: #fafafc;
}
.pell-content span {
  border-radius: 2px;
}

#engagement-tiles {
  position: relative;
}
#engagement-tiles .tiles-loader {
  display: none;
  position: absolute;
  left: 50%;
  top: 15%;
  z-index: 999;
}
#engagement-tiles.is-loading .tiles-loader {
  display: block;
}
#engagement-tiles.is-loading:after {
  background: white;
  content: "";
  height: 100%;
  left: 0;
  opacity: 0.8;
  position: absolute;
  top: 0;
  width: 100%;
}

body {
  line-height: 1.3125rem;
  border-top: 4px solid #002663;
}

.title {
  margin-bottom: 2rem;
}

.bg-muted {
  background-color: #f9f6f3;
}

.hide,
.hidden {
  display: none;
}

.show,
.shown {
  display: block;
}

@media (min-width: 1200px) {
  nav.main-nav > ul:after {
    content: "";
    display: block;
    width: 7.5rem;
    height: 3.125rem;
    background: url("/img/lsm-logo.svg") no-repeat;
    background-size: contain;
    margin: 4vw 0 0;
  }
}

section {
  padding: 0;
}

.ql-mention-list-item {
  width: 100%;
  max-width: 450px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.display-1,
.display-2,
.display-3,
.display-4,
.font-18,
.font-20 {
  font-family: "Roboto", sans-serif, serif;
}

.font-body {
  font-family: "Roboto", sans-serif, serif;
}

.organisation-logo {
  position: relative;
  width: 110px;
  background-size: 90%;
  background-position: center center;
  background-repeat: no-repeat;
  margin-right: 1rem;
}
.organisation-logo:before {
  display: block;
  content: " ";
  width: 100%;
  padding-top: 100%;
}
.organisation-logo > .inner {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.organisation-contacts-item:not(:nth-child(3n)) {
  border-bottom: 1px solid #d8dce6;
}

.recommendations h4 {
  font-size: 1rem;
  font-weight: normal;
  text-align: center;
}
.recommendations-data {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  justify-content: center;
}
.recommendations-data span {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  margin-right: 0.625rem;
}
.recommendations-data p {
  font-size: 2.8125rem;
  color: #337ab7;
  line-height: 45px;
  font-weight: 300;
  letter-spacing: -2.5px;
}
.recommendations-data p sup {
  font-size: 0.9375rem;
  font-weight: 500;
  vertical-align: super;
  padding-right: 5px;
}
.recommendations-data .open-status {
  color: #337ab7;
}
.recommendations-data .overdue-status {
  color: #d05a5a;
}
@media (min-width: 980px) {
  .recommendations-data.expiry-date p {
    font-size: 1.6rem;
  }
}
@media (min-width: 1580px) {
  .recommendations-data.expiry-date p {
    font-size: 2.5rem;
  }
}
@media (min-width: 1200px) and (max-width: 1579.98px) {
  .recommendations > .row > *:nth-child(1), .learning .form-horizontal .recommendations > .form-group > *:nth-child(1), .recommendations > .row > *:nth-child(2), .learning .form-horizontal .recommendations > .form-group > *:nth-child(2) {
    margin-bottom: 2rem;
  }
}
@media (max-width: 979.98px) {
  .recommendations > .row > *:nth-child(1), .learning .form-horizontal .recommendations > .form-group > *:nth-child(1), .recommendations > .row > *:nth-child(2), .learning .form-horizontal .recommendations > .form-group > *:nth-child(2) {
    margin-bottom: 2rem;
  }
}

.chartContainer.pieChart, .chartContainer#pieChart {
  height: 220px;
}
.chartContainer.pieChart.chartContainerBroker, .chartContainer#pieChart.chartContainerBroker {
  height: 280px;
}
@media (min-width: 1580px) {
  .chartContainer.pieChart.chartContainerBroker, .chartContainer#pieChart.chartContainerBroker {
    width: 50%;
  }
}
.chartContainer.columnChart, .chartContainer#pieChart, .chartContainer.columnChartCompany, .chartContainer#columnChartCompany {
  height: 350px;
}
@media (min-width: 1580px) {
  .chartContainer.columnChart.chartContainerBroker, .chartContainer#pieChart.chartContainerBroker, .chartContainer.columnChartCompany.chartContainerBroker, .chartContainer#columnChartCompany.chartContainerBroker {
    height: 270px;
  }
}
.chartContainer.horizontalColumnChart, .chartContainer#horizontalColumnChart {
  height: 300px;
}
@media (min-width: 600px) and (max-width: 979.98px) {
  .chartContainer {
    min-width: 300px;
  }
}

.legendContainer {
  width: 100%;
}
@media (min-width: 1580px) {
  .legendContainer {
    margin: 0 auto;
    width: 90%;
  }
}
.legendContainer.pieChartLegend, .legendContainer#pieChartLegend {
  height: 170px;
  min-height: 150px;
}
@media (min-width: 1200px) and (max-width: 1579.98px) {
  .legendContainer.pieChartLegend, .legendContainer#pieChartLegend {
    height: 180px;
  }
}
@media (min-width: 1580px) {
  .legendContainer.pieChartLegend.legendContainerBroker, .legendContainer#pieChartLegend.legendContainerBroker {
    width: 50%;
    min-height: 200px;
    height: 220px;
  }
}
.legendContainer.columnChartLegend, .legendContainer.columnChartCompanyLegend, .legendContainer#columnChartLegend, .legendContainer#columnChartCompanyLegend, .legendContainer#columnChartLegend2 {
  height: 50px;
  min-height: 50px;
}
@media (min-width: 1580px) {
  .legendContainer.columnChartLegend.legendContainerBroker, .legendContainer.columnChartCompanyLegend.legendContainerBroker, .legendContainer#columnChartLegend.legendContainerBroker, .legendContainer#columnChartCompanyLegend.legendContainerBroker, .legendContainer#columnChartLegend2.legendContainerBroker {
    height: 30px;
    min-height: 50px;
  }
}

.chartContainer#pieChart {
  height: 220px;
}

.chartContainer#pieChart.chartContainerBroker {
  height: 280px;
}

@media (min-width: 1580px) {
  .chartContainer#pieChart.chartContainerBroker {
    width: 50%;
  }
}
.chartContainer#columnChart,
.chartContainer#columnChartLossEstimate,
.chartContainer#columnChartCompany,
.chartContainer#columnChartLossEstimate {
  height: 350px;
}

@media (min-width: 1580px) {
  .chartContainer#columnChart.chartContainerBroker,
.chartContainer#columnChartLossEstimate.chartContainerBroker,
.chartContainer#columnChartCompany.chartContainerBroker {
    height: 270px;
  }
}
.chartContainer#horizontalColumnChart {
  height: 300px;
}

@media (min-width: 600px) and (max-width: 979.98px) {
  .chartContainer {
    min-width: 300px;
  }
}
.legendContainer {
  width: 100%;
}

@media (min-width: 1580px) {
  .legendContainer {
    margin: 0 auto;
    width: 90%;
  }
}
.legendContainer#pieChartLegend {
  height: 170px;
  min-height: 150px;
}

@media (min-width: 1200px) and (max-width: 1579.98px) {
  .legendContainer#pieChartLegend {
    height: 180px;
  }
}
@media (min-width: 1580px) {
  .legendContainer#pieChartLegend.legendContainerBroker {
    width: 50%;
    min-height: 200px;
    height: 220px;
  }
}
.legendContainer#columnChartLegend,
.legendContainer#columnChartLegend2,
.legendContainer#columnChartCompanyLegend {
  height: 50px;
  min-height: 50px;
}

@media (min-width: 1580px) {
  .legendContainer#columnChartLegend.legendContainerBroker,
.legendContainer#columnChartLegend2.legendContainerBroker,
.legendContainer#columnChartCompanyLegend.legendContainerBroker {
    height: 30px;
    min-height: 50px;
  }
}
.csr-table thead th:first-child,
.csr-table tbody th:first-child {
  position: -webkit-sticky;
  position: sticky;
  left: 0;
  z-index: 10;
  box-shadow: 0px 2px 4px -3px rgba(0, 0, 0, 0.5);
}
.csr-table tbody th:first-child span {
  color: rgba(94, 105, 131, 0.7);
  line-height: 15px;
  width: auto;
  height: auto;
  margin: 0;
  display: inline-block;
}
.csr-table thead th,
.csr-table th,
.csr-table td {
  font-size: 0.625rem;
}
.csr-table thead th {
  position: relative;
  height: 110px;
}
.csr-table th {
  background: #fff;
}
.csr-table span {
  display: block;
  width: 1.125rem;
  height: 1.125rem;
  border-radius: 50%;
  margin: 0 auto;
}
.csr-table span.csr-title {
  line-height: 11px;
  transform: rotate(-90deg);
  width: 110px;
  height: 110px;
  position: absolute;
  top: 0;
}

.engagement-item {
  width: 100%;
  border: 1px solid #d8dce6;
  border-radius: 0.125rem;
  background: -webkit-gradient(linear, left top, left bottom, from(#f4f8fc), to(#fff));
  background: -o-linear-gradient(top, #f4f8fc 0%, #fff 100%);
  background: linear-gradient(to bottom, #f4f8fc 0%, #fff 100%);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  flex-direction: column;
  align-content: center;
  align-items: center;
  padding: 3rem 1rem;
  margin-bottom: 2rem;
}
.engagement-item span {
  font-size: 2.1875rem;
  line-height: 1;
  display: inline-block;
  height: 118px;
  min-width: 118px;
  border-radius: 50%;
  color: #002663;
  letter-spacing: -1.5px;
  margin-bottom: 1rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}
.engagement-item span.border {
  font-size: 2.8125rem;
  font-weight: 300;
}
.engagement-item p {
  text-align: center;
}

.riskgrading-item {
  background: #fff !important;
  border: none !important;
  margin: 0px !important;
  padding: 0px !important;
  padding-top: 20px !important;
}
.riskgrading-item span {
  color: red;
  background: #FFE5E5;
  border-color: #FFE5E5 !important;
}

a.policy-link:hover {
  text-decoration: none;
}
a.policy-link i {
  padding-right: 10px;
}

div.display-2-height {
  line-height: 2 !important;
}

span.claim-handler {
  font-size: 1.25rem !important;
  height: 100% !important;
}

div.custom-multiselect {
  width: 100%;
}

span.page-break {
  width: 100%;
  border-bottom: 1px solid #d8dce6;
  margin: 15% 0 15% 0;
}

span.span-policies {
  font-size: 0.8rem !important;
  margin: 0 10px 10px 0;
}

div.underwriter-policy-search {
  padding-bottom: 15px !important;
}

.lets-talk {
  /*
  * admin
   */
}
.lets-talk-header {
  padding: 1.5rem 0 3rem;
  font-size: 16px;
  line-height: 26px;
}
@media (min-width: 980px) {
  .lets-talk-header {
    background-image: url("/img/lets-talk/header.jpg");
    background-size: contain;
    background-position: 90% top;
    background-repeat: no-repeat;
  }
}
.lets-talk-header .logo {
  background-image: url("/img/logo.png");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: top left;
  width: 15a4px;
  height: 60px;
  margin-left: -10px;
}
.lets-talk-wrapper {
  position: relative;
  overflow: hidden;
}
.lets-talk-schedule {
  font-size: 12px;
}
.lets-talk-schedule .timeline-grid {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(10, minmax(30px, 1fr));
  grid-template-rows: 8px;
  grid-auto-rows: 8px;
  background: #f4f8fc;
  border-radius: 10px;
}
.lets-talk-schedule .timeline-grid > * {
  background: #337ab7;
  border-radius: 10px;
  -ms-grid-row: 1;
  cursor: pointer;
}
.lets-talk-schedule .timeline-grid > *.task-start-1 {
  grid-column-start: 1;
}
.lets-talk-schedule .timeline-grid > *.task-span-1 {
  -ms-grid-column-span: 1;
  grid-column-end: span 1;
}
.lets-talk-schedule .timeline-grid > *.task-start-2 {
  grid-column-start: 2;
}
.lets-talk-schedule .timeline-grid > *.task-span-2 {
  -ms-grid-column-span: 2;
  grid-column-end: span 2;
}
.lets-talk-schedule .timeline-grid > *.task-start-3 {
  grid-column-start: 3;
}
.lets-talk-schedule .timeline-grid > *.task-span-3 {
  -ms-grid-column-span: 3;
  grid-column-end: span 3;
}
.lets-talk-schedule .timeline-grid > *.task-start-4 {
  grid-column-start: 4;
}
.lets-talk-schedule .timeline-grid > *.task-span-4 {
  -ms-grid-column-span: 4;
  grid-column-end: span 4;
}
.lets-talk-schedule .timeline-grid > *.task-start-5 {
  grid-column-start: 5;
}
.lets-talk-schedule .timeline-grid > *.task-span-5 {
  -ms-grid-column-span: 5;
  grid-column-end: span 5;
}
.lets-talk-schedule .timeline-grid > *.task-start-6 {
  grid-column-start: 6;
}
.lets-talk-schedule .timeline-grid > *.task-span-6 {
  -ms-grid-column-span: 6;
  grid-column-end: span 6;
}
.lets-talk-schedule .timeline-grid > *.task-start-7 {
  grid-column-start: 7;
}
.lets-talk-schedule .timeline-grid > *.task-span-7 {
  -ms-grid-column-span: 7;
  grid-column-end: span 7;
}
.lets-talk-schedule .timeline-grid > *.task-start-8 {
  grid-column-start: 8;
}
.lets-talk-schedule .timeline-grid > *.task-span-8 {
  -ms-grid-column-span: 8;
  grid-column-end: span 8;
}
.lets-talk-schedule .timeline-grid > *.task-start-9 {
  grid-column-start: 9;
}
.lets-talk-schedule .timeline-grid > *.task-span-9 {
  -ms-grid-column-span: 9;
  grid-column-end: span 9;
}
.lets-talk-schedule .timeline-grid > *.task-start-10 {
  grid-column-start: 10;
}
.lets-talk-schedule .timeline-grid > *.task-span-10 {
  -ms-grid-column-span: 10;
  grid-column-end: span 10;
}
@media screen and (-ms-high-contrast: active), screen and (-ms-high-contrast: none) {
  .lets-talk-schedule .timeline-grid {
    display: block;
    position: relative;
    height: 8px;
  }
  .lets-talk-schedule .timeline-grid > * {
    width: 3.5vw;
    position: absolute;
    height: 100%;
  }
  .lets-talk-schedule .timeline-grid > *.task-start-0 {
    left: -webkit-calc(0 * 3.5vw);
    left: calc(0 * 3.5vw);
  }
  .lets-talk-schedule .timeline-grid > *.task-span-0 {
    width: -webkit-calc(0 * 3.5vw);
    width: calc(0 * 3.5vw);
  }
  .lets-talk-schedule .timeline-grid > *.task-start-1 {
    left: -webkit-calc(1 * 3.5vw);
    left: calc(1 * 3.5vw);
  }
  .lets-talk-schedule .timeline-grid > *.task-span-1 {
    width: -webkit-calc(1 * 3.5vw);
    width: calc(1 * 3.5vw);
  }
  .lets-talk-schedule .timeline-grid > *.task-start-2 {
    left: -webkit-calc(2 * 3.5vw);
    left: calc(2 * 3.5vw);
  }
  .lets-talk-schedule .timeline-grid > *.task-span-2 {
    width: -webkit-calc(2 * 3.5vw);
    width: calc(2 * 3.5vw);
  }
  .lets-talk-schedule .timeline-grid > *.task-start-3 {
    left: -webkit-calc(3 * 3.5vw);
    left: calc(3 * 3.5vw);
  }
  .lets-talk-schedule .timeline-grid > *.task-span-3 {
    width: -webkit-calc(3 * 3.5vw);
    width: calc(3 * 3.5vw);
  }
  .lets-talk-schedule .timeline-grid > *.task-start-4 {
    left: -webkit-calc(4 * 3.5vw);
    left: calc(4 * 3.5vw);
  }
  .lets-talk-schedule .timeline-grid > *.task-span-4 {
    width: -webkit-calc(4 * 3.5vw);
    width: calc(4 * 3.5vw);
  }
  .lets-talk-schedule .timeline-grid > *.task-start-5 {
    left: -webkit-calc(5 * 3.5vw);
    left: calc(5 * 3.5vw);
  }
  .lets-talk-schedule .timeline-grid > *.task-span-5 {
    width: -webkit-calc(5 * 3.5vw);
    width: calc(5 * 3.5vw);
  }
  .lets-talk-schedule .timeline-grid > *.task-start-6 {
    left: -webkit-calc(6 * 3.5vw);
    left: calc(6 * 3.5vw);
  }
  .lets-talk-schedule .timeline-grid > *.task-span-6 {
    width: -webkit-calc(6 * 3.5vw);
    width: calc(6 * 3.5vw);
  }
  .lets-talk-schedule .timeline-grid > *.task-start-7 {
    left: -webkit-calc(7 * 3.5vw);
    left: calc(7 * 3.5vw);
  }
  .lets-talk-schedule .timeline-grid > *.task-span-7 {
    width: -webkit-calc(7 * 3.5vw);
    width: calc(7 * 3.5vw);
  }
  .lets-talk-schedule .timeline-grid > *.task-start-8 {
    left: -webkit-calc(8 * 3.5vw);
    left: calc(8 * 3.5vw);
  }
  .lets-talk-schedule .timeline-grid > *.task-span-8 {
    width: -webkit-calc(8 * 3.5vw);
    width: calc(8 * 3.5vw);
  }
  .lets-talk-schedule .timeline-grid > *.task-start-9 {
    left: -webkit-calc(9 * 3.5vw);
    left: calc(9 * 3.5vw);
  }
  .lets-talk-schedule .timeline-grid > *.task-span-9 {
    width: -webkit-calc(9 * 3.5vw);
    width: calc(9 * 3.5vw);
  }
}
.lets-talk-schedule .legend {
  font-size: 10px;
}
.lets-talk-schedule .legend.week-view {
  font-size: 13px;
}
.lets-talk-schedule .legend > * {
  margin-right: 8px;
  position: relative;
  padding-left: 10px;
  white-space: nowrap;
}
.lets-talk-schedule .legend > *::before {
  content: "";
  display: block;
  width: 5px;
  height: 5px;
  border-radius: 50%;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}
.lets-talk-schedule .legend-now {
  color: #2e6da4;
}
.lets-talk-schedule .legend-now:before {
  background: #2e6da4;
}
.lets-talk-schedule .legend-future {
  color: #64d091;
}
.lets-talk-schedule .legend-future:before {
  background: #64d091;
}
.lets-talk-schedule .timeline-times {
  display: grid;
  grid-template-columns: repeat(10, minmax(30px, 1fr));
  grid-template-rows: 20px;
  grid-auto-rows: 20px;
  position: relative;
}
.lets-talk-schedule .timeline-times > * {
  font-size: 10px;
  font-weight: normal;
  -ms-grid-row: 1;
  position: relative;
}
@media (min-width: 1200px) {
  .lets-talk-schedule .timeline-times > * {
    text-indent: -10px;
  }
}
.lets-talk-schedule .timeline-times > *:after {
  content: "";
  display: block;
  height: 5px;
  width: 1px;
  background: #d8dce6;
  position: absolute;
  left: 0;
  bottom: -5px;
}
.lets-talk-schedule .timeline-times:after {
  content: "6pm";
  position: absolute;
  right: -10px;
  top: 0;
  font-size: 10px;
}
@media (max-width: 1199.98px) {
  .lets-talk-schedule .timeline-times:after {
    right: -20px;
  }
}
.lets-talk-schedule .timeline-times:before {
  content: "";
  display: block;
  height: 5px;
  width: 1px;
  background: #d8dce6;
  position: absolute;
  right: 0;
  bottom: -5px;
}
.lets-talk-schedule .timeline-times .current_time {
  color: #337ab7;
  font-weight: bold;
}
@media screen and (-ms-high-contrast: active), screen and (-ms-high-contrast: none) {
  .lets-talk-schedule .timeline-times {
    display: block;
    position: relative;
    height: 30px;
  }
  .lets-talk-schedule .timeline-times > *:nth-child(1) {
    width: 3.5vw;
    position: absolute;
    height: 100%;
    left: -webkit-calc(1 * 3.5vw);
    left: calc(1 * 3.5vw);
  }
  .lets-talk-schedule .timeline-times > *:nth-child(2) {
    width: 3.5vw;
    position: absolute;
    height: 100%;
    left: -webkit-calc(2 * 3.5vw);
    left: calc(2 * 3.5vw);
  }
  .lets-talk-schedule .timeline-times > *:nth-child(3) {
    width: 3.5vw;
    position: absolute;
    height: 100%;
    left: -webkit-calc(3 * 3.5vw);
    left: calc(3 * 3.5vw);
  }
  .lets-talk-schedule .timeline-times > *:nth-child(4) {
    width: 3.5vw;
    position: absolute;
    height: 100%;
    left: -webkit-calc(4 * 3.5vw);
    left: calc(4 * 3.5vw);
  }
  .lets-talk-schedule .timeline-times > *:nth-child(5) {
    width: 3.5vw;
    position: absolute;
    height: 100%;
    left: -webkit-calc(5 * 3.5vw);
    left: calc(5 * 3.5vw);
  }
  .lets-talk-schedule .timeline-times > *:nth-child(6) {
    width: 3.5vw;
    position: absolute;
    height: 100%;
    left: -webkit-calc(6 * 3.5vw);
    left: calc(6 * 3.5vw);
  }
  .lets-talk-schedule .timeline-times > *:nth-child(7) {
    width: 3.5vw;
    position: absolute;
    height: 100%;
    left: -webkit-calc(7 * 3.5vw);
    left: calc(7 * 3.5vw);
  }
  .lets-talk-schedule .timeline-times > *:nth-child(8) {
    width: 3.5vw;
    position: absolute;
    height: 100%;
    left: -webkit-calc(8 * 3.5vw);
    left: calc(8 * 3.5vw);
  }
  .lets-talk-schedule .timeline-times > *:nth-child(9) {
    width: 3.5vw;
    position: absolute;
    height: 100%;
    left: -webkit-calc(9 * 3.5vw);
    left: calc(9 * 3.5vw);
  }
  .lets-talk-schedule .timeline-times > *:nth-child(10) {
    width: 3.5vw;
    position: absolute;
    height: 100%;
    left: -webkit-calc(10 * 3.5vw);
    left: calc(10 * 3.5vw);
  }
}
@media (max-width: 599.98px) {
  .lets-talk-schedule .timeline-scroll {
    overflow: auto;
    padding: 0 40px 0 30px !important;
  }
  .lets-talk-schedule .timeline-scroll > * {
    min-width: 420px;
  }
}
.lets-talk-schedule .timeline-days {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  justify-content: space-between;
}
.lets-talk-schedule .timeline-days > * {
  min-width: 80px;
  margin-right: 5px;
}
.lets-talk-schedule .timeline-day {
  display: block;
  height: 8px;
  width: 100%;
  background: #337ab7;
  border-radius: 10px;
  cursor: pointer;
}
@media (min-width: 980px) {
  .lets-talk-schedule .current_time_separator {
    color: red;
    width: 100vw;
    height: 100%;
    background: rgba(255, 255, 255, 0.7);
    position: absolute;
    top: 85px;
    left: 0;
    z-index: 1;
    border-right: 1px solid #337ab7;
  }
  .lets-talk-schedule .current_time_separator:before {
    content: "";
    display: block;
    background: #337ab7;
    width: 5px;
    height: 5px;
    border-radius: 50%;
    position: absolute;
    top: 0;
    right: -3px;
  }
}
.lets-talk-team {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: flex-start;
  position: relative;
  z-index: 2;
}
.lets-talk-team-profile {
  border-radius: 50%;
}
.lets-talk-team-profile-img {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  border: 2px solid #fff;
}
.lets-talk-team-profile.active {
  border: 2px solid #64d091;
}
.lets-talk-team-profile.active.big {
  border-width: 4px;
}
.lets-talk-team-profile.big .lets-talk-team-profile-img {
  width: 78px;
  height: 78px;
  border-width: 4px;
}
.lets-talk-team-name {
  font-size: 10px;
  line-height: 14px;
}
.lets-talk-team-name b {
  color: #002663;
  font-size: 12px;
}
.lets-talk-pager {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}
.lets-talk-pager span,
.lets-talk-pager a {
  display: block;
  width: 40px;
  height: 40px;
  border: 1px solid #d8dce6;
  border-radius: 50%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 5px;
  color: #5e6983;
  cursor: pointer;
}
.lets-talk-pager span:hover,
.lets-talk-pager a:hover {
  text-decoration: none;
  background: #d8dce6;
}
.lets-talk-pager span.disabled,
.lets-talk-pager a.disabled {
  cursor: default;
  background: none;
  opacity: 0.5;
}
.lets-talk-pager.header {
  position: absolute;
  right: 0;
  top: 12px;
}
.lets-talk-pager .display-date {
  font-size: 14px;
  font-weight: 500;
  color: #5e6983;
}
.lets-talk-tooltip {
  opacity: 1 !important;
}
.lets-talk-tooltip .tooltip-inner {
  background: #fff;
  border: 1px solid #d8dce6;
  color: #5e6983;
}
.lets-talk-tooltip p {
  margin: 0.4rem 0 0.5rem !important;
}
.lets-talk-multiselect {
  border: 1px solid #d8dce6;
  background: #fafafc;
  max-height: 300px;
  overflow: auto;
}
.lets-talk-multiselect > * label {
  display: block;
  padding: 0.5rem 40px 0.5rem 1rem;
  margin: 0;
  font-weight: normal;
  cursor: pointer;
  position: relative;
}
.lets-talk-multiselect > * input {
  display: none;
}
.lets-talk-multiselect > * input:checked + label {
  background-color: #337ab7;
  color: #fff;
}
.lets-talk-multiselect > * input:checked + label:after {
  content: "\E92B";
  font-family: "lsm-icons";
  font-size: 20px;
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
}
.lets-talk-admin-slots > * {
  padding: 8px 20px;
  border: 1px solid #d8dce6;
  color: #5e6983;
  font-size: 12px;
  cursor: pointer;
  background: #fff;
  box-shadow: 1px 0 5px -2px rgba(0, 0, 0, 0.2);
  margin-right: 5px;
  margin-bottom: 5px;
  display: inline-block;
}
.lets-talk-admin-slots > *.availability:not(.disabled) {
  border-color: #337ab7;
}
.lets-talk-admin-slots > *.availability:not(.text-light) {
  color: #337ab7 !important;
}
.lets-talk-admin-slots > *.availability.bg-primary .dropdown-toggle span, .lets-talk-admin-slots > *.availability.bg-primary .dropdown-toggle span:before, .lets-talk-admin-slots > *.availability.bg-primary .dropdown-toggle span:after {
  background: #fff !important;
}
.lets-talk-admin-slots > *.availability.bg-primary .dropdown-toggle:hover, .lets-talk-admin-slots > *.availability.bg-primary .dropdown-toggle[aria-expanded=true] {
  background: transparent;
}
.lets-talk-admin-slots > *.recurring {
  position: relative;
}
.lets-talk-admin-slots > *.recurring:before {
  font-family: "lsm-icons";
  content: "\E9C0";
  background: #337ab7;
  color: #fff;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  height: 20px;
  width: 20px;
  position: absolute;
  top: 50%;
  left: 3%;
  font-size: 12px;
  transform: translate(-50%, -50%);
}
.lets-talk-admin-slots > *.disabled {
  opacity: 0.4;
  color: #5e6983 !important;
}
.lets-talk-admin-slots > *.disabled .dropdown {
  visibility: hidden;
}
.lets-talk-admin-slots > *:hover {
  background-color: #fafafc;
}
.lets-talk-admin-legend > * {
  padding-left: 20px;
}
.lets-talk-admin-legend > *:before {
  border-radius: 0;
  width: 15px;
  height: 15px;
}
.lets-talk-admin-legend > *.legend-unavailable:before {
  border: 1px solid #d8dce6;
}
.lets-talk-admin-legend > *.legend-available:before {
  border: 1px solid #337ab7;
}
.lets-talk-admin-legend > *.legend-booked:before {
  background: #337ab7;
}

.tooltip.lets-talk-tooltip.bs-tooltip-bottom .arrow::before,
.tooltip.lets-talk-tooltip.bs-tooltip-auto[x-placement^=bottom] .arrow::before {
  border-bottom-color: #d8dce6;
}

#bookingmodal .selectize-control .selectize-input,
#bookingmodal .selectize-control.single .selectize-input {
  border: 1px solid #d8dce6;
  background: #fafafc;
  border-radius: 0.125rem;
  box-shadow: none;
  padding: 0 15px;
  height: 45px;
  line-height: 45px;
  color: #5e6983;
  position: relative;
  border-radius: 0.125rem;
}
#bookingmodal .selectize-control .selectize-input:after,
#bookingmodal .selectize-control.single .selectize-input:after {
  display: none;
}
#bookingmodal .selectize-control .selectize-input .item,
#bookingmodal .selectize-control.single .selectize-input .item {
  display: none;
}
#bookingmodal .selectize-control .selectize-input input,
#bookingmodal .selectize-control.single .selectize-input input {
  color: #898b8f;
  font-size: 0.875rem;
  width: 100%;
}
#bookingmodal .selectize-control .selectize-input .name,
#bookingmodal .selectize-control.single .selectize-input .name {
  font-size: 0.875rem;
}
#bookingmodal .selectize-control .selectize-input.dropdown-active:after,
#bookingmodal .selectize-control.single .selectize-input.dropdown-active:after {
  margin-top: 0;
  border-left: 1px solid #d8dce6;
}
#bookingmodal .selectize-control [type=select-multiple],
#bookingmodal .selectize-control.single [type=select-multiple] {
  top: 7px;
}
#bookingmodal .selectize-control .selectize-dropdown,
#bookingmodal .selectize-control.single .selectize-dropdown {
  border: 1px solid #d8dce6;
  box-shadow: 0 4px 10px -5px #d8dce6;
  color: #5e6983;
}
#bookingmodal .selectize-control .selectize-dropdown-scroll,
#bookingmodal .selectize-control.single .selectize-dropdown-scroll {
  overflow-x: hidden;
  max-height: 200px;
  overflow-y: auto;
}
#bookingmodal .selectize-control .selectize-dropdown-scroll .simplebar-scrollbar:before,
#bookingmodal .selectize-control.single .selectize-dropdown-scroll .simplebar-scrollbar:before {
  background: #5e6983;
}
#bookingmodal .selectize-control .selectize-dropdown-scroll .simplebar-scrollbar.simplebar-visible:before,
#bookingmodal .selectize-control.single .selectize-dropdown-scroll .simplebar-scrollbar.simplebar-visible:before {
  opacity: 1;
}
#bookingmodal .selectize-control .selectize-dropdown-content,
#bookingmodal .selectize-control.single .selectize-dropdown-content {
  overflow: visible;
  -webkit-overflow-scrolling: unset;
  max-height: none;
}
#bookingmodal .selectize-control .selectize-dropdown-content > *,
#bookingmodal .selectize-control .selectize-dropdown-content option,
#bookingmodal .selectize-control.single .selectize-dropdown-content > *,
#bookingmodal .selectize-control.single .selectize-dropdown-content option {
  padding: 1.1875rem 1.1rem 1.1875rem 1.0625rem;
  position: relative;
  cursor: pointer;
}
#bookingmodal .selectize-control .selectize-dropdown-content > *.active,
#bookingmodal .selectize-control .selectize-dropdown-content option.active,
#bookingmodal .selectize-control.single .selectize-dropdown-content > *.active,
#bookingmodal .selectize-control.single .selectize-dropdown-content option.active {
  color: #002663;
  background: #f7f7f9;
}
#bookingmodal .selectize-control .selectize-dropdown-content > *.selected:after,
#bookingmodal .selectize-control .selectize-dropdown-content option.selected:after,
#bookingmodal .selectize-control.single .selectize-dropdown-content > *.selected:after,
#bookingmodal .selectize-control.single .selectize-dropdown-content option.selected:after {
  font-family: "lsm-icons";
  content: "\E92B";
  color: #337ab7;
  position: absolute;
  top: 50%;
  right: 1rem;
  transform: translateY(-50%);
  font-size: 18px;
}
#bookingmodal .selectize-control.single .selectize-dropdown-content > *.selected:after,
#bookingmodal .selectize-control.single .selectize-dropdown-content option.selected:after {
  display: none;
}
#bookingmodal .form-group {
  position: relative;
}
#bookingmodal .feedback-name-selectize.invalid-feedback {
  display: block;
  position: absolute;
  right: 0;
  top: 78px;
}
#bookingmodal .feedback-name-selectize.invalid-feedback + .selectize-control.single .selectize-input {
  border-color: #cc6565;
}

.login-page-bg {
  background-size: cover;
  background-repeat: no-repeat;
  background-position: top center;
  background-image: url("/img/lets-talk/login.jpg");
  height: 50vh;
  width: 100%;
}
@media (min-width: 980px) {
  .login-page-bg {
    position: absolute;
    width: 50%;
    height: 100%;
    top: 0;
    right: 0;
  }
}

.logo {
  height: 85px;
}

.logo img {
  max-width: 100%;
  max-height: 100%;
}

.caption:not(.no-opacity) {
  opacity: 0.6;
}

p:last-of-type {
  margin-bottom: 0;
}

.caption {
  font-size: 0.75rem;
}

.poll-container .text-purple {
  color: #191446;
}
.poll-container .poll-option-label .custom-radio-checkmark {
  width: 1em;
  height: 1em;
  border: 1px solid #E0E3EB;
  border-radius: 50%;
  transform: translateY(-0.05em);
}
.poll-container .poll-option-label input {
  opacity: 0;
  height: 0;
  width: 0;
}
.poll-container .poll-option-label input:checked + .custom-radio-checkmark {
  background: -o-radial-gradient(#337ab7 40%, rgba(255, 0, 0, 0) 41%);
  background: radial-gradient(#337ab7 40%, rgba(255, 0, 0, 0) 41%);
}
.poll-container .poll-option-label .poll-option-text {
  line-height: 1;
  margin-left: 0.5em;
}
.poll-container .poll-option-label .poll-option-text.voted {
  line-height: normal;
}
.poll-container .poll-option .voting-tally-container .voting-tally-bar {
  background-color: #78E1E1;
  border-radius: 100px;
}
.poll-container .poll-option .voting-tally-container p {
  color: #06748C;
}
.poll-container .submit-container button {
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}
.poll-container .submit-container button:disabled {
  cursor: default;
}

.message-poll-container .text-purple {
  color: #191446;
}
.message-poll-container .message-poll-tally-container .message-poll-tally-bar {
  background-color: #78E1E1;
  border-radius: 100px;
}
.message-poll-container .message-poll-tally-container p {
  color: #06748C;
}

.font-14 {
  font-size: 14px !important;
}

.submission-gradings.disable-box {
  background-color: #fff;
  opacity: 0.5;
}
.submission-gradings.disable-box .item-tier-2 {
  pointer-events: none;
}
.submission-gradings .show-toggle-tier1 p, .submission-gradings .show-toggle-tier2 p {
  margin-left: 5px !important;
  margin-bottom: 0 !important;
}
.submission-gradings .item {
  position: relative;
  margin-bottom: 10px;
  margin-top: 20px;
}
.submission-gradings .item.disable-box {
  background-color: #fff;
  opacity: 0.5;
}
.submission-gradings .item.disable-box .field-wrapper {
  pointer-events: none;
}
.submission-gradings .item.disable-box .show-toggle-tier1, .submission-gradings .item.disable-box .show-toggle-tier2 {
  opacity: 1;
  visibility: visible;
}
.submission-gradings .item .show-toggle {
  position: absolute;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  justify-content: center;
  align-items: center;
  top: 28px;
  left: 50%;
  width: 223px;
  height: 36px;
  border-radius: 30px;
  background: #002663;
  z-index: 2;
  opacity: 0;
  visibility: hidden;
  transform: translateX(-50%);
  transition: opacity 0.4s ease-out, visibility 0.4s ease-out;
}
@media (prefers-reduced-motion: reduce) {
  .submission-gradings .item .show-toggle {
    transition: none;
  }
}
.submission-gradings .item .show-toggle:hover {
  text-decoration: none;
}
.submission-gradings .item .show-toggle p {
  font-size: 14px;
  color: #C7DCEE;
  margin-left: 10px;
}
.submission-gradings .item .show-toggle p span {
  color: #fff;
  text-decoration: underline;
}
.submission-gradings .item .show-toggle img {
  width: 16px;
  height: auto;
}
.submission-gradings .item .show-hide-toggle {
  position: absolute;
  top: -8px;
  right: 0;
  width: 36px;
  height: 36px;
  border-radius: 30px;
  padding: 7px;
  transition: background 0.4s ease-out;
  font-size: 22px;
  cursor: pointer;
}
@media (prefers-reduced-motion: reduce) {
  .submission-gradings .item .show-hide-toggle {
    transition: none;
  }
}
.submission-gradings .item .show-hide-toggle .icon-eye {
  display: block;
  position: relative;
  z-index: 1;
}
.submission-gradings .item .show-hide-toggle:hover {
  background: rgba(94, 105, 131, 0.1);
}
.submission-gradings .item .show-hide-toggle .icon-eye:hover + .hide-tip {
  opacity: 1;
  visibility: visible;
}
.submission-gradings .item .item-tier-2 {
  padding: 16px;
}
.submission-gradings .item .item-tier-2 .item:last-of-type {
  margin-bottom: 0;
}
.submission-gradings .item .item-tier-2 .item.disable-box .show-toggle-tier2 {
  opacity: 1;
  visibility: visible;
}
.submission-gradings .item .head-toggle {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  cursor: pointer;
  margin-bottom: 10px;
}
.submission-gradings .item .drag {
  margin-right: 10px;
  cursor: move;
  cursor: -webkit-grabbing;
}
.submission-gradings .item .drag img {
  display: block;
}
.submission-gradings .item .head-title {
  position: relative;
  padding-right: 40px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
}
.submission-gradings .item .head-title p {
  font-size: 16px;
}
@media not print {
  @media (max-width: 1135px) {
    .submission-gradings .item .head-title p {
      font-size: 14px;
    }
  }
}
.submission-gradings .item .head-title .srg-field {
  padding-top: 0 !important;
}
.submission-gradings .item .item-tier-toggle {
  display: none;
}
@media print {
  .submission-gradings .item .item-tier-toggle {
    display: block;
  }
}
.submission-gradings .item .toggle-content {
  display: none;
  background: #fff;
  padding: 16px;
}
@media print {
  .submission-gradings .item .toggle-content {
    display: block;
  }
}
.submission-gradings .item .toggle-content p {
  font-size: 16px;
  margin-bottom: 10px;
}
.submission-gradings .item .toggle-content .readmore {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  color: #337AB7;
  line-height: 20px;
  letter-spacing: 0.32px;
  -webkit-text-decoration-line: underline;
          text-decoration-line: underline;
}
.submission-gradings .item .toggle-arrow {
  position: absolute;
  top: 13px;
  right: 22px;
  transition: transform 0.5s ease-out;
}
@media (prefers-reduced-motion: reduce) {
  .submission-gradings .item .toggle-arrow {
    transition: none;
  }
}
.submission-gradings .item .reminder {
  position: absolute;
  top: 10px;
  right: 51px;
}

.commentary-wrapper {
  position: relative;
}
.commentary-wrapper.disable-box .toggle-container {
  opacity: 0.5;
  pointer-events: none;
}
.commentary-wrapper.disable-box #commentary-show {
  opacity: 1;
  visibility: visible;
}
.commentary-wrapper #commentary-show p {
  margin-left: 5px !important;
  margin-bottom: 0 !important;
}
.commentary-wrapper .head {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
}
.commentary-wrapper .head #commentary-hide {
  font-size: 22px;
  margin-top: 20px;
  margin-left: 5px;
  cursor: pointer;
}
.commentary-wrapper #commentary-show {
  position: absolute;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  justify-content: center;
  align-items: center;
  top: 28px;
  left: 50%;
  width: 223px;
  height: 36px;
  border-radius: 30px;
  background: #002663;
  z-index: 2;
  opacity: 0;
  visibility: hidden;
  transform: translateX(-50%);
  transition: opacity 0.4s ease-out, visibility 0.4s ease-out;
}
@media (prefers-reduced-motion: reduce) {
  .commentary-wrapper #commentary-show {
    transition: none;
  }
}
.commentary-wrapper #commentary-show:hover {
  text-decoration: none;
}
.commentary-wrapper #commentary-show p {
  font-size: 14px;
  color: #C7DCEE;
  margin-left: 10px;
}
.commentary-wrapper #commentary-show p span {
  color: #fff;
  text-decoration: underline;
}
.commentary-wrapper #commentary-show img {
  width: 16px;
  height: auto;
}

#ffFormCreate #ffForm div ul {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  flex-wrap: wrap;
}
#ffFormCreate #ffForm div ul li {
  margin-right: 0.625rem;
}
#ffFormCreate #ffForm div ul li a:hover {
  background: #fff;
}
#ffFormCreate .tab-pane h2 {
  font-size: 14px;
  margin-bottom: 0;
}
#ffFormCreate .tab-pane a:hover {
  text-decoration: none;
}
#ffFormCreate .tab-pane ul {
  list-style-type: none;
  margin: 0;
  padding: 0 1.25rem 1.25rem;
}

.underwriter-policy .selectric-wrapper {
  width: 100%;
}
.underwriter-policy .selectric-wrapper .selectric {
  margin-top: 3px;
}
.underwriter-policy .selectric-wrapper .selectric .label, .underwriter-policy .selectric-wrapper .selectric .button {
  height: 30px;
  line-height: 30px;
}

.ffaccordion h3 {
  display: block;
  text-align: left;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 1.5px;
  position: relative;
  cursor: pointer;
}
.ffaccordion h3:last-of-type + div {
  display: none;
}
.ffaccordion h3:after {
  font-family: "lsm-icons";
  content: "\E92E";
  margin-left: 0.5rem;
}

.survey-report .btn, .survey-report #ffFormCreate #ffForm div ul li a, #ffFormCreate #ffForm div ul li .survey-report a {
  margin-bottom: 0.9375rem;
}

.in {
  display: block;
}

.fade.in {
  opacity: 1 !important;
}

@media (min-width: 980px) {
  /* .underwriter-policy {
      .selectric {
          min-width: 230px;
      }
  } */
  .files td:first-child {
    width: 20%;
  }
  .files td:nth-child(2) {
    width: 30%;
  }
  .files td:nth-child(3) {
    width: 50%;
  }
  .files .preview {
    display: block;
    max-width: 300px;
  }
  .files .preview img {
    max-width: 100%;
  }
}
.messenger__input-options-title {
  text-transform: none;
}

select {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background-size: 14px;
  background-repeat: no-repeat;
  background-position: -webkit-calc(100% - .8rem) center;
  background-position: calc(100% - .8rem) center;
  background-image: url("data:image/svg+xml,%3Csvg id='Layer_1' data-name='Layer 1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24.73 13.65'%3E%3Cpolyline points='12.37 13.65 0 1.29 1.29 0 12.37 11.08 23.42 0 24.73 1.29 12.37 13.65' style='fill: %23565656'/%3E%3C/svg%3E%0A");
}

.checkbox-slider--a input + span:after {
  background: #d8dce6 !important;
}

.checkbox-slider--a input + span:before {
  box-shadow: none !important;
  background: #f7f7f9 !important;
  border: 1px solid #d8dce6 !important;
}

.checkbox-slider-danger.checkbox-slider--default input:checked + span:after, .checkbox-slider-danger.checkbox-slider--a input:checked + span:after, .checkbox-slider-danger.checkbox-slider--c input:checked + span:after, .checkbox-slider-danger.checkbox-slider--c-weight input:checked + span:after {
  background: #64d091 !important;
}

.checkbox-slider--a.checkbox-slider-md input + span:after {
  font-size: 12px !important;
  letter-spacing: 1.5px;
  text-transform: uppercase;
  font-weight: normal;
}

.select2 {
  width: 100% !important;
}

.select2-container .select2-selection--single,
.select2-container .select2-selection--multiple {
  height: 45px !important;
}

.select2-container--default .select2-selection--single .select2-selection__rendered,
.select2-container--default .select2-selection--multiple .select2-selection__rendered {
  line-height: 45px !important;
}

.select2-container--default .select2-selection--multiple,
.select2-container--default .select2-selection--single {
  border: 1px solid #d8dce6 !important;
  border-radius: 0 !important;
}

.select2-container--default .select2-selection--single .select2-selection__arrow,
.select2-container--default .select2-selection--multiple .select2-selection__arrow {
  height: 45px !important;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
  line-height: 22px;
  background: #f7f7f9 !important;
  border: none !important;
}

.select2-selection__choice__remove {
  color: #d8dce6;
}

table.dataTable.dtr-inline.collapsed > tbody > tr[role=row] > td:first-child:before, table.dataTable.dtr-inline.collapsed > tbody > tr[role=row] > th:first-child:before {
  font-family: "lsm-icons";
  content: "\E92E";
  box-shadow: none !important;
  background-color: #d8dce6 !important;
  width: 18px !important;
  height: 18px !important;
  color: #5e6983 !important;
  line-height: 20px !important;
}

table.dataTable.dtr-inline.collapsed > tbody > tr.parent > td:first-child:before, table.dataTable.dtr-inline.collapsed > tbody > tr.parent > th:first-child:before {
  font-family: "lsm-icons";
  content: "\E935";
  background-color: #d8dce6 !important;
}

.dtr-details {
  width: 100%;
}

.list-group-item {
  margin-bottom: 0.5rem;
  padding: 0.4rem;
}
.list-group-item.active {
  background: #f7f7f9;
  color: #5e6983;
  border: 1px solid #d8dce6;
}
.list-group-item a {
  color: inherit;
}

.icon-sortable {
  cursor: move;
}

.blockUI {
  background-color: #5e6983 !important;
}
.blockUI.blockMsg {
  border-radius: 0.125rem !important;
  padding: 3vw !important;
}
.blockUI.blockMsg h1 {
  font-size: 0px;
}
.blockUI.blockMsg h1:after {
  display: block;
  font-family: "lsm-icons";
  content: "\E98A";
  font-size: 40px !important;
  color: #fff !important;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  100% {
    transform: rotate(360deg);
  }
}
.dataTables_wrapper .dataTables_filter label input {
  border-radius: 0.125rem;
}

.assign-users__list-item__icon {
  display: none;
}

.active .assign-users__list-item__icon {
  display: inline;
}

.default_pagination .pagination .disabled {
  display: none;
}
.default_pagination .pagination a, .default_pagination .pagination span {
  border-radius: 50% !important;
  font-weight: bold;
  width: 3.5rem;
  height: 3.5rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #5e6983;
  transition: color 0.3s ease-in-out;
}
.default_pagination .pagination a:hover, .default_pagination .pagination span:hover {
  text-decoration: none;
  color: rgba(94, 105, 131, 0.4);
}
.default_pagination .pagination .active > * {
  border: 1px solid #d8dce6;
  color: #337ab7;
}
.default_pagination .pagination [rel=prev],
.default_pagination .pagination [rel=next] {
  font-size: 0;
  font-weight: normal;
}
.default_pagination .pagination [rel=prev]:after {
  font-family: "lsm-icons";
  content: "\E92F";
  font-size: 14px;
}
.default_pagination .pagination [rel=next]:after {
  font-family: "lsm-icons";
  content: "\E930";
  font-size: 14px;
}

.fc {
  direction: ltr;
  text-align: left;
}

.fc-rtl {
  text-align: right;
}

body .fc {
  font-size: 1em;
}

.fc-unthemed th, .fc-unthemed td, .fc-unthemed thead, .fc-unthemed tbody, .fc-unthemed .fc-divider, .fc-unthemed .fc-row {
  border-color: #fff;
}
.fc-unthemed .fc-popover {
  background-color: #fff;
}
.fc-unthemed .fc-divider {
  background: #eee;
}
.fc-unthemed .fc-popover .fc-header {
  background: #f7f7f9;
}
.fc-unthemed .fc-popover .fc-header .fc-close {
  background-color: #337ab7;
  color: #fff;
}
.fc-unthemed td {
  border-style: double;
}
.fc-unthemed td.fc-mon, .fc-unthemed td.fc-tue, .fc-unthemed td.fc-wed, .fc-unthemed td.fc-thu, .fc-unthemed td.fc-fri {
  background: #f7f7f9;
}
.fc-unthemed td.fc-sat, .fc-unthemed td.fc-sun {
  background: #d8dce6;
}
.fc-unthemed td.fc-today {
  background-color: #337ab7;
  color: #fff;
}

.fc-highlight {
  background: #bce8f1;
  opacity: 0.3;
  filter: alpha(opacity=30);
}

.fc-bgevent {
  background: #8fdf82;
  opacity: 0.3;
  filter: alpha(opacity=30);
}

.fc-nonbusiness {
  background: #d7d7d7;
}

.fc-icon {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 1em;
  height: 1em;
  line-height: 1em;
  font-size: 1em;
  overflow: hidden;
  font-family: "Courier New", Courier, monospace;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
.fc-icon:after {
  color: #fff;
}

.fc-icon-left-single-arrow:after {
  font-family: "lsm-icons";
  content: "\E92F";
}

.fc-icon-right-single-arrow:after {
  font-family: "lsm-icons";
  content: "\E930";
}

.fc-icon-left-double-arrow:after {
  font-family: "lsm-icons";
  content: "\E932";
}

.fc-icon-right-double-arrow:after {
  font-family: "lsm-icons";
  content: "\E933";
}

.fc-icon-left-triangle:after {
  font-family: "lsm-icons";
  content: "\E92F";
}

.fc-icon-right-triangle:after {
  font-family: "lsm-icons";
  content: "\E930";
}

.fc-icon-down-triangle:after {
  font-family: "lsm-icons";
  content: "\E92E";
}

.fc-icon-x:after {
  font-family: "lsm-icons";
  content: "\EA0B";
}

.fc button {
  box-sizing: border-box;
  margin: 0;
  height: 2.2em;
  padding: 0 0.6em;
  font-size: 1em;
  white-space: nowrap;
  cursor: pointer;
  text-transform: capitalize;
}
.fc button::-moz-focus-inner {
  margin: 0;
  padding: 0;
}

.fc-state-default {
  border: 0;
}
.fc-state-default.fc-corner-left {
  border-top-left-radius: 0.125rem;
  border-bottom-left-radius: 0.125rem;
}
.fc-state-default.fc-corner-right {
  border-top-right-radius: 0.125rem;
  border-bottom-right-radius: 0.125rem;
}

.fc button .fc-icon {
  position: relative;
  top: 1px;
}

.fc-state-default {
  background-color: #337ab7;
  color: #fff;
  outline: none;
}

.fc-state-hover, .fc-state-down, .fc-state-active, .fc-state-disabled {
  background-color: #032862;
  color: #fff;
}

.fc-state-hover {
  color: #fff;
  text-decoration: none;
  transition: background-position 0.1s linear;
}

.fc-state-down, .fc-state-active {
  background-color: #032862;
}

.fc-state-disabled {
  background-color: #dfe1e0;
  color: #999;
  cursor: default;
  opacity: 0.65;
  filter: alpha(opacity=65);
}

.fc-button-group {
  display: inline-block;
}

.fc-loading > table {
  opacity: 0.2;
  filter: alpha(opacity=20);
}

.fc-loading-progress {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  text-align: center;
  z-index: 999;
  font-size: 15em;
}
.fc-loading-progress:before {
  content: "";
  display: inline-block;
  height: 100%;
  vertical-align: middle;
}
.fc-loading-progress i {
  color: #337ab7;
  display: inline-block;
  vertical-align: middle;
}

.fc .fc-button-group > * {
  float: left;
  margin: 0 0 0 -1px;
}
.fc .fc-button-group > :first-child {
  margin-left: 0;
}

.fc-popover {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0 !important;
  left: 0 !important;
}
.fc-popover .fc-header {
  padding: 2px 4px;
}
.fc-popover .fc-header .fc-title {
  margin: 0 2px;
}
.fc-popover .fc-header .fc-close {
  cursor: pointer;
}

.fc-ltr .fc-popover .fc-header .fc-title {
  float: left;
}

.fc-rtl .fc-popover .fc-header .fc-close {
  float: left;
}
.fc-rtl .fc-popover .fc-header .fc-title {
  float: right;
}

.fc-ltr .fc-popover .fc-header .fc-close {
  float: right;
}

.fc-unthemed .fc-popover .fc-header .fc-title {
  color: #616265;
  padding: 0.25em;
}
.fc-unthemed .fc-popover .fc-header .fc-close {
  border-radius: 0.8em;
  font-size: 0.9em;
  line-height: 1.6em;
  margin: 0.25em;
  width: 1.6em;
  height: 1.6em;
}

.fc-popover > .ui-widget-header + .ui-widget-content {
  border-top: 0;
}

.fc-divider {
  border-style: solid;
  border-width: 1px;
}

hr.fc-divider {
  height: 0;
  margin: 0;
  padding: 0 0 2px;
  border-width: 1px 0;
}

.fc-clear {
  clear: both;
}

.fc-bg, .fc-bgevent-skeleton, .fc-highlight-skeleton, .fc-helper-skeleton {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.fc-bg {
  bottom: 0;
}
.fc-bg table {
  height: 100%;
}

.fc table {
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
  border-spacing: 0;
  font-size: 1em;
}
.fc th {
  text-align: right;
  border: 0;
  padding: 0 0.1em;
  vertical-align: top;
}
.fc td {
  border-style: solid;
  border-width: 1px;
  padding: 0;
  vertical-align: top;
}
.fc td.fc-today {
  border-style: double;
}
.fc .fc-row {
  border-style: solid;
  border-width: 0;
}

.fc-row {
  position: relative;
}
.fc-row table {
  border-left: 0 hidden transparent;
  border-right: 0 hidden transparent;
  border-bottom: 0 hidden transparent;
}
.fc-row:first-child table {
  border-top: 0 hidden transparent;
}
.fc-row .fc-bg {
  z-index: 1;
}
.fc-row .fc-bgevent-skeleton, .fc-row .fc-highlight-skeleton {
  bottom: 0;
}
.fc-row .fc-bgevent-skeleton table {
  height: 100%;
}
.fc-row .fc-highlight-skeleton table {
  height: 100%;
}
.fc-row .fc-highlight-skeleton td {
  border-color: transparent;
}
.fc-row .fc-bgevent-skeleton {
  z-index: 2;
}
.fc-row .fc-bgevent-skeleton td {
  border-color: transparent;
}
.fc-row .fc-highlight-skeleton {
  z-index: 3;
}
.fc-row .fc-content-skeleton {
  position: relative;
  z-index: 4;
  padding-bottom: 2px;
}
.fc-row .fc-helper-skeleton {
  z-index: 5;
}
.fc-row .fc-content-skeleton td, .fc-row .fc-helper-skeleton td {
  background: none;
  border-color: transparent;
  border-bottom: 0;
}
.fc-row .fc-content-skeleton tbody td, .fc-row .fc-helper-skeleton tbody td {
  border-top: 0;
}

.fc-scroller {
  overflow-y: scroll;
  overflow-x: hidden;
}
.fc-scroller > * {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.fc-event {
  background-color: #fff;
  position: relative;
  display: block;
  font-size: 11px;
  line-height: 1.2;
  font-weight: normal;
  color: inherit;
  cursor: pointer;
  text-decoration: none;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
.fc-event .fc-content {
  padding: 5px 0 5px 30px;
}
.fc-event:hover {
  text-decoration: none;
}
.fc-event.calendar--re-admin {
  color: #fff;
  background-color: #cc6565;
  border: 1px solid #af4d4d;
}
.fc-event.calendar--holiday {
  color: #fff;
  background-color: #64d091;
  border: 1px solid #4ca872;
}
.fc-event.calendar--survey {
  color: #fff;
  background-color: #ef8c3f;
  border: 1px solid #c37336;
}
.fc-event.calendar--rereview {
  color: #fff;
  background-color: #388695;
  border: 1px solid #83ACB4;
}
.fc-event .fa {
  color: #fff;
  position: absolute;
  left: 8px;
  top: 3px;
  font-style: normal;
  font-size: 14px;
}
.fc-event .fa.fa-plane:before {
  font-family: "lsm-icons";
  content: "\E942";
}
.fc-event .fa.fa-pencil:before {
  font-family: "lsm-icons";
  content: "\E9CB";
}
.fc-event .fa.fa-list-alt:before {
  font-family: "lsm-icons";
  content: "\E9B5";
}
.fc-event .fa.fa-pencil:before {
  font-family: "lsm-icons";
  content: "\E9CB";
}

.ui-widget .fc-event {
  color: #fff;
  text-decoration: none;
}

.fc-not-allowed {
  cursor: not-allowed;
}
.fc-not-allowed .fc-event {
  cursor: not-allowed;
}

.fc-event .fc-bg {
  z-index: 1;
  background: #fff;
  opacity: 0.25;
  filter: alpha(opacity=25);
}
.fc-event .fc-content {
  position: relative;
  z-index: 2;
}
.fc-event .fc-resizer {
  position: absolute;
  z-index: 3;
}

.fc-ltr .fc-h-event.fc-not-start, .fc-rtl .fc-h-event.fc-not-end {
  margin-left: 0;
  border-left-width: 0;
  padding-left: 1px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.fc-ltr .fc-h-event.fc-not-end, .fc-rtl .fc-h-event.fc-not-start {
  margin-right: 0;
  border-right-width: 0;
  padding-right: 1px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.fc-h-event .fc-resizer {
  top: -1px;
  bottom: -1px;
  left: -1px;
  right: -1px;
  width: 5px;
}

.fc-ltr .fc-h-event .fc-start-resizer {
  right: auto;
  cursor: w-resize;
}
.fc-ltr .fc-h-event .fc-start-resizer:before, .fc-ltr .fc-h-event .fc-start-resizer:after {
  right: auto;
  cursor: w-resize;
}

.fc-rtl .fc-h-event .fc-end-resizer {
  right: auto;
  cursor: w-resize;
}
.fc-rtl .fc-h-event .fc-end-resizer:before, .fc-rtl .fc-h-event .fc-end-resizer:after {
  right: auto;
  cursor: w-resize;
}

.fc-ltr .fc-h-event .fc-end-resizer {
  left: auto;
  cursor: e-resize;
}
.fc-ltr .fc-h-event .fc-end-resizer:before, .fc-ltr .fc-h-event .fc-end-resizer:after {
  left: auto;
  cursor: e-resize;
}

.fc-rtl .fc-h-event .fc-start-resizer {
  left: auto;
  cursor: e-resize;
}
.fc-rtl .fc-h-event .fc-start-resizer:before, .fc-rtl .fc-h-event .fc-start-resizer:after {
  left: auto;
  cursor: e-resize;
}

.fc-day-grid-event {
  margin: 0 0.5em 0.25em;
  padding: 0;
}
.fc-day-grid-event .fc-content {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.fc-day-grid-event .fc-time {
  font-weight: bold;
}
.fc-day-grid-event .fc-resizer {
  left: -3px;
  right: -3px;
  width: 7px;
}

.fc-more-cell {
  text-align: right;
}

a.fc-more {
  margin: 0 0.5em;
  font-size: 0.85em;
  cursor: pointer;
  text-decoration: none;
}
a.fc-more:hover {
  text-decoration: underline;
}

.fc-limited {
  display: none;
}

.fc-day-grid .fc-row {
  z-index: 1;
}

.fc-more-popover {
  z-index: 2;
}
.fc-more-popover .fc-event-container {
  border: 1px solid #f7f7f9;
  margin: 0;
  padding: 0.5em 0 0.25em;
  position: absolute;
  top: 2.1em;
  right: 0;
  bottom: 0;
  left: 0;
}

.fc-now-indicator {
  position: absolute;
  border: 0 solid red;
}

.fc-toolbar {
  text-align: center;
  margin-bottom: 1em;
}
.fc-toolbar .fc-left {
  float: left;
}
.fc-toolbar .fc-right {
  float: right;
}
.fc-toolbar .fc-center {
  display: inline-block;
}

.fc .fc-toolbar > * > * {
  float: left;
  margin-left: 0.75em;
}
.fc .fc-toolbar > * > :first-child {
  margin-left: 0;
}

.fc-toolbar h2 {
  color: #032862;
  font-size: 1.5em;
  line-height: 1em;
}
.fc-toolbar button {
  position: relative;
}
.fc-toolbar .fc-state-hover, .fc-toolbar .ui-state-hover {
  z-index: 2;
}
.fc-toolbar .fc-state-down {
  z-index: 3;
}
.fc-toolbar .fc-state-active, .fc-toolbar .ui-state-active {
  z-index: 4;
}
.fc-toolbar button:focus {
  z-index: 5;
}

.fc-prev-button,
.fc-next-button {
  padding: 0;
  width: 2.2em;
}
.fc .fc-prev-button,
.fc .fc-next-button {
  padding: 0;
}

.fc-view-container {
  position: relative;
}
.fc-view-container * {
  box-sizing: content-box;
}
.fc-view-container *:before, .fc-view-container *:after {
  box-sizing: content-box;
}

.fc-view {
  position: relative;
  z-index: 1;
}
.fc-view > table {
  position: relative;
  z-index: 1;
}

.fc-basicWeek-view .fc-content-skeleton, .fc-basicDay-view .fc-content-skeleton {
  padding-top: 1px;
  padding-bottom: 1em;
}

.fc-basic-view .fc-body .fc-row {
  min-height: 4em;
}

.fc-row.fc-rigid {
  overflow: hidden;
}
.fc-row.fc-rigid .fc-content-skeleton {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.fc-basic-view .fc-week-number, .fc-basic-view .fc-day-number {
  padding: 0.5em;
}
.fc-basic-view td.fc-week-number span, .fc-basic-view td.fc-day-number {
  padding: 0.5em;
}
.fc-basic-view .fc-week-number {
  text-align: center;
}
.fc-basic-view .fc-week-number span {
  display: inline-block;
  min-width: 1.25em;
}

.fc-ltr .fc-basic-view .fc-day-number {
  text-align: right;
}

.fc-rtl .fc-basic-view .fc-day-number {
  text-align: left;
}

.fc-day-number.fc-other-month {
  opacity: 0.3;
  filter: alpha(opacity=30);
}

.fc-agenda-view .fc-day-grid {
  position: relative;
  z-index: 2;
}
.fc-agenda-view .fc-day-grid .fc-row {
  min-height: 3em;
}
.fc-agenda-view .fc-day-grid .fc-row .fc-content-skeleton {
  padding-top: 1px;
  padding-bottom: 1em;
}

.fc .fc-axis {
  vertical-align: middle;
  padding: 0 4px;
  white-space: nowrap;
}

.fc-ltr .fc-axis {
  text-align: right;
}

.fc-rtl .fc-axis {
  text-align: left;
}

.ui-widget td.fc-axis {
  font-weight: normal;
}

.fc-time-grid-container {
  position: relative;
  z-index: 1;
}

.fc-time-grid {
  position: relative;
  z-index: 1;
  min-height: 100%;
}
.fc-time-grid table {
  border: 0 hidden transparent;
}
.fc-time-grid > .fc-bg {
  z-index: 1;
}
.fc-time-grid .fc-slats, .fc-time-grid > hr {
  position: relative;
  z-index: 2;
}
.fc-time-grid .fc-content-col {
  position: relative;
}
.fc-time-grid .fc-content-skeleton {
  position: absolute;
  z-index: 3;
  top: 0;
  left: 0;
  right: 0;
}
.fc-time-grid .fc-business-container {
  position: relative;
  z-index: 1;
}
.fc-time-grid .fc-bgevent-container {
  position: relative;
  z-index: 2;
}
.fc-time-grid .fc-highlight-container {
  position: relative;
  z-index: 3;
}
.fc-time-grid .fc-event-container {
  position: relative;
  z-index: 4;
}
.fc-time-grid .fc-now-indicator-line {
  z-index: 5;
}
.fc-time-grid .fc-helper-container {
  position: relative;
  z-index: 6;
}
.fc-time-grid .fc-slats td {
  height: 1.5em;
  border-bottom: 0;
}
.fc-time-grid .fc-slats .fc-minor td {
  border-top-style: dotted;
}
.fc-time-grid .fc-slats .ui-widget-content {
  background: none;
}
.fc-time-grid .fc-highlight-container {
  position: relative;
}
.fc-time-grid .fc-highlight {
  position: absolute;
  left: 0;
  right: 0;
}

.fc-ltr .fc-time-grid .fc-event-container {
  margin: 0 2.5% 0 2px;
}

.fc-rtl .fc-time-grid .fc-event-container {
  margin: 0 2px 0 2.5%;
}

.fc-time-grid .fc-event {
  position: absolute;
  z-index: 1;
}
.fc-time-grid .fc-bgevent {
  position: absolute;
  z-index: 1;
  left: 0;
  right: 0;
}

.fc-v-event.fc-not-start {
  border-top-width: 0;
  padding-top: 1px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.fc-v-event.fc-not-end {
  border-bottom-width: 0;
  padding-bottom: 1px;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.fc-time-grid-event {
  overflow: hidden;
}
.fc-time-grid-event .fc-time, .fc-time-grid-event .fc-title {
  padding: 0 1px;
}
.fc-time-grid-event .fc-time {
  font-size: 0.85em;
  white-space: nowrap;
}
.fc-time-grid-event.fc-short .fc-content {
  white-space: nowrap;
}
.fc-time-grid-event.fc-short .fc-time, .fc-time-grid-event.fc-short .fc-title {
  display: inline-block;
  vertical-align: top;
}
.fc-time-grid-event.fc-short .fc-time span {
  display: none;
}
.fc-time-grid-event.fc-short .fc-time:before {
  content: attr(data-start);
}
.fc-time-grid-event.fc-short .fc-time:after {
  content: "\A0-\A0";
}
.fc-time-grid-event.fc-short .fc-title {
  font-size: 0.85em;
  padding: 0;
}
.fc-time-grid-event .fc-resizer {
  left: 0;
  right: 0;
  bottom: 0;
  height: 8px;
  overflow: hidden;
  line-height: 8px;
  font-size: 11px;
  font-family: monospace;
  text-align: center;
  cursor: s-resize;
}
.fc-time-grid-event .fc-resizer:after {
  content: "=";
}

.fc-time-grid .fc-now-indicator-line {
  border-top-width: 1px;
  left: 0;
  right: 0;
}
.fc-time-grid .fc-now-indicator-arrow {
  margin-top: -5px;
}

.fc-ltr .fc-time-grid .fc-now-indicator-arrow {
  left: 0;
  border-width: 5px 0 5px 6px;
  border-top-color: transparent;
  border-bottom-color: transparent;
}

.fc-rtl .fc-time-grid .fc-now-indicator-arrow {
  right: 0;
  border-width: 5px 6px 5px 0;
  border-top-color: transparent;
  border-bottom-color: transparent;
}

.fc-view-container .fc-day {
  box-sizing: border-box;
}

@media print {
  .fc {
    max-width: 100% !important;
  }

  .fc-event {
    background: #fff !important;
    color: #000 !important;
    page-break-inside: avoid;
  }
  .fc-event .fc-resizer {
    display: none;
  }

  th, td, hr, thead, tbody, .fc-row {
    border-color: #ccc !important;
    background: #fff !important;
  }

  .fc-bg, .fc-bgevent-skeleton, .fc-highlight-skeleton, .fc-helper-skeleton, .fc-bgevent-container, .fc-business-container, .fc-highlight-container, .fc-helper-container {
    display: none;
  }

  .fc tbody .fc-row {
    height: auto !important;
    min-height: 0 !important;
  }
  .fc tbody .fc-row .fc-content-skeleton {
    position: static;
    padding-bottom: 0 !important;
  }
  .fc tbody .fc-row .fc-content-skeleton tbody tr:last-child td {
    padding-bottom: 1em;
  }
  .fc tbody .fc-row .fc-content-skeleton table {
    height: 1em;
  }

  .fc-more-cell, .fc-more {
    display: none !important;
  }

  .fc tr.fc-limited {
    display: table-row !important;
  }
  .fc td.fc-limited {
    display: table-cell !important;
  }

  .fc-popover {
    display: none;
  }

  .fc-time-grid {
    min-height: 0 !important;
  }

  .fc-agenda-view .fc-axis {
    display: none;
  }

  .fc-slats {
    display: none !important;
  }

  .fc-time-grid hr {
    display: none !important;
  }
  .fc-time-grid .fc-content-skeleton {
    position: static;
  }
  .fc-time-grid .fc-content-skeleton table {
    height: 4em;
  }
  .fc-time-grid .fc-event-container {
    margin: 0 !important;
  }
  .fc-time-grid .fc-event {
    position: static !important;
    margin: 3px 2px !important;
  }
  .fc-time-grid .fc-event.fc-not-end {
    border-bottom-width: 1px !important;
  }
  .fc-time-grid .fc-event.fc-not-end:after {
    content: "...";
  }
  .fc-time-grid .fc-event.fc-not-start {
    border-top-width: 1px !important;
  }
  .fc-time-grid .fc-event.fc-not-start:before {
    content: "...";
  }
  .fc-time-grid .fc-event .fc-time {
    white-space: normal !important;
  }
  .fc-time-grid .fc-event .fc-time span {
    display: none;
  }
  .fc-time-grid .fc-event .fc-time:after {
    content: attr(data-full);
  }

  .fc-scroller, .fc-day-grid-container, .fc-time-grid-container {
    overflow: visible !important;
    height: auto !important;
  }

  .fc-row {
    border: 0 !important;
    margin: 0 !important;
  }

  .fc-button-group, .fc button {
    display: none;
  }
}
a.content-type-card {
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
a.content-type-card:hover {
  text-decoration: none;
  border-color: #337ab7;
  box-shadow: 0 5px 5px rgba(0, 0, 0, 0.03);
  color: #5e6983;
}
a.content-type-card:hover .content-type-card-body::after {
  border: 0.0625rem solid #337ab7;
  border-radius: 50%;
  width: 1.875rem;
  height: 1.875rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  justify-content: center;
  align-items: center;
  right: 1.25rem;
}

.content-type-card {
  font-size: 0.625rem;
  border: 1px solid #d8dce6;
  border-radius: 0.125rem;
  font-weight: bold;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  flex-direction: column;
  color: #5e6983;
  margin-bottom: 1.25rem;
}

.content-type-card-body {
  min-height: 86px;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  padding: 1.25rem 5rem 1.25rem 1.25rem;
}
.content-type-card-body::after {
  font-family: "lsm-icons";
  content: "\E912";
  display: block;
  position: absolute;
  right: 1.875rem;
  color: #337ab7;
}

.content-type-card-title {
  font-size: 0.75rem;
  text-transform: uppercase;
  line-height: 0.9375rem;
  margin-bottom: 0;
}

.hero.bg-section-default-light .hero-content {
  color: #002663;
}
.hero.bg-section-default-light .hero-content h2 {
  color: #002663;
}
.hero.bg-section-default-light:before {
  background-image: url("data:image/svg+xml,%3Csvg id='Layer_1' data-name='Layer 1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 514.69 427'%3E%3Ctitle%3Eskew1%3C/title%3E%3Cpath id='Subtraction_11' data-name='Subtraction 11' d='M0,0H353.27L514.69,427H0Z' style='fill: %23F3F6FA;isolation: isolate'/%3E%3C/svg%3E%0A");
}
.hero.bg-section-default-light:after {
  background-image: url("data:image/svg+xml,%3Csvg id='Layer_1' data-name='Layer 1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 612.91 427'%3E%3Ctitle%3Eskew2%3C/title%3E%3Cpath id='Subtraction_12' data-name='Subtraction 12' d='M0,0H612.91L429.41,427H0Z' style='fill: %23F3F6FA;isolation: isolate'/%3E%3C/svg%3E%0A");
}
.hero.bg-section-default-dark .hero-content {
  color: #fff;
}
.hero.bg-section-default-dark .hero-content h2 {
  color: #fff;
}
.hero.bg-section-default-dark:before {
  background-image: url("data:image/svg+xml,%3Csvg id='Layer_1' data-name='Layer 1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 514.69 427'%3E%3Ctitle%3Eskew1%3C/title%3E%3Cpath id='Subtraction_11' data-name='Subtraction 11' d='M0,0H353.27L514.69,427H0Z' style='fill: %236283A8;isolation: isolate'/%3E%3C/svg%3E%0A");
}
.hero.bg-section-default-dark:after {
  background-image: url("data:image/svg+xml,%3Csvg id='Layer_1' data-name='Layer 1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 612.91 427'%3E%3Ctitle%3Eskew2%3C/title%3E%3Cpath id='Subtraction_12' data-name='Subtraction 12' d='M0,0H612.91L429.41,427H0Z' style='fill: %236283A8;isolation: isolate'/%3E%3C/svg%3E%0A");
}
.hero.bg-section-copper-light .hero-content {
  color: #c37336;
}
.hero.bg-section-copper-light .hero-content h2 {
  color: #c37336;
}
.hero.bg-section-copper-light:before {
  background-image: url("data:image/svg+xml,%3Csvg id='Layer_1' data-name='Layer 1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 514.69 427'%3E%3Ctitle%3Eskew1%3C/title%3E%3Cpath id='Subtraction_11' data-name='Subtraction 11' d='M0,0H353.27L514.69,427H0Z' style='fill: %23FAF7F4;isolation: isolate'/%3E%3C/svg%3E%0A");
}
.hero.bg-section-copper-light:after {
  background-image: url("data:image/svg+xml,%3Csvg id='Layer_1' data-name='Layer 1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 612.91 427'%3E%3Ctitle%3Eskew2%3C/title%3E%3Cpath id='Subtraction_12' data-name='Subtraction 12' d='M0,0H612.91L429.41,427H0Z' style='fill: %23FAF7F4;isolation: isolate'/%3E%3C/svg%3E%0A");
}
.hero.bg-section-copper-dark .hero-content {
  color: #fff;
}
.hero.bg-section-copper-dark .hero-content h2 {
  color: #fff;
}
.hero.bg-section-copper-dark:before {
  background-image: url("data:image/svg+xml,%3Csvg id='Layer_1' data-name='Layer 1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 514.69 427'%3E%3Ctitle%3Eskew1%3C/title%3E%3Cpath id='Subtraction_11' data-name='Subtraction 11' d='M0,0H353.27L514.69,427H0Z' style='fill: %23BB8256;isolation: isolate'/%3E%3C/svg%3E%0A");
}
.hero.bg-section-copper-dark:after {
  background-image: url("data:image/svg+xml,%3Csvg id='Layer_1' data-name='Layer 1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 612.91 427'%3E%3Ctitle%3Eskew2%3C/title%3E%3Cpath id='Subtraction_12' data-name='Subtraction 12' d='M0,0H612.91L429.41,427H0Z' style='fill: %23BB8256;isolation: isolate'/%3E%3C/svg%3E%0A");
}
.hero.bg-section-sage-light .hero-content {
  color: #388695;
}
.hero.bg-section-sage-light .hero-content h2 {
  color: #388695;
}
.hero.bg-section-sage-light:before {
  background-image: url("data:image/svg+xml,%3Csvg id='Layer_1' data-name='Layer 1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 514.69 427'%3E%3Ctitle%3Eskew1%3C/title%3E%3Cpath id='Subtraction_11' data-name='Subtraction 11' d='M0,0H353.27L514.69,427H0Z' style='fill: %23F4F6F6;isolation: isolate'/%3E%3C/svg%3E%0A");
}
.hero.bg-section-sage-light:after {
  background-image: url("data:image/svg+xml,%3Csvg id='Layer_1' data-name='Layer 1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 612.91 427'%3E%3Ctitle%3Eskew2%3C/title%3E%3Cpath id='Subtraction_12' data-name='Subtraction 12' d='M0,0H612.91L429.41,427H0Z' style='fill: %23F4F6F6;isolation: isolate'/%3E%3C/svg%3E%0A");
}
.hero.bg-section-sage-dark .hero-content {
  color: #fff;
}
.hero.bg-section-sage-dark .hero-content h2 {
  color: #fff;
}
.hero.bg-section-sage-dark:before {
  background-image: url("data:image/svg+xml,%3Csvg id='Layer_1' data-name='Layer 1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 514.69 427'%3E%3Ctitle%3Eskew1%3C/title%3E%3Cpath id='Subtraction_11' data-name='Subtraction 11' d='M0,0H353.27L514.69,427H0Z' style='fill: %2383ADB4;isolation: isolate'/%3E%3C/svg%3E%0A");
}
.hero.bg-section-sage-dark:after {
  background-image: url("data:image/svg+xml,%3Csvg id='Layer_1' data-name='Layer 1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 612.91 427'%3E%3Ctitle%3Eskew2%3C/title%3E%3Cpath id='Subtraction_12' data-name='Subtraction 12' d='M0,0H612.91L429.41,427H0Z' style='fill: %2383ADB4;isolation: isolate'/%3E%3C/svg%3E%0A");
}
.hero.bg-section-redclay-light .hero-content {
  color: #9A4A4A;
}
.hero.bg-section-redclay-light .hero-content h2 {
  color: #9A4A4A;
}
.hero.bg-section-redclay-light:before {
  background-image: url("data:image/svg+xml,%3Csvg id='Layer_1' data-name='Layer 1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 514.69 427'%3E%3Ctitle%3Eskew1%3C/title%3E%3Cpath id='Subtraction_11' data-name='Subtraction 11' d='M0,0H353.27L514.69,427H0Z' style='fill: %23FCF3F3;isolation: isolate'/%3E%3C/svg%3E%0A");
}
.hero.bg-section-redclay-light:after {
  background-image: url("data:image/svg+xml,%3Csvg id='Layer_1' data-name='Layer 1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 612.91 427'%3E%3Ctitle%3Eskew2%3C/title%3E%3Cpath id='Subtraction_12' data-name='Subtraction 12' d='M0,0H612.91L429.41,427H0Z' style='fill: %23FCF3F3;isolation: isolate'/%3E%3C/svg%3E%0A");
}
.hero.bg-section-redclay-dark .hero-content {
  color: #fff;
}
.hero.bg-section-redclay-dark .hero-content h2 {
  color: #fff;
}
.hero.bg-section-redclay-dark:before {
  background-image: url("data:image/svg+xml,%3Csvg id='Layer_1' data-name='Layer 1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 514.69 427'%3E%3Ctitle%3Eskew1%3C/title%3E%3Cpath id='Subtraction_11' data-name='Subtraction 11' d='M0,0H353.27L514.69,427H0Z' style='fill: %23AC6B6B;isolation: isolate'/%3E%3C/svg%3E%0A");
}
.hero.bg-section-redclay-dark:after {
  background-image: url("data:image/svg+xml,%3Csvg id='Layer_1' data-name='Layer 1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 612.91 427'%3E%3Ctitle%3Eskew2%3C/title%3E%3Cpath id='Subtraction_12' data-name='Subtraction 12' d='M0,0H612.91L429.41,427H0Z' style='fill: %23AC6B6B;isolation: isolate'/%3E%3C/svg%3E%0A");
}
.hero.bg-section-orchid-light .hero-content {
  color: #7A4883;
}
.hero.bg-section-orchid-light .hero-content h2 {
  color: #7A4883;
}
.hero.bg-section-orchid-light:before {
  background-image: url("data:image/svg+xml,%3Csvg id='Layer_1' data-name='Layer 1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 514.69 427'%3E%3Ctitle%3Eskew1%3C/title%3E%3Cpath id='Subtraction_11' data-name='Subtraction 11' d='M0,0H353.27L514.69,427H0Z' style='fill: %23FCF6FD;isolation: isolate'/%3E%3C/svg%3E%0A");
}
.hero.bg-section-orchid-light:after {
  background-image: url("data:image/svg+xml,%3Csvg id='Layer_1' data-name='Layer 1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 612.91 427'%3E%3Ctitle%3Eskew2%3C/title%3E%3Cpath id='Subtraction_12' data-name='Subtraction 12' d='M0,0H612.91L429.41,427H0Z' style='fill: %23FCF6FD;isolation: isolate'/%3E%3C/svg%3E%0A");
}
.hero.bg-section-orchid-dark .hero-content {
  color: #fff;
}
.hero.bg-section-orchid-dark .hero-content h2 {
  color: #fff;
}
.hero.bg-section-orchid-dark:before {
  background-image: url("data:image/svg+xml,%3Csvg id='Layer_1' data-name='Layer 1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 514.69 427'%3E%3Ctitle%3Eskew1%3C/title%3E%3Cpath id='Subtraction_11' data-name='Subtraction 11' d='M0,0H353.27L514.69,427H0Z' style='fill: %239C71A2;isolation: isolate'/%3E%3C/svg%3E%0A");
}
.hero.bg-section-orchid-dark:after {
  background-image: url("data:image/svg+xml,%3Csvg id='Layer_1' data-name='Layer 1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 612.91 427'%3E%3Ctitle%3Eskew2%3C/title%3E%3Cpath id='Subtraction_12' data-name='Subtraction 12' d='M0,0H612.91L429.41,427H0Z' style='fill: %239C71A2;isolation: isolate'/%3E%3C/svg%3E%0A");
}

.dashboard-section.bg-white .dashboard-section-title h1, .dashboard-section#ffgenerated .dashboard-section-title h1,
.dashboard-section.bg-white .dashboard-section-title h2,
.dashboard-section#ffgenerated .dashboard-section-title h2,
.dashboard-section.bg-white .dashboard-section-title h3,
.dashboard-section#ffgenerated .dashboard-section-title h3,
.dashboard-section.bg-white .dashboard-section-title h4,
.dashboard-section#ffgenerated .dashboard-section-title h4,
.dashboard-section.bg-white .dashboard-section-title h5,
.dashboard-section#ffgenerated .dashboard-section-title h5,
.dashboard-section.bg-white .dashboard-section-title h6,
.dashboard-section#ffgenerated .dashboard-section-title h6,
.dashboard-section.bg-white .dashboard-section-title [class*=display-],
.dashboard-section#ffgenerated .dashboard-section-title [class*=display-] {
  color: #002663;
}
.dashboard-section.bg-section-default-light, .dashboard-section.bg-section-default-dark {
  background: #F3F6FA;
}
.dashboard-section.bg-section-default-light .dashboard-section-title h1,
.dashboard-section.bg-section-default-light .dashboard-section-title h2,
.dashboard-section.bg-section-default-light .dashboard-section-title h3,
.dashboard-section.bg-section-default-light .dashboard-section-title h4,
.dashboard-section.bg-section-default-light .dashboard-section-title h5,
.dashboard-section.bg-section-default-light .dashboard-section-title h6,
.dashboard-section.bg-section-default-light .dashboard-section-title [class*=display-], .dashboard-section.bg-section-default-dark .dashboard-section-title h1,
.dashboard-section.bg-section-default-dark .dashboard-section-title h2,
.dashboard-section.bg-section-default-dark .dashboard-section-title h3,
.dashboard-section.bg-section-default-dark .dashboard-section-title h4,
.dashboard-section.bg-section-default-dark .dashboard-section-title h5,
.dashboard-section.bg-section-default-dark .dashboard-section-title h6,
.dashboard-section.bg-section-default-dark .dashboard-section-title [class*=display-] {
  color: #002663;
}
.dashboard-section.bg-section-copper-light, .dashboard-section.bg-section-copper-dark {
  background: #FAF6F3;
}
.dashboard-section.bg-section-copper-light .dashboard-section-title h1,
.dashboard-section.bg-section-copper-light .dashboard-section-title h2,
.dashboard-section.bg-section-copper-light .dashboard-section-title h3,
.dashboard-section.bg-section-copper-light .dashboard-section-title h4,
.dashboard-section.bg-section-copper-light .dashboard-section-title h5,
.dashboard-section.bg-section-copper-light .dashboard-section-title h6,
.dashboard-section.bg-section-copper-light .dashboard-section-title [class*=display-], .dashboard-section.bg-section-copper-dark .dashboard-section-title h1,
.dashboard-section.bg-section-copper-dark .dashboard-section-title h2,
.dashboard-section.bg-section-copper-dark .dashboard-section-title h3,
.dashboard-section.bg-section-copper-dark .dashboard-section-title h4,
.dashboard-section.bg-section-copper-dark .dashboard-section-title h5,
.dashboard-section.bg-section-copper-dark .dashboard-section-title h6,
.dashboard-section.bg-section-copper-dark .dashboard-section-title [class*=display-] {
  color: #c37336;
}
.dashboard-section.bg-section-sage-light, .dashboard-section.bg-section-sage-dark {
  background: #F4F6F6;
}
.dashboard-section.bg-section-sage-light .dashboard-section-title h1,
.dashboard-section.bg-section-sage-light .dashboard-section-title h2,
.dashboard-section.bg-section-sage-light .dashboard-section-title h3,
.dashboard-section.bg-section-sage-light .dashboard-section-title h4,
.dashboard-section.bg-section-sage-light .dashboard-section-title h5,
.dashboard-section.bg-section-sage-light .dashboard-section-title h6,
.dashboard-section.bg-section-sage-light .dashboard-section-title [class*=display-], .dashboard-section.bg-section-sage-dark .dashboard-section-title h1,
.dashboard-section.bg-section-sage-dark .dashboard-section-title h2,
.dashboard-section.bg-section-sage-dark .dashboard-section-title h3,
.dashboard-section.bg-section-sage-dark .dashboard-section-title h4,
.dashboard-section.bg-section-sage-dark .dashboard-section-title h5,
.dashboard-section.bg-section-sage-dark .dashboard-section-title h6,
.dashboard-section.bg-section-sage-dark .dashboard-section-title [class*=display-] {
  color: #388695;
}
.dashboard-section.bg-section-redclay-light, .dashboard-section.bg-section-redclay-dark {
  background: #FCF3F3;
}
.dashboard-section.bg-section-redclay-light .dashboard-section-title h1,
.dashboard-section.bg-section-redclay-light .dashboard-section-title h2,
.dashboard-section.bg-section-redclay-light .dashboard-section-title h3,
.dashboard-section.bg-section-redclay-light .dashboard-section-title h4,
.dashboard-section.bg-section-redclay-light .dashboard-section-title h5,
.dashboard-section.bg-section-redclay-light .dashboard-section-title h6,
.dashboard-section.bg-section-redclay-light .dashboard-section-title [class*=display-], .dashboard-section.bg-section-redclay-dark .dashboard-section-title h1,
.dashboard-section.bg-section-redclay-dark .dashboard-section-title h2,
.dashboard-section.bg-section-redclay-dark .dashboard-section-title h3,
.dashboard-section.bg-section-redclay-dark .dashboard-section-title h4,
.dashboard-section.bg-section-redclay-dark .dashboard-section-title h5,
.dashboard-section.bg-section-redclay-dark .dashboard-section-title h6,
.dashboard-section.bg-section-redclay-dark .dashboard-section-title [class*=display-] {
  color: #9A4A4A;
}
.dashboard-section.bg-section-orchid-light, .dashboard-section.bg-section-orchid-dark {
  background: #FCF6FD;
}
.dashboard-section.bg-section-orchid-light .dashboard-section-title h1,
.dashboard-section.bg-section-orchid-light .dashboard-section-title h2,
.dashboard-section.bg-section-orchid-light .dashboard-section-title h3,
.dashboard-section.bg-section-orchid-light .dashboard-section-title h4,
.dashboard-section.bg-section-orchid-light .dashboard-section-title h5,
.dashboard-section.bg-section-orchid-light .dashboard-section-title h6,
.dashboard-section.bg-section-orchid-light .dashboard-section-title [class*=display-], .dashboard-section.bg-section-orchid-dark .dashboard-section-title h1,
.dashboard-section.bg-section-orchid-dark .dashboard-section-title h2,
.dashboard-section.bg-section-orchid-dark .dashboard-section-title h3,
.dashboard-section.bg-section-orchid-dark .dashboard-section-title h4,
.dashboard-section.bg-section-orchid-dark .dashboard-section-title h5,
.dashboard-section.bg-section-orchid-dark .dashboard-section-title h6,
.dashboard-section.bg-section-orchid-dark .dashboard-section-title [class*=display-] {
  color: #7A4883;
}
@media (max-width: 1199.98px) {
  .dashboard-section.bg-section-blue, .dashboard-section.bg-section-orange, .dashboard-section.bg-section-sage {
    background: none;
  }
}

.dashboard-section {
  padding: 2rem 0 2.5rem;
}

.dashboard-section-title {
  padding: 1.5rem 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}
.dashboard-section-title h1,
.dashboard-section-title h2,
.dashboard-section-title h3,
.dashboard-section-title h4,
.dashboard-section-title h5,
.dashboard-section-title h6 {
  margin-bottom: 0;
  max-width: -webkit-calc(100% - 100px);
  max-width: calc(100% - 100px);
}
@media (max-width: 599.98px) {
  .dashboard-section-title {
    flex-direction: column;
    align-items: flex-start;
  }
}

#announcements .card .card-title {
  font-size: 1rem;
  line-height: 1.2;
  font-weight: 700;
  color: #002663;
  margin-bottom: 0.625rem;
}
#announcements .card .card-text {
  margin: 0.625rem 0 1.5625rem;
}

.service-card {
  border: none;
  border-radius: 0;
  background: #fff;
  width: 100%;
  flex-direction: row;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}
.service-card .inner {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  justify-content: center;
}

.service-card-img {
  position: relative;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 65%;
  width: 100%;
  border-radius: 0;
  width: 45%;
  flex-shrink: 0;
}
.service-card-img:before {
  display: block;
  content: " ";
  width: 100%;
  padding-top: 60%;
}
.service-card-img > .inner {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.service-card-body {
  padding: 1rem 2rem 1rem 1rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  justify-content: center;
  flex-grow: 1;
}

.service-card-title {
  font-size: 1rem;
  line-height: 1.2;
  font-family: "Roboto", sans-serif, serif;
  font-weight: 700;
  color: #002663;
  margin-bottom: 0.625rem;
}

.service-card-text {
  color: #5e6983;
}

.hero {
  padding: 0 0 3rem 0;
  position: relative;
  overflow: hidden;
}
.hero .hero-logo {
  width: 115px;
  height: 115px;
  background: #fff;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  justify-content: center;
  align-items: center;
}
.hero .hero-logo img {
  max-width: 80%;
}
.hero .hero-content {
  font-size: 0.875rem;
  line-height: 1.5;
  margin-top: 2rem;
  width: 100%;
  max-width: 25rem;
  color: #5e6983;
}
.hero .hero-content h1,
.hero .hero-content h2 {
  color: #002663;
  font-weight: 300;
  line-height: 1.2;
  margin-bottom: 10px;
}
.hero .hero-content h1 b,
.hero .hero-content h2 b {
  font-weight: 700;
}
.hero .hero-image {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 70%;
  background-position: center right;
  background-repeat: no-repeat;
  background-size: cover;
  z-index: 1;
}
.hero > * {
  position: relative;
  z-index: 3;
}
.hero:before {
  content: "";
  display: block;
  width: 54%;
  position: absolute;
  top: -1px;
  left: 0;
  z-index: 2;
  background-image: url("data:image/svg+xml,%3Csvg id='Layer_1' data-name='Layer 1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 514.69 427'%3E%3Ctitle%3Eskew1%3C/title%3E%3Cpath id='Subtraction_11' data-name='Subtraction 11' d='M0,0H353.27L514.69,427H0Z' style='fill: %23ffffff;isolation: isolate'/%3E%3C/svg%3E%0A");
  background-size: cover;
  background-repeat: no-repeat;
  opacity: 0.94;
  height: 0;
  padding: 0;
  padding-bottom: -webkit-calc(100% * 268 / 685);
  padding-bottom: calc(100% * 268 / 685);
}
.hero:after {
  content: "";
  display: block;
  width: 56%;
  position: absolute;
  top: -1px;
  left: 0;
  z-index: 2;
  background-image: url("data:image/svg+xml,%3Csvg id='Layer_1' data-name='Layer 1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 612.91 427'%3E%3Ctitle%3Eskew2%3C/title%3E%3Cpath id='Subtraction_12' data-name='Subtraction 12' d='M0,0H612.91L429.41,427H0Z' style='fill: %23ffffff;isolation: isolate'/%3E%3C/svg%3E%0A");
  background-size: cover;
  background-repeat: no-repeat;
  opacity: 0.94;
  height: 0;
  padding: 0;
  padding-bottom: -webkit-calc(100% * 268 / 685);
  padding-bottom: calc(100% * 268 / 685);
}
@media (min-width: 600px) {
  .hero {
    min-height: 460px;
  }
}
@media (max-width: 979.98px) {
  .hero {
    background-image: none !important;
    padding: 0 5vw 5vw;
  }
}
@media (max-width: 1579.98px) {
  .hero:before {
    min-width: 600px;
  }
  .hero:after {
    min-width: 570px;
  }
}

.card-featured .card-title {
  color: #002663;
}

.card-list-item .card-title {
  color: #002663;
}

footer {
  font-size: 10px;
  font-family: "Roboto", sans-serif, serif;
  line-height: 1.7;
}
footer ul {
  font-size: 12px;
  font-family: "Roboto", sans-serif, serif;
}
footer ul li a {
  color: #5e6983;
  font-weight: bold;
}

.selectric {
  min-width: 120px;
}
.selectric .button::after {
  position: relative;
}
.selectric-open .selectric-items:before {
  top: 0;
}

.form-container.no-footer .form-body {
  margin-bottom: auto;
  padding: 1.875rem;
}
.form-container .form-body {
  width: 100%;
}
.form-container .setup-fontsize-16px p, .form-container .setup-fontsize-16px li, .form-container .setup-fontsize-16px a, .form-container .setup-fontsize-16px button {
  font-size: 16px !important;
  line-height: 24px;
}
.form-container .setup-fontsize-16px .small-setup-text {
  font-size: 14px !important;
  line-height: 21px;
}
.form-container .setup-fontsize-16px .form-footer .btn, .form-container .setup-fontsize-16px .form-footer #ffFormCreate #ffForm div ul li a, #ffFormCreate #ffForm div ul li .form-container .setup-fontsize-16px .form-footer a {
  font-size: 18px;
  font-weight: 700;
  line-height: 24px;
}
.form-container .setup-fontsize-16px .form-footer .continue-button {
  font-size: 16px;
  line-height: 24px;
  font-weight: bold;
}
.form-container .setup-fontsize-16px .alert-heading {
  font-size: 18px;
}

#ffgenerated {
  border: none !important;
}
#ffgenerated p {
  margin: 0 0 1rem !important;
}
#ffgenerated h2.ffheader {
  font-weight: 700 !important;
  margin: 1.5rem 0 !important;
}
#ffgenerated h2.ffheader + hr {
  border-top: 1px solid #d8dce6;
  margin-bottom: 2rem;
}
#ffgenerated .radio-inline {
  margin-right: 0.625rem;
}
.btn.btn-icon.disabled, #ffFormCreate #ffForm div ul li a.btn-icon.disabled {
  background: none !important;
  border: 0 !important;
}

#two-factor-text {
  font-size: 1rem;
}

.kanban {
  clear: both;
  position: relative;
  padding: 0;
}

.custom--kanban--filters {
  background-color: #2e4064;
  color: #fff;
  margin-bottom: 15px;
  padding: 10px;
  z-index: 10;
}

.kanban--filters {
  background-color: #fff;
  color: #5e6983;
  margin: 0;
  padding: 10px;
  width: 100%;
  z-index: 10;
}

.kanban--filters-dropdown {
  clear: both;
}
.kanban--filters-dropdown.in {
  padding-bottom: 12px;
}
.kanban--filters-dropdown form {
  border-top: 2px solid #4285b9;
  padding: 20px 10px 0;
  position: relative;
  top: 12px;
}

.kanban--filter-toggle {
  background-color: transparent;
  border: 0;
  cursor: pointer;
  font-size: 2.2em;
  line-height: 1em;
  outline: none;
}
.kanban--filter-toggle:active {
  color: #4285b9;
}

.kanban--reset-filters {
  color: #5cb85c;
}
.kanban--reset-filters:active, .kanban--reset-filters:hover {
  color: inherit;
}

.kanban--type-acronym {
  background-color: #337ab7;
  border-radius: 100%;
  color: #fff;
  display: inline-block;
  font-size: 1.2em;
  height: 2em;
  line-height: 2em;
  text-align: center;
  width: 2em;
}

.kanban--board {
  clear: both;
  overflow-x: auto;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
  position: relative;
  white-space: nowrap;
  height: 650px;
}
.kanban--board::-webkit-scrollbar,
.kanban--board ::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}
.kanban--board::-webkit-scrollbar-button,
.kanban--board ::-webkit-scrollbar-button {
  width: 0px;
  height: 0px;
}
.kanban--board::-webkit-scrollbar-thumb,
.kanban--board ::-webkit-scrollbar-thumb {
  border: 0;
  border-radius: 5px;
}
.kanban--board::-webkit-scrollbar-track,
.kanban--board ::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border: 0;
  border-radius: 10px;
}
.kanban--board::-webkit-scrollbar-corner,
.kanban--board ::-webkit-scrollbar-corner {
  background: transparent;
}

.kanban--columns {
  font-size: 0;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.kanban--column {
  font-size: 12px;
  background-color: #f7f7f9;
  display: inline-block;
  height: 100%;
  width: 25%;
  padding: 0;
  position: relative;
  vertical-align: top;
  margin: 0 1px 0 0;
}
.kanban--column:last-child {
  margin: 0;
}
.kanban--column:nth-child(odd) {
  background-color: #fff;
}
.kanban--column > * {
  white-space: normal;
}
.kanban--column h2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #032862;
  text-align: center;
  text-transform: uppercase;
  font-size: 13px;
  padding: 10px;
  width: 100%;
  height: 64px;
}
.kanban--column h2 > span {
  letter-spacing: 1.5px;
}

.kanban--cards {
  position: absolute;
  top: 74px;
  right: 10px;
  bottom: 10px;
  left: 10px;
  overflow-x: hidden;
  overflow-y: scroll;
}
.kanban--cards a {
  color: inherit;
  display: block;
}
.kanban--cards a:hover {
  text-decoration: none;
}
.kanban--cards a:hover, .kanban--cards a:active {
  color: inherit;
}

.kanban--card {
  background-color: #fff;
  border: 1px solid #d8dce6;
  border-radius: 0.125rem;
  margin: 10px;
  padding: 10px;
}
.kanban--card:first-child {
  margin: 0 10px 10px;
}
.kanban--card:last-child {
  margin: 10px 10px 0;
}
.kanban--card dl dt {
  clear: left;
  display: block;
  float: left;
  font-weight: 700;
  width: 50%;
}
.kanban--card dl dd {
  display: block;
  float: left;
  width: 50%;
}
.kanban--card dl:before, .kanban--card dl:after {
  content: " ";
  display: table;
  clear: both;
}
.kanban--card dl:after {
  clear: both;
}

.kanban--card_title {
  font-weight: 700;
  font-size: 14px;
  color: #002663;
  padding-bottom: 1rem;
}

.kanban--surveys::-webkit-scrollbar-thumb,
.kanban--surveys ::-webkit-scrollbar-thumb {
  background: #337ab7;
}
.kanban--surveys .kanban--column h2 {
  color: #fff;
  background-color: #337ab7;
}

.kanban--risk-recommendations::-webkit-scrollbar-thumb,
.kanban--risk-recommendations ::-webkit-scrollbar-thumb {
  background: #9A4A4A;
}
.kanban--risk-recommendations .kanban--column h2 {
  color: #fff;
  background-color: #9A4A4A;
}

.learning p {
  margin-bottom: 20px;
}
.learning p:last-of-type {
  margin-bottom: 20px;
}
.learning .table-controls__date-filters {
  text-align: left;
  margin-bottom: 0;
}
.learning .lesson-list__btns {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}
.learning .lesson-list__btns button {
  border: 0;
  background-color: transparent;
  outline: none;
  padding: 0;
}
.learning .add-content {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  flex-wrap: wrap;
}
.learning .add-content__item {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  flex-grow: 1;
}
.learning .add-content__item-button {
  padding: 1.75rem !important;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  align-items: center;
  justify-content: center;
}
.learning .add-content__item-icon {
  margin-bottom: 0.5rem;
}
.learning .page-list__item {
  margin-bottom: 0.75rem;
}
.learning .btn.btn-default:not(.js--table--open-child), .learning #ffFormCreate #ffForm div ul li a.btn-default:not(.js--table--open-child), #ffFormCreate #ffForm div ul li .learning a.btn-default:not(.js--table--open-child) {
  background-color: #fff;
  border-color: #d8dce6;
  padding: 5px 15px;
  min-width: unset !important;
}
.learning .page-builder__item-title > .icon, .learning #ffFormCreate .page-builder__item-title > .fa, #ffFormCreate .learning .page-builder__item-title > .fa {
  margin: 0 0 0 1rem;
  line-height: 1.75;
}
.learning .page-builder__item-title > .icon .icon, .learning #ffFormCreate .page-builder__item-title > .fa .icon, #ffFormCreate .learning .page-builder__item-title > .fa .icon, .learning .page-builder__item-title > .icon #ffFormCreate .fa, #ffFormCreate .learning .page-builder__item-title > .icon .fa, .learning #ffFormCreate .page-builder__item-title > .fa .fa, #ffFormCreate .learning .page-builder__item-title > .fa .fa {
  margin: 0;
}
.learning .page-jumbo-header:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: transparent !important;
}

.page--lms {
  padding-left: 0;
  padding-right: 0;
}
.page--lms h2.page-jumbo-header__title {
  font-size: 1.4375rem;
  line-height: 1.625rem;
  font-family: inherit;
  color: #fff;
}
.page--lms .card-link .card-meta {
  padding-left: 0;
  padding-right: 0.9375rem;
}
.page--lms .card-link .card-meta::before {
  display: none;
}
.page--lms .library__section.library__section--highlight {
  background-color: #f4f8fc;
}
.page--lms .library__section-header--button {
  padding: 0 !important;
}
.page--lms .page-jumbo-header {
  text-align: left;
}
.page--lms .page-jumbo-header--has-description {
  padding: 2.5rem 1.5rem 1.25rem;
}
@media (min-width: 600px) {
  .page--lms .course-view__header {
    padding: 5rem 3rem 2.5rem;
  }
}
.page--lms h2 {
  color: #002663;
}
.page--lms a.card-img {
  text-decoration: none;
}
.page--lms .page-builder-view__page-nav {
  text-align: left;
}
.page--lms .page-builder-view__page-nav .btn--back,
.page--lms .page-builder-view__page-nav .btn--next {
  position: relative;
}
.page--lms .page-builder-view__page-nav .pagination .active a {
  color: #337ab7;
  background-color: inherit;
  border: 1px solid #d8dce6;
}
.page--lms .page-builder-view__page-item__question-texts {
  color: #002663;
  font-size: 21px;
  line-height: 26px;
}
.page--lms .page-builder-view__page-item__select-text {
  color: #5e6983;
  margin-bottom: 1.5rem;
}
.page--lms .page-builder-view__page-item__help-text {
  font-size: 16px;
  line-height: 25px;
  color: #5e6983;
}
.page--lms .card-img .header-progress {
  position: relative;
  top: -2rem;
  margin-left: 2rem;
  margin-right: 2rem;
}
.page--lms .card-img .library-item__image-icon {
  position: absolute;
  bottom: 2rem;
  left: 2rem;
  font-weight: bold;
  color: #fff;
}
.page--lms .icon-sortable,
.page--lms .icon-delete,
.page--lms .icon-duplicate,
.page--lms .icon-edit {
  opacity: 1;
}
.page--lms .document-downloads__item-btn {
  color: #fff !important;
}
.page--lms ul.page-builder-view__progress {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}
.page--lms ul.page-builder-view__progress li.active a {
  background-color: #64d091;
}
.page--lms ul.page-builder-view__progress li:not(.active) a {
  border: 1px solid #d8dce6;
  border-radius: 6.25rem;
}
.page--lms ul.page-builder-view__progress li:not(:last-of-type) a {
  margin-right: 0.75rem;
}
.page--lms ul.page-builder-view__progress li:last-of-type a {
  padding: 0 1.875rem;
}
.page--lms ul.page-builder-view__progress li a {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 2.625rem;
  height: 2.625rem;
  border-radius: 50%;
  align-items: center;
  justify-content: center;
  padding: 0 !important;
}
.page--lms .header-progress {
  text-align: left;
}
.page--lms .header-progress > span {
  font-weight: bold;
  margin-bottom: 1rem;
  display: block;
  color: #fff;
}
.page--lms .header-progress .progress {
  background-color: rgba(255, 255, 255, 0.2);
  height: 2px;
  border: 0;
}
.page--lms .header-progress .progress .progress-bar {
  background-color: #fff;
}
.page--lms .course-view__description {
  font-size: 1rem;
  line-height: 1.4375rem;
}
.page--lms h3 {
  color: #002663;
}
.page--lms h3.course-section__body-title {
  font-size: 1.3125rem;
  line-height: 1.625rem;
  font-family: inherit;
  margin: 0 0 0.5rem;
}
.page--lms .award {
  background: url("/img/learning/award.svg");
}
.page--lms .panel--grey {
  background-color: #f4f8fc;
}
.page--lms .course-section__panel {
  border-radius: 0.125rem;
}
.page--lms .course-section__panel .panel-heading {
  padding: 0.9375rem 2.1875rem;
}
.page--lms .course-section__panel .panel-heading::after {
  font-family: "lsm-icons";
  content: "\E923";
  font-size: 1.125rem;
  background: unset;
  color: #2e6da4;
  top: 1.125rem;
  margin-right: 0.125rem;
}
.page--lms .course-section__panel .panel-body {
  padding: 3.4375rem 2.1875rem 2.1875rem;
}
.page--lms .course-section__panel .panel-body {
  background-color: #fff;
}
.page--lms .course-section__panel {
  box-shadow: none !important;
  border: 1px solid #d8dce6;
}
.page--lms .course-view__section:first-of-type::before {
  display: none;
}
.page--lms .course-view__section h4 {
  font-size: 0.875rem;
  line-height: 1.625rem;
}
.page--lms .course-view__section.course-section--complete h4 {
  color: #64d091;
}
.page--lms .course-view__section.course-section--pending h4 {
  color: #5e6983;
}
.page--lms .course-view__section.course-section--pending .heading-progress-bar {
  display: block;
  position: absolute;
  background: #2e6da4;
  height: 2px;
  left: 0;
  bottom: 0;
}
.page--lms .panel-primary > .panel-heading {
  background-color: #f4f8fc;
  border-color: #f4f8fc;
}

.login-page .hero p,
.login-page .hero a {
  font-size: 0.75rem;
}
.login-page .hero a {
  color: #002663;
}
.login-page .hero .hero-content {
  max-width: none;
}
@media (min-width: 600px) and (max-width: 979.98px) {
  .login-page .hero:before {
    min-width: 80%;
  }
  .login-page .hero:after {
    min-width: 85%;
  }
}
@media (max-width: 599.98px) {
  .login-page .hero:before {
    min-width: 100%;
  }
  .login-page .hero:after {
    min-width: 110%;
  }
}
.login-page .login-video {
  position: relative;
  background-repeat: no-repeat;
  background-size: 60%;
}
.login-page .login-video:before, .login-page .login-video:after {
  content: "";
  position: absolute;
  z-index: 2;
}
.login-page .login-video:before {
  width: 23%;
  height: 100%;
  background-image: url("/img/login-video-1.png");
  background-position: right center;
  background-repeat: no-repeat;
  background-size: cover;
  top: 0;
  left: 0;
}
.login-page .login-video:after {
  width: 52%;
  height: 100%;
  background-image: url("/img/login-video-2.png");
  background-position: right center;
  background-repeat: no-repeat;
  background-size: cover;
  top: 0;
  right: 0;
  min-width: 650px;
}
.login-page .login-video-content {
  position: relative;
  z-index: 3;
  color: #fff;
}
@media (max-width: 979.98px) {
  .login-page .login-video {
    background: rgba(56, 134, 149, 0.7) !important;
  }
  .login-page .login-video:before, .login-page .login-video:after {
    display: none;
  }
}
.login-page .alert-warning {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  flex-direction: row;
  color: #5e6983;
  align-items: start;
  background-color: #f9edef;
  border-color: #cb5960;
  border-radius: 0.25rem;
}
.login-page .alert-warning p {
  font-size: 1rem;
}
.login-page .alert-warning i {
  color: #cb5960;
}
.login-page .alert-warning .error-link {
  font-size: 1rem;
  color: #388695;
  margin: 0;
  padding: 0;
  text-decoration: underline;
}

.messenger__body-context {
  padding: 0 !important;
}

.messenger__message-list {
  list-style-type: none;
  padding: 0;
}
.messenger__message-list .message-list__item {
  padding: 0 20px !important;
}
.messenger__message-list .message-list__item-author-name {
  font-weight: bold;
  color: #002663;
}
.messenger__message-list .message-list__item--left .message-list__item-message-wrapper {
  background: #f9f6f3;
}
.messenger__message-list .message-list__item--left .message-list__item-message-wrapper:before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'%3E%3Cpath fill='%23f9f6f3' d='M30 22.9c-3 3-22.3 5.9-22.3 5.9S15 25 22.4 15.5C29.7 6 30 0 30 0'/%3E%3C/svg%3E%0A");
}
.messenger__message-list .message-list__item--right .message-list__item-message-wrapper {
  background: #00AEEF;
}
.messenger__message-list .message-list__item--right .message-list__item-message-wrapper:before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'%3E%3Cpath fill='%2300AEEF' d='M0 22.9c3 3 22.3 5.9 22.3 5.9S15 25 7.6 15.5C.3 6 0 0 0 0'/%3E%3C/svg%3E%0A");
}

.modal--messenger .bootbox-close-button {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff !important;
  width: 3.375rem !important;
  height: 3.375rem !important;
  border-radius: 50% !important;
  position: fixed !important;
  top: 2.8125rem !important;
  right: 2.8125rem !important;
  font-size: 0px !important;
  pointer-events: all;
  cursor: pointer;
}
.modal--messenger .bootbox-close-button:after {
  font-size: 18px !important;
  background: none !important;
  color: #002663 !important;
  font-family: "lsm-icons" !important;
  content: "\EA0B" !important;
  position: unset !important;
}
.modal--messenger .bootbox-close-button:focus {
  box-shadow: none;
  outline: none;
}

.modal.bootbox .modal-header {
  padding: 3.125rem 3.125rem 0;
}
.modal.bootbox .modal-title {
  margin-bottom: 0;
}
.modal.bootbox .modal-footer {
  padding: 0 3.125rem 3.125rem;
  justify-content: space-between;
}
.modal.bootbox .modal-footer .btn, .modal.bootbox .modal-footer #ffFormCreate #ffForm div ul li a, #ffFormCreate #ffForm div ul li .modal.bootbox .modal-footer a {
  flex-grow: 1;
}
.modal.bootbox button.close {
  padding: 18px 0;
  background-color: transparent;
  border: 0;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  position: relative;
  top: -30px;
  right: -30px;
}

.panel {
  border-color: #d8dce6;
  margin-bottom: 20px;
  background-color: #fff;
  border: 1px solid transparent;
  border-radius: 2px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
  background-color: white;
}
.panel.panel-default {
  border-color: #d8dce6;
}
.panel.panel-default > .panel-heading {
  background-color: #f4f8fc;
  border-color: #f4f8fc;
}
.panel .panel-title {
  margin: 0 !important;
  color: #555 !important;
  font-size: 17px;
  font-weight: 700;
}
.panel .panel-heading {
  background-color: #f4f8fc;
  border-bottom: 1px solid #f4f8fc;
  padding: 0.9375rem 2.1875rem;
  border-top-right-radius: 2px;
  border-top-left-radius: 2px;
}
.panel .panel-body {
  padding: 2.1875rem;
}

.survey-date p {
  color: #002663;
  text-transform: uppercase;
  letter-spacing: 1.2px;
  font-weight: 700;
  text-align: center;
  padding-bottom: 0.625rem;
}
.survey-date span {
  font-size: 2.75rem;
  line-height: 1;
  color: #337ab7;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 5.9375rem;
  height: 5.9375rem;
  border: 1px solid #d8dce6;
  border-radius: 50%;
}

.survey-delete {
  margin: 10px 0px 10px 0px;
  width: 100%;
}
.survey-delete a {
  width: 100%;
}

table .hidden {
  display: none;
}
table .shown {
  display: table-row;
}
table.firstBold th,
table.firstBold td {
  vertical-align: top;
}
table.dataTable thead .sorting:before, table.dataTable thead .sorting:after,
table.dataTable thead .sorting_asc:before,
table.dataTable thead .sorting_asc:after,
table.dataTable thead .sorting_desc:before,
table.dataTable thead .sorting_desc:after {
  content: "" !important;
}
table.dataTable thead th {
  font-size: 0.875rem;
}
table.table th,
table.table td {
  padding: 0.75rem 1rem;
}

.dataTables_wrapper > .row, .learning .form-horizontal .dataTables_wrapper > .form-group {
  align-items: center;
}
.dataTables_wrapper .dataTables_filter {
  text-align: left !important;
}
.dataTables_wrapper .dataTables_filter label {
  position: relative;
}
.dataTables_wrapper .dataTables_filter label input {
  margin-left: 0 !important;
  min-width: 20vw;
}
.dataTables_wrapper .dataTables_filter label:after {
  font-family: "lsm-icons";
  content: "\E9C8";
  position: absolute;
  right: 0.5rem;
  top: 0.75rem;
}
.dataTables_wrapper .dataTables_length label {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.dataTables_wrapper .dataTables_length .selectric-wrapper {
  margin: 0 0.75rem;
}
.dataTables_wrapper .dataTables_length .selectric-wrapper.selectric-open .selectric-items:before {
  display: none;
}
.dataTables_wrapper .dataTables_paginate {
  margin-top: 3.25rem !important;
}
.dataTables_wrapper .dataTables_paginate .pagination {
  justify-content: flex-start !important;
}
.dataTables_wrapper .dataTables_paginate .pagination .previous .page-link,
.dataTables_wrapper .dataTables_paginate .pagination .next .page-link {
  width: auto !important;
}
.dataTables_wrapper .dataTables_info {
  font-size: 0.625rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  justify-content: flex-end;
}

.assign-users__list .dataTables_filter {
  display: none;
}

body {
  -ms-text-size-adjust: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
}

p:last-of-type {
  margin-bottom: 0;
}

.row.sm-gutters, .learning .form-horizontal .sm-gutters.form-group {
  margin-left: -webkit-calc(1.25rem / -2);
  margin-left: calc(1.25rem / -2);
  margin-right: -webkit-calc(1.25rem / -2);
  margin-right: calc(1.25rem / -2);
}
.row.sm-gutters > [class*=col-], .learning .form-horizontal .sm-gutters.form-group > [class*=col-] {
  padding-left: -webkit-calc(1.25rem / 2);
  padding-left: calc(1.25rem / 2);
  padding-right: -webkit-calc(1.25rem / 2);
  padding-right: calc(1.25rem / 2);
}

label.disabled {
  color: #c1c1c1;
}

span.label-circle {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 12px;
  margin-right: 10px;
}

span.label-circle.color1 {
  background-color: #fc0d1b;
}

span.label-circle.color2 {
  background-color: #fdbf2d;
}

span.label-circle.color3 {
  background-color: #fffd38;
}

span.label-circle.color4 {
  background-color: #00b050;
}

span.label-circle.color5 {
  background-color: #0070c0;
}

span.label-circle.color6 {
  background-color: #dddddd;
}

.text-white {
  color: #ffffff;
}