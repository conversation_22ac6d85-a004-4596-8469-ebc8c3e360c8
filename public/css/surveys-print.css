@page { 
  background-repeat: no-repeat;
  margin: 60px 50px 80px 50px !important; 
}

table, table thead, table thead tr, table tbody, table tbody tr {
  width: 99% !important;
  
} 

tr {
    /*page-break-inside: avoid !important;*/
}

table {
  margin-bottom: 20px;
  border-collapse: unset;
  page-break-inside: avoid !important;
}

table.rr_table {
  page-break-inside: auto !important;
}

body.formsubmissions-showpdf div#page{
    width: 100%;
    margin: 0 auto;
}


body{
    /*background-image: url("/images/print_header.jpg");
    background-repeat: no-repeat;
    background-position: left top;
    background-attachment: fixed;
    background-size: 720px 124px;*/
}

thead:before, thead:after { display: none; }
tbody:before, tbody:after { display: none; }
.col-md-2 {
  width: 16.66%;
  padding-left: 0px !important;
  padding-right: 0px !important;
}
.col-md-offset-10 {
  margin-left: 83.33%;
}
.btn {
  display: inline-block;
  padding: 6px 12px;
  margin-bottom: 0;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.42857143;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  -ms-touch-action: manipulation;
  touch-action: manipulation;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-image: none;
  border: 1px solid transparent;
  border-radius: 0px;
}

input {
  border: 0 0;
  border-color: #eeeeee;
  box-shadow: 0 0;
}

.btn-primary {
  color: #fff;
  background-color: #337ab7;
  border-color: #2e6da4;
}
h1{
  font-size: 25px !important;
  font-weight: bold;
}
