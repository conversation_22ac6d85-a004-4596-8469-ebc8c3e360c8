.modal--messenger .modal-body {
  padding: 0; }

.modal--messenger .bootbox-close-button {
  font-size: 30px;
  width: 1em;
  height: 1em;
  border-radius: 50%;
  background-color: #032862;
  color: #FFF;
  opacity: 1;
  right: -0.5em;
  top: -0.5em;
  position: absolute;
  margin-top: 0 !important;
  font-weight: 100;
  position: relative; }
  .modal--messenger .bootbox-close-button:after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: url(/img/icon-close.svg) 0 0 no-repeat;
    background-size: cover; }

.plupload_wrapper * {
  box-sizing: border-box !important;
}

.plupload_view_list div.plupload_file_size, 
.plupload_view_list div.plupload_file_status, 
.plupload_view_list div.plupload_file_action {
  padding: 8px 0;
}

.messenger__header {
  background-color: #42b7a7;
  color: #FFF;
  padding: 10px;
  text-transform: uppercase; }

.messenger__title:nth-child(n) {
  margin-bottom: 0;
  font-size: 18px; }

.messenger__body {
  background-color: #e7edf2;
  overflow: hidden; }

.messenger__body-context {
  padding: 10px;
  height: 100%; }

.messenger__footer {
  padding: 25px;
  background-color: #d2d8e2; }

textarea.messenger__input-field {
  font-size: 14px;
  border-radius: 6px;
  line-height: 1.428571429;
  color: #333;
  padding: 10px;
  border-radius: 6px !important;
  margin-bottom: 10px;
  height: 60.000000012px; }

.messenger__input-options .checkbox {
  margin: 0 0 5px;
  font-size: 14px; }

.messenger__input-options-fields__content {
  overflow: hidden;
  margin-top: 10px; }

.messenger__input-options-title {
  text-transform: uppercase;
  margin: 20px 0 0;
  cursor: pointer;
  line-height: 1em; }

.messenger__input-options-fields__colleagues {
  margin-bottom: 0; }
  .messenger__input-options-fields__colleagues .messenger__input-options-title {
    margin-top: 20px; }

.messenger__input-plupload {
  margin: 20px 0; }
  .messenger__input-plupload .ui-widget-header th,
  .messenger__input-plupload .ui-widget-header td {
    color: #777777;
    vertical-align: middle; }
  .messenger__input-plupload .plupload_wrapper {
    min-width: 100px;
    font-family: inherit;
    font-size: 14px; }
  .messenger__input-plupload .plupload_container {
    min-height: 200px; }
  .messenger__input-plupload .plupload_header_title {
    font-size: 16px;
    font-family: inherit; }
  .messenger__input-plupload .plupload_message {
    height: auto; }
  .messenger__input-plupload .plupload_logo,
  .messenger__input-plupload .plupload_header_content {
    display: none; }
  .messenger__input-plupload .ui-state-default,
  .messenger__input-plupload .ui-widget-content .ui-state-default,
  .messenger__input-plupload .ui-widget-header .ui-state-default,
  .messenger__input-plupload .ui-widget-header,
  .messenger__input-plupload .ui-widget-content {
    background-image: none; }
  .messenger__input-plupload .plupload_content {
    /* top: 28px;
    bottom: 38px;  */
    position: static !important;
    max-height: 110px;
    overflow: auto;
  }
  .messenger__input-plupload .plupload_droptext {
    line-height: 134px;
    text-transform: uppercase;
    color: #777777; }
  .messenger__input-plupload.file-added .plupload_droptext {
    opacity: 0; }
  .messenger__input-plupload .ui-widget-header {
    font-weight: normal;
    border: 0;
    background-color: #eeeeee; }
  .messenger__input-plupload .ui-widget-content {
    border: 0; }
  .messenger__input-plupload .ui-state-active,
  .messenger__input-plupload .ui-state-hover {
    border-width: 0; }
  .messenger__input-plupload .ui-state-active.btn-primary,
  .messenger__input-plupload .ui-state-hover.btn-primary {
    background: #337ab7;
    border-width: 1px; }
  .messenger__input-plupload .plupload_button {
    color: #FFF !important; }
  .messenger__input-plupload .plupload_start {
    display: none; }

.message-list:before, .message-list:after {
  content: " ";
  display: table; }

.message-list:after {
  clear: both; }

.message-list__item {
  position: relative;
  min-height: 1px;
  padding-left: 15px;
  padding-right: 15px;
  clear: both;
  margin-bottom: 20px; }
  @media (min-width: 992px) {
    .message-list__item {
      float: left;
      width: 75%; } }

.message-list__item-author,
.message-list__item-time-date,
.message-list__item-file {
  font-size: 12px; }
  .message-list__item-author,
  .message-list__item-author a,
  .message-list__item-time-date,
  .message-list__item-time-date a,
  .message-list__item-file,
  .message-list__item-file a {
    color: #777777; 
    word-wrap: break-word; }

.message-list__item-author {
  margin: 0 0 3px 6px; }

.message-list__item-author-name {
  color: #2b2b2b; }

.message-list__item-author-role:before {
  content: '- '; }

.message-list__item-message-wrapper {
  background-color: #FFF;
  color: #333;
  padding: 10px;
  border-radius: 6px;
  padding-bottom: 3em;
  position: relative;
  font-size: 14px; }
  .message-list__item-message-wrapper .message-list__item-message {
    line-height: 1.4em; }
    .message-list__item-message-wrapper .message-list__item-message p {
      margin-bottom: 1em; }
      .message-list__item-message-wrapper .message-list__item-message p:last-child {
        margin-bottom: 0; }
  .message-list__item-message-wrapper:before {
    content: '';
    background: url(/img/speech-mark.svg) 0 0 no-repeat;
    position: absolute;
    font-size: 30px;
    left: -1em;
    bottom: 0;
    height: 1em;
    width: 1em; }

.message-list__item-footer {
  margin-top: 10px; }

.message-list__item-icon {
  border-radius: 50%;
  height: 1em;
  width: 1em;
  box-sizing: content-box;
  padding: 0.5em;
  line-height: 1em; }
  .message-list__item-icon i {
    line-height: 1em; }
  .message-list__item-icon.message-list__item-icon--grey {
    background-color: #9b9b9b;
    border-color: #9b9b9b; }

.message-list__item-files {
  margin-top: 1em; }

.message-list__item-time-date {
  text-align: right;
  margin: 3px 6px 0 0; }

.messenger__input-attach {
  position: relative;
  display: inline-block;
  overflow: hidden;
  vertical-align: middle; }

.messenger__input-attach-btn {
  font-size: 30px;
  color: #777777;
  padding-top: 0;
  padding-bottom: 0;
  line-height: 1em; }
  .messenger__input-attach-btn:hover {
    color: #333; }
  .messenger__input-attach-btn:active {
    box-shadow: none;
    color: #000; }

.message-list__item--right {
  float: right; }
  .message-list__item--right .message-list__item-message-wrapper {
    background-color: #337ab7;
    color: #FFF; }
    .message-list__item--right .message-list__item-message-wrapper:before {
      background-image: url(/img/speech-mark--blue.svg);
      left: auto;
      right: -1em; }
  .message-list__item--right .message-list__item-file,
  .message-list__item--right .message-list__item-file a {
    color: #FFF; }

.popover-list {
  list-style: disc;
  margin: 1em 0 0 1em; }

@media (min-width: 768px) {
  .modal--messenger .bootbox-close-button {
    font-size: 36px; }
  .messenger__header {
    padding: 15px; }
  .messenger__body-context {
    padding: 15px; }
  .messenger__footer {
    padding: 30px; }
  .message-list__item-message-wrapper {
    padding: 15px; }
  textarea.messenger__input-field {
    margin-bottom: 15px;
    padding: 15px;
    height: 70.000000012px;
    min-height: 70.000000012px !important; } }

@media (360px <= width <= 760px) {
  .modal--messenger .bootbox-close-button {
    display: none;}
}
