.form_box_container { display: inline-block;}

.form_box_container input[type="text"] {
    width: 24%;
    float: left;
    display: inherit;
    margin-right: 1%;
}

.box_field legend{
    margin-bottom: 10px;
}

.boxTitle{ width: 70%; margin-bottom: 10px; margin-right: 0px; float: left;}
.block{ margin-bottom: 10px; float: left; background: #f2f5f6; padding: 20px 10px; }

.form-group #console {
    margin-top: 10px;
}

.table-fields-sub th {
    background-color: #fff !important;
    color: #333 !important;
}

.extra-style {
    background-color: red !important;
}

.margin-top-0 { margin-top: 0 !important; }

#no-default-search .dataTables_filter {
    display: none;
}

.cs--logo img {
    margin: 40px 0;
}

#cs--form {
    background: #E5EAED;
    padding: 20px;
    border: 1px solid #ADB1B4;
}

#cs--form h2 {
    margin-bottom: 20px;
    color: #032862;
    font-size: 100%;
}

#cs--form .form-group {
    margin-bottom: 15px;
}

#cs--form label {
    line-height: 34px;
    font-size: 90%;
    white-space: nowrap;
}

#cs--form .btn {
    display: inline-block;
    margin-bottom: 0;
    font-weight: normal;
    text-align: center;
    vertical-align: middle;
    touch-action: manipulation;
    cursor: pointer;
    background-image: none;
    border: 1px solid transparent;
    white-space: nowrap;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.42857;
    border-radius: 4px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    border-radius: 0;
    text-decoration: none;
}

#cs--form .btn-dark-grey {
    float: right;
}

#cs--form hr {
    border-color: #ADB1B4;
    margin: 30px 0;
}

#cs--form .checkbox input[type="checkbox"] {
    bottom: 11px;
}

#cs--form .checkbox label {
    font-size: 100%;
}

.aspen--grey-bg {
    background: #E5EAED;
    padding: 15px;
    margin-bottom: 15px;
}

.aspen--lwb {
    padding: 12px 16px 0;
}

.aspen--table {
    margin-top: 10px !important;
}

.aspen--reply p {
    line-height: 34px;
}

.aspen--reply p:last-of-type {
    line-height: normal;
}

.aspen--table-spacer {
    height: 70px;
}

.panel-dashboard .panel-body {
    min-height: 192px;
}

.no-col-padding {
    padding: 0;
}

.no-col-padding-left {
    padding-left: 0;
}

.no-col-padding-right {
    padding-right: 0;
}

@media (max-width: 992px) {
    .no-col-padding, .no-col-padding-left, .no-col-padding-right {
        padding-left: 15px;
        padding-right: 15px;
    }
}
.begin{ margin-bottom: 20px ;}

.well{
    border: none ;
    border-radius: 0 ;
    box-shadow: none ;
}