<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg width="100%" height="100%" viewBox="0 0 192 90" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" style="fill-rule:evenodd;clip-rule:evenodd;stroke-linejoin:round;stroke-miterlimit:1.41421;">
    <g transform="matrix(1,0,0,1,-4.27531,-55.139)">
        <path d="M71.675,93.038C71.457,92.749 70.916,92.987 70.916,92.987L62.265,96.193C61.595,94.556 60.823,93.013 59.941,91.584L62.742,83.941C62.742,83.941 63.092,83.182 62.767,82.993C62.445,82.802 61.919,83.31 61.919,83.31L56.831,87.483C54.847,85.358 52.518,83.644 49.842,82.431C48.846,81.986 47.805,81.615 46.723,81.309L45.144,75.262C45.144,75.262 45.008,74.656 44.602,74.656C44.197,74.656 44.087,75.228 44.087,75.228L43.075,80.571C41.97,80.431 40.83,80.351 39.649,80.351C35.854,80.351 32.483,81.071 29.533,82.395C28.267,82.962 27.079,83.641 25.969,84.42C25.785,84.55 25.609,84.69 25.428,84.826C24.378,82.123 23.552,79.917 23.397,79.254C23.981,78.725 25.205,76.797 25.504,75.496C25.919,73.676 25.792,72.815 25.355,71.438C25.038,70.431 24.4,69.489 24.443,68.853C24.475,68.377 24.728,68.038 24.941,67.818C24.825,67.15 24.39,66.859 23.873,66.792C23.397,66.728 23.026,66.868 22.603,66.887C22.496,66.691 22.284,66.728 22.444,66.039C22.686,64.994 24.688,64.504 26.328,63.869C28.403,63.064 29.563,61.569 29.632,59.17C29.683,57.426 28.71,56.134 27.491,55.552C27.246,56.256 26.235,57.203 25.532,57.589C24.84,57.972 23.88,58.118 23.186,57.956C23.338,56.673 22.746,55.58 21.809,55.139C21.509,55.686 20.382,56.439 18.689,56.388C16.994,56.334 16.885,55.933 14.505,55.94C11.117,55.946 8.311,57.99 7.966,60.99C7.744,62.939 8.494,64.574 9.059,65.421C8.636,65.739 7.647,66.691 7.469,66.977C7.224,66.691 6.552,66.311 5.743,66.84C5.918,67.266 5.951,67.784 5.813,68.281C5.673,68.772 4.436,71.173 4.296,73.112C4.056,76.393 5.951,78.614 7.541,79.249C7.189,79.779 7.117,80.342 7.364,80.803C7.527,81.107 7.856,81.403 8.529,81.403C8.141,81.826 7.994,82.465 8.246,83.095C8.529,83.8 9.338,84.825 10.045,84.825C10.01,85.423 10.293,86.832 10.434,87.398C10.188,87.857 9.939,88.205 9.976,89.05C10.01,89.932 10.575,90.644 11.28,90.786C11.457,91.21 11.589,91.676 11.835,91.957C12.082,92.241 12.302,92.584 12.833,92.936C13.361,93.288 13.961,93.573 14.668,93.573C15.373,93.573 15.994,93.607 16.312,93.501C16.327,93.703 16.393,95.571 16.475,97.644C14.7,102.582 13.816,108.306 13.816,114.448L13.816,114.451C13.817,125.457 16.956,135.264 22.949,141.583C22.949,141.583 23.293,142.008 24.255,142.63C24.255,142.63 26.132,139.424 28.984,135.779C29.705,134.86 30.537,133.962 31.446,133.1C32.852,131.881 34.212,130.794 35.537,129.832C39.225,127.321 43.493,125.407 47.437,124.592C42.913,126.923 40.349,128.173 36.097,131.819C33.405,134.127 29.115,138.201 26.184,143.783C26.794,144.267 27.616,144.764 28.585,144.862C29.205,144.134 32.394,138.288 37.289,133.587C47.19,124.315 58.855,122.171 64.961,122.289C65.305,119.775 65.481,117.154 65.481,114.448C65.481,110.015 65.019,105.805 64.097,101.947L71.388,93.832C71.388,93.832 71.891,93.329 71.675,93.038Z" style="fill:rgb(42,64,119);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-4.27531,-55.139)">
        <path d="M17.452,89.375C17.029,89.129 15.865,88.561 14.948,88.599C13.997,88.637 13.194,88.829 12.444,89.304C12.127,89.091 12.028,88.668 12.232,88.174C12.434,87.682 12.727,87.611 12.727,87.611L12.458,82.988C12.458,82.988 13.757,82.064 14.108,81.959C14.46,81.853 14.621,81.578 14.621,81.226C15.195,81.617 16.044,82.482 16.5,83.132C16.924,83.732 17.171,84.4 17.207,85.142C17.242,85.882 17.249,86.765 17.099,87.682C17.389,87.93 17.664,88.14 17.699,88.527C17.734,88.918 17.594,89.058 17.452,89.375Z" style="fill:rgb(254,254,254);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-4.27531,-55.139)">
        <path d="M59.577,103.768C59.525,102.867 58.48,100.434 57.845,99.904L68.819,95.037L59.577,103.768Z" style="fill:rgb(254,254,254);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-4.27531,-55.139)">
        <path d="M29.473,85.586L38.76,94.013C37.863,94.517 36.519,95.873 36.149,96.983L29.473,85.586Z" style="fill:rgb(254,254,254);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-4.27531,-55.139)">
        <path d="M34.907,102.277C34.59,102.772 34.476,103.069 34.37,103.666C34.263,104.268 34.342,104.601 34.415,105.095L31.564,103.129C31.599,102.461 31.424,101.373 31.379,100.803C31.374,100.741 31.354,100.641 31.338,100.556L34.907,102.277Z" style="fill:rgb(254,254,254);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-4.27531,-55.139)">
        <path d="M19.215,103.096C19.04,102.779 18.229,85.634 18.193,85.037L19.11,84.368L19.11,84.191C18.441,84.155 17.664,83.909 17.419,83.518L17.171,78.265C16.252,78.193 15.308,77.902 14.955,77.551C14.516,77.11 14.173,76.569 14.668,75.616C15.125,75.936 16.464,76.323 17.629,76.323C17.875,76.323 18.394,76.878 18.689,76.994C19.025,77.128 19.59,77.44 19.857,77.558C20.098,77.663 20.246,78.227 20.317,78.438C20.387,78.651 28.88,100.7 29.023,100.946C29.165,101.193 29.66,104.895 29.692,105.178C29.73,105.46 30.999,108.421 31.245,108.706C30.117,108.671 28.268,109.318 26.752,110.802C25.27,112.249 23.958,115.43 23.661,116.897C23.556,116.12 19.393,103.414 19.215,103.096Z" style="fill:rgb(254,254,254);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-4.27531,-55.139)">
        <path d="M30.01,124.19C29.641,127.579 28.815,130.519 27.576,132.619C26.779,133.97 24.539,136.711 23.755,137.746C24.604,136.054 25.26,131.968 25.205,128.792C25.17,126.731 24.544,122.181 25.09,119.165C25.677,115.929 26.597,114.111 28.763,112.117C29.867,111.1 32.128,109.8 35.357,110.677L37.969,115.589C36.571,115.323 34.725,115.562 33.493,116.509C31.174,118.289 30.395,120.687 30.01,124.19Z" style="fill:rgb(254,254,254);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-4.27531,-55.139)">
        <path d="M30.265,130.87C30.899,129.44 31.735,125.365 31.969,124C32.271,122.251 32.551,119.698 34.635,117.979C35.659,117.134 37.341,117.024 38.01,117.219L40.965,123.236C41.229,123.873 42.097,124.098 42.678,124.063C39.91,124.962 32.931,128.276 30.265,130.87Z" style="fill:rgb(254,254,254);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-4.27531,-55.139)">
        <path d="M55.399,111.66C54.924,112.402 55.094,113.114 55.356,113.611C55.656,114.17 57.219,117.059 57.219,117.059C57.712,118.369 58.097,119.028 58.567,119.873C59.414,119.873 60.331,119.85 61.299,119.873C60.266,117.559 57.907,113.088 57.907,113.088L57.737,109.481C57.05,109.895 55.874,110.919 55.399,111.66Z" style="fill:rgb(254,254,254);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-4.27531,-55.139)">
        <path d="M42.806,92.507C43.97,92.296 46.606,92.538 47.451,92.962L44.774,77.81L42.806,92.507Z" style="fill:rgb(254,254,254);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-4.27531,-55.139)">
        <path d="M20.317,78.438C20.246,78.227 20.1,77.663 19.857,77.558C19.593,77.439 19.025,77.126 18.689,76.992C18.396,76.876 17.875,76.323 17.63,76.323C16.466,76.323 15.125,75.936 14.668,75.616C14.173,76.569 14.516,77.11 14.955,77.551C15.308,77.902 16.252,78.189 17.171,78.265L17.419,83.518C17.664,83.909 18.441,84.153 19.11,84.187L19.11,84.364L18.193,85.037C18.229,85.634 19.04,102.779 19.218,103.096C19.393,103.414 23.556,116.12 23.661,116.894C23.958,115.43 25.27,112.247 26.752,110.8C28.268,109.318 30.117,108.671 31.245,108.706C30.999,108.421 29.73,105.46 29.692,105.178C29.66,104.895 29.165,101.193 29.025,100.946C28.883,100.698 20.387,78.651 20.317,78.438Z" style="fill:rgb(254,254,254);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-4.27531,-55.139)">
        <path d="M14.893,74.234C17.292,74.239 18.428,75.236 18.991,75.765C19.061,74.955 18.933,74.239 18.179,73.628C17.551,73.119 16.449,72.669 14.758,72.669C13.628,72.669 12.494,73 11.788,73.598C11.141,74.148 11.035,74.769 11.175,75.687C11.386,75.018 12.917,74.226 14.893,74.234Z" style="fill:rgb(254,254,254);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-4.27531,-55.139)">
        <path d="M10.595,61.648C10.543,62.563 10.785,63.519 11.21,64.083C11.451,64.404 12.022,65.024 12.444,64.999C13.045,64.961 13.608,63.813 15.018,63.869C15.935,63.905 16.534,65.2 17.312,65.244C17.911,65.282 18.229,64.682 18.835,64.617C19.354,64.562 19.534,64.823 19.853,65.139C20.065,64.647 20.37,64.312 21.157,63.73C22.109,63.023 25.355,62.353 26.731,61.119C27.421,60.5 27.576,59.358 27.359,58.59C26.836,59.323 25.39,60.097 23.981,60.166C23.273,60.202 21.579,59.889 20.98,59.573C21.281,59.057 21.379,58.47 21.132,57.904C20.672,58.258 18.689,58.45 17.911,58.45C17.135,58.45 15.623,58.134 14.448,58.199C11.872,58.336 10.68,60.166 10.595,61.648Z" style="fill:rgb(254,254,254);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-4.27531,-55.139)">
        <path d="M15.04,69.654C20.615,69.761 23.131,72.335 23.203,74.453C23.486,73.816 23.448,73.217 23.413,72.511C23.386,71.949 22.534,70.361 22.496,69.831C22.461,69.302 22.603,68.95 22.709,68.456C22.356,68.525 21.122,69.02 20.592,68.597C20.025,68.143 20.418,67.011 19.675,66.728C18.933,66.443 17.771,67.327 17.029,67.222C16.29,67.114 15.655,66.094 14.88,66.094C14.101,66.094 13.713,67.044 12.761,67.257C12.027,67.421 11.137,66.978 10.703,67.011C10.223,67.044 9.933,67.419 9.721,67.876C9.509,68.337 9.338,68.525 8.776,68.666C8.326,68.78 7.682,68.386 7.364,68.456C7.61,68.702 7.734,69.706 7.512,70.29C7.23,71.031 6.625,71.808 6.552,72.732C6.492,73.548 6.765,73.924 6.977,74.523C6.835,71.524 10.173,69.561 15.04,69.654Z" style="fill:rgb(254,254,254);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-4.27531,-55.139)">
        <path d="M11.527,80.342C11.14,80.414 10.328,81.049 9.933,81.711C10.048,82.138 10.505,83.132 10.955,83.404C11.057,82.945 11.562,82.18 11.812,82.002C12.162,81.751 12.514,81.579 12.796,81.44C13.079,81.297 13.184,81.156 13.184,80.835C13.184,80.52 13.143,80.371 13.002,80.054C13.281,80.019 13.803,80.063 13.983,79.814C14.158,79.569 14.243,79.249 14.243,78.933C14.243,78.614 14.101,78.543 13.785,78.509C13.466,78.476 12.542,78.451 12.085,78.487C12.085,78.487 12.796,78.122 13.009,78.014C13.219,77.911 13.291,77.699 13.327,77.521C13.361,77.345 13.354,76.948 13.256,76.464C12.197,76.498 10.328,77.45 9.833,77.873L10.223,78.829L9.516,78.799C9.411,79.283 9.481,80.027 9.693,80.449C10.328,80.342 11.067,80.308 11.527,80.342Z" style="fill:rgb(254,254,254);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-4.27531,-55.139)">
        <path d="M57.155,102.444C57.025,101.759 55.306,99.71 52.376,97.49C50.092,95.753 46.738,94.041 43.98,94.056C39.324,94.083 37.401,97.28 37.05,98.949C36.837,99.958 36.889,101.05 37.101,101.65C36.361,102.285 36.079,102.796 35.937,103.519C35.836,104.04 35.937,104.788 36.414,105.248C36.361,104.558 36.829,103.761 37.209,103.237C37.633,102.654 38.338,102.427 38.975,102.041L39.08,103.314C38.478,103.661 37.733,104.401 37.683,104.788L37.733,106.889L39.334,105.935L39.642,106.572L39.06,110.275L39.96,110.115L41.599,110.792L40.012,112.124L40.012,112.657C40.012,112.657 40.253,112.329 40.812,112.354C41.371,112.379 42.815,113.026 42.815,113.026L42.763,113.346L42.128,113.346C41.491,113.346 39.96,113.346 39.96,113.346C39.852,113.715 40.182,114 40.394,114.158C40.762,114.053 41.145,113.863 41.356,113.863C41.566,113.863 42.056,113.988 42.478,114.04L41.971,114.72C41.971,114.72 41.709,114.92 41.229,114.825C40.965,114.773 40.702,115.142 40.702,115.445C40.702,115.765 40.906,116.559 40.906,116.559C41.167,117.751 42.383,117.508 42.538,117.497C43.313,117.429 45.832,116.519 45.832,116.519L43.41,119.915L44.468,123.545C46.066,122.888 50.839,121.192 56.022,120.342C55.456,119.403 55.364,119.028 54.669,117.628L52.024,111.544L52.394,110.645C52.976,110.857 53.875,110.432 54.457,109.957C55.039,109.481 55.726,108.633 55.991,108.104C55.621,107.894 55.306,107.944 54.986,108.159C54.669,108.372 54.422,108.779 53.982,108.581C53.503,108.372 53.737,107.849 53.79,107.372C53.842,106.897 53.24,106.889 52.818,107.256C52.394,107.626 51.493,107.469 50.911,106.889C50.329,106.305 47.528,103.976 46.521,103.399C45.074,102.567 43.135,102.354 41.706,102.612C40.872,102.759 40.277,103.129 39.96,102.971L38.913,101.352L39.08,101.248C39.45,101.408 39.799,101.703 40.277,101.544C40.752,101.385 42.283,101.036 43.663,101.016C45.781,100.98 48.355,101.754 49.484,102.235C49.537,103.609 52.305,105.355 53.557,105.512C54.614,105.645 55.254,105.355 55.886,104.983L53.187,102.612L54.617,100.803C54.617,100.803 56.893,102.971 57.21,103.399C57.313,103.149 57.21,102.737 57.155,102.444ZM40.914,105.196C40.914,105.196 42.076,104.528 43.17,104.293C44.784,103.949 46.573,104.918 47.843,105.915L47.601,106.297L45.781,106.727C45.427,106.413 44.757,106.272 44.034,106.305C43.39,106.337 42.871,106.572 42.871,106.572L43.229,107.152L42.393,107.364C42.023,106.889 41.441,106.305 40.914,106.147L40.914,105.196ZM38.159,101.016C37.683,99.419 38.955,97.84 39.535,97.26L40.434,99.323C39.75,99.694 38.69,100.501 38.159,101.016ZM46.521,99.217C45.674,98.634 42.871,98.739 41.706,99.004L40.859,96.568C41.496,96.093 42.712,95.776 43.822,95.776C44.934,95.776 46.15,96.043 46.996,96.465L46.521,99.217ZM52.024,101.712C50.86,100.963 48.583,99.853 47.898,99.639L48.9,97.048C49.749,97.473 52.87,99.374 53.346,99.853L52.024,101.712Z" style="fill:rgb(254,254,254);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-4.27531,-55.139)">
        <path d="M51.663,94.823C52.71,95.195 54.924,97.057 55.454,97.48L61.061,85.526L51.663,94.823Z" style="fill:rgb(254,254,254);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-4.27531,-55.139)">
        <path d="M86.293,101.777C86.293,103.616 87.062,104.191 89.821,104.191C92.58,104.191 93.845,103.349 95.648,99.627L96.298,99.627L95.648,105.457L78.511,105.457L78.511,104.805C81.08,104.154 81.347,103.577 81.347,101.162L81.347,86.822C81.347,84.828 80.85,84.254 78.511,83.677L78.511,83.025L89.13,83.025L89.13,83.677C86.906,84.254 86.293,84.713 86.293,86.669L86.293,101.777Z" style="fill:rgb(97,105,109);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-4.27531,-55.139)">
        <path d="M103.507,102.349C103.507,103.806 104.237,104.613 105.846,104.805L105.846,105.457L96.758,105.457L96.758,104.805C98.712,104.42 99.174,103.883 99.174,102.044L99.174,94.337C99.174,92.573 98.674,92.075 96.565,91.652L96.565,90.999L102.855,90.465L103.507,90.465L103.507,102.349ZM101.361,81.455C102.741,81.455 103.852,82.567 103.852,83.946C103.852,85.328 102.741,86.478 101.361,86.478C99.979,86.478 98.83,85.328 98.83,83.946C98.83,82.567 99.979,81.455 101.361,81.455Z" style="fill:rgb(97,105,109);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-4.27531,-55.139)">
        <path d="M112.94,94.107C114.397,92.764 115.162,92.381 116.313,92.381C118.879,92.381 120.606,94.835 120.606,98.363C120.606,103.079 118.46,104.996 115.242,104.996C113.512,104.996 112.94,104.228 112.94,102.582L112.94,94.107ZM112.94,81.455L112.286,81.455L106.421,81.877L106.421,82.529C107.763,82.759 108.607,83.449 108.607,84.676L108.607,105.457C110.792,105.877 112.94,106.03 115.662,106.03C122.717,106.03 125.398,102.349 125.398,97.901C125.398,93.683 122.41,90.426 118.5,90.426C116.428,90.426 114.662,91.269 112.94,93.074L112.94,81.455Z" style="fill:rgb(97,105,109);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-4.27531,-55.139)">
        <path d="M131.762,96.062C131.877,93.301 133.064,91.462 134.831,91.462C136.592,91.462 137.897,93.378 138.049,96.062L131.762,96.062ZM142.727,96.944C142.192,92.958 139.241,90.426 135.136,90.426C130.576,90.426 127.085,93.837 127.085,98.325C127.085,102.734 130.459,106.03 134.983,106.03C138.471,106.03 141.422,104.114 142.727,101.007L142.077,101.007C140.695,103.117 139.008,104.154 136.9,104.154C134.216,104.154 131.533,101.697 131.762,96.944L142.727,96.944Z" style="fill:rgb(97,105,109);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-4.27531,-55.139)">
        <path d="M156.607,96.023C155.34,94.798 154.343,94.145 153.346,94.145C152.044,94.145 151.237,94.682 150.127,96.214L150.127,102.12C150.127,103.846 150.47,104.38 152.579,104.805L152.579,105.457L143.378,105.457L143.378,104.805C145.298,104.343 145.793,103.768 145.793,102.12L145.793,94.489C145.793,92.649 144.991,91.806 143.152,91.652L143.152,90.999L149.473,90.426L150.127,90.426L150.127,95.028L150.203,95.028C151.622,91.882 153.194,90.426 154.998,90.426C155.953,90.426 156.875,90.85 157.564,91.652L156.607,96.023Z" style="fill:rgb(97,105,109);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-4.27531,-55.139)">
        <path d="M181.024,90.999L181.024,91.652C182.866,92.034 183.286,93.186 181.984,96.254L179.987,100.892L176.921,94.568C176.037,92.726 176.037,91.882 177.686,91.652L177.686,90.999L169.323,90.999L169.06,90.999L164.877,90.999L164.877,85.404L164.148,85.404C162.114,88.011 159.738,90.312 157.936,91.423L157.936,92.153L160.543,92.153L160.543,101.432C160.543,104.728 162.729,106.03 165.45,106.03C167.598,106.03 169.018,105.263 170.089,103.458L169.63,103.039C168.94,103.846 168.286,104.191 167.524,104.191C165.49,104.191 164.877,102.812 164.877,100.47L164.877,92.153L168.753,92.153C170.862,92.153 171.476,92.92 172.396,94.875L177.686,106.03C175.921,110.785 174.39,112.702 171.707,112.702C171.129,112.702 170.669,112.664 169.75,112.357L168.868,116.727C169.408,116.804 169.712,116.804 170.094,116.804C173.085,116.804 174.735,115.538 176.269,112.011L183.552,95.371C184.742,92.687 185.202,92.153 187.041,91.652L187.041,90.999L181.024,90.999Z" style="fill:rgb(97,105,109);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-4.27531,-55.139)">
        <path d="M106.831,131.466C107.098,133.42 108.06,134.532 110.244,134.952L110.244,135.604L99.162,135.604L99.162,134.952C101.616,134.34 102.039,132.925 101.616,130.012L99.776,117.202L99.699,117.202L92.416,135.604L91.608,135.604L84.4,117.202L84.325,117.202L82.371,130.392C81.986,133 82.484,134.762 85.015,134.952L85.015,135.604L76.889,135.604L76.889,134.952C79.303,134.842 80.337,133.457 80.874,130.012L83.099,115.363C82.216,114.443 80.99,113.869 79.84,113.829L79.84,113.176L87.928,113.176L93.794,127.976L99.736,113.176L107.331,113.176L107.331,113.829C105.029,114.253 104.492,115.4 104.797,117.471L106.831,131.466Z" style="fill:rgb(97,105,109);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-4.27531,-55.139)">
        <path d="M112.003,124.297C112.003,122.686 111.466,122.112 109.74,121.804L109.74,121.152L115.684,120.575L116.335,120.575L116.335,131.234C116.335,132.96 117.448,133.765 119.022,133.765C120.476,133.765 121.358,133.153 122.625,131.157L122.625,123.951C122.625,122.761 121.855,122.074 120.361,121.804L120.361,121.152L126.303,120.575L126.956,120.575L126.956,132.079C126.956,133.995 127.34,134.455 129.259,134.952L129.259,135.604L122.625,136.179L122.625,132.656L122.547,132.656C120.938,135.107 119.134,136.179 116.795,136.179C113.807,136.179 112.003,134.532 112.003,131.771L112.003,124.297Z" style="fill:rgb(97,105,109);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-4.27531,-55.139)">
        <path d="M132.592,122.301L129.984,122.301L129.984,121.574C131.785,120.462 134.164,118.161 136.193,115.555L136.925,115.555L136.925,121.152L141.677,121.152L141.677,122.301L136.925,122.301L136.925,130.622C136.925,132.96 137.537,134.34 139.569,134.34C140.334,134.34 140.988,133.995 141.677,133.19L142.137,133.613C141.065,135.415 139.646,136.179 137.497,136.179C134.776,136.179 132.592,134.879 132.592,131.579L132.592,122.301Z" style="fill:rgb(97,105,109);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-4.27531,-55.139)">
        <path d="M144.476,124.297C144.476,122.686 143.939,122.112 142.212,121.804L142.212,121.152L148.157,120.575L148.811,120.575L148.811,131.234C148.811,132.96 149.92,133.765 151.49,133.765C152.949,133.765 153.831,133.153 155.095,131.157L155.095,123.951C155.095,122.761 154.328,122.074 152.834,121.804L152.834,121.152L158.776,120.575L159.428,120.575L159.428,132.079C159.428,133.995 159.813,134.455 161.729,134.952L161.729,135.604L155.095,136.179L155.095,132.656L155.02,132.656C153.409,135.107 151.607,136.179 149.266,136.179C146.278,136.179 144.476,134.532 144.476,131.771L144.476,124.297Z" style="fill:rgb(97,105,109);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-4.27531,-55.139)">
        <path d="M171.622,133.116C170.622,134.113 169.78,134.61 168.898,134.61C167.709,134.61 166.904,133.573 166.904,132.003C166.904,130.049 168.013,129.165 171.622,127.631L171.622,133.116ZM175.989,125.982C175.989,121.994 172.731,120.575 169.625,120.575C166.252,120.575 163.913,122.378 163.146,125.485L163.723,125.485C164.64,123.873 166.097,123.071 168.053,123.071C170.662,123.071 171.659,124.297 171.659,126.867C168.13,128.168 162.381,128.475 162.381,132.386C162.381,134.495 164.335,136.179 166.714,136.179C168.475,136.179 169.817,135.759 171.274,134.455L171.622,134.15C172.346,135.604 173.345,136.179 175.107,136.179C177.218,136.179 178.483,135.184 178.635,133.383L178.023,133.383C177.868,134.265 177.486,134.722 176.872,134.722C176.221,134.722 175.989,134.15 175.989,132.808L175.989,125.982Z" style="fill:rgb(97,105,109);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-4.27531,-55.139)">
        <path d="M181.627,115.555C181.627,113.523 180.971,112.793 178.903,112.679L178.903,112.029L185.306,111.604L185.959,111.604L185.959,132.079C185.959,133.995 186.381,134.455 188.525,134.952L188.525,135.604L178.903,135.604L178.903,134.952C181.014,134.61 181.627,133.92 181.627,132.079L181.627,115.555Z" style="fill:rgb(97,105,109);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-4.27531,-55.139)">
        <path d="M189.057,132.853L190.184,132.853L190.184,135.784L190.634,135.784L190.634,132.853L191.76,132.853L191.76,132.454L189.057,132.454L189.057,132.853Z" style="fill:rgb(97,105,109);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-4.27531,-55.139)">
        <path d="M195.269,135.784L195.269,133.818C195.269,133.72 195.276,133.298 195.276,132.986L195.269,132.986L194.332,135.784L193.884,135.784L192.95,132.991L192.94,132.991C192.94,133.298 192.95,133.72 192.95,133.818L192.95,135.784L192.513,135.784L192.513,132.454L193.158,132.454L194.11,135.269L194.117,135.269L195.064,132.454L195.704,132.454L195.704,135.784L195.269,135.784Z" style="fill:rgb(97,105,109);fill-rule:nonzero;"/>
    </g>
</svg>
