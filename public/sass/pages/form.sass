#ffFormCreate
  input, select
    font-size: 0.8em
  .tabs
    border: 0
    .ui-tabs-nav
      li
        display: inline-block
        a
          display: block
          border: 1px solid #8194B0
          border-bottom: 0
          padding: 10px 20px
          background: #445E8C
          outline: none
          text-decoration: none
          color: #fff
        &.ui-state-active
          a
            position: relative
            background: #fff
            color: #333
            &:after
              content: ''
              display: block
              position: absolute
              bottom: -1px
              left: 0
              right: 0
              height: 1px 
              background: #fff
    .ui-tabs-panel
      padding: 15px
      border: 1px solid #8194B0
  #fields
    .elements
      h2
        margin-bottom: 10px
    .ffForm-target
      > div
        background: #445E8C
        color: #fff
        overflow: auto
        h2
          a
            color: #fff
          p
            margin-bottom: 15px
        #dependValue
          color: #333
    #ffForm
      li
        a
          background: #445E8C
          border-color: #445E8C
          color: #fff
          
#conditionalText
  input
    color: #333
      
#formOptions
  h2
    margin-bottom: 20px
  p
    margin: 10px 0
    
.ffaccordion
  .ui-accordion-content
    padding: 10px 10px 1px 10px