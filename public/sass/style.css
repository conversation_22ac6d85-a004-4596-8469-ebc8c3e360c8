/*
Error: Inconsistent indentation: "      \t\t" was used for indentation, but the rest of the document was indented using 1 tab.
        on line 611 of partials/learning.sass
        from line 29 of style.sass

606: 		background-color: #000
607: 		border-radius: 3px
608: 		overflow: hidden
609: 
610: 		.img-responsive-courses
611:       		height: inherit
612: 
613: 		.liberty-img-thumb
614: 			background-repeat: no-repeat !important
615: 			background-size: cover !important
616: 			background-position: center center !important

Backtrace:
partials/learning.sass:611
style.sass:29
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/lib/sass/engine.rb:473:in `block in tabulate'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/lib/sass/engine.rb:431:in `each'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/lib/sass/engine.rb:431:in `each_with_index'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/lib/sass/engine.rb:431:in `tabulate'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/lib/sass/engine.rb:396:in `_to_tree'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/lib/sass/engine.rb:299:in `to_tree'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/lib/sass/tree/visitors/perform.rb:323:in `block in visit_import'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/lib/sass/stack.rb:88:in `block in with_import'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/lib/sass/stack.rb:115:in `with_frame'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/lib/sass/stack.rb:88:in `with_import'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/lib/sass/tree/visitors/perform.rb:322:in `visit_import'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/lib/sass/tree/visitors/base.rb:36:in `visit'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/lib/sass/tree/visitors/perform.rb:158:in `block in visit'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/lib/sass/stack.rb:79:in `block in with_base'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/lib/sass/stack.rb:115:in `with_frame'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/lib/sass/stack.rb:79:in `with_base'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/lib/sass/tree/visitors/perform.rb:158:in `visit'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/lib/sass/tree/visitors/base.rb:52:in `block in visit_children'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/lib/sass/tree/visitors/base.rb:52:in `map'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/lib/sass/tree/visitors/base.rb:52:in `visit_children'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/lib/sass/tree/visitors/perform.rb:167:in `block in visit_children'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/lib/sass/tree/visitors/perform.rb:179:in `with_environment'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/lib/sass/tree/visitors/perform.rb:166:in `visit_children'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/lib/sass/tree/visitors/base.rb:36:in `block in visit'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/lib/sass/tree/visitors/perform.rb:186:in `visit_root'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/lib/sass/tree/visitors/base.rb:36:in `visit'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/lib/sass/tree/visitors/perform.rb:157:in `visit'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/lib/sass/tree/visitors/perform.rb:8:in `visit'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/lib/sass/tree/root_node.rb:36:in `css_tree'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/lib/sass/tree/root_node.rb:29:in `render_with_sourcemap'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/lib/sass/engine.rb:368:in `_render_with_sourcemap'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/lib/sass/engine.rb:285:in `render_with_sourcemap'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/lib/sass/plugin/compiler.rb:490:in `update_stylesheet'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/lib/sass/plugin/compiler.rb:215:in `block in update_stylesheets'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/lib/sass/plugin/compiler.rb:209:in `each'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/lib/sass/plugin/compiler.rb:209:in `update_stylesheets'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/lib/sass/plugin.rb:82:in `update_stylesheets'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/lib/sass/exec/sass_scss.rb:361:in `watch_or_update'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/lib/sass/exec/sass_scss.rb:51:in `process_result'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/lib/sass/exec/base.rb:52:in `parse'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/lib/sass/exec/base.rb:19:in `parse!'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551@global/gems/sass-3.4.13/bin/sass:13:in `<top (required)>'
/Users/<USER>/.rvm/rubies/ruby-1.9.3-p551/bin/sass:23:in `load'
/Users/<USER>/.rvm/rubies/ruby-1.9.3-p551/bin/sass:23:in `<main>'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551/bin/ruby_executable_hooks:15:in `eval'
/Users/<USER>/.rvm/gems/ruby-1.9.3-p551/bin/ruby_executable_hooks:15:in `<main>'
*/
body:before {
  white-space: pre;
  font-family: monospace;
  content: "Error: Inconsistent indentation: \"      \t\t\" was used for indentation, but the rest of the document was indented using 1 tab.\A         on line 611 of partials/learning.sass\A         from line 29 of style.sass\A \A 606: 		background-color: #000\A 607: 		border-radius: 3px\A 608: 		overflow: hidden\A 609: \A 610: 		.img-responsive-courses\A 611:       		height: inherit\A 612: \A 613: 		.liberty-img-thumb\A 614: 			background-repeat: no-repeat !important\A 615: 			background-size: cover !important\A 616: 			background-position: center center !important"; }
