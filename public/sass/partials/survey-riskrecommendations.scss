.survey-report--csr,.survet-report--uw {
  a.disabled {
    color: #fff !important
  }
}
// SHOW Route

section.survey-show{

  padding-left: 15px;
  padding-right: 15px;
  padding-bottom: 30px;

  #survey-details{
    background-color: #e4e9ef;

    .panel{
      background: none;
      border-radius: 0 !important;

      &:first-of-type{

        margin-top: 30px;

      }


      .panel-heading{
        background-color: #579099;
        border-radius: 0 !important;
        color: #fff;
        h4{
          font-size: 15px;
        }


        &::after {
          content: "";
          display: block;
          width: 30px;
          height: 54px;
          position: absolute;
          background-color: #e4e9ef;
          margin-top: -42px;
          right: 6px;
          -ms-transform: rotate(-25deg); /* IE 9 */
          -webkit-transform: rotate(-25deg);
          transform: rotate(-25deg);

        }


      }

      .panel-body{

        h4{
          font-size: 13px;
        }

        p{

          font-size: 12px;

        }
      }




    }

  }


  #next-booking{
    padding-left: 0;
    padding-right: 0;
    #next-booking-panel{
      background-color: #445f8e;
      color: #fff;
      margin-bottom: 0;

      .panel-heading{
        background-color: #1e3159;
        color: #fff;
      }


      #next-booking-panel__icon{
        padding-left: 0;
        padding-right: 0;
        .calendar-icon{
          background-image: url('/img/survey_calendar.png');
          background-repeat: no-repeat;
          background-size: cover;
          min-height: 190px;
          width: 100%;
          display: block;

          span{
            position: absolute;
            left: 40px;
            top: 95px;
            font-size: 40px;
            color: #445f8e;

            &#day{
              margin-top: 40px;
            }
          }
        }

      }


      #next-booking-panel__date{
        position: absolute;
        bottom: 0;
        right: 0;

        h3{
          font-weight: 400;
          font-size: 150%;
          white-space: nowrap;
        }
      }


      #next-booking-panel__download{

        h2{
          color: #fff;
          text-align: center;
          font-size: 190%;
          line-height: 120%;
        }

        img{
          text-align: center;
          font-size: 3em;
          display: block;
          margin: 5px auto;
        }

        .btn{
          text-align: center;
          margin: 10px auto 0;
          display: block;
          background-color: #6199c8;
          width: 120px
        }

      }
    }

  }

  #next-booking-panel__review{

    background-color: #dcdfe8;

    #next-booking-panel__review__ready{

      #icon{
        padding-left: 0;
        padding-right: 0;
      }

      .small-title{
        position: absolute;
        right: 0;
        bottom: 0;
      }

    }

  }

}



body.survey-showriskrecommendation,
body.survey-showlegacyriskrecommendation{

  section{


    &.details{
      background-color: #DCE0E8;
      margin: 20px 0;

      #details__detail{

        .row{
          border-bottom: 1px solid #333;
          padding: 10px 0px;
          margin: 0 -5px;
          div{
            padding: 0 5px;
          }
        }

      }


      #details__messages{



      }
    }


    &#photography{

      background-color: #DCE0E8;
      margin: 20px 0 0;

    }


  }




}



