@mixin clearfix
  &:after
    content: ""
    display: table
    clear: both

@mixin adaptive
  display: block
  width: 100%
  height: auto

@mixin placeholder
  &::-webkit-input-placeholder
    @content
  &:-moz-placeholder
    @content
  &::-moz-placeholder
    @content
  &:-ms-input-placeholder
    @content

@mixin keep-aspect-ratio($ratio: 16 9)
  width: 100%
  display: inline-block
  position: relative
  &:after
    padding-top: unquote(nth($ratio, 2)/(nth($ratio, 1)/100)+'%')
    display: block
    content: ''
  > div,
  > video
    width: 100% !important
    height: 100% !important
    position: absolute
    top: 0
    bottom: 0
    right: 0
    left: 0

@mixin half-size
  -moz-transform: scale(0.5)
  -webkit-transform: scale(0.5)
  transform: scale(0.5)

@mixin vertical-middle
  display: inline-block
  height: 100%
  vertical-align: middle