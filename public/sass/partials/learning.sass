.assign-users__list 
	.table 
		tr 
			.assign-users__list-item__icon
				display: none
		tr.active 
			.assign-users__list-item__icon
				display: inline-block
.learning
	padding-bottom: 45px
	margin-bottom: 0

	.field-rich-text-editor
		b
			font-weight: bold
		i
			font-style: italic
		ol 
			display: block
			list-style-type: decimal
			margin-top: 1em
			margin-bottom: 1em
			margin-left: 0
			margin-right: 0
			padding-left: 40px
		ul
			list-style-type: disc
			margin-top: 1em
			margin-bottom: 1em
			margin-left: 0
			margin-right: 0
			padding-left: 40px
			

	.messenger__video-upload-placeholder
		video
			max-width: 100%

	header
		border-bottom: 0
		
	.messenger__video-upload-placeholder
		video
			max-width: 100%

	h2
		font-size: 20px
		color: #445e8c

	h3
		font-size: 17px
		color: #445e8c
		font-weight: 700
		margin: 24px 0 15px 0

	.btn-danger
		background-color: #d9534f
		border-color: #d43f3a

		&:hover
			background-color: #c9302c

	.btn-success
		background-color: #69c057
		border-color: #4cae4c

		&:hover
			background-color: #50a63e

	.btn-default
		background-color: #fff
		border-color: #ccc
		color: #333 !important

		&:hover
			background-color: #e6e6e6

	div.dataTables_wrapper div.dataTables_length
		margin-top: 10px
		padding-top: 6px

	.panel-primary
		border-color: #445e8c

		.panel-title
			color: #FFF

		> .panel-heading
			border-radius: 0
			background-color: #445e8c
			border-color: #445e8c

	.panel-title
		margin: 0
		color: #555

	.alert-success
		color: #69c057

	.alert-danger
		color: #d9534f

	&.page
		margin: 0 0 15px 0

	hr
		border-color: #e7e8e9

	section#title
		border-bottom: 1px solid #e7e8e9
		padding: 25px
		margin: 0




	table.table-bordered.dataTable
		margin-top: 0 !important

	div.dataTables_wrapper div
		&.dataTables_length, &.dataTables_filter, &.dataTables_info, &.dataTables_paginate
			text-align: center

		&.dataTables_info
			margin-bottom: 10px

	.table--striped-tbody .odd
		background-color: #f9f9f9
	.tr--child td:first-child
		padding-left: 28px

	div[contenteditable].form-control
		height: auto

	.select--with-text
		width: auto
		display: inline-block

	.form-control--text-only
		border-color: transparent
		background-color: transparent
		box-shadow: none
		height: auto
		min-height: 34px
		padding-left: 0

	.form-control--html
		border-color: transparent
		background-color: transparent
		box-shadow: none
		height: auto
		min-height: 34px
		padding-left: 0
		padding-right: 0

	.form-control--html-no-padding
		padding-top: 0
		padding-bottom: 0

	.control-label small
		display: block
		color: #777
		font-weight: normal
		font-size: 12px
		line-height: 1.2em

	.form__validation-message .error
		color: #d9534f

	.icon-sortable, .icon-delete, .icon-duplicate, .icon-edit, .icon-delete-section, .icon-duplicate-section
		font-size: 14px
		opacity: 0.4
		line-height: 1em
	.icon-sortable:hover, .icon-delete:hover, .icon-duplicate:hover, .icon-edit:hover, .icon-delete-section:hover, .icon-duplicate-section:hover
		opacity: 1
		text-decoration: none

	.icon-sortable
		position: relative

		&:hover
			opacity: 1

	.btn:not(.btn-xs)
		min-width: 62px

	.badge
		font-size: 12px
		font-weight: normal
		background-color: #a5acb4
		padding: 6px 12px
		line-height: 1.428571429
		border-radius: 20px

	.badge--rectangle
		border-radius: 0

	.badge--btn-size
		font-size: 14px
		padding: 7px 13px
		line-height: 1.428571429
		min-width: 150px

	.badge--success
		background-color: #69c057

	.badge--danger
		background-color: #d9534f

	.award
		color: #445e8c
		line-height: 1em
		background: url(../img/learning/award.svg)
		height: 1em
		width: 1em
		background-size: cover
		margin: 0 auto

	.award--large
		font-size: 150px

	.page__content
		margin-top: 30px

	.progress
		height: 35px
		border-radius: 20px
		background-color: transparent
		border: 3px solid #f0ad4e

	.progress-bar
		border-radius: 0 20px 20px 0
		box-shadow: none

	.page-builder__item-content
		clear: both
		margin-bottom: 15px

		.editor__content div[contenteditable]
			min-height: 300px

	.page-builder__item-buttons--complete
		margin: 0 auto 15px auto
		text-align: center

	.page-builder__item-title
		cursor: pointer

		.icon-sortable, .icon-delete, .icon-duplicate,  .icon-delete-section, .icon-duplicate-section
			float: right
			margin-left: 1em

		.icon-sortable
			margin-top: 1px

	.page-builder__item--document-upload__file-list
		li
			position: relative
			padding-right: 30px

		.icon-delete
			position: absolute
			right: 10px
			top: 10px
			color: #777

	.page-builder-view__page
		padding: 30px 0

	.page-builder-view__progress
		margin: -6px 0 0 0
		display: block

		li
			> a
				margin-right: 0.5em
				border-radius: 6px
				color: #555
				border: none

			&:last-child a
				margin-right: 0

		.active a
			background-color: #69c057
			cursor: pointer
			color: #FFF

	.page-builder-view__page-item
		margin-bottom: 30px

	.page-builder-view__page-item--image
		text-align: center

		img
			max-width: 100%
		padding: 30px
		background-color: #eeeeee

	.page-builder-view__page-item--video-embed, .page-builder-view__page-item--video-upload
		padding: 30px
		background-color: #eeeeee

	.page-builder-view__page-item--text-rich
		b, strong
			font-weight: 700

		i, em
			font-style: italic

		p
			margin: 0 0 1em 0

		ul, ol
			margin: 0 0 1em 1em

		ul li, ol li
			margin-bottom: 0.5em

		ul
			list-style: disc

		ol
			list-style: decimal

		h1, h2
			font-size: 26px
			font-weight: normal

		h3, h4, h5, h6
			font-size: 22px
			font-weight: normal

	.page-builder-view__page-item--heading h2
		font-size: 26px

	.page-builder-view__page-item--document-upload h3
		font-size: 22px
		font-weight: normal

	.page-builder-view__page-item--question
		margin: 60px 0

	.page-builder-view__page-item__question-text
		color: #445e8c
		margin-bottom: 0.5em
		font-weight: normal
		font-size: 18px

	.page-builder-view__page-item-answer
		&.answer--chosen label:after
			content: ' - chosen incorrect answer'
		&.answer--correct label:after
			content: ' - correct answer'
		&.answer--chosen.answer--correct label:after
			content: ' - chosen correct answer'

	.page-builder-view__page-item__select-text
		font-style: italic
		color: #777
		margin-top: 1em

	.page-builder-view__page-nav
		color: #777
		position: relative
		text-align: center

		.btn--back
			background-color: #777
			left: 0

		.btn--next
			right: 0

		.pagination
			li a
				color: #777
				border: none

			.active a
				color: #FFF
				background-color: #777

	.page-builder-view__page-results__failed, .page-builder-view__page-results__passed
		margin: 0 0 30px
		background-color: transparent
		border: none
		position: relative
		padding-left: 50px

	.page-builder-view__page-results__failed .fa, .page-builder-view__page-results__passed .fa
		font-size: 40px
		position: absolute
		top: 15px
		left: 0

	.page-builder-view__page-results__failed .alert__title, .page-builder-view__page-results__passed .alert__title
		font-weight: 700
		margin-bottom: 0

	.page-builder-view__page-results__failed .alert__body, .page-builder-view__page-results__passed .alert__body
		color: #555
		margin-top: 0

	.add-content
		list-style: none
		padding: 0
		margin: 0 -7.5px

		&:before
			content: " "
			display: table

		&:after
			content: " "
			display: table
			clear: both

	.add-content__item
		position: relative
		float: left
		width: 50%
		min-height: 1px
		padding-left: 15px
		padding-right: 15px
		position: relative
		min-height: 1px
		padding-left: 15px
		padding-right: 15px
		padding-left: 7.5px
		padding-right: 7.5px
		margin-bottom: 15px

	.add-content__item-button
		display: block
		text-align: center
		padding: 7.5px

	.add-content__item-icon
		display: block
		font-size: 30px

	.document-downloads
		list-style: none
		margin-top: 30px
		padding: 0

		&:before
			content: " "
			display: table

		&:after
			content: " "
			display: table
			clear: both

	.document-downloads__item
		margin-bottom: 15px

		a.pull-right
			margin-top: -30px
			margin-right: 10px

	.document-downloads__item-btn
		white-space: normal
		display: block
		text-align: left
		background-color: #eeeeee
		border: none
		color: #445e8c !important
		padding: 12px 18px



		&:hover
			background-color: #e2e2e2

	.document-downloads__btn-context
		display: block
		position: relative
		padding-left: 36px
		line-height: 1.3em

	.document-downloads__item-icon
		position: absolute
		top: 50%
		-webkit-transform: translateY(-50%)
		-ms-transform: translateY(-50%)
		transform: translateY(-50%)
		left: 0
		font-size: 24px

	.editor__toolbar
		margin-bottom: 15px

	.page-list__buttons
		margin-top: 7.5px

	.page-list__item
		position: relative

		.page-list__item-name
			display: block
			padding-right: 36px

		&.active .page-list__item-name
			color: #FFF

		.page-list__item-input-group
			display: none

			.input-group
				margin-top: 7.5px

		.icon-sortable
			position: absolute
			right: 15px
			top: 13px

	.test-question__buttons--add
		margin-top: 7.5px

	.test-question__radio-legend
		text-align: center

	.test-question__radio input
		&[type="radio"], &[type="checkbox"]
			position: relative
			margin: 9px auto 0
			display: block

	.test-question__checkbox input
		&[type="radio"], &[type="checkbox"]
			position: relative
			margin: 9px auto 0
			display: block

	.test-question__answer-input-holder
		position: relative
		padding-right: 20px

	.test-question__answer-delete
		position: absolute
		top: 7px
		color: #333
		right: 0

	.test-question__answer
		&:first-child .test-question__answer-delete, &:nth-child(2) .test-question__answer-delete
			display: none

	.test-question__checkbox:nth-child(3) .test-question__answer-delete
		display: none

	.library
		margin: 0 -15px

	.library__section
		padding: 30px 15px

		&.library__section--highlight
			background-color: #e7e8e9

			.library-item
				border-color: #e7e8e9
				margin-bottom: 60px

	h2.library__section-title
		margin: 0 0 40px
		font-size: 20px

	.library-item
		margin-bottom: 30px
		padding-bottom: 20px
		border-bottom: 1px solid #e7e8e9

	.library-grid-item
		position: relative
		min-height: 1px
		padding-left: 15px
		padding-right: 15px
		position: relative
		min-height: 1px
		padding-left: 15px
		padding-right: 15px

		&:nth-child(2n+1)
			clear: left

	.library-item__body
		&:before
			content: " "
			display: table

		&:after
			content: " "
			display: table
			clear: both

	.library-item__btns
		text-align: right
		margin-top: 20px

	.library-item__image
		position: relative
		background-color: #000
		border-radius: 3px
		overflow: hidden

		.img-responsive-courses
			height: inherit

		.liberty-img-thumb
			background-repeat: no-repeat !important
			background-size: cover !important
			background-position: center center !important
			width: 100%
			height: 180px
			max-height: 180px
			background-color: #fff !important

		img
			opacity: 0.3
			height: 185px
			width: 100%

		.progress
			position: absolute
			top: 50%
			width: 50%
			left: 50%
			margin-left: -25%
			-webkit-transform: translateY(-50%)
			-ms-transform: translateY(-50%)
			transform: translateY(-50%)
			height: 35px
			border-radius: 20px
			background-color: transparent
			border: 3px solid #f0ad4e

		.progress-bar
			border-radius: 0 20px 20px 0
			box-shadow: none

		.library-item__image-icon
			position: absolute
			top: 50%
			left: 50%
			font-size: 50px
			margin: -0.5em 0 0 -0.5em
			color: #f0ad4e

			&.icon--pending
				background: url(../img/learning/icon-training-book.png) 50% 50% no-repeat
				font-size: 51px
				width: 1em
				height: 1em

			&.icon--unpublished
				font-size: 16px
				text-align: center
				width: 100%
				height: auto
				left: 0
				line-height: 1.2em
				-webkit-transform: translateY(-50%)
				-ms-transform: translateY(-50%)
				transform: translateY(-50%)
				margin: 0
	.library-item__organisation:before
		content: 'Organisation: '

	.library__section-header--button
		.btn, .library__section-header__form
			float: right
			margin-left: 1em

	.library-item--has-status .library-item__body-header
		&:before
			content: " "
			display: table

		&:after
			content: " "
			display: table
			clear: both

		.library-item__status
			float: right
			margin-top: 24px
			margin-left: 10px

	.library-item--in-progress .library-item__status
		background-color: #f0ad4e

	.library-item--completed
		.library-item__status
			background-color: #69c057

		.library-item__image-icon
			color: #69c057

	.page-jumbo-header
		text-align: center
		background-color: #dfe5f0
		background-repeat: no-repeat
		background-position: 50% 50%
		background-size: cover
		position: relative
		color: #FFF
		padding: 90px 30px 75px
		margin-top: 30px

	h2.page-jumbo-header__title
		color: #f0ad4e
		margin-bottom: 45px
		font-size: 24px
		font-family: "Rockwell W01"
		line-height: 1.3em

	.page-jumbo-header__context
		position: relative

	.page-jumbo-header--has-description
		padding: 30px 30px 15px

		h2.page-jumbo-header__title
			margin-bottom: 20px

	.course-view__description, .course-view__header-btns
		margin-bottom: 30px

	.course-view__header-progress
		text-align: right

	.course-view__sections
		margin-bottom: 90px

	.course-view__section
		padding: 90px 0 0 0
		position: relative

		&:nth-child(n)
			padding: 90px 0 0 0
			position: relative

		&:before, &:nth-child(n):before
			content: ''
			position: absolute
			top: 0
			left: 50%
			margin-left: -2px
			width: 4px
			height: 90px
			background-color: #eeeeee

	h2
		&.certificate-view__title
			margin-bottom: 24px

		&.certificate-view__title-name
			color: #445e8c
			font-size: 26px
			font-weight: 700
			margin: 24px 0 24px

	.certificate-view__award
		background-image: url(../img/learning/award--white-tick.svg)

	.certificate-view__title-course-title
		font-weight: normal
		line-height: 1
		color: #777
		margin: 20px 0 30px
		font-weight: 700

	.certificate-view__body-header
		text-align: center
		padding-bottom: 30px

	.certificate-view__lessons-list
		list-style: none
		border-top: 1px solid #e7e8e9
		margin: 0 -15px 30px
		padding: 30px 0 0 0

	.certificate-view__lesson-item
		position: relative
		min-height: 1px
		padding-left: 15px
		padding-right: 15px
		margin-bottom: 30px

		&:nth-child(2n+1)
			clear: left

	.certificate-view__lessons-list--items-1 .certificate-view__lesson-item
		&:nth-child(odd), &:nth-child(even)
			padding-left: 30px
			padding-right: 30px

	.certificate-view__lessons-list--items-2 .certificate-view__lesson-item
		&:nth-child(odd), &:nth-child(even)
			padding-left: 30px
			padding-right: 30px

	.certificate-view__lessons-list--items-3 .certificate-view__lesson-item
		&:nth-child(odd), &:nth-child(even)
			padding-left: 30px
			padding-right: 30px

	.certificate-view__lesson-item-context
		position: relative
		padding-right: 46px

	.certificate-view__lesson-title
		margin-bottom: 5px
		font-weight: 700
		margin-top: 0

	.certificate-view__lesson-icon
		font-size: 36px
		color: #69c057
		position: absolute
		right: 0
		top: -6px
		line-height: 1em

		.fa
			vertical-align: top

	.certificate-view__logo--main
		max-width: 200px
		margin: 30px auto 66px

	.certificate-view__logos-additional
		list-style: none
		position: relative
		margin: 0 -15px 30px
		padding: 30px 0 15px
		text-align: center
		font-size: 0

		&:before
			content: ''
			top: 0
			left: 15px
			right: 15px
			position: absolute
			border-top: 1px solid #e7e8e9

		&:after
			content: ''
			top: 0
			left: 15px
			right: 15px
			position: absolute
			border-top: 1px solid #e7e8e9
			top: auto
			bottom: 1px

	.certificate-view__logos-additional__item
		position: relative
		float: left
		width: 33.3333333333%
		min-height: 1px
		padding-left: 15px
		padding-right: 15px
		position: relative
		min-height: 1px
		padding-left: 15px
		padding-right: 15px
		display: inline-block !important
		float: none !important
		margin-bottom: 15px
		font-size: 14px
		vertical-align: middle

	.certificate-view__footer-btn
		text-align: center
		margin-bottom: 30px

	.course-section__panel
		margin-bottom: 0
		border: none
		box-shadow: 5px 5px 0 #dadbdd !important

		.panel-body
			padding-left: 30px
			padding-right: 30px

		.panel-heading
			padding-left: 30px
			padding-right: 30px
			position: relative

			&:after
				content: ''
				font-size: 58px
				height: 1em
				width: 1em
				background: url(../img/learning/tag.svg) 0 0 no-repeat
				background-size: cover
				position: absolute
				top: -2px
				right: 20px

		.panel-body
			background-color: #e7e8e9

	h3.course-section__body-title
		margin: 20px 0 20px
		font-size: 20px
		font-family: "Rockwell W01"

	.course-section__description
		margin-bottom: 35px

	.course-section__status-icon
		font-size: 36px
		line-height: 1em
	.course-section__panel-body-footer-col:first-child
		margin-bottom: 15px

	.course-section__status-badge
		margin-left: 1em

	.course-section--complete
		.course-section__status-icon
			color: #69c057

		.course-section__status-badge
			background-color: #69c057

	.course-section--failed
		.course-section__status-icon
			color: #d9534f

		.course-section__status-badge
			background-color: #d9534f

	.course-section--course-completed
		.course-section__panel
			background-color: #dfe5f0
			text-align: center

		.panel-body
			padding-top: 36px

		.course-section__body-title, h3.course-section__body-title
			color: #445e8c
			margin-bottom: 30px

	.lesson-list__lesson
		position: relative
		padding-right: 90px

	.lesson-list__btns
		position: absolute
		right: 0
		top: 0

		.icon
			margin-left: 0.5em
			color: #445e8c

			&:first-child
				margin-left: 0

		button
			border: 0
			background-color: transparent
			outline: none
			padding: 0

	.lesson-list__body
		padding: 0
		border-bottom: 0

		.list-group-item
			border-radius: 0
			border-left: 0
			border-right: 0

			&:first-child
				border-top: 0

	.assign-users__list .table
		margin: 0 !important
		width: 100% !important

		tr
			&:first-child td
				border-top: 0

			&.active .assign-users__list-item__icon
				display: inline-block

	.assign-users__filter
		margin-bottom: 10px

	.assign-users__list-item__icon
		display: none
		margin-right: 0.5em

	.bootbox-body
		.alert
			margin: 0

		.alert--bottom-margin
			margin-bottom: 1em

		.alert--small
			font-size: 14px
			padding: 10px

	.input__files
		margin-top: 10px

	.input__files--full
		clear: left

		.input__file
			position: relative
			float: left
			width: 50%
			min-height: 1px
			padding-left: 15px
			padding-right: 15px
			position: relative
			min-height: 1px
			padding-left: 15px
			padding-right: 15px
			position: relative
			min-height: 1px
			padding-left: 15px
			padding-right: 15px

			&:nth-child(2n+1)
				clear: left

	.input__file-inner
		position: relative
		border: 1px solid #ccc
		margin-bottom: 5px

	.input__file-icon
		font-size: 18px
		width: 18px
		line-height: 1
		text-align: center
		background-color: #FFF
		position: absolute
		top: 3px
		right: 3px

	label.input__file-icon
		color: #337ab7

		&:hover
			color: #23527c
			cursor: pointer

	.table-controls__date-filters
		margin-bottom: 15px

	.panel--grey
		background-color: #eeeeee
		border: none

	.panel-heading--with-buttons
		position: relative
		overflow: hidden

		.panel-title
			margin-bottom: 10px

@media (min-width: 992px)
	.learning
		section#title
			margin-left: -25px
			margin-right: -25px
			padding: 25px 50px

@media (min-width: 992px)
	.learning div.dataTables_wrapper div
		&.dataTables_info
			margin-bottom: 0

		&.dataTables_length, &.dataTables_filter
			text-align: left

		&.dataTables_paginate
			text-align: right

@media (min-width: 992px)
	.learning .page-builder-view__page-nav
		.btn--back, .btn--next
			position: absolute

		.pagination
			margin-top: 0

@media (min-width: 1200px)
	.learning .add-content__item
		float: left
		width: 25%

@media (min-width: 768px)
	.learning
		.page-list__buttons
			margin-top: 0
			position: absolute
			top: 7.5px
			right: 7.5px
			display: none
		.page-list__item:hover .page-list__buttons
			display: block

@media (min-width: 768px)
	.learning .library-grid-item
		float: left
		width: 50%

@media (min-width: 992px)
	.learning .library-grid-item
		float: left
		width: 33.3333333333%

@media (min-width: 992px)
	.learning .library-grid-item
		padding-right: 45px

		&:nth-child(2n+1)
			clear: none

		&:nth-child(3n+1)
			clear: left

@media (min-width: 992px)
	.learning
		.library
			margin-left: -30px
			margin-right: -30px

		.library__section
			padding-left: 30px
			padding-right: 30px

		.library__section-header--button
			padding-right: 30px

@media (min-width: 1200px)
	.learning
		.library
			margin-left: -30px
			margin-right: -30px

		.library__section
			padding-left: 30px
			padding-right: 30px

@media (min-width: 768px)
	.learning
		.course-view__header
			padding: 60px 60px 30px

		.course-view__header-btns
			text-align: left
			margin-bottom: 0

		.course-view__description
			margin-bottom: 45px

@media (min-width: 768px)
	.learning .certificate-view__lesson-item
		float: left
		width: 50%

@media (min-width: 768px)
	.learning
		.certificate-view__lessons-list--items-1 .certificate-view__lesson-item, .certificate-view__lessons-list--items-2 .certificate-view__lesson-item, .certificate-view__lessons-list--items-3 .certificate-view__lesson-item
			margin-left: 25%

@media (min-width: 768px)
	.learning .certificate-view__logos-additional__item
		float: left
		width: 20%

@media (min-width: 768px)
	.learning
		.certificate-view__header
			margin-bottom: 105px

		.certificate-view__award
			position: absolute
			top: 20px
			left: 50%
			margin-left: -0.5em

		.certificate-view__lesson-item
			padding: 0 90px

			&:nth-child(odd)
				padding-right: 30px

			&:nth-child(even)
				padding-left: 30px

@media (min-width: 768px)
	.learning .course-section__panel-body-footer-col
		&:first-child
			margin: 0
		&:last-child
			text-align: right

@media (min-width: 768px)
	.learning .course-edit__lessons-btns
		text-align: right

@media (min-width: 768px)
	.learning .assign-users__list
		max-height: 400px

@media (min-width: 992px)
	.learning .assign-users__filter
		margin-bottom: 0

@media (min-width: 768px)
	.learning .input__files--full .input__file
		float: left
		width: 33.3333333333%

		&:nth-child(2n+1)
			clear: none

		&:nth-child(3n+1)
			clear: left


@media (min-width: 992px)
	.learning .input__files--full .input__file
		float: left
		width: 20%

		&:nth-child(3n+1)
			clear: none

		&:nth-child(5n+1)
			clear: left

@media (min-width: 768px)
	.learning .input__files:not(.input__files--full)
		margin-top: 0

@media (min-width: 768px)
	.learning
		.table-controls__date-filters
			text-align: right

		.table-controls__date-filters-col
			width: auto
			display: inline-block
			float: none

@media (min-width: 768px)
	.learning
		.panel-heading--with-buttons .panel-title
			float: left
			margin: 7px 0 0 0

		.panel-heading__buttons
			float: right
