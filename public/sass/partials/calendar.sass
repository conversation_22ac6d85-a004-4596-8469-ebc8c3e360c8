.fc
  direction: ltr
  text-align: left

.fc-rtl
  text-align: right

body .fc
  font-size: 1em

.fc-unthemed
  th, td, thead, tbody, .fc-divider, .fc-row
    border-color: #fff
  .fc-popover
    background-color: #fff
  .fc-divider
    background: #eee
  .fc-popover .fc-header
    background: #e5e5e5
    .fc-close
      background-color: #445e8c
      color: #fff
  td
    border-style: double

    &.fc-mon,
    &.fc-tue,
    &.fc-wed,
    &.fc-thu,
    &.fc-fri
      background: #e5e5e5
    &.fc-sat,
    &.fc-sun
      background: #cdcdcd
    &.fc-today
      background-color: #c1c7d1

.fc-highlight
  background: #bce8f1
  opacity: .3
  filter: alpha(opacity = 30)

.fc-bgevent
  background: rgb(143, 223, 130)
  opacity: .3
  filter: alpha(opacity = 30)

.fc-nonbusiness
  background: #d7d7d7

.fc-icon
  display: inline-block
  width: 1em
  height: 1em
  line-height: 1em
  font-size: 1em
  text-align: center
  overflow: hidden
  font-family: "Courier New", Courier, monospace
  -webkit-touch-callout: none
  -webkit-user-select: none
  -khtml-user-select: none
  -moz-user-select: none
  -ms-user-select: none
  user-select: none
  &:after
    position: relative
    margin: 0 -1em

.fc-icon-left-single-arrow:after
  content: "\02039"
  font-weight: bold
  font-size: 200%
  top: -7%
  left: 3%

.fc-icon-right-single-arrow:after
  content: "\0203A"
  font-weight: bold
  font-size: 200%
  top: -7%
  left: -3%

.fc-icon-left-double-arrow:after
  content: "\000AB"
  font-size: 160%
  top: -7%

.fc-icon-right-double-arrow:after
  content: "\000BB"
  font-size: 160%
  top: -7%

.fc-icon-left-triangle:after
  content: "\25C4"
  font-size: 125%
  top: 3%
  left: -2%

.fc-icon-right-triangle:after
  content: "\25BA"
  font-size: 125%
  top: 3%
  left: 2%

.fc-icon-down-triangle:after
  content: "\25BC"
  font-size: 125%
  top: 2%

.fc-icon-x:after
  content: "\000D7"
  font-size: 200%
  top: 6%

.fc button
  -moz-box-sizing: border-box
  -webkit-box-sizing: border-box
  box-sizing: border-box
  margin: 0
  height: 2.2em
  padding: 0 .6em
  font-size: 1em
  white-space: nowrap
  cursor: pointer
  &::-moz-focus-inner
    margin: 0
    padding: 0
  text-transform: capitalize

.fc-state-default
  border: 0
  &.fc-corner-left
    border-top-left-radius: 1.1em
    border-bottom-left-radius: 1.1em
  &.fc-corner-right
    border-top-right-radius: 1.1em
    border-bottom-right-radius: 1.1em

.fc button .fc-icon
  position: relative
  top: -0.05em
  margin: 0 .2em
  vertical-align: middle

.fc-state-default
  background-color: #445e8c
  color: #fff
  outline: none

.fc-state-hover, .fc-state-down, .fc-state-active, .fc-state-disabled
  background-color: #032862
  color: #fff

.fc-state-hover
  color: #fff
  text-decoration: none
  -webkit-transition: background-position 0.1s linear
  -moz-transition: background-position 0.1s linear
  -o-transition: background-position 0.1s linear
  transition: background-position 0.1s linear

.fc-state-down, .fc-state-active
  background-color: #032862

.fc-state-disabled
  background-color: #dfe1e0
  color: #999
  cursor: default
  opacity: 0.65
  filter: alpha(opacity = 65)

.fc-button-group
  display: inline-block

.fc-loading
  > table
    opacity: 0.2
    filter: alpha(opacity = 20)

.fc-loading-progress
  position: absolute
  top: 0
  right: 0
  bottom: 0
  left: 0
  text-align: center
  z-index: 999
  font-size: 15em
  &:before
    content: ''
    display: inline-block
    height: 100%
    vertical-align: middle

  i
    color: #445e8c
    display: inline-block
    vertical-align: middle

.fc .fc-button-group >
  *
    float: left
    margin: 0 0 0 -1px
  \:first-child
    margin-left: 0

.fc-popover
  position: absolute
  height: 100%
  width: 100%
  top: 0!important
  left: 0!important

  .fc-header
    padding: 2px 4px
    .fc-title
      margin: 0 2px
    .fc-close
      cursor: pointer

.fc-ltr .fc-popover .fc-header .fc-title
  float: left

.fc-rtl .fc-popover .fc-header
  .fc-close
    float: left
  .fc-title
    float: right

.fc-ltr .fc-popover .fc-header .fc-close
  float: right

.fc-unthemed .fc-popover
  .fc-header
    .fc-title
      color: #616265
      font-family: "Rockwell W01"
      padding: .25em

    .fc-close
      border-radius: .8em
      font-size: .9em
      line-height: 1.6em
      margin: .25em
      width: 1.6em
      height: 1.6em

.fc-popover > .ui-widget-header + .ui-widget-content
  border-top: 0

.fc-divider
  border-style: solid
  border-width: 1px

hr.fc-divider
  height: 0
  margin: 0
  padding: 0 0 2px
  border-width: 1px 0

.fc-clear
  clear: both

.fc-bg, .fc-bgevent-skeleton, .fc-highlight-skeleton, .fc-helper-skeleton
  position: absolute
  top: 0
  left: 0
  right: 0

.fc-bg
  bottom: 0
  table
    height: 100%

.fc
  table
    width: 100%
    table-layout: fixed
    border-collapse: collapse
    border-spacing: 0
    font-size: 1em
  th
    text-align: right
    border: 0
    padding: 0 .1em
    vertical-align: top
  td
    border-style: solid
    border-width: 1px
    padding: 0
    vertical-align: top
    &.fc-today
      border-style: double
  .fc-row
    border-style: solid
    border-width: 0

.fc-row
  table
    border-left: 0 hidden transparent
    border-right: 0 hidden transparent
    border-bottom: 0 hidden transparent
  &:first-child table
    border-top: 0 hidden transparent
  position: relative
  .fc-bg
    z-index: 1
  .fc-bgevent-skeleton, .fc-highlight-skeleton
    bottom: 0
  .fc-bgevent-skeleton table
    height: 100%
  .fc-highlight-skeleton
    table
      height: 100%
    td
      border-color: transparent
  .fc-bgevent-skeleton
    td
      border-color: transparent
    z-index: 2
  .fc-highlight-skeleton
    z-index: 3
  .fc-content-skeleton
    position: relative
    z-index: 4
    padding-bottom: 2px
  .fc-helper-skeleton
    z-index: 5
  .fc-content-skeleton td, .fc-helper-skeleton td
    background: none
    border-color: transparent
    border-bottom: 0
  .fc-content-skeleton tbody td, .fc-helper-skeleton tbody td
    border-top: 0

.fc-scroller
  overflow-y: scroll
  overflow-x: hidden
  > *
    position: relative
    width: 100%
    overflow: hidden

.fc-event
  background-color: #fff
  border: 1px solid #445e8c
  position: relative
  display: block
  font-size: .8em
  line-height: 1.3
  font-weight: normal
  color: inherit
  cursor: pointer
  text-decoration: none
  -webkit-touch-callout: none // iOS Safari
  -webkit-user-select: none   // Chrome/Safari/Opera
  -khtml-user-select: none    // Konqueror
  -moz-user-select: none      // Firefox
  -ms-user-select: none       // IE/Edge
  user-select: none           // non-prefixed version, currently not supported by any browser

  &:hover
    text-decoration: none

  &.calendar--re-admin
    color: #fff
    background-color: #d9604d

  &.calendar--holiday
    color: #fff
    background-color: #72ba6c

  &.calendar--survey
    color: #fff
    background-color: #dfaa54

  &.calendar--rereview
    color: #fff
    background-color: #42b7a7

  .fa
    color: #fff
    background-color: #445e8c
    display: inline-block
    margin-right: .1em
    width: 2em
    height: 2em
    line-height: 2em
    text-align: center

.ui-widget .fc-event
  color: #fff
  text-decoration: none

.fc-not-allowed
  cursor: not-allowed
  .fc-event
    cursor: not-allowed

.fc-event
  .fc-bg
    z-index: 1
    background: #fff
    opacity: .25
    filter: alpha(opacity = 25)
  .fc-content
    position: relative
    z-index: 2
  .fc-resizer
    position: absolute
    z-index: 3

.fc-ltr .fc-h-event.fc-not-start, .fc-rtl .fc-h-event.fc-not-end
  margin-left: 0
  border-left-width: 0
  padding-left: 1px
  border-top-left-radius: 0
  border-bottom-left-radius: 0

.fc-ltr .fc-h-event.fc-not-end, .fc-rtl .fc-h-event.fc-not-start
  margin-right: 0
  border-right-width: 0
  padding-right: 1px
  border-top-right-radius: 0
  border-bottom-right-radius: 0

.fc-h-event .fc-resizer
  top: -1px
  bottom: -1px
  left: -1px
  right: -1px
  width: 5px

.fc-ltr .fc-h-event .fc-start-resizer
  right: auto
  cursor: w-resize
  &:before, &:after
    right: auto
    cursor: w-resize

.fc-rtl .fc-h-event .fc-end-resizer
  right: auto
  cursor: w-resize
  &:before, &:after
    right: auto
    cursor: w-resize

.fc-ltr .fc-h-event .fc-end-resizer
  left: auto
  cursor: e-resize
  &:before, &:after
    left: auto
    cursor: e-resize

.fc-rtl .fc-h-event .fc-start-resizer
  left: auto
  cursor: e-resize
  &:before, &:after
    left: auto
    cursor: e-resize

.fc-day-grid-event
  margin: 0 0.5em .25em
  padding: 0
  .fc-content
    white-space: nowrap
    overflow: hidden
    text-overflow: ellipsis
  .fc-time
    font-weight: bold
  .fc-resizer
    left: -3px
    right: -3px
    width: 7px

.fc-more-cell
  text-align: right

a.fc-more
  margin: 0 .5em
  font-size: .85em
  cursor: pointer
  text-decoration: none
  &:hover
    text-decoration: underline

.fc-limited
  display: none

.fc-day-grid .fc-row
  z-index: 1

.fc-more-popover
  z-index: 2

  .fc-event-container
    border: 1px solid #e5e5e5
    margin: 0
    padding: .5em 0 .25em
    position: absolute
    top: 2.1em
    right: 0
    bottom: 0
    left: 0

.fc-now-indicator
  position: absolute
  border: 0 solid red

.fc-toolbar
  text-align: center
  margin-bottom: 1em
  .fc-left
    float: left
  .fc-right
    float: right
  .fc-center
    display: inline-block

.fc .fc-toolbar > * >
  *
    float: left
    margin-left: .75em
  \:first-child
    margin-left: 0

.fc-toolbar
  h2
    color: #032862
    font-size: 1.5em
    line-height: 1em
    font-family: "Rockwell W01"
  button
    position: relative
  .fc-state-hover, .ui-state-hover
    z-index: 2
  .fc-state-down
    z-index: 3
  .fc-state-active, .ui-state-active
    z-index: 4
  button:focus
    z-index: 5

.fc-prev-button,
.fc-next-button
  padding: 0
  width: 2.2em

  .fc &
    padding: 0

.fc-view-container
  position: relative

  *
    -webkit-box-sizing: content-box
    -moz-box-sizing: content-box
    box-sizing: content-box
    &:before, &:after
      -webkit-box-sizing: content-box
      -moz-box-sizing: content-box
      box-sizing: content-box

.fc-view
  position: relative
  z-index: 1
  > table
    position: relative
    z-index: 1

.fc-basicWeek-view .fc-content-skeleton, .fc-basicDay-view .fc-content-skeleton
  padding-top: 1px
  padding-bottom: 1em

.fc-basic-view .fc-body .fc-row
  min-height: 4em

.fc-row.fc-rigid
  overflow: hidden
  .fc-content-skeleton
    position: absolute
    top: 0
    left: 0
    right: 0

.fc-basic-view
  .fc-week-number, .fc-day-number
    padding: .5em
  td
    &.fc-week-number span, &.fc-day-number
      padding: .5em
  .fc-week-number
    text-align: center
    span
      display: inline-block
      min-width: 1.25em

.fc-ltr .fc-basic-view .fc-day-number
  text-align: right

.fc-rtl .fc-basic-view .fc-day-number
  text-align: left

.fc-day-number.fc-other-month
  opacity: 0.3
  filter: alpha(opacity = 30)

.fc-agenda-view .fc-day-grid
  position: relative
  z-index: 2
  .fc-row
    min-height: 3em
    .fc-content-skeleton
      padding-top: 1px
      padding-bottom: 1em

.fc .fc-axis
  vertical-align: middle
  padding: 0 4px
  white-space: nowrap

.fc-ltr .fc-axis
  text-align: right

.fc-rtl .fc-axis
  text-align: left

.ui-widget td.fc-axis
  font-weight: normal

.fc-time-grid-container
  position: relative
  z-index: 1

.fc-time-grid
  position: relative
  z-index: 1
  min-height: 100%
  table
    border: 0 hidden transparent
  > .fc-bg
    z-index: 1
  .fc-slats, > hr
    position: relative
    z-index: 2
  .fc-content-col
    position: relative
  .fc-content-skeleton
    position: absolute
    z-index: 3
    top: 0
    left: 0
    right: 0
  .fc-business-container
    position: relative
    z-index: 1
  .fc-bgevent-container
    position: relative
    z-index: 2
  .fc-highlight-container
    position: relative
    z-index: 3
  .fc-event-container
    position: relative
    z-index: 4
  .fc-now-indicator-line
    z-index: 5
  .fc-helper-container
    position: relative
    z-index: 6
  .fc-slats
    td
      height: 1.5em
      border-bottom: 0
    .fc-minor td
      border-top-style: dotted
    .ui-widget-content
      background: none
  .fc-highlight-container
    position: relative
  .fc-highlight
    position: absolute
    left: 0
    right: 0

.fc-ltr .fc-time-grid .fc-event-container
  margin: 0 2.5% 0 2px

.fc-rtl .fc-time-grid .fc-event-container
  margin: 0 2px 0 2.5%

.fc-time-grid
  .fc-event
    position: absolute
    z-index: 1
  .fc-bgevent
    position: absolute
    z-index: 1
    left: 0
    right: 0

.fc-v-event
  &.fc-not-start
    border-top-width: 0
    padding-top: 1px
    border-top-left-radius: 0
    border-top-right-radius: 0
  &.fc-not-end
    border-bottom-width: 0
    padding-bottom: 1px
    border-bottom-left-radius: 0
    border-bottom-right-radius: 0

.fc-time-grid-event
  overflow: hidden
  .fc-time, .fc-title
    padding: 0 1px
  .fc-time
    font-size: .85em
    white-space: nowrap
  &.fc-short
    .fc-content
      white-space: nowrap
    .fc-time, .fc-title
      display: inline-block
      vertical-align: top
    .fc-time
      span
        display: none
      &:before
        content: attr(data-start)
      &:after
        content: "\000A0-\000A0"
    .fc-title
      font-size: .85em
      padding: 0
  .fc-resizer
    left: 0
    right: 0
    bottom: 0
    height: 8px
    overflow: hidden
    line-height: 8px
    font-size: 11px
    font-family: monospace
    text-align: center
    cursor: s-resize
    &:after
      content: "="

.fc-time-grid
  .fc-now-indicator-line
    border-top-width: 1px
    left: 0
    right: 0
  .fc-now-indicator-arrow
    margin-top: -5px

.fc-ltr .fc-time-grid .fc-now-indicator-arrow
  left: 0
  border-width: 5px 0 5px 6px
  border-top-color: transparent
  border-bottom-color: transparent

.fc-rtl .fc-time-grid .fc-now-indicator-arrow
  right: 0
  border-width: 5px 6px 5px 0
  border-top-color: transparent
  border-bottom-color: transparent

.fc-view-container
  .fc-day
    box-sizing: border-box

@media print
  .fc
    max-width: 100% !important
  .fc-event
    background: #fff !important
    color: #000 !important
    page-break-inside: avoid
    .fc-resizer
      display: none
  th, td, hr, thead, tbody, .fc-row
    border-color: #ccc !important
    background: #fff !important
  .fc-bg, .fc-bgevent-skeleton, .fc-highlight-skeleton, .fc-helper-skeleton, .fc-bgevent-container, .fc-business-container, .fc-highlight-container, .fc-helper-container
    display: none
  .fc tbody .fc-row
    height: auto !important
    min-height: 0 !important
    .fc-content-skeleton
      position: static
      padding-bottom: 0 !important
      tbody tr:last-child td
        padding-bottom: 1em
      table
        height: 1em
  .fc-more-cell, .fc-more
    display: none !important
  .fc
    tr.fc-limited
      display: table-row !important
    td.fc-limited
      display: table-cell !important
  .fc-popover
    display: none
  .fc-time-grid
    min-height: 0 !important
  .fc-agenda-view .fc-axis
    display: none
  .fc-slats
    display: none !important
  .fc-time-grid
    hr
      display: none !important
    .fc-content-skeleton
      position: static
      table
        height: 4em
    .fc-event-container
      margin: 0 !important
    .fc-event
      position: static !important
      margin: 3px 2px !important
      &.fc-not-end
        border-bottom-width: 1px !important
        &:after
          content: "..."
      &.fc-not-start
        border-top-width: 1px !important
        &:before
          content: "..."
      .fc-time
        white-space: normal !important
        span
          display: none
        &:after
          content: attr(data-full)
  .fc-scroller, .fc-day-grid-container, .fc-time-grid-container
    overflow: visible !important
    height: auto !important
  .fc-row
    border: 0 !important
    margin: 0 !important
  .fc-button-group, .fc button
    display: none