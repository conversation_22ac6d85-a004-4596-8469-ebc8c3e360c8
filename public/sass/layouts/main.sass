section
  padding: 30px

  &.stacked
    padding: 15px 30px

#page
  position: relative
  height: 100%
  background: #fff !important
  padding: 0
  min-height: 800px

#wrapper
  -webkit-transition: all 0.5s ease
  -moz-transition: all 0.5s ease
  -o-transition: all 0.5s ease
  transition: all 0.5s ease

#wrapper.toggled
  padding-left: 250px

.modal-backdrop
  display: block
  width: 100%
  height: 100%
  z-index: 999

nav
  z-index: 1000
  position: fixed
  left: 250px
  width: 0
  height: 100%
  margin-left: -250px
  overflow-y: auto
  -webkit-transition: all 0.5s ease
  -moz-transition: all 0.5s ease
  -o-transition: all 0.5s ease
  transition: all 0.5s ease

#wrapper.toggled nav
  width: 250px

#page-content-wrapper
  width: 100%
  position: absolute

#wrapper.toggled #page-content-wrapper
  position: absolute
  margin-right: -250px

.sidebar-nav
  position: absolute
  top: 0
  width: 250px
  list-style: none

#menu-toggle
  display: none
  position: absolute
  z-index: 99
  top: 30px
  left: -35px
  width: 100px
  -moz-transform: rotate(90deg)
  -webkit-transform: rotate(90deg)
  -o-transform: rotate(90deg)
  -ms-transform: rotate(90deg)
  transform: rotate(90deg)
  font-size: 120%
  i
    margin-right: 5px

@media screen and (max-width: 768px)
  #menu-toggle
    display: inline-block


@media screen and (min-width: 768px)
  #wrapper
    padding-left: 250px

  #wrapper.toggled
    padding-left: 0

  nav
    width: 250px

  #wrapper.toggled nav
    width: 0

  #page-content-wrapper
    position: relative

  #wrapper.toggled #page-content-wrapper
    position: relative
    margin-right: 0

@media screen and (max-width: 768px)
  nav
    position: fixed

@media screen and (min-width: 1200px)
  #page
    min-width: 100%
    &.no-nav
      min-width: 1024px
      max-width: 1024px

@media screen and (min-width: 1270px)
  #page
    min-width: 1270px
    &.no-nav
      min-width: 1024px
      max-width: 1024px

table
  tr
    td
      &:last-child
        .btn
          margin-bottom: 4px