<?php

use Illuminate\Support\Facades\Facade;

return [

    /*
    |--------------------------------------------------------------------------
    | Application Name
    |--------------------------------------------------------------------------
    |
    | This value is the name of your application. This value is used when the
    | framework needs to place the application's name in a notification or
    | any other location as required by the application or its packages.
    |
    */

    'name' => env('APP_NAME', 'Laravel'),

    /*
    |--------------------------------------------------------------------------
    | Application Environment
    |--------------------------------------------------------------------------
    |
    | This value determines the "environment" your application is currently
    | running in. This may determine how you prefer to configure various
    | services the application utilizes. Set this in your ".env" file.
    |
    */

    'env' => env('APP_ENV', 'production'),

    /*
    |--------------------------------------------------------------------------
    | Application Debug Mode
    |--------------------------------------------------------------------------
    |
    | When your application is in debug mode, detailed error messages with
    | stack traces will be shown on every error that occurs within your
    | application. If disabled, a simple generic error page is shown.
    |
    */

    'debug' => (bool) env('APP_DEBUG', false),

    /*
    |--------------------------------------------------------------------------
    | Application URL
    |--------------------------------------------------------------------------
    |
    | This URL is used by the console to properly generate URLs when using
    | the Artisan command line tool. You should set this to the root of
    | your application so that it is used when running Artisan tasks.
    |
    */

    'url' => env('APP_URL', 'http://localhost'),

    'asset_url' => env('ASSET_URL'),

    'client_url' => env('FE_URL', 'https://client-stg-k8.libertyriskreduce.com/'),

    /*
    |--------------------------------------------------------------------------
    | Application Timezone
    |--------------------------------------------------------------------------
    |
    | Here you may specify the default timezone for your application, which
    | will be used by the PHP date and date-time functions. We have gone
    | ahead and set this to a sensible default for you out of the box.
    |
    */

    'timezone' => 'UTC',

    /*
    |--------------------------------------------------------------------------
    | Application Locale Configuration
    |--------------------------------------------------------------------------
    |
    | The application locale determines the default locale that will be used
    | by the translation service provider. You are free to set this value
    | to any of the locales which will be supported by the application.
    |
    */

    'locale' => 'en',

    /*
    |--------------------------------------------------------------------------
    | Application Fallback Locale
    |--------------------------------------------------------------------------
    |
    | The fallback locale determines the locale to use when the current one
    | is not available. You may change the value to correspond to any of
    | the language folders that are provided through your application.
    |
    */

    'fallback_locale' => 'en',

    /*
    |--------------------------------------------------------------------------
    | Faker Locale
    |--------------------------------------------------------------------------
    |
    | This locale will be used by the Faker PHP library when generating fake
    | data for your database seeds. For example, this will be used to get
    | localized telephone numbers, street address information and more.
    |
    */

    'faker_locale' => 'en_US',

    /*
    |--------------------------------------------------------------------------
    | Encryption Key
    |--------------------------------------------------------------------------
    |
    | This key is used by the Illuminate encrypter service and should be set
    | to a random, 32 character string, otherwise these encrypted strings
    | will not be safe. Please do this before deploying an application!
    |
    */

    'key' => '2ET2a0PBiqTueXIxqNdiEcagK1VxyUkl',

    'cipher' => 'AES-256-CBC',

    /*
    |--------------------------------------------------------------------------
    | Maintenance Mode Driver
    |--------------------------------------------------------------------------
    |
    | These configuration options determine the driver used to determine and
    | manage Laravel's "maintenance mode" status. The "cache" driver will
    | allow maintenance mode to be controlled across multiple machines.
    |
    | Supported drivers: "file", "cache"
    |
    */

    'maintenance' => [
        'driver' => 'file',
        // 'store'  => 'redis',
    ],

    /*
    |--------------------------------------------------------------------------
    | Autoloaded Service Providers
    |--------------------------------------------------------------------------
    |
    | The service providers listed here will be automatically loaded on the
    | request to your application. Feel free to add your own services to
    | this array to grant expanded functionality to your applications.
    |
    */

    'providers' => [

        /*
         * Laravel Framework Service Providers...
         */
        Illuminate\Auth\AuthServiceProvider::class,
        Illuminate\Broadcasting\BroadcastServiceProvider::class,
        Illuminate\Bus\BusServiceProvider::class,
        Illuminate\Cache\CacheServiceProvider::class,
        Illuminate\Foundation\Providers\ConsoleSupportServiceProvider::class,
        Illuminate\Cookie\CookieServiceProvider::class,
        Illuminate\Database\DatabaseServiceProvider::class,
        Illuminate\Encryption\EncryptionServiceProvider::class,
        Illuminate\Filesystem\FilesystemServiceProvider::class,
        Illuminate\Foundation\Providers\FoundationServiceProvider::class,
        Illuminate\Hashing\HashServiceProvider::class,
        Illuminate\Mail\MailServiceProvider::class,
        Illuminate\Notifications\NotificationServiceProvider::class,
        Illuminate\Pagination\PaginationServiceProvider::class,
        Illuminate\Pipeline\PipelineServiceProvider::class,
        Illuminate\Queue\QueueServiceProvider::class,
        Illuminate\Redis\RedisServiceProvider::class,
        Illuminate\Auth\Passwords\PasswordResetServiceProvider::class,
        Illuminate\Session\SessionServiceProvider::class,
        Illuminate\Translation\TranslationServiceProvider::class,
        Illuminate\Validation\ValidationServiceProvider::class,
        Illuminate\View\ViewServiceProvider::class,

        /*
         * Package Service Providers...
         */

        /*
         * Application Service Providers...
         */
        App\Providers\AppServiceProvider::class,
        App\Providers\AuthServiceProvider::class,
        // App\Providers\BroadcastServiceProvider::class,
        App\Providers\EventServiceProvider::class,
        App\Providers\RouteServiceProvider::class,
        App\Providers\CacheServiceProvider::class,
        Aws\Laravel\AwsServiceProvider::class,
    ],

    /*
    |--------------------------------------------------------------------------
    | Class Aliases
    |--------------------------------------------------------------------------
    |
    | This array of class aliases will be registered when this application
    | is started. However, feel free to register as many as you wish as
    | the aliases are "lazy" loaded so they don't hinder performance.
    |
    */

    'aliases' => Facade::defaultAliases()->merge(
        [
            'AWS'               => Aws\Laravel\AwsFacade::class,
            'Helpers'           => App\Helpers\Helpers::class,
            'RiskGradingHelper' => App\Helpers\RiskGradingHelper::class,
            'CacheService'      => App\Facades\CacheServiceFacade::class,
        ]
    )->toArray(),

    'show_lsm' => true,

    'api' => [
        'endpoint'      => env('API_URL', 'http://risk-reduce-api-e791-stg-srv.risk-reduce-api.svc.cluster.local/'),
        'username'      => 'admin',
        'password'      => 'Password@123'
    ],
    'rackspace' => [
        'username'  =>  'brandformula',
        'apikey'    =>  '71bcff66015e47b9bb8f3512b672e250',
        'region'    =>  'IAD',
        'container' =>  'document_store_test',
        'image_container'   =>  'image_store_test',
        'document_key'  =>  '24D76465B0B88CAF4B15996F93DE4D5DD031ABFB6F4ACC1BDF5B5C0A8BE215FD'
    ],
    'rackspace_cdn' => [
        'images'    =>    [
            'https'    => 'https://7acf5c6229ab88618390-ee34c3278168540d1814d85603de0a44.ssl.cf5.rackcdn.com/',
        ],
    ],
    'aws' => [
        'access_key' => env('AWS_ACCESS_KEY_ID', ''),
        'secret_key' => env('AWS_SECRET_ACCESS_KEY', ''),
        'region' => env('AWS_DEFAULT_REGION', 'eu-west-1'),
        'bucket' => env('AWS_BUCKET', 'liberty-rr-a2be-risk-reduce-stg'),
        'vr_bucket' => env('AWS_VR_BUCKET', 'liberty-rr-a2be-virtual-rooms-stg'),
        'cms_bucket' => env('AWS_CMS_BUCKET', 'lsm-cms-uat'),
        'vr_bucket_transcoded' => env('AWS_VR_TRANCODED_BUCKET', 'liberty-rr-a2be-virtual-rooms-transcoded-stg'),
        'force_cache_rebuild' => env('AWS_USER_CACHE_BUILDING', 'https://sqs.eu-west-1.amazonaws.com/145961640041/force-cache-rebuild-risk-reduce-client-11bc-stg-sqs'),
        'lambda_pdf_url' => 'https://eqrpvisvrlqsco5kteqr4ximim0syqeg.lambda-url.eu-west-1.on.aws/',
        'invalidate_cache_sqs_admin' => env('SQS_INVALIDATE_CACHE_ADMIN', 'cache-invalidation-risk-reduce-admin-stg'),
        'invalidate_cache_sqs_client' => env('SQS_INVALIDATE_CACHE_CLIENT', 'cache-invalidation-risk-reduce-client-stg'),
        'invalidate_cache_sqs_temp' => env('SQS_INVALIDATE_CACHE_CLIENT', 'cache-rebuild-admin-temp'),
    ],

    'cmsForum' => [
        'endpoint' => 'https://www.libertycms.dev/api/',
        'username' => 'libcms19',
        'password' => '4!qJzrxJXxcs',
        'key' => '3b9a73b2-61da-4c5a-be40-2d272eb89000',
        'forum_sector_workspace_id' => '5d498d6f08936701996cc232',
        'forum_sector_content_type_id' => '5ec7ae72476ea2582b4fa712',
        'forum_tag_content_type_id' => '5ec7aebd476ea2582a73e2f3',
    ],

    'twilio' => [
        'statusCallBackUrl' => 'twilio/statuscallback',
        'sms' => [
            'sid' => '**********************************',
            'token' => 'f3eadf1259a3d18a6708a0fa94b09a24',
            'from' => '+447380309735',
            'from-us' => '+18188734569',
        ],
        'video' => [
            'sid' => '**********************************',
            'apiKey' => '**********************************',
            'apiSecret' => 'wpbrpaJEhk15aUoncuFG14cJ2SQjhpIf',
        ],
    ],
    'cyber_virtual_rooms' => [
        'can_access' => [
            '5d36de5bbf3b21131941e7e2',
            '5ef3467031239970014119d2',
            '5f1ab7c5312399348e1b13c2',
            '60f1808b31239915bf3c1962',
            '62878c81476ea208b93890a2',
            '640aec823123993dcd5e1a02'
        ], /*Temporary IDs*/
        'matt_id'     => '60f1808b31239915bf3c1962',
        'jennifer_id' => '640aec823123993dcd5e1a02',
    ],

    /*
	|--------------------------------------------------------------------------
	| Allowed routes
	|--------------------------------------------------------------------------
	|
	| Used for broker user currently, all routes will be 401 unless added here
	|
	*/
    'allowed-routes' => [
        'broker-user' => [
            'dashboard',
			'forms.show',
			'static.iosh-nebosh',
			'static.assessments',
			'static.articles-and-policies',
			'static.awareness-courses',
			'static.access-group',
            'organisation.index',
            'organisation.create',
            'organisation.store',
            'organisation.show',
            'organisation.edit',
            'organisation.update',
            'trades.options', // ajax
            'users.index',
            'users.edit',
            'users.update',
            'users.create',
            'users.store',
            'users.destroy',
            'surveys.index',
            'surveys.create',
            'surveys.store',
            'surveys.show',
            'surveys.edit',
            'surveys.update',
            'surveys.send-message',
            'surveys.message.attachment',
            'surveys.update-surveyor',
            'kanban.index',
            'kanban.risk-recommendations.export',
            'organisation.policy-numbers.show',
            'ri_form.submission.show_pdf',
            'print.pdf',
            'survey.create.upload',
            'surveys.report.risk-recommendation',
            'surveys.report',
            'organisation.document.retrieve',
            'organisation.user.create',
            'organisation.user.store',
            'organisation.user.edit',
            'organisation.user.destroy',
			'ri_form.get_files',
			'ri_form.submission.downloadprintpdfbinder',
			'broker-documents',
            'public_form.show',
            'submission.show',
            'submission.show.pdf',
            'submission.print.pdf',
            'preview.public-form-submission-withmapping',
            'public-form-submission.print.pdf',
            'public-form-submission.show.pdf',
            're-metrics.dashboard',
        ]
    ],
    'allowed_broker_to_create_org' => [1, 2, 72],

    'rg_policy_types' => [
        1 => 'Property',
        2 => 'Casualty',
        4 => 'Equine',
        5 => 'Construction All Risk',
    ],
    'contact_us_email' => '<EMAIL>',
    'can_pause_notifications' => [
        '<EMAIL>',
        // '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
    ],
];
