<?php

return [
    'fas_pi_risk_guidances' => [
        'FAS Risk Guidance' 			   => 8,
		'Professional Lines Risk Guidance' => 10,
    ],
    'hide_vr_buttons' => true,
    'hide_claims_tab' => false,
    'hide_video_tour' => false,
    'video_tour_url'  => 'https://player.vimeo.com/video/642258626?h=a1ca8c6260&badge=0&autopause=0&player_id=0&app_id=58479&title=0&byline=0&portrait=0',
	'learning_and_guidance' => [
        [
            // 'url'       => env('APP_URL') . '/learning/iosh-nebosh',
            'title'     => 'IOSH & NEBOSH',
            'image_url' => env('APP_URL') . '/img/dashboard/v2/astutis_logo.png',
        ],
        [
            // 'url'       => env('APP_URL') . '/learning/course/all',
            'title'     => 'Liberty Learning',
            'image_url' => '',
        ],
        [
            // 'url'       => env('APP_URL').'/simplesaml/saml2/idp/SSOService.php?spentityid=https://libertymutual.safetylearning.co.uk',
            'title'     => 'eLearning',
            'image_url' => env('APP_URL') . '/img/dashboard/v2/access_group.png',
            'image_style' => 'height: 25px;'
        ],
        // [
        //     'url'               => '',
        //     'title'             => 'Risk Guidance',
        //     'image_url'         => '',
        //     'group'             => 'risk_guidance',
        //     'is_group_header'   => true,
        // ],
        [
            // 'url'       => env('APP_URL') . '/risk_guidance/Property+Guidance',
            'title'     => 'Property Guidance',
            'image_url' => '',
            'group'     => 'risk_guidance'
        ],
        [
            // 'url'       => env('APP_URL') . '/risk_guidance/Liability+Guidance',
            'title'     => 'Liability Guidance',
            'image_url' => '',
            'group'     => 'risk_guidance'
        ],
        [
            // 'url'       => env('APP_URL') . '/risk_guidance/Safeguarding+Guidance',
            'title'     => 'Safeguarding Guidance',
            'image_url' => '',
            'group'     => 'risk_guidance'
        ],
        [
            // 'url'       => env('APP_URL') . '/loss-lesson',
            'title'     => 'Loss Lessons',
            'image_url' => '',
        ],
        [
            // 'url'       => 'https://www.libertyriskreduce.com/croner/saml2/idp/SSOService.php?spentityid=wkuk.net:default:entityId&target=https://risk-reduce.croneri.co.uk',
            'title'     => 'Articles and Policies',
            'image_url' => env('APP_URL') . '/img/dashboard/v2/croner_logo.png',
            'image_style' => 'height: 16px;'
        ],

    ],
	'accident_and_claims' => [
        [
            // 'url'       => env('APP_URL') . '/accident-reporting',
            'title'     => 'Accident Reporting',
            'image_url' => '',
        ],
        [
            // 'url'       => env('APP_URL') . '/claims',
            'title'     => 'Claims',
            'image_url' => '',
        ],
        [
            // 'url'       => env('APP_URL') . '/accidents-and-claims-plus',
            'title'     => 'Claims +',
            'image_url' => '',
        ],
        [
            // 'url'       => env('APP_URL') . '/accidents-and-loss-lessons',
            'title'     => 'Loss Lessons',
            'image_url' => '',
        ],
        [
            // 'url'       => env('APP_URL') . '/corpore',
            'title'     => 'Rehabilitation',
            'image_url' => env('APP_URL') . '/img/dashboard/v2/corpore_logo.png',
        ],
    ],
    'tools' => [
        [
            // 'url'       => route('sso.cati'),
            'title'     => 'Building Compliance',
            'image_url' => env('APP_URL') . '/img/dashboard/v2/cati_logo.png',
        ],
        [
            // 'url'       => env('APP_URL') . '/slip-alert',
            'title'     => 'Slip Testing',
            'image_url' => '',
        ],
        [
            // 'url'       => env('APP_URL') . '/manual-handling-analysis',
            'title'     => 'Manual Handling Analysis',
            'image_url' => '',
        ],
        [
            // 'url'       => route('flood-alerts.index'),
            'title'     => 'Flood Alerts',
            'image_url' => env('APP_URL') . '/img/dashboard/v2/previsico_black_solid_logo.jpg',
            'image_style' => 'height: 15px;',
            'is_hidden' => (bool) env('PREVISICO_PRELAUNCH_MODE', false) === true
        ],
    ],
    'health_and_well_being' => [
        [
            // 'url'       => env('APP_URL') . '/iosh-nebosh',
            'title'     => 'IOSH & NEBOSH',
            'image_url' => env('APP_URL') . '/img/dashboard/v2/astutis_logo.png',
        ],
        [
            // 'url'       => env('APP_URL') . '/assessments',
            'title'     => 'Assessments',
            'image_url' => env('APP_URL') . '/img/dashboard/v2/corpore_logo.png',
        ],
        [
            // 'url'       => env('APP_URL') . '/articles-and-policies',
            'title'     => 'Articles and Policies',
            'image_url' => env('APP_URL') . '/img/dashboard/v2/croner_logo.png',
            'image_style' => 'height: 16px;'
        ],
        [
            // 'url'       => env('APP_URL') . '/awareness-courses',
            'title'     => 'Awareness Courses',
            'image_url' => env('APP_URL') . '/img/dashboard/v2/rethink_logo.png',
        ],
        [
            // 'url'       => env('APP_URL') . '/access-group',
            'title'     => 'Access Group',
            'image_url' => env('APP_URL') . '/img/dashboard/v2/access_group.png',
            'image_style' => 'height: 25px;'
        ],
    ],
    'communities' => [
        [
            // 'url'       => env('APP_URL') . '/communities',
            'title'     => 'Find out more',
            'image_url' => '',
        ],
    ],
    'export_options' => [
        [
            'type' => 'format',
            'format' => 'png',
            'label' => 'Export PNG'
        ],
        [
            'type' => 'format',
            'format' => 'csv',
            'label' => 'Export CSV'
        ]
    ],
    'services' => [
        'learning' => [
            [
                // 'url'       => env('APP_URL') . '/learning/iosh-nebosh',
                'title'     => 'IOSH & NEBOSH',
                'image_url' => env('APP_URL') . '/img/dashboard/v2/astutis_logo.png',
                'slug'     => 'lag_iosh_nebosh'
            ],
            [
                // 'url'       => env('APP_URL') . '/learning/course/all',
                'title'     => 'Liberty Learning',
                'image_url' => '',
                'slug'     => 'lag_liberty_learning'
            ],
            [
                // 'url'       => env('APP_URL').'/simplesaml/saml2/idp/SSOService.php?spentityid=https://libertymutual.safetylearning.co.uk',
                'title'     => 'eLearning',
                'image_url' => env('APP_URL') . '/img/dashboard/v2/access_group.png',
                'image_style' => 'height: 25px;',
                'slug'     => 'lag_e_learning'
            ],
            [
                // 'url'       => 'https://www.libertyriskreduce.com/croner/saml2/idp/SSOService.php?spentityid=wkuk.net:default:entityId&target=https://risk-reduce.croneri.co.uk',
                'title'     => 'Articles and Policies',
                'image_url' => env('APP_URL') . '/img/dashboard/v2/croner_logo.png',
                'image_style' => 'height: 16px;',
                'slug'     => 'lag_articles_and_policies'
            ],

        ],
        'risk_guidance' => [
            [
                // 'url'       => env('APP_URL') . '/risk_guidance/Property+Guidance',
                'title'     => 'Property Guidance',
                'image_url' => '',
                'group'     => 'risk_guidance',
                'slug'     => 'lag_property_guidance'
            ],
            [
                // 'url'       => env('APP_URL') . '/risk_guidance/Liability+Guidance',
                'title'     => 'Liability Guidance',
                'image_url' => '',
                'group'     => 'risk_guidance',
                'slug'     => 'lag_liability_guidance'
            ],
            [
                // 'url'       => env('APP_URL') . '/risk_guidance/Safeguarding+Guidance',
                'title'     => 'Safeguarding Guidance',
                'image_url' => '',
                'group'     => 'risk_guidance',
                'slug'     => 'lag_safeguarding_guidance'
            ],
        ],
        'accident_and_claims' => [
            [
                // 'url'       => env('APP_URL') . '/accident-reporting',
                'title'     => 'Accident Reporting',
                'image_url' => '',
                'slug'     => 'aac_accident_reporting'
            ],
            [
                // 'url'       => env('APP_URL') . '/claims',
                'title'     => 'Claims',
                'image_url' => '',
                'slug'     => 'aac_claims'
            ],
            [
                // 'url'       => env('APP_URL') . '/accidents-and-claims-plus',
                'title'     => 'Claims +',
                'image_url' => '',
                'slug'     => 'aac_claims_plus'
            ],
            [
                // 'url'       => env('APP_URL') . '/accidents-and-loss-lessons',
                'title'     => 'Loss Lessons',
                'image_url' => '',
                'slug'     => 'aac_loss_lessons'
            ],
            [
                // 'url'       => env('APP_URL') . '/corpore',
                'title'     => 'Rehabilitation',
                'image_url' => env('APP_URL') . '/img/dashboard/v2/corpore_logo.png',
                'slug'     => 'aac_rehabilitation'
            ],
        ],
        'tools' => [
            [
                // 'url'       => route('sso.cati'),
                'title'     => 'Building Compliance',
                'image_url' => env('APP_URL') . '/img/dashboard/v2/cati_logo.png',
                'slug'      => 'tools_building_compliance'
            ],
            [
                // 'url'       => env('APP_URL') . '/slip-alert',
                'title'     => 'Slip Testing',
                'image_url' => '',
                'slug'     => 'tools_slip_testing'
            ],
            [
                // 'url'       => env('APP_URL') . '/manual-handling-analysis',
                'title'     => 'Manual Handling Analysis',
                'image_url' => '',
                'slug'     => 'tools_manual_handling_analysis'
            ],
            [
                // 'url'       => route('flood-alerts.index'),
                'title'     => 'Flood Alerts',
                'image_url' => env('APP_URL') . '/img/dashboard/v2/previsico_black_solid_logo.jpg',
                'image_style' => 'height: 15px;',
                'is_hidden' => (bool) env('PREVISICO_PRELAUNCH_MODE', false) === true,
                'slug'     => 'tools_flood_alerts'
            ],
        ],
        'health_and_well_being' => [
            [
                // 'url'       => env('APP_URL') . '/iosh-nebosh',
                'title'     => 'IOSH & NEBOSH',
                'image_url' => env('APP_URL') . '/img/dashboard/v2/astutis_logo.png',
                'slug'     => 'hawb_iosh_nebosh'
            ],
            [
                // 'url'       => env('APP_URL') . '/assessments',
                'title'     => 'Assessments',
                'image_url' => env('APP_URL') . '/img/dashboard/v2/corpore_logo.png',
                'slug'     => 'hawb_assessments'
            ],
            [
                // 'url'       => env('APP_URL') . '/articles-and-policies',
                'title'     => 'Articles and Policies',
                'image_url' => env('APP_URL') . '/img/dashboard/v2/croner_logo.png',
                'image_style' => 'height: 16px;',
                'slug'     => 'hawb_articles_and_policies'
            ],
            [
                // 'url'       => env('APP_URL') . '/awareness-courses',
                'title'     => 'Awareness Courses',
                'image_url' => env('APP_URL') . '/img/dashboard/v2/rethink_logo.png',
                'slug'     => 'hawb_awareness_courses'
            ],
            [
                // 'url'       => env('APP_URL') . '/access-group',
                'title'     => 'Access Group',
                'image_url' => env('APP_URL') . '/img/dashboard/v2/access_group.png',
                'image_style' => 'height: 25px;',
                'slug'     => 'hawb_access_group'
            ],
        ],
    ]
];