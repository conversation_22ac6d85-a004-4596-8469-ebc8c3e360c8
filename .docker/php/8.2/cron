*/10 * * * *    cd /var/www/html; curl -s https://XXXXAPI_SERVICEXXXX/api/v1/survey-submission/rr-all?refresh_cache=yes > /dev/null 2>&1
0 */2 * * *     cd /var/www/html; /usr/local/bin/php artisan riskreduce:riskrectrackercache > /dev/null 2>&1
*/30 * * * *    cd /var/www/html; /usr/local/bin/php artisan riskreduce:surveytrackercache > /dev/null 2>&1
* 2 * * *    cd /var/www/html; /usr/local/bin/php artisan riskreduce:build_cache_for_orgs > /dev/null 2>&1
*/30 * * * *    cd /var/www/html; /usr/local/bin/php artisan riskreduce:brokerdashboardcache > /dev/null 2>&1
*/30 * * * *    cd /var/www/html; /usr/local/bin/php artisan riskreduce:build_organisation_cache >> /var/www/html/storage/logs/cron.log
* * * * *    cd /var/www/html; /usr/local/bin/php artisan cache:invalidate_cache >> /var/www/html/storage/logs/cron.log
# * * * * *    cd /var/www/html; /usr/local/bin/php artisan cache:invalidate_cache_temp >> /var/www/html/storage/logs/cron.log
