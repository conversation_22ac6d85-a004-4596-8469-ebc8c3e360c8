version: '3'

services:
  rr.redis:
    container_name: "rr_redis"
    build: ./redis
    restart: always
    ports:
      - 6379:6379
    volumes:
      - '/data'
    networks:
      - rr_development_network 

  rr.webserver:
    container_name: "rr_webserver"
    build: ./php/${PHP_VERSION}
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ../:/var/www/html
    links:
      - "rr.mysql:mysql"
      - "rr.mongodb:mongodb"
    networks:
      - rr_development_network

  rr.mysql:
    container_name: "rr_mysql"
    build: ./mysql/${DB_VERSION}
    ports: 
      - "3306:3306"
    volumes:
      - ./mysql/${DB_VERSION}/rr_mysql:/var/lib/rr_mysql
    environment:
      - MYSQL_ROOT_PASSWORD=root
      - MYSQL_DATABASE=docker
    networks:
      - rr_development_network
      
  rr.mongodb:
    container_name: "rr_mongodb"
    build: ./mongo/${MONGO_VERSION}
    environment:
      - MONGO_DATA_DIR=/data/db
      - MONGO_LOG_DIR=/dev/null
    volumes:
      - ./mongo/${MONGO_VERSION}/rr_mongo:/data/rr_mongo
    ports:
        - 27017:27017
    command: mongod --logpath /dev/null
    networks:
      - rr_development_network
networks:
  rr_development_network:
