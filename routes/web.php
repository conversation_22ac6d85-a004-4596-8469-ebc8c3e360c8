<?php

use App\Http\Controllers\RiskInsights\DashboardController as RiskInsightsDashboardController;
use App\Http\Controllers\RiskInsights\BenchmarkController as RiskInsightsBenchmarkController;
use App\Http\Controllers\RiskInsights\RiskGradingController as RiskInsightsRiskGradingController;
use App\Http\Controllers\RiskInsights\LocationController as RiskInsightsLocationController;
use App\Http\Controllers\RiskInsights\ModalController as RiskInsightsModalController;
use App\Http\Controllers\RiskInsights\BoardController as RiskInsightsBoardController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\OrganisationSubmissionLinksController;
use App\Http\Controllers\OrganisationSubmissionsController;
use App\Http\Controllers\OrganisationController;
use App\Http\Controllers\UploadBorderauController;
use App\Http\Controllers\PublicFormSubmissionsController;
use App\Http\Controllers\PublicFormsController;
use App\Http\Controllers\FormsController;
use App\Http\Controllers\FormSubmissionsController;
use App\Http\Controllers\RiskImprovementFormController;
use App\Http\Controllers\RiskRecTitleController;
use App\Http\Controllers\DocumentLibController;
use App\Http\Controllers\LinkController;
use App\Http\Controllers\AccidentReportingController;
use App\Http\Controllers\CyberVR\CyberGuestController;
use App\Http\Controllers\CyberVR\CyberVirtualRoomsController;
use App\Http\Controllers\MgaSchemeController;
use App\Http\Controllers\StatisticsController;
use App\Http\Controllers\EnquiryController;
use App\Http\Controllers\LearningCategoryController;
use App\Http\Controllers\SurveyController;
use App\Models\FileUpload;
use App\Http\Controllers\CsrMicrosite\MicrositeController;
use App\Http\Controllers\CsrMicrosite\TemplateController;
use App\Http\Controllers\DtrMicrosite\DtrMicrositeController;
use App\Http\Controllers\ReMetricsController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// PATTERNS
//Route::pattern('id', '\d+');

//$user = (object) [
//'id' => 7,
//'login_type' => 'risk-control',
//'role' => 'admin',
//'email' => '<EMAIL>',
//'type' => 'risk-engineer',
//'first_name' => 'shashi',
//'last_name' => 'surav'
//];
//Session::put('user', $user);

//healthcheck
Route::get('/healthcheck', function () {
    return response()->json([
        'status' => 200,
        'message' => 'OK'
    ]);
});

// @NOTE Decommissioned
// Virtual Rooms Magic Link
// Route::get('virtual-rooms/login', ['as' => 'virtual-rooms.login', 'uses' => 'LetsTalk\AuthenticateUserController@login']);
// Route::get('virtual-rooms/customer-login', ['as' => 'virtual-rooms.customer-login', 'uses' => 'LetsTalk\AuthenticateUserController@customerLogin']);
// Route::post('virtual-rooms/generateLink', ['as' => 'virtual-rooms.generateLink', 'uses' => 'LetsTalk\AuthenticateUserController@generateLink']);
// Route::post('virtual-rooms/customer-generateLink', ['as' => 'virtual-rooms.customer-generateLink', 'uses' => 'LetsTalk\AuthenticateUserController@customerGenerateLink']);
// Route::get('virtual-rooms/customer-email-sent', ['as' => 'virtual-rooms.customer-email-sent', 'uses' => 'LetsTalk\AuthenticateUserController@customerEmailSent']);
// Route::get('virtual-rooms/authenticate/{token}', ['as' => 'virtual-rooms.authenticate', 'uses' => 'LetsTalk\AuthenticateUserController@authenticate']);
// Route::get('virtual-rooms/cookie-check/{room_id}', ['as' => 'virtual-rooms.cookie-check', 'uses' => 'LetsTalk\SocialDashboardController@cookieCheck']);
// Route::get('virtual-rooms/auth-check', ['as' => 'virtual-rooms.auth-check', 'uses' => 'LetsTalk\SessionCheckerController@authCheck']);
// Route::any('virtual-rooms/session-destroy', function () {
//     Session::flush();

//     return Redirect::to('virtual-rooms/login');
// });

// @NOTE Decommissioned
// Route::any('virtual-rooms/external-my-message/{encoded_id}', [
//     'as' => 'virtual-rooms.external-my-message', 'uses' => 'LetsTalk\PublicCustomerVideoRecordController@viewMyMessage',
// ]);

Route::get('/community/unsubscribe-email/{token}', 'SubscriptionController@unsubscribeCommunityEmail');
Route::get('/community/unsubscribe-email/{token}/confirm', 'SubscriptionController@confirmUnsubscribeCommunityEmail');

// @NOTE Decommissioned
// Route::middleware(['auth-socials-customer'])->group( function () {
//     Route::get('virtual-rooms/customer-my-messages', ['as' => 'virtual-rooms.customer-my-messages', 'uses' => 'LetsTalk\CustomerMyMessagesController@index']);
//     Route::get('virtual-rooms/customer-my-message/{id}', ['as' => 'virtual-rooms.customer-single-view-message', 'uses' => 'LetsTalk\CustomerMyMessagesController@viewMyMessage']);
//     Route::get('virtual-rooms/customer-dashboard', ['as' => 'virtual-rooms.customer-dashboard', 'uses' => 'LetsTalk\SocialCustomerDashboardController@home']);
//     Route::get('virtual-rooms/customer-team-meeting-rooms/{room}', ['as' => 'virtual-rooms.customer-team-meeting-rooms.space', 'uses' => 'LetsTalk\SocialTeamMeetingController@showSpace']);
//     Route::get('virtual-rooms/customer-check-notifications', ['as' => 'virtual-rooms.customer-check-notifications', 'uses' => 'LetsTalk\CustomerMyMessagesController@checkNotifications']);
//     Route::post('virtual-rooms/customer-update-notification', ['as' => 'virtual-rooms.update-check-notification', 'uses' => 'LetsTalk\CustomerMyMessagesController@updateNotification']);
// });

Route::group(['prefix' => 'virtual-rooms/cyber-vr', 'as' => 'cyber-vr.'], function () {
    Route::get('/login', [CyberGuestController::class, 'login'])->name('login');
    Route::post('/generateLink', [CyberGuestController::class, 'generateLink'])->name('generateLink');
    Route::get('/guests/authenticate', [CyberGuestController::class, 'authenticate'])->name('authenticate');
});

Route::group(['before' => ['can-access-guest-cyber-vr'], 'as' => 'virtual-rooms.'], function () {
    Route::get('virtual-rooms/cyber-vr/guests', [CyberGuestController::class, 'index'])->name('cyber-vr');
    Route::get('virtual-rooms/cyber-vr/guests/{roomCode}', [CyberGuestController::class, 'space'])->name('cyber-vr.space');
    Route::get('virtual-rooms/cyber-vr/{room_type_id}/{room_id}/download-ics', [CyberGuestController::class, 'downloadIcs'])->name('cyber-vr.download-ics');
});

Route::middleware(['auth-socials'])->group( function () {
    // Route::get('virtual-rooms/dashboard', ['as' => 'virtual-rooms.dashboard', 'uses' => 'LetsTalk\SocialDashboardController@home']);
    // Route::get('virtual-rooms/get-external-contacts', ['as' => 'virtual-rooms.get-external-contacts', 'uses' => 'LetsTalk\SocialDashboardController@getExternalContacts']);
    // Route::get('virtual-rooms/my-availability', ['as' => 'virtual-rooms.my-availabiltiy', 'uses' => 'LetsTalk\SocialMyAvailabilityController@index']);
    // Route::post('virtual-rooms/my-availability', ['as' => 'virtual-rooms.my-availabiltiy', 'uses' => 'LetsTalk\SocialMyAvailabilityController@index']);
    // Route::post('virtual-rooms/social-rooms', ['as' => 'virtual-rooms.social-rooms', 'uses' => 'LetsTalk\SocialDashboardController@socialRooms']);
    // Route::post('virtual-rooms/join-room', ['as' => 'virtual-rooms.join-room', 'uses' => 'LetsTalk\SocialDashboardController@joinRoom']);
    // Route::post('virtual-rooms/room-creation', ['as' => 'virtual-rooms.room-creation', 'uses' => 'LetsTalk\SocialThemeRoomsController@roomCreation']);
    // Route::get('virtual-rooms/{approved_by}/{room_id}/{approval_type}/room-approval', ['as' => 'virtual-rooms.room-approval', 'uses' => 'LetsTalk\SocialThemeRoomsController@roomApproval']);

    // Route::post('virtual-rooms/theme-rooms', ['as' => 'virtual-rooms.theme-rooms', 'uses' => 'LetsTalk\SocialThemeRoomsController@store']);
    // Route::get('virtual-rooms/theme-rooms', ['as' => 'virtual-rooms.theme-rooms', 'uses' => 'LetsTalk\SocialThemeRoomsController@index']);
    // Route::get('virtual-rooms/room-approval-status', ['as' => 'virtual-rooms.room-approval-status', 'uses' => 'LetsTalk\SocialThemeRoomsController@roomApprovalStatus']);
    // Route::delete('virtual-rooms/theme-rooms/{room_id}', ['as' => 'virtual-rooms.theme-rooms.delete', 'uses' => 'LetsTalk\SocialThemeRoomsController@deleteSpace']);
    // Route::post('virtual-rooms/theme-rooms/delete-schedule', ['as' => 'virtual-rooms.theme-rooms.delete-schedule', 'uses' => 'LetsTalk\SocialThemeRoomsController@deleteSchedule']);
    // Route::get('virtual-rooms/cyber-virtual-rooms/{room_code}', ['as' => 'virtual-rooms.cyber-virtual-rooms.space', 'uses' => 'LetsTalk\SocialThemeRoomsController@showSpace']);

    // Route::get('virtual-rooms/theme-rooms/{room_code}', ['as' => 'virtual-rooms.theme-rooms.space', 'uses' => 'LetsTalk\SocialThemeRoomsController@showSpace']);
    // Route::get('virtual-rooms/communities', ['as' => 'virtual-rooms.communities', 'uses' => 'LetsTalk\SocialThemeRoomsController@communities']);
    // Route::get('virtual-rooms/sync-communities', ['as' => 'virtual-rooms.sync-communities', 'uses' => 'LetsTalk\SocialThemeRoomsController@syncCommunities']);
    // Route::get('virtual-rooms/togglepin/{room_id}', ['as' => 'virtual-rooms.togglepin', 'uses' => 'LetsTalk\SocialThemeRoomsController@togglepin']);
    // Route::get('virtual-rooms/community/{room_code}', ['as' => 'virtual-rooms.community.space', 'uses' => 'LetsTalk\SocialThemeRoomsController@showSpace']);
    // Route::get('virtual-rooms/check-dot-notifications', ['as' => 'virtual-rooms.check-dot-notifications', 'uses' => 'LetsTalk\SocialDotNotificationsController@getDotNotification']);
    // Route::post('virtual-rooms/dismiss-theme-room-dot-notifications', ['as' => 'virtual-rooms.dismiss-theme-room-dot-notifications', 'uses' => 'LetsTalk\SocialDotNotificationsController@dismissThemeRoomNotification']);
    // Route::post('virtual-rooms/dismiss-theme-room-dot-notification-message', ['as' => 'virtual-rooms.dismiss-theme-room-dot-notification-message', 'uses' => 'LetsTalk\SocialDotNotificationsController@dismissThemeRoomNotificationMessage']);

    // Route::post('virtual-rooms/slt-rooms', ['as' => 'virtual-rooms.slt-rooms', 'uses' => 'LetsTalk\SocialSltRoomsController@store']);
    // Route::get('virtual-rooms/slt-rooms/spaces', ['as' => 'virtual-rooms.slt-rooms.spaces', 'uses' => 'LetsTalk\SocialSltRoomsController@viewAllByCreator']);
    // Route::get('virtual-rooms/slt-rooms/with/{person_id}', ['as' => 'virtual-rooms.slt-rooms.with', 'uses' => 'LetsTalk\SocialSltRoomsController@showSltWith']);
    // Route::get('virtual-rooms/slt-rooms/{room_code}', ['as' => 'virtual-rooms.slt-rooms.space', 'uses' => 'LetsTalk\SocialSltRoomsController@showSpace']);
    // Route::post('virtual-rooms/slt-rooms/{room_id}/create-schedule', ['as' => 'virtual-rooms.slt-rooms.create-schedule', 'uses' => 'LetsTalk\SocialSltRoomsController@createSchedule']);
    // Route::delete('virtual-rooms/slt-rooms/{room_id}/delete', ['as' => 'virtual-rooms.slt-rooms.delete', 'uses' => 'LetsTalk\SocialSltRoomsController@deleteSpace']);
    // Route::post('virtual-rooms/slt-rooms/delete-schedule', ['as' => 'virtual-rooms.slt-rooms.delete-schedule', 'uses' => 'LetsTalk\SocialSltRoomsController@deleteSchedule']);

    // Route::get('virtual-rooms/{room_type_id}/{room_id}/download-ics', ['as' => 'virtual-rooms.social-rooms.download-ics', 'uses' => 'LetsTalk\SocialRoomScheduleController@downloadIcs']);

    // // Discussion
    // Route::group(['prefix' => '/virtual-rooms/discussion/'], function () {
    //     Route::resource('reply', 'LetsTalk\SocialRoomMessageController');

    //     Route::group(['prefix' => 'reply/'], function () {
    //         Route::post('store', ['as' => 'virtual-rooms.discussion.reply.store', 'uses' => 'LetsTalk\SocialRoomMessageController@store']);
    //         Route::post('{id}/update', ['as' => 'virtual-rooms.discussion.reply.update', 'uses' => 'LetsTalk\SocialRoomMessageController@update']);
    //         Route::delete('{id}/delete', ['as' => 'virtual-rooms.discussion.reply.delete', 'uses' => 'LetsTalk\SocialRoomMessageController@delete']);
    //         Route::put('{id}/like', ['as' => 'virtual-rooms.discussion.like', 'uses' => 'LetsTalk\SocialRoomMessageController@like']);
    //         Route::get('{id}', ['as' => 'virtual-rooms.discussion.reply.show', 'uses' => 'LetsTalk\SocialRoomMessageController@show']);
    //         Route::post('store-content-image', ['as' => 'virtual-rooms.discussion.store-content-image', 'uses' => 'LetsTalk\SocialRoomMessageController@storeContentImage']);
    //         Route::post('update-content-image', ['as' => 'virtual-rooms.discussion.update-content-image', 'uses' => 'LetsTalk\SocialRoomMessageController@updateContentImage']);
    //     });

    //     Route::group(['prefix' => 'file/'], function () {
    //         Route::post('upload', ['as' => 'virtual-rooms.discussion.uploadfile', 'uses' => 'LetsTalk\SocialRoomMessageController@uploadFile']);
    //         Route::delete('{id}/delete', ['as' => 'virtual-rooms.discussion.deletefile', 'uses' => 'LetsTalk\SocialRoomMessageController@deleteFile']);
    //         Route::get('{id}', ['as' => 'virtual-rooms.discussion.linkfile', 'uses' => 'LetsTalk\SocialRoomMessageController@linkFile']);
    //     });
    // });

    // Cyber Virtual Room
    Route::group(['before' => 'can-access-cyber-vr', 'prefix' => '/virtual-rooms/cyber-virtual-rooms/'], function () {
        Route::get('/', [CyberVirtualRoomsController::class, 'index'])->name('virtual-rooms.cyber-virtual-rooms');
    });

    // Route::get('virtual-rooms/water-cooler-representatives/{page?}/{search?}/{person_id?}', ['as' => 'virtual-rooms.water-cooler-representatives', 'uses' => 'LetsTalk\SocialDashboardController@waterCoolerRepresentatives']);

    // Route::post('virtual-rooms/dashboard-announcements', ['as' => 'virtual-rooms.dashboard-announcements', 'uses' => 'LetsTalk\SocialDashboardController@announcementsForDashboard']);

    // Route::get('virtual-rooms/announcements/index',
    //     ['as' => 'virtual-rooms.announcements.index', 'uses' => 'LetsTalk\AnnouncementController@index']);

    // Route::get('virtual-rooms/announcements/for-space',
    //     ['as' => 'virtual-rooms.announcements.for-space', 'uses' => 'LetsTalk\AnnouncementController@announcementsForSpace']);

    // Route::post('virtual-rooms/chat-with-me', ['as' => 'virtual-rooms.chat-with-me', 'uses' => 'LetsTalk\SocialDashboardController@chatWithMe']);

    // Route::get('virtual-rooms/announcements/archive',
    //     ['as' => 'virtual-rooms.announcements.archive', 'uses' => 'LetsTalk\AnnouncementController@archive']);
    // Route::get('virtual-rooms/announcements/{announcement_id}',
    //     ['as' => 'virtual-rooms.show-announcement', 'uses' => 'LetsTalk\AnnouncementController@show']);
    // Route::get('virtual-rooms/announcements/{announcement_id}/related',
    //     ['as' => 'virtual-rooms.show-related-announcement', 'uses' => 'LetsTalk\AnnouncementController@showRelatedAnnouncements']);

    // Route::post('virtual-rooms/catch-up-rooms', ['as' => 'virtual-rooms.catch-up-rooms', 'uses' => 'LetsTalk\SocialCatchupController@store']);
    // Route::get('virtual-rooms/catch-up-rooms/upcoming', ['as' => 'virtual-rooms.catch-up-rooms.upcoming', 'uses' => 'LetsTalk\SocialCatchupController@getUpcomingCatchups']);
    // Route::get('virtual-rooms/catch-up-rooms/manage', ['as' => 'virtual-rooms.catch-up-rooms.manage', 'uses' => 'LetsTalk\SocialCatchupController@showManage']);
    // Route::get('virtual-rooms/catch-up-rooms/representatives', ['as' => 'virtual-rooms.catch-up-rooms.representatives', 'uses' => 'LetsTalk\SocialCatchupController@getRepresentatives']);
    // Route::get('virtual-rooms/catch-up-rooms/{room}', ['as' => 'virtual-rooms.catch-up-rooms.space', 'uses' => 'LetsTalk\SocialCatchupController@showSpace']);
    // Route::post('virtual-rooms/catch-up-rooms/delete-schedule', ['as' => 'virtual-rooms.catch-up-rooms.delete-schedule', 'uses' => 'LetsTalk\SocialCatchupController@deleteSchedule']);
    // Route::delete('virtual-rooms/catch-up-rooms/{room_id}', ['as' => 'virtual-rooms.catch-up-rooms.delete', 'uses' => 'LetsTalk\SocialCatchupController@deleteSpace']);
    // Route::get('virtual-rooms/catch-up-rooms/with/{person_id}', ['as' => 'virtual-rooms.catch-up-rooms.with', 'uses' => 'LetsTalk\SocialCatchupController@showCatchupWith']);

    // Route::post('virtual-rooms/social-room-schedule', ['as' => 'virtual-rooms.social-room-schedule.store', 'uses' => 'LetsTalk\SocialRoomScheduleController@store']);
    // Route::get('virtual-rooms/delete-schedule/{room_id}', ['as' => 'virtual-rooms.delete-schedule', 'uses' => 'LetsTalk\SocialRoomScheduleController@deleteSchedule']);

    // Route::post('virtual-rooms/team-meeting-rooms', ['as' => 'virtual-rooms.team-meeting-rooms', 'uses' => 'LetsTalk\SocialTeamMeetingController@store']);
    // Route::get('virtual-rooms/team-meeting-rooms/upcoming', ['as' => 'virtual-rooms.team-meeting-rooms.upcoming', 'uses' => 'LetsTalk\SocialTeamMeetingController@getUpcomingTeamMeetings']);
    // Route::get('virtual-rooms/team-meeting-rooms/{id}/social-room', ['as' => 'virtual-rooms.team-meeting-rooms.social-room', 'uses' => 'LetsTalk\SocialTeamMeetingController@getSocialRoomWithId']);
    // Route::post('virtual-rooms/team-meeting-rooms/{id}/edit-social-room', ['as' => 'virtual-rooms.team-meeting-rooms.edit-social-room', 'uses' => 'LetsTalk\SocialTeamMeetingController@editTeamMeeting']);
    // Route::post('virtual-rooms/team-meeting-rooms/{id}/edit-room-title', ['as' => 'virtual-rooms.team-meeting-rooms.edit-room-title', 'uses' => 'LetsTalk\SocialTeamMeetingController@editRoomTitleMeeting']);
    // Route::get('virtual-rooms/team-meeting-rooms/manage', ['as' => 'virtual-rooms.team-meeting-rooms.manage', 'uses' => 'LetsTalk\SocialTeamMeetingController@showManage']);
    // Route::get('virtual-rooms/team-meeting-rooms/representatives', ['as' => 'virtual-rooms.team-meeting-rooms.representatives', 'uses' => 'LetsTalk\SocialTeamMeetingController@getRepresentatives']);
    // Route::get('virtual-rooms/team-meeting-rooms/brokers', ['as' => 'virtual-rooms.team-meeting-rooms.brokers', 'uses' => 'LetsTalk\SocialTeamMeetingController@getBrokers']);
    // Route::get('virtual-rooms/team-meeting-rooms/{room}', ['as' => 'virtual-rooms.team-meeting-rooms.space', 'uses' => 'LetsTalk\SocialTeamMeetingController@showSpace']);

    // Route::get('virtual-rooms/my-messages', ['as' => 'virtual-rooms.my-messages', 'uses' => 'LetsTalk\MyMessagesController@index']);
    // Route::post('virtual-rooms/video-messages', ['as' => 'virtual-rooms.video-messages', 'uses' => 'LetsTalk\MyMessagesController@videoMessages']);
    // Route::post('virtual-rooms/customer-video-messages', ['as' => 'virtual-rooms.customer-video-messages', 'uses' => 'LetsTalk\CustomerMyMessagesController@videoMessages']);
    // Route::post('virtual-rooms/update-notification', ['as' => 'virtual-rooms.update-notification', 'uses' => 'LetsTalk\MyMessagesController@updateNotification']);
    // Route::get('virtual-rooms/check-notifications', ['as' => 'virtual-rooms.check-notifications', 'uses' => 'LetsTalk\MyMessagesController@checkNotifications']);
    // Route::get('virtual-rooms/my-message/{id}', ['as' => 'virtual-rooms.single-view-message', 'uses' => 'LetsTalk\MyMessagesController@viewMyMessage']);
    // Route::get('virtual-rooms/download-my-message/{id}', ['as' => 'virtual-rooms.download-message', 'uses' => 'LetsTalk\MyMessagesController@downloadMessage']);
    // Route::get('virtual-rooms/video-record/my-messages-members', ['as' => 'virtual-rooms.my-message-members', 'uses' => 'LetsTalk\MyMessagesController@getMyMessageMembers']);
    // Route::get('virtual-rooms/my-members', ['as' => 'virtual-rooms.my-members', 'uses' => 'LetsTalk\MyMessagesController@getMyMessageMembers']);

    // Social Room Polls
    // Route::post('virtual-rooms/social-rooms/{roomCode}/polls', ['as' => 'virtual-rooms.social-rooms.polls.create', 'uses' => 'LetsTalk\SocialRoomPollsController@store']);
    // Route::put('virtual-rooms/social-rooms/{roomCode}/polls/{pollId}', ['as' => 'virtual-rooms.social-rooms.polls.update', 'uses' => 'LetsTalk\SocialRoomPollsController@update']);
    // Route::delete('virtual-rooms/social-rooms/{roomCode}/polls/{pollId}', ['as' => 'virtual-rooms.social-rooms.polls.delete', 'uses' => 'LetsTalk\SocialRoomPollsController@delete']);
    // Route::post('virtual-rooms/social-rooms/{roomCode}/polls/{pollId}/votes', ['as' => 'virtual-rooms.social-rooms.polls.vote.create', 'uses' => 'LetsTalk\SocialRoomPollVotesController@store']);
});

Route::get('virtual-rooms/footer', 'LetsTalk\SocialFooterController@index');

Route::pattern('user_type', '(liberty-user|external-surveyor|broker-user|dms-user)');
Route::pattern('kanban_type', '(surveys|risk-recommendations)');
Route::pattern('schedule_type', '[a-z0-9-]+');
Route::get('login/{user_type?}', ['as' => 'login', 'uses' => 'AuthController@login']);
Route::post('login/{user_type?}', ['before' => 'csrf', 'uses' => 'AuthController@login']);
Route::post('mobile/login/{user_type?}', ['before' => 'csrf', 'uses' => 'AuthController@mobilelogin']);

Route::post('dms/login/{user_type?}', ['before' => 'csrf', 'uses' => 'AuthController@dmslogin']);

Route::get('logout', 'AuthController@logout');
Route::match(['GET', 'POST'], 'password-reset/{user_type?}', ['as' => 'password-reset', 'uses' => 'AuthController@passwordReset']);
Route::post('password-reset-code/{user_type?}', ['as' => 'password-reset-code', 'uses' => 'AuthController@passwordResetCode']);
Route::match(['GET', 'POST'], 'activate/{user_type?}', ['as' => 'activate', 'uses' => 'AuthController@activate']);

Route::get('two-factor-create', ['as' => 'two-factor-create', 'uses' => 'AuthController@handleNew2FA']);
Route::post('two-factor-create', ['as' => 'two-factor-verify', 'uses' => 'AuthController@checkNewVerify']);
Route::get('two-factor-verify', ['uses' => 'AuthController@handleVerify']);
Route::post('two-factor-verify', ['as' => 'two-factor-verify', 'uses' => 'AuthController@checkVerify']);
Route::get('two-factor-skip', ['uses' => 'AuthController@twoFactorSkip']);

Route::post('mobile/risk-improvement/form/upload', ['as' => 'ri_form.upload', 'uses' => 'SurveySubmissionsController@mobile_add_attachment']);

Route::post('mobile/risk-improvement/form/submission/addcaption', ['as' => 'ri_form.mobileaddcaption', 'uses' => 'SurveySubmissionsController@mobile_addCaption']);
Route::post('mobile/risk-improvement/form/submission/addannotation', ['as' => 'ri_form.mobileaddannotations', 'uses' => 'SurveySubmissionsController@mobile_addAnnotation']);
Route::post('mobile/risk-improvement/form/submission/addgeolocation', ['as' => 'ri_form.mobileaddgeolocation', 'uses' => 'SurveySubmissionsController@mobile_addGeolocation']);

Route::get('mobile/risk-improvement/form/upload', ['as' => 'ri_form.get_files', 'uses' => 'SurveySubmissionsController@mobile_add_attachment']);

Route::post('mobile/risk-improvement/form/save', ['as' => 'ri_form.sdsdsd', 'uses' => 'SurveySubmissionsController@mobile_csubmission']);

Route::get('mobile/risk-improvement/form/surveyor/{id}', ['as' => 'ri_form.index', 'uses' => 'RiskImprovementFormController@mobile_surveyorForms']);
Route::get('mobile/risk-improvement/form/{form_id}', ['as' => 'ri_form.form', 'uses' => 'RiskImprovementFormController@mobile_surveyorForm']);

Route::get('mobile/surveys/surveyor/{id}/{string?}', ['as' => 'surveys.get-surveys', 'uses' => 'SurveyController@mobile_getSurveysForSurveyors']);

Route::get('mobile/surveys/link/{cloud_file_name}/{file_name}', ['as' => 'surveys.get-link', 'uses' => 'SurveyController@mobile_getDownloadLink']);

Route::get('mobile/surveys/{id}', ['as' => 'surveys.get-surveys', 'uses' => 'SurveyController@mobile_getSurvey']);
Route::get('mobile/survey-forms/all', ['as' => 'surveys.all-survey-forms', 'uses' => 'RiskImprovementFormController@mobile_getSurveyforms']);
Route::get('mobile/risk-recommendation/titles', ['as' => 'surveys.get-titles', 'uses' => 'RiskRecTitleController@mobile_show']);

Route::get('mobile/risk-improvement/submission/{submission_id}', ['as' => 'ri_form.submission', 'uses' => 'SurveySubmissionsController@mobile_submission']);

//Document Management System

Route::post('dms/dms-organisations', ['as' => 'dms.get-organisations', 'uses' => 'DocumentManagementController@GetAllOrganisations']);
Route::post('dms/{id}', ['as' => 'dms.get-organisation-by-id', 'uses' => 'DocumentManagementController@GetOrganisationDetailsForDMS']);

Route::post('/email-hook/{brand?}', 'SubscriptionController@receiveEmail');

Route::group(['prefix' => 'rhs-organisations'], function () {
    Route::post('/', 'RhsOrganisationController@store');
});

Route::group(['prefix' => 'cqlive'], function () {
    //CQLive
    Route::post('store-payload', 'CQLive\CQLiveController@storePayload');
});

Route::get('/organisation/form/{_id}', ['as' => 'organisation.form', 'uses' => 'OrganisationSubmissionsController@showForm']);
Route::post('/organisation/form/{_id}', 'OrganisationSubmissionsController@submitForm');

Route::get('/file/{id}/{type?}', function ($id, $type = 'client') {
    $fileUpload = new FileUpload();
    if ($type == 'broker') {
        $id = 'liberty_users_images/'.$id;
    }

    return $fileUpload->link($id);
});

/*
 * Temp Routes
 */

// Route::post('risk-improvement/form/upload', array('as' => 'ri_form.upload', 'uses' => 'SurveySubmissionsController@add_attachment'));
// Route::get('risk-improvement/form/upload', array('as' => 'ri_form.get_files', 'uses' => 'SurveySubmissionsController@add_attachment'));
Route::get('surveys/{survey_id}/delete/{attachment_id}', ['as' => 'ri_form.delete_files', 'uses' => 'SurveySubmissionsController@delete_attachment']);
// Route::post('risk-improvement/form/save', array('as' => 'ri_form.sdsdsd', 'uses' => 'SurveySubmissionsController@csubmission'));
// Route::get('risk-improvement/form/surveyor/{id}', array('as' => 'ri_form.index', 'uses' => 'RiskImprovementFormController@surveyorForms'));
// Route::get('surveys/surveyor/{id}', array('as' => 'surveys.get-surveys', 'uses' => 'SurveyController@getSurveysForSurveyors'));

// Previsico Flood Tiles API Proxy
Route::group(['before' => ['auth']], function () {
    Route::get(
        'previsico/floodmap/{year}/{month}/{day}/{issueHour}/{floodHour}/{z}/{x}/{y}',
        [
            'as' => 'flood-alerts.tiles',
            'uses' => 'FloodAlertsController@getTile',
        ]
    );
    // Route::get('/regenerate-csrf', function() {
    //     Session::regenerate();
    //     return response()->json(['token' => csrf_token()]);
    // });
    Route::get('/refresh-csrf', function() {
        return response()->json(['token' => csrf_token()]);
    });
});

Route::group(['middleware' => ['auth', 'allowed-routes']], function () {
    
    Route::get('suppression/{type}', ['as' => 'suppression', 'uses' => 'SubscriptionController@suppressions']);
    Route::get('suppression/email/delete/{token}', ['as' => 'suppression.delete', 'uses' => 'SubscriptionController@deleteSuppression']);

    Route::post('register-notification-token', ['as' => 'register-notification-token', 'uses' => 'AuthController@registerNotificationToken']);

    Route::post('sha1', ['as' => 'sha1', 'uses' => 'AuthController@sha1']);

    Route::get('rhs-pdf', ['as' => 'affiliates.rhs.pdf', 'uses' => 'RhsExportController@generatePdf']);
    Route::post('rhs-pdf', ['as' => 'affiliates.rhs.pdf', 'uses' => 'RhsExportController@generatePdf']);

    Route::get('rr-inspire', ['as' => 'rr-inspire', 'uses' => 'AuthController@rrInspireLogin']);

    Route::post('legacy-risk-improvement/form/upload', ['as' => 'ri_form.upload', 'uses' => 'LegacySurveySubmissionsController@add_attachment']);

    Route::post('risk-improvement/form/upload', ['as' => 'ri_form.upload', 'uses' => 'SurveySubmissionsController@add_attachment']);
    Route::get('risk-improvement/form/upload', ['as' => 'ri_form.get_files', 'uses' => 'SurveySubmissionsController@add_attachment']);
    Route::post('risk-improvement/form/save', ['as' => 'ri_form.sdsdsd', 'uses' => 'SurveySubmissionsController@csubmission']);
    Route::get('risk-improvement/form/surveyor/{id}', ['as' => 'ri_form.index', 'uses' => 'RiskImprovementFormController@surveyorForms']);
    Route::get('surveys/surveyor/{id}', ['as' => 'surveys.get-surveys', 'uses' => 'SurveyController@getSurveysForSurveyors']);

    // Dashboard
    Route::get('/', ['as' => 'dashboard', 'uses' => 'DashboardController@dashboard']);

    // Latest Guidance
    Route::get('/latest-guidance', ['as' => 'broker-documents', 'uses' => 'BrokerDocumentController@showDocuments']);

    // Static pages
    Route::get('iosh-nebosh', ['as' => 'static.iosh-nebosh', 'uses' => 'StaticPageController@ioshNebosh']);
    Route::get('assessments', ['as' => 'static.assessments', 'uses' => 'StaticPageController@assessments']);
    Route::get('articles-and-policies', ['as' => 'static.articles-and-policies', 'uses' => 'StaticPageController@articlesAndPolicies']);
    Route::get('awareness-courses', ['as' => 'static.awareness-courses', 'uses' => 'StaticPageController@awarenessCourses']);
    Route::get('access-group', ['as' => 'static.access-group', 'uses' => 'StaticPageController@accessGroup']);

    // Latest Guidance
    Route::get('/latest-guidance', ['as' => 'broker-documents', 'uses' => 'BrokerDocumentController@showDocuments']);

    // Image
    Route::get('/image/{str}', ['as' => 'image', 'uses' => 'ImageController@cropper']);
    Route::get('/image/{str}/{x}/{y}/{width}/{height}', ['as' => 'image.crop', 'uses' => 'ImageController@crop']);

    //Assets
    Route::group(['prefix' => 'assets'], function () {
        Route::get('show/{entity?}', ['as' => 'assets.show', 'uses' => 'AssetController@show']);
        Route::get('create/{entity?}', ['as' => 'assets.create', 'uses' => 'AssetController@store']);
        Route::post('create', ['as' => 'assets.create-new', 'uses' => 'AssetController@store']);
        Route::get('link/{key}/{id?}', ['as' => 'assets.link', 'uses' => 'AssetController@assetLink']);
    });

    //Safety Media
    Route::get('sm/demo', ['as' => 'sm.demo', 'uses' => 'SafetymediaController@demo']);
    Route::get('sm/add-user', ['as' => 'sm.adduser', 'uses' => 'SafetymediaController@addUser']);
    Route::get('sm/edit-user', ['as' => 'sm.edituser', 'uses' => 'SafetymediaController@editUser']);

    // Search
    Route::get('search', ['as' => 'search.search', 'uses' => 'SearchController@search']);
    Route::post('search', ['as' => 'search.search', 'uses' => 'SearchController@search']);

    // public forms
    Route::resource('public-forms', 'PublicFormsController', ['except' => ['index', 'edit']]);

    Route::get('public-forms-submission/show_pdf/{id}', [PublicFormSubmissionsController::class,'showPdf'])->name('public-form-submission.show.pdf');
    Route::post('public-forms-submission/print/pdf', ['as' => 'public-form-submission.print.pdf', 'uses' => 'PublicFormSubmissionsController@printPdf']);

    Route::get('forms/show/{form_id}/public', [PublicFormsController::class,'show']); /* completed */
    Route::get('forms/edit/{form_id}/public', 'PublicFormsController@edit');
    Route::get('forms/delete/{form_id}/public', [PublicFormsController::class,'delete']); /* completed */
    Route::get('forms/create/{form_id?}/public', [PublicFormsController::class,'create']); /* completed */
    Route::post('forms/create-link', 'PublicFormsController@createLink');
    Route::get('forms/delete-link/{link_id}', 'PublicFormsController@deleteLink');

    Route::post('forms/shared-link', 'PublicFormsController@sharedLink');
    Route::post('forms/delete-shared-link/{link_id}', 'PublicFormsController@deleteSharedLink');
    Route::get('forms/submissions/{form_id}/public', 'PublicFormSubmissionsController@submissions');

    Route::get('forms/submissions/getPrivateSubmissions/{form_id}', 'FormSubmissionsController@ajaxSubmissions');
    Route::get('forms/submissions/getPublicSubmissions/{form_id}', 'PublicFormSubmissionsController@ajaxSubmissions');

    //Route::get('forms/submissions/{form_id}/public', 'PublicFormSubmissionsController@index');

    Route::get('forms/submission/{submission_id}/public', ['as' => 'public_form.show', 'uses' => 'PublicFormSubmissionsController@show']);
    Route::get('forms/submission/delete/{submission_id}/public', 'PublicFormSubmissionsController@delete');


    Route::get('forms/export-submissions/{form_id}', [PublicFormSubmissionsController::class,'exportSubmissions'])->name('submissions.export-submissions');

    Route::get('public-forms-submission/{map_id}', [
        'as' => 'preview.public-form-submission-withmapping',
        'uses' => 'PublicFormSubmissionsController@previewSubmission',
    ]);

    Route::post('forms/submission/verify/public', [
            'as' => 'verify.public-form-submission',
            'uses' => 'PublicFormSubmissionsController@verify',
        ]);
    Route::get('form/attachment/{name}', [
            'as' => 'public_form_documents.retrieve',
            'uses' => 'PublicFormSubmissionsController@retrieveAttachment',
        ]);

    // Forms
    Route::get('forms/show/{form_id}',[FormsController::class,'show']); /* completed */
    Route::get('forms/edit/{form_id}',[FormsController::class,'edit']); /* completed */
    Route::get('forms/create/{form_id?}', [FormsController::class,'create']); /* completed */
    Route::get('forms/claims',  [FormsController::class,'claims']); /* completed */
    Route::get('forms/success', [FormsController::class,'success']); /* completed */
    Route::get('forms/updated', [FormsController::class,'updated']); /* completed */
    Route::get('forms/get-liberty-users', [FormsController::class,'getLibertyUsers']);
    Route::get('forms/get-client-users', [FormsController::class,'getClientUsers']);
    Route::resource('forms', FormsController::class); /* completed */
    Route::get('forms/delete/{form_id}', [FormsController::class,'delete']); /* completed */
    Route::get('forms/ajax/getPrivateForms', [FormsController::class, 'getPrivateForms']);
    Route::get('forms/ajax/getPublicForms', [PublicFormsController::class, 'getPublicForms']);

    // Form Submissions
    Route::get('forms/submission/show_pdf/{id}/{ispublic?}', [FormSubmissionsController::class,'showPdf'])->name('submission.show.pdf'); /* completed */
    Route::post('forms/submission/print_pdf', [FormSubmissionsController::class,'printPdf'])->name('submission.print.pdf'); /* completed */

    Route::get('forms/submissions/{submission_id}', [FormSubmissionsController::class,'submissions']); /* completed */
    Route::post('forms/submission/{id}', [FormSubmissionsController::class,'edit']); /* completed */
    Route::get('forms/submission/delete/{id}', [FormSubmissionsController::class,'delete']); /* completed */
    Route::resource('forms/submission', FormSubmissionsController::class); /* completed */
    Route::get('attachment/{name}', [FormSubmissionsController::class,'retrieveAttachment'])->name('form_documents.retrieve');  /* completed */

    Route::group(['prefix' => 'risk-improvement/form'], function () {
        Route::get('/',[RiskImprovementFormController::class,'index'])->name('ri_form.index'); /* completed */
        Route::get('/list-all-forms',[RiskImprovementFormController::class,'listAllForms'])->name('ri_form.list.all');
        Route::post('/risk-grading-fields',[RiskImprovementFormController::class,'riskGradingFields'])->name('ri_form.risk-grading-fields');
        Route::post('/ri-temp-data',[RiskImprovementFormController::class,'riskImprovementAutoSaveData'])->name('ri_form.ri-temp-data');
        Route::get('create/{form_id?}', ['as' => 'ri_form.create', 'uses' => 'RiskImprovementFormController@create']);
        Route::get('edit/{form_id}',[RiskImprovementFormController::class,'edit'])->name('ri_form.edit'); /* completed */
        Route::get('show/{form_id}/{survey_id?}',[RiskImprovementFormController::class,'show'])->name('ri_form.show'); /* completed */
        Route::post('create/{form_id?}', ['as' => 'ri_form.store', 'uses' => 'RiskImprovementFormController@store']);
        Route::get('delete/{form_id?}',[RiskImprovementFormController::class,'delete'])->name('ri_form.delete'); /* completed */
        Route::post('{form_id?}', [RiskImprovementFormController::class,'update'])->name('ri_form.update');
        Route::get('updated', ['as' => 'ri_form.updated', 'uses' => 'RiskImprovementFormController@updated']);
        Route::post('save', ['as' => 'ri_form.save', 'uses' => 'RiskImprovementFormController@createsubmission']);
        Route::post('submission/{id}', ['as' => 'ri_form.submission.save', 'uses' => 'SurveySubmissionsController@edit']);
        Route::get('submission/delete/{id}', ['as' => 'ri_form.submission.delete', 'uses' => 'SurveySubmissionsController@delete']);
        Route::resource('submission', 'SurveySubmissionsController');

        Route::get('submissions/{submission_id}', ['as' => 'ri_form.submission.show', 'uses' => 'SurveySubmissionsController@submissions']);
        Route::get('show_pdf/{submission_id}/{show?}', ['as' => 'ri_form.submission.show_pdf', 'uses' => 'SurveySubmissionsController@showPdf']);
        Route::get('printpdfbinder/{submission_id}/{show?}', ['as' => 'ri_form.submission.printpdfbinder', 'uses' => 'SurveyController@printPdfBinder']);
        Route::get('downloadprintpdfbinder/{submission_id}/{show?}/{download?}', ['as' => 'ri_form.submission.downloadprintpdfbinder', 'uses' => 'SurveyController@downloadPrintPdfBinder']);

        Route::post('submission/{submission_id}', ['as' => 'ri_form.submission.save', 'uses' => 'SurveySubmissionsController@edit']);
        Route::post('submission/{submission_id}/close', ['as' => 'ri_form.submission.close_issue', 'uses' => 'SurveySubmissionsController@close_issue']);

        Route::get('cache/{form_id}', ['as' => 'ri_form.cache-get', 'uses' => 'RiskImprovementFormController@form_cache']);
        Route::post('cache/{form_id}', ['as' => 'ri_form.cache-post', 'uses' => 'RiskImprovementFormController@form_cache']);
        // Route::post('upload', array('as' => 'ri_form.upload', 'uses' => 'SurveySubmissionsController@add_attachment'));
        Route::post('/standard-risk-grading/calculate-attribute-grade', ['as'                 => 'calculate-attribute-grade', 'uses'                 => 'SurveySubmissionsController@calculateAttributeGrade']);

    });
    //Route::resource('risk-improvement/form', 'RiskImprovementFormController');
    Route::post('/print/pdf', ['as' => 'print.pdf', 'uses' => 'SurveySubmissionsController@printPdf']);
    // reports
    Route::get('reports/accident-reporting',[AccidentReportingController::class,'index'])->name('accident-reporting.index'); 
    Route::post('reports/filter',[AccidentReportingController::class,'filter'])->name('accident-reporting.filter'); 
    Route::post('reports/printing',[AccidentReportingController::class,'printing'])->name('accident-reporting.printing'); 
    Route::get('reports/print',[AccidentReportingController::class,'printPreview'])->name('accident-reporting.print'); 
    Route::get('reports/mga-case-summary',[MgaSchemeController::class,'mgaCaseSummary'])->name('mga-case-summary.show'); /* completed */
    Route::get('reports/mga-split-by-trade',[MgaSchemeController::class,'mgaSplitByTrade'])->name('mga-split-by-trade.show'); /* completed */
    // Doc levels
    Route::get('get_level/{level}/{parent}', ['as' => 'organisation.document.get_level', 'uses' => 'DocumentController@get_level']);

    // branch users
    Route::get('branch-users/{id?}', ['as' => 'users.branchuser', 'uses' => 'UserController@branchUsers']);

    Route::group(['prefix' => 'do-facilities'], function () {
        Route::get('submission-links', [OrganisationSubmissionLinksController::class,'index'])->name('do-facilities.submission-links.index'); /* completed */
        Route::get('submission-links/create', [OrganisationSubmissionLinksController::class,'create'])->name('do-facilities.submission-links.create'); /* completed */
        Route::get('submission-links/{id}', [OrganisationSubmissionLinksController::class,'show'])->name('do-facilities.submission-links.show'); /* completed */
        Route::post('submission-links', [OrganisationSubmissionLinksController::class,'store'])->name('do-facilities.submission-links.store'); /* completed */
        Route::post('submission-links/{_id}/revoke', [OrganisationSubmissionLinksController::class,'revoke'])->name('submission-links.revoke'); /* completed */

        Route::resource('pending-submissions', 'OrganisationSubmissionsController');
        Route::get('pending-submissions', ['as' => 'do-facilities.pending-submissions.index', 'uses' => 'OrganisationSubmissionsController@index']);
        Route::get('pending-submissions/{id}', [OrganisationSubmissionsController::class,'show'])->name('do-facilities.pending-submissions.show'); /* completed */
        Route::post('pending-submissions/{id}/status/{status}',  [OrganisationSubmissionsController::class,'status'])->name('pending-submissions.status');;

        Route::get('upload-bordereau', [UploadBorderauController::class,'show'])->name('upload-borderau.show'); /* completed */
        Route::get('upload-bordereau-download', ['as' => 'upload-borderau.download', 'uses' => 'UploadBorderauController@download']);
        Route::post('upload-borderau', ['as' => 'upload-borderau.store', 'uses' => 'UploadBorderauController@store']);
    });

    /* ------ Account Documents  ----- */
    Route::group(['prefix' => 'account-documents'], function () {
        Route::get('create', ['as' => 'account-documents.create', 'uses' => 'AccountDocumentController@create']);
        Route::post('store', ['as' => 'account-documents.store', 'uses' => 'AccountDocumentController@store']);
        Route::get('{id}', ['as' => 'account-documents.show', 'uses' => 'AccountDocumentController@show']);
        Route::get('{id}/edit', ['as' => 'account-documents.edit', 'uses' => 'AccountDocumentController@edit']);
        Route::post('{id}/update', ['as' => 'account-documents.update', 'uses' => 'AccountDocumentController@update']);
        Route::post('{id}/destroy', ['as' => 'account-documents.destroy', 'uses' => 'AccountDocumentController@destroy']);
    });

    /* ------ Organisation  ----- */
    Route::resource(
        'organisation',
        'OrganisationController'
    )->parameters(['organisation' => 'id']);

    Route::get('organisation/{id}/additional-details', [
        'uses' => 'OrganisationController@getOrganisationAccordionData'
    ])->name('organisation.additional-details');

    Route::group(['prefix' => 'organisation'], function () {
        Route::get('/flood-alerts/{id}', ['as' => 'organisations.flood-alerts.view', 'uses' => 'FloodAlertsController@showAlert']);
        Route::group(['prefix' => 'bordereau'], function () {
            Route::get('import', ['as' => 'organisation.bordereau.import', 'uses' => 'OrganisationBordereauController@index']);
            Route::get('pending-submission/{submission_id}', ['as' => 'organisation.bordereau.pending-submission', 'uses' => 'OrganisationBordereauController@show']);
            Route::post('upload', ['as' => 'organisation.bordereau.upload', 'uses' => 'OrganisationBordereauController@upload']);
            Route::post('reject', ['as' => 'organisation.bordereau.reject', 'uses' => 'OrganisationBordereauController@reject']);
            Route::post('approve', ['as' => 'organisation.bordereau.approve', 'uses' => 'OrganisationBordereauController@approve']);
        });

        Route::get('{organisation_id}/download-sov/{cloudname}/{filename}', ['as' => 'organisation.download-sov', 'uses' => 'OrganisationController@downloadSov']);
        Route::post('{organisation_id}/delete-sov', ['as' => 'organisation.delete-sov', 'uses' => 'OrganisationController@deleteSov']);

        Route::group(['prefix' => '{organisation_id}/reports'], function () {
            Route::get('create', ['as' => 'organisation.reports.create', 'uses' => 'OrganisationReportController@create']);
            Route::post('store', ['as' => 'organisation.reports.store', 'uses' => 'OrganisationReportController@store']);
            Route::get('{id}', ['as' => 'organisation.reports.show', 'uses' => 'OrganisationReportController@show']);
            Route::get('{id}/edit', ['as' => 'organisation.reports.edit', 'uses' => 'OrganisationReportController@edit']);
            Route::post('{id}/update', ['as' => 'organisation.reports.update', 'uses' => 'OrganisationReportController@update']);
            Route::post('{id}/destroy', ['as' => 'organisation.reports.destroy', 'uses' => 'OrganisationReportController@destroy']);
        });

        Route::group(['prefix' => '{organisation_id}/notes'], function () {
            Route::get('create', ['as' => 'organisation.notes.create', 'uses' => 'OrganisationNoteController@create']);
            Route::post('store', ['as' => 'organisation.notes.store', 'uses' => 'OrganisationNoteController@store']);
            Route::get('{note_id}', ['as' => 'organisation.notes.show', 'uses' => 'OrganisationNoteController@show']);
            Route::get('{note_id}/edit', ['as' => 'organisation.notes.edit', 'uses' => 'OrganisationNoteController@edit']);
            Route::post('{note_id}/update', ['as' => 'organisation.notes.update', 'uses' => 'OrganisationNoteController@update']);
            Route::post('{note_id}/destroy', ['as' => 'organisation.notes.destroy', 'uses' => 'OrganisationNoteController@destroy']);
        });

        Route::group(['prefix' => '{organisation_id}'], function () {
            Route::get('locations', array('as' => 'organisation.locations.create', 'uses' => 'OrganisationLocationController@index'));
            Route::get('locations/create', array('as' => 'organisation.locations.create-location', 'uses' => 'OrganisationLocationController@createLocation'));
            Route::post('locations/store', array('as' => 'organisation.locations.store-location', 'uses' => 'OrganisationLocationController@storeLocation'));
            Route::get('locations/bulk-upload', array('as' => 'organisation.locations.bulk-upload.form', 'uses' => 'OrganisationLocationController@showBulkUploadForm'));
            Route::post('locations/bulk-upload', array('as' => 'organisation.locations.bulk-upload.store', 'uses' => 'OrganisationLocationController@bulkUploadLocations'));
            Route::post('locations/bulk-validate', array('as' => 'organisation.locations.bulk-validate.store', 'uses' => 'OrganisationLocationController@bulkUploadLocationValidation'));
            Route::get('locations/bulk-upload/download-template', array('as' => 'organisation.locations.bulk-upload.download-template', 'uses' => 'OrganisationLocationController@downloadBulkUploadTemplate'));
            Route::post('upload', array('as' => 'organisation.locations.upload', 'uses' => 'OrganisationLocationController@upload'));
            Route::post('update', array('as' => 'organisation.locations.update', 'uses' => 'OrganisationLocationController@update'));
            Route::get('/flood-alerts', ['as' => 'organisations.flood-alerts', 'uses' => 'FloodAlertsController@index']);
        });

        Route::get('location/{location_id}', ['as' => 'organisation.locations.edit-location', 'uses' => 'OrganisationLocationController@edit']);
        Route::delete('delete/{id}/{location_id}', ['as' => 'organisation.locations.delete', 'uses' => 'OrganisationLocationController@delete']);
        Route::get('{id}/get-locations', ['as' => 'organisation.locations.getlocations', 'uses' => 'OrganisationLocationController@getLocations']);
        Route::get('{id}/location/download', ['as' => 'organisation.locations.download-location', 'uses' => 'OrganisationLocationController@download']);
        Route::get('location/{location_id}/flood-alerts', ['as' => 'organisation.locations.flood-alerts', 'uses' => 'FloodAlertsController@show']);

        Route::post('calculate-attribute-grade', array('as' => 'organisation.locations.calculate-attribute-grade', 'uses' => 'OrganisationLocationController@calculateAttributeGrade'));

        Route::post('risk-grading-logs', array('as' => 'organisation.risk-grading-logs', 'uses' => 'OrganisationLocationController@riskGradingLogs'));
        Route::post('risk-grading-logs-overview', array('as' => 'organisation.risk-grading-logs-overview', 'uses' => 'OrganisationLocationController@riskGradingLogsOverview'));
        Route::get('location/{location_id}', array('as' => 'organisation.locations.edit-location', 'uses' => 'OrganisationLocationController@edit'));
        Route::delete('delete/{id}/{location_id}', array('as' => 'organisation.locations.delete', 'uses' => 'OrganisationLocationController@delete'));
        Route::get('{id}/get-locations', array('as' => 'organisation.locations.getlocations', 'uses' => 'OrganisationLocationController@getLocations'));
        Route::get('{id}/location/download', array('as' => 'organisation.locations.download-location', 'uses' => 'OrganisationLocationController@download'));
        Route::get('location/{location_id}/flood-alerts', array('as' => 'organisation.locations.flood-alerts', 'uses' => 'FloodAlertsController@show'));

        Route::get('dashboard-statistics/{org_id}', array('as' => 'organisation.dashboard-statistics', 'uses' => 'OrganisationController@dashboardStatistics'));
    });

    /* ------ Sector ------- */
    Route::resource(
            'sector',
            'SectorController',
            ['except' => ['show']]
        );

    Route::resource(
            'liberty-users',
            'LibertyUserController',
            ['middleware' => 'risk-control', 'except' => ['show']]
        );

    // Branches
    Route::resource(
            'liberty-branches',
            'BranchController',
            ['except' => ['show']]
        )->parameters([
            'liberty-branches' => 'id'
        ]);

    Route::get('liberty-branches/{id?}', ['as' => 'liberty-branches.details', 'uses' => 'BranchController@details']);

    Route::resource(
            'external-surveyors',
            'ExternalSurveyorController',
            ['except' => ['show']]
        );

    Route::resource(
            'broker-users',
            'BrokerUserController',
            ['except' => ['show']]
        );

    Route::group(['before' => ['notAspen']], function () {
        /* ------ DOCUMENT LIBRARY ------- */
        Route::group(['prefix' => 'document'], function () {
            //GET
            Route::get('all', [DocumentLibController::class,'all'])->name('document.all'); /* completed */
            Route::get('create', [DocumentLibController::class,'create'])->name('document.create'); /* completed */

            Route::get('retrieve/{docID}', [DocumentLibController::class,'retrieve'])->name('document.retrieve');
            Route::get('retrieve-notes/{orgid}/{noteid}', ['as' => 'document.retrieve.note', 'uses' => 'DocumentLibController@retrieveNotes']);
            Route::get('destroy/{docID}', [DocumentLibController::class,'destroy'])->name('document.destroy'); /* completed */
            Route::get('edit/{docID}', [DocumentLibController::class,'edit'])->name('document.edit'); /* completed */

            //POST
            Route::post('store', ['as' => 'document.store', 'uses' => 'DocumentLibController@store']);

            //POST
            Route::post('update', [DocumentLibController::class,'update'])->name('document.update'); /* completed */

            //Delete
            Route::get('create/loss-lesson', [DocumentLibController::class,'create_loss_lessons'])->name('document.create.loss-lesson'); /* completed */
            Route::post('store/loss-lesson', ['as' => 'document.store.loss-lesson', 'uses' => 'DocumentLibController@store_loss_lessons']);
            Route::post('store/survey-report', ['as' => 'document.store.survey-report', 'uses' => 'ReportController@store_survey_reports']);
            Route::post('update/survey-report', ['as' => 'document.update.survey-report', 'uses' => 'ReportController@update_survey_reports']);

            Route::get('edit/loss-lesson/{docID}', [DocumentLibController::class,'edit_loss_lessons'])->name('document.edit.loss-lesson'); /* completed */
            Route::post('update/loss-lesson', [DocumentLibController::class,'update_loss_lessons'])->name('document.update.loss-lesson'); /* completed */

            Route::group(['prefix' => 'link'], function () {
                Route::get('/', [LinkController::class,'index'])->name('document.link.index'); /* completed */
                Route::get('edit/{id}/{LinkID}', [LinkController::class,'edit'])->name('document.link.edit'); /* completed */
                Route::get('create', [LinkController::class,'create'])->name('document.link.create'); /* completed */

                Route::post('store', [LinkController::class,'store'])->name('document.link.store'); /* completed */
                Route::post('update', [LinkController::class,'update'])->name('document.link.update'); /* completed */
                Route::post('destroy/{id}/{linkID}', [LinkController::class,'destroy'])->name('document.link.destroy'); /* completed */
            });
        });

        Route::resource('users', 'UserController', ['except' => ['show']]);

        Route::post('user/resetLink/{id}', ['as' => 'user.resetLogin', 'uses' => 'UserController@resetLogin']);
        Route::post('user/resetLinkExternal/{id}', ['as' => 'user.resetLoginExternal', 'uses' => 'UserController@resetLoginExternal']);
        Route::post('user/reset-login-attempts/{id}', ['as' => 'user.resetLoginAttempts', 'uses' => 'UserController@resetLoginAttempts']);

        // user activation links
        Route::get('user/link/{id}', ['as' => 'user.link', 'uses' => 'UserController@generateLink']);
        Route::get('liberty-user/link/{id}', ['as' => 'liberty-user.link', 'uses' => 'LibertyUserController@generateLink']);
        Route::get('broker-user/link/{id}', ['as' => 'broker-user.link', 'uses' => 'BrokerUserController@generateLink']);

        Route::get('liberty-user/{user_id}/reset-tfa', ['as' => 'liberty-users.reset-tfa', 'uses' => 'LibertyUserController@resetTfa']);
        Route::get('external-surveyor/link/{id}', ['as' => 'external-surveyors.link', 'uses' => 'ExternalSurveyorController@generateLink']);
        Route::get('external-surveyor/{user_id}/reset-tfa', ['as' => 'external-surveyors.reset-tfa', 'uses' => 'ExternalSurveyorController@resetTfa']);
        Route::get('broker-user/{user_id}/reset-tfa', ['as' => 'broker-users.reset-tfa', 'uses' => 'BrokerUserController@resetTfa']);
        Route::get('users/{user_id}/reset-tfa', ['as' => 'users.reset-tfa', 'uses' => 'UserController@resetTfa']);

        // reset broker user login attempts
        Route::post('broker-user/{email}/reset-login-attempts', ['as' => 'broker-users.reset-login-attempts', 'uses' => 'BrokerUserController@resetLoginAttempts']);

        Route::group(['prefix' => 'document-type'], function () {
            //GET
            Route::get('/policy-types', ['as' => 'document.types', 'uses' => 'DocumentPolicyController@index']);
            Route::get('/policy-types/type/{type_id?}', ['as' => 'document.type', 'uses' => 'DocumentPolicyController@show']);

            //POST
            Route::post('save', ['as' => 'document.store_policy', 'uses' => 'DocumentController@store']);
            Route::post('save/policy-type/{type_id?}', ['as' => 'document.document_type', 'uses' => 'DocumentPolicyController@save']);
            Route::post('delete/policy-type/{type_id?}', ['as' => 'document.delete_type', 'uses' => 'DocumentPolicyController@delete']);
        });
    });

    Route::group(['prefix' => 'risk-recommendation'], function () {
        //GET
        Route::get('titles', [RiskRecTitleController::class,'show'])->name('riskrec.titles'); /* completed */

        //Route::get('titles/store', array('as' => 'riskrec.store', 'uses' => 'RiskRecTitleController@store'));
        Route::match(['GET', 'POST'], 'titles/store', [RiskRecTitleController::class,'store'])->name('riskrec.store'); /* completed */

        Route::match(['GET', 'POST'], 'titles/update/{id}', [RiskRecTitleController::class,'update'])->name('riskrec.update'); /* completed */

        //POST
        //Route::post('titles/store', array('as' => 'riskrec.store', 'uses' => 'RiskRecTitleController@store'));
        //Route::post('titles/update/{id}', array('as' => 'riskrec.update', 'uses' => 'RiskRecTitleController@update'));
        Route::get('titles/delete/{id}', [RiskRecTitleController::class,'delete'])->name('riskrec.delete');

    });

    Route::group(['before' => ['onlyAspen']], function () {
        /* ------ Organisation ----- */
        Route::group(['prefix' => 'organisation/{id}'], function () {
            /* Document policy */
            Route::group(['prefix' => 'document-policy'], function () {
                //GET
                Route::get('/', ['as' => 'policy.documents.all', 'uses' => 'DocumentPolicyController@get_all_documents']);
                Route::get('add/{document_id?}', ['as' => 'organisation.document.policy_doc', 'uses' => 'DocumentPolicyController@get_policydocument_show']);
                Route::get('retrieve-policy-document/{name}/{document_id}', ['as' => 'organisation.document.retrieve_policy_document', 'uses' => 'DocumentPolicyController@retrieve_policy_document']);
                //POST
                Route::post('update/type/{document_id}', ['as' => 'organisation.document.update_policy_type', 'uses' => 'DocumentPolicyController@update_policy_type']);
                Route::post('delete/document/{document_id}', ['as' => 'organisation.document.delete.policy', 'uses' => 'DocumentPolicyController@delete_policy_document']);
                Route::post('notify/policy-doc/{document_id?}', ['as' => 'organisation.document.notify.policy', 'uses' => 'DocumentPolicyController@notify_admins']);
            });
        });
    });

    Route::get('organisation/{id}/overview', array('as' => 'organisation.overview.index', 'uses' => 'OrganisationOverviewController@index'));
    Route::get('organisation/{id}/overview/edit', array('as' => 'organisation.overview.edit', 'uses' => 'OrganisationOverviewController@edit'));
    Route::post('organisation/{id}/overview', array('as' => 'organisation.overview.update', 'uses' => 'OrganisationOverviewController@update'));
    Route::get('organisation/{id}/engagement', ['as' => 'organisation.engagement', 'uses' => 'OrganisationEngagementController@index']);
    Route::get('organisation/{id}/engagement-new', ['as' => 'organisation.engagement-new', 'uses' => 'OrganisationEngagementController@newIndex']);
    Route::get('organisation/{id}/engagement/tiles', ['as' => 'organisation.engagement-tiles', 'uses' => 'OrganisationEngagementController@tilesBlock']);
    Route::get('organisation/{id}/policy-numbers', array('as' => 'organisation.policy-numbers.show', 'uses' => 'OrganisationController@showPolicyNumbers'));
    Route::get('organisation/broker-users/{mga_id}/{broker_org_id}', array('as' => 'organisation.broker-users.show', 'uses' => 'OrganisationController@getBrokerUsers'));

    Route::group(['before' => ['notAspen']], function () {
        /* ------ Organisation ----- */
        Route::group(['prefix' => 'organisation/{id}'], function () {
            Route::get('/user', ['as' => 'organisation.user.get', 'uses' => 'UserController@organisationUsers']);

            //Users
            Route::group(['prefix' => 'user'], function () {
                //GET
                Route::get('{userID}/edit', ['as' => 'organisation.user.edit', 'uses' => 'UserController@organisationEdit']);
                Route::get('create', ['as' => 'organisation.user.create', 'uses' => 'UserController@organisationCreate']);

                //POST
                Route::post('store', ['as' => 'organisation.user.store', 'uses' => 'UserController@organisationStore']);

                //PUT
                Route::put('update', ['as' => 'organisation.user.update', 'uses' => 'UserController@update']);
                Route::put('update', ['as' => 'organisation.contact.update', 'uses' => 'UserController@UpdateContacts']);

                //DELETE
                Route::delete('destroy/{userID}', ['as' => 'organisation.user.destroy', 'uses' => 'UserController@organisationDestroy']);
            });

            Route::resource('report', 'ReportController');
            Route::post('report/destroy', ['as' => 'organisation.{id}.report.destroy', 'uses' => 'ReportController@delete']);

            /* ------ Organisation DOCUMENTs ------- */
            Route::group(['prefix' => 'document'], function () {
                //GET
                Route::get('create', ['as' => 'organisation.document.create', 'uses' => 'DocumentController@create']);
                Route::get('retrieve/{docID}', ['as' => 'organisation.document.retrieve', 'uses' => 'DocumentController@retrieve']);
                Route::get('destroy/{docID}', ['as' => 'organisation.document.destroy', 'uses' => 'DocumentController@destroy']);
                Route::get('edit/{docID}', ['as' => 'organisation.document.edit', 'uses' => 'DocumentController@edit']);

                //POST
                Route::post('store', ['as' => 'organisation.document.store', 'uses' => 'DocumentController@store']);
                Route::post('update', ['as' => 'organisation.document.update', 'uses' => 'DocumentController@update']);
            });

            /* Document policy type */
            Route::group(['prefix' => 'document-type'], function () {
                //GET
                //Route::get('/policy-types', array('as' => 'organisation.document.types', 'uses' => 'DocumentPolicyController@index'));
                //Route::get('/policy-types/type/{type_id?}', array('as' => 'organisation.document.type', 'uses' => 'DocumentPolicyController@show'));

                //POST
                Route::post('save', ['as' => 'organisation.document.store_policy', 'uses' => 'DocumentController@store']);
                // Route::post('save/policy-type/{type_id?}', array('as' => 'organisation.document.document_type', 'uses' => 'DocumentPolicyController@save'));
                            // Route::post('delete/policy-type/{type_id?}', array('as' => 'organisation.document.delete_type', 'uses' => 'DocumentPolicyController@delete'));
            });

            /* Document policy */
            Route::group(['prefix' => 'document-policy'], function () {
                //GET
                Route::get('/', ['as' => 'policy.documents.all', 'uses' => 'DocumentPolicyController@get_all_documents']);
                Route::get('add/{document_id?}', ['as' => 'organisation.document.policy_doc', 'uses' => 'DocumentPolicyController@get_policydocument_show']);
                Route::get('retrieve-policy-document/{name}/{document_id}', ['as' => 'organisation.document.retrieve_policy_document', 'uses' => 'DocumentPolicyController@retrieve_policy_document']);
                //POST
                Route::post('update/type/{document_id}', ['as' => 'organisation.document.update_policy_type', 'uses' => 'DocumentPolicyController@update_policy_type']);
                Route::post('delete/document/{document_id}', ['as' => 'organisation.document.delete.policy', 'uses' => 'DocumentPolicyController@delete_policy_document']);
                Route::post('notify/policy-doc/{document_id?}', ['as' => 'organisation.document.notify.policy', 'uses' => 'DocumentPolicyController@notify_admins']);
            });

            /* ---- Organisation Link -----*/
            Route::group(['prefix' => 'link'], function () {
                Route::get('create', ['as' => 'organisation.link.create', 'uses' => 'LinkController@create']);
                Route::get('edit/{linkId}', ['as' => 'organisation.link.edit', 'uses' => 'LinkController@edit']);

                Route::post('store', ['as' => 'organisation.link.store', 'uses' => 'LinkController@store']);
                Route::post('destroy/{linkId}', ['as' => 'organisation.link.destroy', 'uses' => 'LinkController@destroy']);
                Route::post('update', ['as' => 'organisation.link.update', 'uses' => 'LinkController@update']);
            });
        });

        //Usage Statistics
        Route::group(['prefix' => 'statistics'], function () {
            Route::get('/', [StatisticsController::class,'index'])->name('statistics.index'); /* completed */

            Route::get('/reset', [StatisticsController::class,'indexReset'])->name('statistics.index-reset');

            Route::get('/licenses', [StatisticsController::class,'licenses'])->name('statistics.licenses'); /* completed */
            Route::get('/activations/export', [StatisticsController::class,'export'])->name('statistics.activations-export');

            //1st Level
            Route::get('/{type}', [StatisticsController::class,'metric'])->name('statistics.metric'); /* completed */
            Route::get('/reset/{type}', [StatisticsController::class,'typeReset'])->name('statistics.type-reset'); /* completed */

            //2nd level
            Route::get('/{type}/organisation/{organisation}', [StatisticsController::class,'organisation'])->name('statistics.organisation');
            Route::get('/reset/{type}/{organisation}', [StatisticsController::class,'organisationReset'])->name('statistics.organisation-reset');

            Route::get('/{type}/page/{page}', [StatisticsController::class,'page'])->name('statistics.page');

            Route::get('/{type}/access/{access}', [StatisticsController::class,'access'])->name('statistics.access');

            Route::get('/{type}/{organisation}', [StatisticsController::class,'documents'])->name('statistics.document');
        });

        // Learning System
        Route::group(['prefix' => 'learning'], function () {
            Route::resource('categories', LearningCategoryController::class)->names([
                'index'   => 'learning.course.category.index',
                'store'   => 'learning.course.category.store',
                'edit'    => 'learning.course.category.edit',
                'update'  => 'learning.course.category.update',
                'destroy' => 'learning.course.category.destroy',
            ]);

            Route::get('course/{course_id}/view', ['as' => 'learning.course.view', 'uses' => 'LearningCourseController@viewCourse']);
            Route::get('lesson/{lesson_id}/view', ['as' => 'learning.lesson.view', 'uses' => 'LearningLessonController@viewLesson']);

            Route::post('course/{course_id}/reminder', ['as' => 'learning.course.reminder', 'uses' => 'LearningCourseController@reminder']);
            Route::group(['prefix' => 'course/{course_id}/lessons'], function () {
                Route::put('/sort', ['as' => 'learning.course.lesson.sort', 'uses' => 'LearningLessonController@sort']);
                Route::post('{lesson_id}/page', ['as' => 'learning.course.lesson.page.store', 'uses' => 'LearningLessonPageController@store']);
                Route::post('{lesson_id}/upload/image', ['as' => 'learning.course.lesson.upload.image', 'uses' => 'LearningLessonPageController@uploadImage']);
                Route::post('{lesson_id}/upload/video', ['as' => 'learning.course.lesson.upload.video', 'uses' => 'LearningLessonPageController@uploadVideo']);
                Route::post('{lesson_id}/upload/audio', ['as' => 'learning.course.lesson.upload.audio', 'uses' => 'LearningLessonPageController@uploadAudio']);
                Route::post('{lesson_id}/uuid/{uu_id}/filecheck', ['as' => 'learning.course.lesson.uuid.filecheck', 'uses' => 'LearningLessonPageController@filecheck']);
                Route::post('{lesson_id}/upload/document', ['as' => 'learning.course.lesson.upload.document', 'uses' => 'LearningLessonPageController@uploadDocument']);

                Route::delete('{lesson_id}/page/{page_id}/section/{section_id}/document/{document_id}', ['as' => 'learning.course.lesson.document.delete', 'uses' => 'LearningLessonPageController@deleteDocument']);

                Route::get('{lesson_id}/page/{page_id}/button', ['as' => 'learning.course.lesson.page.button', 'uses' => 'LearningLessonPageController@showButton']);
                Route::Get('{lesson_id}/page/{page_id}/page', ['as' => 'learning.course.lesson.page.page', 'uses' => 'LearningLessonPageController@showPage']);
                Route::delete('{lesson_id}/page/{page_id}', ['as' => 'learning.course.lesson.page.destroy', 'uses' => 'LearningLessonPageController@delete']);
                Route::put('{lesson_id}/page/sort', ['as' => 'learning.course.lesson.page.sort', 'uses' => 'LearningLessonPageController@sort']);

                //Sections
                Route::group(['prefix' => '{lesson_id}/page/{page_id}'], function () {
                    Route::get('/edit', ['as' => 'learning.course.lesson.page.edit', 'uses' => 'LearningLessonPageController@edit']);
                    Route::get('section/{section_id}', ['as' => 'learning.course.lesson.page.section.show', 'uses' => 'LearningLessonPageController@showSection']);
                    Route::post('/duplicate', ['as' => 'learning.course.lesson.page.duplicate', 'uses' => 'LearningLessonPageController@duplicate']);
                    Route::post('section/{section_name?}', ['as' => 'learning.course.lesson.page.section', 'uses' => 'LearningLessonPageController@saveSection']);
                    Route::post('/section/{section_id}/duplicate', ['as' => 'learning.course.lesson.page.section.duplicate', 'uses' => 'LearningLessonPageController@duplicateSection']);
                    Route::delete('section/{section_id}', ['as' => 'learning.course.lesson.page.section.destroy', 'uses' => 'LearningLessonPageController@deleteSection']);
                    Route::put('section/{section_id}', ['as' => 'learning.course.lesson.page.section.update', 'uses' => 'LearningLessonPageController@updateSection']);
                    Route::put('/sort', ['as' => 'learning.course.lesson.page.sort', 'uses' => 'LearningLessonPageController@sortPage']);
                });

                //Tests
                Route::group(['prefix' => '{lesson_id}/test'], function () {
                    Route::get('/', ['as' => 'learning.course.lesson.test.show', 'uses' => 'LearningLessonTestController@show']);
                    Route::get('/create', ['as' => 'learning.course.lesson.test.create', 'uses' => 'LearningLessonTestController@create']);
                    Route::post('/store', ['as' => 'learning.course.lesson.test.store', 'uses' => 'LearningLessonTestController@store']);
                    Route::post('/edit', ['as' => 'learning.course.lesson.test.edit', 'uses' => 'LearningLessonTestController@edit']);
                    Route::post('/option', ['as' => 'learning.course.lesson.test.option', 'uses' => 'LearningLessonTestController@setOptions']);
                    Route::put('/update', ['as' => 'learning.course.lesson.test.option', 'uses' => 'LearningLessonTestController@update']);
                    Route::delete('/delete', ['as' => 'learning.course.lesson.test.delete', 'uses' => 'LearningLessonTestController@delete']);
                    Route::get('/result', ['as' => 'learning.course.lesson.test.result', 'uses' => 'LearningLessonTestController@testResult']);
                    Route::get('/result/pdf', ['as' => 'learning.course.lesson.test.result.pdf', 'uses' => 'LearningLessonTestController@testResultPdf']);
                    Route::post('/submitpreview', ['as' => 'learning.course.lesson.test.submit', 'uses' => 'LearningLessonTestController@submitTestPreview']);

                    Route::group(['prefix' => '/edit/section'], function () {
                        Route::put('/sort', ['as' => 'learning.course.lesson.test.section.sort', 'uses' => 'LearningLessonTestController@sortSection']);
                        Route::get('/{section_type}', ['as' => 'learning.course.lesson.test.section.show', 'uses' => 'LearningLessonTestController@showSection']);
                        Route::put('/{section_id}', ['as' => 'learning.course.lesson.page.section.update', 'uses' => 'LearningLessonTestController@updateSection']);
                        Route::post('/{section_id}/duplicate', ['as' => 'learning.course.lesson.test.section.duplicate', 'uses' => 'LearningLessonTestController@duplicateSection']);
                        Route::post('/{section_id}/option/{question_type}', ['as' => 'learning.course.lesson.test.section.addoption', 'uses' => 'LearningLessonTestController@addOption']);
                        Route::post('/{section_id}/changequestiontype/{question_type}', ['as' => 'learning.course.lesson.test.section.changequestiontype', 'uses' => 'LearningLessonTestController@changeQuestionType']);
                        Route::delete('/{section_id}/option/{option_id}', ['as' => 'learning.course.lesson.test.section.removeoption', 'uses' => 'LearningLessonTestController@removeOption']);
                        Route::delete('/{section_id}', ['as' => 'learning.course.lesson.test.section.show', 'uses' => 'LearningLessonTestController@deleteSection']);
                    });
                });
            });
            Route::resource('course/{course_id}/lessons', 'LearningLessonController', [
                            'names' => [
                                'index' => 'learning.course.lesson.index',
                                'create' => 'learning.course.lesson.create',
                                'store' => 'learning.course.lesson.store',
                                'show' => 'learning.course.lesson.show',
                                'edit' => 'learning.course.lesson.edit',
                                'update' => 'learning.course.lesson.update',
                                'destroy' => 'learning.course.lesson.destroy',
                            ],
                        ]);
            Route::get('course/{course_id}/lessons/{id}/duplicate', ['as' => 'learning.course.lesson.duplicate', 'uses' => 'LearningLessonController@duplicate']);

            Route::delete('course/{course_id}/image/{type}/{path}', ['as' => 'learning.course.image.destroy', 'uses' => 'LearningCourseController@destroyImage']);
            Route::get('course/{course_id}/assign/{type?}', ['as' => 'learning.course.assign', 'uses' => 'LearningCourseController@assign']);
            Route::post('course/{course_id}/assign/{type}', ['as' => 'learning.course.type.assign', 'uses' => 'LearningCourseController@assign']);
            Route::get('course/{course_id}/organisations', ['as' => 'learning.course.organisations', 'uses' => 'LearningCourseController@organisationAssigned']);
            Route::get('course/{course_id}/sectors', ['as' => 'learning.course.sectors', 'uses' => 'LearningCourseController@sectorHandler']);
            Route::post('course/{course_id}/sectors', ['as' => 'learning.course.sectors', 'uses' => 'LearningCourseController@sectorHandler']);
            Route::post('course/{course_id}/duplicate', ['as' => 'learning.course.duplicate', 'uses' => 'LearningCourseController@duplicate']);
            Route::post('course/{course_id}/organisations/{type?}', ['as' => 'learning.course.organisations', 'uses' => 'LearningCourseController@organisationAssigned']);
            
            Route::name('learning.')->group(function() {
                Route::resource('course', 'LearningCourseController');
            });

            Route::get('progress', ['as' => 'learning.progress.index', 'uses' => 'LearningSystemController@progress']);
            Route::get('reports', ['as' => 'learning.reports.index', 'uses' => 'LearningSystemController@reports']);
            Route::get('reports/export', ['as' => 'learning.reports.export', 'uses' => 'LearningSystemController@exportReports']);
        });
    });

    // Aspen
    // Route::post('aspen/filter', 'AspenController@filter');
    // Route::get('aspen/clear', 'AspenController@clear');
    // Route::post('aspen/reply/{id}', 'AspenController@reply');
    // Route::get('aspen/create/{id?}', 'AspenController@create');
    // Route::get('aspen/documents-access', array('as' => 'aspen.documents-access', 'uses' => 'AspenDocumentsController@documentsAccess'));
    // Route::resource('aspen', 'AspenController');
    // Route::get('aspen-documents/create/{id?}', 'AspenDocumentsController@create_document');
    // Route::resource('aspen-documents', 'AspenDocumentsController');
    // Route::get('aspen/file/download/{cloud}/{name}', 'AspenController@downloadFile');
    // Route::resource('daily-claims-run', 'DcrDocumentController');
    // Route::get('daily-claims-run', array('as'        => 'aspen.daily-claims-run', 'uses'        => 'DcrDocumentController@index'));
    // Route::get('daily-claims-run/create', array('as' => 'aspen.daily-claims-run.create', 'uses' => 'DcrDocumentController@index'));
    // Route::get('aspen-dcr/file/download/{cloud}/{name}', 'DcrDocumentController@downloadFile');
    // Route::get('aspen-ew/file/download/{cloud}/{name}/{id?}', 'EwDocumentController@downloadFile');

    Route::group(['prefix' => 'endorsement-wordings'], function () {
        Route::get('/', ['as' => 'aspen.endorsement-wordings', 'uses' => 'EwDocumentController@index']);
        Route::get('create', ['as' => 'aspen.endorsement-wordings.create', 'uses' => 'EwDocumentController@create']);
        Route::post('create', ['as' => 'aspen.endorsement-wordings.store', 'uses' => 'EwDocumentController@store']);
        Route::get('edit/{id}', ['as' => 'aspen.endorsement-wordings.edit', 'uses' => 'EwDocumentController@edit']);
        Route::post('update', ['as' => 'aspen.endorsement-wordings.update', 'uses' => 'EwDocumentController@update']);
        Route::get('delete/{id}', ['as' => 'aspen.endorsement-wordings.delete', 'uses' => 'EwDocumentController@destroy']);
        // Route::get('authorize/{id}', array('as' => 'aspen.endorsement-wordings.authorize', 'uses' => 'EwDocumentController@authorize'));
    });

    // MGA Schemes
    Route::group(['prefix' => 'mga-schemes'], function () {
        Route::get('/', ['as' => 'mga-schemes.index', 'uses' => 'MgaSchemeController@index']);
        Route::get('create', ['as' => 'mga-schemes.create', 'uses' => 'MgaSchemeController@create']);
        Route::post('create', ['as' => 'mga-schemes.store', 'uses' => 'MgaSchemeController@store']);
        Route::get('edit/{id}', ['as' => 'mga-schemes.edit', 'uses' => 'MgaSchemeController@show']);
        Route::put('edit', ['as' => 'mga-schemes.update', 'uses' => 'MgaSchemeController@update']);
        Route::delete('delete/{id}', ['as' => 'mga-schemes.destroy', 'uses' => 'MgaSchemeController@destroy']);
    });

    Route::group(['prefix' => 'rhs'], function () {
        Route::get('financial-report', ['as' => 'rhs.financial-report', 'uses' => 'RhsFinancialReportController@index']);
        Route::get('financial-report/export', ['as' => 'rhs.financial-report.export', 'uses' => 'RhsFinancialReportController@export']);

        Route::group(['prefix' => 'organisations'], function () {
            Route::get('/', ['as' => 'rhs.organisations.index', 'uses' => 'RhsOrganisationController@index']);
            Route::get('{organisation}', ['as' => 'rhs.organisations.show', 'uses' => 'RhsOrganisationController@show']);
        });

        Route::group(['prefix' => 'customers'], function () {
            Route::get('{customer}', ['as' => 'rhs.customers.show', 'uses' => 'RhsCustomerController@show']);
            Route::get('{customer}/edit', ['as' => 'rhs.customers.edit', 'uses' => 'RhsCustomerController@edit']);
            Route::put('{customer}', ['as' => 'rhs.customers.update', 'uses' => 'RhsCustomerController@update']);
        });

        Route::group(['prefix' => 'orders'], function () {
            Route::get('/', ['as' => 'rhs.orders.index', 'uses' => 'RhsOrderController@index']);
            Route::get('create', ['as' => 'rhs.orders.create', 'uses' => 'RhsOrderController@create']);
            Route::post('/', ['as' => 'rhs.orders.store', 'uses' => 'RhsOrderController@store']);
            Route::get('{customer}/{order}', ['as' => 'rhs.orders.show', 'uses' => 'RhsOrderController@show']);
            Route::get('{customer}/{order}/{organisation}/edit', ['as' => 'rhs.orders.edit', 'uses' => 'RhsOrderController@edit']);
            Route::put('{order}', ['as' => 'rhs.orders.update', 'uses' => 'RhsOrderController@update']);
        });
    });

    //Community
    Route::group(['prefix' => 'community', 'before' => 'community', 'after' => 'no-cache'], function () {
        Route::get('/', ['as' => 'community.index', 'uses' => 'CommunityController@index']);

        Route::get('/sectors', ['uses' => 'CommunitySectorController@index']);
        Route::get('/download-ics/{message_id}', ['as' => 'community.compileICSFile', 'uses' => 'CommunityController@compileICSFile']);
        Route::post('message-search', ['uses' => 'CommunityController@messageSearch', 'as' => 'community.message-search']);

        // Poll
        Route::get('poll', 'CommunityController@getDiscussionWithPoll');

        // Discussion
        Route::group(['prefix' => 'discussion/'], function () {
            Route::get('start', ['as' => 'community.discussion.start', 'uses' => 'CommunityController@start']);
            Route::post('store', ['as' => 'community.discussion.store', 'uses' => 'CommunityController@store']);
            Route::post('vote', ['as' => 'community.discussion.vote', 'uses' => 'CommunityController@vote']);

            Route::get('{id}/edit', ['as' => 'community.discussion.edit', 'uses' => 'CommunityController@edit']);
            Route::post('{id}/update', ['as' => 'community.discussion.update', 'uses' => 'CommunityController@update']);
            Route::delete('{id}/delete', ['as' => 'community.discussion.delete', 'uses' => 'CommunityController@delete']);

            Route::put('{id}/interested', ['as' => 'community.discussion.interested', 'uses' => 'CommunityController@interested']);

            Route::get('{id}', ['as' => 'community.discussion.show', 'uses' => 'CommunityController@show']);

            Route::put('{id}/reply/{reply_id}/like', ['as' => 'community.discussion.like', 'uses' => 'CommunityController@like']);

            Route::group(['prefix' => 'reply/'], function () {
                Route::get('{id}', ['as' => 'community.discussion.reply.edit', 'uses' => 'CommunityController@showReply']);
                Route::post('store', ['as' => 'community.discussion.reply.store', 'uses' => 'CommunityController@storeReply']);
                //Route::get('{id}/edit', array('as' => 'community.discussion.reply.edit', 'uses' => 'CommunityController@editReply'));
                Route::post('{id}/update', ['as' => 'community.discussion.reply.update', 'uses' => 'CommunityController@updateReply']);
                Route::delete('{id}/delete', ['as' => 'community.discussion.reply.delete', 'uses' => 'CommunityController@deleteReply']);
            });

            Route::group(['prefix' => 'file/'], function () {
                Route::post('upload', ['as' => 'community.discussion.uploadfile', 'uses' => 'CommunityController@uploadFile']);

                Route::delete('{id}', ['as' => 'community.discussion.linkfile', 'uses' => 'CommunityController@linkFile']);
                Route::delete('{id}/delete', ['as' => 'community.discussion.deletefile', 'uses' => 'CommunityController@deleteFile']);
            });

            Route::group(['prefix' => 'popular/'], function () {
                Route::get('{type}/{subscription}', ['as' => 'community.discussion.popular', 'uses' => 'CommunityController@getPopular']);
            });
        });

        // Sector
        Route::group(['prefix' => '/sector'], function () {
            Route::post('/subscribe', ['as' => 'community.sector.subscribe', 'uses' => 'CommunitySectorController@subscribe']);
            Route::post('/unsubscribe', ['as' => 'community.sector.unsubscribe', 'uses' => 'CommunitySectorController@unsubscribe']);
            Route::post('/organisation-subscribe', ['as' => 'community.sector.org-subscribe', 'uses' => 'CommunitySectorController@orgSubscribe']);
            Route::post('/organisation-unsubscribe', ['as' => 'community.sector.org-unsubscribe', 'uses' => 'CommunitySectorController@orgUnsubscribe']);
        });
    });

    Route::group(['prefix' => 'rr-appetite'], function () {
        Route::get('/products', ['as' => 'rr-appetite.products.index', 'uses' => 'ProductController@index']);
        Route::get('/products/partial/{partial}/{count}/{product_id?}', ['as' => 'rr-appetite.products.partials', 'uses' => 'ProductController@combinationPartials']);
        Route::get('/products/partial/underwriters/{product_id?}', ['as' => 'rr-appetite.products.uwrpartials', 'uses' => 'ProductController@uwrPartials']);
        Route::get('/products/create', ['as' => 'rr-appetite.products.create', 'uses' => 'ProductController@store']);
        Route::post('/products/create', ['as' => 'rr-appetite.products.store', 'uses' => 'ProductController@store']);
        Route::post('/products/relationship', ['as' => 'rr-appetite.products.relationship.delete', 'uses' => 'ProductController@deleteCombination']);
        Route::get('/products/{id}/edit', ['as' => 'rr-appetite.products.edit', 'uses' => 'ProductController@update']);
        Route::post('/products/{id}/update', ['as' => 'rr-appetite.products.update', 'uses' => 'ProductController@update']);
        Route::delete('/products/{id}', ['as' => 'rr-appetite.products.destroy', 'uses' => 'ProductController@destroy']);
        Route::post('/products/{id}/delete-video-frame', ['as' => 'rr-appetite.products.delete-video-frame', 'uses' => 'ProductController@deleteVideoFrame']);

        Route::group(['prefix' => 'sectors'], function () {
            Route::get('/', ['as' => 'rr-appetite.sectors.index', 'uses' => 'SectorController@index']);
            Route::get('/create', ['as' => 'rr-appetite.sectors.create', 'uses' => 'SectorController@store']);
            Route::post('/create', ['as' => 'rr-appetite.sectors.store', 'uses' => 'SectorController@store']);
            Route::get('/{id}/edit', ['as' => 'rr-appetite.sectors.edit', 'uses' => 'SectorController@update']);
            Route::post('/{id}/update', ['as' => 'rr-appetite.sectors.update', 'uses' => 'SectorController@update']);
            Route::get('/{id}/delete', ['as' => 'rr-appetite.sectors.delete', 'uses' => 'SectorController@delete']);
        });

        Route::group(['prefix' => 'subsectors'], function () {
            Route::get('/', ['as' => 'rr-appetite.subsectors.index', 'uses' => 'SubsectorController@index']);
            Route::get('/create', ['as' => 'rr-appetite.subsectors.create', 'uses' => 'SubsectorController@store']);
            Route::post('/create', ['as' => 'rr-appetite.subsectors.store', 'uses' => 'SubsectorController@store']);
            Route::get('/{id}/edit', ['as' => 'rr-appetite.subsectors.edit', 'uses' => 'SubsectorController@update']);
            Route::post('/{id}/update', ['as' => 'rr-appetite.subsectors.update', 'uses' => 'SubsectorController@update']);
            Route::get('/{id}/delete', ['as' => 'rr-appetite.subsectors.delete', 'uses' => 'SubsectorController@delete']);
        });
        Route::group(['prefix' => 'enquiries'], function () {
            Route::get('/', [EnquiryController::class,'index'])->name('rr-appetite.enquiries.index');
            Route::get('/export/{type?}', [EnquiryController::class,'export'])->name('rr-appetite.enquiries.export');
            Route::get('/{id}', [EnquiryController::class,'show'])->name('rr-appetite.enquiries.show');
            Route::get('/{id}/archive', [EnquiryController::class,'changeArchiveStatus'])->name('rr-appetite.enquiries.archive');
            Route::get('/{id}/status', [EnquiryController::class,'changeEnquiryStatus'])->name('rr-appetite.enquiries.status');
            Route::get('/{id}/resend/{send_to?}', [EnquiryController::class,'resendBrokerEmail'])->name('rr-appetite.enquiries.resend');
        });
        Route::group(['prefix' => 'underwriters'], function () {
            Route::get('/', ['as' => 'rr-appetite.underwriters.index', 'uses' => 'UnderwriterController@index']);
        });
    });

    //CMS Products
    Route::group(['prefix' => 'cms-products'], function () {
        Route::get('get-products/{sector}/{subsector}', 'OrganisationController@getCmsProducts');
        Route::get('sector/{sector}/subsectors', 'OrganisationController@getCmsSubSector');
    });

    //Previsico Assets
    Route::group(['prefix' => 'previsico'], function () {
        Route::get('/get-assets/{org_id}', [OrganisationController::class,'getPrevisicoAssets'])->name('organisation-get-previsico-assets');
    });

    // Surveys
    Route::group(['prefix' => 'surveys'], function () {
        Route::post('resurvey-check', array('as' => 'surveys.resurvey-check', 'uses' => 'SurveyController@resurveyCheck'));
        Route::get('/my-surveys', [SurveyController::class,'mySurveys'])->name('surveys.my-surveys');

        Route::get('/create-legacy', array('as' => 'surveys.create-legacy', 'uses' => 'SurveyController@createLegacy'));
        Route::get('/edit-legacy/{id}', array('as' => 'surveys.edit-legacy', 'uses' => 'SurveyController@editLegacy'));
        Route::post('/edit-legacy/{id}', array('as' => 'surveys.update-legacy', 'uses' => 'SurveyController@updateLegacy'));
        Route::post('/store-legacy', array('as' => 'surveys.store-legacy', 'uses' => 'SurveyController@storeLegacy'));
        Route::get('/{id}/duplicate', array('as'                                           => 'surveys.duplicate', 'uses'                                                => 'SurveyController@duplicate'));
        Route::get('export-all', array('as'                                                => 'surveys.export-all', 'uses'                                                => 'SurveyController@exportAll'));
        Route::get('show_print', array('as'                                                => 'surveys.show_print', 'uses'                                                => 'SurveyController@showPdf'));
        Route::post('print/pdf', array('as'                                                => 'print.pdf', 'uses'                                                => 'SurveyController@printPdf'));
        Route::get('re-reviews', array('as'                                                => 'surveys.re-reviews-list', 'uses'                                                => 'SurveyController@reReviews'));
        Route::get('my-re-reviews', array('as'                                                => 'surveys.my-re-reviews-list', 'uses'                                                => 'SurveyController@myReReviews'));
        Route::get('re-actions/{id?}', array('as'                                          => 'surveys.re-actions', 'uses'                                          => 'SurveyController@showREReviewReport'));
        Route::get('{id}/decline', array('as'                                              => 'surveys.decline-survey', 'uses'                                              => 'SurveyController@declineSurvey'));
        Route::get('/legacy-report/{id}', array('as' => 'surveys.legacy-report', 'uses' => 'SurveyController@showLegacyReport'));
        Route::get('/report/{id}', array('as'                                              => 'surveys.report', 'uses'                                              => 'SurveyController@showReport'));
        Route::get('/{id}/report', array('as'                                              => 'surveys.reportalt', 'uses'                                              => 'SurveyController@showReport'));
        Route::get('messaging/attachment/{survey_id}/{attachment}/{filename?}', array('as' => 'surveys.message.getattachment', 'uses' => 'SurveyController@getMessageAttachment'));
        Route::get('/report/{id}/risk-recommendation/{risk_recommendation}', array('as'    => 'surveys.report.risk-recommendation', 'uses'    => 'SurveyController@showRiskRecommendation'));
        Route::get('/report/{id}/legacy-risk-recommendation/{legacy_risk_recommendation}', array('as'    => 'surveys.report.legacy-risk-recommendation', 'uses'    => 'SurveyController@showLegacyRiskRecommendation'));

        Route::post('messaging/send', array('as'     => 'surveys.send-message', 'uses'     => 'SurveyController@sendMessage'));
        Route::post('messaging/upload', array('as'   => 'surveys.message.attachment', 'uses'   => 'SurveyController@sendMessageWithAttachment'));
        Route::post('messaging/temp/upload', ['as' => 'surveys.message.temp.attachment', 'uses' => 'SurveyController@tempUploadAttachment']);
        Route::post('messaging/temp/deleteAttachment', ['as' => 'surveys.message.temp.delete-attachment', 'uses' => 'SurveyController@tempDeleteAttachment']);
        Route::post('/actual-dates/{id}', array('as' => 'surveys.actual-dates', 'uses' => 'SurveyController@actual_dates'));
        Route::post('{id}/surveyor', array('as'      => 'surveys.update-surveyor', 'uses'      => 'SurveyController@updateSurveyor'));
        Route::post('re-action', array('as'          => 'surveys.re-action-update', 'uses'          => 'SurveyController@reactionupdate'));
        Route::post('/update-survey-status', array('as' => 'update-survey-status', 'uses' => 'SurveyController@updateSurveyStatus'));

        Route::get('overview-report', array('as'                                                => 'surveys.overview-report', 'uses'                                                => 'SurveyController@overviewreport'));
        Route::get('export-overview-report', array('as'                                                => 'surveys.export-overview-report', 'uses'                                                => 'SurveyController@exportOverviewReport'));

        Route::post('{id}/agenda', array('as'                                                                    => 'surveys.update-agenda', 'uses'                                                                    => 'SurveyController@uploadAgenda'));
        Route::get('agenda/download/{cloud_file_name}/{file_name}', array('as'                                   => 'surveys.download-agenda', 'uses'                                   => 'SurveyController@downloadAgenda'));
        Route::get('file/download/{cloud_file_name}/{file_name}', array('as'                                     => 'surveys.download-survey-files', 'uses'                                     => 'SurveyController@downloadFiles'));
        Route::get('file/download/{cloud_folder_name}/{uploader_name}/{cloud_file_name}/{file_name}', array('as' => 'surveys.download-survey-files', 'uses' => 'SurveySubmissionsController@downloadFileUploads'));

        Route::get('delete/{survey_id}', array('as' => 'surveys.delete', 'uses' => 'SurveyController@delete'));

        // survey create plupload request
        Route::any('create/upload-files', ['as' => 'survey.create.upload', 'uses' => 'SurveyController@uploadFiles']);
        Route::any('{id}/destroy/{file_id}', 'SurveyController@deleteFile');
        Route::any('{id}/destroy-legacy/{file_id}', 'SurveyController@deleteLegacyFile');
        Route::get('export/all-organisations/filter', ['as' => 'survey.export.gradings.filter', 'uses' => 'SurveyController@exportRiskGradingsFilter']);

        Route::get('export/all-organisations', ['as' => 'survey.export.allorganisations', 'uses' => 'SurveyController@exportRiskGradings']);

        Route::get('export/fields/filter', ['as' => 'survey.export.fields.filter', 'uses' => 'SurveyController@exportSurveysFilter']);

        Route::get('export/all-fields', ['as' => 'survey.export.fields', 'uses' => 'SurveyController@exportSurveys']);

        Route::get('export/organisation/{organisation_id}', ['as' => 'survey.export.organisation', 'uses' => 'SurveyController@exportOrganisation']);

        Route::get('export/standard-risk-gradings', ['as' => 'survey.export.srg', 'uses' => 'SurveyController@standardRiskGradingsExport']);
        Route::get('export/standard-risk-gradings/data', ['as' => 'survey.export.srg.data', 'uses' => 'SurveyController@exportStandardRiskGradingsData']);

        Route::get('/{id}/data', [SurveyController::class, 'getSurveyData'])->name('surveys.data');
        Route::get('/{id}/options', [SurveyController::class, 'getSurveyOptions'])->name('surveys.options');
    });

    Route::resource('surveys', 'SurveyController')->parameters([
        'surveys' => 'id'
    ]);

    Route::group(['prefix' => 'microsite'], function () {
        Route::get('/{survey_id}', [MicrositeController::class, 'home'])->name('microsite.home');
        Route::get('/preview/{survey_id}', [MicrositeController::class, 'preview'])->name('microsite.preview');
        Route::post('toggle-column', [MicrositeController::class, 'toggleColumn'])->name('microsite.toggle-column');
        Route::post('risk-grading-re-order', [MicrositeController::class, 'riskGradingReOrder'])->name('microsite.risk-grading-re-order');
        Route::post('send-to-client', [MicrositeController::class, 'sendToClient'])->name('microsite.send-to-client');
        Route::post('toggle-commentary', [MicrositeController::class, 'toggleCommentary'])->name('microsite.toggle-commentary');
        Route::get('/{survey_id}/content', [MicrositeController::class, 'loadSurveyContent'])->name('microsite.content');
    });

    Route::group(['before' => ['notAspen']], function () {
        // Survey Calendar
        Route::group(['prefix' => 'calendar'], function () {
            Route::get('/', ['as' => 'schedule.index', 'uses' => 'ScheduleController@index']);
            Route::get('attachment/{cloud_file_name}/{file_name}', ['as' => 'schedule.get_attachment', 'uses' => 'ScheduleController@get_attachment']);
            Route::get('schedule/{schedule_type}', ['as' => 'schedule.create', 'uses' => 'ScheduleController@create']);
            Route::get('schedule/{schedule_type?}/{id}', ['as' => 'schedule.edit', 'uses' => 'ScheduleController@edit']);
            Route::post('schedule/{schedule_type}', ['as' => 'schedule.store', 'uses' => 'ScheduleController@store']);
            Route::post('schedule/{schedule_type?}/{id}', ['as' => 'schedule.update', 'uses' => 'ScheduleController@update']);

            Route::delete('schedule/destroy/{id}', ['as' => 'schedule.destroy', 'uses' => 'ScheduleController@destroy']);

            Route::any('create/upload-files', ['as' => 'schedule.create.upload', 'uses' => 'ScheduleController@uploadFiles']);

            //Route::post('schedule/re-admin/upload-files', array('as'          => 'schedule.upload-files', 'uses'          => 'ScheduleController@uploadFiles'));
        });
    });

    Route::get('cqlive/get-payload', 'CQLive\CQLiveController@getPayload');

    // Client Enquiries
    Route::group(['prefix' => 'client-enquiries'], function () {
        Route::get('/', ['as' => 'enquiries.client-enquiries', 'uses' => 'ClientEnquiriesController@index']);
    });

    // Kanban Boards
    Route::get('tracker/{kanban_type}', ['as' => 'kanban.index', 'uses' => 'KanbanController@index']);
    Route::get('tracker/surveys/next', ['as' => 'kanban.surveynext', 'uses' => 'KanbanController@surveyNext']);
    Route::get('tracker/{kanban_type}/next', ['as' => 'kanban.next', 'uses' => 'KanbanController@next']);

    // Key account export
    Route::get('keyaccounts/export', ['as' => 'keyaccount.export', 'uses' => 'OrganisationKeyAccountController@exportKeyAccount']);
    
    Route::get('tracker/risk-recommendations/export', ['as' => 'kanban.risk-recommendations.export', 'uses' => 'KanbanController@export']);

    Route::get('tracker/risk-recommendations/export-queue', ['as' => 'kanban.risk-recommendations.export-queue', 'uses' => 'KanbanController@queueExport']);

    //if(Session::has('user') && Session::get('user')->login_type != 'aspen-user') {
    // External Survey Companies
    Route::resource('external-survey-companies', 'ExternalSurveyCompanyController', ['except' => ['show']]);

    // Brokers
    Route::get('brokers/schemes/{id}', ['as' => 'brokers.schemes', 'uses' => 'BrokerController@schemes']);
    Route::resource('brokers', 'BrokerController', ['except' => ['show']]);

    // Ajax calls
    //Route::get('learning/course/{id}/get-data', 'OrganisationController@index');
    Route::get('trade-groupings/{id}/default', 'TradeGroupsController@defaultTradeGroup');
    Route::resource('trade-groupings', 'TradeGroupsController')->parameters([
        'trade-groupings' => 'id'
    ]);
    Route::get('trades/options/{id}', ['as' => 'trades.options', 'uses' => 'TradesController@options']);
    Route::resource('trades', 'TradesController');

    // Video Calls
    Route::resource('video-call', 'VideoCallController');

    // @NOTE: Decommissioned
    // Route::group(['prefix' => 'virtual-rooms'], function () {
    //     Route::get('/rep-schedules/{person_id}/{date?}', ['as' => 'virtual-rooms.rep-schedules', 'uses' => 'LetsTalk\VideoCallController@getRepSchedules']);
    //     Route::get('/vr-session/{business}', 'AuthController@vrSession');
    //     Route::resource('video-call', 'LetsTalk\VideoCallController');
    //     Route::get('/schedule', 'LetsTalk\VideoCallController@schedule');
    //     Route::post('/book', ['as' => 'virtual-rooms.book', 'uses' => 'LetsTalk\VideoCallController@book']);
    //     Route::get('/bookings/{date?}', ['as' => 'virtual-rooms.bookings', 'uses' => 'LetsTalk\VideoCallController@bookings']);
    //     Route::get('/{uuid}/downloads3link', 'LetsTalk\VideoCallController@downloads3link');
    //     Route::get('/representative-call-availability/{date?}', ['as' => 'virtual-rooms.representative-call-availability', 'uses' => 'LetsTalk\VideoCallController@representativeCallAvaialbility']);
    //     Route::post('/representative-call-availability-slots/{date?}', ['as' => 'virtual-rooms.representative-call-availability-slots', 'uses' => 'LetsTalk\VideoCallController@getCallAvaialbilitySlots']);
    //     Route::get('/representative-call-availability-slots/{date?}', ['as' => 'virtual-rooms.representative-call-availability-slots', 'uses' => 'LetsTalk\VideoCallController@getCallAvaialbilitySlots']);
    //     Route::get('/create-representative-call-availability/{person_id}/{date_range}', ['as' => 'virtual-rooms.create-representative-call-availability', 'uses' => 'LetsTalk\VideoCallController@createRepresentativeCallAvaialbility']);
    //     Route::post('/set-recurring-call-availability', ['as' => 'virtual-rooms.set-recurring-call-availability', 'uses' => 'LetsTalk\VideoCallController@setRecurringAvailability']);
    //     Route::post('/remove-all-repeated-availability-slots', ['as' => 'virtual-rooms.remove-all-repeated-availability-slots', 'uses' => 'LetsTalk\VideoCallController@removeAllRecurringAvailability']);
    //     Route::get('/get-representatives/{date?}', ['as' => 'virtual-rooms.get-representatives', 'uses' => 'LetsTalk\VideoCallController@updatedRepresentativesData']);
    //     Route::get('/delete-booking/{booking_id}', ['as' => 'virtual-rooms.delete-booking', 'uses' => 'LetsTalk\VideoCallController@deleteBooking']);
    //     Route::get('/check-call-now-bookings', ['as' => 'virtual-rooms.check-call-now-bookings', 'uses' => 'LetsTalk\VideoCallController@checkCallNowBookings']);
    //     Route::resource('external-contacts', 'LetsTalk\ExternalContactsController')->except('create', 'edit');
    //     Route::get('external-contacts', ['as' => 'virtual-rooms.external-contacts.index', 'uses' => 'LetsTalk\ExternalContactsController@index']);
    //     Route::get('/parse-ics', ['as' => 'virtual-rooms.get-ics', 'uses' => 'LetsTalk\VideoCallController@parseICS']);
    //     Route::get('/reception-notification-subscription', ['as' => 'virtual-rooms.reception-notification-subscription', 'uses' => 'LetsTalk\VideoCallController@receptionNotificationSubscription']);
    //     Route::get('/get-lib-rep', ['as' => 'virtual-rooms.get-lib-rep', 'uses' => 'LetsTalk\VideoCallController@getLibRepByName']);

    //     Route::get('/export-contacts', 'LetsTalk\ExportController@exportContacts');
    //     Route::get('/export-profiles', 'LetsTalk\ExportController@exportProfiles');
    //     Route::get('/import-contacts', 'LetsTalk\ExportController@importContacts');
    //     Route::get('/import-liberty-users', 'LetsTalk\ExportController@importLibertyUsers');
    //     Route::get('/login-attempt-logs', ['as' => 'virtual-rooms.login-attempt-logs', 'uses' => 'LetsTalk\AuthenticateUserController@loginAttemptLogs']);
    //     Route::post('/delete-login-attempt-log', ['as' => 'virtual-rooms.delete-login-attempt-log', 'uses' => 'LetsTalk\AuthenticateUserController@deleteLoginAttemptLog']);
    // });

    // Flood Alerts
    Route::get('/flood-alert/status', ['as' => 'flood-alerts.status', 'uses' => 'FloodAlertsController@getAlertUiStatus']);

    // RR Portfolio Views
    Route::group(['prefix' => 'portfolio-views', 'middleware' => ['auth-portfolio-views']], function() {
        Route::get('/insights', ['as' => 'portfolio-view.insights.index', 'uses' => 'PortfolioViews\InsightsController@index']);
        Route::get('/insights/{id}', ['as' => 'portfolio-view.insights.show', 'uses' => 'PortfolioViews\InsightsController@show']);
        Route::post('/insights', ['as' => 'portfolio-view.insights.store', 'uses' => 'PortfolioViews\InsightsController@store']);
        Route::delete('/insights/{id}', ['as' => 'portfolio-view.insights.delete', 'uses' => 'PortfolioViews\InsightsController@delete']);
        Route::get('/customise', ['as' => 'portfolio-view.customise.index', 'uses' => 'PortfolioViews\InsightsController@customiseIndex']);
        Route::get('/customise/{template}', ['as' => 'portfolio-view.customise.report', 'uses' => 'PortfolioViews\InsightsController@customiseReport']);
    });

    // API routes
    Route::group(['prefix' => 'api/v1/'], function () {
        Route::post('video-call', ['as' => 'video-call.post']);

        // @NOTE: Decommissioned
        // Route::group(['prefix' => 'virtual-rooms'], function () {
        //     Route::post('video-call', ['as' => 'virtual-rooms.video-call.post']);
        // });

        Route::group(['prefix' => 'community'], function () {
            Route::get('/get-sector', ['as' => 'api.community.getSector']);
            Route::get('/get-community-tag', ['as' => 'api.community.getCommunityTag']);

            // Sectors
            Route::group(['prefix' => '/sectors'], function () {
                Route::get('/subscriptions/user/{id}/{type}', ['as' => 'api.community.sector.subscriptions']);
                Route::get('/subscriptions/organisation/{id}', ['as' => 'api.community.sector.org-subscriptions']);

                Route::post('/subscribe', ['as' => 'api.community.sector.subscribe']);
                Route::post('/unsubscribe', ['as' => 'api.community.sector.unsubscribe']);
                Route::post('/organisation-subscribe', ['as' => 'api.community.sector.org-subscribe']);
                Route::post('/organisation-unsubscribe', ['as' => 'api.community.sector.org-unsubscribe']);

                Route::get('/deleted', ['as' => 'api.community.sector.deleted']);
            });

            // Messages

            Route::group(['prefix' => 'messages/'], function () {
                Route::put('{id}/reply/{reply}/like', ['as' => 'api.community.message.like']);

                Route::get('get-popular-event/{type}', ['as' => 'api.community.messages.popular']);
                Route::get('upcoming/{id}/{type}', ['as' => 'api.community.messages.upcoming']);

                Route::put('{id}/interested', ['as' => 'api.community.webinar.interested']);

                Route::post('vote', ['as' => 'api.community.message.vote']);

                Route::post('store', ['as' => 'api.community.message.store']);
                Route::get('{id}', ['as' => 'api.community.message.get']);
                Route::get('{id}/update', ['as' => 'api.community.message.update']);
                Route::delete('{id}/delete', ['as' => 'api.community.message.delete']);

                Route::group(['prefix' => 'file/'], function () {
                    Route::post('upload', ['as' => 'api.community.message.file.upload']);
                    Route::get('{id}', ['as' => 'api.community.message.file.link']);
                    Route::delete('{id}/delete', ['as' => 'api.community.message.file.delete']);
                });

                Route::group(['prefix' => 'reply/'], function () {
                    Route::post('store', ['as' => 'api.community.message.reply.store']);
                    Route::get('{id}', ['as' => 'api.community.message.reply.get']);
                    Route::get('{id}/update', ['as' => 'api.community.message.reply.update']);
                    Route::delete('{id}/delete', ['as' => 'api.community.message.reply.delete']);
                });
            });
        });

        Route::group(['prefix' => '/risk-insights'], function () {
            Route::get('/dashboard', ['as' => 'api.risk-insights.dashboard']);
            Route::get('/dashboard/company', ['as' => 'api.risk-insights.dashboard.company']);
            Route::get('/dashboard/location', ['as' => 'api.risk-insights.dashboard.location']);
            Route::get('/dashboard/risk', ['as' => 'api.risk-insights.dashboard.risk']);
            Route::get('/benchmarking', ['as' => 'api.risk-insights.benchmarking']);
            Route::get('/benchmarking/organisation', ['as' => 'api.risk-insights.benchmarking.organisation']);
            Route::get('/benchmarking/attribute/{attributeId}', ['as' => 'api.risk-insights.benchmarking.attribute']);
            Route::get('/portfolio-distribution', ['as' => 'api.risk-insights.portfolio-distribution']);
        });
    });

    Route::prefix('dtr')->group(function() {
        Route::get('/', [DtrMicrositeController::class, 'index'])->name('dtr.index');
        Route::post('/', [DtrMicrositeController::class, 'store'])->name('dtr.store');
        Route::get('/export-all', [DtrMicrositeController::class, 'exportAll'])->name('dtr.export-all');
        Route::get('/create', [DtrMicrositeController::class, 'create'])->name('dtr.create');
        Route::get('/my-reviews', [DtrMicrositeController::class, 'myReviews'])->name('dtr.my-reviews');
        
        Route::post('/oganisation/create', [DtrMicrositeController::class, 'createOrganisation'])->name('dtr.organisation.create');
        
        Route::get('microsite/{survey_id}', [MicrositeController::class, 'home'])->name('dtr.microsite.home');
        Route::get('microsite/preview/{survey_id}', [MicrositeController::class, 'preview'])->name('dtr.microsite.preview');

        Route::get('/{id}', [SurveyController::class, 'show'])->name('dtr.show');
        Route::put('/{id}', [DtrMicrositeController::class, 'update'])->name('dtr.update');
        Route::get('/{id}/edit', [DtrMicrositeController::class, 'edit'])->name('dtr.edit');
    });

    Route::get('/microsite/print/{survey_id}', [MicrositeController::class, 'printPrivateVersion'])->name('csrmicrosite.public.print-pdf');

    Route::prefix('/risk-insights')->group(function() {
        Route::get('/', [RiskInsightsDashboardController::class, 'index'])->name('risk-insights.index');
        Route::get('/risk-grading', [RiskInsightsRiskGradingController::class, 'index'])->name('risk-insights.risk-grading');
        Route::get('/risk-benchmark', [RiskInsightsBenchmarkController::class, 'index'])->name('risk-insights.risk-benchmark');
        Route::get('/location', [RiskInsightsLocationController::class, 'index'])->name('risk-insights.risk-location');
        Route::post('/location/update', [RiskInsightsLocationController::class, 'updateRiskReportLocation'])->name('risk-insights.risk-location.update');
        Route::get('/view-report', [RiskInsightsLocationController::class, 'viewReport'])->name('risk-insights.view-report');
        Route::get('/review-risk-report', [RiskInsightsDashboardController::class, 'getReviewRiskReport'])->name('risk-insights.review-risk-report');

        Route::get('/view-report-narratives', [RiskInsightsLocationController::class, 'viewReportNarratives'])->name('risk-insights.view-report-narratives');
        Route::get('/download-full-survey-report', [RiskInsightsLocationController::class, 'downloadFullSurveyReport'])->name('risk-insights.download-full-survey-report');

        Route::get('/get-portfolio-impact', [RiskInsightsModalController::class, 'getPortfolioImpact'])->name('risk-insights.get-portfolio-impact');

        Route::get('/organizations', [RiskInsightsDashboardController::class, 'getOrganizations'])->name('risk-insights.organizations');
        Route::get('/organization-locations', [RiskInsightsDashboardController::class, 'getOrganizationLocations'])->name('risk-insights.organization-locations');

        Route::get('/organisation-dashboard/{organizationId}', [RiskInsightsDashboardController::class, 'getOrganizationDashboard'])->name('risk-insights.organization-dashboard');
        Route::get('/organisation-location-dashboard/{organizationId}/{locationId}', [RiskInsightsDashboardController::class, 'getOrganizationLocationDashboard'])->name('risk-insights.organization-location-dashboard');
        Route::post('/set-org-animated', [RiskInsightsDashboardController::class, 'setOrgAnimated'])->name('risk-insights.set-org-animated');
        Route::post('/set-org-added', [RiskInsightsDashboardController::class, 'setOrgAdded'])->name('risk-insights.set-org-added');

        Route::get('/modal-content', [RiskInsightsModalController::class, 'getModalContent'])->name('risk-insights.modal-content');
        Route::get('/review-modal-content', [RiskInsightsModalController::class, 'getReviewModalContent'])->name('risk-insights.review-modal-content');
        Route::post('/process-files-for-rafa', [RiskInsightsModalController::class, 'processFilesForRAFA'])->name('risk-insights.process-files-for-rafa');
        Route::post('/add-to-database', [RiskInsightsModalController::class, 'addToDatabase'])->name('risk-insights.add-to-database');

        Route::get('/risk-report-data/{documentId}', [RiskInsightsModalController::class, 'getRiskReportResult'])->name('risk-insights.risk-report-data');
        Route::post('/complete-review', [RiskInsightsModalController::class, 'completeReview'])->name('risk-insights.complete-review');
        Route::post('/remove-document-nhitl', [RiskInsightsModalController::class, 'removeDocumentNHITLTag'])->name('risk-insights.remove-document-nhitl');
        Route::post('/submit-review-weightage', [RiskInsightsModalController::class, 'submitReviewWeightage'])->name('risk-insights.submit-review-weightage');

        Route::get('/poll-updates', [RiskInsightsModalController::class, 'pollUpdates'])->name('risk-insights.poll-updates');

        Route::get('/document-board/{type?}', [RiskInsightsBoardController::class, 'index'])->name('risk-insights.document-request-board');
        Route::post('/document-assignment', [RiskInsightsBoardController::class, 'documentAssignment'])->name('risk-insights.document-assignment');
    });


    // Risk Engineering Productivity Metrics
    Route::group(['middleware' => ['auth-re-metrics-views'], 'prefix' => 're-metrics'], function () {
        Route::get('/', [ReMetricsController::class, 'index'])->name('re-metrics.dashboard');
        Route::get('/organisation-table', [ReMetricsController::class, 'organisationTable'])->name('re-metrics.organisation-table');
        Route::get('/fetch-organisation-table-data', [ReMetricsController::class, 'fetchOrganisationTableData'])->name('re-metrics.fetch-organisation-table-data');
        Route::get('/fetch-chart-data', [ReMetricsController::class, 'fetchChartData'])->name('re-metrics.fetch-chart-data');
        Route::get('/table-by-chart/{chart_type}', [ReMetricsController::class, 'tableByChart'])->name('re-metrics.table-by-chart')
            ->where('chart_type', 'srfs|dtrs|risk-recommendation|desktop-reviews-impact|survey-programme-in-place|re-overview-updated|re-meeting-held|accounts-by-sector');
    });
});

// @NOTE: Remove comment once Social Auth needed for azure
// Route::prefix('auth')->group(function() {
//     Route::get('/redirect/{provider}', [SocialLoginController::class, 'redirect']);
//     Route::get('/callback/{provider}', [SocialLoginController::class, 'callback']);
// });

Route::group(['prefix' => 'csr-microsite'], function () {
    Route::group(['prefix' => 'templates'], function () {
        Route::get('/', [TemplateController::class, 'index'])->name('csrmicrosite.template.index');
        Route::get('/home', [TemplateController::class, 'home'])->name('csrmicrosite.template.home');
        Route::get('/login', [TemplateController::class, 'login'])->name('csrmicrosite.template.login');
        Route::get('/otp', [TemplateController::class, 'otp'])->name('csrmicrosite.template.otp');
        Route::get('/print-preview', [TemplateController::class, 'printpreview'])->name('csrmicrosite.template.printpreview');
    });
});

Route::get('/microsite/print-preview/{guid}', [MicrositeController::class, 'previewVersion'])->name('csrmicrosite.public.preview-pdf')->where('guid', '.*');
Route::get('/microsite/print-public/{guid}', [MicrositeController::class, 'printVersion'])->name('csrmicrosite.publicprint.print-pdf')->where('guid', '.*');
