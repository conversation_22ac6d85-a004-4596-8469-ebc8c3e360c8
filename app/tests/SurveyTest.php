<?php

class SurveyTest extends TestCase {

	// IMPORTANT: comment redirectNotAccessRoute during testing !!!
	// public function test_visit_survey_page_using_admin_role()
	// {
	// 	$this->loginRiskControl();
	// 	$this->call('GET', 'surveys');
	// 	$this->assertResponseStatus(200);
	// }

	// public function test_visit_existing_survey_page_using_admin_role()
	// {
	// 	$this->loginRiskControl();
	// 	$this->call('GET', 'surveys/460');
	// 	$this->assertResponseStatus(200);
	// }

	// public function test_visit_create_survey_page_using_admin_role()
	// {
	// 	$this->loginRiskControl();
	// 	$this->call('GET', 'surveys/create');
	// 	$this->assertResponseStatus(200);
	// }

	public function test_visit_create_eternal_survey_login_page()
	{
		$this->loginRiskControl();
		$this->call('GET', 'login/external-surveyor');
		$this->assertResponseStatus(200);
	}

	// public function test_visit_survey_page_using_external_surveyor()
	// {
	// 	$this->loginExternalSurveyor();
	// 	$this->call('GET', 'surveys');
	// 	$this->assertResponseStatus(200);
	// }

	// public function test_visit_existing_survey_page_using_external_surveyor()
	// {
	// 	$this->loginExternalSurveyor();
	// 	$this->call('GET', 'surveys/438');
	// 	$this->assertResponseStatus(200);
	// }

	// public function test_visit_existing_csr_page_using_external_surveyor()
	// {
	// 	$this->loginExternalSurveyor();
	// 	$this->call('GET', 'risk-improvement/form/submission/61e820e3de45cb5b2ad90e43?form=csr&csr_status=completed');
	// 	$this->assertResponseStatus(200);
	// }

	// public function test_visit_existing_uwr_page_using_external_surveyor()
	// {
	// 	$this->loginExternalSurveyor();
	// 	$this->call('GET', 'risk-improvement/form/submission/61e820e3de45cb5b2ad90e43?form=uwr');
	// 	$this->assertResponseStatus(200);
	// }

	private function loginRiskControl() {
		// establish session
		$this->session(
			['user' =>  
				(object) [
					'id' => 5,
					'first_name' => "Shashi",
					'last_name' => "Saurav",
					'email' => '<EMAIL>',
					'type' => 'liberty-user',
					'role'=> 'admin',
					'login_type' => 'risk-control'
				]
			]
		);
	}

	private function loginExternalSurveyor() {
		$this->session(
			['user' =>  
				(object) [
					'id' => 5,
					'first_name' => "Shashi",
					'last_name' => "Saurav",
					'email' => '<EMAIL>',
					'type' => 'external-surveyor',
					'role'=> 'admin',
					'login_type' => 'risk-engineer',
					'external_survey_company_id' => 1
				]
			]
		);
	}
}
