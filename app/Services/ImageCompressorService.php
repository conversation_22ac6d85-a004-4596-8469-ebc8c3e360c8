<?php
namespace App\Services;

use Illuminate\Http\UploadedFile;
use Intervention\Image\Facades\Image;

class ImageCompressorService
{
    const MAX_WIDTH_PX = 1240;
    const MAX_HEIGHT_PX = 1754;

    public static function compress($file)
    {
        $filename  = $file->getClientOriginalName();
        $extension = $file->getClientOriginalExtension();

        if ($extension === 'png' || $extension === 'jpg' || $extension === 'jpeg') {
            Image::configure(['driver' => 'imagick']);

            $image = Image::make($file);
            $size = $image->filesize();
            $calcSize = $size / 1024 / 1024; // Convert into MB

            if ($calcSize > 1) { // compress if greater than 1MB
                \Log::info('[Image Compress]: Starting...');

                $width = $image->width();
                $height = $image->height();

                $newWidth = $width;
                $newHeight = $height;
                if ($width > self::MAX_WIDTH_PX && $height > self::MAX_HEIGHT_PX) {
                    $newWidth = self::MAX_WIDTH_PX;
                    $newHeight = (int)(($newWidth / $width) * $height);
                } elseif ($width > self::MAX_WIDTH_PX) {
                    $newWidth = self::MAX_WIDTH_PX;
                    $newHeight = (int)(($newWidth / $width) * $height);
                } elseif ($height > self::MAX_HEIGHT_PX) {
                    $newHeight = self::MAX_HEIGHT_PX;
                    $newWidth = (int)(($newHeight / $height) * $width);
                }

                \Log::info('[Image Compress]: Resizing to ' . $newWidth . 'x' . $newHeight);

                $image->resize($newWidth, $newHeight, function ($constraint) {
                    $constraint->aspectRatio();  // Maintain aspect ratio
                    $constraint->upsize();       // Prevent upsizing
                });

                $tmpPath = tempnam(sys_get_temp_dir(), 'compress-');

                // Correct the orientation based on EXIF data
                $image->orientate();

                $image->encode($extension, '50');
                $image->save($tmpPath);

                $mimeType = mime_content_type($tmpPath);

                $uploadFile = new UploadedFile(
                    $tmpPath,
                    $filename,
                    $mimeType,
                    null,
                    true
                );

                \Log::info('[Image Compress]: Done');
    
                return $uploadFile;
            }
        }
        return $file;
    }
}
