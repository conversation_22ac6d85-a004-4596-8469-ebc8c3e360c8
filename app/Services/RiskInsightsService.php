<?php

namespace App\Services;

use App\Models\Api;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Session;

class RiskInsightsService
{
    public function getOrganisationBenchmarking($params = [])
    {
        return json_decode(Api::getWithParams(route('api.risk-insights.benchmarking.organisation', [], false), $params));
    }

    public function getDashboardData($params = [])
    {
        return json_decode(Api::getWithParams(route('api.risk-insights.dashboard', [], false), $params));
    }

    public function getDashboardCompany($params = [])
    {
        return json_decode(Api::getWithParams(route('api.risk-insights.dashboard.company', [], false), $params));
    }

    public function getDashboardLocation($params = [])
    {
        return json_decode(Api::getWithParams(route('api.risk-insights.dashboard.location', [], false), $params));
    }

    public function getRiskDashboard()
    {
        return json_decode(Api::get(route('api.risk-insights.dashboard.risk', [], false)));
    }

    public function getBenchMarking($params = [])
    {
        return json_decode(Api::getWithParams(route('api.risk-insights.benchmarking', [], false), $params));
    }

    public function getPortfolioDistribution($params = [])
    {
        return json_decode(Api::getWithParams(route('api.risk-insights.portfolio-distribution', [], false), $params));
    }

    public function getRiskReportResult($documentId)
    {
        $user = Session::get('user');
        return json_decode(Api::get('api/v1/risk-insights/risk-report-data/'.$documentId, ['userId' => $user->id]));
    }

    public function updateDocumentStatus($documentId, $userId)
    {
        $response = Api::post('/api/v1/risk-insights/update-document-status', ['documentId' => $documentId, 'userId' => $userId]);
        return $response;
    }

    public function removeDocumentNHITLTag($documentId)
    {
        $response = Api::post('/api/v1/risk-insights/remove-document-nhitl', ['documentId' => $documentId]);
        return $response;
    }

    public function getFilters()
    {
        return json_decode(Api::get('/api/v1/risk-insights/get-filters', [], false));
    }

    public function getAttributeScores($attributeId, $organisationId = null)
    {
        $data = ['organisation_id' => $organisationId];
        return json_decode(Api::get(route('api.risk-insights.benchmarking.attribute', ['attributeId' => $attributeId], false), $data));
    }
}