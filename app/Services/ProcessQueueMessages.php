<?php

namespace App\Services;

use Aws\Laravel\AwsFacade as AWS;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ProcessQueueMessages extends Command
{
    private $queueName = '';

    public function __construct(string $queueName = null)
    {
        $this->queueName = $queueName;
    }

    public function process()
    {
        // Get Queue messages
        $messages = $this->fetchMessages();
        if (empty($messages)) {
            Log::info("ProcessQueueMessages: No queue message found in " . $this->getQueueName());
            return;
        }

        // loop through each and execute services class
        foreach ($messages as $message) {
            try {
                Log::info("[ProcessQueueMessages]: " . $message['Body']);
                
                $body = json_decode($message['Body']);
                if (empty($body->serviceClass)) {
                    continue;
                }
                $class = new $body->serviceClass;

                if (method_exists($class, 'recache')) {
                    $class->recache($body->params ?? '');
                }
                Log::info("[ProcessQueueMessages][{$this->getQueueName()}]: Done:" . $body->params ?? '');

                $this->deleteMessage($message['ReceiptHandle']);
                $processedIds[] = $message['MessageId'];
            } catch (\Exception $e) {
                $this->deleteMessage($message['ReceiptHandle']);
                $this->sendRequeue($message);

                Log::error("[ProcessQueueMessages:ERROR] " . $e->getMessage());
                Log::error("[ProcessQueueMessages:ERROR] " . $e->getTraceAsString());
            }
        }
    }

    public function fetchMessages(): array
    {
        $queues = AWS::createClient('Sqs')->receiveMessage(
            [
                'QueueUrl' => $this->getQueueName(),
                'MaxNumberOfMessages' => 10,
            ]
        );
        return $queues['Messages'] ?? [];
    }

    private function deleteMessage(string $receiptHandle): void
    {
        AWS::createClient('Sqs')->deleteMessage(
            [
                'QueueUrl' => $this->getQueueName(),
                'ReceiptHandle' => $receiptHandle,
            ]
        );
    }

    private function getQueueName()
    {
        return $this->queueName ?? config('app.aws.invalidate_cache_sqs_admin');
    }

    protected function sendRequeue($message)
    {
        $body = json_decode($message['Body'], true);
        $body['isRequeue'] = true;
        SendSqsMessageService::sendMessages([$body], $this->getQueueName());
    }
}