<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use App\Models\Api;

class ReCacheService
{
    public function __construct()
    {

    }

    public static function reCache(string $key, string $api, $method = 'get')
    {
        $response = json_decode(Api::{$method}($api));
        $data = $response;
        if($response){
            if (isset($response->data)) {
                $data = $response->data;
            }
            Cache::forever($key, $data);
            return $data;
        }
        return [];
    }
}
