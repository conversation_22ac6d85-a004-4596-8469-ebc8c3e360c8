<?php

namespace App\Services\CacheContent;

use App\Models\Api;

class GetBrokerUserService extends CacheContentService {

    public static function get($id = null, $getUpdated = false)
    {
        $cacheKey = 'broker-user-for-datatable';
        $resource = self::getSetCacheForever($cacheKey);

        if (!$resource || $getUpdated || ($resource->response ?? '' === 'error')) {
            $resource = json_decode(Api::get('api/v1/broker-users/get-data-for-datatable'));
            self::getSetCacheForever($cacheKey, $resource);
        }

        return $resource;
    }

    public static function recache($id = null)
    {
        return static::get($id, getUpdated: true);
    }
}