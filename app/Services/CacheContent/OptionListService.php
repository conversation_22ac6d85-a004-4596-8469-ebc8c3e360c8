<?php

namespace App\Services\CacheContent;

use App\Models\Api;

class OptionListService extends CacheContentService {

    const OPTIONS_URI = [
        'branches'      => 'liberty-branches/options',
        'organisations' => 'organisation/options',
        'underwriters'  => 'liberty-users/options/underwriter',
        'aspenusers'    => 'liberty-users/options/aspen-user',
        'brokers'       => 'brokers/options',
        'forms'         => 'risk-improvement/form/list',
    ];
    
    public static function get($id, $getUpdated = false)
    {
        $optCacheKey = 'option-list-' . $id;
        $resource    = self::getSetCacheForever($optCacheKey);

        if (!$resource || $getUpdated || ($resource->response ?? '' === 'error')) {
            if (array_key_exists($id, self::OPTIONS_URI)) {
                $endpoint = 'api/v1/' . self::OPTIONS_URI[$id];
                $resource = json_decode(Api::get($endpoint));

                self::getSetCacheForever($optCacheKey, $resource);
            }
        }

        return $resource ?? [];
    }
}