<?php

namespace App\Services\CacheContent;

use App\Models\Api;
use Illuminate\Support\Facades\Cache;

class GetSurveyService extends CacheContentService
{

    private static function getCacheKey($id)
    {
        return "admin_survey-data-for-surveyid-{$id}";
    }

    public static function get($id, $getUpdated = false)
    {
        if (empty($id)) {
            return;
        }

        $cacheKey = self::getCacheKey($id);
        $resource = self::getSetCacheForever($cacheKey);
        
        if (!$resource || $getUpdated || ($resource->response ?? '' === 'error')) {
            $resource = json_decode(Api::get('api/v1/surveys/' . $id));

            // Clear related cache
            if ($resource->response == 'success') {
                if (($resource->data->resurvey_id ?? '') != '') {
                    Cache::forget("admin_csr-pdf-link-for-resurvey-id-{$resource->data->resurvey_id}");
                    Cache::forget("admin_uwr-pdf-link-for-resurvey-id-{$resource->data->resurvey_id}");
                    Cache::forget("admin_resurvey-details-for-resurvey-id-{$resource->data->resurvey_id}");
                }

                Cache::forget("admin_csr-microsite-survey-{$id}");
            }

            self::getSetCacheForever($cacheKey, $resource);

            // Rebuild cache of related cache
            self::recacheSurveyDetails($id, true);
        }

        return $resource;
    }

    public static function getSurveyDetails(int $id): array
    {
        if (empty($id)) {
            return [];
        }

        return self::recacheSurveyDetails($id);
    }

    private static function recacheSurveyDetails(int $id, bool $getUpdated = false): array
    {
        $cacheKey = "admin_csr-microsite-survey-details-{$id}";
        $surveyDetailsData = self::getSetCacheForever($cacheKey);
        if (!$surveyDetailsData || $getUpdated || ($surveyDetailsData->response ?? '' === 'error')) {
            $surveyData = self::getSetCacheForever(self::getCacheKey($id));
            $surveyDetailsData = (new self())->surveyDetails($surveyData);
            self::getSetCacheForever($cacheKey, $surveyDetailsData);
        }

        return $surveyDetailsData;
    }

    private function surveyDetails(\stdClass $surveyData): array
    {
        $data = $surveyData->data;
        $surveyDetails = [
            'location' => $data->location ?? null,
            'organisation_name' => $data->organisation_name ?? $data->organisation->name ?? '',
            'organisation_id' => $data->organisation->id ?? null,
            'sector' => $data->organisation->sector ?? null,
            'next_survey_date' => $data->next_survey_due_date ?? null,
            'business_interruption' => $data->srf->business_interruption ?? null,
            'bi_total' => isset($data->srf->business_interruption) ? $this->getSRFLineOfBusinessTotal(
                $data->srf->business_interruption
            ) : 0,
            'material_damage' => $data->srf->material_damage ?? null,
            'material_damage_total' => isset($data->srf->material_damage) ? $this->getSRFLineOfBusinessTotal(
                $data->srf->material_damage
            ) : 0,
            'specified_material_damage' => $data->srf->specified_material_damage ?? null,
            'specified_material_damage_total' => isset($data->srf->specified_material_damage)
                ? $this->getSRFLineOfBusinessTotal(
                    $data->srf->specified_material_damage
                ) : 0,
            'employee_liability_sir' => $data->srf->employee_liability_sir ?? null,
            'emp_liability_total' => isset($data->srf->employee_liability_sir) ? $this->getSRFLineOfBusinessTotal(
                $data->srf->employee_liability_sir
            ) : 0,
            'public_products_liability_sir' => $data->srf->public_products_liability_sir ?? null,
            'public_products_liability_total' => isset($data->srf->public_products_liability_sir)
                ? $this->getSRFLineOfBusinessTotal(
                    $data->srf->public_products_liability_sir
                ) : 0,
            'el_exposure' => $data->srf->el_exposure ?? null,
            'el_exposure_total' => isset($data->srf->el_exposure) ? $this->getSRFLineOfBusinessTotal(
                $data->srf->el_exposure
            ) : 0,
            'public_products_exposure' => $data->srf->public_products_exposure ?? null,
            'public_products_exposure_total' => isset($data->srf->public_products_exposure)
                ? $this->getSRFLineOfBusinessTotal(
                    $data->srf->public_products_exposure
                ) : 0,
            'policy_coverage_including_twc' => $data->srf->policy_coverage_including_twc ?? null,
            'policy_coverage_including_total' => isset($data->srf->policy_coverage_including_twc)
                ? $this->getSRFLineOfBusinessTotal(
                    $data->srf->policy_coverage_including_twc
                ) : 0,
            'account_engineers' => $data->organisation->org_risk_engineer ?? null,
            'survey_column_is_hidden' => $data->microsite_survey_details_is_hidden ?? null,
            'risk_grading_column_is_hidden' => $data->microsite_risk_grading_is_hidden ?? null,
            'recommendation_column_is_hidden' => $data->microsite_risk_rec_is_hidden ?? null,
            'survey_commentaries' => $data->commentaries ?? null,
            'survey_attendees_details' => $data->submissions->survey_attendees_details ?? null,
        ];

       
       
        if (isset($data->surveyor)) {
            $surveyDetails['contacts'][] = $this->labelUser($data->surveyor, 'Field Engineer');
        }

        if (isset($data->broker_contact)) {
            $surveyDetails['contacts'][] = $this->labelUser($data->broker_contact, 'Broker Contact');
        }

        if (isset($data->client_contact)) {
            $surveyDetails['contacts'][] = $this->labelUser($data->client_contact, 'Location Contact');
        }

        return $surveyDetails;
    }

    private function getSRFLineOfBusinessTotal(array $srfData): string
    {
        $srfTotal = 0;
        if (!empty($srfData)) {
            $numericValues = array_column($srfData, 1);
            $total = array_sum(array_map('floatval', $numericValues));
            $srfTotal = number_format($total) ?? '0';
        }

        return $srfTotal;
    }

    private function labelUser($user, $role)
    {
        $user->role_label = $role;
        return $user;
    }
}
