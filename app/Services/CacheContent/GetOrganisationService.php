<?php

namespace App\Services\CacheContent;

use App\Http\Controllers\OrganisationController;
use App\Models\Api;
use App\Models\FileUpload;
use Illuminate\Support\Facades\Cache;

class GetOrganisationService extends CacheContentService
{

    private static function getCacheKey($id)
    {
        return "organisation-details-for-organisation-{$id}";
    }

    public static function get($id, $getUpdated = false)
    {
        if (empty($id)) {
            return;
        }

        $cacheKey = self::getCacheKey($id);
        $organisation = self::getSetCacheForever($cacheKey);
        $files = new FileUpload();

        if (!$organisation || $getUpdated || ($organisation->response ?? '' === 'error')) {
            $organisation = json_decode(Api::get('api/v1/organisation/' . $id));
            if (empty($organisation->data)) {
                return [];
            }

            if (isset($organisation->data->logo) && $organisation->data->logo != '' && $organisation->data->logo != 'none') {
                $organisation->data->image_url = $files->link($organisation->data->logo);
            } else {
                $organisation->data->image_url = '/img/dummy/logo-placeholder.png';
            }

            $organisation_policies = [];

            if (isset($organisation->data->policy_numbers)) {
                $organisationPolicies = array_map(
                    function ($policies) {
                        return $policies->type;
                    },
                    $organisation->data->policy_numbers
                );

                $organisation_policies = array_map(
                    function ($policies) {
                        return strtolower($policies->name);
                    },
                    $organisationPolicies
                );
            }

            $orgController = app()->make(OrganisationController::class);
            $productCacheKey = "organisation-cms-all-products-organisation-{$id}";
            $allproducts = self::getSetCacheForever($productCacheKey);
            if (!$allproducts) {
                $allproducts = $orgController->getCmsProducts();
                self::getSetCacheForever($productCacheKey, $allproducts);
            }

            if (isset($organisation->data->products) && !empty($organisation->data->products)) {
                foreach ($organisation->data->products as $product) {
                    $products = array_filter(
                        $allproducts,
                        function ($prod) use ($product) {
                            return $product->slug == $prod->slug;
                        }
                    );
                    $product->name = array_values($products)[0]->name;
                }

                $organisation->data->display_products = array_filter(
                    $organisation->data->products,
                    function ($prod) use ($organisation_policies) {
                        return !in_array($prod->slug, $organisation_policies);
                    }
                );
            }

            $options = $orgController->getConstants('gradingColorOptions');
            $bgcolor = $orgController->getConstants('bgColorOptions');

            foreach ($options as $key => $value) {
                if ($organisation->data->risk_grading == $value) {
                    $organisation->data->risk_grading = (object) ['code' => $key, 'color' => $value, 'background' => $bgcolor[$value]];
                }
            }

            $otherLinesOfBusiness = [];
            $organisation = isset($organisation->data) ? $organisation->data : null;
            if (isset($organisation->products) && count($organisation->products) > 0) {
                foreach ($organisation->products as $product) {
                    if (!empty($product->loss_ratio)) {
                        $otherLinesOfBusiness[] = [
                            'name' => $product->name,
                            'loss_ratio' => $product->loss_ratio
                        ];
                    }
                }
            }

            $organisation->otherLinesOfBusiness = $otherLinesOfBusiness;
            self::getSetCacheForever($cacheKey, $organisation);
        }

        return $organisation;
    }

    public static function recache($id)
    {
        if (empty($id)) {
            return;
        }

        Cache::forget("organisation-cms-all-products-organisation-{$id}");

        return self::get($id, getUpdated: true);
    }
}
