<?php

namespace App\Services\CacheContent;

use App\Models\Api;
use App\Models\CsrReport;
use Illuminate\Support\Facades\Cache;

class GetOrgRiskGradingData extends CacheContentService
{
    private static function getCacheKey($id)
    {
        return "organisation-risk-grading-data-for-organisation-{$id}";
    }

    public static function get($id, $getUpdated = false)
    {
        if (empty($id)) {
            return;
        }

        $cacheKey = self::getCacheKey($id);
        $organisationData = self::getSetCacheForever($cacheKey);
        if ($organisationData && !$getUpdated || ($organisationData->response ?? '' === 'error')) {
            return $organisationData;
        }

        $org_id = $id;
        $csrReport = new CsrReport();
        $rrTracker = $csrReport->getRiskRecommendationTracker($org_id, true);

        $rr_tyre = [];
        $rr_status = [];
        $rr_recommendation = [];
        $rr_tyre_label = [];

        // Risk Recommendation Tyre statistics
        if (isset($rrTracker) && isset($rrTracker['RR_tyre'])) {
            $rr_tyre = $rrTracker['RR_tyre'];
            $rr_tyre = array_count_values($rr_tyre);
            $total = array_sum($rr_tyre);
            $running_percentage = $rr_count = 0;

            foreach ($rr_tyre as $rr => $tyre) {
                if ($rr_count === count($rr_tyre) - 1) {
                    $rr_tyre[$rr] = 100 - $running_percentage;
                } else {
                    $percent = round(($tyre / $total) * 100);
                    $rr_tyre[$rr] = $percent;
                    $running_percentage += $percent;
                }
                ++$rr_count;
            }
        }

        // Risk Recommendation Tyre Label

        if (isset($rrTracker) && isset($rrTracker['RR_tyre_label'])) {
            $labels = (array)$rrTracker['RR_tyre_label'];
            foreach ($labels as $key => $value) {
                if (isset($labels[$key])) {
                    $rr_tyre_label[$key] = $labels[$key];
                }
            }
        }


        // Open Close statistics
        if (isset($rrTracker) && isset($rrTracker['RR_status'])) {
            $status = [];

            foreach ($rrTracker['RR_status'] as $element) {
                $status[self::trimString($element[2][0]) . "\n" . $element[2][1]][] = $element[1];
            }
        }

        $submissionCards = json_decode(
            Api::get('api/v1/risk-recommendations/cards?limit=1000&organisation=' . $org_id),
            true
        );
        foreach ($submissionCards as $column => $cards) {
            foreach ($cards['data'] as $card) {
                $cardLocation = $card['properties']['Location'];
                $location = $cardLocation ? (self::trimString($cardLocation['location_name']) . "\n" . $cardLocation['postcode']) : '-';
                $col = $card['column'] === "closed" ? "closed" : "open";
                if (empty($rr_status[$location])) {
                    // instantiate
                    $rr_status[$location] = [
                        "open" => 0,
                        "closed" => 0
                    ];
                }
                $rr_status[$location][$col]++;
            }
        }

        uksort(
            $rr_status,
            function ($a, $b) {
                $aa = trim(substr($a, 3));
                $bb = trim(substr($b, 3));

                return ($aa > $bb) ? -1 : 1;
            }
        );

        // survey status statistics
        $possible_statistics = [
            'closed' => 0,
            'open' => 0,
            'greater_than_30' => 0,
            'less_than_30' => 0,
        ];

        $survey_statistics = count($rrTracker) > 0
            ? array_merge($possible_statistics, array_count_values($rrTracker['survey_status']))
            : $possible_statistics;

        // recommendation statistics
        if (isset($rrTracker) && isset($rrTracker['RR_recommendation'])) {
            $rr_recommendation = $rrTracker['RR_recommendation'];
            $rr_recommendation = array_count_values($rr_recommendation);

            ksort($rr_recommendation);

            uasort(
                $rr_recommendation,
                function ($a, $b) {
                    return ($a > $b) ? -1 : 1;
                }
            );

            $rr_recommendation = array_slice($rr_recommendation, 0, 5, true);
            $rrkeys = array_slice($rr_recommendation, 0, 5, true);
        }

        $survey_statistics = [];
        $tracker_count = json_decode(Api::get('api/v1/risk-recommendations/cards?organisation=' . $org_id . '&limit=1000'));
        foreach ($tracker_count as $key => $value) {
            $survey_statistics[$key] = count($value->data);
        }

        $csrData = $csrReport->getCSRReport($org_id);

        $csrData = isset($csrData['data']) ? $csrData['data'] : null;

        $out = [];
        $csrTitles = [];

        foreach ($csrData as $dk => $dv) {
            foreach ($dv as $dvk => $dvv) {
                $dvv = str_replace('SRF', 'CSR', $dvv);
                //$dvv="<span>".$dvv."</span>";

                if ($dvk == 0) {
                    if (strpos($dvv, 'Risk Control') !== false) {
                        $dvv = str_replace('Risk Control', '', $dvv);
                    }

                    $csrTitles[$dvk][$dk] = trim($dvv);
                } else {
                    $out[$dvk][$dk] = $dvv;
                }
            }
        }

        uasort(
            $out,
            function ($a, $b) {
                $ab = preg_match_all('!\d+!', $a[0], $matchesa);
                $bb = preg_match_all('!\d+!', $b[0], $matchesb);

                return ($matchesa[0] > $matchesb[0]) ? -1 : 1;
            }
        );

        $organisationData = array(
            'csrReport'               => $out,
            'csrTitles'               => $csrTitles,
            'rrTracker'               => $survey_statistics,
            'rr_tyre_label'           => $rr_tyre_label,
            'rrTyre'                  => $rr_tyre,
            'rr_status'               => $rr_status,
            'rr_srr_recommendation'   => $rr_recommendation,
            'rr_loss_estimate'        => isset($rrTracker['RR_loss_estimate']) ? ($rrTracker['RR_loss_estimate']) : [],
        );

        self::getSetCacheForever($cacheKey, $organisationData);

        return $organisationData;
    }

    public static function recache($id)
    {
        if (empty($id)) {
            return;
        }

        // Clear related data cache that needs to have updated data
        Cache::forget("surveys-$id");

        $data = self::get($id, getUpdated: true);

        return $data;
    }

    private static function trimString($string)
    {
        $maxLength = 20;
        $dots = "...";
        if (strlen($string) > $maxLength) {
            return substr($string, 0, $maxLength - strlen($dots)) . $dots;
        }
        return $string;
    }
}
