<?php

namespace App\Services\CacheContent;

use App\Models\Api;

class GetOrganisationPolicyDocs extends CacheContentService
{
    private static function getCacheKey($id)
    {
        return "policy-data-for-organisation-{$id}";
    }

    public static function get($id, $getUpdated = false)
    {
        if (empty($id)) {
            return;
        }

        $cacheKey = self::getCacheKey($id);
        $policyData = self::getSetCacheForever($cacheKey);

        if (!$policyData || $getUpdated || ($policyData->response ?? '' === 'error')) {
            $policy_doc_types = json_decode(Api::get('/api/v1/document-policy/all'));
            $documents = json_decode(Api::get('/api/v1/document/policy/' . $id));
            $policy_types     = json_decode(Api::get('api/v1/policy-types/all'));

            if (isset($policy_doc_types->data) && isset($documents)) {
                $policy_docs = $documents;
                $policy_doc_types = $policy_doc_types->data;
            }
            $policy_docs      = isset($policy_docs) ? $policy_docs : [];
            $policy_doc_types = isset($policy_doc_types) ? $policy_doc_types : [];
            $policyData = (object)['policy_docs' => $policy_docs, 'policy_doc_types' => $policy_doc_types, 'policy_types' => $policy_types->data];

            self::getSetCacheForever($cacheKey, $policyData);
        }

        return $policyData;
    }
}
