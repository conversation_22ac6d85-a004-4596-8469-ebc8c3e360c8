<?php

namespace App\Services\CacheContent;

use Illuminate\Support\Facades\Cache;
use App\Models\Api;

abstract class CacheContentService
{
    abstract public static function get($id, $getUpdated = false);

    public static function getSetCacheForever(string $cacheKey, $data = null)
    {
        if ($data) {
            Cache::forever($cacheKey, $data);
        }

        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        return $data;
    }

    public static function recache($id)
    {
        if (empty($id)) {
            return;
        }

        return static::get($id, getUpdated: true);
    }
}
