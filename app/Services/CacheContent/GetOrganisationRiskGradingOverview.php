<?php

namespace App\Services\CacheContent;

use App\Models\Api;
use App\Http\Controllers\CacheRebuildController;
class GetOrganisationRiskGradingOverview extends CacheContentService
{
    private static function getApiEndpoint($id)
    {
        return sprintf('/api/v1/standard-risk/attributes/overview/%s', $id);
    }

    private static function getCacheKey($id)
    {
        return sprintf('risk-grading-overview-for-organisation-%s', $id);
    }

    public static function get($id, $getUpdated = false)
    {
        $apiEndpoint = self::getApiEndpoint(($id));
        $cacheKey = self::getCacheKey($id);

        $riskGradingOverview = self::getSetCacheForever($cacheKey);
        if (!$riskGradingOverview || $getUpdated || ($riskGradingOverview->response ?? '' === 'error')) {
            //Force Rebuild Risk Grading Overview Cache
            app()->make(CacheRebuildController::class)->rebuildRiskGradingCache($id);
            $riskGradingOverview = json_decode(Api::get($apiEndpoint));
            foreach ($riskGradingOverview->overview as $overview) {
                $exists = [];
                $subOverviews = [];
                foreach ($overview->standard_locations_gradings_overview as $subOverview) {
                    if (!in_array($subOverview->attribute, $exists)) {
                        $exists[] = $subOverview->attribute;
                        $subOverviews[] = $subOverview;
                    }
                }
                $overview->standard_locations_gradings_overview = $subOverviews;
            }

            self::getSetCacheForever($cacheKey, $riskGradingOverview);
        }
        return $riskGradingOverview;
    }
}
