<?php

namespace App\Services\CacheContent;

use App\Models\Api;
use App\Services\CacheContent\CacheContentService;

class GetMgaSchemeService extends CacheContentService
{
    public static function get($id, $getUpdated = false)
    {
        $cacheKey = 'admin_mga-schemes_all_0-99999';
        $resource = self::getSetCacheForever($cacheKey);
        if (!$resource || $getUpdated || ($resource->response ?? '' === 'error')) {
            $resource = json_decode(Api::get('api/v1/mga-schemes/all/0/99999'));
            self::getSetCacheForever($cacheKey, $resource);

            if (!empty($id)) {
                self::getMgaSchemeById($id, true);
            }
        }

        return $resource;
    }

    public static function getMgaSchemeById($id, $getUpdated = false)
    {
        $cacheKey = "admin_mga-schemes_all_0-99999_{$id}";
        $resource = self::getSetCacheForever($cacheKey);
        if (!$resource || $getUpdated || ($resource->response ?? '' === 'error')) {
            $resource = json_decode(Api::get('api/v1/mga-schemes/all/0/99999?broker_id=' . $id));
            self::getSetCacheForever($cacheKey, $resource);
        }
        return self::getSetCacheForever($cacheKey);
    }

    public static function recache($id)
    {
        return static::get($id, getUpdated: true);
    }
}
