<?php

namespace App\Services\CacheContent;

use App\Models\Api;
use Illuminate\Support\Facades\Cache;

class GetSurveyMicroSiteService extends CacheContentService
{
    private static function getCacheKey($id)
    {
        return "admin_survey-microsite-risk-grading-{$id}";
    }
    
    private static function getTooltipCacheKey()
    {
        return "microsite-risk-grading-tooltips";
    }

    private static function getNarrativesCacheKey()
    {
        return "microsite-risk-grading-narratives";
    }

    public static function get($id, $getUpdated = false)
    {
        if (empty($id)) {
            return null;
        }

        $cacheKey = self::getCacheKey($id);
        $resource = self::getSetCacheForever($cacheKey);

        if (!$resource || $getUpdated || ($resource->response ?? '' === 'error')) {
            $resource = json_decode(Api::get('api/v1/microsite/risk-grading/' . $id));
            if ($resource->status == 200 && !empty($resource->data)) {
                Cache::forget($cacheKey);
                Cache::forget("admin_csr-microsite-survey-{$id}");
            }

            self::getSetCacheForever($cacheKey, $resource);
        }

        return $resource;
    }

    public static function getRiskGradingTooltip($getUpdated = false) {
        $cacheKey = self::getTooltipCacheKey();
        $resource = self::getSetCacheForever($cacheKey);
        if (!$resource || $getUpdated) {
            $resource = json_decode(Api::get('api/v1/microsite/risk-grading-tooltip'));
            if ($resource->status == 200) {
                Cache::forget($cacheKey);
                self::getSetCacheForever($cacheKey, $resource->data);
            }
            return $resource->data;
        }
        return $resource;
    }

    public static function getRiskGradingNarratives($getUpdated = false) {
        $cacheKey = self::getNarrativesCacheKey();
        $resource = self::getSetCacheForever($cacheKey);
        if (!$resource || $getUpdated) {
            $resource = json_decode(Api::get('api/v1/microsite/risk-grading-narratives'));
            if ($resource->status == 200) {
                Cache::forget($cacheKey);
                self::getSetCacheForever($cacheKey, $resource->data);
            }
            return $resource->data;
        }
        return $resource;
    }
}
