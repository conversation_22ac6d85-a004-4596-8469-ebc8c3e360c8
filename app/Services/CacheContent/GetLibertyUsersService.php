<?php

namespace App\Services\CacheContent;

use App\Models\Api;

class GetLibertyUsersService extends CacheContentService
{

    private static function getCacheKey()
    {
        return "admin_all-liberty-users-for-surveys";
    }

    public static function get($id = null, $getUpdated = false)
    {
        $cacheKey = self::getCacheKey();
        $resource = self::getSetCacheForever($cacheKey);

        if (!$resource || $getUpdated || ($resource->response ?? '' === 'error')) {
            $resource = json_decode(Api::get('api/v1/liberty-users/all'));
            self::getSetCacheForever($cacheKey, $resource);
        }

        return $resource;
    }

    public static function recache($id = null)
    {
        return self::get($id, getUpdated: true);
    }
}
