<?php

namespace App\Services\CacheContent;

use App\Models\Api;

class GetBrokerOrgsOptionService extends CacheContentService {

    public static function get($id, $getUpdated = false)
    {
        if (!$id) {
            return [];
        }

        $cacheKey = 'org-broker-option-' . $id;
        $resource = self::getSetCacheForever($cacheKey);

        if (!$resource || $getUpdated || ($resource->response ?? '' === 'error')) {
            $resource = json_decode(Api::get('api/v1/organisation/options/broker/' . $id));
            self::getSetCacheForever($cacheKey, $resource);
        }

        return $resource;
    }
}