<?php

namespace App\Services\CacheContent;

use App\Models\Api;

class GetBrokerService extends CacheContentService
{
    public static function get($id, $getUpdated = false)
    {
        $cacheKey = 'admin_broker_all_0-99999';
        $resource = self::getSetCacheForever($cacheKey);
        if (!$resource || $getUpdated || ($resource->response ?? '' === 'error')) {
            $resource = json_decode(Api::get('api/v1/brokers/all/0/99999'));
            self::getSetCacheForever($cacheKey, $resource);

            if (!empty($id)) {
                self::getBrokerById($id, true);
            }
        }

        return $resource;
    }

    public static function getBrokerById($id, $getUpdated = false)
    {
        $cacheKey = "admin_broker_all_0-99999_{$id}";
        $resource = self::getSetCacheForever($cacheKey);
        if (!$resource || $getUpdated || ($resource->response ?? '' === 'error')) {
            $resource = json_decode(Api::get('api/v1/brokers/all/0/99999/' . $id));
            self::getSetCacheForever($cacheKey, $resource);
        }
        return self::getSetCacheForever($cacheKey);
    }

    public static function recache($id)
    {
        return static::get($id, getUpdated: true);
    }
}
