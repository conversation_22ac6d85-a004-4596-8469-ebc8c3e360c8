<?php

namespace App\Services;

use App\Models\Api;
use stdClass;
use Carbon\Carbon;
use Illuminate\Support\Facades\Session;

class RiskRecommendationTrackerService
{
    public static function filterMgaScheme(array $cards, stdClass $card, int $key): array
    {
        $request = request();
        if (!$request->has('mga_scheme')) {
            return $cards;
        }

        if ($request->get('mga_scheme') != 0 && $request->get('mga_scheme') != 'All') {
            $mga_scheme = $request->get('mga_scheme');
            if (
                isset($card->survey->organisation->mga_scheme)
                && intval($mga_scheme) > 0
                && intval($mga_scheme) == $card->survey->organisation->mga_scheme
            ) {
                // do nothing, scheme matches
            } else {
                // remove card as it has an invalid scheme
                unset($cards[$key]);
            }
        }
        return $cards;
    }

    public static function filterOrganisation(array $cards, stdClass $card, int $key): array
    {
        $request = request();
        if (!$request->has('organisation')) {
            return $cards;
        }
        if ($request->get('organisation') != 0) {
            $organisation_id = $request->get('organisation');
            if (!isset($card->survey->organisation) || $card->survey->organisation == null || $card->survey->organisation->id != (int) $organisation_id) {
                unset($cards[$key]);
            }
        }
        return $cards;
    }


    public static function filterAspenUser(array $cards, stdClass $card, int $key): array
    {
        if (Session::get('user')->login_type !== 'aspen-user') {
            return $cards;
        }
        if (isset($card->survey->organisation) && isset($card->survey->organisation->branch) && isset($card->survey->organisation->branch->is_aspen) && $card->survey->organisation->branch->is_aspen == 1) {
            unset($cards[$key]);
        }
        return $cards;
    }

    public static function filterBranch(array $cards, stdClass $card, int $key): array
    {
        $request = request();
        if (!$request->has('branch')) {
            return $cards;
        }
        $branch_id = $request->get('branch');
        if ($branch_id != 0) {
            if (!isset($card->survey) || $card->survey == null || $card->survey->branch_id != (int) $branch_id) {
                unset($cards[$key]);
            }
        }
        return $cards;
    }

    public static function filterLob(array $cards, stdClass $card, int $key): array
    {
        $request = request();
        if (!$request->has('lob')) {
            return $cards;
        }
        $lob = $request->get('lob');
        if ($lob != 'All') {
            if (!isset($card->survey->policy_name) || $card->survey->policy_name == null || $card->survey->policy_name != $lob) {
                unset($cards[$key]);
            }
        }
        return $cards;
    }

    public static function filterUnderwriter(array $cards, stdClass $card, int $key): array
    {
        $request = request();
        if (!$request->has('underwriter')) {
            return $cards;
        }
        $uwr = $request->get('underwriter');
        if ($uwr != '0') {
            if (!isset($card->survey->underwriter_id) || $card->survey->underwriter_id == null || $card->survey->underwriter_id != (int) $uwr) {
                unset($cards[$key]);
            }
        }
        return $cards;
    }

    public static function filterSurveyDate(array $cards, stdClass $card, int $key): array
    {
        $request = request();
        if (!$request->has('survey_date')) {
            return $cards;
        }
        if ($request->get('survey_date') != '') {
            $survey_date = Carbon::createFromFormat('d/m/Y', $request->Get('survey_date'));

            $set = false;
            if (isset($card->schedule->meta) || $card->schedule != null) {
                foreach ($card->schedule->meta as $meta) {
                    if ($meta->key == 'actual_submission_deadline') {
                        $actual_date = Carbon::createFromFormat('d/m/Y', $meta->value);
                        if ($actual_date >= $survey_date) {
                            $set = true;
                        }
                    }
                }

                if ($set === false) {
                    unset($cards[$key]);
                }
            } else {
                unset($cards[$key]);
            }
        }
        return $cards;
    }
}
