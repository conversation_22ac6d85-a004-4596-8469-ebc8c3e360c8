<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use App\Models\Api;

class CacheService
{
    public function __construct()
    {

    }

    public function get(string $key, string $api, string $method)
    {
        $cacheExpiry = 600;

        if (Cache::has($key)) {
            return Cache::get($key);
        }
        
        $response = Api::{$method}($api);
        if($response){
            Cache::put($response, $key, $cacheExpiry);
            return $response;
        }

        return [];
    }
}
