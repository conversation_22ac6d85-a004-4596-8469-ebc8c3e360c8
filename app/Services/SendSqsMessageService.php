<?php

namespace App\Services;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Config;
use Aws\Laravel\AwsFacade as AWS;

class SendSqsMessageService extends Command
{
    public static function sendMessages(array $messages)
    {
        if(!$messages) return;
        foreach ($messages as $message) {
            AWS::createClient('Sqs')->sendMessage([
                'QueueUrl'          => self::getQueueUrl($message['isClient'] ?? false),
                'MessageBody'       => json_encode($message),
                'MessageAttributes' => [],
                'DelaySeconds'      => 2
            ]);
        }
    }

    private static function getQueueUrl(bool $isClient): string
    {
        return $isClient ? Config::get('app.aws.invalidate_cache_sqs_client') : Config::get('app.aws.invalidate_cache_sqs_admin');
    }
}
