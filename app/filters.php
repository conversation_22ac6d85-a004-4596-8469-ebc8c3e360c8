<?php

/*
|--------------------------------------------------------------------------
| Application & Route Filters
|--------------------------------------------------------------------------
|
| Below you will find the "before" and "after" events for the application
| which may be used to do any work before or after a request into your
| application. Here you may also register your custom route filters.
|
*/

App::before(function($request)
{
	//
});


App::after(function($request, $response)
{
	//
});

Route::filter('allowed-routes', function()
{
    if (!Auth::guest()){
        $types = array('broker-user') ; 
        $user = Session::get('user');
        $exceptions = [
        	'organisation.engagement',
        	'organisation.overview.index',
        	'organisation.overview.edit',
        	'organisation.overview.update',
			'organisation.document.retrieve_policy_document',
			'organisation.risk-grading-logs',
			'preview.public-form-submission-withmapping',
			'public_form.show',
			'public-form-submission.print',
			'public-form-submission.print.pdf',
        ];
        $regexExceptions = [
            '/^community\.(.*)/m'
        ];

        $matches = [];
        foreach($regexExceptions as $pattern)
        {
            preg_match_all($pattern, Route::currentRouteName(), $matches, PREG_SET_ORDER, 0);
            if (!empty($matches)) {
                break;
            }
        }

        if($user && in_array($user->login_type, $types) && (! (in_array(Route::currentRouteName(), $exceptions)) && empty($matches))){
            $routes = Config::get('app.allowed-routes.'.$user->login_type) ;
            if(!in_array(Route::currentRouteName(), $routes)){
                App::abort(403, 'Unauthorized action. '.Route::currentRouteName());
            }
        }
    }
}) ;

/*
|--------------------------------------------------------------------------
| Authentication Filters
|--------------------------------------------------------------------------
|
| The following filters are used to verify that the user of the current
| session is logged into this application. The "basic" filter easily
| integrates HTTP Basic authentication for quick, simple checking.
|
*/

Route::filter('auth', function()
{
	if (Auth::guest())
	{
		if (Request::ajax())
		{
			return Response::make('Unauthorized', 401);
		}

		else
		{
			if(Input::has('u')) {
				$cookie = Cookie::make('login-cookie', Input::get('u'), 15);
				return Redirect::guest('login')->withCookie($cookie);
			}

			if (Input::has('type') && Input::get('type')=='broker_user') {
				return Redirect::guest('/login/broker-user');
			}

			return Redirect::guest('login');
		}
	}
	else{
		$user = Session::get('user');

		if(isset($user))
		{
			$routeName = strtolower (Route::currentRouteName());


			if(!empty($routeName))
			{
				$type = Config::get('app.'.$user->type); //$configuredRoutes[$user->type];				

				if(isset($type))
				{
					if(isset($type['roles']))
					{
						$roles = $type['roles'];					
						if(isset($roles[$user->login_type]))
						{
							foreach ($roles[$user->login_type] as $key => $value) {
								if(redirectNotAccessRoute($value, $routeName))
									return Response::make('Unauthorized', 401);
							}
						}
					} else {
						foreach ($type['routes'] as $key => $value) {
							if(redirectNotAccessRoute($value, $routeName))
								return Response::make('Unauthorized', 401);
						}
					}
				}
		    }
		}
	}	

	if (Request::segment(1) == 'organisation' && is_numeric(intval(Request::segment(2))) && intval(Request::segment(2)) > 0) {
	    if (Session::get('user')->login_type == 'broker-user') {
	    	// print_r(Api::get('api/v1/broker-users/orgs/'.Session::get('user')->broker_id));exit;
	        $orgs_for_broker = json_decode(Api::get('api/v1/broker-users/orgs/'.Session::get('user')->broker_id).'?open_market=true');
			
	        if ($orgs_for_broker && isset($orgs_for_broker->schemes)) {
		        if (! in_array(intval(Request::segment(2)), $orgs_for_broker->schemes)) {
		        	return Response::make('Unauthorized', 401);
		        }
	        }
	    }
	}

	if (Request::segment(1) == 'users' && is_numeric(intval(Request::segment(2))) && intval(Request::segment(2)) > 0) {
	    if (Session::get('user')->login_type == 'broker-user') {
	    	// print_r(Api::get('/api/v1/user/find/'.Request::segment(2)));exit;
	    	$user = json_decode(Api::get('api/v1/user/find/'.Request::segment(2)));

	    	// print_r(Api::get('api/v1/broker-users/orgs/'.Session::get('user')->broker_id));exit;
	        $orgs_for_user = json_decode(Api::get('api/v1/broker-users/orgs/'.Session::get('user')->broker_id));

	        if (
	        	$user && isset($user->data) && isset($user->data->organisation_id) && 
	        	$orgs_for_user && isset($orgs_for_user->schemes)
	        ) {
		        if (! in_array($user->data->organisation_id, $orgs_for_user->schemes)) {
		        	return Response::make('Unauthorized', 401);
		        }
	        }
	    }
	}

	if(Session::get('user')->login_type == 'virtual-rooms' && (Request::segment(1) != 'virtual-rooms' && Request::segment(1) != 'suppression' && Request::segment(1) != 'sha1' && Request::segment(1) != 'register-notification-token') ){
		return Redirect::route('virtual-rooms.representative-call-availability');
	}
});


Route::filter('auth.basic', function()
{
	return Auth::basic();
});

Route::filter('auth-socials', function()
{
	if (!Session::has('socials-user') && !Session::has('socials-role')) {
		$url = Request::url();
		Session::put('login-attempt-url', $url);
		return Redirect::to('virtual-rooms/login');
	}
	return;
});

Route::filter('auth-socials-customer', function()
{
	if (!Session::has('socials-customer') && !Session::has('socials-role')) {
		return Redirect::to('virtual-rooms/customer-login');
	}
	return;
});

/*
|--------------------------------------------------------------------------
| Two Factor Auth Filter
|--------------------------------------------------------------------------
|
*/
Route::filter('2factor', function()
{
	// if (
	// 	Session::get('user')->type == 'liberty-user' || 
	// 	Session::get('user')->type == 'external-surveyor' || 
	// 	Session::get('user')->type == 'broker-user'
	// ) {
	// 	$invalid = ! Cookie::get('remember_2fa') && Session::get('user')->valid == 0;

	// 	if ($invalid) {
	// 	 	$google2fa = app()->make('PragmaRX\Google2FA\Contracts\Google2FA');

	// 	 	$google2fa_url = $google2fa->getQRCodeGoogleUrl(
	// 	 		'RiskReduce',
	// 	 		Session::get('user')->email,
	// 	 		Session::get('user')->secret
	// 	 	);

	// 		if (Session::get('user')->established) {
	// 	 		return View::make('auth.two-factor-verify');
	// 	 	}

	// 	 	return View::make('auth.two-factor')->with(compact('google2fa_url'));
	// 	}
	// }
});

Route::filter('notAspen', function()
{
	if (! Session::has('user')) {
		return Redirect::guest('login');
	}

	if (Session::get('user')->login_type != 'aspen-user') {
		// proceed
	} else {
		return Response::make('Unauthorized', 401);
	}
});

Route::filter('onlyAspen', function()
{
	if (! Session::has('user')) {
		return Redirect::guest('login');
	}

	if (Session::get('user')->login_type == 'aspen-user') {
		// proceed
	} else {
		return Response::make('Unauthorized', 401);
	}
});

/*
|--------------------------------------------------------------------------
| Guest Filter
|--------------------------------------------------------------------------
|
| The "guest" filter is the counterpart of the authentication filters as
| it simply checks that the current user is not logged in. A redirect
| response will be issued if they are, which you may freely change.
|
*/

Route::filter('guest', function()
{
	if (Auth::check()) return Redirect::to('/');
});

/*
|--------------------------------------------------------------------------
| CSRF Protection Filter
|--------------------------------------------------------------------------
|
| The CSRF filter is responsible for protecting your application against
| cross-site request forgery attacks. If this special token in a user
| session does not match the one given in this request, we'll bail.
|
*/

Route::filter('csrf', function()
{
	if (Session::token() !== Input::get('_token'))
	{
		Session::flush();
		Auth::logout();
	}
});

Blade::extend(function($value) {
	return preg_replace('/\@define(.+)/', '<?php ${1}; ?>', $value);
});

/*function disallowedRoutes()
{
	$result = [];

	$result['external-surveyor'] = array('routes' => ['surveys.create', 'surveys.edit']);

	$result['liberty-user'] = array('roles' => 
		array(
			'underwriter' => ['brokers.index', 'brokers.create', 'liberty-users.index', 'liberty-branches.index', 'external-surveyors.index', 'external-survey-companies.index'],
			'admin' => [], 
			'risk-engineer' => ['kanban.index'], 
			'risk-control' => []
			));

	return $result;
}*/
if (!function_exists("redirectNotAccessRoute")) {
	function redirectNotAccessRoute($value, $routeName)
	{
		$value = strtolower($value);
		$routeName = strtolower($routeName);

		return $value == $routeName;
	}
}

/**
 * RHS Filter
 */
Route::filter('rhs', function () {
    if (Input::get('_rhs') != 'NzkSPcZS3xwTT8eY') {
        return Redirect::to('/');
    }
});

Route::filter('no-cache',function($route, $request, $response){
    $response->headers->set('Cache-Control','nocache, no-store, max-age=0, must-revalidate');
    $response->headers->set('Pragma','no-cache');
    $response->headers->set('Expires','Fri, 01 Jan 1990 00:00:00 GMT');  // any date in the past
});

Route::filter('community', function () {
    $login_type = Session::get('user')->login_type;
    $user_role = Session::get('user')->role;

    if (!($login_type == 'underwriter' || $login_type == 'risk-engineer' ||
        ($login_type == 'risk-control' && Session::get('user')?->isRoleAdminOrAccountManager()) || User::isBrokerAndAllowedInCommunity())
    ) {
        App::abort(403, 'Unauthorized action. '.Route::currentRouteName());
    }
});