<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8"/>
    <script>
        /*<![CDATA[*/
        function subst() {
            var vars = {};
            var query_strings_from_url = document.location.search.substring(1).split('&');

            for (var query_string in query_strings_from_url) {
                if (query_strings_from_url.hasOwnProperty(query_string)) {
                    var temp_var = query_strings_from_url[query_string].split('=', 2);
                    vars[temp_var[0]] = decodeURI(temp_var[1]);
                }
            }

            var css_selector_classes = ['page'];

            for (var css_class in css_selector_classes) {
                if (css_selector_classes.hasOwnProperty(css_class)) {
                    var element = document.getElementsByClassName(css_selector_classes[css_class]);
                    for (var j = 0; j < element.length; ++j) {
                        var page = vars[css_selector_classes[css_class]];

                        if (page == 1) {
                            document.getElementById('confidential').style.display = 'none';
                            element[j].style.display = 'none';
                        } else {
                            element[j].textContent = page;
                        }
                    }
                }
            }
        }
        /*]]>*/
    </script>
    <style type="text/css">
        .footer-table {
            margin-top: 40px !important;
            padding-top: 40px !important;
        }
    </style>
</head>
<body onload="subst()">
    <table class="footer-table">
        <tbody>
            <tr>
                <td style="width: 200px;"><span id="confidential" style="background-color: #efeff0; color: #666; padding: 0 15px; display: block; height: 50px; line-height: 50px; text-align: center;">Confidential</span></td>
                <td style="width: 500px;" align="center"></td>
                <td style="width: 200px;" align="right"><span class="page" style="background-color: #fff; border: 1px solid #efeff0; display: block; width: 50px; height: 50px; line-height: 50px; text-align: center;"></span></td>
            </tr>
        </tbody>
    </table>
</body>
