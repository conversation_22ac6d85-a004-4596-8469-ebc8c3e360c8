<?php

namespace App\Providers;

use App\Rules\DictionaryWords;
use App\Rules\NotPreviouslyUsedPassword;
use App\Rules\RiskReducePassword;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\ServiceProvider;
use Illuminate\Validation\Rules\Password;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        if (!app()->environment('local')) {
            URL::forceScheme('https');
        }

        Password::defaults(function () {
            $rule = Password::min(12)
                ->mixedCase()
                ->symbols()
                ->numbers()
                ->letters()
                ->uncompromised()
                ->rules([
                    new DictionaryWords,
                    new RiskReducePassword,
                ]);
            return $rule;
        });
    }
}
