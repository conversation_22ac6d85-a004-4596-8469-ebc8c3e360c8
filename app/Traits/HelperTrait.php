<?php

namespace App\Traits;
use App\Models\Api;
use Illuminate\Support\Facades\Cache;

trait HelperTrait {

    public function setCache(string $cacheKey, $data)
    {
        $cacheKey='admin_'.$cacheKey;
        Cache::put($cacheKey, $data, 3000);
    }

    public function getData(string $apiEndpoint)
    {
        $data = json_decode(Api::get($apiEndpoint));
        return $data;
    }

    public function getOrStoreCache(string $cacheKey, string $apiEndpoint, string $method, bool $accessData = false)
    {
        $cacheKey='admin_'.$cacheKey;
        if(Cache::has($cacheKey)){
            return Cache::get($cacheKey);
        }

        $data = [];
        $data = json_decode(Api::{$method}($apiEndpoint));

        if($data){
            $dataOverride = $accessData ? $data->data : $data;
            Cache::put($cacheKey, $dataOverride, 3000);
            return $dataOverride;
        }

        return $data;
    }

    public function getSetCache(string $cacheKey, $data=null, $ttl = 3000)
    {
        $cacheKey='admin_'.$cacheKey;
        
        if(Cache::has($cacheKey) && !$data){
            return Cache::get($cacheKey);
        }
        if($data){
            Cache::put($cacheKey, $data, $ttl);
        }
        return $data;
    }

    public function permanentCache(string $cacheKey, $data)
    {
        Cache::forever($cacheKey, $data);
        return $data;
    }

    public function getConstants(string $key)
    {
        switch ($key) {
            case 'rgColors':
                $colors = [
                    'Poor'                                                        => '#fc0d1b',
                    'Below Average'                                               => '#fdbf2d',
                    'Average'                                                     => '#fffd38',
                    'Above Average'                                               => '#00b050',
                    'Good'                                                        => '#00b050',
                    'Superior'                                                    => '#0070c0',
                    'Not Applicable / Not Assessed'                               => '#dddddd',
                    'No Data'                                                     => '#fff',
                    'Contact U/W within 24 hours'                                 => '#fc0d1b',
                    'Multiple Requirements identified - monthly updates required' => '#fdbf2d',
                    'Single Requirement - monitor progress'                       => '#fffd38',
                    'Recommendations Only -generally reasonable controls'         => '#00b050',
                    'Satisfactory'                                                => '#00b050',
                    'Not Applicable'                                              => '#dddddd',
                    'Requires Improvement'                                        => '#fc0d1b',
                ];
                return $colors;

            case 'gradingOptions':
                $gradings = [
                    'Poor' => '#fc0d1b',
                    'Below Average' => '#fdbf2d',
                    'Average' => '#fffd38',
                    'Above Average' => '#00b050',
                    'Superior' => '#0070c0',
                    'Not Applicable / Not Assessed' => '#dddddd',
                    'Good' => '#00b050', // this will handle legacy colors
                    'Requires Improvement' => '#fc0d1b' // this will handle legacy colors
                ];
                return $gradings;


            case 'gradingColorToolTips':
                $gradingsTips = [
                    'Poor' => '#fc0d1b',
                    'Below Average' => '#fdbf2d',
                    'Average' => '#fffd38',
                    'Above Average' => '#00b050',
                    'Superior' => '#0070c0',
                    'Not Applicable / Not Assessed' => '#dddddd',
                    'Good' => '#00b050', // this will handle legacy colors
                    'Requires Improvement' => '#fc0d1b' // this will handle legacy colors
                ];
                return $gradingsTips;

            case 'gradingColorOptions':
                $options = ['A' => 'blue', 'B' => 'green', 'C' => 'yellow', 'D' => 'orange', 'E' => 'red'];
                return $options;

            case 'bgColorOptions':
                $bgcolor = ['blue' => '#31B6FF', 'green' => '#90EE90', 'yellow' => '#feffeb', 'orange' => '#FDDFBB', 'red' => '#FFE5E5'];
                return $bgcolor;
          }
    }

    public function trimString($string){
        $maxLength = 20;
        $dots = "...";
        if(strlen($string) > $maxLength){
            return substr($string, 0, $maxLength - strlen($dots)) . $dots;
        }
        return $string;
    }

    public function flattenArray($array) {
        $result = array();
    
        foreach ($array as $subArray) {
            foreach ($subArray as $item) {
                $result[] = $item;
            }
        }
    
        return $result;
    }

    public function findArrayValueByPartialKey(array $array, string $partialKey): ?string
    {
        $pattern = '/^' . preg_quote($partialKey, '/') . '/';

        $matchingKeys = array_filter(
            array_keys($array),
            fn($key) => preg_match($pattern, $key)
        );

        if (empty($matchingKeys)) {
            return null;
        }

        $matchedKey = reset($matchingKeys);
        return $array[$matchedKey];
    }

    public function checkMessageAttachments(array &$messages)
    {
        foreach ($messages as $message) {
            if (isset($message->attachments) && strpos($message->attachments, 'undefined') !== false && !empty($message->uploaded)) {
                $hrefs = json_decode($message->uploaded);

                $index = 0;
                $message->attachments = preg_replace_callback('/href="undefined"/', function($matches) use ($hrefs, &$index) {
                    $replacement = 'href="' . $hrefs[$index] . '"';
                    $index++;
                    return $replacement;
                }, $message->attachments);
            }
        }
    }
}