<?php

namespace App\Traits;

use App\Models\Api;
use Illuminate\Http\Request;

trait HasForms
{
    public function getLibertyUsers(Request $request)
    {
        $search = $request->get('search');
        if (empty($search)) {
            return response()->json([]);
        }

        $query = [
            'roles' => 'admin,risk-engineer,underwriter,broker-user',
            'search' => $search,
            'columns' => 'id,email',
        ];

        $libertyUsers = json_decode(Api::get('api/v1/liberty-users/all-filtered?' . http_build_query($query)), false);

        return $this->filterUserColumns($libertyUsers);
    }

    public function getClientUsers(Request $request)
    {
        $search = $request->get('search');
        if (empty($search)) {
            return response()->json([]);
        }
        $query = [
            'search' => $search,
            'columns' => 'id,email',
        ];

        $users = json_decode(Api::get('api/v1/user/all-filtered?' . http_build_query($query)), false);

        return $this->filterUserColumns($users);
    }

    private function filterUserColumns($users)
    {
        $formatted_users = [];
        foreach ($users->data as $user) {
            $formatted_users[] = $this->userColumns($user);
        }
        return $formatted_users;
    }

    private function userColumns($user)
    {
        return [
            'id' => $user->id,
            'value' => $user->email . ' ',
        ];
    }
}
