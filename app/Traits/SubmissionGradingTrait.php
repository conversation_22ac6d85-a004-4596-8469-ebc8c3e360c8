<?php

namespace App\Traits;

use App\Models\Api;

trait SubmissionGradingTrait {


    private function updateInconsistentGrading($riskGradings, &$submission)
    {
        $groupedRiskGradings = array_reduce($riskGradings, function ($carry, $item) {
            $policyTypeId = $item->get_rg_attribute->rg_policy_type_id ?? null;
            if ($policyTypeId !== null) {
                $carry[$policyTypeId][] = $item;
            }
            return $carry;
        }, []);

        foreach ($groupedRiskGradings as $policyTypeId => $riskGradings) {
            $this->updateInconsistentGradingIndividualPolicyType($riskGradings, $submission, $policyTypeId);
        }
    }

    private function filterArray($array) {
        // Define the strings in a variable for reuse
        $notApplicableValues = ['Not Applicable / Not Assessed', 'not_applicable_not_assessed'];
        $filteredArray = [];
    
        // Separate `attribute_names` key
        if (isset($array['attribute_names'])) {
            $filteredArray['attribute_names'] = array_values(array_unique($array['attribute_names']));
            unset($array['attribute_names']); // Remove it from the main array to process others
        }
    
        foreach ($array as $mainKey => $mainValue) {
            foreach ($mainValue as $key => $subAttributes) {
                $keyCondition = in_array($key, $notApplicableValues);
                $allValuesCondition = count(array_unique($subAttributes)) === 1 && 
                                      in_array(reset($subAttributes), $notApplicableValues);
                $anyValueCondition = !$allValuesCondition && 
                                     !empty(array_filter($subAttributes, function ($value) use ($notApplicableValues) {
                                         return !in_array($value, $notApplicableValues);
                                     }));
    
                if (($keyCondition && $anyValueCondition) || (!$keyCondition && $allValuesCondition)) {
                    $filteredArray[$mainKey][$key] = $subAttributes;
                }
            }
        }
    
        return $filteredArray;
    }
    

    private function transformArray($array) {
        // Extract attribute_names and policy_type_id to retain them separately
        $attributeNames = isset($array['attribute_names']) ? $array['attribute_names'] : [];
        $policyTypeId = isset($array['policy_type_id']) ? $array['policy_type_id'] : null;
    
        // Remove attribute_names and policy_type_id from the main array for processing
        unset($array['attribute_names'], $array['policy_type_id']);
    
        $transformedArray = [];
    
        // Process each attribute
        foreach ($array as $attributeField => $data) {
            foreach ($data as $key => $subAttributes) {
                // Add sub-attributes and the attribute_field key
                $subAttributes['attribute_field'] = $attributeField;
                $transformedArray[] = $subAttributes;
            }
        }
    
        // Add back attribute_names and policy_type_id
        $transformedArray['attribute_names'] = $attributeNames;
        $transformedArray['policy_type_id'] = $policyTypeId;
    
        return $transformedArray;
    }

    private function transformArrayWithBanding($array) {
        $result = [];
        $notApplicableValues = ['not_applicable_not_assessed', 'Not Applicable / Not Assessed'];
    
        foreach ($array as $key => $value) {
            // Check if the key is an integer and value is an array
            if (is_int($key) && is_array($value)) {
                $attributeField = $value['attribute_field'] ?? null;
                
                // Remove 'attribute_field' to check the rest of the values
                unset($value['attribute_field']);
                
                // If all remaining values are 'not_applicable_not_assessed' or 'Not Applicable / Not Assessed'
                if (!empty($value) && array_diff($value, $notApplicableValues) === []) {
                    $result[] = (object) [
                        'attribute_field' => $attributeField,
                        'percentage' => '0%',
                        'banding_output' => 'Not Applicable / Not Assessed',
                    ];
                }
            }
        }
    
        return $result;
    }

    private function filterPayload(array $payloadToCalc, array $noCalculationPayload) {
        // Extract attribute_field values from $noCalculationPayload
        $noCalcFields = array_map(function ($item) {
            return $item->attribute_field;
        }, $noCalculationPayload);
    
        // Filter $payloadToCalc, removing items that have matching attribute_field in $noCalcFields
        $filteredPayload = array_filter($payloadToCalc, function ($item) use ($noCalcFields) {
            return !(isset($item['attribute_field']) && in_array($item['attribute_field'], $noCalcFields));
        });
    
        return $filteredPayload;
    }

    private function updateInconsistentGradingIndividualPolicyType($riskGradings, &$submission, $policyTypeId)
    {
        $payloadToCalc = [];
        $submissionDetails=[];

        foreach ($riskGradings as $attributes) {
            $attribute          = $attributes->get_rg_attribute;
            $attrAttribute      = $attribute->attribute;
            $attrPolicyId       = $attribute->rg_policy_type_id;
            $attrWithUnderscore = str_replace(' ', '_', $attrAttribute);
            $attributeField     = 'attr-' . $attrWithUnderscore . '-' . $attrPolicyId;

            if(isset($submission->{$attributeField})){
                $submissionDetails[$attributeField][$submission->{$attributeField}]=[];
            }

            $subAttributes  = $attributes->sub_attributes ?? $attributes->ordered_by_id_sub_attributes;

            foreach ($subAttributes as $subAttr) {
                $subAttribute       = $subAttr->get_rg_sub_attribute;
                $subAttrAttribute   = $subAttribute->sub_attribute;
                $subAttrWithHypen   = str_replace(' ', '-', $subAttrAttribute);
                $subAttributeField  = 'sub-attribute-' . $subAttrWithHypen . '_' . $attrWithUnderscore . '-' . $attrPolicyId;
                if(isset($submissionDetails[$attributeField])){
                    $submissionDetails[$attributeField][$submission->{$attributeField}][$subAttrAttribute]=$submission->{$subAttributeField}??'not_applicable_not_assessed';
                }
                $submissionDetails['attribute_names'][]     = $attrAttribute;
            }
        }

        $filteredArray = $this->filterArray($submissionDetails);
        $filteredArray['policy_type_id']=$policyTypeId;

        if(count($filteredArray)>0){
            $payloadToCalc=$this->transformArray($filteredArray);
        }

        $noCalculationPayload=$this->transformArrayWithBanding($payloadToCalc);
        
        foreach ($noCalculationPayload as $grading) {
            $submission->{$grading->attribute_field} = 'not_applicable_not_assessed';
        }

        $payloadToCalc=$this->filterPayload($payloadToCalc,$noCalculationPayload);

        if (!empty($payloadToCalc) && count(array_keys($payloadToCalc)) > 2) {
            $result = json_decode(Api::post('/api/v1/standard-risk/recalculate-gradings', $payloadToCalc));

            foreach ($result as $grading) {
                $submission->{$grading->attribute_field} = $grading->banding_output;
            }
        }
    }
}
