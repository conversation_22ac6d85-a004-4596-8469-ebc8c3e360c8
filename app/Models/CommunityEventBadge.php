<?php

namespace App\Models;

use Exception;
class CommunityEventBadge
{
    static function getByType($eventType) {
        switch ($eventType) {
            case CommunityMessageTypes::TYPE_WEBINAR:
                return [ 'icon' => 'icon-play-circle', 'title' => 'Webinar'];
            case CommunityMessageTypes::TYPE_PODCAST:
                return [ 'icon' => 'icon-radio', 'title' => 'Podcast'];
            case CommunityMessageTypes::TYPE_EVENT:
                return [ 'icon' => 'icon-users', 'title' => 'In Person Event'];
            default:
                throw new Exception('No badge for given event type '.$eventType.'.');
        }
    }
}