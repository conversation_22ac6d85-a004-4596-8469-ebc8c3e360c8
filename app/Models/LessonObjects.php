<?php

namespace App\Models;

use Aws\Laravel\AwsFacade as AWS;
use Exception;
class LessonObjects
{
    protected $s3;

    public function __construct()
    {
        $this->s3 = AWS::createClient('s3');
    }


    public function uploadImage($path, $filename, $course_id, $lesson_id){

        try{
            $upload = $this->s3->putObject(array(
                'Bucket' => config('app.aws.bucket'),
                'Key'    => 'lms/' . $course_id . '/' . $lesson_id . '/' . $filename,
                'SourceFile' => $path
            ));

            return $upload;

        }
        catch(Exception $ex){
            return null;
        }


    }

    public function uploadVideo($path, $filename, $course_id, $lesson_id, $raw_filename){
        try{
            $upload = $this->s3->putObject(array(
                'Bucket' => config('app.aws.bucket'),
                'Key'    => 'lms/' . $course_id . '/' . $lesson_id . '/video-' . $filename,
                'SourceFile' => $path,
                'Metadata'     => array(    
                    's3path' => 'lms/' . $course_id . '/' . $lesson_id. '/',
                    'filename' => 'video-' . $raw_filename
                )
            ));

            return $upload;

        }
        catch(Exception $ex){
            return null;
        }


    }

    public function uploadAudio($path, $filename, $course_id, $lesson_id, $raw_filename){
        try{
            $upload = $this->s3->putObject(array(
                'Bucket' => config('app.aws.bucket'),
                'Key'    => 'lms/' . $course_id . '/' . $lesson_id . '/audio-' . $filename,
                'SourceFile' => $path,
                'Metadata'     => array(    
                    's3path' => 'lms/' . $course_id . '/' . $lesson_id. '/',
                    'filename' => 'audio-' . $raw_filename,
                    'isaudio' => '1'
                )
            ));

            return $upload;

        }
        catch(Exception $ex){
            return null;
        }


    }


    public function getImage($uuid, $course_id, $lesson_id){

        try{

            $signedUrl = $this->s3->getObjectUrl(config('app.aws.bucket'),
                'lms/'. $course_id . '/' . $lesson_id .'/' . $uuid,
                '+10 minutes'
                );



            return $signedUrl;
        }
        catch(Exception $ex){
            return null;
        }

    }

    public function checkExist($file_name, $course_id, $lesson_id){

        try{
            return $object = $this->s3->doesObjectExist(
                config('app.aws.bucket'),
                'lms/' . $course_id .'/' . $lesson_id .'/' . $file_name
            );
            

        }catch (Exception $ex){
            return null;
        }

    }
    public function checkExist2($uu_id, $course_id, $lesson_id){

        try{
            return $this->s3->waitUntilObjectExists(array(
                'Bucket' => config('app.aws.bucket'),
                'Key' => 'lms/' . $course_id .'/' . $lesson_id .'/' . $uu_id)
            );

        }catch (Exception $ex){
            return null;
        }

    }
    public function duplicateFolder($file_name, $course_id, $lesson_id, $newLesson_id){
        try{
            $upload = $this->s3->copyObject(
            array(
                'Bucket' => config('app.aws.bucket'),
                'Key'    => 'lms/' . $course_id . '/' . $newLesson_id . '/' . $file_name,
                'CopySource' => config('app.aws.bucket').'/lms/' . $course_id . '/' . $lesson_id . '/' . $file_name
            ));
            return $upload;

        }
        catch(Exception $ex){
            return null;
        }


    }

}