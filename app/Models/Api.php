<?php

namespace App\Models;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Http\Request;
use GuzzleHttp\Client as GuzzleClient;
class Api
{
    /**
     * Get method
     *
     * @var string
     */
    public static function get($url, $queryParams = [])
    {
        $url    = self::fixTrailingSlash($url);
        $client = new GuzzleClient();
        $res    = $client->request('GET', $url);
        return $res->getBody();
    }

    /**
     * Get method
     *
     * @var string
     */
    public static function getWithParams($url, $queryParams = [])
    {
        $url    = self::fixTrailingSlash($url);
        $client = new GuzzleClient();
        if (!empty($queryParams)) { 
            $res    = $client->request('GET', $url, ['query' => $queryParams]);
        } else {
            $res    = $client->request('GET', $url);
        }
        return $res->getBody();
    }

    /**
     * Post method
     *
     * @var string
     * @var array
     */
    public static function post($url, $data=array())
    {
        $url = self::fixTrailingSlash($url);
		$client = new GuzzleClient();
		$res = $client->request('POST', $url,
			['form_params' => $data]
		);
		return $res->getBody();
    }

    /**
     * Put method
     *
     * @var string
     * @var int
     * @var array
     */
    public static function put($url, $data=array())
    {
        $url = self::fixTrailingSlash($url);
		$client = new GuzzleClient();
		$res = $client->request('PUT', $url,
			['form_params' => $data]
		);
		return $res->getBody();
        // $curl = curl_init();
        // curl_setopt_array($curl, [
        // CURLOPT_RETURNTRANSFER  => 1,
        // CURLOPT_USERPWD         => Crypt::encrypt(config('app.api.username')).':'.Crypt::encrypt(config('app.api.password')),
        // CURLOPT_URL             => config('app.api.endpoint').$url,
        // CURLOPT_CUSTOMREQUEST   => 'PUT',
        // CURLOPT_POSTFIELDS      => http_build_query($data),
        // //CURLOPT_HTTPHEADER      => array('session_ip: '.Request::getClientIp(),'session_agent: '.$_SERVER['HTTP_USER_AGENT'])
        // ]);
        // $response = curl_exec($curl);
        // curl_close($curl);
        // return $response;
    }

    /**
     * delete method
     *
     * @var string
     * @var int
     */
    public static function delete($url)
    {
        $url    = self::fixTrailingSlash($url);
        $client = new GuzzleClient();
        $res    = $client->request('DELETE', $url);
        return $res->getBody();

        // $curl = curl_init();
        // curl_setopt_array($curl, [
        // CURLOPT_RETURNTRANSFER  => 1,
        // CURLOPT_USERPWD         => Crypt::encrypt(config('app.api.username')).':'.Crypt::encrypt(config('app.api.password')),
        // CURLOPT_URL             => config('app.api.endpoint').$url,
        // CURLOPT_CUSTOMREQUEST   => 'DELETE',
        // //CURLOPT_HTTPHEADER      => array('session_ip: '.Request::getClientIp(),'session_agent: '.$_SERVER['HTTP_USER_AGENT'])
        // ]);
        // $response = curl_exec($curl);
        // curl_close($curl);
        // return $response;
    }

    public static function check_key($userType)
    {
        //print_r(config('app.api.endpoint').'api/v1/external-surveyors/mobile/auth'); exit;
        if (!isset($_SERVER["HTTP_X_RR_API_KEY"])) {
            return '';
        } else {
            $data = ['api_key' => $_SERVER["HTTP_X_RR_API_KEY"]];
            $curl = curl_init();
            curl_setopt_array($curl, [
                CURLOPT_RETURNTRANSFER  => 1,
                CURLOPT_USERPWD         => Crypt::encrypt(config('app.api.username')).':'.Crypt::encrypt(config('app.api.password')),
                CURLOPT_URL             => config('app.api.endpoint').'api/v1/'.$userType.'/mobile/auth',
                CURLOPT_POST            => 1,
                CURLOPT_POSTFIELDS      => http_build_query($data),
                //CURLOPT_HTTPHEADER      => array('session_ip: '.Request::getClientIp(),'session_agent: '.$_SERVER['HTTP_USER_AGENT'])
            ]);
            $response = curl_exec($curl);
            curl_close($curl);
            return $response;
        }
    }
    
    public static function check_dms_key()
    {
        if (!isset($_SERVER["HTTP_X_DMS_API_KEY"])) {
            return '';
        } else {
            $data = ['api_key' => $_SERVER["HTTP_X_DMS_API_KEY"]];
            $curl = curl_init();
            curl_setopt_array($curl, [
                CURLOPT_RETURNTRANSFER  => 1,
                CURLOPT_USERPWD         => Crypt::encrypt(config('app.api.username')).':'.Crypt::encrypt(config('app.api.password')),
                CURLOPT_URL             => config('app.api.endpoint').'api/v1/external-surveyors/mobile/auth',
                CURLOPT_POST            => 1,
                CURLOPT_POSTFIELDS      => http_build_query($data),
                CURLOPT_HTTPHEADER      => array('session_ip: '.Request::getClientIp(),'session_agent: '.$_SERVER['HTTP_USER_AGENT'])
            ]);
            $response = curl_exec($curl);
            curl_close($curl);
            return $response;
        }
	}	
    
    public static function postCQLive($url, $data=array())
    {
        if (!isset($_SERVER["HTTP_X_CQLIVE_API_KEY"])) {
            return '';
        } else {            
            $curl = curl_init();
            curl_setopt_array($curl, [
                CURLOPT_RETURNTRANSFER  => 1,
                CURLOPT_URL             => config('app.api.endpoint').$url,
                CURLOPT_POST            => 1,
                CURLOPT_POSTFIELDS      => http_build_query($data),
                CURLOPT_HTTPHEADER      => array('x-cqlive-api-key:'.Request::header('x-cqlive-api-key'),'user:'.Request::header('user'),'password:'.Request::header('password'))
            ]);
            $response = curl_exec($curl);
            if ($response === false) {
                throw new \Exception(curl_error(($curl), curl_errno($curl)));
            }
            curl_close($curl);
            return $response;
        }
    }

    /**
     * Remove trailing slash from endpoint to avoid double slash in link
     *
     * @param string $endpoint
     * @param string $baseUrl
     * @return string
     */
    public static function fixTrailingSlash(string $endpoint, string $baseUrl = ""): string
    {
        if (empty($baseUrl)) {
            $baseUrl = trim(config('app.api.endpoint'), '/');
        }
        return $baseUrl . '/' . trim($endpoint, '/');
    }
}
