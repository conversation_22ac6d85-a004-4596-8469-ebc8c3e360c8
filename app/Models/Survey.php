<?php

namespace App\Models;

use Aws\Laravel\AwsFacade as AWS;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Cache;
class Survey
{

	protected $s3;

    public function __construct()
    {
        $this->s3 = AWS::createClient('s3');
    }

	public function download($file, $name)
	{
		$cmd = $this->s3->getCommand('GetObject', [
            'Bucket' => config('app.aws.bucket'),
            'Key' => $file
        ]);

		$request = $this->s3->createPresignedRequest($cmd, '+10 minutes');
		$signedUrl = (string)$request->getUri();

		if ($signedUrl) {
			//$tempImage = tempnam(sys_get_temp_dir(), $name);
			$stream = file_get_contents($signedUrl);
			File::put(storage_path() . '/file_to_download/'.$name, $stream);
			return storage_path() .'/file_to_download/' . $name;
			// copy($signedUrl, $tempImage);

			// return Response::download($tempImage, $name);
		}
	}

	public function downloadLink($file, $name, $time = '+10 minutes')
	{
		$cmd = $this->s3->getCommand('GetObject', [
			'Bucket' => config('app.aws.bucket'),
			'Key' => $file
		]);
		$request = $this->s3->createPresignedRequest($cmd, '+' . $time);
		
		return (string)$request->getUri();
	}

	public function downloadLinkForMicrosite($file, $name, $time = '+10 minutes')
	{
		$cmd = $this->s3->getCommand('GetObject', [
			'Bucket' => config('app.aws.bucket'),
			'Key' => $file,
			'ResponseContentDisposition' => 'inline; filename='.$name
		]);
		$request = $this->s3->createPresignedRequest($cmd, '+' . $time);

		$url=(string)$request->getUri();
		
		return $url;
	}
	

	public function upload($file, $documentName, $organisation)
	{
		$file = fopen($file, 'r');

	    $upload = $this->s3->putObject([
			'Bucket'     		   => config('app.aws.bucket', null),
			'Key'                  => $organisation.'/'.$documentName,
			'Body'           => $file,
			'ServerSideEncryption' => 'AES256'
	    ]);
	    // return $upload;

	    return [
	    	'response' => 'success',
	    	'message'  => 'Uploaded document'
	    ];
	}

	public function update($oldFile, $file, $name, $organisationID)
	{
		$file = fopen($file, 'r');
		$this->s3->putObject([
			'Bucket'     		   => config('app.aws.bucket', null),
			'Key'        		   => $oldFile,
			'Body' 				   => $file,
			'ServerSideEncryption' => 'AES256'
		]);

		return [
			'response' => 'success',
			'message'  => 'Uploaded document'
		];
	}

	public function destroy($fileName)
	{
		$result = $this->s3->deleteObject([
			'Bucket' => config('app.aws.bucket'),
			'Key'    => $fileName
	    ]);

		if ($result) {
			return [
				'response' => 'success',
				'message'  => 'Document deleted'
			];
		}

		return [
			'response' => 'error',
			'message'  => 'Message does not exist'
		];
	}

	public function getSurveyPhotographs($attachments, $survey_id)
	{
		if (!empty($attachments)) {
			$fieldName = ($attachments[0])->field_name;
			$cacheKey  = "survey_attachments_{$survey_id}_{$fieldName}";
			$attachmentData = $this->getSetAttachmentCache($cacheKey);
			if ($attachmentData) {
				return $attachmentData;
			} else {
				$attachmentData=$this->getSurveyPhotographsData($attachments, $survey_id);
				$this->getSetAttachmentCache($cacheKey,$attachmentData);
				return $attachmentData;
			}
		}
		return '';
	}

	private function getSurveyPhotographsData($attachments, $survey_id){
		$files['files'] = [];
		$image_files = array('jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'tiff');
		foreach ($attachments as $attachment) {
			$fileName_cloud = 'survey_uploads/'.$attachment->field_name.'/'.$attachment->cloud_file_name;
			$file = $this->downloadLink($fileName_cloud, $attachment->file_name);

			$file_info = pathinfo($attachment->file_name);
			$extension = $file_info['extension'];

			$ch = curl_init($file);

			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($ch, CURLOPT_HEADER, true);
			curl_setopt($ch, CURLOPT_NOBODY, true);

			$data = curl_exec($ch);
			$size = curl_getinfo($ch, CURLINFO_CONTENT_LENGTH_DOWNLOAD);

			curl_close($ch);

			$arr = [
			"id"    =>    $attachment->id,
			"name"    =>    $attachment->file_name,
			"cloud"    =>    $fileName_cloud,
			"notes"    =>    $attachment->notes,
			"url"    =>    in_array($extension, $image_files) ? $file : '',
			"extension" => $extension,
			"thumbnailUrl"    =>    in_array($extension, $image_files) ? $file : '',
			"deleteUrl"    =>    config('app.url').'/surveys/'.$survey_id.'/delete/'.$attachment->id,
			"size"    => $size
			];
			array_push($files['files'], $arr);
		}
		return json_encode($files);
	}

	private function getSetAttachmentCache(string $cacheKey, $data=null)
    {
        if($data){
            Cache::put($cacheKey, $data, 570); //9.5 mins, as signed url is set as 10 mins.
        }
        
        if(Cache::has($cacheKey)){
            return Cache::get($cacheKey);
        }
        return $data;
    }

}
