<?php

namespace App\Models;
use Illuminate\Support\Facades\Config;
class Mailgun
{
    /**
     * Get method
     *
     * @var string
     * @return json
     */
    public static function get($url)
    {
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_RETURNTRANSFER  => 1,
            CURLOPT_URL             => $url,
            CURLOPT_HTTPHEADER      => array('Authorization:Basic ' . Config::get('app.mailgun.secret_key'))
        ]);
        $response = curl_exec($curl);
    
        curl_close($curl);
        return $response;
    }

}