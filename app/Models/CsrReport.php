<?php

namespace App\Models;
use App\Models\Api;
use Exception;
use Illuminate\Support\Facades\Session;
use DateTime;
use Illuminate\Support\Facades\Cache;
class CsrReport
{
    public function getCSRReport($organisation_id)
    {
        $forms = [];
        if ($organisation_id == 'all') {
            //NOTE: This cache key is shared and is used by Admin and Client 'surveys-all'
            //Reference cache Risk-Reduce-Client/app/Models/CsrReport getCSRReport($organisation_id) 
            //TODO: List down all cache keys that is shared on all application
            if(Cache::has('surveys-all')){
                $surveys = Cache::get('surveys-all');
            }else{
                $surveys = json_decode(Api::get('api/v1/surveys/all'));
                Cache::put('surveys-all', $surveys, 1000);
            }
            $organisation = 'all';
        } else {
            //NOTE:: 'organisation'.$oganisation_id, this cache can be used on Client and Admin
            //Reference cache Risk-Reduce-Client/app/Models/CsrReport getCSRReport($organisation_id) 
            if(Cache::has('organisation'.$organisation_id)){
                $organisation = Cache::get('organisation'.$organisation_id);
            }else{
                $organisation = json_decode(Api::get('api/v1/organisation/'.$organisation_id));
                Cache::put('organisation'.$organisation_id, $organisation, 1000);
            }

            $organisation = strtolower(str_replace(' ', '-', $organisation->data->name));
            $organisation = preg_replace('/[^a-z0-9\-]/', '', $organisation);

            //NOTE: Shared Cache. TODO: Cleanup and remove comments
            if(Cache::has('surveys-'.$organisation_id)){
                $surveys = Cache::get('surveys-'.$organisation_id);
            }else{
                $surveys = json_decode(Api::get('api/v1/surveys/all?organisation_id='.$organisation_id));
                Cache::put('surveys-'.$organisation_id, $surveys, 1000);
            }
        }
        
        $survey_ids = [];
        $legacy_srfs = [];
        $alphas = range('B', 'Z');
        $survey_count = 1;
        if (isset($surveys->data) && !empty($surveys->data)) {
            foreach ($surveys->data as $survey) {
                $survey_ids[$survey->id] = $this->num2alpha($survey_count);

                if (isset($survey->legacy_srf) && $survey->legacy_srf !== null) {
                    $legacy_srfs[$survey->id] = $survey->legacy_srf;
                }
                ++$survey_count;
            }
        }

        $submissions = json_decode(Api::get('api/v1/survey-submission/all?org_id='.$organisation_id));
        
        if ($submissions) {
            foreach ($submissions->data as $submission) {
                if (isset($submission->csr_status) && $submission->csr_status == 'submitted') {
                    // TODO: comma seperated value API call from submissions form ids as a single call
                    $response = json_decode(Api::get('api/v1/risk-improvement/form/'.$submission->form_id));

                    if (!is_array($response) && !is_array($response->data)) {
                        $form = json_decode($response->data, true);

                        if (array_key_exists('fields', $form)) {
                            array_push($forms, $form['fields']);
                        }
                    }
                }
            }
        }

        $rr_colors = [
                'Requires Improvement' => '#fc0d1b',
                'Below Average' => '#fdbf2d',
                'Average' => '#fffd38',
                'Good' => '#00b050',
                'Superior' => '#0070c0',
                'Not Applicable / Not Assessed' => '#dddddd',
                'Contact U/W within 24 hours' => '#fc0d1b',
                'Multiple Requirements identified - monthly updates required' => '#fdbf2d',
                'Single Requirement - monitor progress' => '#fffd38',
                'Recommendations Only -generally reasonable controls' => '#00b050',
                'Satisfactory' => '#00b050',
                'Not Applicable' => '#dddddd',
            ];

        $rcas = [];
        $srfs = [];
        $rca_values = [];
        $srf_colors = [];

        if ($submissions) {
            foreach ($submissions->data as $submission_obj) {
                $location=isset($submission_obj->survey->location->id)?$submission_obj->survey->location:null;                
                foreach ($forms as $form_fields) {
                    foreach ($form_fields as $fields) {
                        foreach ($fields as $key => $field_type) {
                            if ($key == 'select_risk_control' && isset($field_type[1]) && isset($field_type[1]['value'])) {
                                $field_id = $field_type[1]['value'];

                                if (isset($submission_obj->$field_id)) {
                                    array_push($rcas, (string) $field_id);
                                    try {
                                        $srf = in_array($submission_obj->survey->id, $legacy_srfs) ? $legacy_srfs[$submission_obj->survey->id] : $submission_obj->survey->id;
                                    } catch (Exception $e) {
                                        continue;
                                    }

                                    $srfs[(string) $srf] = isset($location)?(string) ($srf."<span>".$location->location_name.", ".$location->location_id."</span>") : (string)$srf;

                                    if (!isset($srf_colors[(string) $srf])) {
                                        $srf_colors[(string) $srf] = [];
                                    }

                                    $srf_colors[(string) $srf][(string) $field_id] = $rr_colors[$submission_obj->$field_id];

                                    $rca_values[(string) $field_id] = $field_type[0]['value'];
                                }
                            }
                        }
                    }
                }
            }
        }       

        $rcas = array_values(array_unique($rcas));
        $srfs = array_unique($srfs); // can be asort'ed if required
        $rca_values = array_unique($rca_values);

        $data = [];

        // SFAs
        $data[0] = [];
        $sfa_count = 0;

        foreach ($srfs as $srf) {
            if ($sfa_count == 0) {
                array_push($data[0], '');
            }

            array_push($data[0], 'SRF'.$srf);

            ++$sfa_count;
        }

        $var = [];

        // RCAs
        foreach ($rcas as $rca) {
            $arr = [];

            if (array_key_exists((string) $rca, $rca_values)) {
                array_push($arr, (string) $rca_values[(string) $rca]);
            } else {
                array_push($arr, $rca);
            }

            foreach ($srfs as $srf) {
                $color = isset($srf_colors[intval($srf)][(string) $rca]) ? $srf_colors[intval($srf)][(string) $rca] : '';

                array_push($arr, $color);
            }

            array_push($var, $arr);
        }

        foreach ($var as $va) {
            array_push($data, $va);
        }

        $csrData['organisation'] = $organisation;
        $csrData['data'] = $data;
        $csrData['survey_ids'] = $survey_ids;
        $csrData['rcas'] = $rcas;
        $csrData['rca_values'] = $rca_values;

        return $csrData;
    }

    // http://stackoverflow.com/questions/3302857/algorithm-to-get-the-excel-like-column-name-of-a-number
    public function num2alpha($n)
    {
        for ($r = ''; $n >= 0; $n = intval($n / 26) - 1) {
            $r = chr($n % 26 + 0x41).$r;
        }

        return $r;
    }

    public function getRiskRecommendationTracker($organisation_id, $cron = false)
    {
        $survey_status = [];
        $RR_tyre = [];
        $RR_status = [];
        $RR_recommendation = [];
        $RR_loss_estimate=[];

        $submissions = json_decode(Api::get('/api/v1/survey-submission/rr-all?organisation_id='.$organisation_id));        

        if (!$cron && Session::get('user')->login_type == 'broker-user') {
            $valid = false;
            $orgs_for_broker = json_decode(Api::get('api/v1/organisation/broker/'.Session::get('user')->broker_id));
            $orgs_broker = [];

            foreach ($orgs_for_broker->data as $org) {
                array_push($orgs_broker, $org->id);
                if ($organisation_id == $org->id) {
                    $valid = true;
                }
            }
        }

        if ($submissions && $submissions->data) {
            $cards             = $submissions->data;
            $columns           = [];
            $survey_status     = [];
            $RR_tyre           = [];
            $RR_status         = [];
            $RR_recommendation = [];
            foreach ($cards as $key => $card) {
                $location_info = [
                    !empty($card->survey->location->location_name) ? $card->survey->location->location_name : '',
                    !empty($card->survey->location->postcode) ? $card->survey->location->postcode : ''
                ];

                $location_name     = !empty($card->survey->location->location_name) ? $card->survey->location->location_name : '';
                $location_postcode = !empty($card->survey->location->postcode) ? $card->survey->location->postcode : '';

                // Get reports where uwr_status = "submitted"
                if (isset($card->survey_id) && 
                    !empty($card->survey_id) && 
                    isset($cards->loss_estimates->{$card->survey_id}->PML_Property) &&
                    !empty($cards->loss_estimates->{$card->survey_id}->PML_Property) &&
                    isset($cards->loss_estimates->{$card->survey_id}->PML_Business_Interruption) &&
                    !empty($cards->loss_estimates->{$card->survey_id}->PML_Business_Interruption) &&
                    isset($card->uwr_status) && 
                    !empty($card->uwr_status) && 
                    $card->uwr_status=='submitted' &&
                    (isset($card->survey) && !empty($card->survey)) &&
                    $card->survey->uwr_uw_status==2) {

                    $graphData = [];
                    $graphData['postcode'] = $card->survey_contacts[0]->postcode;
                    
                    if ($location_info[0] != ''){
                        $graphData['postcode'] = $this->trimString($location_name) . "\n" . $location_postcode;
                    }

                    $graphData['property'] = (float) str_replace(" ", "", str_replace(",","", str_replace("£", "", $cards->loss_estimates->{$card->survey_id}->PML_Property)));
                    $graphData['business-interruption'] = (float) str_replace(" ", "", str_replace(",","", str_replace("£", "", $cards->loss_estimates->{$card->survey_id}->PML_Business_Interruption)));

                    if (isset($graphData['property']) && !empty($graphData['property'])) {
                        $RR_loss_estimate[$graphData['postcode']]=[
                            'Property Damage' => $graphData['property'],
                            'Business Interruption'=> $graphData['business-interruption']
                        ];
                    }
                }

                if (isset($card->schedule->meta)) {
                    foreach ($card->risk_recommendations as $risk_rec) {
                        if (!$cron && Session::get('user')->login_type == 'broker-user') {
                            if (!in_array($card->organisation->id, $orgs_broker)) {
                                continue;
                            }
                        }

                        for ($i = 1; $i <= 15; ++$i) {
                            if (isset($card->{$risk_rec.'_'.$i.'_classification'}) && $card->{$risk_rec.'_'.$i.'_classification'} != '') {
                                $survey_date = isset($card->schedule) ? (isset($card->schedule->start) ? date('d/m/Y', strtotime($card->schedule->start)) : '-') : null;
                                $srf = isset($card->survey->legacy_srf) && $card->survey->legacy_srf != null ? 'SRF'.$card->survey->legacy_srf : 'SRF'.$card->survey->id;

                                $title = $card->{$risk_rec.'_'.$i.'_ref'};
                                $title = substr($title, 0, strripos($title, ':'));
                                array_push($RR_tyre, $title);

                                $properties = [
                                    'Client' => $card->organisation != null ? $card->organisation->name : 'N/A',
                                    'SRF' => isset($card->survey->legacy_srf) && $card->survey->legacy_srf !== null ? 'SRF'.$card->survey->legacy_srf : 'SRF'.$card->survey->id,
                                    'Survey Type' => isset($card->survey->policy_name) && $card->survey->policy_name != null ? $card->survey->policy_name : '',
                                    'Survey Date' => isset($card->schedule) ? (isset($card->schedule->actual_submission_deadline) ? date('d/m/Y', strtotime($card->schedule->actual_submission_deadline)) : '-') : null,
                                    'MGA Scheme' => $card->organisation->mga_scheme,
                                    'Required By' => isset($card->{$risk_rec.'_'.$i.'_required_by'}) ? $card->{$risk_rec.'_'.$i.'_required_by'} : '-',
                                ];

                                $postcode = 'N/A';
                                if (isset($card->survey->visit_arrangement) && $card->survey->visit_arrangement != '' && isset($card->surveyContacts)) {
                                    foreach ($card->surveyContacts as $contact) {
                                        if ($contact->postcode != '' && !is_null($contact->postcode)) {
                                            $postcode = $contact->postcode;
                                            continue;
                                        }
                                    }
                                }

                                $properties['Organisation'] = isset($card->survey->organisation->name) && $card->survey->organisation->name != null ? $card->survey->organisation->name : '';
                                $properties['Location'] =   $location_info;
                                $properties['Description'] = isset($card->{$risk_rec.'_'.$i.'_description'}) ? $card->{$risk_rec.'_'.$i.'_description'} : '-';
                                $properties['Action'] = isset($card->{$risk_rec.'_'.$i.'_action'}) ? $card->{$risk_rec.'_'.$i.'_action'} : '-';
                                $properties['Title'] = isset($card->{$risk_rec.'_'.$i.'_title'}) ? $card->{$risk_rec.'_'.$i.'_title'} : '-';
                                $properties['Closed'] = isset($card->{$risk_rec.'_'.$i.'_issue_closed'}) ? $card->{$risk_rec.'_'.$i.'_issue_closed'} : '0';
                                $properties['CSR Status'] = isset($card->csr_ststus) ? $card->csr_ststus : 'completed';
                                $properties['Postcode'] = $postcode;
                                $properties['Classification'] = isset($card->{$risk_rec.'_'.$i.'_classification'}) ? $card->{$risk_rec.'_'.$i.'_classification'} : '-';

                                array_push($survey_status, $this->getCardColumnByCriteria($card, $risk_rec.'_'.$i));
                                array_push($RR_status, [str_replace('SRF', 'CSR', $srf), $properties['Closed'], $properties['Location']]);
                                array_push($RR_recommendation, str_replace('SRF', 'CSR', $properties['Title']));

                                $columns[$this->getCardColumnByCriteria($card, $risk_rec.'_'.$i)]['cards'][] = (object) [
                                    'title' => $card->{$risk_rec.'_'.$i.'_ref'},
                                    'url' => '/surveys/report/'.$card->survey->id.'/risk-recommendation/'.$risk_rec.'_'.$i.'_',
                                    'properties' => $properties,
                                ];
                            }
                            $column_headings = [
                                'SRF',
                                'Postcode',
                                'Organisation',
                                'Ref',
                                'Title',
                                'Classification',
                                'Required By',
                                'Status',
                                'Description',
                                'Action',
                                'Survey Type',
                                'Survey Date',
                            ];
                        }
                    }
                }
            }

            // for ($i=1; $i <5 ; $i++) { 
            //      $label="UWR123\nB1 ".$i."JS";
            //      $RR_loss_estimate[$label]=['Property Damage' => rand(3000,5000),'Business Interruption'=>rand(3000,5000)];
            // }

            return ['columns' => $columns, 'survey_status' => $survey_status, 'RR_tyre' => $RR_tyre,'RR_tyre_label' => $submissions->label_array, 'RR_status' => $RR_status, 'RR_recommendation' => $RR_recommendation,'RR_loss_estimate' => $RR_loss_estimate];
        }

        return [];
    }

    public function getCardColumnbyCriteria($card, $risk_rec_prefix = '')
    {
        // mapping:
        // feedback -> less_than_30
        // overdue -> greater_than_30

        if (isset($card->{$risk_rec_prefix.'_issue_closed'}) && $card->{$risk_rec_prefix.'_issue_closed'} == '1') {
            return 'closed';
        }

        $today = new DateTime();
        $current_date = isset($card->{$risk_rec_prefix.'_required_by'}) && ($card->{$risk_rec_prefix.'_required_by'} != '') ? \Carbon\Carbon::createFromFormat('d-m-Y', str_replace('/', '-', $card->{$risk_rec_prefix.'_required_by'})) : null;

        if (isset($current_date)) {
            $interval = $today->diff($current_date);
            $diff = $interval->format('%r%a');
            if ($diff <= 0) {
                return 'greater_than_30';
            }
        }

        $messages = isset($card->rrm->{$risk_rec_prefix.'_message'}) ? $card->rrm->{$risk_rec_prefix.'_message'} : [];
        if (count($messages) > 0 && isset($messages[0]->sent_at)) {
            return 'less_than_30';
        }

        return 'open';
    }

    private function trimString($string){
        $maxLength = 20;
        $dots = "...";
        if(strlen($string) > $maxLength){
            return substr($string, 0, $maxLength - strlen($dots)) . $dots;
        }
        return $string;
    }
}
