<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Sanctum\HasApiTokens;
use Illuminate\Support\Facades\Session;
use App\Models\Api;

class User extends Authenticatable {

	use HasApiTokens, HasFactory, Notifiable;

	/**
	 * The database table used by the model.
	 *
	 * @var string
	 */
	protected $table = 'liberty_users';

	/**
	 * The attributes excluded from the model's JSON form.
	 *
	 * @var array
	 */
	protected $hidden = array('password', 'remember_token');

	protected $fillable = array(
		'email',
		'password',
		'created_at',
		'updated_at',
		'organisation_id',
		'first_name',
		'last_name',
		'activation_code',
		'activation_code_expires',
		'activated',
		'reset_password_code',
		'reset_password_code_expires'
	);

  /**
	 * recast stdClass object to an object with type
	 *
	 * @param string $className
	 * @param stdClass $object
	 * @throws InvalidArgumentException
	 * @return mixed new, typed object
	 */
	public static function recast($className, $object)
	{
		// if (!class_exists($className))
		// 	throw new InvalidArgumentException(sprintf('Inexistant class %s.', $className));
		$newClassName = "\\App\Models\\" . $className;
		$new = new $newClassName();

		foreach($object as $property => $value)
		{
			$new->$property = $value;
			unset($object->$property);
		}
		unset($value);
		$object = $object;
		return $new;
	}

	public static function getUserType()
    {
        $user = Session::get('user');
        $login_type = $user->login_type ?? "";
        $user_role = $user->role ?? "";

        switch($login_type) {
            case 'risk-control':
                if ($user_role == 'admin') {
                    return 'admin';
                }
                break;
            case 'broker-user':
                return 'broker';
            default:
                // underwriters and risk-engineers
                return $login_type;
        }
    }


    public static function isBrokerAndAllowedInCommunity()
    {
        if (Session::get('user')?->login_type === 'broker-user') {
            $user = Session::get('user');
            $openMarket = json_decode(Api::get('api/v1/broker-users/open-market/'. $user->broker_id));
            if ($openMarket && count($openMarket->orgs) > 0) {
                return true;
            }
        }

        return false;
    }

    public function isRoleAdminOrAccountManager()
    {
        return $this->isAdmin() || $this->isAccountManager();
    }

    public function isAdmin()
    {
        return $this?->role === 'admin';
    }

    public function isAccountManager()
    {
        return $this?->role === 'risk-engineer' && $this?->role_override === 'account-engineer';
    }
}
