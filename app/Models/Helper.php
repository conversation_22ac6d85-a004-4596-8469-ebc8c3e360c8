<?php

namespace App\Models;

class Helper {

	public static function convertArrayKeysToString($array)
	{
		$keys = array_keys($array);
		$values = array_values($array);
		$stringKeys = array_map('strval', $keys);
		$data = array_combine($stringKeys, $values);
		return $data;
	}

	public static function getUniqueObjects($array)
	{		
		$final = [];
		if(count($array)>0){
			foreach ($array as $current) {
				if ( ! in_array($current, $final)) {
					$final[] = $current;
				}
			}
		}
		return $final;
	}
}
