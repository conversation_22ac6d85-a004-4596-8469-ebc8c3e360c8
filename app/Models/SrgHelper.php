<?php

namespace App\Models;
class SrgHelper 
{
	public static function gradingOptions()
	{
		return [
            'Requires Improvement'                                        => '#fc0d1b',
            'Below Average'                                               => '#fdbf2d',
            'Average'                                                     => '#fffd38',
            'Above Average'                                               => '#00b050',
            'Good'                                                        => '#00b050',
            'Superior'                                                    => '#0070c0',
            'Not Applicable / Not Assessed'                               => '#dddddd',
            'Contact U/W within 24 hours'                                 => '#fc0d1b',
            'Multiple Requirements identified - monthly updates required' => '#fdbf2d',
            'Single Requirement - monitor progress'                       => '#fffd38',
            'Recommendations Only -generally reasonable controls'         => '#00b050',
            'Satisfactory'                                                => '#00b050',
            'Not Applicable'                                              => '#dddddd',
            'Poor'                                                        => '#fc0d1b',
        ];
	}

    public static function gradingOptionsLessLegacy()
	{
		return [
            'Superior' => '#0070c0',
            'Above Average' => '#00b050',
            'Average' => '#fffd38',
            'Below Average' => '#fdbf2d',
            'Poor' => '#fc0d1b',
            'Not Applicable / Not Assessed' => '#dddddd',
        ];
	}

    // css class names used for styles
    public static function gradingColorToolTips()
	{
		return [
            'Poor' => 'color1',
            'Below Average' => 'color2',
            'Average' => 'color3',
            'Above Average' => 'color4',
            'Superior' => 'color5',
            'Not Applicable / Not Assessed' => 'color6'
        ];
	}
}