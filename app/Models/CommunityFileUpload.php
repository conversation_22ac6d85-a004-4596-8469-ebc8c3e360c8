<?php

namespace App\Models;

use Aws\Laravel\AwsFacade as AWS;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Config;
class CommunityFileUpload
{

	protected $s3;

    public function __construct()
    {
        $this->s3 = AWS::createClient('s3');
    }

	public function download($file, $name)
	{
	
		$signedUrl = $this->s3->getObjectUrl(
			Config::get('app.aws.bucket'),
			$file,
			'+10 minutes'
		);
		
		if ($signedUrl) {			
			$stream = file_get_contents($signedUrl);
			File::put(storage_path() . '/file_to_download/'.$name, $stream);
			return storage_path() .'/file_to_download/' . $name;
			
		}
	}

	public function link($file, $time = '+10 minutes')
	{
		
		$signedUrl = $this->s3->getObjectUrl(
			Config::get('app.aws.bucket'),
			$file,
			$time
		);

		return $signedUrl;
	}

	public function upload($input, $output)
	{
		$file = fopen($input, 'r');
		$file = fread($file, filesize($input));

	    $upload = $this->s3->putObject([
			'Bucket'     		   => Config::get('app.aws.bucket', null),
			'Key'                  => $output,
			'Body'           	   => $file,
			'ServerSideEncryption' => 'AES256'
	    ]);
	    // return $upload;

        if($upload){
            return [
                'response' => 'success',
                'message'  => 'Uploaded file'
            ];
        }

        return [
            'response' => 'error',
            'message'  => 'Failed to upload file'
        ];
	}

	

	public function destroy($fileName)
	{
		$result = $this->s3->deleteObject([
			'Bucket' => Config::get('app.aws.bucket'),
			'Key'    => $fileName
	    ]);

		if ($result) {
			return [
				'response' => 'success',
				'message'  => 'Document deleted'
			];
		}

		return [
			'response' => 'error',
			'message'  => 'Message does not exist'
		];
	}

	

}
