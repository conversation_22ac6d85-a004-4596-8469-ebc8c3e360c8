<?php

namespace App\Models;

use Aws\Laravel\AwsFacade as AWS;
use Illuminate\Support\Facades\File;
use Exception;

class FileUpload
{
    protected $rackspace;
    protected $s3;

    public function __construct()
    {
        $this->s3 = AWS::createClient('s3');
    }


    public function upload($input, $output, $bucket = "bucket")
    {
        $file = fopen($input, 'r');
        $this->s3->putObject(array(
            'Bucket'     => config('app.aws.' . ($bucket == "bucket" ? "bucket" : $bucket), null),
            'Key'        => $output,
            'Body' => $file,
            'ServerSideEncryption' => 'AES256'
        ));
        return true;
    }

    public function download($cloudFileName, $fileName, $bucket = "bucket")
    {
        if (empty($cloudFileName) || !$this->exists($cloudFileName)) {
            abort(404, 'File cannot be found on the server.');
        }

        $cmd = $this->s3->getCommand('GetObject', [
            'Bucket' => config('app.aws.' . ($bucket == "bucket" ? "bucket" : $bucket), null),
            'Key' => $cloudFileName
        ]);
        $request = $this->s3->createPresignedRequest($cmd, '+10 minutes');
        $signedUrl = (string)$request->getUri();
        
        if ($signedUrl) {
            $stream = file_get_contents($signedUrl);
            File::put(storage_path() . '/file_to_download/' . $fileName, $stream);

            return storage_path() . '/file_to_download/' . $fileName;
        }
    }

    public function link($cloudFileName, $time = '10 minutes', $bucket = "bucket"): string
    {
        if (empty($cloudFileName) || !$this->exists($cloudFileName)) {
            return "";
        }

        $cmd = $this->s3->getCommand('GetObject', [
            'Bucket' => config('app.aws.' . ($bucket == "bucket" ? "bucket" : $bucket), null),
            'Key' => $cloudFileName
        ]);
        $request = $this->s3->createPresignedRequest($cmd, '+' . $time);
        return (string)$request->getUri();
    }

    public function lO($cloudFilePath, $marker, $bucket = "bucket")
    {
        $signedUrl = $this->s3->listObjects(array(
            'Bucket' => config('app.aws.' . ($bucket == "bucket" ? "bucket" : $bucket), null),
            'Prefix' => $cloudFilePath,
            'Marker' => $marker,
            'MaxKeys' => 10000
        ));
        return $signedUrl->getAll();
    }

    public function exists($cloudFileName, $bucket = "bucket")
    {
        $exists = $this->s3->doesObjectExist(
            config('app.aws.' . ($bucket == "bucket" ? "bucket" : $bucket), null),
            $cloudFileName
        );
        return $exists;
    }

    public function destroy($fileName, $bucket = "bucket")
    {
        $result = $this->s3->deleteObject(array(
            'Bucket'     => config('app.aws.' . ($bucket == "bucket" ? "bucket" : $bucket)),
            'Key'        => $fileName
        ));
        if ($result)
            return array('response' => 'success', 'message' => 'Document deleted');
        return array('response' => 'error', 'message'   => 'Message does not exist');
    }


    public function duplicate($input, $output)
    {
        $this->initializeRackspace();
        try {
            $fileData = fopen($input, 'r');
            $this->rackspace->uploadObject($output, $fileData);
            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    public function decrypt($file, $name)
    {
        $ssl = 'openssl aes-256-cbc -d -a -in ' . $file . ' -pass pass:' . config('app.rackspace.document_key');

        $output = shell_exec($ssl);

        File::put(storage_path() . '/file_to_download/' . $name, $output);
        File::delete($file);

        return array('response' => 'success', 'data' => storage_path() . '/file_to_download/' . $name);
    }

    // https://www.php.net/manual/de/function.filesize.php
    public function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, $precision) . ' ' . $units[$pow];
    }
}
