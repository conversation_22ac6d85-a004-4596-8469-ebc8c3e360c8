<?php

namespace App\Models;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class RhsImport implements ToCollection, WithHeadingRow
{
    use Importable;
    /**
    * @param Collection $rows
    */
    public function collection(Collection $rows)
    {
        return $rows;
    }
}
