<?php

namespace App\Models;

use Aws\Laravel\AwsFacade as AWS;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;

class Documents
{
    protected $s3;

    public function __construct()
    {
        $this->s3 = AWS::createClient('s3');
    }

    public function download($file, $name)
    {
        if (!$this->exists($file)) {
            abort(404, 'File cannot be found on the server.');
        }

        $cmd = $this->s3->getCommand('GetObject', [
            'Bucket' => config('app.aws.bucket'),
            'Key' => $file
        ]);
        $request = $this->s3->createPresignedRequest($cmd, '+10 minutes');
        $signedUrl = (string)$request->getUri();

        if ($signedUrl) {
            $filePath = 'file_to_decrypt/' . $name;
            $s3File = file_get_contents($signedUrl);
            Storage::disk('storage')->put($filePath, $s3File);
            return array('response' => 'success', 'data' => storage_path($filePath));
        }
        return ['response' => 'error'];
    }

    public function exists($cloudFileName)
    {
        $exists = $this->s3->doesObjectExist(
            config('app.aws.bucket', null),
            $cloudFileName
        );
        return $exists;
    }

    public function link($cloudFileName, $time = '10 minutes'): string
    {
        if (empty($cloudFileName) || !$this->exists($cloudFileName)) {
            return "";
        }

        $cmd = $this->s3->getCommand('GetObject', [
            'Bucket' => config('app.aws.bucket', null),
            'Key' => $cloudFileName
        ]);
        $request = $this->s3->createPresignedRequest($cmd, '+' . $time);
        return (string)$request->getUri();
    }

    public function upload($file, $documentName, $organisation)
    {
        $file = fopen($file, 'r');
        $this->s3->putObject(array(
            'Bucket'     => config('app.aws.bucket', null),
            'Key'        => $organisation . '/' . $documentName,
            'Body' => $file,
            'ServerSideEncryption' => 'AES256',
        ));
        return array('response' => 'success', 'message ' => 'Uploaded document');
    }

    public function update($oldFile, $file, $name = null, $organisationID = null)
    {
        $file = fopen($file, 'r');
        $mm = $this->s3->putObject(array(
            'Bucket'     => config('app.aws.bucket', null),
            'Key'        => $oldFile,
            'Body' => $file,
            'ServerSideEncryption' => 'AES256',
        ));
        return array('response' => 'success', 'message ' => 'Uploaded document');
    }

    public function destroy($fileName)
    {
        $result = $this->s3->deleteObject(array(
            'Bucket'     => config('app.aws.bucket'),
            'Key'        => $fileName
        ));
        if ($result)
            return array('response' => 'success', 'message' => 'Document deleted');
        return array('response' => 'error', 'message'   => 'Message does not exist');
    }

    public function encrypt($file)
    {
        $clear_file = $file->move(storage_path() . '/file_to_encrypt');
        $ssl = 'openssl aes-256-cbc -a -salt -in ' . $clear_file->getRealPath() . ' -pass pass:' . config('app.rackspace.document_key');

        $output = shell_exec($ssl);
        File::put(storage_path() . '/file_to_upload/' . $file->getClientOriginalName(), $output);
        File::delete($clear_file);

        return storage_path() . '/file_to_upload/' . $file->getClientOriginalName();
    }

    public function decrypt($file, $name)
    {
        $ssl = 'openssl aes-256-cbc -d -a -in ' . $file . ' -pass pass:' . config('app.rackspace.document_key');
        $output = shell_exec($ssl);
        Storage::disk('storage')->put('file_to_download/' . $name, $output);
        File::delete($file);
        return array('response' => 'success', 'data' => storage_path() . '/file_to_download/' . $name);
    }
}
