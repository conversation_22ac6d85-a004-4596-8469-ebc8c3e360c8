<?php

namespace App\Helpers;

use App\Models\FileUpload;
use Illuminate\Support\Facades\Session;
use Carbon\Carbon;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Str;
use Ramsey\Uuid\Uuid;
use Ramsey\Uuid\Exception\UnsatisfiedDependencyException;

class Helpers
{
    public static function assetLink($key, $id = '')
    {
        if ($key == '1') {
            return;
        }
        $_this = new FileUpload;
        //$fileUpload = new file();
        return urldecode($_this->link('assets_library/' . $key));
    }

    public static function shorten($string, $width)
    {
        if (strlen($string) > $width) {
            $string = wordwrap($string, $width);
            $string = substr($string, 0, strpos($string, "\n"));
        }

        return $string;
    }

    public static function generateInitialsFromName($name)
    {
        $words = explode(' ', $name);
        if (count($words) >= 2) {
            return strtoupper(substr($words[0], 0, 1) . substr(end($words), 0, 1));
        }
        return self::makeInitialsFromSingleWord($name);
    }

    protected static function makeInitialsFromSingleWord($name)
    {
        preg_match_all('#([A-Z]+)#', $name, $capitals);
        if (count($capitals[1]) >= 2) {
            return substr(implode('', $capitals[1]), 0, 2);
        }
        return strtoupper(substr($name, 0, 2));
    }

    public static function encryptInt($string, $key)
    {
        $result = '';
        for ($i = 0; $i < strlen($string); $i++) {
            $char = substr($string, $i, 1);
            $keychar = substr($key, ($i % strlen($key)) - 1, 1);
            $char = chr(ord($char) + ord($keychar));
            $result .= $char;
        }
        return base64_encode($result);
    }

    public static function decryptString($string, $key)
    {
        $result = '';
        $string = base64_decode($string);
        for ($i = 0; $i < strlen($string); $i++) {
            $char = substr($string, $i, 1);
            $keychar = substr($key, ($i % strlen($key)) - 1, 1);
            $char = chr(ord($char) - ord($keychar));
            $result .= $char;
        }
        return $result;
    }

    public static function getMeetingDateForUi($date, $timezone)
    {
        $meetingDate = Carbon::parse($date, $timezone);
        $schedForUi = $meetingDate->copy()->format('j M @ G:ia');
        $meetingDay = $meetingDate->copy()->format('d');

        if ($meetingDate->isToday()) {
            $schedForUi = "Today @ " . $meetingDate->copy()->format('G:ia');
        }
        if ($meetingDate->isTomorrow()) {
            $schedForUi = "Tomorrow @ " . $meetingDate->copy()->format('G:ia');
        }

        return [$meetingDay, $schedForUi];
    }

    public static function getVrForStaffClass()
    {
        return (Session::has('socials-user')) ? 'vr-social-lm' : '';
    }

    public static function canAccessPortfolioView(string $route): bool
    {
        $configKey = ($route === 'insights') ? 'insights_page' : 'customise_page';
        $config = config('portfolio_views.auth.' . $configKey);

        // If no config found, means the config for soft launch has been commented/deleted so this should return true
        if (!$config) {
            return true;
        }

        $allowedEmails = collect($config['emails'])->map(fn ($email) => trim(strtolower($email)))->toArray();
        $allowedEmailDomains = $config['email_domains'];

        $user = Session::get('user');
        if (!$user?->email) {
            return false;
        }
        $email = trim(strtolower($user->email));

        $emailArr = explode('@', $email);
        $emailDomain = trim(strtolower(array_pop($emailArr)));

        if (in_array($email, $allowedEmails)) {
            return true;
        }

        if (in_array($emailDomain, $allowedEmailDomains)) {
            return true;
        }

        return false;
    }

    public static function isBSTorGMT()
    {
        $time = time();
        $tz   = new \DateTimeZone('Europe/London');
        $transistion = $tz->getTransitions($time, $time);
        $abbr = $transistion[0]['abbr'];
        return $abbr;
    }

    public static function encryptGUID($data, $key)
    {
        $ivSize = openssl_cipher_iv_length('aes-256-cbc');
        $iv = openssl_random_pseudo_bytes($ivSize);
        $encrypted = openssl_encrypt($data, 'aes-256-cbc', $key, 0, $iv);
        $str = base64_encode($iv . $encrypted);
        return $str;
    }

    public static function decryptGUID($guid, $key)
    {
        $data = base64_decode($guid);
        $ivSize = openssl_cipher_iv_length('aes-256-cbc');
        $iv = substr($data, 0, $ivSize);
        $encrypted = substr($data, $ivSize);
        return openssl_decrypt($encrypted, 'aes-256-cbc', $key, 0, $iv);
    }

    public static function getPageTitle(string $title = null): string
    {
        $postFix = "";
        if (empty($title)) {
            $controller = static::getCurrentController();
            $method = isset($controller['method']) && match ($controller['method']) {
                'index' => '',
                'show' => 'View',
                'create','edit' => $controller['method'],
                default => ''
            };
            $title = $method . ' ' . ($controller['controller'] ?? '') . " " . $postFix;
            $title = Str::headline(Str::kebab($title));
        }
        
        return str_replace("Liberty Risk Reduce", "", $title) . $postFix;
    }

    public static function getCurrentController()
    {
        // Get the fully qualified class name of the current controller and method
        $action = Route::currentRouteAction();

        // Check if the action is a string (it should be in the format "Controller@method")
        if (is_string($action)) {
            // Split the string by '@' to separate the controller class and method
            [$controller, $method] = explode('@', $action);
            $controllerParts = explode('\\', $controller);
            $controller = last($controllerParts);
            $controller = str_replace('Controllers', '', $controller);
            $controller = str_replace('Controller', '', $controller);

            return [
                'controller' => $controller,
                'method' => $method,
            ];
        }

        return [];
    }

    public static function getGtmUserType(string $loginType) : string {
        return match ($loginType) {
            'underwriter' => 'Underwriter',
            'risk-engineer' => 'Risk Engineer',
            'aspen-user' => 'Aspen User',
            'aspen-branch-admin' => 'Aspen Branch Admin',
            'aspen-branch-user' => 'Aspen Branch User',
            'branch-admin' => 'Branch Admin',
            'branch-user' => 'Branch User',
            'virtual-rooms' => 'Virtual Rooms',
            'external-surveyor-admin' => 'External Surveyor Admin',
            'external-surveyor' => 'External Surveyor',
            'broker-user' => 'Broker',
            'risk-control' => 'Admin',
            default => 'Admin',
        };
    }

    public static function canAccessReMetricsView(): bool
    {
        $roles = config('re_metrics_views.roles_allowed');

        // If no config var set, then everyone can access
        if (!$roles) {
            return true;
        }

        $user = Session::get('user');
        if (!$user?->id || !$user?->email) {
            return false;
        }

        if ($user->type !== 'liberty-user') {
            return false;
        }

        if (in_array($user->role, $roles)) {
            return true;
        }

        return false;
    }

    public static function canPauseNotifications(): bool
    {
        $user = Session::get('user');
        if (!$user?->email) {
            return false;
        }
        $email = trim(strtolower($user->email));

        return in_array($email, config('app.can_pause_notifications'));
    }

    public static function formatSnakeCaseToTitleCase(string $string)
    {
        return ucwords(str_replace('_', ' ', $string));
    }
}
