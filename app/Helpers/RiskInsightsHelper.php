<?php

namespace App\Helpers;

class RiskInsightsHelper
{

    public static function getColorByScore($score) {
        if ($score <= 24) return 'DC6788'; // red
        if ($score <= 49) return 'FF9D43'; // orange
        if ($score <= 69) return 'FDCA41'; // yellow
        if ($score <= 84) return '49C993'; // green
        return '#49B2C9'; // blue
    }

    public static function getColorClass($score) {
        if ($score <= 24) return 'red';
        if ($score <= 49) return 'orange';
        if ($score <= 69) return 'yellow';
        if ($score <= 84) return 'green';
        return 'blue';
    }

    public static function getColorClassByRiskGrading($grade) {
        if ($grade == 'Superior') return 'blue';
        if ($grade == 'Above Average') return 'green';
        if ($grade == 'Average') return 'yellow';
        if ($grade == 'Below Average') return 'orange';
        if ($grade == 'Poor') return 'red';
    }

    public static function getColorHexByScore($score) {
        if ($score <= 24) return '0xDC6788'; // red
        if ($score <= 49) return '0xFF9D43'; // orange
        if ($score <= 69) return '0xFDCA41'; // yellow
        if ($score <= 84) return '0x49C993'; // green
        return '0x49B2C9'; // blue
    }

    public static function getColorClassByGrade($grade) {
        if ($grade <= 24) return 'red'; // red
        if ($grade <= 49) return 'orange'; // orange
        if ($grade <= 69) return 'yellow'; // yellow
        if ($grade <= 84) return 'green'; // green
        return 'blue'; // blue
    }

    public static function getRiskGrading($score) {
        if ($score <= 24) return 'Significantly Below Expectations';
        if ($score <= 49) return 'Below Expectations';
        if ($score <= 69) return 'Meets Expectations';
        if ($score <= 84) return 'Exceeds Expectations';
        return 'Industry Leading';
    }

    public static function isHighlightScore($score1, $score2) {
        return ( $score1 > $score2 ) ? 'highlight' : '';
    }

    public static function isHighlightScoreIfEqual($score1, $score2) {
        return ( $score1 >= $score2 ) ? 'highlight' : '';
    }

    public static function findKeywordsInParam($param, array $keywords): array {
        $param = strtolower($param);
        $matches = [];

        foreach ($keywords as $keyword) {
            if (strpos($param, strtolower($keyword)) !== false) {
                $matches[] = $keyword;
            }
        }

        return $matches;
    }

    public static function canViewDocument($document, $user = null) {
        if (!$user) {
            $user = auth()->user();
        }

        // If user is admin, they can view all documents
        return $user->role === 'admin' || (isset($document['assigned_to_id']) && $document['assigned_to_id'] == $user->id);
    }
}