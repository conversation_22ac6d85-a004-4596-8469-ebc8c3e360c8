<?php

namespace App\Helpers;

class RiskGradingHelper
{
    const MAPPINGS = [
        'not_applicable_not_assessed' => 'Not Applicable / Not Assessed',
        'Superior'                    => 'Industry Leading',
        'Above Average'               => 'Exceeds Expectation',
        'Average'                     => 'Meets Expectations',
        'Below Average'               => 'Below Expectations',
        'Poor'                        => 'Significantly Below Expectations',
    ];

    const DROPDOWN_LABELS = [
        'Superior'                    => 'Industry Leading',
        'Above Average'               => 'Exceeds Expectation',
        'Average'                     => 'Meets Expectations',
        'Below Average'               => 'Below Expectations',
        'Poor'                        => 'Significantly Below Expectations',
        'not_applicable_not_assessed' => 'Not Applicable / Not Assessed',
    ];

    public static function getGradingColorCodes()
    {
        return [
            'Superior'                         => '#fffd38',
            'Above Average'                    => '#0070c0',
            'Average'                          => '#00b050',
            'Below Average'                    => '#fdbf2d',
            'Poor'                             => '#fc0d1b',
            'Industry Leading'                 => '#fffd38',
            'Exceeds Expectation'              => '#0070c0',
            'Meets Expectations'               => '#00b050',
            'Below Expectations'               => '#fdbf2d',
            'Significantly Below Expectations' => '#fc0d1b',
            'Not Applicable / Not Assessed'    => '#dddddd',
            'not_applicable_not_assessed'      => '#dddddd',
        ];
    }

    public static function transformLabel($label)
    {
        return array_key_exists($label, self::MAPPINGS) ? self::MAPPINGS[$label] : $label;
    }

    public static function transformDataSubmission(array $data)
    {
        foreach ($data as $key => $value) {
            $data[$key] = self::transformLabel($value);
        }

        return $data;
    }

    public static function transformDataDescription(array $data)
    {
        $mappings = [
            'not_applicable_not_assessed' => 'Not Applicable / Not Assessed',
            'superior'                    => 'Industry Leading',
            'above_average'               => 'Exceeds Expectation',
            'average'                     => 'Meets Expectations',
            'below_average'               => 'Below Expectations',
            'poor'                        => 'Significantly Below Expectations',
        ];

        foreach ($data as $key => $value) {
            if (array_key_exists($key, $mappings)) {
                $data[$key] = "Description - " . $mappings[$key];
            }
        }

        return $data;
    }
}
