<?php

	class SurveyTest extends TestCase {
		
		private $existing_survey_for_admin = 'surveys/460';
		private $existing_survey_for_external_surveyor = 'surveys/438';
		private $existing_csr_page_for_external_users = 'risk-improvement/form/submission/61e820e3de45cb5b2ad90e43?form=csr&csr_status=completed';
		private $existing_uwr_page_for_external_users = 'risk-improvement/form/submission/61e820e3de45cb5b2ad90e43?form=uwr';

		public function test_visit_survey_page_using_admin_role()
		{
			$this->loginRiskControl();
			$this->call('GET', 'surveys');
			$this->assertResponseStatus(200);
		}

		public function test_visit_existing_survey_page_using_admin_role()
		{
			$this->loginRiskControl();
			$this->call('GET', $this->existing_survey_for_admin);
			$this->assertResponseStatus(200);
		}

		public function test_visit_create_survey_page_using_admin_role()
		{
			$this->loginRiskControl();
			$this->call('GET', 'surveys/create');
			$this->assertResponseStatus(200);
		}

		public function test_visit_create_eternal_survey_login_page()
		{
			$this->loginRiskControl();
			$this->call('GET', 'login/external-surveyor');
			$this->assertResponseStatus(200);
		}

		public function test_visit_survey_page_using_external_surveyor()
		{
			$this->loginExternalSurveyor();
			$this->call('GET', 'surveys');
			$this->assertResponseStatus(200);
		}

		public function test_visit_existing_survey_page_using_external_surveyor()
		{
			$this->loginExternalSurveyor();
			$this->call('GET', $this->existing_survey_for_external_surveyor);
			$this->assertResponseStatus(200);
		}

		public function test_visit_existing_csr_page_using_external_surveyor()
		{
			$this->loginExternalSurveyor();
			$this->call('GET', $this->existing_csr_page_for_external_users);
			$this->assertResponseStatus(200);
		}

		public function test_visit_existing_uwr_page_using_external_surveyor()
		{
			$this->loginExternalSurveyor();
			$this->call('GET', $this->existing_uwr_page_for_external_users);
			$this->assertResponseStatus(200);
		}

		private function loginRiskControl() {
			$this->session(
				['user' =>  
					(object) [
						'id' => 5,
						'first_name' => "Shashi",
						'last_name' => "Saurav",
						'email' => '<EMAIL>',
						'type' => 'liberty-user',
						'role'=> 'admin',
						'login_type' => 'risk-control'
					]
				]
			);
		}

		private function loginExternalSurveyor() {
			$this->session(
				['user' =>  
					(object) [
						'id' => 5,
						'first_name' => "Shashi",
						'last_name' => "Saurav",
						'email' => '<EMAIL>',
						'type' => 'external-surveyor',
						'role'=> 'admin',
						'login_type' => 'risk-engineer',
						'external_survey_company_id' => 1
					]
				]
			);
		}
	}