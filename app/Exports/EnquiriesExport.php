<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithDefaultStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Style;
use PhpOffice\PhpSpreadsheet\Style\Fill;



class EnquiriesExport implements FromArray, ShouldAutoSize, WithDefaultStyles, WithTitle
{

    protected $enquiries;

    public function __construct(array $enquiries)
    {
        $this->enquiries = $enquiries;        
    }

    public function title(): string
    {
        return 'Enquiries';
    }

    public function array(): array
    {
        return $this->enquiries;
    }

    public function headings(): array
    {
        return array_shift($this->enquiries);
    }

    public function defaultStyles(Style $defaultStyle)
    {
        return [
            'font' => [
                'size'   => 15,
                'family' => 'Calibri',
                'bold'   => false,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
            'fill' => [
                'fillType'   => Fill::FILL_SOLID,
            ],
            
        ];
    }
}
