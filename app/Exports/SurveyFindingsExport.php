<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithDefaultStyles;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\Excel\Sheet;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Style;
use PhpOffice\PhpSpreadsheet\Worksheet\PageSetup;

class SurveyFindingsExport implements FromArray, WithTitle, WithEvents, WithHeadings, ShouldAutoSize, WithDefaultStyles
{
    use RegistersEventListeners, Exportable;

    protected $data;

    protected $surveyIds;

    protected $rcas;

    public function __construct($data, $surveyIds, $rcas)
    {
        $this->data      = $data;
        $this->surveyIds = $surveyIds;
        $this->rcas      = $rcas;

        Sheet::macro('styleCells', function (Sheet $sheet, string $cellRange, array $style) {
            $sheet->getDelegate()->getStyle($cellRange)->applyFromArray($style);
        });
    }

    /**
    * @return array
    */
    public function array(): array
    {
        return $this->data;
    }

    public function title(): string
    {
        return 'Survey Findings';
    }

    public function headings(): array
    {
        return $this->data[0];
    }

    public static function afterSheet(AfterSheet $event)
    {
        $event->sheet
            ->getPageSetup()
            ->setOrientation(PageSetup::ORIENTATION_LANDSCAPE);

        $worksheet = $event->sheet->getDelegate();

        $worksheet->freezePaneByColumnAndRow(1, 1);

        foreach ($worksheet->getCoordinates() as $coordinate) {
            $cell = $worksheet->getCell($coordinate);
            $cellVal = $cell->getValue();
            if (strpos($cellVal, '#') !== false) {
                $event->sheet->styleCells($coordinate, [
                    'fill' => [
                        'fillType' => Fill::FILL_SOLID,
                        'color' => ['rgb' => substr($cellVal, 1)]
                    ]
                ]);
                $cell->setValue('');
            }
        }
    }

    public function defaultStyles(Style $defaultStyle)
    {
        return [
            'font' => [
                'size'   => 15,
                'family' => 'Calibri',
                'bold'   => false,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
            'fill' => [
                'fillType'   => Fill::FILL_SOLID,
            ],
        ];
    }
}
