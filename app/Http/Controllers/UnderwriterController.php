<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Models\Api;
class UnderwriterController extends BaseController
{

    //TEMPLATE PATH
    const TEMPLATE_PATH = '/underwriters';

    //ROUTE PREFIX
    const ROUTE_PREFIX = 'rr-appetite/underwriters';

    public function allowForUser(User $user): bool
    {
        return $user->login_type === 'risk-control' || $user?->isRoleAdminOrAccountManager();
    }

    //Index
    public function index(Request $request)
    {
        $page = $request->has('page') ? $request->get('page') : 1;
        $response = json_decode(Api::get('api/v1/liberty-users/all/'.$page.'/99999/underwriter'));

        if($response->response == "success") {
            return view(
                static::TEMPLATE_PATH . '/index',
                array(
                'underwriters' => $response->data
                )
            );
        }
    }
}
