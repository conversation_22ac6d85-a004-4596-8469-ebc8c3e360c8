<?php

namespace App\Http\Controllers;

use App\Models\Api;
use App\Models\FileUpload;
use App\Models\CsrReport;
use App\Models\Helper;
use App\Traits\HelperTrait;
use Exception;


class CacheRebuildController extends BaseController
{
    use HelperTrait;

    public function __construct(FileUpload $fileUpload, CsrReport $csrReport)
    {
        $this->files = $fileUpload;
        $this->csrReport = $csrReport;
    }

    public function getOrgsWithMaxSurveys($maxSurveys)
    {
        $orgUser = json_decode(Api::get('api/v1/organisation/get-users-with-max-surveys/' . $maxSurveys));
        return $orgUser;
    }
    
    public function getRiskGradingDataForOrganisation($org_id)
    {
        $rrTracker = $this->csrReport->getRiskRecommendationTracker($org_id, true);

        $rr_tyre = [];
        $rr_status = [];
        $rr_recommendation = [];
        $rr_tyre_label = [];

        // Risk Recommendation Tyre statistics

        if (isset($rrTracker) && isset($rrTracker['RR_tyre'])) {
            $rr_tyre = $rrTracker['RR_tyre'];
            $rr_tyre = array_count_values($rr_tyre);
            $total = array_sum($rr_tyre);
            $running_percentage = $rr_count = 0;

            foreach ($rr_tyre as $rr => $tyre) {
                if ($rr_count === count($rr_tyre) - 1) {
                    $rr_tyre[$rr] = 100 - $running_percentage;
                } else {
                    $percent = round(($tyre / $total) * 100);
                    $rr_tyre[$rr] = $percent;
                    $running_percentage += $percent;
                }
                ++$rr_count;
            }
        }

        // Risk Recommendation Tyre Label

        if (isset($rrTracker) && isset($rrTracker['RR_tyre_label'])) {
            $labels = (array)$rrTracker['RR_tyre_label'];
            foreach ($labels as $key => $value) {
                if (isset($labels[$key])) {
                    $rr_tyre_label[$key] = $labels[$key];
                }
            }
        }


        // Open Close statistics
        if (isset($rrTracker) && isset($rrTracker['RR_status'])) {
            $status = [];

            foreach ($rrTracker['RR_status'] as $element) {
                $status[$this->trimString($element[2][0]) . "\n" . $element[2][1]][] = $element[1];
            }

            $rr_status = array_map(
                function ($v) {
                    return [
                        'open' => count($v) - array_sum($v),
                        'closed' => array_sum($v),
                    ];
                },
                $status
            );
        }

        uksort(
            $rr_status,
            function ($a, $b) {
                $aa = trim(substr($a, 3));
                $bb = trim(substr($b, 3));

                return ($aa > $bb) ? -1 : 1;
            }
        );

        // survey status statistics
        $possible_statistics = [
            'closed' => 0,
            'open' => 0,
            'greater_than_30' => 0,
            'less_than_30' => 0,
        ];

        $survey_statistics = count($rrTracker) > 0
            ? array_merge($possible_statistics, array_count_values($rrTracker['survey_status']))
            : $possible_statistics;

        // recommendation statistics
        if (isset($rrTracker) && isset($rrTracker['RR_recommendation'])) {
            $rr_recommendation = $rrTracker['RR_recommendation'];
            $rr_recommendation = array_count_values($rr_recommendation);

            ksort($rr_recommendation);

            uasort(
                $rr_recommendation,
                function ($a, $b) {
                    return ($a > $b) ? -1 : 1;
                }
            );

            $rr_recommendation = array_slice($rr_recommendation, 0, 5, true);
            $rrkeys = array_slice($rr_recommendation, 0, 5, true);
        }

        $survey_statistics=[];
        $tracker_count = json_decode(Api::get('api/v1/risk-recommendations/cards?organisation='.$org_id.'&limit=1000'));
        foreach($tracker_count as $key=>$value){
            $survey_statistics[$key]=count($value->data);
        }

        $csrData = $this->csrReport->getCSRReport($org_id);

        $csrData = isset($csrData['data']) ? $csrData['data'] : null;

        $out = [];
        $csrTitles = [];

        foreach ($csrData as $dk => $dv) {
            foreach ($dv as $dvk => $dvv) {
                $dvv = str_replace('SRF', 'CSR', $dvv);
                //$dvv="<span>".$dvv."</span>";

                if ($dvk == 0) {
                    if (strpos($dvv, 'Risk Control') !== false) {
                        $dvv = str_replace('Risk Control', '', $dvv);
                    }

                    $csrTitles[$dvk][$dk] = trim($dvv);
                } else {
                    $out[$dvk][$dk] = $dvv;
                }
            }
        }

        uasort(
            $out,
            function ($a, $b) {
                $ab = preg_match_all('!\d+!', $a[0], $matchesa);
                $bb = preg_match_all('!\d+!', $b[0], $matchesb);

                return ($matchesa[0] > $matchesb[0]) ? -1 : 1;
            }
        );

        $organisationData = array(
            'csrReport'               => $out,
            'csrTitles'               => $csrTitles,
            'rrTracker'               => $survey_statistics,
            'rr_tyre_label'           => $rr_tyre_label,
            'rrTyre'                  => $rr_tyre,
            'rr_status'               => $rr_status,
            'rr_srr_recommendation'   => $rr_recommendation,
            'rr_loss_estimate'        => isset($rrTracker['RR_loss_estimate']) ? ($rrTracker['RR_loss_estimate']) : [],
        );

        return $organisationData;
    }

    
    public function rebuildCache($id)
    {
        $cacheKey="policy-data-for-organisation-{$id}";
        $policyData=$this->getSetCache($cacheKey);
        if(!$policyData){
            $policy_doc_types = json_decode(Api::get('/api/v1/document-policy/all'));
            $documents = json_decode(Api::get('/api/v1/document/policy/' . $id));
            $policy_types     = json_decode(Api::get('api/v1/policy-types/all'));

            if (isset($policy_doc_types->data) && isset($documents)) {
                $policy_docs = $documents;
                $policy_doc_types = $policy_doc_types->data;
            }
            $policy_docs      = isset($policy_docs) ? $policy_docs : [];
            $policy_doc_types = isset($policy_doc_types) ? $policy_doc_types : [];
            $policyData=(object)['policy_docs' => $policy_docs,'policy_doc_types' => $policy_doc_types,'policy_types' => $policy_types->data];
            $this->getSetCache($cacheKey,$policyData);
        }

        $cacheKey="surveys-data-for-organisation-{$id}";
        $surveys=$this->getSetCache($cacheKey);
        if(!$surveys){
            $surveys = json_decode(Api::get('/api/v1/surveys/all/1/10?organisation_id=' . $id));
            $this->getSetCache($cacheKey,$surveys);
        }

        $cacheKey="account-documents-for-organisation-{$id}";
        $accountDocuments=$this->getSetCache($cacheKey);
        if(!$accountDocuments){
            $accountDocuments = json_decode(Api::get('/api/v1/account-documents/all?organisation_id=' . $id))->data;
            foreach ($accountDocuments as &$accountDocument) {
                $cloudpath = $accountDocument->organisation_id . '/' . $accountDocument->cloudname . '/' . $accountDocument->filename;
                $accountDocument->download = $this->files->link($cloudpath, '2 hours');
            }
            $this->getSetCache($cacheKey,$accountDocuments);
        }

        $cacheKey="organisation-report-for-organisation-{$id}";
        $organisationReports=$this->getSetCache($cacheKey);
        if(!$organisationReports){
            $organisationReports = json_decode(Api::get('/api/v1/organisation/' . $id . '/reports/all'))->data;
            foreach ($organisationReports as &$organisationReport) {
                $cloudpath = $organisationReport->organisation_id . '/' . $organisationReport->cloudname . '/' . $organisationReport->filename;
                $organisationReport->download = $this->files->link($cloudpath, '2 hours');
            }
            $this->getSetCache($cacheKey,$organisationReports);
        }

        $cacheKey="risk-grading-overview-for-organisation-{$id}";
        $riskGradingOverview=$this->getSetCache($cacheKey);
        if(!$riskGradingOverview){
            $riskGradingOverview = json_decode(Api::get('/api/v1/standard-risk/attributes/overview/' . $id));
            $this->getSetCache($cacheKey,$riskGradingOverview);
        }

        $cacheKey="organisation-rhs-details-for-organisation-{$id}";
        $organisationDetails=$this->getSetCache($cacheKey);
        if(!$organisationDetails){
            $organisationDetails = json_decode(Api::get('/api/v1/rhs-organisations/name/' . $id));
            $this->getSetCache($cacheKey,$organisationDetails);
        }

        $cacheKey="organisation-documents-for-organisation-{$id}";
        $documents=$this->getSetCache($cacheKey);
        if(!$documents){
            $documents = json_decode(Api::get('/api/v1/document/organisation/' . $id));
            $this->getSetCache($cacheKey,$documents);
        }

        $cacheKey="organisation-links-for-organisation-{$id}";
        $links=$this->getSetCache($cacheKey);
        if(!$links){
            $links = json_decode(Api::get('api/v1/link/organisation/' . $id));
            $this->getSetCache($cacheKey,$links);
        }

        $cacheKey="organisation-notes-for-organisation-{$id}";
        $notes=$this->getSetCache($cacheKey);
        if(!$notes){
            $notes = json_decode(Api::get('api/v1/organisation/' . $id . '/notes'));
            $this->getSetCache($cacheKey,$notes);
        }

        $cacheKey="organisation-claims-for-organisation-{$id}";
        $claims=$this->getSetCache($cacheKey);
        if(!$claims){
            $claims = json_decode(Api::get('api/v1/organisation/' . $id . '/claims'));
            $this->getSetCache($cacheKey,$claims);
        }

        $cacheKey="organisation-contacts-for-organisation-{$id}";
        $contacts=$this->getSetCache($cacheKey);
        if(!$contacts){
            $contacts = json_decode(Api::get('api/v1/organisation/' . $id . '/contacts'));
            $this->getSetCache($cacheKey,$contacts);
        }

        $cacheKey="organisation-reports-second-for-organisation-{$id}";
        $reports=$this->getSetCache($cacheKey);
        if(!$reports){
            $reports = json_decode(Api::get('api/v1/document/report/organisation/' . $id));
            $this->getSetCache($cacheKey,$reports);
        }

        $cacheKey="organisation-requests-for-organisation-{$id}";
        $requests=$this->getSetCache($cacheKey);
        if(!$requests){
            $requests = json_decode(Api::get('/api/v1/aspen/organisation/' . $id))->data;
            $this->getSetCache($cacheKey,$requests);
        }

        $cacheKey="organisation-aspen-documents-for-organisation-{$id}";
        $aspenDocuments=$this->getSetCache($cacheKey);
        if(!$aspenDocuments){
            $aspenDocuments = json_decode(Api::get('/api/v1/aspen-documents/organisation/' . $id))->data;
            $this->getSetCache($cacheKey,$aspenDocuments);
        }

        $cacheKey="organisation-cms-all-products-organisation-{$id}";
        $allproducts=$this->getSetCache($cacheKey);
        if(!$allproducts){
            $allproducts = $this->getCmsProducts();
            $this->getSetCache($cacheKey,$allproducts);
        }

        $cacheKey="organisation-details-for-organisation-{$id}";
        $organisation=$this->getSetCache($cacheKey);
        if(!$organisation){
            $organisation = json_decode(Api::get('api/v1/organisation/' . $id));
            if (!is_null($organisation->data->logo) && $organisation->data->logo != '' && $organisation->data->logo != 'none') {
                $organisation->data->image_url = $this->files->link($organisation->data->logo);
            } else {
                $organisation->data->image_url = '/img/dummy/logo-placeholder.png';
            }

            $organisation_policies = [];

            if (isset($organisation->data->policy_numbers)) {
                $organisationPolicies = array_map(
                    function ($policies) {
                        return $policies->type;
                    },
                    $organisation->data->policy_numbers
                );

                $organisation_policies = array_map(
                    function ($policies) {
                        return strtolower($policies->name);
                    },
                    $organisationPolicies
                );
            }

            if (isset($organisation->data->products) && !empty($organisation->data->products)) {
                foreach ($organisation->data->products as $product) {
                    $products = array_filter(
                        $allproducts,
                        function ($prod) use ($product) {
                            return $product->slug == $prod->slug;
                        }
                    );
                    $product->name = array_values($products)[0]->name;
                }

                $organisation->data->display_products = array_filter(
                    $organisation->data->products,
                    function ($prod) use ($organisation_policies) {
                        return !in_array($prod->slug, $organisation_policies);
                    }
                );
            }

            $options = $this->getConstants('gradingColorOptions');
            $bgcolor = $this->getConstants('bgColorOptions');

            foreach ($options as $key => $value) {
                if ($organisation->data->risk_grading == $value) {
                    $organisation->data->risk_grading = (object) ['code' => $key, 'color' => $value, 'background' => $bgcolor[$value]];
                }
            }

            $otherLinesOfBusiness=[];
            $organisation = isset($organisation->data) ? $organisation->data : null;
            if (isset($organisation->products) && count($organisation->products) > 0) {
                foreach ($organisation->products as $product) {
                    if (!empty($product->loss_ratio)) {
                        $otherLinesOfBusiness[] = [
                            'name' => $product->name,
                            'loss_ratio' => $product->loss_ratio
                        ];
                    }
                }
            }

            $organisation->otherLinesOfBusiness=$otherLinesOfBusiness;
            $this->getSetCache($cacheKey,$organisation);
        }

        $cacheKey="organisation-risk-grading-data-for-organisation-{$id}";
        $riskGradingData=$this->getSetCache($cacheKey);
        if(!$riskGradingData){
            $riskGradingData=$this->getRiskGradingDataForOrganisation($id);
            $this->getSetCache($cacheKey,$riskGradingData);
        }
    }

    public function getCmsProducts($sector = null, $subsector = null)
    {
        $relatedproducts = $this->getAllProducts();

        $products = array_map(
            function ($product) {
                return (object)['slug' => $product->slug, 'name' => htmlspecialchars_decode($product->name)];
            },
            $relatedproducts
        );

        $products = Helper::getUniqueObjects($products);
        usort(
            $products,
            function ($a, $b) {
                return strcmp($a->name, $b->name);
            }
        );

        return $products;
    }

    public function getAllProducts()
    {
        $cms_products = 'api/v1/cms-products/all-products'; //cached response
        $cms_products = json_decode(Api::get($cms_products));
        return  Helper::getUniqueObjects($cms_products);
    }

    public function rebuildRiskGradingCache($id)
    {
        //Force rebuild risk-grading-overview
        $cacheKey="risk-grading-overview-for-organisation-{$id}";
        $riskGradingOverview = json_decode(Api::get('/api/v1/standard-risk/attributes/overview/' . $id));
        $riskGradingOverview=$this->permanentCache($cacheKey, $riskGradingOverview);
    }
}
