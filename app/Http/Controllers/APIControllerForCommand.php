<?php

namespace App\Http\Controllers;

use App\Models\Api;

class APIControllerForCommand extends BaseController
{
    public function getTrackerDataForCleanUP($flag)
    {
        $response = json_decode(Api::get("api/v1/helper-api/survey-details-for-clean-up/{$flag}"));
        return $response;
    }

    public function getData(string $apiEndpoint)
    {
        $data = json_decode(Api::get($apiEndpoint));
        return $data;
    }

    public function saveDataInLogTable($maindata){
        $allData=[];
        foreach ($maindata as $survey) {
            foreach($survey->attributes as $key=>$value){
                $dbData=(object)[];
                $dbData->org_details_id=$survey->id;
                $dbData->survey_id=$survey->survey_id;
                $dbData->submission_id=$survey->submission_id;
                $dbData->grading_id=$key;
                $dbData->is_closed=$value;
                $allData[]=$dbData;
            }
        }
        $response = (Api::post('api/v1/helper-api/insert-srg-close-log',$allData));
        return $response;
    }

    public function getDataInLogTable(){
        $response = json_decode(Api::get('api/v1/helper-api/srg/get-srg-close-log'));
        return $response->data;
    }

    public function registerMessage($survey)
    {
        $data=$survey->input;
        foreach($survey->attributes as $attribute){
            $data['rr_ref']=$attribute.'message';
            $response=json_decode(Api::post('/api/v1/messaging/send', $data));
        }
        return $data;
    }

    public function closeRiskRecommendation($survey)
    {
        $data=[];
        $data['survey_id']=$survey->survey_id;
        $data['from_cron']=true;
        foreach($survey->attributes as $attribute){
            $data[$attribute.'issue_closed']=true;
        }
        $response = json_decode(Api::post('api/v1/survey-submission/close/'.$survey->submission_id, $data));
        return $data;
    }

    public function getGradingAttributes($survey)
    {
        $dataEndpoint = '/api/v1/surveys/' . $survey->survey_id;
        $survey_data = $this->getData($dataEndpoint)->data;
        
        if(!$survey_data->submissions){
            return null;
        }

        $dataEndpoint = 'api/v1/survey-submission/' . $survey_data->submissions->_id;
        $survey_submission = $this->getData($dataEndpoint);

        $submission = json_decode($survey_submission->data);

        $dataEndpoint = 'api/v1/risk-improvement/form/' . $submission->form_id;
        $response = $this->getData($dataEndpoint);

        if ($response->response == 'success') {
            $form = json_decode($response->data, true);
            $rr_data = [];
            if (isset($form) && isset($form['fields'])) {
                foreach ($form['fields'] as $field_types) {
                    foreach ($field_types as $field_type => $field_attr) {

                        if ($field_type == 'risk_recommendation') {
                            foreach ($field_attr as $element) {
                                if ($element['name'] == 'name') {
                                    array_push($rr_data, $element['value']);
                                }
                            }
                        }
                    }
                }
            }
            $data=[];
            foreach ($rr_data as $risk_rec) {
                for ($i = 1; $i <= 15; $i++) {
                    $part_title = $risk_rec . '_' . $i . '_';
                    $classification_title = $part_title . 'classification';
                    $description = $part_title . 'description';
                    $closed = $part_title . 'issue_closed';

                    if (isset($submission->$description) && isset($submission->$classification_title) && ($submission->$classification_title != '' || $submission->$description != '')) {
                        $data[$part_title]=isset($submission->$closed);
                    }
                }
            }

            $grading_data=(object)[];
            $grading_data->data=$data;
            $grading_data->submission_id=$survey_data->submissions->_id;
            return $grading_data;
        }
    }
}
