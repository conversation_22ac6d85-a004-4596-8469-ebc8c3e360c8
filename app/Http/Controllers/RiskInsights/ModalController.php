<?php

namespace App\Http\Controllers\RiskInsights;

use App\Http\Controllers\Controller;
use App\Models\Api;
use App\Services\RiskInsightsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

class ModalController extends Controller
{
    public function __construct(private RiskInsightsService $riskInsightsService)
    {
    }

    public function getModalContent(Request $request)
    {
        // TODO: check if request has documentId, this is to load the review modal instead of the upload modal
        $step = (int) $request->input('step');
        return $this->getUploadModalContentByStep($step, $request);
    }

     public function getReviewModalContent(Request $request)
    {
        $step = (int) $request->input('step');
        return $this->getReportReviewModalContent($step, $request);
    }

    public function getUploadModalContentByStep($step, Request $request)
    {
        switch ($step) {
            case 2:
                $organizationId = $request->input('organizationId');
                $locationIds = $request->input('locationIds');

                $organization = json_decode(Api::get('api/v1/organisation/'.$organizationId))->data;
                $allLocations = json_decode(Api::get('api/v1/location/'.$organizationId))->data;
                $locations = [];
                foreach ($allLocations as $location) {
                    if (in_array($location->id, $locationIds)) {
                        $locations[] = $location;
                    }
                }
                
                return view('risk-insights.components.modals.contents.upload-risk-report', compact('organization', 'locations'));

            case 3:
                $skipHumanInTheLoop = $request->boolean('skip_human_in_the_loop');
                if ($skipHumanInTheLoop) {
                    $documentId = $request->input('document_id');
                    $reportResult = $this->riskInsightsService->getRiskReportResult($documentId);

                    if (isset($reportResult->status) && $reportResult->status === 401) {
                        abort(401);
                    }

                    $reportResult = $reportResult->data;

                    // if document has do_skip_human_in_the_loop
                    // load new modal confirming the data
                    if ($reportResult->document->do_skip_human_in_the_loop) {
                        return view('risk-insights.components.modals.contents.nhitl-report-data-complete', compact('reportResult'));
                    }
                }
                return view('risk-insights.components.modals.contents.file-processing-prompt');

            default: // Step 1
                $organizations = json_decode(Api::get('/api/v1/organisation/options'));

                $locations = [];
                $orgLocations = [];
                $selectedLocations = null;
                if ($request->input('organizationId') !== null) {
                    $organizationId = $request->input('organizationId');
                    $orgLocations = json_decode(Api::get('api/v1/location/'.$organizationId))->data;
                    $selectedLocations = $request->input('locationIds');
                }

                return view('risk-insights.components.modals.contents.organization-select', compact('organizations', 'locations', 'orgLocations', 'selectedLocations'));
        }
    }

    public function getOrganizationLocations(Request $request)
    {
        $organizationId = $request->input('organizationId');
        $organizationLocations = json_decode(Api::get('api/v1/location/'.$organizationId))->data;
        return response()->json($organizationLocations);
    }

    public function processFilesForRAFA(Request $request)
    {
        $user = Session::get('user');
        $data = $request->all();
        $data['uploadedBy'] = $user->email;
        $data['updatedBy'] = $user->email;

        $data['selectedLocationIds'] = explode(',', $data['selectedLocationIds']);

        // Call API for Upload
        $response = Api::post('/api/v1/risk-insights/process-files-for-rafa', $data);

        $responseArr = json_decode($response, true);
        \Log::info('Modal Response:', $responseArr);
        return $response;
    }

    public function getReportReviewModalContent($step, Request $request)
    {
        switch ($step) {
            case 2:
                $documentId = $request->input('documentId');
                $reportResult = $this->riskInsightsService->getRiskReportResult($documentId);

                if (isset($reportResult->status) && $reportResult->status === 401) {
                    abort(401);
                }

                $reportResult = $reportResult->data;
                return view('risk-insights.components.modals.contents.review-risk-report', compact('reportResult'));
            default:
                $documentId = $request->input('documentId');
                $reportResult = $this->riskInsightsService->getRiskReportResult($documentId);

                if (isset($reportResult->status) && $reportResult->status === 401) {
                    abort(401);
                }

                $reportResult = $reportResult->data;
                // if document has do_skip_human_in_the_loop
                // load new modal confirming the data
                if ($reportResult->document->do_skip_human_in_the_loop) {
                    return view('risk-insights.components.modals.contents.nhitl-report-data-complete', compact('reportResult'));
                }
                
                return view('risk-insights.components.modals.contents.review-proposed-weightage', compact('reportResult'));
        }
    }

    public function pollUpdates(Request $request)
    {
        return Api::get('/api/v1/risk-insights/poll', $request->all());
    }

    public function completeReview(Request $request)
    {
        $documentId = $request->input('documentId');
        $response = $this->riskInsightsService->updateDocumentStatus($documentId, $request->input('userId'));
        return $response;
    }

    public function removeDocumentNHITLTag(Request $request)
    {
        $documentId = $request->input('documentId');
        $response = $this->riskInsightsService->removeDocumentNHITLTag($documentId);
        return $response;
    }

    public function submitReviewWeightage(Request $request)
    {
        if ($request->has('documentId') && $request->has('weightages')) {
            $documentId = $request->input('documentId');
            $weightages = $request->input('weightages');

            // Submit the weightages to the API
            $response = json_decode(Api::post('/api/v1/risk-insights/submit-review-weightage', [
                'documentId' => $documentId,
                'weightages' => $weightages,
            ]));

            return $response;
        }

        return response()->json(['error' => 'No weightages provided'], 400);
    }

    public function getPortfolioImpact(Request $request)
    {
        $documentId = $request->input('documentId');
        $riskReport = $this->riskInsightsService->getRiskReportResult($documentId);
        $riskReport = $riskReport->data;
        return view('risk-insights.components.modals.contents.portfolio-impact', compact('riskReport'));
    }
}