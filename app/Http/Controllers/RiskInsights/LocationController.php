<?php

namespace App\Http\Controllers\RiskInsights;

use App\Helpers\Helpers;
use App\Helpers\RiskInsightsHelper;
use App\Http\Controllers\Controller;
use App\Imports\OrgLocationImport;
use App\Models\Api;
use App\Services\RiskInsightsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Response;
use Maatwebsite\Excel\Facades\Excel;
class LocationController extends Controller
{
    public function __construct(private RiskInsightsService $riskInsightsService)
    {
    }

    public function index(Request $request)
    {
        $risk = $request->get('name');
        $filters = $this->riskInsightsService->getFilters();
        $organisationBenchmarking = $this->riskInsightsService->getOrganisationBenchmarking();

        $organisationId = $request->get('organisation_id');
        $locations = $this->riskInsightsService->getDashboardLocation([
            'organisation_id' => $organisationId
        ]);

        return view('risk-insights.location', compact('risk', 'organisationBenchmarking', 'locations', 'organisationId', 'filters'));
    }

    public function viewReport(Request $request)
    {
        $risk = $request->get('name');
        $documentId = $request->get('documentId') ?? 1;
        $organisationBenchmarking = $this->riskInsightsService->getOrganisationBenchmarking();
        $dashboardLocation = $this->riskInsightsService->getDashboardLocation();
        $response = $this->riskInsightsService->getRiskReportResult($documentId);

        if (isset($response->status) && $response->status === 401) {
            abort(401);
        }
        
        [
            'currentLocation' => $locationData,
            'reportData' => $riskReportData,
            'documentData' => $documentData,
            'canEdit' => $canEdit
        ] = $this->getFormattedRiskReportLocation($response->data, $risk);

        return view('risk-insights.view-report', compact(
            'risk', 
            'organisationBenchmarking', 
            'dashboardLocation', 
            'documentData', 
            'riskReportData', 
            'locationData',
            'canEdit',
        ));
    }

    protected function getFormattedRiskReportLocation($riskReportResult, $targetLocation = 'ABC Street, SW1 1TL, London')
    {
        
        $metadata = $riskReportResult->auditLogs->metadata_update;

        $currentLocation = array_values(array_filter(
            $metadata->locations ?? [],
            fn($loc) => $loc->location === $targetLocation
        ))[0] ?? null;

        $newFormat = $currentLocation;
        // 
        $formattedLocationData = [
            'client_name' => $metadata->client_name,
            'document_id' => $currentLocation->document_name,
            'location_name' => $currentLocation->location,
            'commentary' => $currentLocation->executive_summary,
            'survey_date' => $currentLocation->survey_date,
        ];

        $formattedRiskGrading = [];
        $colorHelper = new RiskInsightsHelper();

        foreach ($currentLocation->risk_grading as $index => $risk) {
            $riskData = [
                'category' => Helpers::formatSnakeCaseToTitleCase($index),
                'max_score' => $risk->max_score,
                'score' => $risk->score,
                'percentage' => $risk->percentage,
                'color_class' => $colorHelper->getColorClass($risk->score),
            ];

            $newFormat->risk_grading->$index->category = Helpers::formatSnakeCaseToTitleCase($index);
            $newFormat->risk_grading->$index->color_class = $colorHelper->getColorClass($risk->score);

            $riskData['details'] = [];
            foreach ($risk->details as $subIndex => $subRisk) {
                $riskData['details'][] = [
                    'category' => Helpers::formatSnakeCaseToTitleCase($subIndex),
                    'max_score' => $subRisk->max_score,
                    'score' => $subRisk->score,
                    'commentary' => $subRisk->commentaries,
                    'color_class' => $colorHelper->getColorClass($subRisk->score),
                ];

                $newFormat->risk_grading->$index->details->$subIndex->category = Helpers::formatSnakeCaseToTitleCase($subIndex);
                $newFormat->risk_grading->$index->details->$subIndex->color_class = $colorHelper->getColorClass($subRisk->score);
            }

            $formattedRiskGrading[] = $riskData;
        }

        $formattedLocationData['risk_grading'] = $formattedRiskGrading;

        $user = Session::get('user');
        $canEdit = $riskReportResult->document->status !== 'completed' && $riskReportResult->document->assigned_to_user === $user->id;

        return [
            'reportData' => $metadata,
            'currentLocation' => $newFormat,
            'documentData' => $riskReportResult->document,
            'canEdit' => $canEdit,
        ];
    }

    public function updateRiskReportLocation(Request $request)
    {
        $user = Session::get('user');
        $data = $request->all();
        $data['uploadedBy'] = $user->email;
        $data['updatedBy'] = $user->email;

        $response = Api::post('/api/v1/risk-insights/update-risk-report', $data);

        \Log::info('API response', ['response' => $response]);

        return response()->json($response);

        // $riskReportLocation = $this->getFormattedRiskReportLocation($request->get('documentId'));
        // $locationData = $riskReportLocation['currentLocation'];
        // $riskReportData = $riskReportLocation['reportData'];
        // return view('risk-insights.view-report', compact('risk', 'organisationBenchmarking', 'dashboardLocation', 'riskReportData', 'locationData'));
    }

    public function viewReportNarratives(Request $request)
    {
        $locationData = Excel::toArray(new OrgLocationImport, storage_path('risk-insights-narrative-demo.xlsx'));
        $firstSheet = $locationData[0] ?? [];
        $firstRow = $firstSheet[0] ?? [];

        $locationReportData = $this->formatNarrativeData($firstSheet);

        return view('risk-insights.view-report-narratives-demo', compact('locationReportData'));
    }

    public function downloadFullSurveyReport()
    {
        $storage = storage_path('Tesco OCP Paragon Daventry Frozen DC - May 2023 - FINAL.pdf');

        $headers = [
            'Content-Type: application/pdf',
        ];

        return Response::download($storage, 'Tesco OCP Paragon Daventry Frozen DC - May 2023 - FINAL.pdf', $headers);
    }

    private function formatNarrativeData($narrativeData)
    {
        $formattedData = [];
        $currentAttribute = null;
        foreach ($narrativeData as $row) {
            if ($row['client_name']) {
                $formattedData['client_name'] = $row['client_name'];
                $formattedData['location_name'] = $row['location_name'];
                $formattedData['postcode'] = $row['postcode'];
            }


            if ($row['attribute_name']) {
                $currentAttribute = $row['attribute_name'];
                $formattedData['attribute_grading'][$currentAttribute] = [
                    'attribute_name' => $currentAttribute,
                    'attribute_grading' => $row['attribute_risk_grading'],
                    'sub_attribute_grading' => [],
                ];
                $formattedData['attribute_grading'][$currentAttribute]['sub_attribute_grading'][] = [
                    'sub_attribute_name' => $row['sub_attribute_name'],
                    'narrative' => $row['narrative'],
                    'risk_grading' => $row['risk_grading'],
                ];
            } else {
                if ($row['sub_attribute_name'] == 'Executive Summary') {
                    $formattedData['executive_summary'] = $row['narrative'];
                } else {
                    $formattedData['attribute_grading'][$currentAttribute]['sub_attribute_grading'][] = [
                        'sub_attribute_name' => $row['sub_attribute_name'],
                        'narrative' => $row['narrative'],
                        'risk_grading' => $row['risk_grading'],
                    ];
                }
            }
        }

        return $formattedData;
    }
}
