<?php

namespace App\Http\Controllers\RiskInsights;

use App\Http\Controllers\Controller;
use App\Services\RiskInsightsService;
use Illuminate\Http\Request;

class RiskGradingController extends Controller
{
    public function __construct(private RiskInsightsService $riskInsightsService)
    {
    }

    public function index(Request $request)
    {
        $attributeId = $request->get('attributeId') ?? 1;
        $risk = $request->get('risk');
        $riskDashboard = $this->riskInsightsService->getRiskDashboard();
        $organisationBenchmarking = $this->riskInsightsService->getOrganisationBenchmarking();
        $organisationRisk = $this->riskInsightsService->getAttributeScores($attributeId);
        return view('risk-insights.risk-grading', compact(
            'risk',
            'riskDashboard',
            'organisationBenchmarking',
            'organisationRisk'
        ));
    }
}
