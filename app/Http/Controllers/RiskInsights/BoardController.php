<?php

namespace App\Http\Controllers\RiskInsights;

use App\Http\Controllers\Controller;
use App\Models\Api;
use Illuminate\Http\Request;

class BoardController extends Controller
{
    public function index(string $type)
    {
        if (!$type || ($type !== 'risk-engineer' && $type !== 'underwriter')) {
            abort(404);
        }

        $riAuditLogs = json_decode(Api::get('api/v1/risk-insights/documents-for-board'));
        
        // Process the API response to format data for each column
        $processingDocuments = $this->formatDocumentsForColumn($riAuditLogs->open ?? null, 'processing');
        $underReviewDocuments = $this->formatDocumentsForColumn($riAuditLogs->underReview ?? null, 'reviewing');
        $completedDocuments = $this->formatDocumentsForColumn($riAuditLogs->completed ?? null, 'completed');

        $options = $type === 'risk-engineer' ? 
            json_decode(Api::get('api/v1/organisation-contact/getAllRiskEngineerOptions')) : json_decode(Api::get('api/v1/organisation-contact/getAllUnderwriterOptions'));

        $options = $options->data ?? [];

        return view('risk-insights.document-request-board', compact(
            'type', 
            'options', 
            'processingDocuments', 
            'underReviewDocuments', 
            'completedDocuments'
        ));
    }

    public function documentAssignment(Request $request)
    {
        if (!$request->document_id || !$request->assigned_to) {
            return response()->json([
                'success' => false,
                'message' => 'Document ID and assigned to are required'
            ]);
        }

        $documentId = $request->document_id;
        $assignedTo = $request->assigned_to;

        $response = json_decode(Api::post('api/v1/risk-insights/document-assignment', [
            'document_id' => $documentId,
            'assigned_to' => $assignedTo
        ]));

        if ($request->ajax()) {
            return response()->json($response);
        } else {
            return redirect()->back()->with('success', 'Document assigned successfully');
        }
    }
    

    /**
     * Format documents from API response for board column display
     *
     * @param object|null $columnData The column data from API (open, underReview, completed)
     * @param string $status The status to assign to documents
     * @return array Formatted documents array
     */
    private function formatDocumentsForColumn($columnData, string $status): array
    {
        $formattedDocuments = [];
        
        if (!$columnData || !isset($columnData->documents)) {
            return $formattedDocuments;
        }

        foreach ($columnData->documents as $document) {
            if (isset($document->audit_log->metadata_update->locations) && is_array($document->audit_log->metadata_update->locations)) {
                foreach ($document->audit_log->metadata_update->locations as $index => $location) {
                    $formattedDoc = [
                        'id' => $document->id,
                        'location_index' => $index,
                        'document_name' => $location->document_name ?? 'N/A',
                        'postcode' => $location->postcode ?? 'N/A',
                        'location' => $location->location ?? 'N/A',
                        'client_name' => $document->audit_log->metadata_update->client_name ?? 'N/A',
                        'broker_name' => $location->broker_name ?? 'N/A',
                        'surveyor_name' => $location->surveyor_name ?? 'N/A',
                        'surveyor_organisation' => $location->surveyor_organisation ?? 'N/A',
                        'survey_date' => $location->survey_date ?? null,
                        'created_at' => $document->audit_log->created_at ?? null, // Not available in current API response
                        'assigned_to_id' => $document->assigned_to_user_id ?? null,
                        'assigned_to' => $document->assigned_to_user ?? null,
                        'request_id' => $document->audit_log->metadata_update->request_id ?? 'N/A',
                        'sector' => $document->audit_log->metadata_update->sector ?? 'N/A',
                        'subsector' => $document->audit_log->metadata_update->subsector ?? 'N/A',
                        'executive_summary' => $location->executive_summary ?? 'N/A',
                        'unique_id' => $document->id . '_' . $index,
                        'status' => $status,
                    ];
                    
                    $formattedDocuments[] = $formattedDoc;
                }
            }
        }

        return $formattedDocuments;
    }
}