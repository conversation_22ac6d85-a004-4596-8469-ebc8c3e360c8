<?php

namespace App\Http\Controllers\RiskInsights;

use App\Http\Controllers\Controller;
use App\Models\FileUpload;
use App\Services\RiskInsightsService;
use Carbon\Carbon;
use Illuminate\Http\Request;

class BenchmarkController extends Controller
{
    public function __construct(
        private RiskInsightsService $riskInsightsService,
        private FileUpload $file
    ) {}

    public function index(Request $request)
    {
        $risk = $request->get('risk');
        $organisationId           = $request->organisation_id;
        $dashboardCompany         = $this->riskInsightsService->getDashboardCompany(['organisation_id' => $organisationId]);
        $benchmarking             = $this->riskInsightsService->getBenchMarking();
        $dashboard                = $this->riskInsightsService->getDashboardData(['sector_id' => $dashboardCompany->data->sector->id, 'sub_sector_id' => $dashboardCompany->data->sub_sector_id]);
        $portfolioDistribution    = $this->riskInsightsService->getPortfolioDistribution();
        $organisationBenchmarking = $this->riskInsightsService->getOrganisationBenchmarking(['organisation_id' => $organisationId]);

        if ($dashboardCompany->data) {
            $dashboardCompany->data->image_url = $dashboardCompany->data->logo ? $this->file->link($dashboardCompany->data->logo) : '/img/risk-league/default-logo.png';
            $dashboardCompany->data->renewal_date = Carbon::parse($dashboardCompany->data->expiry_date_of_cover)->format('d/m/Y');
            $dashboardCompany->data->broker_logo = !empty($dashboardCompany->data->broker->image) ? $this->file->link($dashboardCompany->data->broker->image) : '/img/risk-league/default-logo.png';
            $dashboardCompany->data->lead_insurer_logo = !empty($dashboardCompany->data->lead_insurer->image) ? $this->file->link($dashboardCompany->data->lead_insurer->image) : '/img/risk-league/default-logo.png';
        }

        $dashboardCompany = $dashboardCompany->data;
        $locations        = $this->riskInsightsService->getDashboardLocation(['organisation_id' => $organisationId]);

        return view(
            'risk-insights.benchmark',
            compact('risk', 'dashboardCompany', 'benchmarking', 'dashboard', 'portfolioDistribution', 'organisationBenchmarking', 'locations', 'organisationId')
        );
    }
}
