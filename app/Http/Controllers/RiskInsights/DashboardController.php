<?php

namespace App\Http\Controllers\RiskInsights;

use App\Http\Controllers\Controller;
use App\Models\Api;
use App\Services\RiskInsightsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

class DashboardController extends Controller
{
    public function __construct(private RiskInsightsService $riskInsightsService)
    {
    }

    public function index(Request $request)
    {
        if ($request->has('reset')) {
            Session::put('org-animated', false);
            Session::put('org-added', false);

            return redirect()->route('risk-insights.index');
        }

        $documentId = $_GET['documentId'] ?? null;

        if ($documentId) {
            $reportData = $this->riskInsightsService->getRiskReportResult($documentId);

            if (isset($reportData->status) && $reportData->status === 401) {
                abort(401);
            }

            $reportData = $reportData->data;
            $user = Session::get('user');
            $assignedUser = $reportData->document->assigned_to_user;
            
            if (!$assignedUser) {
                $this->riskInsightsService->updateDocumentStatus($documentId, $user->id);
            }
        }

        $sectorId = $request->sectorId;
        $subSector = $request->sub_sector;

        $filters = $this->riskInsightsService->getFilters();
        $dashboard = $this->riskInsightsService->getDashboardData(['sector_id' => $sectorId, 'sub_sector_id' => $subSector]);
        $benchmarking = $this->riskInsightsService->getBenchMarking(['sector_id' => $sectorId, 'sub_sector_id' => $subSector]);

        $portfolioDistribution = $this->riskInsightsService->getPortfolioDistribution(['sector_id' => $sectorId, 'sub_sector_id' => $subSector]);
        
        $forecast = $_GET['forecast'] ?? false;

        // check if session value "org-added" exists
        $orgAdded = Session::get('org-added', false);
        $orgAnimated = Session::get('org-animated', false);

        // Ensure we have the correct data structure for the blade template
        // The blade expects $benchmarking->data to be an array of organizations
        $organisationsArray = isset($benchmarking->data->organisations)
            ? $benchmarking->data->organisations
            : $benchmarking->data;

        $portfolioDistributionArray = isset($portfolioDistribution->data->sub_sector->chart_data)
            ? $portfolioDistribution->data->sub_sector->chart_data
            : $portfolioDistribution->data;

        if (!$orgAdded) {
            // remove first row from benchmarking
            if (is_array($organisationsArray) && count($organisationsArray) > 0) {
                array_shift($organisationsArray);
            }

            if (is_array($portfolioDistributionArray) && count($portfolioDistributionArray) > 0) {
                array_shift($portfolioDistributionArray);
            }
        }

        // Set the data back to the expected structure for the blade
        $benchmarking->data = $organisationsArray;
        $portfolioDistribution->data->sub_sector->chart_data = $portfolioDistributionArray;

        return view('risk-insights.dashboard', compact(
            'dashboard',
            'benchmarking',
            'portfolioDistribution',
            'forecast',
            'filters',
            'orgAdded',
            'orgAnimated'
        ));
    }

    public function getReviewRiskReport()
    {
        $dashboard_after_json = file_get_contents(public_path('templates-league/data/after/dashboard.json'));
        $dashboard_after = json_decode($dashboard_after_json, true);

        $benchmarking_after_json = file_get_contents(public_path('templates-league/data/after/dashboard_benchmarking.json'));
        $benchmarking_after = json_decode($benchmarking_after_json, true);

        $new_score = $dashboard_after['current_score']['portfolio_score'];

        $response = [
            'chart_data' => $dashboard_after['current_score']['chart_data'],
            'portfolio_score' => $new_score,
            'risk_grading' => $dashboard_after['risk_grading'],
            'loss_estimates' => $dashboard_after['loss_estimates'],
            'benchmarking' => $benchmarking_after['organisations']
        ];

        return json_encode($response);
    }

    public function getOrganizations()
    {
        $organizations = json_decode(Api::get('/api/v1/organisation/all'))->data;
        return response()->json($organizations);
    }

    public function getOrganizationLocations(Request $request)
    {
        $organizationId = $request->input('organizationId');
        $organizationLocations = json_decode(Api::get('api/v1/location/'.$organizationId))->data;
        return response()->json($organizationLocations);
    }

    public function getOrganizationDashboard($organisationId, Request $request)
    {
        $documentId = $request->input('documentId');
        $organizationDashboard = json_decode(Api::get('api/v1/organisation/'.$organisationId))->data;
        return view('risk-insights.organisation-dashboard', compact('organizationDashboard'));
    }

    public function getOrganizationLocationDashboard(Request $request)
    {
        $organizationId = $request->input('organizationId');
        $organizationLocationDashboard = json_decode(Api::get('api/v1/location/'.$organizationId))->data;
        return view('risk-insights.organisation-location-dashboard', compact('organizationLocationDashboard'));
    }

    public function setOrgAnimated(Request $request)
    {
        if ($request->has('reset')) {
            Session::put('org-animated', false);
            Session::put('org-added', false);

            return redirect()->route('risk-insights.index');
        }

        // Set the org-animated session variable to true
        Session::put('org-animated', true);

        return response()->json([
            'success' => true,
            'message' => 'org-animated session variable set successfully'
        ]);
    }



    public function setOrgAdded(Request $request)
    {
        // Set the org-added session variable to true
        Session::put('org-added', true);

        return response()->json([
            'success' => true,
            'message' => 'org-added session variable set successfully',
            'redirect_url' => route('risk-insights.index')
        ]);
    }

}
