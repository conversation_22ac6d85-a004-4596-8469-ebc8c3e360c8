<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;
use App\Models\Documents;
use App\Models\Api;
use App\Models\FileUpload;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Str;

class ReportController extends BaseController
{


    //TEMPLATE PATH
    const TEMPLATE_PATH = '/surveyReport';

    //ROUTE PREFIX
    const ROUTE_PREFIX = 'organisation';


    public function __construct(Request $request, Documents $doc, FileUpload $fileUpload)
    {
        parent::__construct($request);
        $this->documents = $doc;
        $this->files = $fileUpload;
    }


    //INDEX
    public function create($id)
    {
        $response = json_decode(Api::get('api/v1/organisation/'.$id));

        if($response->response == "success") {
            return view(
                static::TEMPLATE_PATH . '/create',
                array(
                'organisation' => $response->data
                )
            );
        }
    }

    public function store_survey_reports(Request $request)
    {
        $rules = [
        'name'              =>  'required',
        'release_date'      =>  'required',
        'document'          =>  'required|max:20480|mimes:pdf,docx,doc,xlsx',
        'organisation_id'    =>    'required',
        'document_image'    =>  'required_if:recommended,on|required_if:featured,on|image'
        ];

        $validator = Validator::make($request->all(), $rules);

        if($validator->fails()) {
            return Redirect::back()->withInput($request->old())->withErrors($validator->errors());
        }
        $id = $request->get('organisation_id');
        $release_date = date("Y-m-d H:i:s", strtotime($request->get('release_date')));
        $data = $request->except('release_date');
        $data['release_date'] = $release_date;

        //Upload Document Image
        if($request->hasFile('document_image')) {
            $file = $request->file('document_image');
            $name = Str::uuid()->toString();

            if(!is_bool($this->files->upload($file->getRealPath(), $name->string))) {
                return Redirect::back()->with('error', 'Could not upload report image')->withInput($request->old());
            }
            $data['img_path'] = $name->string;
            unset($data['document_image']);
        }

        if(isset($data['featured'])) {
            $data['featured'] = true;
        }

        if(isset($data['recommended'])) {
            $data['recommended'] = true;
        }

        $file = $request->file('document');
        $fileName = strtolower(preg_replace('/[\s_]|^%20/', '-', $request->file('document')->getClientOriginalName()));
        $uuid = Str::uuid()->toString();

        $upload = $this->documents->upload($file, $uuid, $id);

        if($upload['response'] == 'success') {
            $data['document_title'] = $fileName;
            $data['name'] = $request->get('name');
            $data['document_store_name'] = $uuid;
        }
        else
        {
            return Redirect::back()
                ->withInput($request->old())
                ->with('error', $upload['message']);
        }
        $response = json_decode(Api::post('/api/v1/document/store', $data));
        if($response->response == 'success') {
            return Redirect::to('/organisation/'.$id)
                           ->with('success', 'Report created successfully');
        }
        else
        {
            return Redirect::back()
                ->withInput($request->old())
                ->withErrors($response->errors);
        }
    }


    public function edit($id, $docID)
    {
        $document = json_decode(Api::get('/api/v1/document/find/'.$docID));
        $organisations = json_decode(Api::get('api/v1/organisation/'.$id));
        if($document->response == 'success' && $organisations->response == "success") {
            //get sectors and covers
            $sectors = json_decode(Api::get('/api/v1/sector/all'));
            $covers = json_decode(Api::Get('/api/v1/cover/all'));
            $image = null;
            if(isset($document->data->img_path)) {
                $image = $this->files->link($document->data->img_path);
            }

            return view(
                static::TEMPLATE_PATH . '/edit',
                array(
                'document'  =>  $document->data,
                'sectors'   =>  $sectors->data,
                'covers'    =>  $covers->data,
                'image'     =>  $image,
                'organisation'  =>  $organisations->data
                )
            );
        }
        else
        {
            return Redirect::route('organisation.show', array('id' => $id))
                           ->with('error', 'Cannot find report');
        }
    }

    public function update_survey_reports(Request $request)
    {
        $rules = [
        'name'              =>  'required',
        'release_date'      =>  'required',
        'organisation_id'    =>    'required',
        'document'          =>  'max:20480|mimes:pdf,docx,doc,xlsx',
        'document_image'    =>  'image'
        ];

        $validator = Validator::make($request->all(), $rules);


        if($validator->fails()) {
            return Redirect::back()
                ->withInput($request->old())
                ->withErrors($validator->errors());
        }

        $data = $request->all();
        $release_date = date('Y-m-d H:i:s', strtotime($data['release_date']));
        $data['release_date'] = $release_date;


        if(isset($data['featured'])) {
            $data['featured'] = true;
        }

        if(isset($data['recommended'])) {
            $data['recommended'] = true;
        }

        // Uplaod Document Image
        if($request->hasFile('document_image')) {
            $file = $request->file('document_image');
            $name = Str::uuid()->toString();
            if(!is_bool($this->files->upload($file->getRealPath(), $name->string))) {
                return Redirect::back()->with('error', 'Could not upload report image')->withInput($request->old());
            }
            $data['img_path'] = $name->string;
            unset($data['document_image']);
        }

        // Upload Document if available
        if($request->hasFile('document')) {
            $fileName = strtolower(preg_replace('/[\s_]|^%20/', '-', $request->file('document')->getClientOriginalName()));
            $uuid = Str::uuid()->toString();

            $upload = $this->documents->update($data['organisation_id'].'/'.$data['old_name'], $request->file('document'), $uuid, $data['organisation_id']);
            if($upload['response'] == "success") {
                $data['document_title'] = $fileName;
                $data['name'] = $request->get('name');
                $data['document_store_name'] = $uuid;
            }
            else
            {
                return Redirect::back()->withInput($request->old())->with('error', $upload['message']);
            }
        }
        $response = json_decode(Api::post('/api/v1/document/update', $data));
        if($response->response == "success") {
            return Redirect::route('organisation.show', array('id' => $data['organisation_id']))
            ->with('success', 'Report successfully updated');
        }
        else
        {
            return Redirect::back()->withInput($request->old())->with('error', $response->message);
        }
    }


    public function delete(Request $request, $id)
    {
        $report = $request->get('id');
        $document = json_decode(Api::get('/api/v1/survey/find/'.$report));
        if($document->response == "success") {
            $fileName = $id . '/' . $document->data->document_store_name;
            $delete = $this->documents->destroy($fileName);
            if($delete['response'] == 'success') {
                $response = json_decode(Api::post('/api/v1/document/destroy/'.$document->data->id));
                if($response->response == "success") {
                    return Redirect::back()->with('success', 'Survey deleted');
                }
                else
                {
                    return Redirect::back()->with('error', 'Cannot find survey');
                }
            }
        }
        return Redirect::back()->with('error', 'Survey not found');
    }


}
