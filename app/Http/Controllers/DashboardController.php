<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Cache;
use App\Models\Api;
class DashboardController extends BaseController
{
    protected $possible_statistics = ['closed'=>0,'open'=>0,'greater_than_30'=>0,'less_than_30'=>0];

    protected $scheme_tracker = [];

    protected $rrTyreTotal = 0;

    public function sumArrayItem($array)
    {
        $final = array();
        array_walk_recursive(
            $array, function ($item, $key) use (&$final) {
                $final[$key] = isset($final[$key]) ?  $item + $final[$key] : $item;
            }
        );
        return $final;
    }

    public function dashboard(Request $request)
    {
        $this->clearIndividualCache($request);

        $user_details = Session::get("user");
        $statistics_data['rrTyre']=[];
        $statistics_data['rr_status']=[];
        $statistics_data['rr_srr_recommendation']=[];

        $statistics_data['schemes'] = null;
        $activeTab = '';
        //echo "<pre>"; dd($user_details);
        if(isset($user_details)) {
            if($user_details->type == 'external-surveyor') {
                return Redirect::route('surveys.index');
            }
            else if($user_details->type == 'broker-user') {
                $broker_id=$user_details->broker_id;

                $openMarket = json_decode(Api::get('api/v1/broker-users/open-market/'.$broker_id));
                $brokerHasAccessToOrgs = [];
                if($openMarket->response!="error" && isset($openMarket->orgs)) {
                    foreach($openMarket->orgs as $brokerOrgs) {
                        if(isset($brokerOrgs->id)) {
                            array_push($brokerHasAccessToOrgs, $brokerOrgs->id);
                        }
                        
                    }
                }
                Session::put('NON_MGA_USER_ACCESS', $brokerHasAccessToOrgs);

                $organisations = json_decode(Api::get('api/v1/broker-users/orgs/'.$broker_id));
                if($organisations->response!="error") {

                    $brokerHasAccessToOrgs = [];

                    foreach ($organisations->schemes as $orgid) {
                        array_push($brokerHasAccessToOrgs, $orgid);
                    }
                    Session::put('MGA_USER_ACCESS', $brokerHasAccessToOrgs);
                }


                if(Cache::has("dashboard_statistics_broker_id_".$broker_id)) {
                    return view('dashboard', Cache::get("dashboard_statistics_broker_id_".$broker_id));
                }
                
                $organisation_details=[];
                $organisation_rrtracker=[];
                $hasMgaSchemes = false;


                $openMarketOrgs = $openMarket->orgs ? $openMarket->orgs : [];
                //echo "<pre>";dd($openMarketOrgs);
                if($organisations->response!="error") {

                    $brokerHasAccessToOrgs = [];
                    $hasMgaSchemes = !empty(($organisations->schemes ?? []));

                    foreach ($organisations->schemes as $orgid) {

                        $tyreLabels = [];
                        $orgdetails= app(OrganisationController::class)->dashboardStatistics($orgid, returnArray: true);
                        $orgdetails['organisationName'] = data_get($orgdetails, 'organisation.name', '');
                        $orgdetails['scheme'] = data_get($orgdetails, 'organisation.mga_scheme_name', '');

                        array_push($organisation_details, $orgdetails);

                        $orgname = $orgdetails['organisationName'] ?? '';

                        $orgnameWidth = strlen($orgname) / 2;
                        $orgname = wordwrap($orgname, $orgnameWidth, "\n");

                        array_push(
                            $organisation_rrtracker, [
                            $orgname,
                            $orgdetails['rrTracker']
                            ]
                        );
                        //tabs
                        if(!$activeTab) {
                               $activeTab = strtolower(str_replace(' ', '-', $orgdetails['scheme']));
                        }

                        foreach($orgdetails["rr_tyre_label"] as $key => $value) {
                            // foreach ($label as $key => $value) {
                            $tyreLabels["".$key.""] = $value;
                            // }
                        }

                        if(isset($this->scheme_tracker[$orgdetails['scheme']]['rrTyreLabel'])) {
                            $this->scheme_tracker[$orgdetails['scheme']]['rrTyreLabel'] = array_merge($this->scheme_tracker[$orgdetails['scheme']]['rrTyreLabel'], $tyreLabels);
                        } else {
                            $this->scheme_tracker[$orgdetails['scheme']]['rrTyreLabel'] = $tyreLabels;
                        }



                        $this->scheme_tracker[$orgdetails['scheme']]['orgData'][$orgdetails['organisationName']]['rrTracker'] = $orgdetails['rrTracker'];
                        $this->scheme_tracker[$orgdetails['scheme']]['selector'] = strtolower(str_replace(' ', '-', $orgdetails['scheme']));
                        $this->scheme_tracker[$orgdetails['scheme']]['orgData'][$orgdetails['organisationName']]['details'] = $orgdetails;
                        $this->scheme_tracker[$orgdetails['scheme']]['rrOverview'] = $this->possible_statistics;

                        $this->scheme_tracker[$orgdetails['scheme']]['orgRrTracker'][$orgdetails['organisationName']] =  [
                         $orgname,
                         $orgdetails['rrTracker']
                        ];
                    }
                    
                }

                // echo "</pre>";
                // print_r($organisation_details); exit;

                if(!$activeTab) {
                    $activeTab = 'Open Market';
                }

                //echo "<pre>"; dd($this->scheme_tracker);
                //tabs
                foreach($this->scheme_tracker as $scheme => $mga) {
                    foreach($mga['orgData'] as $orgData) {
                        $this->rrOverview($scheme, $orgData);
                        $this->rrTyre($scheme, $orgData);
                        $this->rrRecommendationByStatus($scheme, $orgData);
                        $this->rrRecommendationByTitle($scheme, $orgData);
                    }

                    $orgout = [];
                    if(count($mga['orgRrTracker']) > 0) {
                        foreach ($mga['orgRrTracker'] as $org) {
                            if(!isset($org[1]['closed'])) {
                                $org[1]['closed'] = 0;
                            }
                            if(!isset($org[1]['open'])) {
                                $org[1]['open'] = 0;
                            }
                            if(!isset($org[1]['greater_than_30'])) {
                                $org[1]['greater_than_30'] = 0;
                            }
                            if(!isset($org[1]['less_than_30'])) {
                                $org[1]['less_than_30'] = 0;
                            }

                            $orgout[$org[0]] = [
                            'closed' => $org[1]['closed'],
                            'open' => $org[1]['open'] + $org[1]['greater_than_30'] + $org[1]['less_than_30'],
                            ];
                        }
                    }
                    $this->scheme_tracker[$scheme]['organisation_rrtracker'] = $orgout;
                }

                //tabs
                foreach($this->scheme_tracker as $scheme => $mga) {
                    if(isset($mga['rrTyre'])) {
                        $this->processRrTyre($scheme, $mga['rrTyre']);
                    }
                    if(isset($mga['rr_srr_recommendation'])) {
                        arsort($mga['rr_srr_recommendation']);
                        $this->scheme_tracker[$scheme]['rr_srr_recommendation'] = array_slice($mga['rr_srr_recommendation'], 0, 5, true);
                    }
                }
                //echo json_encode($this->scheme_tracker); die();
                $orgout = [];

                foreach ($organisation_rrtracker as $org) {
                    if(!isset($org[1]['closed'])) {
                        $org[1]['closed'] = 0;
                    }
                    if(!isset($org[1]['open'])) {
                        $org[1]['open'] = 0;
                    }
                    if(!isset($org[1]['greater_than_30'])) {
                        $org[1]['greater_than_30'] = 0;
                    }
                    if(!isset($org[1]['less_than_30'])) {
                        $org[1]['less_than_30'] = 0;
                    }

                    $orgout[$org[0]] = [
                    'closed' => $org[1]['closed'],
                    'open' => $org[1]['open'] + $org[1]['greater_than_30'] + $org[1]['less_than_30'],
                    ];
                }
                //echo "<pre>"; dd($organisation_rrtracker);
                $statistics_data['organisation_rrtracker'] = $orgout;

                $rr_Tracker=array_map(
                    function ($v) {
                        return $v['rrTracker'];
                    }, $organisation_details
                );


                $rrTracker = $this->sumArrayItem($rr_Tracker);

                $possible_statistics=['closed'=>0,'open'=>0,'greater_than_30'=>0,'less_than_30'=>0];
                $survey_statistics=count($rrTracker)>0?array_merge($possible_statistics, array_count_values($rrTracker)):$possible_statistics;

                $statistics_data['rrTracker']=$survey_statistics;

                foreach ($organisation_rrtracker as $org) {
                    if(!isset($org[1]['closed'])) {
                        $org[1]['closed'] = 0;
                    }
                    if(!isset($org[1]['open'])) {
                        $org[1]['open'] = 0;
                    }
                    if(!isset($org[1]['greater_than_30'])) {
                        $org[1]['greater_than_30'] = 0;
                    }
                    if(!isset($org[1]['less_than_30'])) {
                        $org[1]['less_than_30'] = 0;
                    }

                    $statistics_data['rrTracker']['closed'] += $org[1]['closed'];
                    $statistics_data['rrTracker']['open'] += $org[1]['open'];
                    $statistics_data['rrTracker']['greater_than_30'] += $org[1]['greater_than_30'];
                    $statistics_data['rrTracker']['less_than_30'] += $org[1]['less_than_30'];
                }

                $rr_Tyre = array_map(
                    function ($v) {
                        return $v['rrTyre'];
                    }, $organisation_details
                );

                $rrTyre = $this->sumArrayItem($rr_Tyre);

                $total = array_sum($rrTyre);

                $rr_tyre = array_map(
                    function ($v) use ($total) {
                        return floor(($v / $total) * 100);
                    }, $rrTyre
                );

                $statistics_data['rrTyre']=$rr_tyre;

                $rr_Status=array_map(
                    function ($v) {
                        return $v['rr_status'];
                    }, $organisation_details
                );

                $rrstatus=[];
                foreach($rr_Status as $status){
                    foreach($status as $key=>$value){
                        $rrstatus[$key]=$value;
                    }
                }

                $statistics_data['rr_status']=$rrstatus;

                $rr_srr_recommendation=array_map(
                    function ($v) {
                        return $v['rr_srr_recommendation'];
                    }, $organisation_details
                );


                $rrRecommendation = $this->sumArrayItem($rr_srr_recommendation);
                $price = array();
                foreach ($rrRecommendation as $key => $row)
                {
                    $price[$key] = $row;
                }
                array_multisort($price, SORT_DESC, $rrRecommendation);
                $rrRecommendation=array_slice($rrRecommendation, 0, 5, true);

                $statistics_data['rr_srr_recommendation']=$rrRecommendation;

                // echo "</pre>";
                // print_r($organisation_details); exit;

                $rr_tyre_label=array_map(
                    function ($v) {
                        return $v['rr_tyre_label'];
                    }, $organisation_details
                );
                $tyreLabels = [];
                foreach($rr_tyre_label as $label) {
                    foreach ($label as $key => $value) {
                        $tyreLabels["".$key.""] = $value;
                    }
                }

                //print_r($rr_tyre_label); exit;

                //$rr_tyre_label = $this->sumArrayItem($rr_tyre_label);

                $statistics_data['rr_tyre_label']=$tyreLabels;

                //$openMarket = $this->scheme_tracker['Open Market'];
                //unset($this->scheme_tracker['Open Market']);

                //$this->scheme_tracker['Open Market'] = $openMarket;
                //echo json_encode($statistics_data);die();
                   
                $brokerId=$user_details->broker_id;

                Cache::put("dashboard_statistics_broker_id_".$broker_id, array_merge($statistics_data, ['schemes' => $this->scheme_tracker, 'activeTab' => $activeTab, 'openMarket' => $openMarketOrgs, 'hasMgaSchemes' => $hasMgaSchemes]), 2880);
             

                return view('dashboard', array_merge($statistics_data, ['schemes' => $this->scheme_tracker, 'activeTab' => $activeTab, 'openMarket' => $openMarketOrgs, 'hasMgaSchemes' => $hasMgaSchemes]));
            }
        }
        return view('dashboard', $statistics_data);
    }

    public function rrOverview($scheme, $orgTracker)
    {
        if(isset($orgTracker['rrTracker']['closed'])) { $this->scheme_tracker[$scheme]['rrOverview']['closed'] += $orgTracker['rrTracker']['closed'];}
        if(isset($orgTracker['rrTracker']['open'])) { $this->scheme_tracker[$scheme]['rrOverview']['open'] += $orgTracker['rrTracker']['open'];}
        if(isset($orgTracker['rrTracker']['greater_than_30'])) { $this->scheme_tracker[$scheme]['rrOverview']['greater_than_30'] += $orgTracker['rrTracker']['greater_than_30'];}
        if(isset($orgTracker['rrTracker']['less_than_30'])) { $this->scheme_tracker[$scheme]['rrOverview']['less_than_30'] += $orgTracker['rrTracker']['less_than_30'];}
    }

    public function rrTyre($scheme, $orgRRTyre)
    {
        foreach($orgRRTyre['details']['rrTyre'] as $tyre => $val){
            $currentVal = isset($this->scheme_tracker[$scheme]['rrTyre'][$tyre]) ? $this->scheme_tracker[$scheme]['rrTyre'][$tyre] : 0;
            $this->scheme_tracker[$scheme]['rrTyre'][$tyre] = $currentVal + $val;
            $this->rrTyreTotal += $val;
        }
    }

    public function processRrTyre($scheme, $rrTyre)
    {
        $running_percentage = $rr_count = 0;

        foreach($rrTyre as $tyre => $value) {
            if ($rr_count === count($rrTyre) - 1) {
                $this->scheme_tracker[$scheme]['rrTyre'][$tyre] = 100 - $running_percentage;
            }else{
                $percent = round(($value / $this->rrTyreTotal) * 100);
                $this->scheme_tracker[$scheme]['rrTyre'][$tyre] = $percent;
                $running_percentage += $percent;
            }
            ++$rr_count;
        }
    }

    public function rrRecommendationByStatus($scheme, $orgRRStatus)
    {
        foreach($orgRRStatus['details']['rr_status'] as $status => $val){

            //$currentVal = isset($this->scheme_tracker[$scheme]['rrTyre'][$tyre]) ? $this->scheme_tracker[$scheme]['rrTyre'][$tyre] : 0;
            $closed = isset($this->scheme_tracker[$scheme]['rrStatus'][$status]['closed']) ? $this->scheme_tracker[$scheme]['rrStatus'][$status]['closed'] : 0;
            $this->scheme_tracker[$scheme]['rrStatus'][$status]['closed'] = $closed + $val['closed'];

            $open = isset($this->scheme_tracker[$scheme]['rrStatus'][$status]['open']) ? $this->scheme_tracker[$scheme]['rrStatus'][$status]['open'] : 0;
            $this->scheme_tracker[$scheme]['rrStatus'][$status]['open'] = $closed + $val['open'];
        }
    }

    public function rrRecommendationByTitle($scheme, $orgTitle)
    {
        foreach($orgTitle['details']['rr_srr_recommendation'] as $title => $val){
            $currentVal = isset($this->scheme_tracker[$scheme]['rr_srr_recommendation'][$title]) ? $this->scheme_tracker[$scheme]['rr_srr_recommendation'][$title] : 0;
            $this->scheme_tracker[$scheme]['rr_srr_recommendation'][$title] = $currentVal + $val;
        }
    }

    private function clearIndividualCache($request){
        $cacheKey=$request->get('cachekey');
        if(isset($cacheKey)){
            Cache::forget($cacheKey);
        }
    }
}
