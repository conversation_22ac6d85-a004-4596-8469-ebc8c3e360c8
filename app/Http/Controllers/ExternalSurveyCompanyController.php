<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;
class ExternalSurveyCompanyController extends BaseResourceController
{
    const
    NAV_ID = 'our-team',
    TEMPLATE_PATH = '/external-survey-companies',
    ROUTE_PREFIX = 'external-survey-companies';

    public function __construct(Request $request)
    {
        BaseController::__construct($request);
    }

    /**
     * Get validation rules for a specific method (store/update)
     *
     * @param  string $method
     * @return array of validation rules
     */
    public function getValidatorRules(Request $request, $method)
    {
        $rules = [
            'name'      =>  'required',
            'address_1' =>  'required',
            'postcode'  =>  'required',
            'city'      =>  'required',
            'country'   =>  'required',
            'email'     =>  'required',
            'phone'     =>  'required',
        ];

        return $rules;
    }
}
