<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;
use App\Models\FileUpload;
use App\Models\Api;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
class DcrDocumentController extends BaseResourceController
{

    public function __construct(Request $request, FileUpload $fileUpload)
    {
        BaseController::__construct($request);
        $this->files = $fileUpload;
    }

    public function index()
    {
        $data = json_decode(Api::get('/api/v1/daily-claims-run/all'));

        if ($data == null) {
            return view('aspen.daily-claims-run')->with('error', 'Failed to fetch Daily Claims Run results');
        }

        if ($data->response == 'success') {
            return view(
                'aspen.daily-claims-run', [
                'data' => $data->data
                ]
            );
        }

        return view('aspen.daily-claims-run')->with('error', 'Failed to fetch Daily Claims Run results');
    }

    public function store(Request $request)
    {
        $data = $request->except('_token');

        if ($request->hasFile('upload')) {
            $file = $request->file('upload');

            $data['original_filename'] = $request->file('upload')->getClientOriginalName();

            $name = Str::uuid()->toString();

            if (! is_bool($this->files->upload($file->getRealPath(), '/daily_claims_run/'.$name->string))) {
                return Redirect::back()->with('error', 'Could not upload report image')->withInput($request->old());
            }

            $data['file'] = $name->string;
            $data['liberty_user_id'] = Session::get('user')->id;

            unset($data['upload']);

            $response = json_decode(Api::post('/api/v1/daily-claims-run/upload', $data));

            if ($response == null) {
                return Redirect::back()->with('error', 'Unable to upload document');
            }

            if ($response->response == 'success') {
                return Redirect::back()->with('success', 'Uploaded document successfully');
            }
        }


        return Redirect::back()->with('error', 'Unable to upload document');
    }

    public function downloadFile($cloudFileName, $fileName)
    {
        if (Session::get('user')->login_type == 'aspen-user') {
            $method = Request::method();
            $path   = Request::path();

            $data = [
                'user_id'    => Session::get('user')->id,
                'first_name' => Session::get('user')->first_name,
                'last_name'  => Session::get('user')->last_name,
                'user_email' => Session::get('user')->email,
                'role'       => Session::get('role'),
                'referer'    => isset($_SERVER["HTTP_REFERER"]) ? $_SERVER["HTTP_REFERER"] : 'direct',
                'segments'   => Request::segments()
            ];

            $data['method'] = $method;
            $data['path']   = $path;

            Api::post('api/v1/weblog', $data);
        }

        $file = $this->files->download('daily_claims_run/'.$cloudFileName, $fileName);

        if ($file) {
            $method = Request::method();
            $path = Request::path();

            $data = [
                'user_id'    => Session::get('user')->id,
                'user_email' => Session::get('user')->email,
                'role'       => Session::get('role'),
                'referer'    => isset($_SERVER["HTTP_REFERER"]) ? $_SERVER["HTTP_REFERER"] : 'direct',
                'segments'   => Request::segments()
            ];

            $data['method'] = $method;
            $data['path'] = $path;

            Api::post('api/v1/weblog', $data);

            return Response::download($file, $fileName);
        }
    }

}
