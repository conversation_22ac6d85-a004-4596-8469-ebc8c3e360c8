<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Api;
use App\Services\CacheContent\OptionListService;
use App\Services\SendSqsMessageService;

class BranchController extends BaseResourceController
{
    const
        NAV_ID = 'our-team',
        TEMPLATE_PATH = '/branch',
        ROUTE_PREFIX = 'liberty-branches';

    public function __construct(Request $request)
    {
        BaseController::__construct($request);
    }

    /**
     * Get validation rules for a specific method (store/update)
     *
     * @param  string $method
     * @return array of validation rules
     */
    public function getValidatorRules(Request $request, $method)
    {
        $rules = [
            'name' => 'required',
        //            'address_1' =>  'required',
        //            'postcode'  =>  'required',
        //            'city'      =>  'required',
        //            'country'   =>  'required',
        //            'email'     =>  'required',
        //            'phone'     =>  'required',
        ];

        return $rules;
    }

    public function details($id)
    {
        $branches = Api::get('/api/v1/liberty-branches/' . $id);
        return $branches;
    }

    public static function onStoreSuccess($data)
    {
        self::recacheRelatedData();
    }

    public static function onUpdateSuccess($data)
    {
        self::recacheRelatedData();
    }

    public static function onDestroySuccess()
    {
        self::recacheRelatedData();
    }

    public static function recacheRelatedData()
    {
        SendSqsMessageService::sendMessages([
            [
                'serviceClass' => OptionListService::class,
                'params' => 'branches',
            ],
        ]);
    }
}
