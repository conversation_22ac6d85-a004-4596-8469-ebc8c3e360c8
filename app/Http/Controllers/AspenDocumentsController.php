<?php

namespace App\Http\Controllers;

use App\Models\Api;
use App\Models\FileUpload;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
class AspenDocumentsController extends BaseResourceController
{
    const
        TEMPLATE_PATH = '/aspen-documents',
        ROUTE_PREFIX = 'aspen-documents';

    public function __construct(Request $request, FileUpload $fileUpload)
    {
        parent::__construct($request);
        $this->files = $fileUpload;
    }

    /**
     * new request
     */
    public function create_document($id)
    {
        $data = json_decode(Api::get('api/v1/organisation/' . $id));

        if ($data->response == 'success') {
            return view(
                'aspen-documents/create', [
                'organisation' => $data->data,
                ]
            );
        }
    }

    /**
     * store request
     */
    public function store(Request $request)
    {
        $data = $request->except('_token');
        //upload files
        if ($request->hasFile('upload')) {
            $file = $request->file('upload');
            $data['original_filename'] = $request->file('upload')->getClientOriginalName();
            $uuid = Str::uuid()->toString();
            if (!is_bool($this->files->upload($file->getRealPath(), $uuid))) {
                return Redirect::back()->with(
                    'error',
                    'Could not upload report image'
                )->withInput($request->old());
            }
            $data['file'] = $uuid;
        }
        unset($data['upload']);

        $response = json_decode(Api::post('/api/v1/aspen-documents', $data));
        if ($response->response == "success") {
            return Redirect::to('organisation/' . $data['organisation_id']);
        }
    }

    public function documentsAccess(Request $request)
    {
        $page = $request->has('page')
            ? $request->get('page')
            : 1;
        $limit = Session::has('limit')
            ? Session::get('limit')
            : 10;
        $search = $request->has('search')
            ? $request->get('search')
            : null;

        if ($search == null) {
            $data = json_decode(Api::get('/api/v1/aspen-documents-access/all/' . $page . '/' . $limit));
        } else {
            $data = json_decode(Api::get('/api/v1/aspen-documents-access/all/' . $page . '/' . $limit . '/' . $search));
        }

        return view(
            'aspen-documents.access', [
            'data' => $data,
            'page' => $page,
            'limit' => $limit,
            'total' => $data->total,
            'link' => 'aspen.documents-access',
            'search' => null,
            ]
        );
    }
}
