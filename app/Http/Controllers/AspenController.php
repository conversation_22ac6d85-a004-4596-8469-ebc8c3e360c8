<?php

namespace App\Http\Controllers;

use App\Models\Api;
use App\Models\FileUpload;
use Aws\Laravel\AwsFacade;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
class AspenController extends BaseResourceController
{
    const
        TEMPLATE_PATH = '/aspen',
        ROUTE_PREFIX = 'aspen';

    public $s3;

    public function __construct(Request $request, FileUpload $fileUpload)
    {
        parent::__construct($request);
        $this->files = $fileUpload;
        $this->s3 = AwsFacade::createClient('s3');
    }

    /**
     * Show all requests
     */
    public function index(Request $request)
    {
        $page = $request->get('page');
        $page = isset($page)
            ? $request->get('page')
            : 1;
        $limit = Session::has('limit')
            ? Session::get('limit')
            : 10;

        $directions = [
            'la' => 'Liberty to Aspen',
            'al' => 'Aspen to Liberty',
        ];

        $priorities = [
            0 => 'Priority',
            'critical' => 'Priority 1: need it within 24 hours',
            'medium' => 'Priority 2: everything else',
        ];

        $data = json_decode(Api::get('/api/v1/liberty-users/all/1/99999999999/null/orderByNameASC'));
        $liberty_user = [];
        foreach ($data->data as $user) {
            if ($user->role != 'aspen-user') {
                $liberty_user[$user->id] = $user->first_name . ' ' . $user->last_name;
            }
        }

        $data = json_decode(Api::get('/api/v1/liberty-users/all/1/99999999999/aspen-user/orderByNameASC'));
        $aspen_user = [];
        foreach ($data->data as $user) {
            $aspen_user[$user->id] = $user->first_name . ' ' . $user->last_name;
        }

        $clients = json_decode(Api::get('api/v1/organisation/options?org_type=is_aspen'), true);
        //dd($clients);
        // $clients_output = [];

        // foreach ($clients as $client) {
        //     $clients_output[$client->id] = $client->name;
        // }

        $statuses = ['pending-liberty' => 'Pending Liberty', 'pending-aspen' => 'Pending Aspen', 'closed' => 'Closed'];

        $priority = Session::has('aspen.filter.priority')
            ? Session::get('aspen.filter.priority')
            : 0;
        $direction = Session::has('aspen.filter.direction')
            ? Session::get('aspen.filter.direction')
            : 0;
        $aspen_users = Session::has('aspen.filter.aspen_user')
            ? Session::get('aspen.filter.aspen_user')
            : 0;
        $liberty_users = Session::has('aspen.filter.liberty_user')
            ? Session::get('aspen.filter.liberty_user')
            : 0;
        $client = Session::has('aspen.filter.client')
            ? Session::get('aspen.filter.client')
            : 0;
        $status = Session::has('aspen.filter.status')
            ? Session::get('aspen.filter.status')
            : 0;

        $response = json_decode(Api::get('/api/v1/aspen/all/' . $page . '/' . $limit . '/' . $priority . '/' . $direction . '/' . $aspen_users . '/' . $liberty_users . '/' . $client . '/' . $status . '/orderByDateDESC'));
        $requests = $response->data;

        return view(
            'aspen/index', [
            'requests' => $requests,
            'total' => $response->total,
            'limit' => $limit,
            'page' => $page,
            'link' => 'aspen.index',
            'search' => '',
            'directions' => $directions,
            'liberty_user' => $liberty_user,
            'aspen_user' => $aspen_user,
            'clients' => $clients,
            'statuses' => $statuses,
            'priorities' => $priorities,
            ]
        );
    }

    /**
     * show a request
     */
    public function show($id)
    {
        //clients
        $data = json_decode(Api::get('/api/v1/organisation/all'));
        $clients = json_decode(Api::get('/api/v1/organisation/options?org_type=is_aspen'), true);


        // liberty users
        $data = json_decode(Api::get('/api/v1/liberty-users/all/1/9999999999/null/orderByNameASC'));

        $libertyUsers = [];
        foreach ($data->data as $user) {
            if (isset($user->role) && $user->role != 'aspen-user') {
                $libertyUsers[$user->id] = $user->first_name . ' ' . $user->last_name;
            }
        }

        $priorities = [
            0 => 'Priority',
            'critical' => 'Priority 1: need it within 24 hours',
            'medium' => 'Priority 2: everything else',
        ];

        // aspen users
        // TODO: create a seperate method for this
        $data = json_decode(Api::get('/api/v1/liberty-users/all/1/99999999999/aspen-user/orderByNameASC'));
        $aspenUsers = [];
        foreach ($data->data as $user) {
            $aspenUsers[$user->id] = $user->first_name . ' ' . $user->last_name;
        }

        // requests
        $request = json_decode(Api::get('/api/v1/aspen/' . $id))->data;

        // policies
        $data = json_decode(Api::get('/api/v1/organisation/' . $request->user_id . '/policy-numbers'));

        $policies = [];

        if (!isset($data)) {
            return redirect()->back()->with('error', 'Unable to access aspen request');
        }

        if (isset($data->data)) {
            foreach ($data->data as $policy) {
                $policies[$policy->id] = $policy->policy_number . ' [' . $policy->type->name . ' ' . date(
                    'd/m/Y',
                    strtotime($policy->inception_date_of_cover)
                ) . ' to ' . date(
                    'd/m/Y',
                    strtotime($policy->expiry_date_of_cover)
                ) . '] ';
            }
        }

        $request->uploads = unserialize($request->uploads);
        if (!$request->uploads) {
            $request->uploads = [];
        }
        foreach ($request->uploads as $key => &$upload) {
            $aspenDocument = json_decode(Api::get('/api/v1/aspen-documents/code/' . $upload))->data;
            $upload = [
                'original_filename' => $aspenDocument->original_filename,
                'cloud_file_name' => $upload,
            ];
        }

        // reply uploads
        $uploads = [];
        $request_items = $request->items;
        foreach ($request->replies as $replies) {
            if ($replies->uploads) {
                $replies->uploads = unserialize($replies->uploads);
                foreach ($replies->uploads as $attachment) {

                    $aspenDocument = json_decode(Api::get('/api/v1/aspen-documents/code/' . $attachment))->data;
                    $created_at = $aspenDocument->created_at;
                    // echo "<pre>";
                    // print_r($replies); exit;
                    $uploads[] = [
                        'url' => $this->files->link($attachment),
                        'original_filename' => $aspenDocument->original_filename,
                        'cloud_file_name' => $attachment,
                        'created_at' => $created_at,
                        'author' => isset($replies->user->first_name)
                            ? $replies->user->first_name . ' ' . $replies->user->last_name
                            : 'A deleted user',
                        'item_types' => $request_items,
                    ];
                }
            }
        }

        return view(
            'aspen/show', [
            'clients' => $clients,
            'policies' => $policies,
            'request' => $request,
            'libertyUsers' => $libertyUsers,
            'aspenUsers' => $aspenUsers,
            'uploads' => $uploads,
            'priorities' => $priorities,
            ]
        );
    }


    public function downloadFile($cloudFileName, $fileName)
    {
        $signedUrl = $this->s3->getObjectUrl(
            config('app.aws.bucket', null),
            $cloudFileName,
            '+10 minutes'
        );
        if ($signedUrl) {
            $tempImage = tempnam(sys_get_temp_dir(), $fileName);
            copy($signedUrl, $tempImage);
            return Response::download($tempImage, $fileName);
        }
    }

    /**
     * new request
     */
    public function create($id = null)
    {
        $clients = json_decode(Api::get('/api/v1/organisation/options?org_type=is_aspen'), true);

        $policyType = json_decode(Api::get('/api/v1/policy-types/all'));
        $policies = [];


        return view(
            'aspen/create', [
            'clients' => $clients,
            'client_id' => $id,
            ]
        );
    }

    /**
     * store request
     */
    public function store(Request $request)
    {
        if ($request->get('user_id') == '' || ($request->has('policy_id') && $request->get('policy_id') == '0') || $request->get('priority') == '0') {
            return Redirect::back()->with(
                'error',
                'Required fields have been left blank'
            )->withInput($request->old());
        }

        $data = $request->except('_token');
        if (isset($data['items'])) {
            $data['items'] = implode(',', $data['items']);
        }
        $data['from'] = Session::get('user');


        $document = [
            'user_id' => Session::get('user'),
            'item_type' => $data['items'] ?? '',
        ];

        //upload files
        if ($request->hasFile('uploads')) {
            $files = $request->file('uploads');
            foreach ($files as $file) {
                $document['original_filename'] = $file->getClientOriginalName();
                $uuid = Str::uuid()->toString();
                $document['file'] = $uuid;
                $response = json_decode(Api::post('/api/v1/aspen-documents', $document));
                if (!is_bool($this->files->upload($file->getRealPath(), $uuid))) {
                    return Redirect::back()->with('error', 'Could not upload report image')->withInput($request->old());
                }
                $data['attachments'][] = $uuid;
            }
            $data['uploads'] = serialize($data['attachments']);
            unset($data['attachments']);
        }
        $data['from_user_id'] = Session::get('user');
        $data['role'] = Session::get('role');
        $response = json_decode(Api::post('/api/v1/aspen', $data));
        if ($response->response == "success") {
            return Redirect::to('aspen');
        }
    }

    /**
     * update a request
     */
    public function update(Request $request, $id)
    {
        $data = $request->except('_token', '_method');
        $response = json_decode(Api::put('/api/v1/aspen/' . $id, $data));
        if ($response->response == "success") {
            return Redirect::to('aspen/' . $id)->with('success', 'Request updated successfully');
        }
    }

    /**
     * reply to a request
     */
    public function reply(Request $request, $id)
    {

        $data = $request->except('_token', '_method');
        if ($request->has('close')) {
            $response = json_decode(Api::put('/api/v1/aspen/' . $id, ['status' => 'closed']));
            return Redirect::to('aspen/' . $id)->with(
                'success',
                'Request closed'
            );
        }

        if ($request->has('reply-close')) {
            $response = json_decode(Api::put('/api/v1/aspen/' . $id, ['status' => 'closed']));
        }

        if ($request->has('reply')) {
            $status = Session::get('role') == 'aspen-user'
                ? 'pending-liberty'
                : 'pending-aspen';
            $response = json_decode(Api::put('/api/v1/aspen/' . $id, ['status' => $status]));
        }

        if ($request->has('message') || $request->hasFile('uploads')) {
            $data = [
                'message' => $data['message'],
                'user_id' => Session::get('user'),
                'request_id' => $id,
            ];

            $document = [
                'organisation_id' => $request->get('organisation_id'),
                'user_id' => Session::get('user'),
                'item_type' => $request->get('item_type'),
            ];

            if ($request->hasFile('uploads')) {
                $files = $request->file('uploads');
                foreach ($files as $file) {
                    $document['original_filename'] = $file->getClientOriginalName();
                    $uuid = Str::uuid()->toString();
                    $document['file'] = $uuid;
                    $response = json_decode(Api::post('/api/v1/aspen-documents', $document));

                    if (!is_bool($this->files->upload($file->getRealPath(), $uuid))) {
                        return Redirect::back()->with(
                            'error',
                            'Could not upload report image'
                        )->withInput($request->old());
                    }
                    $data['attachments'][] =$uuid;
                }
                $data['uploads'] = serialize($data['attachments']);
                unset($data['attachments']);
            }
            $data['role'] = Session::get('user')->role;

            $response = json_decode(Api::post('/api/v1/aspen/reply', $data));
        }
        return Redirect::to('aspen/' . $id)->with('success', 'Request updated successfully');
    }

    public function clear()
    {
        Session::forget('aspen.filter');
        return Redirect::to('aspen');
    }

    public function filter(Request $request)
    {
        $session_type = 'aspen.filter';
        $category = [
            'priority',
            'direction',
            'aspen_user',
            'liberty_user',
            'client',
            'status',
        ];
        foreach ($category as $input) {
            if ($request->has($input)) {
                Session::put($session_type . '.' . $input, $request->get($input));
            } else {
                Session::forget($session_type . '.' . $input);
            }
        }
        return Redirect::to('aspen');
    }

}
