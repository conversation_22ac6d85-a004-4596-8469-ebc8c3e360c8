<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Documents;
use App\Models\Api;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Response;
use App\Models\FileUpload;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Str;

class DocumentLibController extends BaseController
{

    const TEMPLATE_DIRECTORY = '/documentlib';

    const ROUTE_PREFIX = 'document';

    public function __construct(Request $request, Documents $doc, FileUpload $fileUpload)
    {
        parent::__construct($request);
        $this->documents = $doc;
        $this->files = $fileUpload;
    }

    /**
     * GET: Create Risk Guidance doc upload
     */
    public function create()
    {
        $sectors = json_decode(Api::get('/api/v1/sector/all'));
        $covers = json_decode(Api::Get('/api/v1/cover/all'));
        $level1 = json_decode(Api::Get('/api/v1/doc_level/null/1'));

        return view(
            static::TEMPLATE_DIRECTORY . '/create',
            array(
            'sectors'       => $sectors->data,
            'covers'        => $covers->data,
            'level1'        => $level1->data
            )
        );
    }

    /**
     * GET: Create Loss Lessons doc upload
     */
    public function create_loss_lessons()
    {

        $sectors = json_decode(Api::get('/api/v1/sector/all'));
        $covers = json_decode(Api::Get('/api/v1/cover/all'));

        return view(
            static::TEMPLATE_DIRECTORY . '/create_loss_lessons',
            array(
            'sectors'       => $sectors->data,
            'covers'        => $covers->data
            )
        );
    }

    public function store_loss_lessons(Request $request)
    {
        $id = 'doc_lib';
        $rules = [
        'name'              =>  'required',
            'description'       =>  'required',
        'sector'            =>  'required',
            'document'          =>  'required|max:20480|mimes:pdf,docx,doc,xlsx,xls,ppt,pptx,wmv,video/x-ms-asf',
        'document_image'    =>  'required_if:featured,on|image|required_if:recommended,on',
        ];
        if($request->file('document') !== null) {
            if($request->file('document')->getClientOriginalExtension() == 'wmv') {
                unset($rules['document']);
            }

        }

        $validator = Validator::make($request->all(), $rules);

        if($validator->fails()) {
            return Redirect::back()->withInput($request->old())->withErrors($validator->errors());
        }

        $data = $request->except('_token');
        $data['sector'] = implode(',', $data['sector']);
        if(isset($data['featured'])) {
            $data['featured'] = true;
        }

        //Upload Document Image
        if($request->hasFile('document_image')) {
            $file = $request->file('document_image');
            $name = (string) Str::uuid()->toString();
            if(!is_bool($this->files->upload($file->getRealPath(), $name))) {
                return Redirect::back()->with('error', 'Could not upload document image')->withInput($request->old());
            }
            $data['img_path'] = $name;
            unset($data['document_image']);
        }
        $file = $request->file('document');
        $fileName = strtolower(preg_replace('/[\s_]|^%20/', '-', $request->file('document')->getClientOriginalName()));
        $uuid = (string) Str::uuid()->toString();

        $upload = $this->documents->upload($file, $uuid, $id);
        if($upload['response'] == 'success') {
            $data['document_title'] = $fileName;
            $data['document_store_name'] = $uuid;
        }

        $data['user_id'] = Session::get('user')->id;
        $data['type'] = 'admin';
        $response = json_decode(Api::post('/api/v1/document/store', $data));
        if($response->response == 'success') {
            return Redirect::route('document.all')
                           ->with('success', 'Loss Lesson Document created successfully');
        }
        else
        {
            return Redirect::back()
                ->withInput($request->old())
                ->with('error', $response->message);
        }
    }


    public function edit_loss_lessons($docID)
    {
        $id = 'doc_lib';
        $document = json_decode(Api::get('/api/v1/document/find/'.$docID));

        if(isset($document->response) &&  $document->response== 'success') {
            //get sectors and covers
            $sectors = json_decode(Api::get('/api/v1/sector/all'));
            $covers = json_decode(Api::Get('/api/v1/cover/all'));
            $image = null;
            if(isset($document->data->img_path)) {
                $image = $this->files->link($document->data->img_path);
            }

            return view(
                static::TEMPLATE_DIRECTORY . '/edit_loss_lessons',
                array(
                'document'  =>  $document->data,
                'sectors'   =>  $sectors->data,
                'covers'    =>  $covers->data,
                'image'     =>  $image
                )
            );
        }
        else
        {
            return Redirect::route('document.all')
                           ->with('error', 'Cannot find file');
        }
    }


    /**
     * POST: Store
     * WIll upload to rackspace and store the name of the file within DB
     */

    public function store(Request $request)
    {
        $id = 'doc_lib';
        $rules = [
            'name'              =>  'required',
            'description'       =>  'required',
            'sector'            =>  'required',
            'document'          =>  'required|max:20480|mimes:pdf,docx,doc,xlsx,xls,ppt,pptx,wmv,video/x-ms-asf',
            'document_image'    =>  'required_if:featured,on|image|required_if:recommended,on',
        ];

        if ($request->file('document') !== null) {
            if ($request->file('document')->getClientOriginalExtension() == 'wmv') {
                unset($rules['document']);
            }
        }
        //print_r($request->file('document')->getClientOriginalExtension());exit;
        $validator = Validator::make($request->all(), $rules);

        if($validator->fails()) {
            return Redirect::route(static::ROUTE_PREFIX . '.create')
                ->withInput($request->old())
                ->withErrors($validator->errors());
        }
        $data = $request->except('_token');
        $data['sector'] = implode(',', $data['sector']);
        if(isset($data['featured'])) {
            $data['featured'] = true;
        }

        if(isset($data['recommended'])) {
            $data['recommended'] = true;
        }

        //Upload Document Image
        if($request->hasFile('document_image')) {
            $file = $request->file('document_image');
            $name = (string) Str::uuid()->toString();
            if(!is_bool($this->files->upload($file->getRealPath(), $name))) {
                return Redirect::back()->with('error', 'Failed to upload document image')
                    ->withInput($request->old());
            }

            $data['img_path'] = $name;
            unset($data['document_image']);
        }

        $file     = $request->file('document');
        $uuid     = (string) Str::uuid()->toString();
        $fileName = strtolower(preg_replace('/[\s_]|^%20/', '-', $request->file('document')->getClientOriginalName()));
        $upload   = $this->documents->upload($file, $uuid, $id);
        if ($upload['response'] == 'success') {
            $data['document_title'] = $fileName;
            $data['document_store_name'] = $uuid;
        } else {
            return Redirect::back()->with('error', $upload['message'])->withInput($request->old());
        }

        $data['user_id']  = Session::get('user')->id;
        $data['type']     = 'admin';
        $data['category'] = '';
        $response = json_decode(Api::post('/api/v1/document/store', $data));
        if ($response->response == 'success') {
            return Redirect::route('document.all')
                           ->with('success', 'Document created successfully');
        } else {
            return Redirect::route('document.create')
                ->withInput($request->old())
                ->withErrors($response->errors);
        }
    }

    /**
     * get doc lib documents
     *
     * @return mixed
     */
    public function all()
    {
        $lossLesson = json_decode(Api::get('/api/v1/loss-lesson/all'));
        $doc_lib = json_decode(Api::get('/api/v1/document/getdoclib'));
        return view(
            static::TEMPLATE_DIRECTORY . '/all',
            array(
            'documents'     => $doc_lib->data,
            'lossLessons'   =>  $lossLesson->data
            )
        );

    }

    /**
     * get link from rackspace
     *
     * @param $id
     * @param $name
     *
     * @return mixed
     */
    public function retrieveNotes($orgid,$noteid)
    {
        $note = json_decode(Api::get('api/v1/organisation/'.$orgid.'/notes/'.$noteid));
        if($note->response == 'success' && isset($note->data->attachment_title)) {
            $filetitle=$note->data->attachment_title;
            $fileName = $orgid . '/' .$note->data->attachment_store_name;
            $file = $this->documents->download($fileName, $filetitle);
            if(substr($filetitle, -4) == '.pdf') {
                $content_type = array('Content-Type' => 'application/pdf');
            } else {
                $content_type = array('Content-Type' => 'text/plain');
            }

            if ($file['response'] == 'success') {
                if(strtotime($note->data->created_at)-1480467600 > 0) {
                    $response = Response::download($file['data'], $filetitle, $content_type);
                } else {
                    $decrypt = $this->documents->decrypt($file['data'], $note->data->attachment_store_name);
                    $response = Response::download($decrypt['data'], $note->data->attachment_store_name, $content_type);
                }
            }
            else
            {
                return Response::json(
                    [
                    'response'  =>  'error',
                    'message'   =>  $file['message']
                    ]
                );
            }
            return $response;
        }
    }

    public function retrieve($name)
    {
        $id = 'doc_lib';
        $document = json_decode(Api::get('/api/v1/document/find/'.$name));

        if(isset($document->response) &&  $document->response== 'success') {
            $fileName = $id . '/' . $document->data->document_store_name;
            $file = $this->documents->download($fileName, $document->data->document_title);

            if(substr($document->data->document_title, -4) == '.pdf') {
                $content_type = array('Content-Type' => 'application/pdf');
            } else {
                $content_type = array('Content-Type' => 'text/plain');
            }

            if ($file['response'] == 'success') {
                if(strtotime($document->data->created_at)-1480467600 > 0) {
                    $response = Response::download($file['data'], $document->data->document_title, $content_type);
                } else {
                    $decrypt = $this->documents->decrypt($file['data'], $document->data->document_store_name);
                    $response = Response::download($decrypt['data'], $document->data->document_title, $content_type);
                }

            }
            else
            {
                return Response::json(
                    [
                    'response'  =>  'error',
                    'message'   =>  $file['message']
                    ]
                );
            }
            return $response;

        }
    }

    /**
     * Delete File from rackspace and database
     */
    public function destroy($docID)
    {
        $id = 'doc_lib';
        $document = json_decode(Api::get('/api/v1/document/find/'.$docID));
        if(isset($document->response) &&  $document->response== 'success') {
            $fileName = $id . '/' . $document->data->document_store_name;

            $delete = $this->documents->destroy($fileName);
            if($delete['response'] == 'success') {
                //delete from server
                $response = json_decode(Api::post('/api/v1/document/destroy/'.$docID));
                if($response->response == 'success') {
                    return Redirect::route('document.all')
                                   ->with('success', 'Document Deleted');
                }
                else
                {
                    return Redirect::route('document.all')
                                   ->with('error', 'Cannot find file');
                }
            }
        }
        else
        {
            return Redirect::route('document.all')
            ->with('error', 'Cannot find file');
        }
    }

    public function get_level($level, $parent)
    {
        $levels = json_decode(Api::Get('/api/v1/doc_level/'.$level.'/'.$parent));
        if($levels->response === 'success') {
            $html_string = '';
            foreach ($levels->data as $current_level) {
                $html_string .= '<option value="'.$current_level->level_id.'">'.$current_level->level_name.'</option>';
            }
            return json_encode(['response' => 'success', 'html_data' => $html_string]);
        }
    }


    /*
    * GET: Edit document
    */

    public function edit($docID)
    {
        $document = json_decode(Api::get('/api/v1/document/find/'.$docID));
		
		if (isset($document->response) &&  $document->response== 'success') {
			$type1 = $document->data->level1_type;
			$type2 = $document->data->level2_type;
			$type3 = $document->data->level3_type;
			$type4 = $document->data->level4_type;

			//get sectors and covers
			$sectors = json_decode(Api::get('/api/v1/sector/all'));
			$covers  = json_decode(Api::get('/api/v1/cover/all'));
			$image   = null;
			$level1  = json_decode(Api::get('/api/v1/doc_level/null/1'));
			$level4  = [];

			// FOR FAS PORTAL LOGIC
			$isFasDo = false;
			if (!empty($document->data->level1) 
				&& array_key_exists($document->data->level1->level_name, config('client_dashboard.fas_pi_risk_guidances'))) {
				$level4 = json_decode(Api::get('/api/v1/doc_level/null/4/' . $document->data->level1->level_id));
				$isFasDo = true;
			}

			if (isset($document->data->img_path)) {
				$image = $this->files->link($document->data->img_path);
			}

            $doc_levels = [];
            if (!is_null($type4)) {
                $level1Type = $type1;
                $level2Type = isset($type2) ? $type2 : 0;
                $level3Type = isset($type3) ? $type3 : 0;
                $level4Type = $type4;

                $doc_levels = json_decode(Api::get('/api/v1/doc_levels_all/' . $level1Type . '/' . $level2Type . '/' . $level3Type . '/' . $level4Type));
                $doc_levels = !empty($doc_levels) && $doc_levels->response === 'success' ? $doc_levels->data : [];
		    }

            return View::make(
                static::TEMPLATE_DIRECTORY . '/edit',
                [
                    'document'      => $document->data,
                    'sectors'       => $sectors->data,
                    'covers'        => $covers->data,
                    'image'         => $image,
                    'isFasDo'       => $isFasDo,
                    'level1'        => $level1->data,
                    'level4Options' => !empty($level4->data) ? $level4->data : $level4,
                    'levels'        => $doc_levels
                ]
            );
		} else {
            return Redirect::route('document.all')
                ->with('error','Cannot find file');
        }
    }


    /**
     * POST: Update document
     * If a new document has not been selected then update just DB,
     * If there is a new document, delete the old one and upload a new one
     */

    public function update(Request $request)
    {
        $rules = [
        'name'              =>  'required',
        'description'       =>  'required',
        'sector'            =>  'required',
        'document'          =>  'max:20480|mimes:pdf,docx,doc,xlsx,xls,ppt,pptx,wmv,video/x-ms-asf',
        'document_image'    =>  'image|required_if:featured,on|required_if:recommended,on',
        ];

        if ($request->file('document') !== null) {
            if ($request->file('document')->getClientOriginalExtension() == 'wmv') {
                unset($rules['document']);
            }
        }

        if ($request->get('old_document_image') != null && $request->file('document_image') == null) {
            if ($request->get('featured') == 'on' || $request->get('recommended') == 'on') {
                unset($rules['document_image']);
            }
        }

        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            return Redirect::route('document.edit', array('docID' => $request->get('id')))
                ->withInput($request->old())
                ->withErrors($validator->errors());
        } else {
            $document = $request->file('document');
            $data = $request->except('_token');

            if (empty($data['level2_type'])) {
                $data['level2_type'] = '';
            }

            if (empty($data['level3_type'])) {
                $data['level3_type'] = '';
            }


            if(isset($data['featured'])) {
                $data['featured'] = true;
            }

            if (isset($data['recommended'])) {
                $data['recommended'] = true;
            }

            //Upload Document image
            if($request->hasFile('document_image')) {
                $file = $request->file('document_image');
                $name = (string) Str::uuid()->toString();
                if (!is_bool($this->files->upload($file->getRealPath(), $name))) {
                    return Redirect::back()->with('error', 'Failed to upload document image')
                        ->withInput($request->old());
                }

                if (
                    isset($data['old_document_image'])
                    && $data['old_document_image'] != null
                    && $data['old_document_image'] != ''
                    && $this->files->exists($data['old_document_image'])
                ) {
                    $this->files->destroy($data['old_document_image']);
                }

                $data['img_path'] = $name;
                unset($data['document_image']);
            }

            if (isset($document)) {
                //delete old document and upload new one
                $docID = $request->get('id');
                $oldDocument = json_decode(Api::get('/api/v1/document/find/'.$docID));
                if ($oldDocument->response == 'success') {
                    if (is_null($oldDocument->data->organisation_id)) {
                        $id = 'doc_lib';
                    } else {
                        $id = $oldDocument->data->organisation_id;
                    }

                    $oldDocumentName = $id .'/'. $oldDocument->data->document_store_name;
                    $uuid = (string) Str::uuid()->toString();
                    $upload = $this->documents->upload($document, $uuid, $id);
                    if($upload['response'] == 'success') {
                        $fileName = strtolower(preg_replace('/[\s_]|^%20/', '-', $request->file('document')->getClientOriginalName()));
                        $data['document_title'] = $fileName;
                        $data['document_store_name'] = $uuid;

                    }
                }
            }

            $data['sector']  = implode(',', $data['sector']);
            $data['user_id'] = Session::get('user')->id;
            $response        = json_decode(Api::post('/api/v1/document/update', $data));
            if ($response->response == 'success') {
                return Redirect::route('document.all')
                ->with('success', 'Document updated');
            } else {
                return Redirect::route('document.edit', array('docID' => $data['id']))
                    ->with('error', $response->message)
                    ->withInput($request->old());
            }
        }

    }

}
