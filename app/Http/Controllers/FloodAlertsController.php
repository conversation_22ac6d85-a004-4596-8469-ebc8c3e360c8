<?php

namespace App\Http\Controllers;
use Carbon\Carbon;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Aws\Laravel\AwsFacade as AWS;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Log;
use App\Models\Api; 
use App\Models\Documents;
use App\Models\FileUpload;
class FloodAlertsController extends BaseController
{
    protected $file;

    protected $documents;

    public function __construct(Documents $doc, FileUpload $fileUpload)
    {
        $this->documents = $doc;
        $this->file = $fileUpload;
    }

    const ALERT_LEVEL_MAPPING = [
        1 => 'Prepare',
        2 => 'Act',
        3 => 'Severe Flood Warning',
    ];

    const ALERT_LEVEL_CLASS_MAPPING = [
        1 => 'alert-card-prepare',
        2 => 'alert-card-act',
        3 => 'alert-card-severe',
    ];

    const ALERT_LEVEL_RECOM_MAPPING = [
        1 => "<p class='font-weight-bold alert-recommendation-title'><PERSON> Alert - Prepare</p>
            <p>We recommend you refer to your site-level flood emergency response plan (FERP) for your next steps towards flood event mitigation measures.</p>
            <p>Such actions you may wish to take include:</p>
            <ul>
                <li>Deployment of any temporary or permanent flood barriers</li>
                <li>Check internal and external drains and associated infrastructure for vegetation and other blockages</li>
                <li>Prepare to relocate ground-level stock inside and outside the building</li>
                <li>Prepare to mobilise </li>
            </ul>",
        2 => "<p class='font-weight-bold alert-recommendation-title'>Flood Alert - Act</p>
            <p>We recommend you action your site-level flood emergency response plan (FERP) and take appropriate steps towards flood event mitigation.</p>
            <p>Such actions you may wish to take include:</p>
            <ul>
                <li>Final check of temporary and permanent barrier placement across all building entry points</li>
                <li>Relocate ground-level stock inside and outside the building</li>
                <li>Mobilise any flood mitigation equipment</li>
                <li>Isolate all utilities where safe to do so</li>
                <li>Refer to crisis management and business continuity plans</li>
            </ul>",
        3 => "<p class='font-weight-bold alert-recommendation-title'>Flood Alert - Severe Flood Warning</p>
            <p>We recommend you action your site-level flood emergency response plan (FERP) and take appropriate steps towards flood event mitigation.</p>
            <p>Such actions you may wish to take include:</p>
            <ul>
                <li>Ensure all staff and visitors are kept safe</li>
                <li>Follow advice from emergency services</li>
                <li>Action your crisis management and business continuity plans, where appropriate</li>
            </ul>"
    ];

    public function index($organisation_id)
    {
        // Clear UI notification if any
        $data['organisation_id'] = $organisation_id;
        $data['liberty_user_id'] = Session::get('user')->id;
        json_decode(Api::post('/api/v1/liberty-users/clear-previsico-ui-notif', $data));

        $data = json_decode(Api::get("api/v1/organisation/{$organisation_id}/flood-alerts"));

        // Display relevent message
        if ((int) $data->status == 204) {
            $data->noAccessOrNoAssets = true;
        }

        if (isset($data->organisation->logo) && $data->organisation->logo != 'none' && $data->organisation->logo != '') {
            $file=new FileUpload();
            $signedUrl = $file->link($data->organisation->logo);

            if ($signedUrl) {
                $data->organisation->logo = $signedUrl;
            } else {
                $data->organisation->logo = '/img/hero-image.png';
            }
        }
        
        // Transform data for view
        if (!empty($data->items)) {
            foreach ($data->items as &$item) {
                $this->transformData($item);
            }
        } else {
            $data->noAlerts = true;
        }

        return view(
            'flood-alerts/index',
            (array) $data
        );
    }

    public function showAlert($id)
    {
        $data = json_decode(Api::get("api/v1/organisation/flood-alerts/{$id}"));
        if (empty($data) || empty($data->status)) {
            throw new HttpException(500);
        }
        $data=$data->data;
        foreach($data->files as $file){
            $cloudname="{$data->organisation_id}/{$file->cloud_file_name}";
            $file->url=$this->file->link($cloudname);
        }

        $data->alert_type = self::ALERT_LEVEL_MAPPING[$data->alert_level];

        return view(
            'flood-alerts/view',
            (array)$data
        );
    }

    public function show($location_id)
    {
        $data = json_decode(Api::get("api/v1/location/{$location_id}/flood-alerts"));

        if (empty($data) || empty($data->status)) {
            throw new HttpException(500);
        }

        // Location with given ID does not exist, or is not part of the Previsico Assets list.
        if ((int) $data->status == 404) {
            return view('errors.404');
        }

        // There is no recent alert for the given location
        if ((int) $data->status == 204) {
            $data->noAlert = true;
        }

        if (isset($data->asset->organisation_location->organisation->logo) && $data->asset->organisation_location->organisation->logo != 'none' && $data->asset->organisation_location->organisation->logo != '') {
            $signedUrl = AWS::createClient('s3')->getObjectUrl(
                config('app.aws.bucket', null),
                $data->asset->organisation_location->organisation->logo,
                '+10 minutes'
            );

            if ($signedUrl) {
                $data->asset->organisation_location->organisation->logo = $signedUrl;
            } else {
                $data->asset->organisation_location->organisation->logo = '/img/hero-image.png';
            }
        }

        // Transform data for view
        $this->transformData($data);

        return view(
            'flood-alerts/show',
            (array) $data
        );
    }

    /**
     * Transforms API response to be fit for use by the view template
     *
     * @param  stdClass $transformData
     * @return void
     */
    protected function transformData(&$data)
    {
        if (!empty($data->asset->alert_level)) {
            $data->asset->alertLevelLabel          = self::ALERT_LEVEL_MAPPING[$data->asset->alert_level];
            $data->asset->alertLevelClass          = self::ALERT_LEVEL_CLASS_MAPPING[$data->asset->alert_level];
            $data->asset->alertLevelRecommendation = self::ALERT_LEVEL_RECOM_MAPPING[$data->asset->alert_level];
        }

        if (!empty($data->alert->date)) {
            $dataAlertDateOverride = date('Y-m-d H:i:s', strtotime($data->alert->date));
            $data->alert->date     = Carbon::createFromFormat('Y-m-d H:i:s', $dataAlertDateOverride);
        }

        if (!empty($data->alert->from_period)) {
            $dataAlertDateFromPeriod  = date('Y-m-d H:i:s', strtotime($data->alert->from_period));
            $data->alert->from_period = Carbon::createFromFormat('Y-m-d H:i:s', $dataAlertDateFromPeriod);
            $data->alert->flood_tiles = [
                'Y'  => $data->alert->from_period->format('Y'),
                'm'  => $data->alert->from_period->format('m'),
                'd'  => $data->alert->from_period->format('d'),
                'ih' => $data->alert->from_period->subHour()->format('H')
            ];

            // Reset from_period
            $data->alert->from_period->addHour();

            $data->alert->slider_start = 1;

            $data->alert->flood_hour = '00';

            // $timeHour = Carbon::createFromFormat(
            //     'Y-m-d H:i:s',
            //     $data->alert->from_period->format('Y-m-d')
            //     . ' '
            //     . Carbon::now()->format('H:i:s')
            // );

            $diffInHours = $data->alert->from_period->diffInHours(Carbon::now());
            if ($diffInHours >= 1 && $diffInHours <= 48) {
                $data->alert->slider_start = max($data->alert->slider_start, $diffInHours);

                if ($diffInHours < 10) {
                    $diffInHours = str_pad($diffInHours, 2, '0');
                }
                $data->alert->flood_hour = (string) $diffInHours;
            }
        }

        if (!empty($data->alert->to_period)) {
            $dataAlertDateToPeriod  = date('Y-m-d H:i:s', strtotime($data->alert->to_period));
            $data->alert->to_period = Carbon::createFromFormat('Y-m-d H:i:s', $dataAlertDateToPeriod);
        }

        // List slider dates
        if (!empty($data->alert->from_period) && !empty($data->alert->to_period)) {
            $data->alert->slider_dates = [$data->alert->from_period->format('d M Y')];

            $fromPeriodCopy = $data->alert->from_period->copy();

            // Move to from date to next day midnight
            $fromPeriodCopy->addDay()->startOfDay();

            // hard limit for the loop.
            $iterations = 0;

            while($iterations <= 3 && $fromPeriodCopy < $data->alert->to_period) {
                $data->alert->slider_dates[] = $fromPeriodCopy->format('d M Y');
                $fromPeriodCopy->addDay()->startOfDay();
                $iterations++;
            }

            $lastDate = $data->alert->to_period->format('d M Y');
            if (!in_array($lastDate, $data->alert->slider_dates)) {
                $data->alert->slider_dates[] = $lastDate;
            }
        }

        if (!empty($data->alert->created_at)) {
            $dataAlertCreatedAt      = date('Y-m-d H:i:s', strtotime($data->alert->created_at));
            $data->alert->created_at = Carbon::createFromFormat('Y-m-d H:i:s', $dataAlertCreatedAt);
        }

        if (!empty($data->alert->max_water_level_at)) {
            $dataAlertMaxWaterLevelAt        = date('Y-m-d H:i:s', strtotime($data->alert->max_water_level_at));
            $data->alert->max_water_level_at = Carbon::createFromFormat('Y-m-d H:i:s', $dataAlertMaxWaterLevelAt);
        }

        if (!empty($data->alert->details)) {
            $details   = json_decode($data->alert->details, true);
            $chartData = [];

            if (is_array($details) && !empty($details)) {
                $i    = 0;
                $step = 3;
                foreach ($details as $datetime => $level) {
                    $dtCarbon         = $this->getChartDateTime($datetime);
                    $chartData[]      = [
                        'xlabel'      => $i++ * $step,
                        'tooltipTime' => $dtCarbon->format('h:i a'),
                        'depth'       => $level,
                        'date'        => [
                            'Y' => $dtCarbon->format('Y'),
                            'm' => (intval($dtCarbon->format('n')) - 1), // JS Month-index is 0-based
                            'd' => $dtCarbon->format('d'),
                            'H' => $dtCarbon->format('H'),
                            'i' => $dtCarbon->format('i'),
                            's' => $dtCarbon->format('s')
                        ]
                    ];
                }
            }

            $data->alert->chartData = json_encode($chartData);
        }
    }

    public function getAlertUiStatus()
    {
        $data['email'] = Session::get('user')->email;
        return json_decode(Api::post('api/v1/liberty-users/get-previsico-ui-notif', $data));
    }

    public function getTile(
        $year,
        $month,
        $day,
        $issueHour,
        $floodHour,
        $z,
        $x,
        $y
    ) {
        $floodHour = ltrim($floodHour, '0'); // Strip unnecessary leading 0 if any

        $now = Carbon::now('Europe/London');
        if ($now->isDST()) {
            $issueHour = (int)$issueHour;
            if ($issueHour > 0 && $issueHour <= 13) {
                $issueHour = $issueHour - 1;
                $issueHour = (string)$issueHour < 10 ? '0' . $issueHour : $issueHour;
            }
        }

        $curl = curl_init();

        curl_setopt_array(
            $curl, [
            CURLOPT_RETURNTRANSFER  => 1,
            CURLOPT_FOLLOWLOCATION  => 1,
            CURLOPT_URL             => sprintf(
                \config('app.previsico.flood_tiles.url'),
                $year,
                $month,
                $day,
                $issueHour,
                $floodHour,
                $z,
                $x,
                $y
            ),
            CURLOPT_HTTPHEADER      => [
                                        \config('app.previsico.flood_tiles.key').':'.\config('app.previsico.flood_tiles.password'),
                                        'Accept:image/png'
                                    ]
            ]
        );

        $response = curl_exec($curl);
        $error    = curl_error($curl);
        $info     = curl_getinfo($curl);
        curl_close($curl);

        // If a curl error occured, return with 404
        if (!empty($error)) {
            Log::info("Previsico Flood Tile Error: /{$year}/{$month}/{$day}/{$issueHour}/{$floodHour}/{$z}/{$x}/{$y} - $error");
            return Response::make('', 404);
        }

        $httpCode    = !empty($info['http_code']) ? $info['http_code'] : 404;
        $contentType = !empty($info['content_type']) ? $info['content_type'] : 'application/json';

        if ($httpCode != 200 || $contentType != 'image/png') {
            return Response::make('', 404);
        }

        return Response::make($response, $httpCode, ['Content-type' => $contentType]);
    }

    /**
     * Get datetime object for chart data
     * First attempts to create object using d/m/Y H:i format
     * and tries using Y-m-d H:i if it fails
     *
     * @param  string $datetime
     * @return \Carbon\Carbon
     */
    protected function getChartDateTime($datetime)
    {
        // Attempt with d/m/Y H:i
        return Carbon::parse($datetime)->setTimezone('Europe/London');
        // try {
        //     $dt = Carbon::createFromFormat('d/m/Y H:i', $datetime);

        //     if ($dt && $dt->format('d/m/Y H:i') == $datetime) {
        //         return $dt;
        //     }

        //     return Carbon::createFromFormat('Y-m-d H:i', $datetime);
        // } catch (\InvalidArgumentException $e) {
        //     return Carbon::createFromFormat('Y-m-d H:i', $datetime);
        // }
    }
}
