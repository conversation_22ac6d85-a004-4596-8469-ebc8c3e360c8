<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Api;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Session;
class LinkController extends BaseController
{
    const TEMPLATE_DIRECTORY = '/link';

    const ROUTE_PREFIX = 'link';

    public function __construct(Request $request, Api $api)
    {
        parent::__construct($request);
        $this->api = $api;
    }

    public function index()
    {
        $links = json_decode($this->api->get('/api/v1/link/all'));
        if($links->response == "success") {
            return view(
                static::TEMPLATE_DIRECTORY . '/index',
                array(
                    'links' =>  $links->data
                )
            );
        }

        return Redirect::back()->with('error', 'Could not load link pages');
    }

    public function create($organisation_id = 0)
    {
        $sectors = json_decode(Api::get('/api/v1/sector/all'));
        $level1 = json_decode(Api::get('/api/v1/doc_level/null/1'));
        if($organisation_id != 0) {
            $organisation = json_decode($this->api->get('/api/v1/organisation/'.$organisation_id));
            if($organisation->response == "success") {
                return view(
                    static::TEMPLATE_DIRECTORY . '/create',
                    array('organisation' => $organisation->data, 'level1' => $level1->data)
                );
            }
        }

        return view(
            static::TEMPLATE_DIRECTORY . '/create',
            array(
                'level1' => $level1->data,
                'sectors' => $sectors->data
            )
        );
    }

    public function store(Request $request, $organisation_id = 0)
    {
        $rules = array(
            'name'          => 'required',
            'link'          => 'url|required',
            'level1_type'   =>  'required',
            // 'level2_type'   =>  'required', NOTE: Removed for FAS Portal Feature
            // 'level3_type'   =>  'required', NOTE: Removed for FAS Portal Feature
            'level4_type'   =>  'required',
            'sector'        =>  'required'
        );

        $validator = Validator::make($request->all(), $rules);

        if($validator->fails()) {
            return Redirect::back()->withErrors($validator->errors())->withInput($request->old());
        }
        $data = $request->all();

        if (empty($data['level2_type'])) {
            $data['level2_type'] = '';
        }

        if (empty($data['level3_type'])) {
            $data['level3_type'] = '';
        }

        if($organisation_id == 0) {
            $data['sector'] = implode(',', $data['sector']);
        }
        else
        {
            $data['sector'] = intval($data['sector']);
        }

        $data['user_id'] = Session::get('user')->id;
        $link = json_decode($this->api->post('/api/v1/link/create', $data));

        if($link->response == "success") {
            if($organisation_id != 0) {
                return Redirect::route('organisation.show', array('id' => $organisation_id))->with('success', 'Link created successfully');
            }
            return Redirect::route('document.link.index')->with('success', 'Link created successfully');
        }

        return Redirect::back()->with('error', $link->error)->withInput($request->old());
    }

    public function update(Request $request, $organisation_id = 0)
    {
        $rules = array(
            'name'          => 'required',
            'link'          => 'url|required',
            'level1_type'   =>  'required',
            // 'level2_type'   =>  'required', NOTE: Removed for FAS Portal Feature
            // 'level3_type'   =>  'required', NOTE: Removed for FAS Portal Feature
            'level4_type'   =>  'required',
            'sector'        =>  'required'
        );

        $validator = Validator::make($request->all(), $rules);

        if($validator->fails()) {
            return Redirect::back()->withErrors($validator->errors())->withInput($request->old());
        }

        $data = $request->all();

        if (empty($data['level2_type'])) {
            $data['level2_type'] = '';
        }

        if (empty($data['level3_type'])) {
            $data['level3_type'] = '';
        }

        if(!isset($data['organisation_id'])) {
            $data['sector'] = implode(',', $data['sector']);
        }
        else
        {
            $data['sector'] = intval($data['sector']);
        }
        unset($data['_token']);
        $data['user_id'] = Session::Get('user')->id;
        $link = json_decode($this->api->post('/api/v1/link/update', $data));

        if($link->response == "success") {
            if($organisation_id != 0) {
                return Redirect::route('organisation.show', array('id' => $organisation_id))->with('success', 'Link updated successfully');
            }
            return Redirect::route('document.link.index')->with('success', 'Link successfully updated');
        }

        return Redirect::back()->with('error', $link->error);
    }


    public function edit($organisation_id = 0,$linkID)
    {
        $level1 = json_decode(Api::Get('/api/v1/doc_level/null/1'));
        if ($organisation_id != 0) {
            $link = json_decode($this->api->get('/api/v1/link/find/'.$linkID));
            if ($link->response == "success") {
                if($link->data->organisation_id == $organisation_id) {
                    $organisation = json_decode($this->api->get('/api/v1/organisation/' . $organisation_id));
                    $doc_levels = json_decode(Api::Get('/api/v1/doc_levels_all/' . $link->data->level1_type . '/' . $link->data->level2_type . '/' . $link->data->level3_type . '/' . $link->data->level4_type));
                    $doc_levels = $doc_levels->data;
                    if($organisation->response == "success") {
                        return view(
                            static::TEMPLATE_DIRECTORY . '/edit',
                            array('link' => $link->data, 'organisation' => $organisation->data,'level1' => $level1->data, 'levels' => $doc_levels)
                        );
                    }

                }
                return Redirect::back()->with('error', 'Link does not exist');
            }
            return Redirect::back()->with('error', 'Link does not exist');
        }

        $link = json_decode($this->api->get('/api/v1/link/find/' . $linkID));
        if ($link->response == "success") {
            if ($link->data->organisation_id == null) {
                $type1 = $link->data->level1_type;
                $type2 = isset($link->data->level2_type) ? $link->data->level2_type : 0;
                $type3 = isset($link->data->level3_type) ? $link->data->level3_type : 0;
                $type4 = $link->data->level4_type;

                $doc_levels = json_decode(Api::get('/api/v1/doc_levels_all/' . $type1 . '/' . $type2 . '/' . $type3 . '/' . $type4));

                // FOR FAS PORTAL LOGIC
			    $isFasDo      = false;
                $type3Options = [];
                if (!empty($doc_levels->data) 
                    && array_key_exists($doc_levels->data->level1->level_name, config('client_dashboard.fas_pi_risk_guidances'))) {
                    $type3Options = json_decode(Api::get('/api/v1/doc_level/null/4/' . $link->data->level1_type));
                    $isFasDo = true;
                }
                
                $doc_levels = $doc_levels->data;
                $sectors = json_decode(Api::get('/api/v1/sector/all'));

                if ($sectors->response == "success") {
                    return view(
                        static::TEMPLATE_DIRECTORY . '/edit', [
                            'link' => $link->data,
                            'isFasDo' => $isFasDo,
                            'level1' => $level1->data,
                            'level3Options' => !empty($type3Options->data) ? $type3Options->data : [],
                            'levels' => $doc_levels,
                            'sectors' => $sectors->data
                        ]
                    );
                }
            }
            return Redirect::back()->with('error', 'Link does not exist');
        }
        return Redirect::back()->with('error', 'Link does not exist');
    }

    public function destroy($organisation_id = 0, $linkID = 0)
    {
        if($organisation_id != 0) {
            $link = json_decode($this->api->get('/api/v1/link/find/'.$linkID));
            if($link->response == "success") {
                if($link->data->organisation_id == $organisation_id) {
                    $delete = json_decode($this->api->post('/api/v1/link/delete', array('id' => $link->data->id)));
                    if($delete->response == "success") {
                        return Redirect::back()->with('success', 'Link successfully deleted');
                    }
                    return Redirect::back()->with('error', 'Error deleting link');
                }
                return Redirect::back()->with('error', 'Link does not exist');
            }
            return Redirect::back()->with('error', 'Link does not exist');
        }

        $link = json_decode($this->api->get('/api/v1/link/find/'.$linkID));
        if($link->response == "success") {
            $delete = json_decode($this->api->post('/api/v1/link/delete', array('id' => $link->data->id)));
            if($delete->response == "success") {
                return Redirect::route('document.link.index')->with('success', 'Link successfully deleted');
            }
            return Redirect::back()->with('error', 'Error deleting link');
        }

        return Redirect::back()->with('Link does not exist');
    }
}
