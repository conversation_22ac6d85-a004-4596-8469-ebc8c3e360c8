<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use App\Models\FileUpload;
use Illuminate\Support\Str;
use App\Models\Api;
class OrganisationReportController extends BaseController
{
    const VISIBILITY_INTERNAL = 'Internal - This report is available only to Liberty Users';
    const VISIBILITY_EXTERNAL = 'External - This report is available to Liberty Users and to Client Users';

    /**
     * @var array
     */
    protected $types = [
        'TPA Claims MI'             => 'TPA Claims MI',
        'Risk Engineering Overview' => 'Risk Engineering Overview',
        'Exposure Management'       => 'Exposure Management',
        'Graydon Report'            => 'Graydon Report',
        'Liberty MI'                => 'Liberty MI',
        'Other'                     => 'Other',
    ];

    const MENTION_TAG = 'b';
    private $liberty_users;

    public function __construct(Request $request, FileUpload $fileUpload)
    {
        parent::__construct($request);

        $this->files = $fileUpload;
    }

    public function create($organisation_id)
    {
        $this->setLibertyUsers();

        return view(
            'organisation/reports/create', [
            'types' => $this->types,
            'organisation_id' => $organisation_id,
            'users' => $this->liberty_users,
            'types' => $this->types,
            'tag' => self::MENTION_TAG
            ]
        );
    }

    public function store(Request $request, $organisation_id)
    {
        $data = $request->except('_token');
        $data['upload_date'] = date('Y-m-d H:i:s');
        $data['organisation_id'] = $organisation_id;

        $validator = \Illuminate\Support\Facades\Validator::make(
            $data, [
            'title' => 'required',
            'type' => 'required',
            'visibility' => 'required',
            'report' => 'required',
            ]
        );

        if ($validator->fails()) {
            return Redirect::back()
                ->withErrors($validator)
                ->withInput($request->old());
        }

        if ($request->hasFile('report')) {
            $file = $request->file('report');

            $data['filename'] = $request->file('report')->getClientOriginalName();
            $data['filesize'] = $request->file('report')->getSize();

            $cloudname = Str::uuid()->toString();

            $cloudpath = $data['organisation_id'].'/'.$cloudname.'/'.$data['filename'];

            if (! is_bool($this->files->upload($file->getRealPath(), $cloudpath))) {
                return Redirect::back()->with('error', 'Could not upload report')->withInput($request->old());
            }

            $data['cloudname'] = $cloudname;
        }

        unset($data['report']);

        $user=\Illuminate\Support\Facades\Session::get('user');
        $data['author']=(isset($user->first_name) ? $user->first_name : '') .' '. (isset($user->last_name) ? $user->last_name : '');

        $response = json_decode(Api::post('/api/v1/organisation/' . $data['organisation_id'] . '/reports', $data));

        return Redirect::to('organisation/' . $response->data->organisation_id);
    }

    public function show($organisation_id, $id)
    {
        $report = json_decode(Api::get('api/v1/organisation/' . $organisation_id . '/reports/' . $id))->data;

        $cloudpath = $report->organisation_id.'/'.$report->cloudname.'/'.$report->filename;

        $report->download = $this->files->link($cloudpath, '2 hours');

        return view(
            'organisation/reports/show', [
            'types' => $this->types,
            'report' => $report,
            'visibility' => [
                'internal' => self::VISIBILITY_INTERNAL,
                'external' => self::VISIBILITY_EXTERNAL
            ]
            ]
        );
    }

    public function edit($organisation_id, $id)
    {
        $report = json_decode(Api::get('api/v1/organisation/' . $organisation_id . '/reports/' . $id))->data;
        $cloudpath = $report->organisation_id.'/'.$report->cloudname.'/'.$report->filename;
        $report->download = $this->files->link($cloudpath, '2 hours');

        $this->setLibertyUsers();

        return view(
            'organisation/reports/edit', [
            'types' => $this->types,
            'report' => $report,
            'users' => $this->liberty_users,
            'tag' => self::MENTION_TAG
            ]
        );
    }

    public function update(Request $request, $organisation_id, $id)
    {
        $data = $request->except('_token');
        $data['organisation_id'] = $organisation_id;

        $rules = [
            'title' => 'required',
            'type' => 'required',
            'visibility' => 'required',
        ];

        if (isset($data['report'])) {
            $data['upload_date'] = date('Y-m-d H:i:s');

            $rules['report'] = 'required';
        }

        $validator = \Illuminate\Support\Facades\Validator::make($data, $rules);

        if ($validator->fails()) {
            return Redirect::back()
                ->withErrors($validator)
                ->withInput($request->old());
        }

        if ($request->hasFile('report')) {
            $file = $request->file('report');

            $data['filename'] = $request->file('report')->getClientOriginalName();
            $data['filesize'] = $request->file('report')->getSize();

            $cloudname = Str::uuid()->toString();

            $cloudpath = $data['organisation_id'].'/'.$cloudname.'/'.$data['filename'];

            if (! is_bool($this->files->upload($file->getRealPath(), $cloudpath))) {
                return Redirect::back()
                    ->with('error', 'Could not upload report')
                    ->withInput($request->old());
            }

            $data['cloudname'] = $cloudname;
        }

        unset($data['report']);

        $user=\Illuminate\Support\Facades\Session::get('user');
        $data['author']=(isset($user->first_name) ? $user->first_name : '') .' '. (isset($user->last_name) ? $user->last_name : '');

        $response = json_decode(Api::post('/api/v1/organisation/' . $data['organisation_id'] . '/reports/' . $id, $data));

        return Redirect::to('organisation/' . $response->data->organisation_id);
    }

    public function destroy(Request $request, $organisation_id, $id)
    {
        $data = $request->except('_token');

        $response = json_decode(Api::post('/api/v1/organisation/' . $organisation_id . '/reports/' . $id . '/destroy', $data));

        return Redirect::to('organisation/' . $response->data->organisation_id);
    }

    private function setLibertyUsers()
    {
        $liberty_users_admin = json_decode(Api::get('api/v1/liberty-users/all/1/50/admin'));
        $liberty_users_rw = json_decode(Api::get('api/v1/liberty-users/all/1/50/risk-engineer'));
        $liberty_users_uw = json_decode(Api::get('api/v1/liberty-users/all/1/50/underwriter'));
        $liberty_users_admin_filtered = !empty($liberty_users_admin) ? $this->filterUserColumns($liberty_users_admin) : [];
        $liberty_users_rw_filtered = !empty($liberty_users_rw) ? $this->filterUserColumns($liberty_users_rw) : [];
        $liberty_users_uw_filtered = !empty($liberty_users_uw) ? $this->filterUserColumns($liberty_users_uw) : [];

        $liberty_users = array_merge($liberty_users_admin_filtered, $liberty_users_rw_filtered, $liberty_users_uw_filtered);
        if ($liberty_users) {
            $this->liberty_users = $liberty_users;
        }
    }

    private function filterUserColumns($users)
    {
        $formatted_users = [];
        foreach ($users->data as $user) {
            $formatted_users[] = $this->userColumns($user);
        }
        return $formatted_users;
    }

    private function userColumns($user)
    {
        return [
            'id' => $user->id,
            'value' => sprintf(
                '%s %s &lt;%s&gt;',
                $user->first_name,
                $user->last_name,
                $user->email
            )
        ];
    }
}
