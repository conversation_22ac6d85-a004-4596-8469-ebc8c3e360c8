<?php

namespace App\Http\Controllers;
use Artisaninweb\SoapWrapper\SoapWrapper;
use Illuminate\Http\Request;

class SafetymediaController extends BaseController
{

    public function demo()
    {
        // Add a new service to the wrapper
        SoapWrapper::add(
            function ($service) {
                $service
                    ->name('training')
                    ->wsdl('https://libertymutual-test.safetylearning.co.uk/api/1.2/TrainingManagement?wsdl');
            }
        );


        $data = [
            'serviceCredentials' => ['username' => 'InsightAssured', 'password' => 'wKtbDt7PtkdAPkxZ'],
            // 'users'              => ['items' =>
            //                             [
            //                                 'id' => '1',
            //                                 'username'   => '<EMAIL>',
            //                                 'password'     => 'password',
            //                                 'forename'       => 'Shashi',
            //                                 'surname'       =>  'Saurav',
            //                                 'email'         =>  '<EMAIL>',
            //                                 'adminLevel'    =>  'admin'
            //                             ]
            //                         ]
        ];

        // Using the added service
        SoapWrapper::service(
            'training', function ($service) use ($data) {
                //var_dump($service->getFunctions());
                print_r($service->call('GetCourses', [$data]));
            }
        );
    }

    public function addUser()
    {
        // Add a new service to the wrapper
        SoapWrapper::add(
            function ($service) {
                $service
                    ->name('users')
                    ->wsdl('https://libertymutual-test.safetylearning.co.uk/api/1.2/UserManagement?wsdl');
            }
        );


        $data = [
            'serviceCredentials' => ['username' => 'InsightAssured', 'password' => 'wKtbDt7PtkdAPkxZ'],
            'users'              => ['items' =>
                                        [
                                            'id' => '1',
                                            'username'   => '<EMAIL>',
                                            'password'     => 'password',
                                            'forename'       => 'Shashi',
                                            'surname'       =>  'Saurav',
                                            'email'         =>  '<EMAIL>',
                                            'disabled'      =>  '0',
                                            'departmentName'=>  'Test',
                                            'preferredLanguage' => 'English',
                                            'adminLevel'    =>  'admin',
                                        ]
                                    ]
        ];

        // Using the added service
        SoapWrapper::service(
            'users', function ($service) use ($data) {
                //var_dump($service->getFunctions());
                print_r($service->call('AddUser', [$data]));
            }
        );
    }

    public function editUser()
    {
        // Add a new service to the wrapper
        SoapWrapper::add(
            function ($service) {
                $service
                    ->name('users')
                    ->wsdl('https://libertymutual-test.safetylearning.co.uk/api/1.2/UserManagement?wsdl');
            }
        );


        $data = [
            'serviceCredentials' => ['username' => 'InsightAssured', 'password' => 'wKtbDt7PtkdAPkxZ'],
            'users'              => ['items' =>
                                        [
                                            'id' => '1',
                                            'username'   => '<EMAIL>',
                                            'password'     => 'password',
                                            'forename'       => 'Shashi',
                                            'surname'       =>  'Saurav',
                                            'email'         =>  '<EMAIL>',
                                            'disabled'      =>  '0',
                                            'departmentName'=>  'Test',
                                            'preferredLanguage' => 'English',
                                            'adminLevel'    =>  'admin',
                                        ]
                                    ]
        ];

        // Using the added service
        SoapWrapper::service(
            'users', function ($service) use ($data) {
                //var_dump($service->getFunctions());
                print_r($service->call('EditUser', [$data]));
            }
        );
    }

}
