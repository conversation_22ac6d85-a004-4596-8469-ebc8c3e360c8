<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Models\Api;
use Illuminate\Support\Facades\Redirect;
use PragmaRX\Google2FA\Google2FA;

class ExternalSurveyorController extends BaseResourceController
{
    const
    NAV_ID = 'our-team',
    TEMPLATE_PATH = '/external-surveyors',
    ROUTE_PREFIX = 'external-surveyors';

    public function __construct(Request $request)
    {
        BaseController::__construct($request);
    }


    /**
     * Delete a user
     *
     * @param $id
     *
     * @return mixed
     */

    public function destroy($id)
    {
        $logged_in_user = Session::get('user');

        if (isset($logged_in_user->id) && $logged_in_user->id != $id) {
            return parent::destroy($id);
        } else {
            return Redirect::route(
                static::ROUTE_PREFIX . '.index'
            )->with(
                'error',
                'You are currently logged in as this External Surveyor'
            );
        }
    }

    public function generateLink($userID)
    {
        if(isset($userID)) {
            //$data = ['id' => $userID];
            $response = json_decode(Api::get('/api/v1/external-surveyors/link/'.$userID));

            // print_r(Api::get('api/v1/liberty-users/link/'.$userID)); exit;

            if($response->response == "success") {
                return Response::json(
                    [
                    'response'  => 'success',
                    'data'      =>  $response->data
                    ]
                );
            }
        }
    }

    /**
     * On update success
     *
     * @param array $data
     */
    public function onUpdateSuccess($data)
    {
        if (isset($data['send_invite'])) {
            Api::get(static::get_api_uri('send-welcome/' . $data['id']));
        }

        $logged_in_user = Session::get('user');

        if ($logged_in_user->id == $data['id']) {
            foreach($data as $key => $value) {
                if (isset($logged_in_user->$key)) {
                    $logged_in_user->$key = $value;
                }
            }
            Session::put('user', $logged_in_user);
        }
    }

    /**
     * Parse data before storage
     *
     * @param  array $data
     * @return array parsed version of $data
     */
    public function parseDataBeforeStorage($data)
    {
        if (isset($data['external_survey_company'])) {
            $data['external_survey_company_id'] = $data['external_survey_company'];
            unset($data['external_survey_company']);
        } else {
            $data['external_survey_company_id'] = 0;
        }

        $data['claims_notification'] = (int)(isset($data['claims_notification']));

        return $data;
    }

    /**
     * Get additional view parameters for a specific view (index/create/edit)
     *
     * @param  string $view
     * @param  array  $params
     * @return array of additional parameters
     */
    public function getRouteParams(Request $request, $view, array $params = [])
    {
        return array_merge(
            $params, [
            'options' => [
            'external_survey_company' => static::getExternalSurveyCompanyOptions(),
            'role'                    => static::getRoleOptions(),
            ],
            ]
        );
    }

    public function getAdditionalViewParams(string $view, $params = [])
    {
        return array_merge(
            (array)$params, [
            'options' => [
            'external_survey_company' => static::getExternalSurveyCompanyOptions(),
            'role'                    => static::getRoleOptions(),
            ],
            ]
        );
    }



    /**
     * Get validation rules for a specific method (store/update/etc)
     *
     * @param  string $method
     * @return array of validation rules
     */
    public function getValidatorRules(Request $request, $method)
    {
        return [
        'first_name' =>  'required',
        'last_name'  =>  'required',
        'email'      =>  'required|email',
        'phone'      =>  'required',
        ];
    }

    private static function getExternalSurveyCompanyOptions()
    {
        return json_decode(
            Api::get(
                static::get_api_uri('options', 'external-survey-companies')
            )
        );
    }

    private static function getRoleOptions()
    {
        return static::buildOptionsFromKeys(
            [
            'admin',
            'surveyor',
            ]
        );
    }

    public function resetTfa($user_id)
    {
        $key = (new Google2FA)->generateSecretKey();

        $api = json_decode(
            Api::post(
                '/api/v1/reset-es-tfa?user_id=' . $user_id . '&key=' . $key
            )
        );

        if ($api->response == 'success') {
            return Redirect::route(
                static::ROUTE_PREFIX . '.index'
            )->with(
                'success',
                $api->message
            );
        }

        return Redirect::route(
            static::ROUTE_PREFIX . '.index'
        )->with(
            'error',
            $api->message
        );
    }
}
