<?php

namespace App\Http\Controllers;
use App\Models\Api;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

class CommunitySectorController extends BaseController
{
    // Test CMS API Calls will be removed in the future
    public function index()
    {
        // This is a temporary controller to test the CMS API Calls
        // Community Sectors
        // $response = json_decode(Api::get('api/v1/community/get-sector'));
        // $response = json_decode(Api::get('api/v1/community/get-community-tag'));
        // $response = json_decode(Api::get('api/v1/community/messages/get-upcoming-event'));

        $type = 'webinar'; // parameter
        $response = json_decode(Api::get('api/v1/community/messages/get-popular-event/'.$type));
        echo "<pre>";
        print_r(Api::get('api/v1/community/messages/get-popular-event/'.$type));
    }

    public function subscribe(Request $request)
    {
        return $this->setSubscription($request, true);
    }

    public function unsubscribe(Request $request)
    {
        return $this->setSubscription($request, false);
    }

    public function orgSubscribe(Request $request)
    {
        return $this->setSubscription($request, true, true);
    }

    public function orgUnsubscribe(Request $request)
    {
        return $this->setSubscription($request, false, true);
    }

    /**
     * Sets the subscription for Users or Organisations
     *
     * @param  Request  $request
     * @param $subscribe
     * @param $isOrg
     * @return \Illuminate\Http\JsonResponse
     */
    private function setSubscription(Request $request, $subscribe, $isOrg = false)
    {
        try {
            $user = Session::get('user');
            $entity = $isOrg ? $request->get('org') : $user->id;

            $response = json_decode(
                Api::post(
                    route(
                        $this->getRoute($subscribe, $isOrg),
                        $this->getRouteParams($request, $entity, $isOrg), false
                    )
                )
            );

            if ($response && $response->message === 'success') {
                return response()->json(
                    [
                    'status' => $subscribe ? 'subscribed' : 'unsubscribed',
                    'id' => $this->getSubscriptionId($request),
                    ]
                );
            }

            return response()->json(['status' => $subscribe ? 'subscribing failed' : 'unsubscribing failed'], 501);
        } catch (UnprocessableEntityHttpException $e) {
            return response()->json(['status' => 'fail', 'message' => $e->getMessage()], 422);
        }
    }


    /**
     * Returns the ID of the Community Sector Category
     *
     * @return string $id   MongoDB ID of the sector category
     * @throws UnprocessableEntityHttpException
     */
    private function getSubscriptionId(Request $request)
    {
        $id = $request->get('id');
        if ($id) {
            return $id;
        }

        throw new UnprocessableEntityHttpException('Missing variable: id');
    }

    /**
     * Determines the parameters required by the API based on the type (user or organisation)
     *
     * @param  bool  $isOrg
     * @param  $entity
     * @return array
     */
    private function getRouteParams(Request $request, $entity, bool $isOrg = false)
    {
        $data['sector_id'] = $this->getSubscriptionId($request);
        if ($isOrg) {
            $data['organisation'] = $entity;
        } else {
            $data['user'] = $entity;
            $data['type'] = User::getUserType();
        }

        return $data;
    }

    /**
     * Determines the route of the API based on the type (user or organisation)
     *
     * @param  $subscribe
     * @param  bool $isOrg
     * @return string
     */
    private function getRoute($subscribe, $isOrg = false)
    {
        if ($isOrg) {
            if ($subscribe) {
                return 'api.community.sector.org-subscribe';
            }
            return 'api.community.sector.org-unsubscribe';
        }

        // users
        if ($subscribe) {
            return 'api.community.sector.subscribe';
        }
        return 'api.community.sector.unsubscribe';
    }
}
