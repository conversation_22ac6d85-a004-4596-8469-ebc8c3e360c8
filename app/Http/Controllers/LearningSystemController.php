<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;
use App\Models\Survey;
use App\Models\Api;
use Illuminate\Support\Facades\View;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\LearningSystemReportsExport;
class LearningSystemController extends BaseResourceController
{
    const
    TEMPLATE_PATH = '/learning',
    ROUTE_PREFIX = 'learning';

    public function __construct(Request $request, Survey $survey)
    {
        View::share(
            'main_class',
            'learning'
        );

        parent::__construct($request, $survey);
    }

    public function categories()
    {

    }

    public function progress()
    {
        $data = [];
        $api_calls = [
            'organisations' => ['options', 'organisation'],
            'courses'       => ['courseProgress', 'learning']
        ];

        foreach($api_calls as $key => $values) {
            $api_call = json_decode(Api::get(static::get_api_uri($values[0], $values[1])));
            if (isset($api_call->response)) {
                if (isset($api_call->data->branches)) {
                    $data['branches'] = $api_call->data->branches;
                    $data[$key]       = $api_call->data->courses;
                } else {
                    $data[$key] = $api_call->data;
                }
            } else {
                $data[$key] = $api_call;
            }
        }

        return view(
            static::TEMPLATE_PATH . '/progress/index', $data
        );
    }

    public function reports(Request $request)
    {
        $params = $request->all();

        $data = array();

        $courseLine = count($params)>0?'?'.http_build_query($params, '', '&'):'';
        $api_calls = array(
            'courses'       => ['courseReports' . $courseLine, 'learning'],
            'organisations' => ['options', 'organisation']
        );

        if ($request->has('course')) {
            array_push($api_calls, array('lessons' => array($request->get('course') . '/lessons/all', 'learning/course')));
        }
        foreach ($api_calls as $key => $values) {
            $api_call = json_decode(
                Api::get(
                    static::get_api_uri($values[0], $values[1])
                )
            );

            if (isset($api_call->response)) {
                if(isset($api_call->data->branches)) {
                    $data['branches'] = $api_call->data->branches;
                    $data[$key] = $api_call->data->courses;
                }else{
                    $data[$key] = $api_call->data;
                }
            } else {
                $data[$key] = $api_call;
            }
        }
        return view(
            static::TEMPLATE_PATH . '/reports/index', $data
        );
    }

    public function exportReports(Request $request)
    {
        $params = $request->all();

        $data = array();

        $courseLine = count($params) > 0 ? '?' . http_build_query($params, '', '&') : '';
        $api_calls = array(
            'courses'       => ['reports'.$courseLine, 'learning/course']
        );

        if ($request->has('course')) {
            array_push($api_calls, array('lessons' => array($request->get('course') . '/lessons/all', 'learning/course')));
        }
        foreach ($api_calls as $key => $values) {
            $api_call = json_decode(
                Api::get(
                    static::get_api_uri($values[0], $values[1])
                )
            );

            if (isset($api_call->response)) {
                $data[$key] = $api_call->data->courses;
            }
        }
        $courses = $data['courses'];
        //echo '<pre>';
        //dd($courses);
        $data2 = array();
        array_push($data2, array('Course title','Views','Test attempts','Pass rate'));
        foreach($courses as $key => $course){
            $push = array(
                $course->title,
                $course->views>0?$course->views:'0',
                $course->testAttempts>0?$course->testAttempts:'0',
                $course->passRate>0?$course->passRate:'0');
            array_push($data2, $push);
            foreach($course->lessons as $lessonKey => $lesson){
                $push = array(
                    '     '.$lesson->title,
                    $lesson->views>0?$lesson->views:'0',
                    $lesson->testAttempts>0?$lesson->testAttempts:'0',
                    $lesson->passRate>0?$lesson->passRate:'0');
                array_push($data2, $push);
            }
        }


        $excelRes = Excel::download(new LearningSystemReportsExport($data2), 'Reports-' . date('Y-m-d H:i:s') . '.xlsx');
    }
}
