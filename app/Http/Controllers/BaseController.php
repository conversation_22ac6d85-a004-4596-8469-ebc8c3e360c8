<?php

namespace App\Http\Controllers;

use App\Models\FileUpload;
use App\Models\LessonObjects;
use App\Models\User;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Str;
use Monolog\Logger;
use Monolog\Handler\StreamHandler;
use Illuminate\Http\Request;
use Illuminate\Contracts\View\View as ContractView;

class BaseController extends Controller
{
    const NAV_ID = '';
    const API_NAMESPACE = '';
    public $log;

    public FileUpload $files;
    private Application|Factory|ContractView $layout;
    public LessonObjects $lessonObjects;

    public function __construct(Request $request)
    {
        View::share(
            'nav',
            (defined('static::NAV_ID')
                ? static::NAV_ID
                : strstr($request->path() . '/', '/', true))
        );

        View::share(
            'layout',
            (defined('static::RESPOND_TO_AJAX') && static::RESPOND_TO_AJAX && $request->ajax()
                ? 'modal'
                : 'layout')
        );

        $this->log = new Logger('Action');
        $this->log->pushHandler(
            new StreamHandler(
                storage_path() . '/logs/action-' . date('d-m-Y') . '.log',
                Logger::INFO
            )
        );


        if (Session::has('user')) {
            $method = $request->method();
            $path = $request->path();

            $data = [
                'user_id' => Session::get('user')->id,
                'user_email' => Session::get('user')->email,
                'role' => Session::get('role'),
                'referer' => $_SERVER["HTTP_REFERER"] ?? 'direct',
                'segments' => $request->segments(),
            ];

            // $this->log->addInfo($method.' '.$path, $data);

            $data['method'] = $method;
            $data['path'] = $path;

        }
    }

    /**
     * Setup the layout used by the controller.
     *
     * @return void
     */
    protected function setupLayout(Request $request)
    {
        if (!is_null($this->layout)) {
            $this->layout = view($this->layout);
        }
        View::share('body_class', static::routeClass());

        // limits for pagination
        if ($request->has('limit')) {
            Session::put('limit', $request->get('limit'));
        }
    }

    /**
     * add the current controller-action to body tag
     *
     * @return string
     */
    public static function routeClass()
    {
        $routeArray = Str::parseCallback(Route::currentRouteAction(), null);
        $controller = null;
        $action = null;
        if (last($routeArray) != null) {
            $controller = str_replace('Controller', '', class_basename(head($routeArray)));
            $action = str_replace(['get', 'post', 'patch', 'put', 'delete'], '', last($routeArray));
        }
        return Str::slug($controller . '-' . $action);
    }

    /**
     * get route prefix
     * eg. 'resource.route' -> 'resourse' or 'resource.sub_resource.route' -> 'resource.sub_resource'
     * or set via ROUTE_PREFIX class constant.
     *
     * @return string
     */
    protected static function getRoutePrefix()
    {
        if (defined('static::ROUTE_PREFIX')) {
            return static::ROUTE_PREFIX;
        } else {
            return preg_replace('/\.[^\.]*$/', '', Route::currentRouteName());
        }
    }

    /**
     * Get parent route prefix (eg. 'first.second.third' -> 'first.second')
     *
     * @return integer $id
     */
    protected static function getParentRoutePrefix()
    {
        return preg_replace('/\.[^\.]*$/', '', static::getRoutePrefix());
    }

    /**
     * Build a route array to use for routes in view templates
     *
     * @param  integer  $id
     */
    protected function getRouteArray($route)
    {

        //todo: Fix this if the use case comes up where the parameters are not being returned
        $route_array = array_slice(Route::current()->parameters(), -1);
        //        $route_array = array_slice(Route::current()->parameters(),-1) + ['id' => array_pop((array_slice(Route::current()->parameters(),-1)))];

        array_unshift(
            $route_array,
            sprintf('%s.%s', static::getRoutePrefix(), $route)
        );

        return $route_array;
    }

    protected static function get_api_uri($uri = '', $namespace = null)
    {
        if (!$namespace) {
            $namespace = defined('static::API_NAMESPACE') !== null && static::API_NAMESPACE !== ''
                ? static::API_NAMESPACE
                : static::getRoutePrefix();
        }

        return sprintf(
            'api/v1/%s',
            implode('/', array_filter([$namespace, $uri]))
        );
    }

    protected static function buildOptionsFromKeys($keys)
    {
        return array_combine(
            $keys,
            array_map(
                function ($value) {
                    return ucwords(str_replace('-', ' ', $value));
                }, $keys
            )
        );
    }


    public function processSection($section, $course_id, $lesson_id)
    {
        switch ($section->type) {
            case 'video-upload':
                $section->url = isset($section->uuid)
                    ? $this->lessonObjects->getImage($section->uuid, $course_id, $lesson_id)
                    : '';
                $section->posterUrl = isset($section->uuid)
                    ? $this->lessonObjects->getImage($section->uuid . '-00001.png', $course_id, $lesson_id)
                    : '';
                break;
            case 'audio-upload':
                if (isset($section->file_name)) {
                    $section->url = $this->lessonObjects->getImage($section->file_name, $course_id, $lesson_id);
                }
                break;
            case 'image':
                if (isset($section->file_name)) {
                    $section->url = $this->lessonObjects->getImage($section->file_name, $course_id, $lesson_id);
                }
                break;
            case 'document-upload':
                if (isset($section->documents) && is_array($section->documents)) {
                    foreach ($section->documents as $key => $document) {
                        $section->documents[$key]->url = $this->lessonObjects->getImage(
                            $document->file_name,
                            $course_id, $lesson_id
                        );
                    }
                } elseif (isset($section->documents)) {
                    foreach ($section->documents as $key => $document) {
                        $section->documents->$key->url = $this->lessonObjects->getImage(
                            $document->file_name,
                            $course_id, $lesson_id
                        );
                    }
                }
                break;
        }

        return $section;
    }

    public function phantomPDF(Request $request, $url, $params = [])
    {
        $extension = 'jpg';
        $element = 'download';
        $action = $params['pdfaction'] ?? null;
        if (isset($params)) {
            $extension = $params['extension'] ?? 'jpg';
            $element = $params['element'] ?? null;
            $action = $params['pdfaction'] ?? 'download';
        }
        if (trim($element) != '' && $element != null) {
            $extension = 'jpg';
        } else {
            $element = '';
        }
        $fileName = '../app/storage/' . uniqid() . '_pdf.' . $extension;
        $exec = 'phantomjs js/jsrender.js ' . urlencode($url) . ' ' . $fileName . ' laravel_session=' . $_COOKIE['laravel_session'] . ' ' . $element;
        exec($exec, $result);
        $newResult = '';
        if (is_array($result)) {
            //if(count($result) > 1){
            foreach ($result as $row) {
                $row = json_decode($row);
                if ($row != null) {
                    $newResult = $row;
                }
            }
            /*}else{
                if(isset($result[0])){
                    $newResult = json_decode($result[0]);
                }
            }*/
        }
        $result = $newResult;
        if (is_file($fileName) && isset($result->response) && $result->response == 'success') {
            if ($extension == 'jpg') {
                $width = 1024;
                $height = 1400;

                $source = @imagecreatefromjpeg($fileName);
                $source_width = imagesx($source);
                $source_height = imagesy($source);
                if ($source_width < $width) {
                    $width = $source_width;
                }
                $html = '<style>@page { margin: 0px; }
                            body { margin: 0px; }</style>';
                for ($col = 0; $col < $source_width / $width; $col++) {
                    for ($row = 0; $row < $source_height / $height; $row++) {
                        //$fn = sprintf( "img%02d_%02d.jpg", $col, $row );
                        //$html .= '<img src="'.$fn.'" width="750"/>';
                        $newheight = $height;
                        if (($row + 1) * $height > $source_height) {
                            $newheight = $source_height - ($row * $height);
                        }
                        $im = @imagecreatetruecolor($width, $newheight);
                        imagecopyresized(
                            $im, $source, 0, 0,
                            $col * $width, $row * $height, $width, $newheight,
                            $width, $newheight
                        );

                        //imagejpeg( $im, $fn );

                        ob_start();
                        imagejpeg($im);
                        $data = ob_get_contents();
                        ob_end_clean();
                        $data = base64_encode($data);
                        $heigthShow = '1055';
                        if ($newheight < $height) {
                            //print_r($newheight).'<br>';
                            $heigthShow = ($newheight * 100) / $height;
                            //print_r($heigthShow).'<br>';
                            $heigthShow = ($heigthShow * 1055) / 100;
                            //dd($heigthShow);
                        }
                        $html .= "<img src='data:image/jpg;base64,$data' width='815' height='" . $heigthShow . "'>";

                        imagedestroy($im);
                    }
                }
                unlink($fileName);
                $orientation = $request->has('orientation')
                    ? $request->get('orientation')
                    : 'portrait';
                $pdf = Pdf::loadHTML($html)->setPaper('Letter')->setOrientation($orientation);
                if ($action == 'stream') {
                    return $pdf->stream();
                } else {
                    if ($action == 'download') {
                        return $pdf->download();
                    }
                }
            } else {
                $content = 'attachment;';
                if ($action == 'stream') {
                    $content = 'inline;';
                } else {
                    if ($action == 'download') {
                        $content = 'attachment;';
                    }
                }
                $return = Response::make(
                    file_get_contents($fileName), 200, [
                        'Content-Type' => 'application/pdf',
                        'Content-Disposition' => $content . ' filename="' . $fileName . '"',
                    ]
                );
                unlink($fileName);
                return $return;
            }
        } else {
            return null;
        }
    }

    /**
     * @param  User  $user
     * @return bool
     */
    public function allowForUser(User $user): bool
    {
        return true;
    }
}
