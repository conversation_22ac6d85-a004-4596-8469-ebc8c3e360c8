<?php

namespace App\Http\Controllers;

use App\Models\Api;
use App\Models\FileUpload;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class AccountDocumentController extends BaseController
{
    public function __construct(Request $request, FileUpload $fileUpload)
    {
        parent::__construct($request);

        $this->files = $fileUpload;
    }

    public function create(Request $request)
    {
        $types = json_decode(Api::get('api/v1/account-document-types'));

        return view(
            'account-documents/create',
            [
                'types' => $types->data,
            ]
        );
    }

    public function store(Request $request)
    {
        $data = $request->except('_token');
        $data['upload_date'] = date('Y-m-d H:i:s');

        $validator = Validator::make(
            $data,
            [
                'title' => 'required',
                'account_document_type_id' => 'required',
                'document' => 'required',
            ]
        );

        if ($validator->fails()) {
            return Redirect::back()
                ->withErrors($validator)
                ->withInput($request->old());
        }

        if ($request->hasFile('document')) {
            $file = $request->file('document');
            $data['filename'] = $file->getClientOriginalName();
            $data['filesize'] = $file->getSize();

            $cloudname = Str::uuid()->toString();
            $data['cloudname'] = $cloudname;
            $cloudpath = $data['organisation_id'] . '/' . $cloudname . '/' . $data['filename'];

            if (!is_bool($this->files->upload($file->getRealPath(), $cloudpath))) {
                return Redirect::back()->with('error', 'Could not upload document')->withInput($request->old());
            }
        }

        unset($data['document']);

        $response = json_decode(Api::post('/api/v1/account-documents', $data));

        return Redirect::to('organisation/' . $response->data->organisation_id);
    }

    public function edit($id)
    {
        $document = json_decode(Api::get('api/v1/account-documents/' . $id))->data;

        $cloudpath = $document->organisation_id . '/' . $document->cloudname . '/' . $document->filename;
        $document->download = $this->files->link($cloudpath, '2 hours');

        $types = json_decode(Api::get('api/v1/account-document-types'));

        return view(
            'account-documents/edit',
            [
                'document' => $document,
                'types' => $types->data,
            ]
        );
    }

    public function update(Request $request, $id)
    {
        $data = $request->except('_token');

        $rules = [
            'title' => 'required',
            'account_document_type_id' => 'required',
        ];

        if (isset($data['document'])) {
            $data['upload_date'] = date('Y-m-d H:i:s');

            $rules['document'] = 'required';
        }

        $validator = Validator::make($data, $rules);

        if ($validator->fails()) {
            return Redirect::back()
                ->withErrors($validator)
                ->withInput($request->old());
        }

        if ($request->hasFile('document')) {
            $file = $request->file('document');
            $data['filename'] = $file->getClientOriginalName();
            $data['filesize'] = $file->getSize();

            $cloudname = Str::uuid()->toString();
            $data['cloudname'] = $cloudname;
            $cloudpath = $data['organisation_id'] . '/' . $cloudname . '/' . $data['filename'];

            if (!is_bool($this->files->upload($file->getRealPath(), $cloudpath))) {
                return Redirect::back()
                    ->with('error', 'Could not upload document')
                    ->withInput($request->old());
            }
        }

        unset($data['document']);

        $response = json_decode(Api::post('/api/v1/account-documents/' . $id, $data));

        return Redirect::to('organisation/' . $response->data->organisation_id);
    }

    public function destroy(Request $request, $id)
    {
        $data = $request->except('_token');

        $response = json_decode(Api::post('/api/v1/account-documents/' . $id . '/destroy', $data));

        return Redirect::to('organisation/' . $response->data->organisation_id);
    }
}
