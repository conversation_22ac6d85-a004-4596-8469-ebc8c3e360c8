<?php

namespace App\Http\Controllers;

use App\Services\CacheContent\GetBrokerService;
use App\Services\CacheContent\GetMgaSchemeService;
use Carbon\Carbon;
use App\Models\Api;
use App\Models\Survey;
use App\Models\CsrReport;
use App\Models\Documents;
use App\Models\SrgHelper;
use App\Models\FileUpload;
use App\Exports\SurveyExport;
use App\Exports\SurveyFindingsExport;
use App\Helpers\Helpers;
use App\Helpers\RiskGradingHelper;
use App\Services\CacheContent\GetBrokerOrgsOptionService;
use App\Services\CacheContent\GetLibertyUsersService;
use App\Services\CacheContent\GetOrganisationService;
use App\Services\CacheContent\GetOrgBrokerUserOptionService;
use App\Services\CacheContent\GetOrgRiskGradingData;
use App\Services\CacheContent\GetSurveyService;
use App\Services\CacheContent\OptionListService;
use App\Services\SendSqsMessageService;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Barryvdh\DomPDF\Facade\Pdf;
use Barryvdh\Snappy\Facades\SnappyPdf;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Exception;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use App\Traits\HelperTrait;
use App\Traits\SubmissionGradingTrait;

class SurveyController extends BaseResourceController
{

    use HelperTrait, SubmissionGradingTrait;

    const TEMPLATE_PATH = '/surveys';
    const ROUTE_PREFIX = 'surveys';
    public $micrositeKey = "0mDU4h5PB:@}5!(I>yaBsHZNI)az6n~0si";

    public function __construct(Documents $doc, FileUpload $fileUpload, Survey $survey, CsrReport $csrReport)
    {
        $this->documents = $doc;
        $this->files = $fileUpload;
        $this->survey = $survey;
        $this->csrReport = $csrReport;
    }

    /**
     * Show all resources.
     *
     * @return Response
     *
     * @throws Exception
     */
    public function index(Request $request)
    {
        $user = Session::get('user');
        $order = $request->get('order', 'desc');
        $search = $request->get('search', '');
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 10);

        // filter by mga scheme
        $get = [];
        $request->has('organisation_id') ? $get[] = 'organisation_id=' . $request->get('organisation_id') : '';
        $request->has('mga_scheme') ? $get[] = 'mga_scheme=' . $request->get('mga_scheme') : '';
        $request->has('broker_org') ? $get[] = 'broker_org=' . $request->get('broker_org') : '';
        $request->has('search') ? $get[] = 'search=' . urlencode($request->get('search')) : '';
        $request->has('order') ? $get[] = 'order=' . $request->get('order') : $get[] = 'order=desc';
        $request->has('date_of_next_survey') ? $get[] = 'date_of_next_survey=filled' : '';
        $request->has('auto_scheduled_surveys') ? $get[] = 'auto_scheduled_surveys=filled' : '';
        $request->has('col') ? $get[] = 'col=' . $request->get('col') : '';
        $request->has('sort') ? $get[] = 'sort=' . $request->get('sort') : '';
        $request->has('month') ? $get[] = 'month=' . $request->get('month') : '';
        $request->has('year') ? $get[] = 'year=' . $request->get('year') : '';

        $get[] = 'is_admin_surveys=yes';

        if ($user->type == 'external-surveyor' && $user->isRoleAdminOrAccountManager() || $user->role == 'surveyor') {
            $get[] = 'user_role=' . Session::get('user')->role;
            $get[] = 'user_type=' . Session::get('user')->type;
            $get[] = 'user_login_type=' . Session::get('user')->login_type;
        }

        if ($user->login_type == 'aspen-user') {
            $get[] = 'branch_type=aspen-user';

            // print_r(Api::get(
            //     static::get_api_uri(sprintf(
            //         'all/%d/%d%s',
            //         $page,
            //         $limit,
            //         '?'.implode('&', $get)
            //     )
            // )));exit;

            $resources = json_decode(
                Api::get(
                    static::get_api_uri(
                        sprintf(
                            'all/%d/%d%s',
                            $page,
                            $limit,
                            '?' . implode('&', $get)
                        )
                    )
                )
            );
        } else {
            if ($user->login_type == 'broker-user') {
                $get[]     = 'user_login_type=' . Session::get('user')->login_type;
                $resources = json_decode(Api::get(static::get_api_uri(sprintf('broker/%d/%d/%d%s', $user->broker_id, $page, $limit, '?' . implode('&', $get)))));
            } else {
                $url = static::get_api_uri(
                    sprintf(
                        'all/%d/%d%s',
                        $page,
                        $limit,
                        '?' . implode('&', $get)
                    )
                );

                $resources = json_decode(Api::get($url));
            }
        }

        // get mga schemes
        $mgaSchemeParams = 'fieldsNeeded=id,name,trade_grouping_id,policy_type';
        if ($user->login_type == 'broker-user' && $user?->broker_id) {
                $schemes = json_decode(Api::get('api/v1/mga-schemes/all/0/99999?broker_id=' . $user->broker_id . '&' . $mgaSchemeParams));
        } else {
            $schemes = json_decode(Api::get('api/v1/mga-schemes/all/0/99999?' . $mgaSchemeParams));
        }

        // get broker orgs
        if ($user->login_type == 'broker-user' && $user?->broker_id) {
            $broker_orgs = json_decode(Api::get('api/v1/brokers/options' . '?broker_id=' . $user->broker_id));
        } else {
            $broker_orgs = json_decode(Api::get('api/v1/brokers/options'));
        }

        $months = array(1 => 'Jan', 2 => 'Feb', 3 => 'Mar', 4 => 'Apr', 5 => 'May', 6 => 'Jun', 7 => 'Jul', 8 => 'Aug', 9 => 'Sep', 10 => 'Oct', 11 => 'Nov', 12 => 'Dec');

        //echo "<pre>"; dd($resources->data);
        if ($resources->response == 'success') {
            return view(
                static::TEMPLATE_PATH . '/index',
                array_merge(
                    [
                        'broker_orgs' => isset($broker_orgs) ? $broker_orgs : [],
                        'schemes' => isset($schemes) ? $schemes->data : [],
                        'resources' => $resources->data,
                        'total' => $resources->total,
                        'limit' => $limit,
                        'page' => $page,
                        'search' => $search,
                        'csr_next_survey_due_dates' => isset($resources->dates) ? explode(", ", $resources->dates) : [],
                        'months' => $months,
                        'user_details' => Session::get('user'),
                        'link' => static::ROUTE_PREFIX . '.index',
                    ],
                    static::getAdditionalViewParams('index', $resources->data)
                )
            );
        } else {
            throw new \Exception($resources->message);
        }
    }

    public function mySurveys(Request $request)
    {
        $user = Session::get('user');
        $order = $request->get('order', 'asc');
        $search = $request->get('search', '');
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 10);

        // filter by mga scheme
        $get = [];
        $request->has('mga_scheme') ? $get[] = 'mga_scheme=' . $request->get('mga_scheme') : '';
        $request->has('broker_org') ? $get[] = 'broker_org=' . $request->get('broker_org') : '';
        $request->has('search') ? $get[] = 'search=' . urlencode($request->get('search')) : '';
        $request->has('order') ? $get[] = 'order=' . $request->get('order') : '';
        $request->has('date_of_next_survey') ? $get[] = 'date_of_next_survey=filled' : '';
        $request->has('auto_scheduled_surveys') ? $get[] = 'auto_scheduled_surveys=filled' : '';

        if ($user->type == 'external-surveyor' && $user->isRoleAdminOrAccountManager() || $user->role == 'surveyor') {
            $get[] = 'external_survey_company_id=' . $user->external_survey_company_id;
        }

        if ((($user->type == 'liberty-user' && $user->role != 'aspen-user' && $user->role != 'underwriter' && $user->role != 'admin') || $user->type == 'broker-user')) {
            $get[] = 'user_id=' . Session::get('user')->id;
            $get[] = 'user_role=' . Session::get('user')->role;
            $get[] = 'user_type=' . Session::get('user')->type;
            $get[] = 'user_login_type=' . Session::get('user')->login_type;
            $get[] = 'user_external_survey_company_id=' . Session::get('user')->external_survey_company_id;
        }

        if ($user->login_type == 'aspen-user') {
            $get[] = 'branch_type=aspen-user';

            // print_r(Api::get(
            //     static::get_api_uri(sprintf(
            //         'all/%d/%d%s',
            //         $page,
            //         $limit,
            //         '?'.implode('&', $get)
            //     )
            // )));exit;

            $resources = json_decode(
                Api::get(
                    static::get_api_uri(
                        sprintf(
                            'all/%d/%d%s',
                            $page,
                            $limit,
                            '?' . implode('&', $get)
                        )
                    )
                )
            );
        } else {
            if ($user->login_type == 'broker-user') {
                // print_r(Api::get(
                //     static::get_api_uri(sprintf(
                //         'broker/%d/%d/%d%s',
                //         $user->broker_id,
                //         $page,
                //         $limit,
                //         '?'.implode('&', $get)
                //     )
                // )));exit;
                $resources = json_decode(
                    Api::get(
                        static::get_api_uri(
                            sprintf(
                                'broker/%d/%d/%d%s',
                                $user->broker_id,
                                $page,
                                $limit,
                                '?' . implode('&', $get)
                            )
                        )
                    )
                );
            } else {
                // echo "<pre/>";
                // print_r(json_decode(Api::get(
                //     static::get_api_uri(sprintf(
                //         'all/%d/%d%s',
                //         $page,
                //         $limit,
                //         '?'.implode('&', $get)
                //     )
                // ))));exit;
                $resources = json_decode(
                    Api::get(
                        static::get_api_uri(
                            sprintf(
                                'all/%d/%d%s',
                                $page,
                                $limit,
                                '?' . implode('&', $get)
                            )
                        )
                    )
                );
            }
        }

        // get mga schemes
        if ($user->login_type == 'broker-user') {
            if ($user->broker_id) {
                $schemes = GetMgaSchemeService::getMgaSchemeById(id: $user->broker_id);
            }
        } else {
            $schemes = GetMgaSchemeService::get(id: '');
        }

        // get broker orgs
        if ($user->login_type == 'broker-user') {
            if ($user->broker_id) {
                $broker_orgs = GetBrokerService::getBrokerById(id: $user->broker_id);
            }
        } else {
            $broker_orgs = GetBrokerService::get(id: '');
        }

        if ($resources->response == 'success') {
            return view(
                static::TEMPLATE_PATH . '/my-surveys',
                array_merge(
                    [
                        'broker_orgs' => isset($broker_orgs) ? $broker_orgs->data : [],
                        'schemes' => isset($schemes) ? $schemes->data : [],
                        'resources' => $resources->data,
                        'total' => $resources->total,
                        'limit' => $limit,
                        'page' => $page,
                        'search' => $search,
                        'user_details' => Session::get('user'),
                        'link' => static::ROUTE_PREFIX . '.my-surveys',
                    ],
                    static::getAdditionalViewParams('my-surveys', $resources->data)
                )
            );
        } else {
            throw new \Exception($resources->message);
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     *
     * @return Response
     */
    public function duplicate($id)
    {
        $this->determineNestedResourceID($id);
        $resource = json_decode(Api::get(static::get_api_uri($id)));
        if (Session::get('user')->login_type == 'broker-user') {
            // print_r(Api::get('api/v1/broker-users/orgs/'.Session::get('user')->broker_id));exit;
            $orgs_for_user = json_decode(Api::get('api/v1/broker-users/orgs/' . Session::get('user')->broker_id));

            if (
                $resource && isset($resource->organisation) && isset($resource->data) && isset($resource->data->organisation)
                && $orgs_for_user && isset($orgs_for_user->schemes)
            ) {
                if (!in_array($resource->data->organisation->id, $orgs_for_user->schemes)) {
                    return Response::make('Unauthorized', 401);
                }
            }
        }

        $getResponseId = Api::get('api/v1/surveys/get-survey-files-for-id/' . $resource->data->id);
        $files = json_decode($getResponseId);

        foreach ($files->data as $file) {
            $file->download = $this->survey->downloadLink('survey_attachments/file/' . $file->cloud_file_name, $file->file_name);
        }

        $getSrfResponseId = Api::get('api/v1/surveys/get-srf-for-id/' . $resource->data->id);
        $srf = json_decode($getSrfResponseId);

        return ($resource->response == 'success')
            ? view(
                static::TEMPLATE_PATH . '/duplicate',
                array_merge(
                    ['resource' => $resource->data],
                    ['files' => $files->data],
                    ['srf' => $srf->data],
                    static::getAdditionalViewParams('edit', $resource->data)
                )
            )
            : Redirect::back()->with($resource->response, $resource->message);
    }

    public function reactionupdate(Request $request)
    {
        $data = $request->all();
        $response = json_decode(Api::post('api/v1/re-action/update', $data));

        if ($response->response == 'success') {
            return Response::json($response);
        } else {
            return Response::json($response);
        }
    }

    /**
     * Show all resources.
     *
     * @return Response
     */
    public function reReviews(Request $request)
    {
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 10);
        $get = [];

        $request->has('search') ? $get[] = 'search=' . urlencode($request->get('search')) : '';

        if (Session::get('user')->login_type == 'aspen-user') {
            $get[] = 'branch_id=' . Session::get('user')->branch_id;
            $get[] = 'survey_type=rereview';

            // print_r(Api::get(
            //     static::get_api_uri(sprintf(
            //         'all/%d/%d%s',
            //         $page,
            //         $limit,
            //         '?'.implode('&', $get)
            //     )
            // )));exit;

            $resources = json_decode(
                Api::get(
                    static::get_api_uri(
                        sprintf(
                            'all/%d/%d%s',
                            $page,
                            $limit,
                            '?' . implode('&', $get)
                        )
                    )
                )
            );
        } else {
            $get[] = 'survey_type=rereview';

            // print_r(Api::get(
            //     static::get_api_uri(sprintf(
            //         'all/%d/%d%s',
            //         $page,
            //         $limit,
            //         '?'.implode('&', $get)
            //     )
            // )));exit;

            $resources = json_decode(
                Api::get(
                    static::get_api_uri(
                        sprintf(
                            'all/%d/%d%s',
                            $page,
                            $limit,
                            '?' . implode('&', $get)
                        )
                    )
                )
            );
        }
        // print_r(Session::get('user')->id); exit;
        //dd($resources);

        if ($resources->response == 'success') {
            return view(
                static::TEMPLATE_PATH . '/index',
                array_merge(
                    [
                        'resources' => $resources->data,
                        'total' => $resources->total,
                        'limit' => $limit,
                        'page' => $page,
                        'user_details' => Session::get('user'),
                        'link' => static::ROUTE_PREFIX . '.re-reviews-list',
                    ],
                    static::getAdditionalViewParams('index', $resources)
                )
            );
        } else {
            throw new \Exception($resources->message);
        }
    }

    public function myReReviews(Request $request)
    {
        $user = Session::get('user');
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 10);
        $get = [];

        $request->has('search') ? $get[] = 'search=' . urlencode($request->get('search')) : '';

        if ((($user->type == 'liberty-user' && $user->role != 'aspen-user' && $user->role != 'underwriter' && $user->role != 'admin') || $user->type == 'broker-user')) {
            $get[] = 'user_id=' . Session::get('user')->id;
            $get[] = 'user_role=' . Session::get('user')->role;
            $get[] = 'user_type=' . Session::get('user')->type;
            $get[] = 'user_login_type=' . Session::get('user')->login_type;
            $get[] = 'user_external_survey_company_id=' . Session::get('user')->external_survey_company_id;
        }

        if (Session::get('user')->login_type == 'aspen-user') {
            $get[] = 'branch_id=' . Session::get('user')->branch_id;
            $get[] = 'survey_type=rereview';

            // print_r(Api::get(
            //     static::get_api_uri(sprintf(
            //         'all/%d/%d%s',
            //         $page,
            //         $limit,
            //         '?'.implode('&', $get)
            //     )
            // )));exit;

            $resources = json_decode(
                Api::get(
                    static::get_api_uri(
                        sprintf(
                            'all/%d/%d%s',
                            $page,
                            $limit,
                            '?' . implode('&', $get)
                        )
                    )
                )
            );
        } else {
            $get[] = 'survey_type=rereview';

            // print_r(Api::get(
            //     static::get_api_uri(sprintf(
            //         'all/%d/%d%s',
            //         $page,
            //         $limit,
            //         '?'.implode('&', $get)
            //     )
            // )));exit;

            $resources = json_decode(
                Api::get(
                    static::get_api_uri(
                        sprintf(
                            'all/%d/%d%s',
                            $page,
                            $limit,
                            '?' . implode('&', $get)
                        )
                    )
                )
            );
        }
        // print_r(Session::get('user')->id); exit;
        //dd($resources);

        if ($resources->response == 'success') {
            return view(
                static::TEMPLATE_PATH . '/my-surveys',
                array_merge(
                    [
                        'resources' => $resources->data,
                        'total' => $resources->total,
                        'limit' => $limit,
                        'page' => $page,
                        'user_details' => Session::get('user'),
                        'link' => static::ROUTE_PREFIX . '.my-re-reviews-list',
                    ],
                    static::getAdditionalViewParams('my-surveys', $resources->data)
                )
            );
        } else {
            throw new \Exception($resources->message);
        }
    }

    /**
     * Show all RE Actions.
     *
     * @return Response
     */
    public function reActions(Request $request)
    {
        $search = $request->get('search', '');
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 10);

        $resources = json_decode(
            Api::get(
                static::get_api_uri(
                    sprintf(
                        'surveys/all/%d/%d%s',
                        $page,
                        $limit,
                        ($search !== '' ? '?search=' . urlencode($search) . '&survey_type=RE Review' : '?survey_type=RE Review')
                    )
                )
            )
        );

        if ($resources->response == 'success') {
            return view(
                static::TEMPLATE_PATH . '/index',
                array_merge(
                    [
                        'resources' => $resources->data,
                        'total' => $resources->total,
                        'limit' => $limit,
                        'page' => $page,
                        'search' => $search,
                        'user_details' => Session::get('user'),
                        'link' => static::ROUTE_PREFIX . '.index',
                    ],
                    static::getAdditionalViewParams('index', $resources->data)
                )
            );
        } else {
            throw new \Exception($resources->message);
        }
    }

    public function getSurveysForSurveyors($id)
    {
        $response = json_decode(Api::get(static::get_api_uri($id, 'surveys/surveyor')));

        return Response::json($response);
    }

    public function mobile_getSurveysForSurveyors($id, $user_type = 'none')
    {
        $logged_in = Api::check_key('external-surveyors');

        if (!$logged_in || json_decode($logged_in)->response != 'success') {
            return Response::json(
                [
                    'response' => 'error',
                    'message' => 'Unable to login member',
                ],
                200
            );
        }
        $response = json_decode(Api::get(static::get_api_uri($id . '/1' . $user_type, 'surveys/surveyor')));

        return Response::json($response);
    }

    public function mobile_getDownloadLink($cloud_file_name, $file_name)
    {
        $logged_in = Api::check_key('external-surveyors');

        if (!$logged_in || json_decode($logged_in)->response != 'success') {
            return Response::json(
                [
                    'response' => 'error',
                    'message' => 'Unable to login member',
                ],
                200
            );
        }
        $link = $this->survey->downloadLink('survey_attachments/file/' . $cloud_file_name, $file_name);
        if ($link) {
            $image_type = substr($file_name, strrpos($file_name, '.') + 1);
            $image = 'data:image/' . $image_type . ';base64,' . base64_encode(file_get_contents($link));

            return Response::json(['response' => 'success', 'link' => $image], 200);
        } else {
            return Response::json(['response' => 'error', 'message' => 'Could not get link'], 200);
        }
    }

    public function mobile_getSurvey($id)
    {
        $logged_in = Api::check_key('external-surveyors');

        if (!$logged_in || json_decode($logged_in)->response != 'success') {
            return Response::json(
                [
                    'response' => 'error',
                    'message' => 'Unable to login member',
                ],
                200
            );
        }
        $response = json_decode(Api::get(static::get_api_uri($id, 'surveys')));

        return Response::json($response);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     *
     * @return Application|Factory|\Illuminate\View\View|View|RedirectResponse
     */
    public function show(Request $request, $id)
    {
        $user_details = Session::get('user');

        $cacheKey = "survey-data-for-surveyid-{$id}";
        $resource = $this->getSetCache($cacheKey);
        if (!$resource) {
            $dataEndpoint = static::get_api_uri($id);
            $resource = $this->getData($dataEndpoint);
            $this->getSetCache($cacheKey, $resource);
        }

        if ($user_details->login_type === 'broker-user') {
            $result = $this->surveyForBroker($user_details->broker_id, $resource);

            if (!$result['is_allowed']) {
                return view('errors.404');
            }
        }

        // Early return if the response is not successful
        if ($resource->response !== 'success') {
            return Redirect::back()->with($resource->response, $resource->message);
        }

        $cacheKey="messages-for-survey-id-{$id}";
        $messages=$this->getSetCache($cacheKey);
        if(!$messages){
            $dataEndpoint = static::get_api_uri('survey/' . $id, 'messaging');
            $messages=$this->getData($dataEndpoint);
            $this->getSetCache($cacheKey,$messages);
        }

        return view(static::TEMPLATE_PATH . '/show', [
            'id' => $id,
            'messages' => $messages->data,
            'resource' => $resource->data,
            're_options' => static::getREOptions(),
            'user_details' => $user_details,
            'encrypted_surveyId_url' => (new Helpers())->encryptGUID($resource->data->id, $this->micrositeKey),
        ]);
    }

    public function getSurveyOptions(Request $request, $id)
    {
        $resource = GetSurveyService::get($id);

        // Early return if the response is not successful
        if ($resource->response !== 'success') {
            return Redirect::back()->with($resource->response, $resource->message);
        }

        $user_details = Session::get('user');
        $params['options']['surveyors'] = [];
        if (!isset($resource->data->external_survey_company) || !isset($resource->data->surveyor) && $user_details?->isRoleAdminOrAccountManager() && $user_details->type == 'liberty-user') {
            $params['options']['surveyors'] = static::getSurveyorOptions('0');
        } else {
            if (isset($user_details) && $user_details?->isRoleAdminOrAccountManager() && $user_details->type == 'external-surveyor') {
                $params['options']['surveyors'] = static::getSurveyorOptions('1', $resource->data->external_survey_company->id);
            }
        }

        switch (true) {
            case !isset($resource->data->external_survey_company) && isset($resource->data->surveyor->id):
                $params['surveyor'] = sprintf('risk-engineer:%d', $resource->data->surveyor->id);
                break;

            case isset($resource->data->surveyor->id):
                $params['surveyor'] = sprintf('surveyor:%d', $resource->data->surveyor->id);
                break;

            case isset($resource->data->external_survey_company->id):
                $params['surveyor'] = sprintf('external-survey-company:%d', $resource->data->external_survey_company->id);
                break;

            default:
                $params['surveyor'] = '';
                break;
        }

        $params['risk_engineers'] = '';
        if (!empty($resource->risk_engineers)) {
            $lastKey = count($resource->risk_engineers);
            foreach ($resource->risk_engineers as $idx => $re) {
                $params['risk_engineers'] .= $re->email;
                if ($idx != $lastKey - 1) {
                    $params['risk_engineers'] .= ',';
                }
            }
        }

        return view(static::TEMPLATE_PATH . '/partials/survey-option',
            array_merge($params, ['resource' => $resource->data])
        );
    }

    public function getSurveyData(Request $request, $id)
    {
        $mga_superior = 0;

        $resource = GetSurveyService::get($id);

        // Early return if the response is not successful
        if ($resource->response !== 'success') {
            return Redirect::back()->with($resource->response, $resource->message);
        }

        if (
            Session::get('user')->login_type == 'aspen-user' && isset($resource->data->organisation->liberty_branch)
            && isset($resource->data->branch->is_aspen) && $resource->data->branch->is_aspen != '1'
        ) {
            return Response::make('Unauthorized', 401);
        }

        if (Session::get('user')->login_type == 'broker-user') {
            $brokerId = Session::get('user')->broker_id;
            $result = $this->surveyForBroker($brokerId, $resource);
            if ($result['is_allowed']) {
                $mga_superior = $result['mga_superior'];
            }
        }

        $agenda = [];

        if (Session::get('user')->login_type == 'external-surveyor-admin') {
            if (!isset($resource->data->external_survey_company->id) || (isset($resource->data->external_survey_company->id) && $resource->data->external_survey_company->id != Session::get('user')->external_survey_company_id)) {
                die('Restricted area');
            }
        }

        if (Session::get('user')->login_type == 'external-surveyor') {
            if (!isset($resource->data->external_survey_company->id) || !isset($resource->data->surveyor->id)) {
                die('Restricted area');
            }
            if (isset($resource->data->surveyor->id) && ($resource->data->surveyor->id != Session::get('user')?->id) || (isset($resource->data->external_survey_company->id) && $resource->data->external_survey_company->id != Session::get('user')->external_survey_company_id)) {
                die('Restricted area');
            }
        }

        if (isset($resource->data->agenda_file) && !is_null($resource->data->agenda_file)) {
            $agenda['cloud_file_name'] = $resource->data->agenda_file->cloud_file_name;
            $agenda['file_name'] = $resource->data->agenda_file->file_name;
        }

        $survey_branch_id = isset($resource->data->branch) ? $resource->data->branch->id : null;

        $params = [
            'resource' => $resource->data,
        ];

        $user_details = Session::get('user');
        $lib_users = GetLibertyUsersService::get();

        $params['lib_user']=[];
        $cacheKey="params-all-liberty-users";
        $params_lib_users=$this->getSetCache($cacheKey);
        if(!$params_lib_users){
            foreach ($lib_users->data as $lib_user) {
                if ((is_null($lib_user->branch) || $lib_user->branch->id == $survey_branch_id) && $lib_user->role != 'underwriter') {
                    if (!array_key_exists($lib_user->role, $params['lib_user'])) {
                        $params['lib_user'][$lib_user->role] = [];
                    }
                    array_push($params['lib_user'][$lib_user->role], ['id' => $lib_user->id, 'name' => $lib_user->first_name . ' ' . $lib_user->last_name]);
                }
            }

            $params_lib_users=$params['lib_user'];
            $this->getSetCache($cacheKey,$params_lib_users);
        }

        $params['lib_user'] = $params_lib_users;

        if (isset($resource->data) && isset($resource->data->underwriter)) {
            $params['lib_user']['underwriter'] = [
                [
                    'id' => $resource->data->underwriter->id,
                    'name' => $resource->data->underwriter->first_name . ' ' . $resource->data->underwriter->last_name,
                ],
            ];
        }

        $currentView = $this->getSurveyPartialBox($user_details, $resource->data);

        if (!empty($currentView)) {
            $currentView = 'surveys.partials.' . $currentView;
        }

        if (isset($resource->data) && isset($resource->data->broker_underwriter)) {
            $params['lib_user']['underwriter'] = [
                [
                    'id' => $resource->data->broker_underwriter->id,
                    'name' => $resource->data->broker_underwriter->first_name . ' ' . $resource->data->broker_underwriter->last_name,
                ],
            ];
        }

        $params['mga_superior'] = $mga_superior;

        if ($resource->response == 'success' && !is_null($resource->data->resurvey_id) && $resource->data->resurvey_id != '') {

            if ($this->files->exists('survey_attachments/pdf/CSR_' . $resource->data->resurvey_id . '.pdf')) {
                $cacheKey="csr-pdf-link-for-resurvey-id-{$resource->data->resurvey_id}";
                $params['csr_pdf_link']=$this->getSetCache($cacheKey);
                if(!$params['csr_pdf_link']){
                    $params['csr_pdf_link'] = $this->files->link('survey_attachments/pdf/CSR_' . $resource->data->resurvey_id . '.pdf');
                    $this->getSetCache($cacheKey,$params['csr_pdf_link']);
                }
            }

            if ($this->files->exists('survey_attachments/pdf/UWR_' . $resource->data->resurvey_id . '.pdf')) {
                $cacheKey="uwr-pdf-link-for-resurvey-id-{$resource->data->resurvey_id}";
                $params['uwr_pdf_link']=$this->getSetCache($cacheKey);
                if(!$params['uwr_pdf_link']){
                    $params['uwr_pdf_link'] = $this->files->link('survey_attachments/pdf/UWR_' . $resource->data->resurvey_id . '.pdf');
                    $this->getSetCache($cacheKey,$params['uwr_pdf_link']);
                }
            }

            if (!isset($params['csr_pdf_link']) || !isset($params['uwr_pdf_link'])) {
                $cacheKey="resurvey-details-for-resurvey-id-{$resource->data->resurvey_id}";
                $resurvey_details=$this->getSetCache($cacheKey);
                if(!$resurvey_details){
                    $dataEndpoint = static::get_api_uri($resource->data->resurvey_id);
                    $resurvey_details=$this->getData($dataEndpoint);
                    $this->getSetCache($cacheKey,$resurvey_details);
                }

                if ($resurvey_details->response == 'success' && isset($resurvey_details->data->submissions)) {
                    $params['resurvey_submission_id'] = $resurvey_details->data->submissions->_id;
                }
            }
        }

        if ($resource->data->csr_uw_status == '2' && (!$this->files->exists('survey_attachments/pdf/CSR_' . $id . '.pdf') || $request->has('forceCSR'))) {
            if (isset($resource->data->submissions) && isset($resource->data->submissions->_id)) {
                try {
                    $this->downloadPrintPdfBinder($request, $resource->data->submissions->_id, 'csr', false);
                } catch (\Exception $e) {
                }
            }
        }

        if ($resource->data->survey_type === 'dtr') {
            $keyHelper = new Helpers();
            $params['encrypted_surveyId_url'] = $keyHelper->encryptGUID($resource->data->id, $this->micrositeKey);
        }

        return view(
            static::TEMPLATE_PATH . '/partials/survey_body',
            array_merge($params,
                static::getAdditionalViewParams('show', $resource->data),
                [
                    'box_view' => $currentView,
                    'files' => $resource->data->survey_files ?? [],
                    'agenda' => $agenda
                ]
            )
        );
    }

    private function surveyForBroker($brokerId, $survey)
    {
        $mgaSuperior   = 0;
        $hasPermission = false;
        $cacheKey = "orgs_for_broker-{$brokerId}";
        $orgsForBroker = $this->getSetCache($cacheKey);
        if (!$orgsForBroker) {
            $dataEndpoint  = 'api/v1/organisation/broker/' . $brokerId;
            $orgsForBroker = $this->getData($dataEndpoint);
            $this->getSetCache($cacheKey, $orgsForBroker);
        }

        if ($orgsForBroker->response == 'success') {
            foreach ($orgsForBroker->data as $org) {
                if (isset($survey->data) && isset($survey->data->organisation)
                    && ($org->id == $survey->data->organisation->id || $brokerId == $survey->data->broker->id)) {

                    $hasPermission = true;
                    if ($org->mga_scheme) {
                        $scheme_of_scheme = json_decode(Api::get('api/v1/mga-schemes/' . $org->mga_scheme));

                        if ($scheme_of_scheme) {
                            $mgaSuperior = $scheme_of_scheme->data->enable_uwr ? $scheme_of_scheme->data->enable_uwr : null;
                        }
                    }

                    break;
                }
            }
        }

        return [
            'is_allowed'    => $hasPermission,
            'mga_superior'  => $mgaSuperior,
        ];
    }

    private function getSurveyPartialBox($user_details, $resource)
    {
        //echo "<pre/>"; print_r($resource); exit;
        if ($user_details->type == 'external-surveyor' && !isset($resource->submissions->_id) && isset($resource->surveyor->id) && $user_details->id == $resource->surveyor->id) {
            return 'survey_assigned';
        }

        if ($user_details->type == 'external-surveyor' && $user_details?->isRoleAdminOrAccountManager() && !isset($resource->surveyor->id)) {
            return 'survey_decline';
        }

        if (($user_details->role == 'risk-engineer') && !isset($resource->submissions->_id) && isset($resource->surveyor->id) && is_null($user_details->external_survey_company_id) && $user_details->id == $resource->surveyor->id) {
            return 'survey_assigned';
        }

        if (isset($resource->submissions)) {

            if ((($user_details->role == 'risk-engineer' && !$user_details?->isAccountManager()) || $user_details->type == 'external-surveyor') && $resource->submissions->surveyor_id == $user_details->id) {
                return 'survey_control_extenal_assigned_re';
            } elseif (($user_details->type == 'external-surveyor' && $user_details?->isRoleAdminOrAccountManager()) && isset($resource->external_survey_company) && isset($resource->external_survey_company->id) && $user_details->external_survey_company_id == $resource->external_survey_company->id) {
                return 'survey_control_extenal_assigned_re';
            } elseif (($user_details->role == 'risk-engineer') && $resource->re_id == $user_details->id) {
                return 'survey_control_extenal_assigned_confirmation';
            } elseif (($user_details->role == 'underwriter') && isset($resource->underwriter->id) && $resource->underwriter->id == $user_details->id) {
                return 'survey_control_uwr';
            } elseif (($user_details->role == 'aspen-user')) {
                return 'survey_control_aspen';
            } elseif ((isset($user_details->login_type) && isset($resource->broker_underwriter->id) && $user_details->login_type == 'broker-user' && $resource->broker_underwriter->id == $user_details->id)) {
                return 'survey_control_uwr';
            } elseif ((isset($user_details->login_type) && $user_details->login_type == 'broker-user' && $resource->csr_uw_status === 2)) {
                return 'survey_submitted_broker_user';
            } else {
                if (
                    $user_details->type != 'external-surveyor' && $user_details->login_type != 'broker-user'
                    //&& !empty($resource->submissions->surveyor_id) && isset($resource->submissions->csr_status) && $resource->submissions->csr_status !== 'completed'
                    && !empty($resource->submissions->surveyor_id) && (isset($resource->submissions->csr_status) || isset($resource->submissions->uwr_status))
                ) {
                    return ($user_details?->isRoleAdminOrAccountManager() || $user_details->role == 'branch-admin' || $user_details->role == 'branch-user') ? 'survey_control_rc_qa' : 'survey_control_qa';
                }
            }
        }
    }

    /**
     * Get Surveyor Options.
     *
     * @return array surveyor options (risk engineers & external surveyor companies)
     */
    private static function getSurveyorOptions($get_surveyors, $survey_company = 0)
    {
        $user = Session::get('user');

        $options = [
            '' => 'Unassigned',
        ];

        $types = [
            'Risk Engineers' => [
                'api-call' => ['options/risk-engineer', 'liberty-users'],
                'slug' => 'risk-engineer',
            ],
            'External Survey Companies' => [
                'api-call' => ['options', 'external-survey-companies'],
                'slug' => 'external-survey-company',
            ],
        ];
        if (isset($user->role) && $user?->isRoleAdminOrAccountManager() && $user->type == 'external-surveyor' && $get_surveyors == '1') {
            $types = [
                'Surveyors' => [
                    'api-call' => [$user->external_survey_company_id . '/surveyors', 'external-survey-companies'],
                    'slug' => 'surveyor',
                ],
            ];
        }

        if (isset($user->role) && ($user?->isRoleAdminOrAccountManager() || $user->role == 'risk-engineer' || $user->role == 'underwriter') && $user->type == 'liberty-user' && $get_surveyors == '1') {
            $types = [
                'Surveyors' => [
                    'api-call' => [$survey_company . '/surveyors', 'external-survey-companies'],
                    'slug' => 'surveyor',
                ],
            ];
        }

        foreach ($types as $type => $values) {
            $options[$type] = json_decode(
                Api::get(
                    static::get_api_uri($values['api-call'][0], $values['api-call'][1])
                ),
                1
            );

            if (count($options[$type])) {
                $newOptions = [];
                foreach ($options[$type] as $key => $value) {
                    $newKey = sprintf(
                        '%s:%d',
                        $values['slug'],
                        $key
                    );
                    $newOptions[$type][$newKey] = $value;
                }
                $options[$type] = $newOptions[$type];
            }

        }
        return $options;
    }

    /**
     * Get REs.
     *
     * @return array RE options (risk engineers)
     */
    private static function getREOptions()
    {
        $user = Session::get('user');

        //print_r($user); exit;

        $options = [
            '' => 'Unassigned',
        ];

        $types = [
            'Risk Engineers' => [
                'api-call' => ['options/risk-engineer', 'liberty-users'],
                'slug' => 'risk-engineer',
            ],
        ];

        foreach ($types as $type => $values) {
            $options[$type] = json_decode(
                Api::get(
                    static::get_api_uri($values['api-call'][0], $values['api-call'][1])
                ),
                1
            );

            if (count($options[$type])) {
                $options[$type] = array_flip($options[$type]);

                foreach ($options[$type] as $key => $value) {
                    $options[$type][$key] = $value;
                }

                $options[$type] = array_flip($options[$type]);
            }
        }

        return $options;
    }

    public function declineSurvey($id)
    {
        $data['decline_survey'] = 1;
        $data['company_id'] = isset(Session::get('user')->external_survey_company_id) ? Session::get('user')->external_survey_company_id : '0';

        $api = json_decode(Api::post('api/v1/surveys/update_actual/' . $id, $data));

        if ($api->response == 'success') {
            if (method_exists(get_called_class(), 'onUpdateSuccess')) {
                $this->setCache("survey-data-for-surveyid-{$id}", null);
                static::onUpdateSuccess($data);
            }
        } else {
            if (method_exists(get_called_class(), 'onUpdateError')) {
                static::onUpdateError($data);
            }
        }

        return Redirect::to('/surveys')->with(
            $api->response,
            $api->message
        );
    }

    public function actual_dates(Request $request, $id)
    {
        $data = $request->except('_token');
        $data['notify_underwriter'] = false;

        if (isset($data['deadlines'])) {
            foreach ($data['deadlines'] as $key => $value) {
                $data['deadlines'][$key] = date('Y-m-d H:i:s', strtotime(str_replace('/', '-', $value)));
            }

            if (isset($data['deadlines']['actual_submission'])) {
                $emailData = [];
                $surv = json_decode(
                    Api::get(
                        static::get_api_uri($id)
                    )
                );
                if ($surv->response == 'success') {
                    $survey = $surv->data;

                    $orgid = $survey->organisation->id;
                    $org   = json_decode(Api::get('api/v1/organisation/' . $orgid));
                    if ($org->response == 'success') {
                        $organisation = $org->data;
                    }

                    $emailData['client_org_id']   = $organisation->id;
                    $emailData['client_org_name'] = $organisation->name;
                    $emailData['address_1']       = $survey->client_contact->address_1;
                    $emailData['address_2']       = $survey->client_contact->address_2;
                    $emailData['city']            = $survey->client_contact->city ?? '';
                    $emailData['postcode']        = $survey->client_contact->postcode;
                    $emailData['country']         = $survey->client_contact->country == 'GB' ? 'United Kingdom' : $survey->client_contact->country;
                    $emailData['actual_date']     = $data['deadlines']['actual_submission'];
                    $emailData['srf_id']          = $id;
                    $emailData['srf_link']        = '/surveys/' . $id;

                    if (isset($survey->surveyor)) {
                        $re = $survey->surveyor;
                        $emailData['risk_engineer_label'] = 'Risk Engineer';
                        $emailData['risk_engineer_name'] = (isset($re->first_name) ? $re->first_name : '') . ' ' . (isset($re->last_name) ? $re->last_name : '');
                        $emailData['risk_engineer_phone'] = $re->phone;
                    } elseif (isset($survey->external_survey_company)) {
                        $re = $survey->external_survey_company;
                        $emailData['risk_engineer_label'] = 'External Surveyor';
                        $emailData['risk_engineer_name'] = $re->name;
                        $emailData['risk_engineer_phone'] = $re->phone;
                    } else {
                        $emailData['risk_engineer_label'] = 'Risk Engineer/External Surveyor';
                        $emailData['risk_engineer_name'] = 'unassigned';
                        $emailData['risk_engineer_phone'] = '';
                    }

                    if (isset($survey->notify_underwriter) && $survey->notify_underwriter) {
                        $data['notify_underwriter'] = true;
                        $emailData['underwrite_email'] = $survey->underwriter->email;
                        $emailData['underwrite_name'] = (isset($survey->underwriter->first_name) ? $survey->underwriter->first_name : '') . ' ' . (isset($survey->underwriter->last_name) ? $survey->underwriter->last_name : '');
                    }

                    $data['notify_emaildata'] = (object) $emailData;
                }
            }
        }

        $api = json_decode(Api::post('api/v1/surveys/update_actual/' . $id, $data));

        if ($api->response == 'success') {
            if (method_exists(get_called_class(), 'onUpdateSuccess')) {
                $this->setCache("survey-data-for-surveyid-{$id}", null);
                static::onUpdateSuccess($data);
            }
        } else {
            if (method_exists(get_called_class(), 'onUpdateError')) {
                static::onUpdateError($data);
            }
        }

        return Redirect::to('/surveys/' . $id)->with(
            $api->response,
            $api->message
        );
    }

    public function updateSurveyor(Request $request, $id)
    {
        $surveyor         = $request->get('surveyor', '');
        $location         = $request->get('location', '');
        $disgnatedRE      = !empty($request->get('designated_risk_engineers')) ? explode(',', $request->get('designated_risk_engineers')) : [];
        $data['location'] = $location;

        $surveyor = array_combine(
            ['type', 'id'],
            (strpos($surveyor, ':') !== false ? explode(':', $surveyor, 2) : array_fill(0, 2, ''))
        );

        switch ($surveyor['type']) {
            case 'risk-engineer':
                $data['surveyor_id']                = $surveyor['id'];
                $data['external_survey_company_id'] = 0;
                $data['notify']                     = 'risk_engineer';
                break;

            case 'external-survey-company':
                $data['external_survey_company_id'] = $surveyor['id'];
                $data['surveyor_id']                = 0;
                $data['notify']                     = 'external_survey_company_admin';
                break;

            case 'surveyor':
                $data['surveyor_id'] = $surveyor['id'];
                $data['notify']      = 'external_surveyor';
                break;

            case '':
                $data['surveyor_id'] = 0;
                break;
        }

        $data['org_risk_engineers'] = $disgnatedRE;

        $api = json_decode(
            Api::put(
                static::get_api_uri($id),
                $data
            )
        );

        if ($api->response == 'success') {
            if (method_exists(get_called_class(), 'onUpdateSuccess')) {
                $this->setCache("survey-data-for-surveyid-{$id}", null);
                static::onUpdateSuccess($data);
            }
        } else {
            if (method_exists(get_called_class(), 'onUpdateError')) {
                static::onUpdateError($data);
            }
        }

        return Redirect::back()->with(
            $api->response ?? '',
            $api->message ?? ''
        );
    }

    public function sendMessage(Request $request)
    {
        $user  = Session::get('user');
        $input = $request->except('_token');
        $input['role']        = $user->role;
        $input['type']        = $user->type;
        $input['login_type']  = $user->login_type;
        $input['survey_id']   = $request->get('survey_id');
        $input['user_id']     = $user->id;
        $input['sender_name'] = ucfirst($user->first_name) . ' ' . ucfirst($user->last_name);
        $input['email']       = $user->email;

        $this->setCache("messages-for-survey-id-{$request->get('survey_id')}", null);
        $this->setCache("csr-microsite-survey-messages-{$request->get('survey_id')}", null);
        $this->setCache("csr-microsite-survey-{$request->get('survey_id')}", null);

        return Api::post('/api/v1/messaging/send', $input);
    }

    public function tempUploadAttachment(Request $request)
    {
        $user = Session::get('user');
        $data           = $request->except('_token');
        $fileName       = $user->id . '-' . strtolower(preg_replace('/[\s_]|^%20/', '-', $data['file']->getClientOriginalName()));

        $tempFile       = Storage::putFileAs('file_to_upload', $data['file'], $fileName);
        $uploadFilename = Storage::url($tempFile);

        return response()->json([
            'response' => 'success',
            'name'     => $fileName,
            'uploaded_filename' => $uploadFilename,
        ]);
    }

    public function tempDeleteAttachment(Request $request)
    {
        if ($request->has('attachments')) {
            $temp = [];
            foreach ($request->attachments as $attachment) {
                $temp[] = 'file_to_upload/' . $attachment;
            }

            if (!empty($temp)) {
                Storage::delete($temp);
            }
        }

        return response()->json([
            'response' => 'success',
            'message'  => 'Deleted temp attachments for messenger.'
        ]);
    }

    /**
     * Send the pending attachment made by user
     *
     * @param int $surveyId survey_id
     * @params array $attachments
     * 
     * @return void
     */
    private function sendAttachment($surveyId, $attachments)
    {
        foreach ($attachments as $attachment) {
            $uploadFilename = substr(Str::uuid()->toString(), 0, 4);
            $pathToFile     = storage_path('app') . '/file_to_upload/' . $attachment;
            $upload = $this->survey->upload($pathToFile, $uploadFilename, 'messaging_attachments/' . $surveyId);

            if ($upload['response'] === 'success') {
                Storage::delete('file_to_upload/' . $attachment);
            }
        }
    }

    public function sendMessageWithAttachment(Request $request)
    {
        $user = Session::get('user');
        $input = $request->except('_token');
        $input['role'] = $user->role;
        $input['type'] = $user->type;
        $input['survey_id'] = $request->get('survey_id');
        $input['user_id'] = $user->id;
        $input['sender_name'] = ucfirst($user->first_name) . ' ' . ucfirst($user->last_name);

        $file_field_name = 'file';
        $file = $request->file($file_field_name);
        $size = $request->file($file_field_name)->getSize();
        $path = $file->getRealPath();
        $type = $file->getMimeType();

        // $destinationPath = public_path('uploads'); // upload path
        $extension = $request->file($file_field_name)->getClientOriginalExtension(); // getting image extension

        $fileName = strtolower(preg_replace('/[\s_]|^%20/', '-', $request->file($file_field_name)->getClientOriginalName()));
        $uploaded_filename = substr(Str::uuid()->toString(), 0, 4);

        $upload = $this->survey->upload($file, $uploaded_filename, 'messaging_attachments/' . $request->get('survey_id'));

        if ($upload['response'] == 'success') {
            $data['file_name'] = $fileName;
            $data['uploaded_filename'] = $uploaded_filename;

            return Response::json(
                [
                    'response' => 'success',
                    'name' => $fileName,
                    'uploaded_filename' => $uploaded_filename,
                ]
            );
        } else {
            return Response::json($upload);
        }
    }

    public function getMessageAttachment($survey_id, $fileName, $name = '0')
    {
        $fileName_cloud = 'messaging_attachments/' . $survey_id . '/' . $fileName;
        $file = $this->survey->download($fileName_cloud, $fileName);
        if ($file) {
            if ($name != '0') {
                return Response::download($file, $name);
            }

            return Response::download($file, $fileName);
        }
    }

    /**
     * Show Legacy Report for Risk Recommendations.
     *
     * @param $survey_id
     *
     * @return mixed
     */
    public function showLegacyReport($survey_id)
    {
        print_r('legacy');
        $user = Session::get('user');
        $api_call = json_decode(Api::get('/api/v1/surveys/' . $survey_id));

        if (isset($api_call->response) && $api_call->response == 'success') {
            // print_r(Api::get('api/v1/legacy-survey-submission/' . $api_call->data->legacy_submissions->_id));exit;
            $response = json_decode(Api::get('api/v1/legacy-survey-submission/' . $api_call->data->legacy_submissions->_id));
            $submission = json_decode($response->data);

            $rr_data = $submission;

            $params = [
                'resource' => $api_call->data,
            ];

            $colors = [];

            return view(
                'surveys.legacy-report',
                array_merge(
                    $params,
                    array(
                        'submission' => $submission,
                        'rr_data' => $rr_data,
                        // 'grading_data' => $grading_data,
                        'colors' => $colors,
                        'user_details' => $user,
                    )
                )
            );
        }
    }

    /**
     * Show Report for Risk Recommendations.
     *
     * @param $survey_id
     *
     * @return mixed
     */
    public function showReport($survey_id)
    {
        $user = Session::get('user');
        $cacheKey="survey-report-data-for-surveyid-{$survey_id}";
        $api_call=$this->getSetCache($cacheKey);
        if(!$api_call){
            $dataEndpoint = '/api/v1/surveys/' . $survey_id;
            $api_call=$this->getData($dataEndpoint);
            $this->getSetCache($cacheKey,$api_call);
        }

        $cacheKey="standard-risk-policy-types";
        $policyTypes=$this->getSetCache($cacheKey);
        if(!$policyTypes){
            $dataEndpoint = '/api/v1/standard-risk/policy-types';
            $policyTypes=$this->getData($dataEndpoint);
            $this->getSetCache($cacheKey,$policyTypes);
        }

        if (isset($api_call->response) && $api_call->response == 'success' && isset($api_call->data->submissions)) {
            if (($user->login_type == 'external-surveyor' || $user->login_type == 'external-surveyor-admin') && isset($api_call->data->surveyor->id) && $api_call->data->surveyor->id != Session::get('user')?->id) {
                $check_for_resurvey = json_decode(Api::get('/api/v1/surveys/hasresurvey/' . $survey_id));
                if (isset($check_for_resurvey->response) && $check_for_resurvey->response == 'success') {
                    if (isset($check_for_resurvey->data->surveyor->id) && $check_for_resurvey->data->surveyor->id != Session::get('user')?->id) {
                        die('Unauthorised');
                    }
                } else {
                    die('Unauthorised');
                }
            }

            $cacheKey="survey-submission-for-id-{$api_call->data->submissions->_id}";
            $survey_submission=$this->getSetCache($cacheKey);
            if(!$survey_submission){
                $dataEndpoint = 'api/v1/survey-submission/' . $api_call->data->submissions->_id;
                $survey_submission=$this->getData($dataEndpoint);
                $this->getSetCache($cacheKey,$survey_submission);
            }

            $submission = json_decode($survey_submission->data);

            $submission->json = $survey_submission->data;

            $cacheKey="risk-improvement-form-for-id-{$submission->form_id}";
            $response=$this->getSetCache($cacheKey);
            if(!$response){
                $dataEndpoint = 'api/v1/risk-improvement/form/' . $submission->form_id;
                $response=$this->getData($dataEndpoint);
                $this->getSetCache($cacheKey,$response);
            }

            if ($response->response == 'success') {
                $form = json_decode($response->data, true);

                $file_data = [];
                $rr_data = [];
                $grading_data = [];

                if (isset($form) && isset($form['fields'])) {
                    $file_data_in = [];
                    foreach ($form['fields'] as $field_types) {
                        foreach ($field_types as $field_type => $field_attr) {
                            $file_data_in_name = 'no-name';
                            $file_data_in = [];
                            if ($field_type == 'file' || $field_type == 'risk_recommendation') {
                                foreach ($field_attr as $element) {
                                    $file_data_in[$element['name']] = $element['value'];
                                    $file_data_in['field_type'] = $field_type;
                                    if ($element['name'] == 'name') {
                                        $file_data_in_name = $element['value'];
                                    }
                                }
                                $file_data[$file_data_in_name] = $file_data_in;
                            }
                            if ($field_type == 'risk_recommendation') {
                                foreach ($field_attr as $element) {
                                    if ($element['name'] == 'name') {
                                        array_push($rr_data, $element['value']);
                                    }
                                }
                            }

                            if ($field_type == 'select_risk_control') {
                                $arr = [];
                                foreach ($field_attr as $element) {
                                    if ($element['name'] == 'name') {
                                        $arr['name'] = $element['value'];
                                        // array_push($grading_data, $element['value']);
                                    }
                                    if ($element['name'] == 'label') {
                                        $arr['label'] = $element['value'];
                                        //array_push($grading_data, $arr);
                                    }
                                }
                                array_push($grading_data, $arr);
                            }
                        }
                    }
                }
            }

            $form['fields'] = json_encode($form['fields']);

            $params = [
                'resource' => $api_call->data,
            ];

            if (!$api_call->data->external_survey_company) {
                $params['options']['surveyors'] = static::getSurveyorOptions('0');
            } else {
                $params['options']['surveyors'] = static::getSurveyorOptions('1', $api_call->data->external_survey_company->id);
            }

            switch (true) {
                case !$api_call->data->external_survey_company && isset($api_call->data->surveyor->id):
                    $params['surveyor'] = sprintf('risk-engineer:%d', $api_call->data->surveyor->id);
                    break;

                case isset($api_call->data->surveyor->id):
                    $params['surveyor'] = sprintf('surveyor:%d', $api_call->data->surveyor->id);
                    break;

                case isset($api_call->data->external_survey_company->id):
                    $params['surveyor'] = sprintf('external-survey-company:%d', $api_call->data->external_survey_company->id);
                    break;

                default:
                    $params['surveyor'] = '';
                    break;
            }

            $survey_branch_id = isset($api_call->data->branch->id) ? $api_call->data->branch->id : null;

            $cacheKey="all-liberty-users-for-surveys";
            $lib_users=$this->getSetCache($cacheKey);
            if(!$lib_users){
                $dataEndpoint = static::get_api_uri('all', 'liberty-users');
                $lib_users=$this->getData($dataEndpoint);
                $this->getSetCache($cacheKey,$lib_users);
            }

            $params['lib_user']=[];
            $cacheKey="params-all-liberty-users";
            $params_lib_users=$this->getSetCache($cacheKey);
            if(!$params_lib_users){
                foreach ($lib_users->data as $lib_user) {
                    if ((is_null($lib_user->branch) || $lib_user->branch->id == $survey_branch_id) && $lib_user->role != 'underwriter') {
                        if (!array_key_exists($lib_user->role, $params['lib_user'])) {
                            $params['lib_user'][$lib_user->role] = [];
                        }
                        array_push($params['lib_user'][$lib_user->role], ['id' => $lib_user->id, 'name' => $lib_user->first_name . ' ' . $lib_user->last_name]);
                    }
                }
            }
            if (isset($api_call->data->broker_underwriter->id)) {
                $params['lib_user']['underwriter'] = [['id' => $api_call->data->broker_underwriter->id, 'name' => $api_call->data->broker_underwriter->first_name . ' ' . $api_call->data->broker_underwriter->last_name]];
            } elseif(isset($api_call->data->underwriter->id)) {
                $params['lib_user']['underwriter'] = [['id' => $api_call->data->underwriter->id, 'name' => $api_call->data->underwriter->first_name . ' ' . $api_call->data->underwriter->last_name]];
            }

            $params['lib_user'] = $params_lib_users;

            // dd($resource->data);

            if (isset($api_call->data) && isset($api_call->data->underwriter)) {
                $params['lib_user']['underwriter'] = [
                    [
                        'id' => $api_call->data->underwriter->id,
                        'name' => $api_call->data->underwriter->first_name . ' ' . $api_call->data->underwriter->last_name,
                    ],
                ];
            }

            if (isset($api_call->data) && isset($api_call->data->broker_underwriter)) {
                $params['lib_user']['underwriter'] = [
                    [
                        'id' => $api_call->data->broker_underwriter->id,
                        'name' => $api_call->data->broker_underwriter->first_name . ' ' . $api_call->data->broker_underwriter->last_name,
                    ],
                ];
            }

            $cacheKey="messages-for-survey-id-{$survey_id}";
            $messages=$this->getSetCache($cacheKey);
            if(!$messages){
                $dataEndpoint = static::get_api_uri('survey/' . $survey_id, 'messaging');
                $messages=$this->getData($dataEndpoint);
                $this->getSetCache($cacheKey,$messages);
            }

            $legacyText = [
                'Contact U/W within 24 hours' => 'Requires Improvement',
                'Multiple Requirements identified - monthly updates required' => 'Below Average',
                'Single Requirement - monitor progress' => 'Average',
                'Recommendations Only -generally reasonable controls' => 'Good',
                'Satisfactory' => 'Good',
                'Not Applicable' => 'Not Applicable / Not Assessed',
            ];

            $risk_grading_attributes = json_decode(Api::get('api/v1/microsite/risk-grading/' . (int)$submission->survey_id.'/true'));
            $this->updateInconsistentGrading($risk_grading_attributes->data->settings, $submission);

            $colors = RiskGradingHelper::getGradingColorCodes();
            return view(
                'surveys.report',
                array_merge(
                    $params,
                    [
                        'submission'    => $submission,
                        'rr_data'       => $rr_data,
                        'grading_data'  => $grading_data,
                        'legacyText'    => $legacyText,
                        'user_details'  => Session::get('user'),
                        're_options'    => static::getREOptions(),
                        'messages'      => $messages->data,
                        'user_obj'      => $user,
                        'policy_types'  => $policyTypes,
                        'colors'        => $colors,
                    ]
                )
            );            
            return view('surveys.report',$viewData);
        }

        return Redirect::route('surveys.index')
            ->with('error', 'Survey does not exist');
    }

    /**
     * Show Report for RE Review.
     *
     * @param $survey_id
     *
     * @return mixed
     */
    public function showREReviewReport(Request $request, $survey_id = 0)
    {
        //dd('here');
        $user = Session::get('user');
        $search = $request->get('search', '');
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 10000);

        $all_re_reviews = json_decode(
            Api::get(
                static::get_api_uri(
                    sprintf(
                        'all/%d/%d%s',
                        $page,
                        $limit,
                        ($search !== '' ? '?search=' . urlencode($search) . '&survey_type=RE Review' : '?survey_type=' . urlencode('RE Review'))
                    )
                )
            )
        );

        $re_actions = [];

        if (isset($all_re_reviews->response) && $all_re_reviews->response == 'success') {
            foreach ($all_re_reviews->data as $re_review) {
                // print_r($re_review); exit;
                $submission = $re_review->submission;

                $response = json_decode(Api::get('api/v1/risk-improvement/form/' . $re_review->survey_form));

                if ($response->response == 'success') {
                    $form = json_decode($response->data, true);

                    $file_data = [];
                    $rr_data = [];

                    if (isset($form) && isset($form['fields'])) {
                        $file_data_in = [];
                        foreach ($form['fields'] as $field_types) {
                            foreach ($field_types as $field_type => $field_attr) {
                                $file_data_in_name = 'no-name';
                                $file_data_in = [];

                                if ($field_type == 're_review') {
                                    foreach ($field_attr as $element) {
                                        if ($element['name'] == 'name') {
                                            array_push($rr_data, $element['value']);
                                        }
                                    }
                                }
                            }
                        }
                    }

                    if (!is_null($re_review->organisation)) {
                        array_push($re_actions, ['submission' => $submission, 'rr_data' => $rr_data, 'organisation_name' => $re_review->organisation->name]);
                    }
                }
            }
            //print_r($re_actions);
            return view('surveys.re-actions-report', ['re_actions' => $re_actions]);
        }

        return Redirect::route('surveys.index')
            ->with('error', 'Survey does not exist');
    }

    public function showLegacyRiskRecommendation($survey_id, $legacy_risk_recommendation_id)
    {
        $api_call = json_decode(Api::get(static::get_api_uri($survey_id, 'surveys')));

        if (isset($api_call->response) && $api_call->response == 'success') {
            $response = json_decode(Api::get('api/v1/legacy-survey-submission/' . $api_call->data->legacy_submissions->_id));
            // $submission = json_decode($response->data);
            $submission = json_decode(str_replace('\r\n', ' <br/>', $response->data));
            $rr_data = $submission;

            //$submission = str_replace('\n',' <br/>', $submission);

            $params = [
                'resource' => $api_call->data,
            ];

            //Get Attachments
            // print_r(Api::get('/api/v1/surveys/legacy-get-attachment-info/' . rtrim($legacy_risk_recommendation_id, "_") . '/' . $survey_id));exit;
            $api_attachments_call = json_decode(Api::get('/api/v1/surveys/legacy-get-attachment-info/' . rtrim($legacy_risk_recommendation_id, '_') . '/' . $survey_id));
            $attachments = $api_attachments_call->data;

            foreach ($attachments as $attachment) {
                $fileName_cloud = 'survey_uploads/' . $attachment->field_name . '/' . $attachment->cloud_file_name;
                $attachment->url = $this->survey->downloadLink($fileName_cloud, $attachment->file_name);
            }

            return view(
                'surveys.legacy-submission-show',
                array_merge(
                    $params,
                    array(
                        'resource' => $api_call->data,
                        'submission' => $submission,
                        'survey' => isset($api_call->data) ? $api_call->data : null,
                        'attachments' => $attachments,
                        'rr_data' => $rr_data,
                        'user_details' => Session::get('user'),
                        're_options' => static::getREOptions(),
                        'risk_improvement_id' => $legacy_risk_recommendation_id,
                        'legacy_risk_recommendation_id' => rtrim($legacy_risk_recommendation_id, '_'),
                    )
                )
            );
        }

        return Redirect::route('surveys.report', array('survey_id' => $survey_id))
            ->with('error', 'Could not find risk recommendation');
    }

    public function showRiskRecommendation($survey_id, $risk_recommendation_id)
    {
        $api_call = json_decode(Api::get(static::get_api_uri($survey_id, 'surveys')));

        if (isset($api_call->response) && $api_call->response == 'success' && isset($api_call->data->submissions)) {
            // $response_raw = Api::get('api/v1/survey-submission/' . $api_call->data->submissions->_id);
            $response = json_decode(Api::get('api/v1/survey-submission/' . $api_call->data->submissions->_id));
            //print_r($response->data); exit;
            // $response = json_decode(str_replace('\\r\\n','[br]', $response_raw));
            $submission = json_decode(str_replace('\r\n', ' <br/>', $response->data));
            //$submission = str_replace('\n',' <br/>', $submission);

            //print_r($submission); exit;

            $submission->json = $response->data;

            $response = json_decode(Api::get('api/v1/risk-improvement/form/' . $submission->form_id));

            if ($response->response == 'success') {
                $form = json_decode($response->data, true);

                $file_data = [];
                $rr_data = [];

                if (isset($form) && isset($form['fields'])) {
                    $file_data_in = [];
                    foreach ($form['fields'] as $field_types) {
                        foreach ($field_types as $field_type => $field_attr) {
                            $file_data_in_name = 'no-name';
                            $file_data_in = [];
                            if ($field_type == 'file' || $field_type == 'risk_recommendation') {
                                foreach ($field_attr as $element) {
                                    $file_data_in[$element['name']] = $element['value'];
                                    $file_data_in['field_type'] = $field_type;
                                    if ($element['name'] == 'name') {
                                        $file_data_in_name = $element['value'];
                                    }
                                }
                                $file_data[$file_data_in_name] = $file_data_in;
                            }
                            if ($field_type == 'risk_recommendation') {
                                foreach ($field_attr as $element) {
                                    if ($element['name'] == 'name') {
                                        array_push($rr_data, $element['value']);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            //$submission_title = $rr_data[0] . '_';

            foreach (get_object_vars($submission) as $key => $sub) {
                if (strpos($key, $risk_recommendation_id) !== false) {
                    if (strpos($key, $risk_recommendation_id) === false) {
                        unset($submission->$key);
                    } else {
                        $new_key = str_replace($risk_recommendation_id, '', $key);
                        $submission->$new_key = $sub;
                    }
                }
            }

            $form['fields'] = json_encode($form['fields']);

            $params = [
                'resource' => $api_call->data,
            ];

            if (!$api_call->data->external_survey_company) {
                $params['options']['surveyors'] = static::getSurveyorOptions('0');
            } else {
                $params['options']['surveyors'] = static::getSurveyorOptions('1', $api_call->data->external_survey_company->id);
            }

            switch (true) {
                case !$api_call->data->external_survey_company && isset($api_call->data->surveyor->id):
                    $params['surveyor'] = sprintf('risk-engineer:%d', $api_call->data->surveyor->id);
                    break;

                case isset($api_call->data->surveyor->id):
                    $params['surveyor'] = sprintf('surveyor:%d', $api_call->data->surveyor->id);
                    break;

                case isset($api_call->data->external_survey_company->id):
                    $params['surveyor'] = sprintf('external-survey-company:%d', $api_call->data->external_survey_company->id);
                    break;

                default:
                    $params['surveyor'] = '';
                    break;
            }

            $lib_users = json_decode(Api::get(static::get_api_uri('all', 'liberty-users')));

            $params['lib_user'] = [];

            $survey_branch_id = isset($api_call->data->branch->id) ? $api_call->data->branch->id : null;

            foreach ($lib_users->data as $lib_user) {
                if ((is_null($lib_user->branch) || $lib_user->branch->id == $survey_branch_id) && $lib_user->role != 'underwriter') {
                    if (!array_key_exists($lib_user->role, $params['lib_user'])) {
                        $params['lib_user'][$lib_user->role] = [];
                    }
                    array_push($params['lib_user'][$lib_user->role], ['id' => $lib_user->id, 'name' => $lib_user->first_name . ' ' . $lib_user->last_name]);
                }
            }
            if (isset($api_call->data->broker_underwriter->id)) {
                $params['lib_user']['underwriter'] = [['id' => $api_call->data->broker_underwriter->id, 'name' => $api_call->data->broker_underwriter->first_name . ' ' . $api_call->data->broker_underwriter->last_name]];
            } else {
                $params['lib_user']['underwriter'] = [['id' => $api_call->data->underwriter->id, 'name' => $api_call->data->underwriter->first_name . ' ' . $api_call->data->underwriter->last_name]];
            }

            //Get Attachments
            $api_attachments_call = json_decode(Api::get('/api/v1/surveys/get-attachment-info/' . rtrim($risk_recommendation_id, '_') . '/' . $survey_id));
            $attachments = $api_attachments_call->data;

            foreach ($attachments as $attachment) {
                $fileName_cloud = 'survey_uploads/' . $attachment->field_name . '/' . $attachment->cloud_file_name;
                $attachment->url = $this->survey->downloadLink($fileName_cloud, $attachment->file_name);
            }

            $messages = json_decode(Api::get(static::get_api_uri('survey/' . $survey_id, 'messaging')));
            //dd($rr_data);

            return view(
                'surveys.submission-show',
                array_merge(
                    $params,
                    array(
                        'resource' => $api_call->data,
                        'submission' => $submission,
                        'survey' => isset($api_call->data) ? $api_call->data : null,
                        'attachments' => $attachments,
                        'rr_data' => $rr_data,
                        'user_details' => Session::get('user'),
                        're_options' => static::getREOptions(),
                        'messages' => $messages->data,
                        'risk_improvement_id' => $risk_recommendation_id,
                        'risk_recommendation_id' => rtrim($risk_recommendation_id, '_'),
                    )
                )
            );
        }

        return Redirect::route('surveys.report', array('survey_id' => $survey_id))
            ->with('error', 'Could not find risk recommendation');
    }

    /**
     * Parse data before storage.
     *
     * @param array $data
     *
     * @return array parsed version of $data
     */
    public function parseDataBeforeStorage($data)
    {
        $ids = [
            'organisation',
            'branch',
            'underwriter',
            'surveyor',
        ];

        foreach ($ids as $id) {
            if (isset($data[$id])) {
                $data[sprintf('%s_id', $id)] = $data[$id];
                unset($data[$id]);
            }
        }

        if (isset($data['broker']['organisation'])) {
            $data['broker_id'] = $data['broker']['organisation'];
            unset($data['broker']['organisation']);
        }

        if (isset($data['deadlines']) && count($data['deadlines'])) {
            foreach ($data['deadlines'] as $key => $value) {
                $data['deadlines'][$key] = date(
                    'Y-m-d H:i:s',
                    strtotime(str_replace('/', '-', $value))
                );
            }
        }

        return $data;
    }

    public function deleteFile($id, $file_id)
    {
        $data['file_id'] = $file_id;

        $deleteResponse = Api::post('api/v1/surveys/destroy-survey-attachment-empty', $data);
        $decodedResponse = json_decode($deleteResponse);

        return Response::json($decodedResponse);
    }

    public function deleteLegacyFile($id, $file_id)
    {
        $data['file_id'] = $file_id;

        $deleteResponse = Api::post('api/v1/surveys/destroy-legacy-survey-attachment-empty', $data);
        $decodedResponse = json_decode($deleteResponse);

        return Response::json($decodedResponse);
    }

    public function uploadFiles(Request $request)
    {
        if (strtolower($request->method()) == 'post') {
            $fields = $_FILES;

            foreach ($fields as $key => $val) {
                $file_field_name = $key;
            }

            $file = $request->file($file_field_name);
            $size = $request->file($file_field_name)->getSize();
            $path = $file->getRealPath();
            $type = $file->getMimeType();

            $extension = $request->file($file_field_name)->getClientOriginalExtension(); // getting image extension

            $file = $request->file($file_field_name);
            $uuid = Str::uuid()->toString();

            $fileName = strtolower(preg_replace('/[\s_]|^%20/', '-', $request->file($file_field_name)->getClientOriginalName()));

            $upload = $this->survey->upload($file, $uuid, 'survey_attachments/' . $file_field_name);

            if ($upload['response'] == 'success') {
                $data['file_name'] = $fileName;
                $data['cloud_file_name'] = $uuid;

                // If the form has a survey id
                if ($request->get('survey_id') && !$request->has('from')) {
                    $data['survey_id'] = $request->get('survey_id'); // form has id
                    $insertResponse = Api::post('api/v1/surveys/add-survey-attachments-empty', $data);
                    $this->recacheRelatedData('');
                } else {
                    // form does not have id
                    $insertResponse = Api::post('api/v1/surveys/add-survey-attachments-empty', $data);
                    $decodedResponse = json_decode($insertResponse);
                    Session::push('ApiFiles', $decodedResponse->{'id'});
                }

                $data['field_name'] = $file_field_name;

                return Response::json($insertResponse);
            }
        }
    }

    public function uploadAgenda(Request $request)
    {
        if (strtolower($request->method()) == 'post') {
            $fields = $_FILES;

            foreach ($fields as $key => $val) {
                $file_field_name = $key;
            }

            $file = $request->file($file_field_name);
            $size = $request->file($file_field_name)->getSize();
            $survey_id = $request->get('survey_id');
            $path = $file->getRealPath();
            $type = $file->getMimeType();

            $extension = $request->file($file_field_name)->getClientOriginalExtension(); // getting image extension

            $file = $request->file($file_field_name);
            $uuid = Str::uuid()->toString();

            $fileName = strtolower(preg_replace('/[\s_]|^%20/', '-', $request->file($file_field_name)->getClientOriginalName()));

            $upload = $this->survey->upload($file, $uuid, 'survey_attachments/agenda');

            if ($upload['response'] == 'success') {
                $data['file_name'] = $fileName;
                $data['cloud_file_name'] = $uuid;

                // If the form has a survey id
                if ($request->get('survey_id')) {
                    $data['survey_id'] = $request->get('survey_id'); // form has id
                    $insertResponse = json_decode(Api::post('api/v1/surveys/survey-agenda', $data));
                    $this->recacheRelatedData('');
                    return Response::json($insertResponse);
                }
            }
        }
    }

    /**
     * Get additional View parameters.
     *
     * @param  string  $view
     * @param null   $resource_id
     *
     * @return array
     */
    public function getAdditionalViewParams(string $view, $resource)
    {
        $loginType = Session::get('user')->login_type;
        $params = [];

        if (in_array($view, ['create', 'edit'])) {
            $api_calls = [
                'branches',
                'organisations',
                'underwriters',
                'aspenusers',
                'brokers',
                'forms',
            ];

            foreach ($api_calls as $key) {
                $params['options'][$key] = OptionListService::get($key);
                if ($key === 'forms') {
                    $riForms = $params['options']['forms']->response === 'success' ? $params['options']['forms']->data : [];
                    $finalizeForms = [];
                    foreach ($riForms as $key => $form) {
                        if (!str_contains($form, 'OLD - DO NOT USE')) {
                            $finalizeForms[$key] = $form;
                        }
                    }

                    $params['options']['forms']->data = json_decode(json_encode($finalizeForms));
                }
            }

            if ($loginType == 'broker-user') {
                $brokerId    = Session::get('user')->broker_id;
                $params['options']['organisations'] = GetBrokerOrgsOptionService::get($brokerId);
                $params['options']['broker_users']  = GetOrgBrokerUserOptionService::get($brokerId);
            }

            // If not broker but there's a broker_id in the request data pass
            if ($loginType !== 'broker-user' && isset($resource->broker->id)) {
                $params['options']['broker_users']  = GetOrgBrokerUserOptionService::get($resource->broker->id);

            }

            $params['options']['visit-arrangement'] = [
                'client' => 'Arrange visit with the client',
                'broker' => 'Arrange visit with the broker',
            ];

            $params['options']['notify-underwriter'] = [
                1 => 'Yes',
                0 => 'No',
            ];

            $params['options']['user_details'] = Session::get('user');
        }

        if (in_array($view, ['create'])) {
            $organisation_array = (array) $params['options']['organisations'];
            $organisation_first = key($organisation_array);

            $rs_pn = json_decode(Api::get('/api/v1/organisation/' . $organisation_first . '/policy-numbers'));

            $params['options']['policy_numbers'] = isset($rs_pn->data) ? $rs_pn->data : [];
        }

        if (in_array($view, ['edit'])) {
            if (isset($resource->organisation->id)) {
                $params['options']['policy_numbers'] = json_decode(Api::get('/api/v1/organisation/' . $resource->organisation->id . '/policy-numbers'))->data;
            }
        }

        return $params;
    }

    /**
     * Get validation rules for a specific method (store/update/etc).
     *
     * @param string $method
     *
     * @return array of validation rules
     */
    public function getValidatorRules(Request $request, $method)
    {
        if ($request->get('location') == 'Select Location') {
            $rules = [
                'organisation'                   => 'numeric|min:1',
                'location'                       => 'required|min:16',
                'branch'                         => 'numeric|min:1',
                'policy_id'                      => 'required',
                'visit_arrangement'              => 'required',
                'broker.organisation'            => 'required',
                'deadlines.survey'               => 'required',
                'deadlines.client_survey_report' => 'required',
                'deadlines.underwriter'          => 'required',
                'survey_form'                    => 'required',
            ];
        } else {
            if ($request->has('location')) {
                $rules = [
                    'organisation'                   => 'numeric|min:1',
                    'location'                       => 'required',
                    'branch'                         => 'numeric|min:1',
                    'policy_id'                      => 'required',
                    'visit_arrangement'              => 'required',
                    'broker.organisation'            => 'required',
                    'deadlines.survey'               => 'required',
                    'deadlines.client_survey_report' => 'required',
                    'deadlines.underwriter'          => 'required',
                    'survey_form'                    => 'required',
                ];
            } else {
                $rules = [
                    'organisation'                   => 'numeric|min:1',
                    'branch'                         => 'numeric|min:1',
                    'policy_id'                      => 'required',
                    'visit_arrangement'              => 'required',
                    'broker.organisation'            => 'required',
                    'deadlines.survey'               => 'required',
                    'deadlines.client_survey_report' => 'required',
                    'deadlines.underwriter'          => 'required',
                    'survey_form'                    => 'required',
                ];
            }
        }

        if ($method == 'store') {
            unset($rules['deadlines.client_survey_report']);
            unset($rules['deadlines.underwriter']);
        }

        if ($method == 'update_status') {
            $rules = ['branch' => 'numeric|min:1'];
        }

        if ($request->has('survey_type') && $request->get('survey_type') == 'rereview') {
            unset($rules['deadlines.client_survey_report']);
            unset($rules['deadlines.underwriter']);
        }

        return $rules;
    }

    /**
     * Get additional view parameters for a specific view (index/create/edit).
     *
     * @param string $view
     * @param  array  $params
     * @param array  $params
     *
     * @return array of additional parameters
     */
    public function getRouteParams(Request $request, $view, array $params = [])
    {
        if ($request->has('branch')) {
            switch ($view) {
                case 'index':
                case 'create':
                case 'edit':
                    return $params;
            }
        } else {
            switch ($view) {
                case 'index':
                case 'create':
                case 'edit':
                    return [];
            }
        }
    }

    /**
     * Get validation messages for a specific method (store/update/etc).
     *
     * @param string $method
     *
     * @return array of validation rules
     */
    public function getValidatorMessages($method)
    {
        return [
            'required' => 'The :attribute is required.',
            'min' => 'The :attribute is required.',
        ];
    }

    public function downloadAgenda($cloud_file_name, $file_name)
    {
        $agenda_url = $this->survey->download('survey_attachments/agenda/' . $cloud_file_name, $cloud_file_name);

        if ($agenda_url) {
            return Response::download($agenda_url, $file_name);
        }
    }

    public function downloadFiles($cloud_file_name, $file_name)
    {
        $agenda_url = $this->survey->download('survey_attachments/file/' . $cloud_file_name, $cloud_file_name);

        if ($agenda_url) {
            return Response::download($agenda_url, $file_name);
        }
    }

    public function delete(Request $request, $id)
    {
        if ($request->has('auto_scheduled_surveys') && $request->get('auto_scheduled_surveys') == 'yes') {
            $response = json_decode(Api::get(static::get_api_uri($id, 'surveys/delete') . '?auto_scheduled_surveys=yes'));
            $param = '?auto_scheduled_surveys=yes';
        } else {
            $response = json_decode(Api::get(static::get_api_uri($id, 'surveys/delete')));
            $param = '';
        }

        if ($response->response == 'success') {
            if ($response->type == 'survey') {
                return Redirect::to('/surveys' . $param)->with(
                    $response->response,
                    $response->message
                );
            } else {
                return Redirect::to('/surveys/re-reviews' . $param)->with(
                    $response->response,
                    $response->message
                );
            }
        } else {
            return Redirect::back()->with(
                $response->response,
                $response->message
            );
        }
    }

    /**
     * Get validation attribute names.
     *
     * @return array of validation attribute names
     */
    public function getValidatorAttributeNames()
    {
        return [
            'type' => 'survey type',
            'client.name' => 'client\'s name',
            'client.phone' => 'client\'s phone number',
            'client.address_1' => 'client\'s first address line',
            'client.city' => 'client\'s city',
            'client.country' => 'client\'s country',
            'client.postcode' => 'client\'s postcode',
            'broker.organisation' => 'broker\'s organisation',
            'broker.name' => 'broker\'s name',
            'broker.phone' => 'broker\'s phone number',
            'deadlines.survey' => 'survey deadline',
            'deadlines.client_survey_report' => 'client survey report deadline',
            'deadlines.underwriter' => 'underwriter report deadline',
            'survey_form' => 'Survey form',
        ];
    }

    public function showPdf(Request $request)
    {
        // $response = (object) ['response' => 'success']; // temp

        // if ($response->response == 'success') {
        //  return view('surveys.show_print');
        // } else {
        //  return Redirect::route('surveys.index')->with(['error' => 'Error, please try again']);
        // }

        // echo "<pre>";
        // print_r(Session::get('user')); exit;
        $search = $request->get('search', '');
        $page = $request->get('page', 1);
        $limit = 10000;

        // filter by mga scheme
        $get = [];
        $request->has('mga_scheme') ? $get[] = 'mga_scheme=' . $request->get('mga_scheme') : '';
        $request->has('search') ? $get[] = 'search=' . $request->get('search') : '';
        $request->has('survey_type') ? $get[] = 'survey_type=' . $request->get('survey_type') : '';

        if (Session::get('user')->login_type == 'aspen-user') {
            $resources = json_decode(
                Api::get(
                    static::get_api_uri(
                        sprintf(
                            'all/%d/%d%s%s',
                            $page,
                            $limit,
                            '?branch_type=aspen-user',
                            implode('&', $get)
                        )
                    )
                )
            );
        } else {
            if (Session::get('user')->login_type == 'broker-user') {
                $resources = json_decode(
                    Api::get(
                        static::get_api_uri(
                            sprintf(
                                'broker/%d/%d/%d%s',
                                Session::get('user')->broker_id,
                                $page,
                                $limit,
                                '?' . implode('&', $get)
                            )
                        )
                    )
                );
            } else {
                $resources = json_decode(
                    Api::get(
                        static::get_api_uri(
                            sprintf(
                                'all/%d/%d%s',
                                $page,
                                $limit,
                                '?' . implode('&', $get)
                            )
                        )
                    )
                );
            }
        }

        //get mga schemes
        $schemes = GetMgaSchemeService::get('');

        if ($resources->response == 'success') {
            return view(
                'surveys.show_print',
                array_merge(
                    [
                        'schemes' => $schemes->data,
                        'resources' => $resources->data,
                        'total' => $resources->total,
                        'limit' => $limit,
                        'page' => $page,
                        'search' => $search,
                        'user_details' => Session::get('user'),
                        'link' => static::ROUTE_PREFIX . '.index',
                    ],
                    static::getAdditionalViewParams('index', $resources->data)
                )
            );
        } else {
            throw new \Exception($resources->message);
        }
    }

    public function printPdf(Request $request)
    {
        $html = stripslashes($request->get('html'));
        $orientation = $request->get('orientation');
        $html = preg_replace('/[\t\n\r\0\x0B]/', '', $html);
        $html = preg_replace('/([\s])\1+/', ' ', $html);
        $html = trim($html);
        $pdf = Pdf::loadHTML($html)->setPaper('a4', $orientation);

        $pdf->output();

        return $pdf->stream();
    }

    public function exportOrganisation($organisation_id)
    {
        $csrData = $this->csrReport->getCSRReport($organisation_id);

        $data = $csrData['data'];
        $organisation = $csrData['organisation'];
        $survey_ids = $csrData['survey_ids'];
        $rcas = $csrData['rcas'];
        $rca_values = $csrData['rca_values'];

        $excel = Excel::create(
            'survey-findings-' . $organisation . '-' . Carbon::now()->format('d-m-Y'),
            function ($excel) use ($organisation, $data, $survey_ids, $rcas, $rca_values) {
                $excel->sheet(
                    'Survey Findings',
                    function ($sheet) use ($data, $survey_ids, $rcas, $rca_values) {
                        $sheet->fromArray($data, null, 'A1', false, false);
                        $sheet->freezeFirstRowAndColumn();
                        // Font family
                        $sheet->setFontFamily('Calibri');

                        // Font size
                        $sheet->setFontSize(15);

                        // Font bold
                        $sheet->setFontBold(false);

                        if (isset($data[0]) && count($data[0]) > 0) {
                            $lastCell = $this->num2alpha(count($data[0]) - 1) . (string) (count($data));
                            $sheet->setBorder('A1:' . $lastCell, 'thin');
                        }

                        for ($i = 0; $i < intval(count($rcas)); ++$i) {
                            foreach ($survey_ids as $alpha) {
                                $current_cell = (string) ($alpha . intval($i + 2));

                                $sheet->cell(
                                    $current_cell,
                                    function ($cell) use ($sheet, $current_cell) {
                                        $cell_value = $sheet->getCell($current_cell)->getValue();

                                        if (strpos($cell_value, '#') !== false) {
                                            $cell->setBackground($cell_value);
                                            $cell->setValue('');
                                        }
                                    }
                                );
                            }
                        }
                    }
                );
            }
        )->download('xlsx');
    }

    public function exportRiskGradings(Request $request)
    {
        if (Cache::has("rrgradings-all")) {
            $submissions = Cache::get("rrgradings-all");
        } else {
            $submissions  = json_decode(Api::get('api/v1/survey-submission/rrgradings-all'));
            Cache::put("rrgradings-all", $submissions, 3000);
        }

        if (Cache::has("all-surveys")) {
            $surveys = Cache::get("all-surveys");
        } else {
            $surveys  = json_decode(Api::get('api/v1/surveys/legacy-srf'));
            Cache::put("all-surveys", $surveys, 3000);
        }

        $survey_ids   = [];
        $legacy_srfs  = [];
        $survey_count = 1;
        $organisation = 'all';

        if (!empty($surveys)) {
            foreach ($surveys->data as $survey) {
                $survey_ids[$survey->id] = $this->num2alpha($survey_count);
                if (isset($survey->legacy_srf) && $survey->legacy_srf != null) {
                    $legacy_srfs[$survey->id] = $survey->legacy_srf;
                }

                ++$survey_count;
            }
        }

        $rr_colors = [
            'Requires Improvement' => '#fc0d1b',
            'Below Average' => '#fdbf2d',
            'Average' => '#fffd38',
            'Good' => '#00b050',
            'Superior' => '#0070c0',
            'Not Applicable / Not Assessed' => '#dddddd',
            'Contact U/W within 24 hours' => '#fc0d1b',
            'Multiple Requirements identified - monthly updates required' => '#fdbf2d',
            'Single Requirement - monitor progress' => '#fffd38',
            'Recommendations Only -generally reasonable controls' => '#00b050',
            'Satisfactory' => '#00b050',
            'Not Applicable' => '#dddddd',
        ];

        $rcas       = [];
        $srfs       = [];
        $rca_values = [];
        $srf_colors = [];
        $lob        = $request->has('lob') ? $request->get('lob') : '';
        if ($submissions) {
            foreach ($submissions->data as $submission_obj) {
                if (isset($submission_obj->risk_gradings)) {
                    foreach ($submission_obj->risk_gradings as $risk_grading_key => $risk_grading_value) {
                        if (isset($submission_obj->$risk_grading_key) && (isset($submission_obj->survey) && isset($submission_obj->survey->policy_name) && $submission_obj->survey->policy_name == $lob || in_array($lob, ['', 'All']))) {
                            
                            $rcas[] = (string) $risk_grading_key;
                            
                            array_push($rcas, (string) $risk_grading_key);
                            try {
                                $srf = in_array($submission_obj->survey->id, $legacy_srfs) ? $legacy_srfs[$submission_obj->survey->id] : $submission_obj->survey->id;
                            } catch (\Exception $e) {
                                continue;
                            }

                            $srfs[(string) $srf] = (string) $srf;

                            if (!isset($srf_colors[(string) $srf])) {
                                $srf_colors[(string) $srf] = [];
                            }

                            $srf_colors[(string) $srf][(string) $risk_grading_key] = $rr_colors[$submission_obj->$risk_grading_key];

                            $rca_values[(string) $risk_grading_key] = $risk_grading_value;
                        }
                    }
                }
            }
        }

        $rcas       = array_unique($rcas);
        $srfs       = array_unique($srfs); // can be asort'ed if required
        $rca_values = array_unique($rca_values);
        $data       = [];

        // SFAs
        $data[0]   = [];
        $sfa_count = 0;

        foreach ($srfs as $srf) {
            if ($sfa_count == 0) {
                array_push($data[0], '');
            }

            array_push($data[0], 'SRF' . $srf);

            ++$sfa_count;
        }

        $var = [];

        // RCAs
        foreach ($rcas as $rca) {
            $arr = [];

            if (array_key_exists((string) $rca, $rca_values)) {
                array_push($arr, (string) $rca_values[(string) $rca]);
            } else {
                array_push($arr, $rca);
            }

            foreach ($srfs as $srf) {
                $color = isset($srf_colors[intval($srf)][(string) $rca]) ? $srf_colors[intval($srf)][(string) $rca] : '';

                array_push($arr, $color);
            }

            array_push($var, $arr);
        }

        foreach ($var as $va) {
            array_push($data, $va);
        }

        // Check if data is empty redirect back to the page cancelling the excel download
        if (isset($data) && !empty($data) && empty($data[0])) {
            return Redirect::back()->withError('No data to be exported');
        }

        $surveyExportFileName = 'survey-findings-' . $organisation . '-' . Carbon::now()->format('d-m-Y') . '.xlsx';
        return Excel::download(new SurveyFindingsExport($data, $survey_ids, $rcas), $surveyExportFileName);
    }

    public function standardRiskGradingsExport()
    {
        return view('surveys.export.standard-risk-gradings', []);
    }

    public function exportStandardRiskGradingsData(Request $request)
    {
        // Export all surveys LOB choosen is ALL
        $lineOfBusiness = $request->has('lob') ? $request->get('lob') : '';
        $export = json_decode(Api::post('api/v1/standard-risk/export', [
            'line_of_business' => $lineOfBusiness,
            'user_id' => Session::get('user')->id
        ]));
    }

    public function exportRiskGradingsFilter()
    {
        return view('surveys.riskGradingsFilter', []);
    }

    public function exportSurveys(Request $request)
    {
        $rr_colors = [
            'Requires Improvement' => '#fc0d1b',
            'Below Average' => '#fdbf2d',
            'Average' => '#fffd38',
            'Good' => '#00b050',
            'Superior' => '#0070c0',
            'Not Applicable / Not Assessed' => '#dddddd',
            'Contact U/W within 24 hours' => '#fc0d1b',
            'Multiple Requirements identified - monthly updates required' => '#fdbf2d',
            'Single Requirement - monitor progress' => '#fffd38',
            'Recommendations Only -generally reasonable controls' => '#00b050',
            'Satisfactory' => '#00b050',
            'Not Applicable' => '#dddddd',
        ];

        $legacyText = [
            'Contact U/W within 24 hours' => 'Requires Improvement',
            'Multiple Requirements identified - monthly updates required' => 'Below Average',
            'Single Requirement - monitor progress' => 'Average',
            'Recommendations Only -generally reasonable controls' => 'Good',
            'Satisfactory' => 'Good',
            'Not Applicable' => 'Not Applicable / Not Assessed',
        ];

        if (!$request->has('surveyForm')) {
            return Redirect::to('/surveys/export/fields/filter')->with('error', 'An error occured, please try again.');
        }

        $formId = $request->get('surveyForm');
        $limit = $request->has('limit') ? $request->get('limit') : 50;
        $dateFrom = $request->get('from_date');
        $dateFrom = str_replace('/', '-', $dateFrom);
        if ($dateFrom != '') {
            $dateFrom = date('Y-m-d', strtotime($dateFrom));
        }
        $dateTo = $request->get('to_date');
        $dateTo = str_replace('/', '-', $dateTo);
        if ($dateTo != '') {
            $dateTo = date('Y-m-d', strtotime($dateTo));
        }

        $params = '?limit=' . $limit;
        $params .= '&dateFrom=' . $dateFrom . '&dateTo=' . $dateTo;

        $form = json_decode(Api::get('/api/v1/risk-improvement/form/' . $formId));

        if ($form->response == 'success') {
            $formDefinition = json_decode($form->data, true);
            $formFields = $formDefinition['fields'];
            $formSections = $formDefinition['section_names'];
            $submissionsCall = json_decode(Api::get('/api/v1/survey-submission/form/' . $formId . $params));

            if ($submissionsCall->response == 'success') {
                $submissions = json_decode($submissionsCall->data, true);
            }
        }

        // echo "<pre>";
        // print_r($submissions); exit;
        $excelFields = [];
        foreach ($formFields as $form_field) {
            // if (key($form_field) == 'select_risk_control' || key($form_field) == 'risk_recommendation') {
            //     continue;
            // }

            foreach ($form_field as $field_type => $form_values) {
                $fieldArray = [];
                $fieldArray['type'] = $field_type;
                foreach ($form_values as $form_value) {
                    if (in_array($form_value['name'], ['label', 'name', 'section'])) {
                        $fieldArray[$form_value['name']] = $form_value['value'];
                    }
                }
                $excelFields[] = $fieldArray;
            }
        }

        // echo "<pre>";
        // print_r($excelFields); exit;

        $new_fields = [];
        $i = 0;
        $excelRow = [];
        // $pdfExists = [];

        // for ($t=0; $t <= 2 ; $t++) {

        //     $exists = $this->files->lO('survey_attachments/pdf/', $t*1000);

        //     foreach ($exists['Contents'] as $exist) {
        //         $fileName = basename($exist['Key']);
        //         if (strpos($fileName, 'UWR_') !== false) {
        //             array_push($pdfExists, $fileName);
        //         }
        //     }

        // }

        foreach ($submissions as $submission_arr) {
            $fileName = 'UWR_' . $submission_arr['survey_id'] . '.pdf';
            if ($this->files->exists('survey_attachments/pdf/UWR_' . $submission_arr['survey_id'] . '.pdf')) {
                $pdfLink = $this->files->link('survey_attachments/pdf/' . $fileName, '7 days');
            } else {
                $pdfLink = $this->printPdfBinder($request, $submission_arr['_id'], 'UWR');
            }
            foreach ($excelFields as $excelField) {
                if (isset($excelField['name']) && isset($excelField['label'])) {
                    $excelRow[$i]['SRF'] = 'SRF' . $submission_arr['survey_id'];

                    $excelRow[$i]['PDF Link Text'] = $pdfLink;

                    $currentRow = $i + 2;
                    $excelRow[$i]['PDF Link'] = '=HYPERLINK(B' . $currentRow . ',B' . $currentRow . ')';

                    $excelRow[$i]['Client Name'] = isset($submission_arr['survey'][0]) && isset($submission_arr['survey'][0]['organisation']) && isset($submission_arr['survey'][0]['organisation']['name']) ? $submission_arr['survey'][0]['organisation']['name'] : 'N/A';
                    $excelRow[$i]['Postcode'] = isset($submission_arr['survey'][0]) && isset($submission_arr['survey'][0]['organisation']) && isset($submission_arr['survey'][0]['organisation']['postcode']) ? $submission_arr['survey'][0]['organisation']['postcode'] : 'N/A';
                    $risk_rec = [];
                    if ($excelField['type'] == 'risk_recommendation') {
                        $excelRow[$i][$excelField['label'] . ' (field name: ' . $excelField['name'] . ')'] = '';
                        for ($j = 1; $j <= 15; ++$j) {
                            if (isset($submission_arr[$excelField['name'] . '_' . $j . '_' . 'classification']) && $submission_arr[$excelField['name'] . '_' . $j . '_' . 'classification'] != '') {
                                $excelRow[$i][$excelField['label'] . ' (field name: ' . $excelField['name'] . ')'] .= isset($submission_arr[$excelField['name'] . '_' . $j . '_' . 'ref']) ? 'Ref: ' . $submission_arr[$excelField['name'] . '_' . $j . '_' . 'ref'] . "\n" : 'Ref: \n';
                                $excelRow[$i][$excelField['label'] . ' (field name: ' . $excelField['name'] . ')'] .= isset($submission_arr[$excelField['name'] . '_' . $j . '_' . 'classification']) ? 'Classification: ' . $submission_arr[$excelField['name'] . '_' . $j . '_' . 'classification'] . "\n" : 'Classification: \n';
                                $excelRow[$i][$excelField['label'] . ' (field name: ' . $excelField['name'] . ')'] .= isset($submission_arr[$excelField['name'] . '_' . $j . '_' . 'title']) ? 'Title: ' . $submission_arr[$excelField['name'] . '_' . $j . '_' . 'title'] . "\n" : 'Title: \n';
                                $excelRow[$i][$excelField['label'] . ' (field name: ' . $excelField['name'] . ')'] .= isset($submission_arr[$excelField['name'] . '_' . $j . '_' . 'cba']) ? 'Indicative Costs: ' . $submission_arr[$excelField['name'] . '_' . $j . '_' . 'cba'] . "\n" : 'Indicative Costs: \n';
                                $excelRow[$i][$excelField['label'] . ' (field name: ' . $excelField['name'] . ')'] .= isset($submission_arr[$excelField['name'] . '_' . $j . '_' . 'required_by']) ? 'Required by: ' . $submission_arr[$excelField['name'] . '_' . $j . '_' . 'required_by'] . "\n" : 'Required by: \n';
                                $excelRow[$i][$excelField['label'] . ' (field name: ' . $excelField['name'] . ')'] .= isset($submission_arr[$excelField['name'] . '_' . $j . '_' . 'description']) ? 'Description: ' . $submission_arr[$excelField['name'] . '_' . $j . '_' . 'description'] . "\n" : 'Description: \n';
                                $excelRow[$i][$excelField['label'] . ' (field name: ' . $excelField['name'] . ')'] .= isset($submission_arr[$excelField['name'] . '_' . $j . '_' . 'action']) ? 'Action: ' . $submission_arr[$excelField['name'] . '_' . $j . '_' . 'action'] . "\n" : 'Action: \n';
                            }
                        }
                    } else {
                        $excelRow[$i][$excelField['label'] . ' (field name: ' . $excelField['name'] . ')'] = isset($submission_arr[$excelField['name']]) ? (!is_array($submission_arr[$excelField['name']]) ? $submission_arr[$excelField['name']] : json_encode($submission_arr[$excelField['name']])) : '-';
                    }
                }
            }
            ++$i;
        }

        if (!isset($excelRow)) {
            return Redirect::to('/surveys/export/fields/filter')->with('error', 'No submissions found for this form.');
        }

        if (count($excelRow) > 0) {
            $headerRow = array_keys($excelRow[0]);
        } else {
            return Redirect::to('/surveys/export/fields/filter')->with('error', 'No submissions found for this form. ID:' . $formId);
        }

        array_unshift($excelRow, $headerRow);

        $surveyExportFileName = 'risk-recommendations-' . date('d-m-Y') . '.xlsx';
        return Excel::download(new SurveyExport($excelRow), $surveyExportFileName);
        
        // $excel = Excel::create(
        //     'survey-data-export-' . Carbon::now()->format('d-m-Y'),
        //     function ($excel) use ($excelRow) {
        //         $excel->sheet(
        //             'Survey Findings',
        //             function ($sheet) use ($excelRow) {
        //                 $sheet->fromArray($excelRow, null, 'A1', false, false);
        //                 $sheet->freezeFirstRowAndColumn();
        //                 // Font family
        //                 $sheet->setFontFamily('Calibri');

        //                 // Font size
        //                 $sheet->setFontSize(15);

        //                 // Font bold
        //                 $sheet->setFontBold(false);

        //                 $sheet->getDefaultStyle()
        //                     ->getAlignment()
        //                     ->applyFromArray(
        //                         array(
        //                             'horizontal' => \PHPExcel_Style_Alignment::HORIZONTAL_LEFT,
        //                             'vertical' => \PHPExcel_Style_Alignment::VERTICAL_TOP,
        //                             'wrap' => true,
        //                         )
        //                     );
        //                 $sheet->getColumnDimension('B')->setVisible(false);
        //             }
        //         );
        //     }
        // )->download('xlsx');
    }

    public function exportSurveysFilter()
    {
        $forms = json_decode(Api::get('/api/v1/risk-improvement/form/list?type=Survey'));

        return view('surveys.surveyFormsFilter', ['forms' => $forms->data]);
    }

    public function printPdfBinder(Request $request, $submission, $show = 'UWR')
    {
        $response    = json_decode(Api::get('api/v1/survey-submission/' . $submission));
        $policyTypes = json_decode(Api::get('/api/v1/standard-risk/policy-types'));
        $submission  = json_decode($response->data);

        $submission->json = $response->data;

        $param_resource = json_decode(
            Api::get(
                static::get_api_uri($submission->survey_id)
            )
        );

        $params = [
            'resource' => $param_resource->data,
        ];

        $response = json_decode(Api::get('api/v1/risk-improvement/form/' . $submission->form_id));

        if ($response->response == 'success') {
            set_time_limit(600);
            $form = json_decode($response->data, true);
            //print_r($form);exit;
            $form['fields'] = json_encode($form['fields']);

            // print_r([
            //     'form' => $form,
            //     'submission' => $submission,
            //     ]); exit;

            $rr_colors = [
                'Requires Improvement' => '#fc0d1b',
                'Below Average' => '#fdbf2d',
                'Average' => '#fffd38',
                'Good' => '#00b050',
                'Superior' => '#0070c0',
                'Not Applicable / Not Assessed' => '#dddddd',
                'Contact U/W within 24 hours' => '#fc0d1b',
                'Multiple Requirements identified - monthly updates required' => '#fdbf2d',
                'Single Requirement - monitor progress' => '#fffd38',
                'Recommendations Only -generally reasonable controls' => '#00b050',
                'Satisfactory' => '#00b050',
                'Not Applicable' => '#dddddd',
            ];

            $legacyText = [
                'Contact U/W within 24 hours' => 'Requires Improvement',
                'Multiple Requirements identified - monthly updates required' => 'Below Average',
                'Single Requirement - monitor progress' => 'Average',
                'Recommendations Only -generally reasonable controls' => 'Good',
                'Satisfactory' => 'Good',
                'Not Applicable' => 'Not Applicable / Not Assessed',
            ];

            $submission_obj = json_decode($submission->json);
            $submission_arr = (array) $submission_obj;
            $form_fields = json_decode($form['fields']);
            $form_sections = $form['section_names'];

            // echo '<pre>';

            $sections = explode(',', $form_sections);

            $new_fields = [];
            $links = [];

            foreach ($sections as $value) {
                foreach ($form_fields as $form_field) {
                    if (key($form_field) == 'select_risk_control') {
                        continue;
                    }

                    foreach ($form_field as $field_type => $form_values) {
                        $array = [];
                        $attrs = [];

                        foreach ($form_values as $form_value) {
                            //if($field_type != 'risk_recommendation') {
                            $attrs[$form_value->name] = $form_value->value;
                            //}

                            if ($form_value->name == 'section' && $form_value->value == $value) {
                                $arr = [
                                    'label' => isset($attrs['label']) ? $attrs['label'] : null,
                                    'name' => isset($attrs['name']) ? $attrs['name'] : null,
                                    'value' => (isset($attrs['label']) && isset($attrs['name'])) ? (isset($submission_arr[$attrs['name']]) ? $submission_arr[$attrs['name']] : null) : null,
                                    'field_type' => $field_type,
                                ];
                                if ($field_type == 'file') {
                                    $links[$attrs['name']] = $this->fileLink($attrs['name'], $submission->survey_id);
                                }
                                if ($field_type == 'risk_recommendation') {
                                    for ($i = 1; $i <= 15; ++$i) {
                                        if (isset($submission_arr[$attrs['name'] . '_' . $i . '_classification']) && $submission_arr[$attrs['name'] . '_' . $i . '_classification'] != '') {
                                            $links[$attrs['name'] . '_' . $i] = $this->fileLink($attrs['name'] . '_' . $i, $submission->survey_id);
                                        }
                                    }
                                }
                                $new_fields[$value][] = $arr;
                            }
                        }
                    }
                }
            }

            $view = view('ri_submissions/print_html', [
                'rr_colors'      => $rr_colors,
                'policy_types'   => $policyTypes,
                'legacyText'     => $legacyText,
                'show'           => $show,
                'params'         => $params,
                'submission_obj' => $submission_obj,
                'submission_arr' => $submission_arr,
                'form_fields'    => $form_fields,
                'form_sections'  => $form_sections,
                'sections'       => $sections,
                'new_fields'     => $new_fields,
                'links'          => $links,
                'pdf_is_srg_migrated' => data_get($submission_obj, 'pdf_is_srg_migrated', false)
            ])->render();

            $id = $submission_arr['_id'];
            $survey_id = $submission_arr['survey_id'];
            $pdf_type = 'UWR';
            if (strtolower($show) == 'csr') {
                $pdf_type = 'CSR';
            }

            $orientation = $request->get('orientation');
            $html = preg_replace('/[\t\n\r\0\x0B]/', '', $view);
            $html = preg_replace('/([\s])\1+/', ' ', $view);
            $html = trim($html);
            $fileName = $survey_id . '_pdf_' . uniqid() . '.html';
            $pdfFileName = 'pdf_' . uniqid() . '.pdf';
            Storage::put(storage_path() . '/file_to_download/' . $fileName, $html);
            if ($survey_id != '4874') {
                PDF::loadHtml(storage_path() . '/file_to_download/' . $fileName)
                    ->setPaper('a4', $orientation)
                    // ->setOrientation($orientation)
                    ->setWarnings(false)
                    ->setOption('background', true)
                    // ->setOption('margin-top','0mm')
                    // ->setOption('margin-left','0mm')
                    // ->setOption('margin-right','0mm')
                    ->setOption('footer-font-size', '10')
                    ->setOption('footer-font-name', 'Ariel')
                    ->setOption('footer-right', 'Page [page] of [topage]')
                    ->setOption('footer-left', 'CONFIDENTIAL')
                    ->setOption('footer-spacing', '3')
                    ->save(storage_path() . '/file_to_download/' . $pdfFileName);
            } else {
                $pdfFileName = 'pdf_5ce7abbf6f43a.pdf';
            }

            try {
                $number_of_pages = exec('pdftk ' . storage_path() . '/file_to_download/' . $pdfFileName . ' dump_data | grep NumberOfPages | awk \'{print $2}\'');

                $first_page = exec('pdftk ' . storage_path() . '/file_to_download/' . $pdfFileName . ' cat 1-1 output ' . storage_path() . '/file_to_download/first_' . $pdfFileName);

                $other_pages = exec('pdftk ' . storage_path() . '/file_to_download/' . $pdfFileName . ' cat 2-' . $number_of_pages . ' output ' . storage_path() . '/file_to_download/other_' . $pdfFileName);

                $cover_page = exec('pdftk ' . storage_path() . '/file_to_download/first_' . $pdfFileName . ' background ' . storage_path() . '/doc_header.pdf output ' . storage_path() . '/file_to_download/cover_' . $pdfFileName);

                $file = exec('pdftk ' . storage_path() . '/file_to_download/cover_' . $pdfFileName . ' ' . storage_path() . '/file_to_download/other_' . $pdfFileName . ' output ' . storage_path() . '/file_to_download/processed_' . $survey_id . '_' . $pdfFileName);

                $pdf_path = storage_path() . '/file_to_download/processed_' . $survey_id . '_' . $pdfFileName;

                $upload = $this->files->upload($pdf_path, 'survey_attachments/pdf/' . $pdf_type . '_' . $survey_id . '.pdf');

                return $this->files->link('survey_attachments/pdf/' . $pdf_type . '_' . $survey_id . '.pdf', '7 days');
            } catch (\Exception $e) {
                return 'Errors';
            }
        } else {
            return 'N/A';
        }
    }

    public function downloadPrintPdfBinder(Request $request, $submission, $show = 'UWR', $download = true)
    {
        $user = Session::get('user');
        if ($user->login_type == 'broker-user' && strtolower($show) === 'uwr') {
            abort(404);
        }

        $response = json_decode(Api::get('api/v1/survey-submission/' . $submission));
        $policyTypes = json_decode(Api::get('/api/v1/standard-risk/policy-types'));

        $submission = json_decode($response->data);
        $attached_files=$submission->attached_files;

        $submission->json = $response->data;

        $param_resource = json_decode(
            Api::get(
                static::get_api_uri($submission->survey_id)
            )
        );

        $params = [
            'resource' => $param_resource->data,
        ];

        $response = json_decode(Api::get('api/v1/risk-improvement/form/' . $submission->form_id));

        if ($response->response == 'success') {
            set_time_limit(600);
            $form = json_decode($response->data, true);
            $form['fields'] = json_encode($form['fields']);

            $rr_colors = [
                'Requires Improvement' => '#fc0d1b',
                'Below Average' => '#fdbf2d',
                'Average' => '#fffd38',
                'Good' => '#00b050',
                'Superior' => '#0070c0',
                'Not Applicable / Not Assessed' => '#dddddd',
                'Contact U/W within 24 hours' => '#fc0d1b',
                'Multiple Requirements identified - monthly updates required' => '#fdbf2d',
                'Single Requirement - monitor progress' => '#fffd38',
                'Recommendations Only -generally reasonable controls' => '#00b050',
                'Satisfactory' => '#00b050',
                'Not Applicable' => '#dddddd',
            ];

            $legacyText = [
                'Contact U/W within 24 hours' => 'Requires Improvement',
                'Multiple Requirements identified - monthly updates required' => 'Below Average',
                'Single Requirement - monitor progress' => 'Average',
                'Recommendations Only -generally reasonable controls' => 'Good',
                'Satisfactory' => 'Good',
                'Not Applicable' => 'Not Applicable / Not Assessed',
            ];

            $submission_obj = json_decode($submission->json);

            $isSrgMigrated = $submission_obj->is_srg_migrated;
            $withLegacyData = $submission_obj->with_legacy_data;
            $pdfIsSrgMigrated = $submission_obj->pdf_is_srg_migrated;

            $submission_arr = (array) $submission_obj;
            $form_fields = json_decode($form['fields']);
            $form_sections = $form['section_names'];

            $sections = explode(',', $form_sections);

            $new_fields = [];
            $links = [];
            $file_counter=0;

            foreach ($sections as $value) {
                foreach ($form_fields as $form_field) {
                    if (key($form_field) == 'select_risk_control') {
                        continue;
                    }

                    foreach ($form_field as $field_type => $form_values) {
                        $attrs = [];
                        foreach ($form_values as $form_value) {
                            if ($field_type != 'header') {
                                $attrs[$form_value->name] = $form_value->value;
                            }

                            if ($form_value->name == 'section' && $form_value->value == $value 
                                && $field_type != 'header') {
                                $arr = [
                                    'label' => isset($attrs['label']) ? $attrs['label'] : null,
                                    'name' => isset($attrs['name']) ? $attrs['name'] : null,
                                    'value' => (isset($attrs['label']) && isset($attrs['name'])) ? (isset($submission_arr[$attrs['name']]) ? $submission_arr[$attrs['name']] : null) : null,
                                    'field_type' => $field_type,
                                    'is_csr' => isset($attrs['CSR_Field']) ? $attrs['CSR_Field'] : "0",
                                ];
                                if ($field_type == 'file') {
                                    $links[$attrs['name']] = $this->fileLink($attrs['name'], $submission->survey_id);
                                }
                                if ($field_type == 'risk_recommendation') {
                                    for ($i = 1; $i <= 15; ++$i) {
                                        if (isset($submission_arr[$attrs['name'] . '_' . $i . '_classification']) && $submission_arr[$attrs['name'] . '_' . $i . '_classification'] != '') {
                                            $links[$attrs['name'] . '_' . $i] = $this->fileLink($attrs['name'] . '_' . $i, $submission->survey_id);
                                        }
                                    }
                                }
                                $new_fields[$value][] = $arr;
                            }
                        }
                    }
                }
            }

            $isDtr = data_get($params,'resource.survey_type') === 'dtr';
            $printTemplate = $isDtr ? 'ri_submissions/print_html_dtr' : 'ri_submissions/print_html';

            $view = view(
                $printTemplate,
                [
                    'rr_colors' => $rr_colors,
                    'rr_standard_colors' => SrgHelper::gradingOptions(),
                    'legacyText' => $legacyText,
                    'show' => $show,
                    'params' => $params,
                    'submission_obj' => $submission_obj,
                    'submission_arr' => $submission_arr,
                    'form_fields' => $form_fields,
                    'form_sections' => $form_sections,
                    'sections' => $sections,
                    'new_fields' => $new_fields,
                    'links' => $links,
                    'policy_types' => $policyTypes,
                    'is_srg_migrated' => $isSrgMigrated,
                    'with_legacy_data' => $withLegacyData,
                    'pdf_is_srg_migrated' => $pdfIsSrgMigrated
                ]
            )->render();
            if($request->has('display')) {
                return $view;
            }

            $id = $submission_arr['_id'];
            $survey_id = $submission_arr['survey_id'];
            $pdf_type = 'UWR';
            if (strtolower($show) == 'csr') {
                $pdf_type = 'CSR';
            }

            if (strtolower($show) == 'csr') {
                $pdf_type = 'CSR';
            }

            $orientation = $request->get('orientation');
            $html = preg_replace('/[\t\n\r\0\x0B]/', '', $view);
            $html = preg_replace('/([\s])\1+/', ' ', $view);
            $html = trim($html);
            $fileName = $survey_id . '_pdf_' . uniqid() . '.html';
            $pdfFileName = 'pdf_' . uniqid() . '.pdf';

            // Make sure directory exists
            Storage::disk('public')->makeDirectory('file_to_download');

            $fileToDownloadBasePath = storage_path('app/public/file_to_download/');
            if ($survey_id != '4874') {
                SnappyPdf::loadHTML($html)
                    ->setPaper('a4')
                    ->setOrientation($orientation)
                    ->setOption('background', true)
                    ->setOption('margin-top', '15mm')
                    ->setOption('margin-left', '20mm')
                    ->setOption('margin-right', '20mm')
                    ->setOption('margin-bottom', '30mm')
                    ->setOption('footer-font-size', '10')
                    ->setOption('footer-font-name', 'Ariel')
                    // ->setOption('defaultFont', 'sans-serif')
                    // ->setOption('footer-right', '[page]')
                    // ->setOption('footer-left', 'CONFIDENTIAL')
                    // ->setOption('footer-spacing', '3')
                    ->setOption('footer-html', storage_path() . '/footer.html')
                    ->save($fileToDownloadBasePath . $pdfFileName);
            } else {
                $pdfFileName = 'pdf_5ce7abbf6f43a.pdf';
            }

            try {
                $number_of_pages = exec('pdftk ' . $fileToDownloadBasePath . $pdfFileName . ' dump_data | grep NumberOfPages | awk \'{print $2}\'');

                $first_page = exec('pdftk ' . $fileToDownloadBasePath . $pdfFileName . ' cat 1-1 output ' . $fileToDownloadBasePath . 'first_' . $pdfFileName);

                $other_pages = exec('pdftk ' . $fileToDownloadBasePath . $pdfFileName . ' cat 2-' . $number_of_pages . ' output ' . $fileToDownloadBasePath . 'other_' . $pdfFileName);

                $cover_page = exec('pdftk ' . $fileToDownloadBasePath . 'first_' . $pdfFileName . ' background ' . storage_path() . '/pdf-cover.pdf output ' . $fileToDownloadBasePath . 'cover_' . $pdfFileName);

                $file = exec('pdftk ' . $fileToDownloadBasePath . 'cover_' . $pdfFileName . ' ' . $fileToDownloadBasePath . 'other_' . $pdfFileName . ' ' . storage_path() . '/pdf-video.pdf ' . storage_path() . '/pdf-end-csr.pdf' . ' output ' . $fileToDownloadBasePath . 'processed_' . $survey_id . '_' . $pdfFileName);

                $pdf_path =  $fileToDownloadBasePath . 'processed_' . $survey_id . '_' . $pdfFileName;

                $upload = $this->files->upload($pdf_path, 'survey_attachments/pdf/' . $pdf_type . '_' . $survey_id . '.pdf');

                $dentifier = isset($params['resource']->legacy_srf) ? $params['resource']->legacy_srf : $params['resource']->id;

                $filenam = $dentifier . '.pdf';

                if (strtolower($show) == 'csr' && $isDtr) {
                    $filenam = 'DTR_' . $dentifier . '.pdf';
                } elseif (strtolower($show) == 'csr') {
                    $filenam = 'CSR_' . $dentifier . '.pdf';
                } elseif (strtolower($show) == 'uwr') {
                    $filenam = 'UWR_' . $dentifier . '.pdf';
                }
                if ($download) {
                    return Response::download($pdf_path, $filenam);
                } else {
                    return true;
                }
                // return $this->files->link('survey_attachments/pdf/'.$pdf_type.'_'.$survey_id.'.pdf', '7 days');
            } catch (\Exception $e) {
                return 'Error while downloading the report';
            }
        } else {
            return 'N/A';
        }
    }

    public function fileLink($field_name, $survey_id)
    {
        $image_files = array('jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'tiff');
        $response = json_decode(Api::get('api/v1/surveys/get-attachment-info/' . $field_name . '/' . $survey_id));
        if ($response->response == 'success') {
            $files['files'] = [];
            foreach ($response->data as $attachment) {
                $fileName_cloud = 'survey_uploads/' . $attachment->field_name . '/' . $attachment->cloud_file_name;
                if ($this->files->exists($fileName_cloud)) {
                    $file = $this->survey->downloadLink($fileName_cloud, $attachment->file_name, '+120 minutes');
                } else {
                    $file = '';
                }

                $file_info = pathinfo($attachment->file_name);
                $extension = $file_info['extension'];

                $ch = curl_init($file);

                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_HEADER, true);
                curl_setopt($ch, CURLOPT_NOBODY, true);

                $data = curl_exec($ch);
                $size = curl_getinfo($ch, CURLINFO_CONTENT_LENGTH_DOWNLOAD);

                curl_close($ch);

                $arr = [
                    'name' => $attachment->file_name,
                    'cloud' => $fileName_cloud,
                    'notes' => $attachment->notes,
                    'url' => in_array($extension, $image_files) ? $file : '',
                    'extension' => $extension,
                    'thumbnailUrl' => in_array($extension, $image_files) ? $file : '',
                    'deleteUrl' => config('app.url') . '/surveys/' . $survey_id . '/delete/' . $attachment->id,
                    'size' => $size,
                ];
                array_push($files['files'], $arr);
            }

            return $files;
        }
    }

    // http://stackoverflow.com/questions/3302857/algorithm-to-get-the-excel-like-column-name-of-a-number
    public function num2alpha($n)
    {
        for ($r = ''; $n >= 0; $n = intval($n / 26) - 1) {
            $r = chr($n % 26 + 0x41) . $r;
        }

        return $r;
    }

    public function exportAll(Request $request)
    {
        $user = Session::get('user');

        if ($user->login_type != 'risk-control' && !Session::get('user')?->isRoleAdminOrAccountManager()) {
            return Response::make('Unauthorized', 401);
        }

        $get = [];

        $get[] = 'export=true';
        $get[] = 'first_name=' . $user->first_name;
        $get[] = 'last_name=' . $user->last_name;
        $get[] = 'email=' . $user->email;

        if ($request->has('mga_scheme')) {
            $get[] = 'mga_scheme=' . $request->get('mga_scheme');
        }

        if ($request->has('broker_org')) {
            $get[] = 'broker_org=' . $request->get('broker_org');
        }

        if ($request->has('month')) {
            $get[] = 'month=' . $request->get('month');
        }

        if ($request->has('year')) {
            $get[] = 'year=' . $request->get('year');
        }

        if ($request->has('search')) {
            $get[] = 'search=' . $request->get('search');
        }

        $resources = json_decode(
            Api::get(
                static::get_api_uri(
                    sprintf(
                        'all/1/10000%s',
                        '?' . implode('&', $get)
                    )
                )
            )
        );
        return response('OK');
    }

    public function exportOverviewReport(Request $request)
    {
        $user = Session::get('user');

        if ($user->login_type != 'risk-control' && !$user?->isRoleAdminOrAccountManager()) {
            return Response::make('Unauthorized', 401);
        }

        $get = [];

        $get[] = 'overview_report=generateOverviewExcelReport';
        $get[] = 'first_name=' . $user->first_name;
        $get[] = 'last_name=' . $user->last_name;
        $get[] = 'email=' . $user->email;

        $request->has('search') ? $get[] = 'search=' . $request->get('search') : '';

        $url = static::get_api_uri(
            sprintf(
                'all/1/10000%s',
                '?' . implode('&', $get)
            )
        );

        Api::get($url);

        return Response::json('OK');
    }

    public function createLegacy()
    {
        $resources = json_decode(Api::get(static::get_api_uri('all-srfs')));

        $srfs = isset($resources->data) ? $resources->data : [];
        $out_srfs = [];

        foreach ($srfs as $key => $srf) {
            $out_srfs[$srf->id] = $srf->id;
        }

        $colors = [
            'Requires Improvement' => '#fc0d1b',
            'Below Average' => '#fdbf2d',
            'Average' => '#fffd38',
            'Good' => '#00b050',
            'Superior' => '#0070c0',
            'Not Applicable / Not Assessed' => '#dddddd',
            'Contact U/W within 24 hours' => '#fc0d1b',
            'Multiple Requirements identified - monthly updates required' => '#fdbf2d',
            'Single Requirement - monitor progress' => '#fffd38',
            'Recommendations Only -generally reasonable controls' => '#00b050',
            'Satisfactory' => '#00b050',
            'Not Applicable' => '#dddddd',
        ];

        return view(
            static::TEMPLATE_PATH . '/create-legacy',
            [
                'srfs' => $out_srfs,
                'colors' => $colors,
            ]
        );
    }

    public function editLegacy(Request $request, $id)
    {
        $user = Session::get('user');
        $search = $request->get('search', '');
        $page = $request->get('page', 1);
        $limit = 10000;
        $get = [];

        $request->has('mga_scheme') ? $get[] = 'mga_scheme=' . $request->get('mga_scheme') : '';
        $request->has('broker_org') ? $get[] = 'broker_org=' . $request->get('broker_org') : '';
        $request->has('search') ? $get[] = 'search=' . $request->get('search') : '';
        $request->has('date_of_next_survey') ? $get[] = 'date_of_next_survey=filled' : '';

        $resources = json_decode(
            Api::get(
                static::get_api_uri(
                    sprintf(
                        'all/%d/%d%s',
                        $page,
                        $limit,
                        '?' . implode('&', $get)
                    )
                )
            )
        );

        $srfs = isset($resources->data) ? $resources->data : [];
        $out_srfs = [];

        foreach ($srfs as $key => $srf) {
            $out_srfs[$srf->id] = $srf->id;
        }

        $resource = json_decode(Api::get('/api/v1/surveys/' . $id . '/legacy'))->data;

        if (isset($resource->status) && $resource->status == 'submitted') {
            return Redirect::to('/surveys/legacy-report/' . $id)->with('error', 'This survey has already been submitted.');
        }

        $resource_files = [];

        foreach ($resource->attached_files as $file) {
            $fileName_cloud = 'survey_uploads/' . $file->field_name . '/' . $file->cloud_file_name;
            $file->url = $this->survey->downloadLink($fileName_cloud, $file->file_name);

            $resource_files[$file->field_name][] = $file;
        }

        $colors = [
            'Requires Improvement' => '#fc0d1b',
            'Below Average' => '#fdbf2d',
            'Average' => '#fffd38',
            'Good' => '#00b050',
            'Superior' => '#0070c0',
            'Not Applicable / Not Assessed' => '#dddddd',
            'Contact U/W within 24 hours' => '#fc0d1b',
            'Multiple Requirements identified - monthly updates required' => '#fdbf2d',
            'Single Requirement - monitor progress' => '#fffd38',
            'Recommendations Only -generally reasonable controls' => '#00b050',
            'Satisfactory' => '#00b050',
            'Not Applicable' => '#dddddd',
        ];

        return view(
            static::TEMPLATE_PATH . '/edit-legacy',
            [
                'resource' => $resource,
                'resource_files' => $resource_files,
                'srfs' => $out_srfs,
                'colors' => $colors,
            ]
        );
    }

    public function storeLegacy(Request $request)
    {
        $data = $request->except(['_token']);

        $validator = Validator::make(
            $data,
            [
                'srf' => 'required|integer',
                'legacy_srf' => 'required|integer',
            ]
        );

        if ($validator->fails()) {
            return Redirect::back()->withInput($request->old())->withErrors($validator->errors());
        }

        // print_r(Api::post(static::get_api_uri() . '/store-legacy', $data));exit;
        $response = json_decode(Api::post(static::get_api_uri() . '/store-legacy', $data));

        if ($response->response == 'success') {
            return Redirect::to('/surveys')->with('success', 'Form saved');
        }

        return Redirect::back()->with('error', 'Error submitting this form data')->withInput($request->old());
    }

    public function updateLegacy(Request $request, $id)
    {
        $data = $request->except(['_token']);

        $validator = Validator::make(
            $data,
            [
                'srf' => 'required|integer',
                'legacy_srf' => 'required|integer',
            ]
        );

        if ($validator->fails()) {
            return Redirect::back()->withInput($request->old())->withErrors($validator->errors());
        }

        // print_r(Api::post(static::get_api_uri() . '/update-legacy/' . $id, $data));exit;
        $response = json_decode(Api::post(static::get_api_uri() . '/update-legacy/' . $id, $data));

        if ($response->response == 'success') {
            return Redirect::to('/surveys')->with('success', 'Form saved');
        }

        return Redirect::back()->with('error', 'Error submitting this form data')->withInput($request->old());
    }

    public function overviewreport(Request $request)
    {
        $user = Session::get('user');
        $order = $request->get('order', 'desc');
        $search = $request->get('search', '');
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 10);
        $get = [];

        $request->has('search') ? $get[] = 'search=' . urlencode($request->get('search')) : '';
        $request->has('order') ? $get[] = 'order=' . $request->get('order') : $get[] = 'order=desc';

        $resources = json_decode(
            Api::get(
                static::get_api_uri(
                    sprintf(
                        'all/%d/%d%s',
                        $page,
                        $limit,
                        '?' . implode('&', $get)
                    )
                )
            )
        );

        $overviewreportData = $resources->data;

        foreach ($overviewreportData as $reportdata) {
            if (isset($reportdata->external_survey_company)) {
                $surveyorg = $reportdata->external_survey_company->name;
            } elseif (isset($reportdata->surveyor->first_name) || isset($reportdata->surveyor->last_name)) {
                $surveyorg = (isset($reportdata->surveyor->first_name) ? $reportdata->surveyor->first_name : '') . ' ' . (isset($reportdata->surveyor->last_name) ? $reportdata->surveyor->last_name : '');
            } else {
                $surveyorg = 'unassigned';
            }

            $reportdata->surveyorg = $surveyorg;

            $survey = json_decode(
                Api::get(
                    static::get_api_uri($reportdata->id)
                )
            );
            $surveyData = $survey->data;

            $csr_data = (isset($surveyData->schedule->actual_submission_deadline)) ? (isset($surveyData->submissions->csr_submission_date) && isset($surveyData->submissions->csr_status) && $surveyData->submissions->csr_status == 'submitted' ? 'submitted' : $surveyData->schedule->client_survey_report_deadline) : (isset($surveyData->submissions->csr_submission_date) ? 'submitted' : '-');

            $uwr_data = (isset($surveyData->schedule->actual_submission_deadline)) ? (isset($surveyData->submissions->uwr_submission_date) && isset($surveyData->submissions->uwr_status) && $surveyData->submissions->uwr_status == 'submitted' ? 'submitted' : $surveyData->schedule->underwriter_deadline) : (isset($surveyData->submissions->uwr_submission_date) ? 'submitted' : '-');

            if ($csr_data != '-' && $csr_data != 'submitted') {
                $csr_data = strtotime($csr_data) > time() ? 'no' : 'yes';
            } elseif ($csr_data == 'submitted') {
                $csr_data = 'no';
            } else {
                $csr_data = '-';
            }

            if ($uwr_data != '-' && $uwr_data != 'submitted') {
                $uwr_data = strtotime($uwr_data) > time() ? 'no' : 'yes';
            } elseif ($uwr_data == 'submitted') {
                $uwr_data = 'no';
            } else {
                $uwr_data = '-';
            }

            $reportdata->elapsedDays = '-';
            if (isset($reportdata->schedule_meta->value)) {
                try {
                    $date = Carbon::parse($reportdata->schedule_meta->value);
                    $created = Carbon::parse($reportdata->created_at);
                    $reportdata->elapsedDays = $date->diffInDays($created) + 1;
                    if (((int) $reportdata->elapsedDays) > 30) {
                        $reportdata->greaterThan30 = true;
                    }
                } catch (\Exception $e) {
                    //'invalid date';
                    $reportdata->elapsedDays = '-';
                }
            }
            $reportdata->csr_date = $csr_data;
            $reportdata->uwr_date = $uwr_data;
        }

        if ($resources->response == 'success') {
            return view(
                static::TEMPLATE_PATH . '/overview-report',
                array_merge(
                    [
                        'resources' => $overviewreportData,
                        'total' => $resources->total,
                        'limit' => $limit,
                        'page' => $page,
                        'user_details' => Session::get('user'),
                        'link' => static::ROUTE_PREFIX . '.overview-report',
                    ]
                )
            );
        } else {
            throw new Exception($resources->message);
        }
    }

    public function updateSurveyStatus(Request $request)
    {
        $data = $request->except('_token');
        $response = json_decode(Api::post('api/v1/surveys/update-survey-status', $data));
        
        $this->recacheRelatedData($data['organisation_id'] ?? '');

        return $response->response;
    }

    public function resurveyCheck(Request $request)
    {
        $data = $request->except('_token');
        $response = json_decode(Api::post('api/v1/surveys/resurvey-check', $data));

        if (
            isset($response->status)
            && !empty($response->status)
            && $response->status = 'success'
        ) {
            return response()->json($response);
        }
    }

    public static function onStoreSuccess($data)
    {
        self::recacheRelatedData($data['organisation_id'] ?? '');
    }
    
    public static function onUpdateSuccess($data)
    {
        self::recacheRelatedData($data['organisation_id'] ?? '');
    }

    public static function recacheRelatedData($orgId)
    {
        try {
            $surveyId = request()->route('id', request()->get('survey_id'));

            if (!empty($surveyId)) {
                SendSqsMessageService::sendMessages([
                    [
                        'serviceClass' => GetSurveyService::class,
                        'params' => $surveyId ?? '',
                    ],
                ]);
            }

            if (empty($orgId)) {
                return;
            }
            SendSqsMessageService::sendMessages([
                [
                    'serviceClass' => GetOrgRiskGradingData::class,
                    'params' => $orgId ?? '',
                ],
                [
                    'serviceClass' => GetOrganisationService::class,
                    'params' => $orgId ?? '',
                ],
                [
                    'serviceClass' => 'App\Services\CacheContent\GetOrganisationDetailsForDashboard',
                    'params' => $orgId ?? '',
                    'isClient' => true,
                ],
            ]);
        } catch (\Exception $e) {
            \Log::error("[SurveyController@recacheRelatedData] " . $e->getMessage());
        }        
    }
}
