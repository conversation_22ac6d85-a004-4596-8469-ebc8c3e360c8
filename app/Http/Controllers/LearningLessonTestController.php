<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;
use App\Models\LessonObjects;
use App\Models\Api;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Redirect;
use App\Http\Controllers\BaseController;
use Barryvdh\DomPDF\Facade\Pdf;

class LearningLessonTestController extends BaseController
{
    const RESPOND_TO_AJAX = true;

    public function __construct(Request $request, LessonObjects $lessonObjects)
    {
        parent::__construct($request);

        $this->lessonObjects = $lessonObjects;
    }


    public function show($course_id, $lesson_id)
    {
        $api_call = json_decode(Api::get('/api/v1/learning/lesson/' . $lesson_id));
        if(isset($api_call->response) && $api_call->response == 'success') {
            $toshow= '';

            if(isset($api_call->data->test->sections) && $api_call->data->test->amount_questions > 0) {
                $sections = array();
                //$sections = $api_call->data->test->sections;
                foreach($api_call->data->test->sections as $sec){
                    $sections[] = $sec;
                }
                $api_call->data->test->sections = $sections;

                $questions = Array();
                $others = Array();

                foreach($sections as $key => $section){
                    if($section->type == 'question-test') {
                        $questions[$key] = $section;
                    }else{
                        $others[$key] = $key;
                    }
                }

                $arr = (array)$questions;

                $amount = $api_call->data->test->amount_questions;
                if($amount > count($arr)) {
                    $amount = count($arr);
                }
                if($amount == 0) {
                    $amount = 1;
                }
                $toshow = array_rand($arr, $amount);

                if($amount == 1) {
                    foreach($arr as $key => $section){
                        if($section->type == 'question-test') {
                            $toshow = array(0=>$key);
                        }
                    }
                }
                if($api_call->data->test->amount_questions == count($questions)) {
                    $toshow = [];
                    foreach($sections as $key => $section){
                        $toshow[] = $key;
                    }
                }else if($api_call->data->test->amount_questions < count($questions)) {
                    $toshow = array_merge($others, $toshow);
                }

            }
            return view(
                'learning.lesson.test.show-test', array(
                'resource'   => $api_call->data,
                'layout'     => 'layout',
                'main_class' => 'learning',
                'toshow'     => $toshow,
                )
            );
        }
    }

    public function create($course_id, $lesson_id)
    {

        $api_call = json_decode(Api::get('/api/v1/learning/lesson/' . $lesson_id));

        if(isset($api_call->response) && $api_call->response == 'success') {
            $cnt = 0;
            if(isset($api_call->data->test->sections)) {
                foreach($api_call->data->test->sections as $section){
                    if($section->type == 'question-test') {
                        $cnt++;
                    }
                }
            }
            $api_call->data->test->questions = $cnt;
            return view(
                'learning.lesson.test.create-test', array(
                'lesson'  =>  $api_call->data,
                'layout'    =>  'layout',
                'main_class' => 'learning'
                )
            );
        }

        return null;

    }
    public function update(Request $request, $course_id, $lesson_id)
    {
        $data = $request->all();
        $api_call = json_decode(Api::put('/api/v1/learning/lesson/' . $lesson_id . '/test/update', $data));

        if(isset($api_call->response) && $api_call->response == 'success') {

            return Redirect::route(
                'learning.course.lesson.test.create', array(
                'course_id' => $course_id,
                'lesson_id' => $lesson_id
                )
            )->with('success', 'The Test has been updated successfully');
        }

        return null;

    }
    public function edit($course_id, $lesson_id)
    {

        $api_call = json_decode(Api::get('/api/v1/learning/lesson/' . $lesson_id));

        if(isset($api_call->response) && $api_call->response == 'success') {

            return view(
                'learning.lesson.test.edit-test', array(
                'lesson'  =>  $api_call->data
                )
            );
        }

        return null;

    }
    public function delete($course_id, $lesson_id)
    {

        $api_call = json_decode(Api::delete('/api/v1/learning/lesson/' . $lesson_id . '/test'));

        if(isset($api_call->response) && $api_call->response == 'success') {

            return Response::json(array('response' => 'success'));

        }

        return Response::json(array('response' => 'error'));
    }

    public function showSection($course_id, $lesson_id, $section_type)
    {

        $api_call = json_decode(
            Api::post(
                '/api/v1/learning/lesson/' . $lesson_id . '/test/section', array(
                'type' => $section_type
                )
            )
        );
        if(isset($api_call->response) && $api_call->response == 'success') {

            return view(
                'learning.lesson.test.' . $section_type, array(
                'section'       =>  $api_call->data,
                'course_id'     =>  $course_id,
                'lesson_id'     =>  $lesson_id
                )
            );
        }
        return Response::json(
            array(
            'response' => 'error'
            )
        );

    }

    public function addOption($course_id, $lesson_id, $section_id, $question_type)
    {

        $api_call = json_decode(
            Api::post(
                '/api/v1/learning/lesson/' . $lesson_id . '/test/section/' . $section_id . '/option/' . $question_type, array(
                'value' => 'Option',
                'correct' => 'false'
                )
            )
        );

        if(isset($api_call->response) && $api_call->response == 'success') {

            return view(
                'learning.lesson.test.new-question', array(
                'question_id'   =>  $api_call->data->id,
                'question_type' => $question_type
                )
            );

        }


        return Response::json(
            array(
            'response' => 'error'
            )
        );

    }
    public function removeOption($course_id, $lesson_id, $section_id, $option_id)
    {

        $api_call = json_decode(Api::delete('/api/v1/learning/lesson/' . $lesson_id . '/test/section/' . $section_id . '/option/' . $option_id));

        if(isset($api_call->response) && $api_call->response == 'success') {

            return Response::json(array('response' => 'success', 'message' => '/test/section/' . $section_id . '/option/' . $option_id));

        }

        return Response::json(
            array(
            'response' => 'error'
            )
        );

    }
    public function changeQuestionType($course_id, $lesson_id, $section_id, $question_type)
    {

        $api_call = json_decode(Api::post('/api/v1/learning/lesson/' . $lesson_id . '/test/section/' . $section_id . '/changequestiontype/' . $question_type));

        if(isset($api_call->response) && $api_call->response == 'success') {

            return Response::json(array('response' => 'success'));

        }

        return Response::json(
            array(
            'response' => 'error'
            )
        );

    }
    public function updateSection(Request $request, $course_id, $lesson_id, $section_id)
    {

        $data = $request->except('_token');

        $api_call = json_decode(Api::put('/api/v1/learning/lesson/' . $lesson_id . '/test/section/' . $section_id, $data));
        //$api_call = json_decode(Api::put('/api/v1/learning/lesson/' . $lesson_id . '/test/section/' . $section_id));

        if(isset($api_call->response) && $api_call->response == 'success') {

            return Response::json(array('response' => 'success'));

        }

        return Response::json(array('response' => 'error'));

    }
    public function deleteSection($course_id, $lesson_id, $section_id)
    {

        $api_call = json_decode(Api::delete('/api/v1/learning/lesson/' . $lesson_id . '/test/section/' . $section_id));

        if(isset($api_call->response) && $api_call->response == 'success') {
            return Response::json(
                array(
                'response' => 'success'
                )
            );
        }

        return Response::json(
            array(
            'response' => 'error'
            )
        );
    }

    public function duplicateSection($course_id, $lesson_id, $section_id)
    {

        $api_call = json_decode(Api::post('/api/v1/learning/lesson/' . $lesson_id . '/test/section/' . $section_id . '/duplicate'));

        if(isset($api_call->response) && $api_call->response == 'success') {

            //     $api_call->data = $this->processSection($api_call->data, $course_id, $lesson_id);

            //return Response::json(array('response' => 'success', 'data' => $api_call->data));

            return view(
                'learning.lesson.test.' . $api_call->data->type, array(
                'section'       =>  $api_call->data,
                'course_id'     =>  $course_id,
                'lesson_id'     =>  $lesson_id
                )
            );
        }

        return Response::json(array('response' => 'error'));

    }


    /**
     * Sort Page Sections
     *
     * @param  $course_id
     * @param  $lesson_id
     * @return $api_call->data
     */

    public function store( $course_id, $lesson_id)
    {

        $api_call = json_decode(Api::post('/api/v1/learning/lesson/' . $lesson_id . '/test', array(  )));
        if(isset($api_call->response) && $api_call->response == 'success') {
            return Response::json(array('response' => 'success', 'data' => $api_call->data));
        }

        return Response::json(array('response' => 'error'));

    }
    //set test options
    public function setOptions(Request $request, $course_id, $lesson_id)
    {

        $data = $request->except('_token');

        $api_call = json_decode(Api::post('/api/v1/learning/lesson/' . $lesson_id . '/test/option', $data));

        if(isset($api_call->response) && $api_call->response == 'success') {
            return Response::json(array('response' => 'success'));
        }

        return Response::json(array('response' => 'error'));
    }

    /**
     * Get test result
     *
     * @param int $course_id Course ID
     * @param int $lesson_id Lesson ID
     *
     * @return mixed Response|Redirect
     */
    public function testResult(Request $request, $course_id, $lesson_id)
    {
        $user_id = $request->get('user_id');
        $user = json_decode(Api::get('api/v1/user/find/' . $user_id));
        $organisation = json_decode(Api::get('api/v1/organisation/' . $user->data->organisation_id));
        $api_call = json_decode(Api::post('/api/v1/learning/lesson/' . $lesson_id . '/test/result', array('user_id' => $user_id)));

        if (isset($api_call->response) && $api_call->response == 'success') {

            return view(
                'learning.lesson.test.test-result', array(
                'resource'   => $api_call->data,
                'layout'     =>  'layout',
                'main_class' => 'learning',
                'user'       => $user->data,
                'org_name'   => $organisation->data->name
                )
            );
        }

        return Redirect::route('learning.progress.index')->with($api_call->response, $api_call->message);
    }

    public function sortSection(Request $request, $course_id, $lesson_id)
    {
        $data = $request->all();
        $api_call = Api::put('/api/v1/learning/lesson/' . $lesson_id . '/test/section/sort', $data);
        $api_call = json_decode($api_call);
        if(isset($api_call->response) && $api_call->response == 'success') {
            return Response::json(array('response' => 'success'));
        }
        return Response::json(array('response' => 'error'));
    }

    /**
     * Generate a test result pdf
     *
     * @param int $course_id Course ID
     * @param int $lesson_id Lesson ID
     *
     * @return PDF
     */
    public function testResultPdf(Request $request, $course_id, $lesson_id)
    {
        $user_id = $request->get('user_id');
        $user = json_decode(Api::get('api/v1/user/find/' . $user_id));
        $organisation = json_decode(Api::get('api/v1/organisation/' . $user->data->organisation_id));
        $api_call = json_decode(Api::post('/api/v1/learning/lesson/' . $lesson_id . '/test/result', array('user_id' => $user_id)));

        if (isset($api_call->response) && $api_call->response == 'success') {
            $pdf = PDF::loadView(
                'learning.lesson.test.test-result-pdf', array(
                'resource'   => $api_call->data,
                'layout'     => 'pdf',
                'main_class' => 'learning',
                'user'       => $user->data,
                'org_name'   => $organisation->data->name
                )
            )->setOption('dpi', 300);

            return $pdf->stream('test-result.pdf');
        }

        return Redirect::back()->with('error', 'Was not possible generate PDF.');
    }


    public function submitTestPreview(Request $request, $course_id, $lesson_id)
    {
        $data = $request->except('_token');
        $questions = $data['data'];
        $api_call = json_decode(Api::post('/api/v1/learning/lesson/' . $lesson_id . '/test/check'));
        if(isset($api_call->response) && $api_call->response == 'success') {
            $sections = $api_call->data->sections;
            $showCorrects = isset($api_call->data->correct_answer)?$api_call->data->correct_answer:0;
            $pass = 0;
            foreach ($sections as $key => $section) {
                foreach ($questions as $key2 => $question) {
                    $question = (object)$question;

                    if($section->id == $question->id) {
                        $cntslc = 0;
                        $cntRigth = 0;
                        $cntCrcts = 0;
                        $cntSlcWr = 0;
                        foreach($question->options as  $key3 => $option){
                            foreach($section->options as $secOpt){
                                if($secOpt->id == $option['id']) {
                                    $option['status'] = $secOpt->correct;
                                }
                            }
                            if($option['status'] == 'true') {
                                $cntRigth++;
                            }
                            if($option['selected'] == 'true') {
                                $cntslc++;
                            }
                            if($option['status'] == 'true' && $option['selected'] == 'true') {
                                $cntCrcts++;
                            }else if($option['status'] == 'false' && $option['selected'] == 'true') {
                                $cntSlcWr++;
                            }
                            $question->options[$key3] = $option;
                        }


                        $question->status = 'fail';
                        if($cntCrcts == $cntRigth && $cntslc > 0 && $cntSlcWr == 0) {
                            $question->status = 'pass';
                            $pass++;
                        }
                    }

                    $questions[$key2] = $question;
                }
            }
            $user_id =  \Illuminate\Support\Facades\Session::get('user')->id;
            $test['pass_mark']        = $api_call->data->pass_mark>0?$api_call->data->pass_mark:1;
            $test['amount_questions'] = $api_call->data->amount_questions>0?$api_call->data->amount_questions:1;
            $test['pass']             = $pass;
            $test['status']           = 'Fail';
            $test['questions']        = $questions;
            $test['show']             = $showCorrects;
            $test['score']            = round(($test['pass']*100)/$test['amount_questions'], 2);
            if($test['pass'] >= $test['pass_mark']) {
                $test['status'] = 'Complete';
            }

            return Response::json(
                array(
                'response' => 'success',
                'data'     =>  $test
                )
            );
        }

        //return null;

    }
}
