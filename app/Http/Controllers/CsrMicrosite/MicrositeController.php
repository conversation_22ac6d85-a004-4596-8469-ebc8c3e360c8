<?php

namespace App\Http\Controllers\CsrMicrosite;


use App\Http\Controllers\BaseResourceController;
use App\Services\CacheContent\GetSurveyMicroSiteService;
use Illuminate\Support\Facades\View;
use Illuminate\Http\Request;
use App\Models\Api;
use App\Models\Survey;
use App\Helpers\Helpers;
use App\Helpers\KanbanHelper;
use App\Helpers\RiskGradingHelper;
use App\Models\FileUpload;
use App\Services\CacheContent\GetSurveyService;
use App\Services\SendSqsMessageService;
use App\Traits\HelperTrait;
use App\Traits\SubmissionGradingTrait;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpKernel\Exception\HttpException;

class MicrositeController extends BaseResourceController
{
    use HelperTrait, SubmissionGradingTrait;
    
    private $keyHelper;
    public $micrositeKey="0mDU4h5PB:@}5!(I>yaBsHZNI)az6n~0si";

    private $fileUpload;
    private $surveyModel;

    public function __construct(FileUpload $fileUpload)
    {
        $this->keyHelper=new Helpers();
        $this->fileUpload = $fileUpload;
        $this->surveyModel = new Survey();
    }

    public function home($survey_id)
    {
        $data = $this->getSurveyHomeDetailsForMicrosite($survey_id);

        $restrictedToBroker = Session::get("user") && Session::get("user")->login_type==='broker-user';
        
        if($restrictedToBroker && isset($data['surveyDetailsData']['survey_commentaries'])){
            foreach ($data['surveyDetailsData']['survey_commentaries']->survey_commentary as $item) {
                if (isset($item->visible_to_all) && $item->visible_to_all != 1) {
                    unset($item->additional_notes);
                }
            }
        }
        
        return view("csr-microsite/home", $data);
    }

    public function loadSurveyContent($survey_id)
    {
        $cacheKey = "csr-microsite-survey-{$survey_id}";
        $data = $this->getSetCache($cacheKey);
        if (!$data) {
            $data = $this->getSurveyDetailsForMicrosite($survey_id);
            $this->getSetCache($cacheKey, $data, 1800);
        }
        if (!empty($data['messages'])) {
            $this->checkMessageAttachments($data['messages']);
        }

        $data['isBroker'] = Session::get("user") && Session::get("user")->login_type==='broker-user';
        return view("csr-microsite/partials/content-area", $data)->render();
    }

    private function getMicrositeDataForSurvey($survey_id){
        $cacheKey="csr-microsite-survey-{$survey_id}";
        $data = $this->getSetCache($cacheKey);
        if(!$data){
            $data=$this->getSurveyDetailsForMicrosite($survey_id);
            $this->getSetCache($cacheKey, $data);
        }
        $isRestrictedToBroker=$isRestrictedToBroker = Session::get("user") && Session::get("user")->login_type === 'broker-user' ? 1 : 0;
        $survey_id=$survey_id.'_'.$isRestrictedToBroker;

        $data['encrypted_surveyId_url']=$this->keyHelper->encryptGUID($survey_id,$this->micrositeKey);
        return $data;
    }

    public function preview($survey_id)
    {   
        $data = $this->getMicrositeDataForSurvey($survey_id);

        //Data will be loaded for print from preview cache
        $cacheKey="csr-microsite-survey-preview-{$survey_id}";
        $data = $this->getSetCache($cacheKey,$data);

        $restrictedToBroker = Session::get("user") && Session::get("user")->login_type==='broker-user';
        $data['isBroker'] = $restrictedToBroker;
        
        if($restrictedToBroker && isset($data['surveyDetailsData']['survey_commentaries'])){
            foreach ($data['surveyDetailsData']['survey_commentaries']->survey_commentary as $item) {
                if (isset($item->visible_to_all) && $item->visible_to_all != 1) {
                    unset($item->additional_notes);
                }
            }
        }

        return View::make("csr-microsite/preview")->with($data);
    }


    //This is the rendered view passed to print
    public function previewVersion($surveyIdGUID)
    {
        $isEncryptedId = !preg_match('/^[0-9]+$/', $surveyIdGUID); // Check if the pass GUID is a real GUID or only a survey id
        $decryptedValue = $isEncryptedId ? $this->keyHelper->decryptGUID($surveyIdGUID, $this->micrositeKey) : 0;

        $survey_id=0;
        $is_broker=false;

        if (strpos($decryptedValue, '_') !== false) {
            list($survey_id, $is_broker) = explode('_', $decryptedValue);
        }

        if($survey_id > 0){
            $cacheKey="csr-microsite-survey-preview-{$survey_id}";
            $data = $this->getSetCache($cacheKey);
            if(!$data){
                $data = $this->getMicrositeDataForSurvey($survey_id);
            }

            if($is_broker && isset($data['surveyDetailsData']['survey_commentaries'])){
                foreach ($data['surveyDetailsData']['survey_commentaries']->survey_commentary as $item) {
                    if (isset($item->visible_to_all) && $item->visible_to_all != 1) {
                        unset($item->additional_notes);
                    }
                }
            }

            $data['print_only']=true;
            $data['isBroker'] = $is_broker;

            return View::make("csr-microsite/pdf/index")->with($data);
        }
        return response()->json([
            'status' => 401,
            'message' => 'unauthorised'
        ], 401);
    }


    private function getPrintFileName($surveyIdGUID){
        $filename="";
        try{
            $survey_id=0;
            $is_broker=false;
            $decryptedValue = $this->keyHelper->decryptGUID($surveyIdGUID, $this->micrositeKey);
            if (strpos($decryptedValue, '_') !== false) {
                list($survey_id, $is_broker) = explode('_', $decryptedValue);
            }
            $data = $this->getSurveyHomeDetailsForMicrosite($survey_id);
            $resource=$data['resource'];
            $filename="{$resource->organisation->name}-{$resource->location->location_name}-SRF{$resource->id}";
            $filename=str_replace(' ', '_', $filename);
            //$timestamp = date('Ymd_His');
            //$filename = "{$filename}_{$timestamp}";
        }
        catch(Exception $e){
            \Log::info("Getting filename error for srf {$surveyIdGUID}");
        }
        finally{
            return $filename;
        }
    }

    private function passDataToLambda($surveyIdGUID){
        $lambdaUrl =  config('app.aws.lambda_pdf_url');
        $fileName=$this->getPrintFileName($surveyIdGUID);

        $data = array(
            'url' => config('app.url').'/microsite/print-preview/'.$surveyIdGUID,
            'file_name' => $fileName
        );

        $jsonData = json_encode($data);

        $options = array(
            'http' => array(
                'method'  => 'POST',
                'header'  => 'Content-type: application/json',
                'content' => $jsonData
            )
        );

        $context = stream_context_create($options);
       
        $response = file_get_contents($lambdaUrl, false, $context);

        if ($response === FALSE) {
            throw new HttpException(500);
        } else {
            $responseData = json_decode($response, true);
            $redirectUrl = $responseData['url'];
            header("Location: $redirectUrl");
            exit();
        }
    }

    public function printVersion($surveyIdGUID)
    {
        $isEncryptedId = !preg_match('/^[0-9]+$/', $surveyIdGUID); // Check if the pass GUID is a real GUID or only a survey id
        $survey_id = $isEncryptedId ? $this->keyHelper->decryptGUID($surveyIdGUID, $this->micrositeKey) : 0;
        if ($survey_id > 0){
            $this->passDataToLambda($surveyIdGUID);
            exit;
        }
        return response()->json([
            'status' => 401,
            'message' => 'unauthorised'
        ], 401);
    }

    public function printPrivateVersion($survey_id)
    {
        $mainSurveyId=$survey_id;
        $isRestrictedToBroker=Session::get("user") && Session::get("user")->login_type==='broker-user' ? 1 : 0;
        $survey_id=$survey_id.'_'.$isRestrictedToBroker;
        $surveyIdGUID = $this->keyHelper->encryptGUID($survey_id,$this->micrositeKey);
        $isEncryptedId = !preg_match('/^[0-9]+$/', $surveyIdGUID);
        if ($isEncryptedId && $mainSurveyId > 0){
            $this->passDataToLambda($surveyIdGUID);
            exit;
        }
        return response()->json([
            'status' => 401,
            'message' => 'unauthorised'
        ], 401);
    }


    public function getSurveyDetailsForMicrosite($survey_id)
    {
        try {
            $surveyId = $survey_id;
            $user_details = Session::get('user');
            $surveyData = GetSurveyService::get($survey_id);
            $riskGrading = GetSurveyMicroSiteService::get($survey_id);
            $riskGradingTooltip = GetSurveyMicroSiteService::getRiskGradingTooltip();
            $riskGradingNarratives = GetSurveyMicroSiteService::getRiskGradingNarratives();
            $riskGradingData = $riskGrading->data->settings;
            $riskGradingHasPropertyPolicy = $riskGrading->has_property_policy;
            $riskGradingHasCasualtyPolicy = $riskGrading->has_casualty_policy;
            $surveyDetailsData = GetSurveyService::getSurveyDetails($survey_id);
            $threadMessageCount = json_decode(Api::get('api/v1/messaging/thread-count/' . $survey_id));
            $submissions = $surveyData->data->submissions;

           
            $resource = $surveyData->data;
            $isSurveyDetailsHidden = $resource->microsite_survey_details_is_hidden;
            $isRiskGradingHidden = $resource->microsite_risk_grading_is_hidden;
            $isRecommendationHidden = $resource->microsite_risk_rec_is_hidden;
            $gradingIndicators = RiskGradingHelper::getGradingColorCodes();
            $cacheKey = "csr-microsite-survey-messages-{$survey_id}";
            $messages = $this->getSetCache($cacheKey);
            if (!$messages) {
                $messages = json_decode(Api::get(static::get_api_uri('survey/' . $survey_id, 'messaging')))
                    ->data;
                $this->getSetCache($cacheKey, $messages);
            }

            if (!empty($messages)) {
                $this->checkMessageAttachments($messages);
            }

            $resource->org_logo = !empty($resource->organisation->logo)
                ? $this->fileUpload->link($resource->organisation->logo)
                : '';
            $resource->commentary_hidden = $resource->commentary_hidden ?? false;

            $recommendationData = [];
            $surveyAttachments  = null;
            $groupGradings      = [];
            if (!empty($submissions)) {
                $this->updateInconsistentGrading($riskGradingData, $submissions);

                $recommendationkeys = $this->extractRecommendations($submissions->form_id);
                $riskRecCards       = json_decode(Api::get('/api/v1/risk-recommendations/' . $survey_id . '/cards'))->data;
                $recommendationData = $this->riskRecommendations($submissions, $recommendationkeys, $threadMessageCount, $surveyId, $riskRecCards);
                $this->attachFilesToRiskRec($recommendationData, $submissions);

                $surveyAttachments=$this->getAttachmentsFromSubmission($submissions);


                $rgOrders = array_map(function ($setting) {
                    return $setting->order;
                }, $riskGradingData);
            
                $orderAlreadySet = count(array_filter($rgOrders, function ($value) {
                    return $value > 0;
                })) > 0;

                if(!$orderAlreadySet){
                    $groupGradings = collect($riskGradingData)
                    ->sortBy(function($item) {
                        return $item->get_rg_attribute->order;
                    })
                    ->groupBy(function($item) {
                        return $item->get_rg_attribute->policy_type->name;
                    });
                }else{
                    $groupGradings = collect($riskGradingData)
                    ->groupBy(function($item) {
                        return $item->get_rg_attribute->policy_type->name;
                    });
                }
            }

            $gradingLabels = RiskGradingHelper::DROPDOWN_LABELS;

            $data = compact([
                'resource',
                'surveyDetailsData',
                'groupGradings',
                'recommendationData',
                'riskGradingHasPropertyPolicy',
                'riskGradingHasCasualtyPolicy',
                'submissions',
                'gradingIndicators',
                'gradingLabels',
                'surveyId',
                'isSurveyDetailsHidden',
                'isRiskGradingHidden',
                'isRecommendationHidden',
                'user_details',
                'messages',
                'surveyAttachments',
                'riskGradingTooltip',
                'riskGradingNarratives'
            ]);
            return $data;
        } catch (\Exception $e) {
            Log::info("csr-microsite error : {$e->getMessage()}");
            return [];
        }
    }

    public function getSurveyHomeDetailsForMicrosite($survey_id)
    {
        $surveyId = $survey_id;
        $user_details = Session::get('user');

        $surveyData = GetSurveyService::get($survey_id);
        $surveyDetailsData = (new GetSurveyService())->getSurveyDetails($survey_id);

        $resource = $surveyData->data;
        $resource->org_logo = !empty($resource->organisation->logo)
            ? $this->fileUpload->link($resource->organisation->logo)
            : '';

        $cacheKey = "csr-microsite-survey-messages-{$survey_id}";
        $messages = $this->getSetCache($cacheKey);
        if (!$messages) {
            $messages = json_decode(Api::get(static::get_api_uri('survey/' . $survey_id, 'messaging')))
                ->data;
            $this->getSetCache($cacheKey, $messages);
        }

        if (!empty($messages)) {
            $this->checkMessageAttachments($messages);
        }

        return compact([
            'resource',
            'user_details',
            'surveyDetailsData',
            'surveyId',
            'messages',
        ]);
    }

    private function extractRecommendations(string $formId)
    {
        $rrData = [];
        $form = json_decode(Api::get('api/v1/risk-improvement/form/' . $formId));
        if ($form->response == 'success') {
            $form = json_decode($form->data, true);

            if (isset($form) && isset($form['fields'])) {
                foreach ($form['fields'] as $field_types) {
                    foreach ($field_types as $field_type => $field_attr) {
                        if ($field_type == 'risk_recommendation') {
                            foreach ($field_attr as $element) {
                                if ($element['name'] == 'name') {
                                    $rrData[] = $element['value'];
                                }
                            }
                        }
                    }
                }
            }
        }
        return $rrData;
    }

    public function login()
    {
        return View::make("csr-microsite/login");
    }

    public function test()
    {
        return view('microsite.index');
    }

    public function riskGradingReOrder(Request $request)
    {
        try {
            $data     = $request->all();
            $surveyId = (int)$data['survey_id'];
            Api::post('/api/v1/microsite/risk-grading-re-order', $data);

            $this->recacheRelatedData($surveyId);
            $this->clearRelatedCache($surveyId);

            return response()->json([
                'status' => 200,
                'data' => compact([
                    'data'
                ])
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 500,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function toggleColumn(Request $request)
    {
        try {
            $startTime = microtime(true);
            $data = $request->all();

            Api::post('/api/v1/microsite/toggle-column', $data);

            $executionTime =  microtime(true) - $startTime;
            $this->recacheRelatedData(($data['survey_id'] ?? ''));
            $this->clearRelatedCache(($data['survey_id'] ?? ''));

            return response()->json([
                'status' => 200,
                'data' => compact([
                    'executionTime',
                ])
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 500,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function toggleCommentary(Request $request)
    {
        if ($request->has('survey_id') && $request->has('value')) {
            $data = $request->all();

            $response = json_decode(Api::post('/api/v1/microsite/toggle-commentary', $data));

            return response()->json([
                'status' => 200,
                'data' => $response,
            ]);
        }
        
        return response()->json([
            'status' => 500,
            'message' => 'Error: survey_id and value is required.'
        ]);

    }

    public function sendToClient(Request $request) {
        try {
            $data = $request->all();        
            Api::post('/api/v1/microsite/send-to-client', $data);            
            return response()->json([
                'status' => 200,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 500,
                'message' => $e->getMessage()
            ], 500);
        }
    }





    private function remapJsonData($messages)
    {
        $jsonData = json_encode($messages);
        $data = json_decode($jsonData);

        $remappedData = array_reduce(
            $data,
            function ($carry, $item) {
                $rrRef = $item->rr_ref;
                $carry[$rrRef][] = $item;
                return $carry;
            },
            []
        );

        return json_decode(json_encode($remappedData, JSON_PRETTY_PRINT));
    }

    private function riskRecommendations($surveyData, $recommendationkeys, $threadMessageCount, $surveyId, $riskRecCards)
    {
        $recommendations = [];
        foreach ($recommendationkeys as $recommendationkey) {

            for ($i = 1; $i <= 15; $i++) {
                $partTitle = $recommendationkey . '_' . $i . '_';
                $messageFieldFilter = $recommendationkey . '_' . $i . '_message';
                $description = $partTitle . 'description';
                $action = $partTitle . 'action';
                $ref = $partTitle . 'ref';
                $classification = $partTitle . 'classification';
                $submissionTitle = $partTitle . 'title';
                $requiredBy = $partTitle . 'required_by';
                $issueClosed = $partTitle . 'issue_closed';

                if (isset($surveyData->$description) && isset($surveyData->$classification) && ($surveyData->$classification != '' || $surveyData->$description != '')) {

                    $refValue = isset($surveyData->{$ref}) ? $surveyData->{$ref} : '-';
                    $classificationValue = isset($surveyData->{$classification}) ? $surveyData->{$classification} : '-';
                    $submissionTitleValue = isset($surveyData->{$submissionTitle}) ? $surveyData->{$submissionTitle} : '-';
                    $requiredByValue = isset($surveyData->{$requiredBy}) ? $surveyData->{$requiredBy} : '-';
                    $issueClosedValue = isset($surveyData->{$issueClosed}) ? $surveyData->{$issueClosed} : '-';
                    $messageCount = $this->getMessageCountById(get_object_vars($threadMessageCount), $messageFieldFilter) ?? 0;

                    $description = isset($surveyData->$description) ? $surveyData->$description : '';
                    $action = isset($surveyData->$action) ? $surveyData->$action : '';

                    $status = 'Open';

                    if (
                        isset($surveyData->{$partTitle . 'issue_closed'}) &&
                        !empty($surveyData->{$partTitle . 'issue_closed'})
                    ) {
                        $issueClosedValue = $surveyData->{$partTitle . 'issue_closed'};

                        switch ($issueClosedValue) {
                            case '1':
                                $status = 'Closed';
                                break;
                            case '2':
                                $status = 'Pending Review';
                                break;
                        }
                    }

                    $statusColor = '';
                    if (!empty($submissionTitleValue)) {
                        foreach ($riskRecCards as $card) {
                            if (!str_contains($card->url, $partTitle)) {
                                continue;
                            } else {
                                if ($status !== 'Closed' && $card->column === KanbanHelper::OVERDUE) {
                                    $requiredBy     = $card->properties->required_by;
                                    $date           = Carbon::createFromFormat('d/m/Y', $requiredBy);
                                    $daysDifference = $date->diffInDays(Carbon::now());
                
                                    // Determine the status based on the difference in days
                                    $statusColor = $daysDifference < 7 ? 'orange' : 'red';
                                    break;
                                } else {
                                    $statusColor = $status === 'Closed' ? 'green' : 'yellow';
                                }
                            }
                        }
                    }

                    $recommendations[] = [
                        'id'               => $recommendationkey,
                        'message_id'       => $messageFieldFilter,
                        'ref'              => $refValue,
                        'classification'   => $classificationValue,
                        'submission_title' => $submissionTitleValue,
                        'required_by'      => $requiredByValue,
                        'issue_closed'     => $issueClosedValue,
                        'issue_closed_id'  => $issueClosed,
                        'description'      => $description,
                        'action'           => $action,
                        'status'           => $status,
                        'message_count'    => $messageCount,
                        'status_color'     => $statusColor,
                        'field_name'       => rtrim($partTitle, '_'),
                    ];
                }
            }
        }

        return $recommendations;
    }

    private function attachFilesToRiskRec(&$recommendations, $submission)
    {
        $surveyId = (int)$submission->survey_id;
        $attachments = json_decode(Api::get('/api/v1/surveys/get-riskrec-attachments/' . $surveyId));
        $submissionNotes = !empty($submission->notes) ? json_decode(json_encode($submission->notes), true) : [];

        if ($attachments->response === 'success') {
            foreach ($recommendations as $idx => $reco) {
                foreach ($attachments->data as $attachment) {
                    if ($attachment->field_name === $reco['field_name']) {
                        // Notes
                        $attachmentNote = $attachment->notes;
                        if (!empty($submissionNotes)) {
                            $fromSubmissionNotes = $this->findArrayValueByPartialKey($submissionNotes, str_replace('.', '_', $attachment->file_name));
                            $attachmentNote = $fromSubmissionNotes ? $fromSubmissionNotes : $attachmentNote;
                        }

                        $filename = $attachment->file_name ?? 'Image.png';
                        $type = strtolower(substr($filename, strrpos("/$filename", '.')));
                        $cloudFilename = 'survey_uploads/' . $attachment->field_name . '/' . $attachment->cloud_file_name;
                        $url = $this->surveyModel->downloadLinkForMicrosite($cloudFilename, $filename) ?? '';

                        $recommendations[$idx]['attachments'][] = (object)[
                            'file_name'       => $filename,
                            'file_type'       => $type,
                            'cloud_file_name' => $cloudFilename,
                            'field_name'      => $attachment->field_name,
                            'url'             => $url,
                            'notes'           => $attachmentNote,
                        ];
                    }
                }
            }
        }
    }

    private function getMessageCountById($recommendationData, $searchId)
    {
        $threadIndex = (array_search($searchId, array_column($recommendationData['message_thread_count'], '_id')));

        if ($threadIndex !== false) {
            return get_object_vars($recommendationData['message_thread_count'][$threadIndex])['message_count'];
        }

        return null;
    }

    private function getAttachmentsFromSubmission($submission){
        $keyword='photographs-';
        $survey_id=$submission->survey_id;
        $matchingKeys = array_filter(array_keys((array)$submission), function ($key) use ($keyword) {
            return strpos($key, $keyword) !== false;
        });
        if(count($matchingKeys) > 0){
            $data=[];
            $photogrephKeys=(array_values($matchingKeys));
            foreach($photogrephKeys as $photogrephKey){
                $apiAttachmentsCall = json_decode(Api::get('/api/v1/surveys/get-attachment-info/' . $photogrephKey . '/' . $survey_id));
                $attachments = $apiAttachmentsCall->data;
                $attachmentData=$this->surveyModel->getSurveyPhotographs($attachments, $survey_id);
                $attachmentData=json_decode($attachmentData);
                if (!empty($attachmentData->files)) {
                    array_push($data, $attachmentData->files);
                }
            }
            return $this->flattenArray($data);
        }
        return null;
    }

    public function recacheRelatedData($surveyId)
    {
        if (empty($surveyId)) {
            return;
        }
        try {
            SendSqsMessageService::sendMessages([
                [
                    'serviceClass' => GetSurveyMicroSiteService::class,
                    'params' => $surveyId ?? '',
                ],
                [
                    'serviceClass' => GetSurveyService::class,
                    'params' => $surveyId ?? '',
                ],
            ]);
        } catch (\Exception $e) {
            \Log::error("MicrositeConroller: " . $e->getMessage());
        }
        
    }

    public function clearRelatedCache($surveyId)
    {
        if (!empty($surveyId)) {
            $this->setCache("csr-microsite-survey-$surveyId", null);
        }
    }
}
