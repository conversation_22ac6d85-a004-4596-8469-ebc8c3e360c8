<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use App\Models\Api;
use Illuminate\Support\Arr;

class LearningCategoryController extends BaseResourceController
{
    const
    TEMPLATE_PATH = '/learning/categories',
    RESPOND_TO_AJAX = true,
    API_NAMESPACE = 'learning/course/categories',
        ROUTE_PREFIX = 'learning.course.category';

    public function __construct(Request $request)
    {
        BaseController::__construct($request);
        View::share(
            'main_class',
            'learning'
        );

        // parent::__construct();
    }

    /**
     * Get additional view parameters for a specific view (index/create/edit)
     *
     * @param  string $view
     * @return array of additional parameters
     */
    public function getRouteParams(Request $request, $view, array $params = [])
    {
        $api_calls = [];

        switch($view) {
        case 'index':
            $api_call = json_decode(
                Api::get(
                    static::get_api_uri(
                        sprintf(
                            'all/%d/%d',
                            $request->get('page', 1),
                            $request->get('limit', 10)
                        )
                    )
                )
            );

            if ($api_call->response == 'success') {
                $params = array_merge(
                    $params, [
                       'resources' => $api_call->data,
                       'total'     => $api_call->total,
                    ]
                );
            }
            break;

        case 'edit':
            if (isset($params['resource'])) {
                $params['resource']->sectors = Arr::pluck(
                    json_decode(json_encode($params['resource']->sectors), 1),
                    'id'
                );

                $params['resource']->cover_types = Arr::pluck(
                    json_decode(json_encode($params['resource']->cover_types), 1),
                    'id'
                );
                    $params['layout'] = 'modal';
            }
            break;
        }

        if (in_array($view, ['index','edit'])) {
            $api_calls = [
                'sectors'  => ['options','sector'],
                'covers'   => ['options','cover'],
            ];
        }

        foreach($api_calls as $key => $values) {
            $params['options'][$key] = json_decode(
                Api::get(
                    static::get_api_uri($values[0], $values[1])
                ), 1
            );
        }

        return $params;
    }

    /**
     * Get validation rules for a specific method (store/update/etc)
     *
     * @param  string $method
     * @return array of validation rules
     */
    public function getValidatorRules(Request $request, $method)
    {
        return array(
        'name' => 'required',
        );
    }

}
