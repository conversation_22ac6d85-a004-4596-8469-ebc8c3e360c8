<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use App\Models\FileUpload;
use App\Models\Api;
use Illuminate\Support\Facades\Validator;
use \Exception;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Str;
class SectorController extends BaseController
{

    //TEMPLATE PATH
    const TEMPLATE_PATH = '/sectors';

    //ROUTE PREFIX
    const ROUTE_PREFIX = 'rr-appetite/sectors';

    public function __construct(Request $request, FileUpload $fileUpload)
    {
        parent::__construct($request);
        $this->files = $fileUpload;
    }

    public function allowForUser(User $user): bool
    {
        return $user->login_type === 'risk-control' || $user?->isRoleAdminOrAccountManager();
    }

    //Index
    public function index()
    {
        $response = json_decode(Api::get('api/v1/rrappetite/sectors/all'));

        if($response->status == "success") {
            return view(
                static::TEMPLATE_PATH . '/index',
                array(
                'sectors' => $response->data
                )
            );
        }
    }

    public function store(Request $request)
    {
        $siccodes = json_decode(Api::get('api/v1/rrappetite/siccodes?list=true'));
        if (Request::isMethod('get')) {
            return view(
                static::TEMPLATE_PATH . '/create',
                array(
                'sic_codes' => $siccodes->data
                )
            );
        } else {
            $data = $request->except(['_token']);

            $validator = Validator::make(
                $data, [
                'description' => 'max:500',
                ]
            );

            if ($validator->fails()) {
                return Redirect::back()->withInput($request->old())->withErrors($validator->errors());
            }

            if($request->hasFile('image')) {
                $file = $request->file('image');
                $ext = $request->file('image')->getClientOriginalExtension();
                $name = Str::uuid()->toString();
                if(!is_bool($this->files->upload($file->getRealPath(), 'ra_sector_images/'.$name->string.'.'.$ext))) {
                    return Redirect::back()->with('error', 'Failed to upload image')->withInput($request->old());
                }
                $data['image'] = $name->string.'.'.$ext;
            }

            $response = json_decode(Api::post('api/v1/rrappetite/sectors/store', $data));

            if($response->status == "success") {
                $sectors = json_decode(Api::get('api/v1/rrappetite/sectors/all'));
                return view(
                    static::TEMPLATE_PATH . '/index',
                    array(
                    'sectors' => $sectors->data
                    )
                );
            }
        }

    }

    public function update(Request $request, $sector_id)
    {
        $siccodes = json_decode(Api::get('api/v1/rrappetite/siccodes?list=true'));
        $associatedsiccodes=json_decode(Api::get('api/v1/rrappetite/siccodes/sector/'.$sector_id));
        $associatedsiccodes=array_map(
            function ($o) {
                return $o->sic_code_id; 
            }, (array)($associatedsiccodes->data)
        );

        if ($request->isMethod('get')) {
            $response = json_decode(Api::get('api/v1/rrappetite/sectors/'.$sector_id));
            try {
                $sector_image = $this->files->link('ra_sector_images/'.$response->data->image);
            } catch(Exception $e) {
                $sector_image = null;
            }

            if($response->status == "success") {
                return view(
                    static::TEMPLATE_PATH . '/edit',
                    [
                    'sector' => $response->data,
                    'sector_image' => $sector_image,
                    'sic_codes' => $siccodes->data,
                    'selected_sic_codes' => $associatedsiccodes,
                    ]
                );
            }
        } else {
            $data = $request->except(['_token']);

            $validator = Validator::make(
                $data, [
                'description' => 'max:500',
                ]
            );

            if ($validator->fails()) {
                return Redirect::back()->withInput($request->old())->withErrors($validator->errors());
            }

            if($request->hasFile('image')) {
                $file = $request->file('image');
                $ext = $request->file('image')->getClientOriginalExtension();
                $name = Str::uuid()->toString();
                if(!is_bool($this->files->upload($file->getRealPath(), 'ra_sector_images/'.$name->string.'.'.$ext))) {
                    return Redirect::back()->with('error', 'Failed to upload image')->withInput($request->old());
                }

                $data['image'] = $name->string.'.'.$ext;
                unset($data['image_id']);
            } else {
                $data['image'] = $data['image_id'];
            }

            $response = json_decode(Api::post('api/v1/rrappetite/sectors/'.$sector_id.'/update', $data));

            if($response->status == "success") {
                $sectors = json_decode(Api::get('api/v1/rrappetite/sectors/all'));
                return view(
                    static::TEMPLATE_PATH . '/index',
                    array(
                    'sectors' => $sectors->data
                    )
                );
            }
        }
    }

    public function delete($sector_id)
    {
        $response = json_decode(Api::delete('api/v1/rrappetite/sectors/'.$sector_id.'/delete'));
        // echo "<pre>";
        if($response->status == "success") {
            $sectors = json_decode(Api::get('api/v1/rrappetite/sectors/all'));
            return view(
                static::TEMPLATE_PATH . '/index',
                [
                'sectors'    => $sectors->data,
                ]
            );
        }
    }
}
