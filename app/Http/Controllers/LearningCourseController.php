<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;
use App\Models\FileUpload;
use App\Models\Api;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Str;
class LearningCourseController extends BaseResourceController
{
    const
    TEMPLATE_PATH = '/learning/course',
        API_NAMESPACE = 'learning/course',
        RESPOND_TO_AJAX = true;

    public function __construct(Request $request, FileUpload $fileUpload)
    {
        BaseController::__construct($request);

        View::share(
            'main_class',
            'learning'
        );

        $this->course_types = [
            'liberty'      => 'Liberty Course',
            'organisation' => 'Organisation course',
        ];

        $this->files = $fileUpload;

        // parent::__construct();
    }

    /**
     * Show all resources
     *
     * @return Response
     */
    public function index(Request $request)
    {
        return view(
            static::TEMPLATE_PATH . '/index',
            static::getRouteParams($request, 'index')
        );
    }

    /**
     * Assign course to user(s)
     *
     * @return Response
     */

    public function assign(Request $request, $id, $type=0)
    {
        if(in_array($type, ['users', 'categories']) && strtolower($request->method()) == 'post') {
            $data = [$type => $request->get($type)];
            $assign = json_decode(Api::post('api/v1/learning/course/'.$id.'/assign', $data));
            if ($request->ajax() && static::RESPOND_TO_AJAX) {
                return Response::json(
                    [
                    'response' => $assign->response,
                    'message'  => $assign->message,
                    ]
                );
            } else {
                return Redirect::to('learning/course')->with($assign->response, $assign->message);
            }
            //return Redirect::back($assign->response, $assign->message);
        } else {
            return view(
                static::TEMPLATE_PATH . '/assign',
                array_merge(static::getRouteParams($request, 'assign', ['course_id' => $id]), ['layout' => 'modal'])
            );
        }
    }

    public function organisationAssigned(Request $request, $id)
    {
        if(strtolower($request->method()) == 'post') {
            $data = $request->all();
            $assign = Api::post('api/v1/learning/course/'.$id.'/organisations', $data);
            $assign = json_decode($assign);
            if ($request->ajax() && static::RESPOND_TO_AJAX) {
                return Response::json(
                    [
                    'response' => $assign->response,
                    'message'  => $assign->message,
                    ]
                );
            } else {
                return Redirect::to('learning/course')->with($assign->response, $assign->message);
            }
        } else {
            $api_org = json_decode(Api::get(static::get_api_uri('options', 'organisation')));
            $assigns = json_decode(Api::get('api/v1/learning/course/' . $id . '/organisations'));

            $organisations       = $api_org;
            $return              = [];
            $return['assigneds'] = $assigns->organisations;
            $assigns             = explode(',', $assigns->organisations);
            $orgs                = [];
            foreach ($organisations as $key => $organisation) {
                $org = (object)Array();
                $org->assigned = false;
                $org->id = $key;
                $org->name = $organisation;
                foreach($assigns as $assign){
                    if($assign == $org->id) {
                        $org->assigned = true;
                    }
                }
                $orgs[] = $org;
            }
            $return['course_id'] = $id;
            $return['orgs'] = $orgs;

            return view(
                static::TEMPLATE_PATH . '/organisation',
                array_merge($return, ['layout' => 'modal'])
            );
        }
    }

    /**
     * Handles the assigning and getting all the sectors
     *
     * @param  int $id
     * @return void
     */
    public function sectorHandler(Request $request, $courseId)
    {
        if ($request->isMethod('get')) { // Show all sectors for options
            $sectors         = json_decode(Api::get(static::get_api_uri('options', 'sector')));
            $assignedSectors = json_decode(Api::get('api/v1/learning/course/' . $courseId . '/sectors'));
            $assigns         = explode(',', $assignedSectors->sectors);
            $data            = [];
            $sectorsTemp     = [];

            $data['assignees'] = $assignedSectors->sectors;
            foreach ($sectors as $id => $name) {
                $sector           = (object)[];
                $sector->assigned = false;
                $sector->id       = $id;
                $sector->name     = $name;
                foreach ($assigns as $sectorId) {
                    if ($sectorId == $sector->id ) {
                        $sector->assigned = true;
                    }
                }
                $sectorsTemp[] = $sector;
            }

            $data['course_id']  = $courseId;
            $data['sectors']    = $sectorsTemp;

            return view(
                static::TEMPLATE_PATH . '/sector',
                $data + ['layout' => 'modal']
            );
        } else { // Add or Remove entry for sector
            $data            = $request->all();
            $assignedSectors = Api::post('api/v1/learning/course/' . $courseId . '/sectors', $data);
            $assign          = json_decode($assignedSectors);

            if (!$request->ajax() && !static::RESPOND_TO_AJAX) {
                return Redirect::to('learning/course')->with($assign->response, $assign->message);
            }

            return Response::json(
                [
                'response' => $assign->response,
                'message'  => $assign->message,
                ]
            );
        }
    }

    public function destroyImage(Request $request)
    {
        $url_params = Route::current()->parameters();
        $type = isset($url_params['type']) ? $url_params['type'] : '';
        if (isset($url_params['course_id']) && isset($url_params['type']) && isset($url_params['path'])) {
            $api = json_decode(
                Api::delete(
                    static::get_api_uri(
                        sprintf('%s/%s', $url_params['type'], $url_params['path']),
                        'learning/course-image'
                    )
                )
            );

            return Redirect::route(
                static::getParentRoutePrefix() . '.edit',
                ['id' => $url_params['course_id']]
            )->with(
                $api->response,
                $api->message
            );
        } else {
            return Redirect::back(
                static::getParentRoutePrefix() . '.edit',
                ['id' => $url_params['course_id']]
            )->withInput(
                $request->old()
            )->with(
                'error',
                sprintf('The %s could not be deleted', ($type ? ucwords(str_replace('-', ' ', $type)) : 'Course Image'))
            );
        }
    }

    /**
     * Get additional view parameters for a specific view (index/create/edit)
     *
     * @param  string $view
     * @return array of additional parameters
     */
    public function getRouteParams(Request $request, $view, array $params = [])
    {
        switch($view) {
        case 'index':
            $params['resources'] = (object)[];

            // get courses by type
            foreach(array_keys($this->course_types) as $course_type) {
                $api_call = json_decode(Api::get(static::get_api_uri(sprintf('%s/1/0', $course_type))));
                if ($api_call->response == 'success') {
                    $params['resources']->{$course_type} = json_decode(json_encode($api_call->data));
                    foreach($params['resources']->{$course_type} as $key => $value) {
                        if(isset($value->image) && $value->image != null) {
                            $value->featured_image = $value->image;

                            $this->getImages($value);
                        }

                        // add buttons to courses
                        $params['resources']->{$course_type}[$key]->btns = [
                            [
                                'url'   => route('learning.course.view', $params['resources']->{$course_type}[$key]->id),
                                'title' => 'View',
                                'class' => 'default pull-left',
                            ],
                            [
                                'url'   => route('learning.course.assign', [$params['resources']->{$course_type}[$key]->id]),
                                'title' => 'Assign to user',
                                'class' => 'primary use-modal',
                            ],
                            [
                                'url'   => route('learning.course.duplicate', $params['resources']->{$course_type}[$key]->id),
                                'title' => 'Duplicate',
                                'class' => 'default btn-duplicate',
                            ],
                            [
                                'url'   => route('learning.course.edit', $params['resources']->{$course_type}[$key]->id),
                                'title' => 'Edit',
                                'class' => 'default',
                            ],
                        ];
                    }
                }
            }

            $api_calls = [
                'organisations' => ['options','organisation'],
            ];

            break;

        case 'create':
            $api_calls = [
                'organisations' => ['options','organisation'],
                'categories'    => ['options','learning/course/categories'],
                'courses'       => ['all/0/0','learning/course'],
            ];
            break;

        case 'assign':
            $api_calls = [
                'organisations' => ['options','organisation'],
            ];
            $assigned_users = json_decode(Api::get('api/v1/learning/course/'.$params['course_id'].'/assign'));
            $params['options']['users'] = $assigned_users->user_ids;
            break;
        case 'edit':
            if (isset($params['resource'])) {

                $params['resource']->featured_image = $params['resource']->image;
                $params['resource']->accreditation_logo_images = $params['resource']->accreditation_logos;
                $this->getImages($params['resource']);
            }

            $api_calls = [
                'organisations' => ['options', 'organisation'],
                'categories'    => ['options', 'learning/course/categories']
            ];

            $params['options']['target_audience'] = [
                1 => 'Organisation admin',
                2 => 'Organisation employee',
            ];

            $params['layout'] = 'layout';
            break;
        default:
            $api_calls = [];
            break;
        }

        foreach($api_calls as $key => $values) {
            $params['options'][$key] = json_decode(
                Api::get(
                    static::get_api_uri($values[0], $values[1])
                ), 1
            );
        }

        return $params;
    }

    /**
     * Parse data before storage
     *
     * @param  array $data
     * @return array parsed version of $data
     */
    public function parseDataBeforeStorage($data)
    {

        $image_properties = [
            'featured_image',
            'accreditation_logo_image',
        ];
        // store uploaded images & set the image property to the uploaded filename
        foreach($image_properties as $image_property) {
            if(isset($data[$image_property])) {
                if(is_array($data[$image_property])) {

                    foreach($data[$image_property] as $key => $upImage){
                        if (class_basename($upImage) == 'UploadedFile') {
                            $file_name = Str::uuid();

                            $file_uploaded = is_bool(
                                $this->files->upload(
                                    $upImage->getRealPath(),
                                    $file_name,
                                    true
                                )
                            );

                            $data[$image_property][$key] = ($file_uploaded)
                                ? $file_name
                                : null;
                        }
                    }
                }else{
                    if (class_basename($data[$image_property]) == 'UploadedFile') {
                        $file_name = Str::uuid();

                        $file_uploaded = is_bool(
                            $this->files->upload(
                                $data[$image_property]->getRealPath(),
                                $file_name,
                                true
                            )
                        );

                        $data[$image_property] = ($file_uploaded)
                            ? $file_name
                            : null;
                    }
                }
            }
        }

        return $data;
    }
    /*return this functions in case of multiple images upload doesn't work
    public function parseDataBeforeStorage($data) {

        $image_properties = [
            'featured_image',
            'accreditation_logo_image',
        ];
        // store uploaded images & set the image property to the uploaded filename
        foreach($image_properties as $image_property) {
            if (isset($data[$image_property]) && class_basename($data[$image_property]) == 'UploadedFile') {
                $file_name = Str::uuid();

                $file_uploaded = is_bool($this->files->upload(
                    $data[$image_property]->getRealPath(),
                    $file_name
                ));

                $data[$image_property] = ($file_uploaded)
                    ? $file_name
                    : null;
            }
        }

        return $data;
    }*/

    /**
     * Get validation rules for a specific method (store/update/etc)
     *
     * @param  string $method
     * @return array of validation rules
     */
    public function getValidatorRules(Request $request, $method)
    {
        switch($method) {
        case 'store':
            return [
                    'title'        => 'required',
                    // 'type'         => 'required',
                    'organisation' => 'required_if:type,organisation',
                ];

        case 'update':
            return [
                    'title'                    => 'required',
                    'featured_image'           => 'image',
                    'accreditation_logo_image[]' => 'image',
                ];

        default:
            return [];
        }
    }

    /**
     * Get validation messages for a specific method (store/update/etc)
     *
     * @param  string $method
     * @return array of validation rules
     */
    public function getValidatorMessages($method)
    {
        switch($method) {
        case 'store':
            return [
                    'organisation.required_if' => 'The :value :other requires an :attribute to also be selected.',
                ];
        case 'update':
            return [
                    'featured_image.image'           => 'The uploaded :attribute has to be an image file.',
                    'accreditation_logo_image[].image' => 'The uploaded :attribute has to be an image file.',
                ];
        default:
            return [];
        }
    }

    /**
     * Get validation attribute names
     *
     * @return array of validation attribute names
     */
    public function getValidatorAttributeNames()
    {
        //dd('1');
        return [
            'title'                    => 'course title',
            'type'                     => 'course type',
            'featured_image'           => 'featured picture',
            'accreditation_logo_image[]' => 'accreditation logo',
        ];
    }

    private function getImages(&$item)
    {
        $image_properties = [
            'featured_image',
            'accreditation_logo_images',
        ];
        //$item);

        foreach($image_properties as $image_property) {
            if (isset($item->{$image_property})) {
                if (is_array($item->{$image_property})) {
                    //dd('here')
                    foreach($item->{$image_property} as $key => $value) {

                        $item->{$image_property}[$key] = $this->files->link($value);
                    }
                } else {
                    //dd($this->files->link($item->{$image_property}));
                    $item->{$image_property} = $this->files->link($item->{$image_property});
                }
            }
        }
    }

    public function viewCourse($course_id)
    {
        $course = json_decode(Api::get('api/v1/learning/course/' . $course_id . '/view'));
        if(isset($course->data->image)) {
            $course->data->featured_image = $course->data->image;
        }
        $this->getImages($course->data);
        $data = isset($course->data) ? $course->data : '';

        return view(
            static::TEMPLATE_PATH . '/view', ['course' => $data]
        );
    }
    public function reminder(Request $request, $course_id)
    {
        $data = $request->all();
        $api_call = Api::post('api/v1/learning/course/' . $course_id . '/reminder', $data);
        $api_call = json_decode($api_call);
        if($api_call && $api_call->response == 'success') {
            return Response::json(
                array(
                'response' => 'success',
                'data' => $data['user_id']
                )
            );
        }else{
            return Response::json(
                array(
                'response' => 'error'
                )
            );
        }
    }
    public function duplicate(Request $request, $course_id)
    {
        $data = $request->except('token');
        $data['src'] = 'liberty';
        //dd(static::get_api_uri(). '/' . $course_id . '/duplicate');
        $api_call = Api::post(static::get_api_uri(). '/' . $course_id . '/duplicate', $data);
        $api_call = json_decode($api_call);
        if(isset($api_call->response) && $api_call->response == 'success') {

            if ($request->ajax() && static::RESPOND_TO_AJAX) {
                return Response::json(
                    [
                    'response' => $api_call->response,
                    'url'  => route('learning.course.edit', array('course' => $api_call->data->id))//'learning/course/'.$api_call->data->id.'/edit',
                    ]
                );
            }else{
                return Redirect::to('learning/course/'.$api_call->data->id.'/edit');
            }
        }else{
            if ($request->ajax() && static::RESPOND_TO_AJAX) {
                return Response::json(
                    [
                    'response' => isset($api_call->response)?$api_call->response:'error',
                    'message'  => isset($api_call->message)?$api_call->message:'error'
                    ]
                );
            }else{
                return Redirect::to('learning/course/'.$course_id.'/edit')->with(isset($api_call->response)?$api_call->response:'error', isset($api_call->message)?$api_call->message:'Was not possible duplicate this course');
            }
        }
    }
}
