<?php

namespace App\Http\Controllers;

use App\Models\Api;
use App\Services\CacheContent\GetBrokerUserService;
use App\Services\SendSqsMessageService;
use Illuminate\Http\Request;
use PragmaRX\Google2FA\Google2FA;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Response;
use GuzzleHttp\Exception\GuzzleException;
use PragmaRX\Google2FA\Exceptions\InvalidCharactersException;
use PragmaRX\Google2FA\Exceptions\SecretKeyTooShortException;
use PragmaRX\Google2FA\Exceptions\IncompatibleWithGoogleAuthenticatorException;

class BrokerUserController extends BaseResourceController
{
    const
        TEMPLATE_PATH = '/broker-users',
        ROUTE_PREFIX = 'broker-users',
        RESPOND_TO_AJAX = true;

    public function __construct(Request $request)
    {
        BaseController::__construct($request);
    }

    /**
     * @throws IncompatibleWithGoogleAuthenticatorException
     * @throws InvalidCharactersException
     * @throws GuzzleException
     * @throws SecretKeyTooShortException
     */
    public function resetTfa($user_id)
    {
        $key = (new Google2FA)->generateSecretKey();

        $api = json_decode(
            Api::post(
                '/api/v1/reset-bu-tfa?user_id=' . $user_id . '&key=' . $key
            )
        );

        if ($api->response == 'success') {
            return Redirect::route(
                static::ROUTE_PREFIX . '.index'
            )->with(
                'success',
                $api->message
            );
        }

        return Redirect::route(
            static::ROUTE_PREFIX . '.index'
        )->with(
            'error',
            $api->message
        );
    }

    public function generateLink($userID)
    {
        if (isset($userID)) {
            $response = json_decode(Api::get('/api/v1/broker-users/link/' . $userID));

            if ($response->response == 'success') {
                return Response::json(
                    [
                        'response' => 'success',
                        'data'     => $response->data
                    ]
                );
            }
        }
    }

    /**
     * Get additional view parameters for a specific view (index/create/edit)
     *
     * @param  string $view
     * @param  array  $params
     * @return array of additional parameters
     */
    public function getRouteParams(Request $request, $view, array $params = [])
    {
        return array_merge(
            $params,
            [
                'options' => [
                    'brokers' => static::getBrokerOptions()
                ]
            ]
        );
    }

    public function getAdditionalViewParams(string $view, $params = [])
    {
        return array_merge(
            (array) $params,
            [
                'options' => [
                    'brokers' => static::getBrokerOptions()
                ]
            ]
        );
    }

    /**
     * Get validation rules for a specific method (store/update/etc)
     *
     * @param  string $method
     * @return array of validation rules
     */
    public function getValidatorRules(Request $request, $method)
    {
        return [
            'first_name' => 'required',
            'last_name'  => 'required',
            'email'      => 'required|email',
            'phone'      => 'required',
        ];
    }

    private static function getBrokerOptions()
    {
        return json_decode(
            Api::get(
                static::get_api_uri('options', 'brokers')
            )
        );
    }

    public static function getBrokerSchemeCount()
    {
        $user = Session::get('user');

        return $user->broker ? count($user->broker->schemes) : 0;
    }

    public function resetLoginAttempts($email)
    {
        $result = json_decode(Api::get('api/v1/broker-users/'.$email.'/reset-login-attempts'));

        if (isset($result->response) && !empty($result->response == 'success')) {
            return Redirect::route('broker-users.edit', $result->user_id)->with(array('message' => 'Reset Login Success'));
        }
    }

    public static function onStoreSuccess($data)
    {
        self::resetCacheForDatatable();
    }

    public static function onUpdateSuccess($data)
    {
        self::resetCacheForDatatable();
    }

    public static function onDestroySuccess()
    {
        self::resetCacheForDatatable();
    }

    public static function resetCacheForDatatable()
    {
        SendSqsMessageService::sendMessages([
            [
                'serviceClass' => GetBrokerUserService::class,
                'params' => '',
            ]
        ]);
    }
}
