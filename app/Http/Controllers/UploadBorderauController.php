<?php

namespace App\Http\Controllers;

use App\Models\FileUpload;
use App\Services\CacheContent\GetBrokerService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Response;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\Api;
use Illuminate\Support\Facades\Validator;
use App\Imports\BorderauImport;
use PhpOffice\PhpSpreadsheet\Shared\Date;

class UploadBorderauController extends BaseController
{
    public function __construct(Request $request, FileUpload $fileUpload)
    {
        parent::__construct($request);

        $this->files = $fileUpload;
    }

    public function show()
    {
        $brokers = GetBrokerService::get('')->data;

        return view(
            'do-facilities/upload-borderau', [
            'brokers' => $brokers,
            ]
        );
    }

    public function download()
    {
        $storage = storage_path('bordereau');


        $file = $storage.'/Template-Bordereau.xlsx';
        $headers = [
            'Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ];

        return Response::download($file, 'Template-Bordereau.xlsx', $headers);
    }

    public function store(Request $request)
    {
        $data = $request->except('_token');

        $validation = Validator::make(
            $data, [
                'file' => 'required',
            ]
        );

        if ($validation->fails()) {
            return Redirect::back()
                ->withErrors($validation->errors())
                ->withInput($request->old());
        }

        $file = $request->file('file')->move(storage_path(), 'borderau.xlsx');;

        $borderauData = Excel::toCollection(new BorderauImport(), storage_path('borderau.xlsx'));
        $borderauData = $borderauData[0];

        $insert = [];

        if (!empty($borderauData)) {
            foreach ($borderauData as $value) {
                $value = (object)$value->toArray();
                if ($value->name == null && $value->policy_number == null) {
                    continue;
                }

                $insert[] = [
                    'name' => $value->name,
                    'liberty_branch' => !empty($value->liberty_branch)
                        ? $value->liberty_branch
                        : null,
                    'policy_number' => $value->policy_number,
                    'inception_date_of_cover' => !empty($value->inception_date_of_cover)
                        ? Date::excelToDateTimeObject($value->inception_date_of_cover)->format('Y-m-d H:i:s')
                        : '-',
                    'expiry_date_of_cover' => !empty($value->expiry_date_of_cover)
                        ? Date::excelToDateTimeObject($value->expiry_date_of_cover)->format('Y-m-d H:i:s')
                        : '-',
                    'premium' => $value->premium,
                    'description' => $value->description,
                    'email' => $value->email,
                    'phone' => $value->phone,
                    'address_line_1' => $value->address_line_1,
                    'address_line_2' => $value->address_line_2,
                    'postcode' => $value->postcode,
                    'country' => $value->country,
                    'image' => $value->image ?? null,
                    'admin_first_name' => $value->admin_first_name,
                    'admin_last_name' => $value->admin_last_name,
                    'admin_job_title' => $value->admin_job_title,
                    'admin_email' => $value->admin_email,
                    'admin_phone' => $value->admin_phone,
                    'manager' => 1,
                    'croner_access' => 1,
                ];
            }
        }

        $data = [
            'facility_name' => $data['facility_name'],
            'broker_id' => $data['broker_id'],
            'insert' => $insert,
        ];

        $response = json_decode(Api::post('api/v1/do-facilities/upload-borderau', $data));

        return Redirect::back()
            ->with($response->status, $response->message);
    }
}
