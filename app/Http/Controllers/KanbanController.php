<?php

namespace App\Http\Controllers;

use App\Models\Api;
use App\Models\User;
use App\Traits\HelperTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Session;
use App\Services\RiskRecommendationTrackerService as RRTrackerService;
use Exception;

class KanbanController extends BaseController
{
    use HelperTrait;

    const
        TEMPLATE_PATH = '/kanban';
    const ROUTE_PREFIX = 'tracker';
    const NAV_ID = 'surveys';

    private static $types = ['surveys', 'risk-recommendations'];

    public function allowForUser(User $user): bool
    {
        return in_array(
            $user->login_type,
            ['risk-control', 'underwriter', 'risk-engineer', 'aspen-user', 'broker-user']
        );
    }

    public function index(Request $request, $type)
    {
        if (Session::get('user')->login_type == 'aspen-user' && $type == 'surveys') {
            return Response::make('Unauthorized', 401);
        }

        if (in_array($type, static::$types)) {
            $viewPath = $type === 'risk-recommendations' ? '/risk-recommendations' : '';
            return view(
                static::TEMPLATE_PATH . $viewPath . '/index',
                static::getRouteParams($request, 'index', $type)
            );
        } else {
            throw new \Exception('The specified type of Kanban Board is invalid.');
        }
    }

    public function next(Request $request)
    {
        $request['user_type'] = Session::get('user')->login_type;
        $request['user_id'] = Session::get('user')->id;
        $cards =  json_decode(Api::get("api/v1/risk-recommendations/cards?" . http_build_query($request->all())), true);
        $cards = $this->reduceCards($cards, $request);
        $column = $request->get('column');

        return view('kanban.partials.cards', compact('cards', 'column'));
    }

    public function surveyNext(Request $request)
    {
        $request['user_type'] = Session::get('user')->login_type;
        $request['user_id'] = Session::get('user')->id;
        $cards =  json_decode(Api::get("api/v1/survey-cards?" . http_build_query($request->all())), true);
        $column = $request->get('column');
        return view('kanban.partials.survey-cards', compact('cards', 'column'));
    }

    /**
     * Get additional view parameters for a specific view (index/create/edit).
     *
     * @param string $view
     * @param array  $params
     *
     * @return array of additional parameters
     */
    public function getRouteParams(Request $request, $view, $type)
    {
        $time_start = microtime(true);
        $params = [];
        if ($view == 'index') {
            $params = [
                'type'           => $type,
                'title'          => static::getPageTitle($type),
                'type_acronym'   => static::getTypeAcronym($type),
                'columns'        => static::getColumns($request, $type),
                'filter_options' => static::getFilterOptions($type),
                'filters'        => static::getFilters($request, $type),
            ];
        } else {
            $params = array_merge($params, [
                'type' => $type,
            ]);
        }
        \Log::info("Query Params:" . parse_url($_SERVER['REQUEST_URI'], PHP_URL_QUERY));
        \Log::info('KanbanController::getRouteParams() $execution_time: ' . microtime(true) - $time_start);

        return $params;
    }

    private static function getPageTitle($type)
    {
        $titles = [
            'surveys' => 'Survey Tracker',
            'risk-recommendations' => 'Risk Recommendations Tracker',
        ];

        return (isset($titles[$type]))
            ? $titles[$type]
            : '';
    }

    private static function getFilters(Request $request, $type)
    {
        $inputs = $request->all();

        return (object) $inputs;
    }

    private static function getTypeAcronym($type)
    {
        $letters = explode('-', $type);

        foreach ($letters as $key => $value) {
            $letters[$key] = strtoupper($value[0]);
        }

        return implode('', $letters);
    }

    private static function getCards(Request $request, string $type)
    {
        $request['user_type'] = Session::get('user')->login_type;
        $request['user_id'] = Session::get('user')->id;
        $columns = static::getEmptyColumns($type);
        if ($request->has('location')) {
            $request->merge(['limit' => 1000]);
        }

        $url='api/v1/risk-recommendations/cards?' . http_build_query($request->all());
       
        $submissionCards = json_decode( Api::get($url),true);

        foreach ($submissionCards as $column => $cards) {
            $cards = self::reduceCards($cards, $request);
            $columns[$column]['cards'] = $cards;
        }

        return $columns;
    }

    private static function getSurveyCards(Request $request, string $type)
    {
        $request['user_type'] = Session::get('user')->login_type;
        $request['user_id'] = Session::get('user')->id;
        $columns = static::getEmptyColumns($type);
        $dataUrl='api/v1/survey-cards/?' . http_build_query($request->all());

        $cards = json_decode(Api::get($dataUrl), true);

        foreach ($cards as $column => $card) {
            $columns[$column]['cards'] = $card;
        }

        return $columns;
    }

    private static function getColumns(Request $request, $type, $export = 0)
    {
        $columns = static::getEmptyColumns($type);
        
        switch ($type) {
            case 'surveys':
                $columns = static::getSurveyCards($request, $type);
                break;

            case 'risk-recommendations':
                $columns = static::getCards($request, $type);
                break;
        }

        if ($type) {
            if ($request->has('srf')) {
                foreach ($columns as $key => $column) {
                    $columns[$key]['cards']['data'] = array_filter(
                        array_map(
                            function ($col) use ($request) {
                                if ($col['properties']['SRF'] == str_replace('CSR%0A', 'SRF', urlencode($request->get('srf')))) {
                                    return $col;
                                }
                            },
                            $column['cards']['data']
                        )
                    );
                }
            }
            // echo json_encode($columns); die();
            return $columns;
        }
    }

    private static function getFilterOptions($type)
    {
        $filters = [];

        $api_calls = [
            'organisations' => ['options', 'organisation'],
            'branches' => ['options', 'liberty-branches'],
            'underwriters' => ['options/underwriter', 'liberty-users'],
            'forms' => ['form/list?type=options', 'risk-improvement'],
        ];

        if (Session::get('user')->login_type == 'broker-user') {
            $api_calls['organisations'] = ['options/broker/' . Session::get('user')->broker_id, 'organisation'];
            $api_calls['broker_users'] = ['organisation/options/' . Session::get('user')->broker_id, 'broker-users'];
        }


        foreach ($api_calls as $key => $values) {
            $cacheKey = "rr-tracker-option-" . $key . "-" . Session::get('user')->broker_id ?? "";
            if (Cache::has($cacheKey) && !request()->has('refresh_cache')) {
                $filters[$key] = Cache::get($cacheKey);
            } else {
                $filters[$key] = json_decode(
                    Api::get(
                        static::get_api_uri($values[0], $values[1])
                    )
                );
                Cache::put($cacheKey, $filters[$key], 1800);
            }
        }

        // print_r($filters);
        // exit;

        if ($type == 'surveys') {
            $cacheKey = "rr-tracker-option-types-" . Session::get('user')->broker_id ?? "";
            if (Cache::has($cacheKey) && !request()->has('refresh_cache')) {
                $filters['types'] = Cache::get($cacheKey);
            } else {
                $filters['types'] = json_decode(
                    Api::get(
                        static::get_api_uri('options/name', 'policy-types')
                    ),
                    1
                );
                Cache::put($cacheKey, $filters['types']);
            }
        }

        // print_r($filters); exit;
        foreach ($filters as $key => $filter) {
            // if ($key!='organisations') {
            // print_r(sprintf('All %s',ucwords(str_replace('-','-',$key))));
            // $filters[$key] = (object) array_merge(['0' => sprintf('All %s', ucwords(str_replace('_', ' ', str_replace('-', '-', $key))))], (array) $filter);
            // array_unshift($filters[$key],
            //     sprintf('All %s',ucwords(str_replace('-','-',$key))) );
            //            $filters[$key] = array_merge(['' => sprintf('All %s',ucwords(str_replace('-','-',$key)))],$filter);
            // }
            $mergedFilter = ['0' => sprintf('All %s', ucwords(str_replace('_', ' ', str_replace('-', '-', $key))))] + (array) $filter;
            $filters[$key] = (object) $mergedFilter;
        }

        // dd($filters);

        $user = Session::get('user');

        // Get cache if available
        $mgaSchemeCacheKey = 'rr-tracker-option-mga-schemes-' . $user->broker_id ?? '';
        $cachedMgaSchemes = Cache::get($mgaSchemeCacheKey, []);
        if (!empty($cachedMgaSchemes) && !request()->has('refresh_cache')) {
            $filters['mga_schemes'] = $cachedMgaSchemes;
            return $filters;
        }

        if ($user->login_type == 'broker-user') {
            if ($user->broker_id) {
                $filters['mga_schemes'] = json_decode(
                    Api::get('api/v1/mga-schemes/all/0/99999?getNameAndIdOnly=true&broker_id=' . $user->broker_id)
                )->data;
            }
        } else {
            $filters['mga_schemes'] = json_decode(
                Api::get('api/v1/mga-schemes/all/0/99999?getNameAndIdOnly=true')
            )->data;
        }

        $mga_schemes = [];
        foreach ($filters['mga_schemes'] as $scheme) {
            $mga_schemes[$scheme->id] = $scheme->name;
        }

        Cache::put($mgaSchemeCacheKey, $mga_schemes, 1800);
        $filters['mga_schemes'] = $mga_schemes;

        return $filters;
    }

    private static function filterCards(Request $request, array $cards, $type)
    {
        // This seems to be unnecessary so I commented this out
        // $cards=Helper::convertArrayKeysToString($cards);

        switch ($type) {
            case 'surveys':
                if ($request->has('mga_scheme') && $request->get('mga_scheme') != 0) {
                    $mga_scheme = $request->get('mga_scheme');

                    foreach ($cards as $key => $card) {
                        if (
                            isset($card->survey->organisation->mga_scheme)
                            && intval($mga_scheme) > 0
                            && intval($mga_scheme) == $card->survey->organisation->mga_scheme
                        ) {
                            // do nothing, scheme matches
                        } else {
                            // remove card as it has an invalid scheme
                            unset($cards[$key]);
                        }
                    }
                }

                if ($request->has('organisation') && $request->get('organisation') != 0) {
                    $organisation_id = $request->Get('organisation');
                    foreach ($cards as $key => $card) {
                        if (!isset($card->survey->organisation) || $card->survey->organisation == null || $card->survey->organisation->id != (int) $organisation_id) {
                            unset($cards[$key]);
                        }
                    }
                }

                if ($request->has('branch') && $request->get('branch') != 0) {
                    $branch_id = $request->Get('branch');
                    foreach ($cards as $key => $card) {
                        if (!isset($card->branch) || $card->branch == null || $card->branch->id != (int) $branch_id) {
                            unset($cards[$key]);
                        }
                    }
                }

                if ($request->has('type') && $request->Get('type') != 0) {
                    $type_id = $request->Get('type');
                    foreach ($cards as $key => $card) {
                        if (isset($card->survey_form) && $card->survey_form != $type_id) {
                            unset($cards[$key]);
                        }
                    }
                }

                if ($request->has('survey_date') && $request->Get('survey_date') != '') {
                    $survey_date = \Carbon\Carbon::createFromFormat('d/m/Y', $request->Get('survey_date'));
                    foreach ($cards as $key => $card) {
                        if (isset($card->schedule->actual_submission_deadline) && $card->schedule != null) {
                            $actual_date = \Carbon\Carbon::createFromFormat('d/m/Y', $card->schedule->actual_submission_deadline);
                            if ($actual_date < $survey_date) {
                                unset($cards[$key]);
                            }
                        } else {
                            unset($cards[$key]);
                        }
                    }
                }

                if ($request->has('underwriter') && $request->get('underwriter') != '0') {
                    $uwr = $request->get('underwriter');
                    foreach ($cards as $key => $card) {
                        if (!isset($card->underwriter->id) || $card->underwriter->id != (int) $uwr) {
                            unset($cards[$key]);
                        }
                    }
                }

                break;

            if ($request->has('broker_underwriter_id') && $request->get('broker_underwriter_id') != '0') {
                $uwr = $request->get('broker_underwriter_id');
                foreach ($cards as $key => $card) {
                    if (!isset($card->broker_underwriter_id) || $card->broker_underwriter_id == null || $card->survey->broker_underwriter_id != (int) $uwr) {
                        unset($cards[$key]);
                    }
                }
            }

                break;

            case 'risk-recommendations':
                foreach ($cards as $key => $card) {
                    // filter mga_scheme
                    $cards = RRTrackerService::filterMgaScheme($cards, $card, $key);
                    // filter organisation
                    // This is commented as filtering by org is now api's reponsibility, can be uncommented if needed
                    // $cards = RRTrackerService::filterOrganisation($cards, $card, $key);
                    // filter aspen-user
                    $cards = RRTrackerService::filterAspenUser($cards, $card, $key);
                    // filter branch
                    $cards = RRTrackerService::filterBranch($cards, $card, $key);
                    // filter lob
                    $cards = RRTrackerService::filterLob($cards, $card, $key);
                    // filter underwriter
                    $cards = RRTrackerService::filterUnderwriter($cards, $card, $key);
                    // filter survey_date
                    $cards = RRTrackerService::filterSurveyDate($cards, $card, $key);
                }
                break;
        }

        return $cards;
    }

    private static function getCardColumnbyCriteria($card, $type, $risk_rec_prefix = '')
    {
        //print_r("<pre>");print_r($card);exit;
        //Underwriter deadline set
        try {
            $underwriter_date = isset($card->schedule->underwriter_deadline) ? \Carbon\Carbon::parse($card->schedule->underwriter_deadline) : null;
        } catch (Exception $ex) {
            $underwriter_date = isset($card->schedule->underwriter_deadline) ? \Carbon\Carbon::createFromFormat('d/m/Y', $card->schedule->underwriter_deadline) : null;
        }

        //CSR Deadline set
        try {
            $csr_date = isset($card->schedule->client_survey_report_deadline) ? \Carbon\Carbon::parse($card->schedule->client_survey_report_deadline) : null;
        } catch (Exception $ex) {
            $csr_date = isset($card->schedule->client_survey_report_deadline) ? \Carbon\Carbon::createFromFormat('d/m/Y', $card->schedule->client_survey_report_deadline) : null;
        }
        /* if($card->id == 58){
            echo "<pre>"; dd($card);
        } */


        if ($type == 'surveys') {
            if (Session::get('user')->login_type == 'broker-user' && isset($card->submission) && isset($card->submission->csr_status) && $card->submission->csr_status == 'submitted') {
                return 'survey-completed';
            }
            switch (true) {
                //APPROVED UWR

                case $card->uwr_uw_status == '2':
                    if ($card->csr_uw_status == '1') {
                        array_push($card->additional_data, 'CSR UNDERWRITER');
                    }
                    if ($card->csr_uw_status == '2') {
                        array_push($card->additional_data, 'CSR APPROVED');
                    }

                    return 'approved-uwr';

                //APPROVED CSR
                case $card->csr_uw_status == '2':
                    if ($card->uwr_uw_status == '1') {
                        array_push($card->additional_data, 'UWR UNDERWRITER');
                    }
                    if ($card->uwr_uw_status == '2') {
                        array_push($card->additional_data, 'UWR APPROVED');
                    }
                    if (isset($card->schedule->actual_submission_deadline) && isset($underwriter_date) && $underwriter_date != null && $underwriter_date < \Carbon\Carbon::now() && ((isset($card->submission->uwr_status) && $card->submission->uwr_status != 'submitted') || !isset($card->submission->uwr_status))) {
                        if (Session::get('user')->login_type != 'broker-user') {
                            return 'past-due-uwr';
                        } else {
                            if (!$card->submission && isset($card->schedule->actual_submission_deadline) && \Carbon\Carbon::parse($card->schedule->actual_submission_deadline) < \Carbon\Carbon::now()) {
                                if (Session::get('user')->login_type == 'broker-user') {
                                    return 'survey-completed';
                                }
                            }
                            if (!$card->submission && isset($card->schedule->actual_submission_deadline)) {
                                return 'survey-date-booked';
                            }
                        }
                    }
                    return 'approved-csr';
                //SENT TO UWR
                case $card->uwr_uw_status == '1':
                    return 'underwriter-uwr';

                //SENT TO CSR
                case $card->csr_uw_status == '1':
                    return 'underwriter-csr';

                //QA UWR
                case $card->uwr_qa_status == '1':
                    if (Session::get('user')->login_type != 'broker-user') {
                        return 'quality-assurance-uwr';
                    }

                    //QA CSR
                case $card->csr_qa_status == '1':
                    if (Session::get('user')->login_type != 'broker-user') {
                        return 'quality-assurance-csr';
                    }
                case isset($card->submission->uwr_status) && $card->submission->uwr_status === 'submitted':
                    return 'submitted-uwr';

                case isset($card->submission->csr_status) && $card->submission->csr_status == 'submitted':
                    return 'submitted-csr';

                case isset($card->submission->uwr_status) && $card->submission->uwr_status != 'submitted':
                    if (Session::get('user')->login_type != 'broker-user') {
                        return 'draft-report-in-progress-uwr';
                    }
                case isset($card->submission->csr_status) && $card->submission->csr_status != 'submitted':
                    if (Session::get('user')->login_type != 'broker-user') {
                        return 'draft-report-in-progress-csr';
                    }
                    //Past Due CSR
                case isset($card->schedule->actual_submission_deadline) && isset($csr_date) && $csr_date != null && $csr_date < \Carbon\Carbon::now() && ((isset($card->submission->csr_status) && $card->submission->csr_status != 'submitted') || !isset($card->submission->csr_status)):
                    if (Session::get('user')->login_type != 'broker-user') {
                        return 'past-due-csr';
                    }
                    //Past DUE UWR
                case isset($card->schedule->actual_submission_deadline) && isset($underwriter_date) && $underwriter_date != null && $underwriter_date < \Carbon\Carbon::now() && ((isset($card->submission->uwr_status) && $card->submission->uwr_status != 'submitted') || !isset($card->submission->uwr_status)):
                    if (Session::get('user')->login_type != 'broker-user') {
                        return 'past-due-uwr';
                    }
                case isset($card->schedule->actual_submission_deadline) && \Carbon\Carbon::parse($card->schedule->actual_submission_deadline) < \Carbon\Carbon::now():
                    if (Session::get('user')->login_type == 'broker-user') {
                        return 'survey-completed';
                    }

                case isset($card->schedule->actual_submission_deadline):
                    return 'survey-date-booked';

                case $card->external_survey_company || (!$card->external_survey_company && isset($card->surveyor)):
                    if (Session::get('user')->login_type != 'broker-user') {
                        return 'assigned';
                    }

                    // no break
                default:
                    $ret = 'backlog';
                    if (Session::get('user')->login_type == 'broker-user') {
                        $ret = 'survey-date-booked';
                    }

                    return $ret;
            }
        }

        if ($type == 'risk-recommendations') {
            // dd($risk_rec_prefix);

            $current_date = isset($card->{$risk_rec_prefix . '_required_by'}) && ($card->{$risk_rec_prefix . '_required_by'} != '') ? \Carbon\Carbon::createFromFormat('d-m-Y', str_replace('/', '-', $card->{$risk_rec_prefix . '_required_by'})) : null;

            //Get Messages for risk recommendation

            switch (true) {
                case isset($card->{$risk_rec_prefix . '_issue_closed'}) && $card->{$risk_rec_prefix . '_issue_closed'} == '1':
                    return 'closed';

                case isset($current_date) && $current_date->lt(\Carbon\Carbon::now()):
                    return 'past-due';

                case (isset($current_date) && $current_date->gte(\Carbon\Carbon::now())) || !isset($current_date):
                    $messages = isset($card->rrm->{$risk_rec_prefix . '_message'}) ? $card->rrm->{$risk_rec_prefix . '_message'} : [];

                    if (isset($messages->sent_at)) {
                        return 'feedback-received';
                    }
                    if (isset($messages) && is_countable($messages) && count($messages)) {
                        foreach ($messages as $message) {
                            if (isset($message->sent_at)) {
                                return 'feedback-received';
                            }
                        }
                    } else {
                        return 'backlog';
                    }

                    // no break
                default:
                    return 'backlog';
            }
        }
    }

    private static function sortColumnCards(&$column, $property, $ascending = 1)
    {
        usort(
            $column['cards'],
            function ($a, $b) use ($property, $ascending) {
                $x = $a->properties[$property];
                $y = $b->properties[$property];
                $v = [-1, 1];

                if ($ascending) {
                    $v = array_reverse($v);
                }

                return ($x != $y)
                ? $v[(int) ($x < $y)]
                : 0;
            }
        );
    }

    private static function getEmptyColumns($type)
    {
        switch ($type) {
            case 'surveys':
                $columns = [
                'broker-user' => [
                    'survey-date-booked' => [
                        'title' => 'Survey Date Booked',
                        'cards' => [],
                    ],
                    'survey-completed' => [
                        'title' => 'Survey Completed',
                        'cards' => [],
                    ],
                    'submitted-csr' => [
                        'title' => 'CSR Submitted',
                        'cards' => [],
                    ],
                    'submitted-uwr' => [
                        'title' => 'UWR Submitted',
                        'cards' => [],
                    ],
                    'underwriter-csr' => [
                        'title' => 'CSR Underwriter',
                        'cards' => [],
                    ],
                    'underwriter-uwr' => [
                        'title' => 'UWR Underwriter',
                        'cards' => [],
                    ],
                    'approved-csr' => [
                        'title' => 'CSR Approved',
                        'cards' => [],
                    ],
                    'approved-uwr' => [
                        'title' => 'UWR Approved',
                        'cards' => [],
                    ],
                ],
                'default' => [
                    'backlog' => [
                        'title' => 'Backlog',
                        'cards' => [],
                    ],
                    'assigned' => [
                        'title' => 'Assigned',
                        'cards' => [],
                    ],

                    'survey-date-booked' => [
                        'title' => 'Survey Date Booked',
                        'cards' => [],
                    ],
                    'draft-report-in-progress-csr' => [
                        'title' => 'Draft Report In Progress (CSR)',
                        'cards' => [],
                    ],
                    'draft-report-in-progress-uwr' => [
                        'title' => 'Draft Report In Progress (UWR)',
                        'cards' => [],
                    ],
                    'submitted-csr' => [
                        'title' => 'CSR Submitted',
                        'cards' => [],
                    ],
                    'submitted-uwr' => [
                        'title' => 'UWR Submitted',
                        'cards' => [],
                    ],
                    'past-due-csr' => [
                        'title' => 'CSR Past Due',
                        'cards' => [],
                    ],
                    'past-due-uwr' => [
                        'title' => 'UWR Past Due',
                        'cards' => [],
                    ],
                    'quality-assurance-csr' => [
                        'title' => 'CSR Quality Assurance',
                        'cards' => [],
                    ],
                    'quality-assurance-uwr' => [
                        'title' => 'UWR Quality Assurance',
                        'cards' => [],
                    ],
                    'underwriter-csr' => [
                        'title' => 'CSR Underwriter',
                        'cards' => [],
                    ],
                    'underwriter-uwr' => [
                        'title' => 'UWR Underwriter',
                        'cards' => [],
                    ],
                    'approved-csr' => [
                        'title' => 'CSR Approved',
                        'cards' => [],
                    ],
                    'approved-uwr' => [
                        'title' => 'UWR Approved',
                        'cards' => [],
                    ],
                ],
                ];

                return Session::get('user')->login_type == 'broker-user' ? $columns['broker-user'] : $columns['default'];
            case 'risk-recommendations':
                return [
                    'backlog' => [
                        'title' => 'Open',
                        'cards' => [],
                    ],
                    'feedback-received' => [
                        'title' => 'Feedback Received',
                        'cards' => [],
                    ],
                    'past-due' => [
                        'title' => 'Overdue',
                        'cards' => [],
                    ],
                    'closed' => [
                        'title' => 'Closed',
                        'cards' => [],
                    ],
                ];

            default:
                return [];
        }
    }

    public function export(Request $request)
    {
        $filename       = 'risk-recommendations-' . date('d-m-Y') . '.xlsx';
        $streamFileData = Api::post('api/v1/risk-recommendations/export', $request->all());
        $headers = [
            'Content-Type'        => 'application/octet-stream',
            'Content-Disposition' => 'attachment; filename=' . $filename,
        ];
        return response($streamFileData, 200, $headers);
    }
    
    public function queueExport(Request $request)
    {
        $data               = $request->all();
        $user               = Session::get('user');
        $data['email']      = $user->email;
        $data['first_name'] = $user->first_name;
        $data['last_name']  = $user->last_name;

        $resource = Api::post('api/v1/risk-recommendations/processExport', $data);
        return response('OK');
    }

    /**
     * @param array $cards
     * @param string|null $postcode
     * @return array
     */
    private static function reduceCards(array $cards, Request $request): array
    {
        $location = $request->get('location');

        $cards['data'] = array_reduce($cards['data'], function ($acc, $card) use ($location, $request) {
            // save postcode before $card columns pass whitelisting
            $tmpLocation = $card['properties']['Location'];
            $tmpLocation['location_name']=$tmpLocation['location_name']??'';
            $tmpLocation['postcode']=$tmpLocation['postcode']??'';
            
            $cardLocation = (new KanbanController($request))
                    ->trimString($tmpLocation['location_name']) . "\n" . $tmpLocation['postcode'];

            // whitelist card properties
            $card['properties'] = [
                'Client' => $card['properties']['Client'],
                'Location Name' => $cardLocation ?? "",
                'Title' => $card['risk_rec_title'] ?? "",
                'SRF' => $card['properties']['SRF'],
                'Survey Type' => $card['properties']['Survey Type'],
                'Survey Date' => $card['properties']['Survey Date'],
                // 'MGA Scheme' => $card['properties']['MGA Scheme'],
                'Required By' => $card['properties']['Required By'],
            ];

            $formTypebasedProperties = [
                'SRF' => $card['properties']['SRF'],
                'Survey Type' => $card['properties']['Survey Type'],
                'Survey Date' => $card['properties']['Survey Date'],
            ];

            if (strpos($card['properties']['SRF'], 'DTR') !== false) {
                $card['form_type'] = 'dtr';
            }

            if (($card['form_type'] ?? '') === 'dtr') {
                $formTypebasedProperties = [
                    'DTR' => $card['properties']['SRF'],
                    'Report Type' => $card['properties']['Survey Type'],
                    'Report Date' => $card['properties']['Survey Date'],
                ];
            }

            // whitelist card properties
            $card['properties'] = array_merge($formTypebasedProperties, [
                'Client' => $card['properties']['Client'],
                'Location Name' => $cardLocation ?? "",
                'Risk Recommendation Title' => $card['risk_rec_title'] ?? "",
                'Required By' => $card['properties']['Required By'],
            ]);

            // if location is in query param, filter by postcode
            if ($location && $location != $cardLocation) {
                return $acc;
            }

            return array_merge($acc, [$card]);
        }, []);
        return $cards;
    }
}
