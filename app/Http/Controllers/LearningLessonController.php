<?php

namespace App\Http\Controllers;

use App\Models\Api;
use Illuminate\Http\Request;
use App\Models\LessonObjects;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Response;

class LearningLessonController extends BaseResourceController
{
    const
    TEMPLATE_PATH = '/learning/lesson',
    API_NAMESPACE = 'learning/lesson',
    RESPOND_TO_AJAX = true;

    public function __construct(Request $request, LessonObjects $lessonObjects)
    {

        BaseController::__construct($request);

        View::share(
            'main_class',
            'learning'
        );

        $this->lessonObjects = $lessonObjects;

        // parent::__construct();
    }

    /**
     * Show all resources
     *
     * @return Response
     */
    public function index($courseId)
    {
        // if the lesson is nested under a course, redirect to the course's edit page
        if ($courseId) {
            Session::reflash();

            return Redirect::route(
                static::getParentRoutePrefix() . '.edit',
                ['course' => $courseId]
            );
        } else {
            // TODO - list all lessons?
            return false;
        }
    }

    public function sort(Request $request, $course_id)
    {

        $rules = array(
            'lessons' => 'required|array'
        );

        $validator = Validator::make($request->all(), $rules);

        if($validator->fails()) {

            return Response::json(array('response' => 'error', 'errors' => $validator->errors()));

        }

        $data = $request->all();
        $api_call = json_decode(Api::put('/api/v1/learning/course/' . $course_id . '/lesson/sort', $data));

        if(isset($api_call->response) && $api_call->response == 'success') {

            return Response::json(array('response' => 'success'));
        }

        return Response::json(array('response' => 'error', 'message' => 'error'));

    }

    public function edit($course_id, $lesson_id)
    {
        if($lesson_id != null) {

            $api_call = json_decode(
                Api::get(
                    static::get_api_uri($lesson_id, 'learning/lesson')
                )
            );

            if(isset($api_call->response) && $api_call->response == 'success') {

                foreach ($api_call->data->pages as $key => $page){

                    if(isset($page->sections)) {
                        foreach ($page->sections as $key_section => $section){

                            if(!is_array($api_call->data->pages[$key]->sections)) {
                                $api_call->data->pages[$key]->sections->$key_section = $this->processSection($section, $course_id, $lesson_id);
                            }else{
                                $api_call->data
                                    ->pages[$key]
                                    ->sections[$key_section] = $this->processSection($section, $course_id, $lesson_id);
                            }


                        }
                    }

                }


                return view('learning.lesson.edit', array('resource' => $api_call->data, 'layout'=>'layout'));

            }

        }

        return Redirect::route('learning.course.edit', array('course' => $course_id))
            ->with('error', 'Lesson does not exist');


    }

    /**
     * duplicate lesson
     *
     * @return Response
     */

    public function duplicate($course_id, $id)
    {
        $api_call = json_decode(Api::get('/api/v1/learning/lesson/' . $id . '/duplicate'));

        if(isset($api_call->response) && $api_call->response == 'success') {
            if(isset($api_call->data->files)) {
                foreach ($api_call->data->files as $key => $file_name) {
                    $this->lessonObjects->duplicateFolder($file_name, $course_id, $id, $api_call->data->id);
                    if(strpos($file_name, 'video-') === 0) {
                        $file_name = str_replace('.mp4', '-00001.png', $file_name);
                        $this->lessonObjects->duplicateFolder($file_name, $course_id, $id, $api_call->data->id);
                        $file_name = str_replace('-00001.png', '', $file_name);
                        $this->lessonObjects->duplicateFolder($file_name, $course_id, $id, $api_call->data->id);
                    }
                }
            }
            //$result = $this->lessonObjects->duplicateFolder($course_id, $id, '100');
            return Redirect::back()->with('success', $api_call->message);
        }
            return Redirect::back()->with('error', 'Error to duplicate lesson.');
    }


    public function show($course_id, $lesson_id)
    {

        $api_call = json_decode(Api::get('/api/v1/learning/lesson/' . $lesson_id));


        if(isset($api_call->response) && $api_call->response == 'success') {

            foreach ($api_call->data->pages as $key => $page){

                if(isset($page->sections)) {
                    foreach ($page->sections as $key_section => $section){
                        if(!is_array($api_call->data->pages[$key]->sections)) {
                            $api_call->data->pages[$key]->sections->$key_section = $this->processSection($section, $course_id, $lesson_id);
                        }else{
                            $api_call->data->pages[$key]->sections[$key_section] = $this->processSection($section, $course_id, $lesson_id);

                        }

                    }
                }


            }


            return view(
                'learning.lesson.show', array(
                'resource' => $api_call->data,
                'layout'=>'layout'
                )
            );

        }

        return Redirect::route('learning.course.index', array('course_id' => $course_id))
            ->with('error', 'Lesson does not exist');

    }


    /**
     * Create Lesson
     *
     * @param  null $course_id
     * @return mixed
     */
    public function create(Request $request, $course_id = null)
    {

        $data = array();

        if($course_id != null) {
            $course = json_decode(
                Api::get(
                    static::get_api_uri($course_id, 'learning/course')
                )
            );

            if(isset($course->response) && $course->response == 'success') {
                $data['course'] = $course->data;
                if(isset($course->data->lessons)) {
                    $lessons = Array();
                    foreach($course->data->lessons as $lesson){
                        $lessons[$lesson->id] = $lesson->title;
                    }
                    $data['lessons'] = $lessons;
                }
            }
        }

        return view(
            static::TEMPLATE_PATH . '/create',
            array_merge(
                $data,
                static::getRouteParams($request, 'create'),
                ['layout'=>'modal']
            )
        );
    }



    /**
     * Get additional view parameters for a specific view (index/create/edit)
     *
     * @param  string $view
     * @return array of additional parameters
     */
    public function getRouteParams(Request $request, $view, array $params = [])
    {

        switch($view) {
        case 'create':
            $params = array_merge(
                $params, [
                'options' => [
                    'method'  => [
                        'new'       => 'New Lesson',
                        'duplicate' => 'Duplicate Existing Lesson',
                    ],
                    'sources' => [],
                ],
                'route' => (object)[
                    'store'  => static::getRouteArray('store'),
                ]
                    ]
            );

            break;

        case 'edit':
            $params['route'] = (object)[
                'update'  => static::getRouteArray('update'),
                'destroy' => static::getRouteArray('destroy'),
            ];
            break;
        }


        return $params;
    }

    public function viewLesson($lesson_id)
    {
        $lesson = json_decode(Api::get('api/v1/learning/lesson/' . $lesson_id . '/view'));

        return view(
            static::TEMPLATE_PATH . '/view', ['lesson' => $lesson->data]
        );
    }
}
