<?php

namespace App\Http\Controllers;

use App\Helpers\RiskGradingHelper;
use App\Models\Api;
use App\Models\User;
use Illuminate\Http\Request;
use App\Models\Documents;
use App\Models\FileUpload;
use App\Models\CsrReport;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Response;
use App\Models\Helper;
use App\Services\CacheContent\GetBrokerService;
use App\Services\CacheContent\GetMgaSchemeService;
use App\Services\CacheContent\GetOrganisationPolicyDocs;
use App\Services\CacheContent\GetOrganisationRiskGradingOverview;
use App\Services\CacheContent\GetOrganisationService;
use App\Services\CacheContent\GetOrgRiskGradingData;
use App\Services\CacheContent\OptionListService;
use App\Services\SendSqsMessageService;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Cache;
use \Exception;
use App\Traits\HelperTrait;
use Carbon\Carbon;
use Illuminate\Support\Facades\Artisan;

class OrganisationController extends BaseController
{
    use HelperTrait;

    //TEMPLATE PATH
    const TEMPLATE_PATH = '/organisation';

    //ROUTE PREFIX
    const ROUTE_PREFIX = 'organisation';

    const ALERT_LEVEL_MAPPING = [
        1 => 'Prepare',
        2 => 'Act',
        3 => 'Severe Flood Warning',
    ];

    public function __construct(Request $request, FileUpload $fileUpload, CsrReport $csrReport, Documents $doc)
    {
        parent::__construct($request);

        $this->files = $fileUpload;
        $this->documents = $doc;
        $this->csrReport = $csrReport;
    }

    public function allowForUser(User $user): bool
    {
        return in_array($user->login_type, ['risk-control', 'underwriter', 'risk-engineer', 'aspen-user', 'broker-user']);
    }

    //INDEX
    public function index(Request $request)
    {
        $page       = $request->get('page');
        $search     = $request->get('search');
        $page       = isset($page) ? $request->get('page') : 1;
        $limit      = $request->has('limit') ? $request->limit : 10;
        $filter_by  = $request->get('filter_by');
        $keyaccount = $request->get('keyaccount');

        $sort = $request->get('sort');
        $col = $request->get('col');
        $sortstring = '&col=' . urlencode($col) . '&sort=' . urlencode($sort) . '&keyaccount=' . $keyaccount . '&withoutAppends=true';
        $sortstringfirst = '?col=' . urlencode($col) . '&sort=' . urlencode($sort) . '&keyaccount=' . $keyaccount;
        $requestType = $request->get('type');
        $requestTypeString = $requestType ? '&type=' . urlencode($requestType) . '&user_id=' . Session::get('user')->id : '';

        if (isset($search) && $search != '') {
            if (Session::get('user')->login_type == 'aspen-user') {
                if ($request->has('branch')) {
                    $response = json_decode(Api::get('api/v1/organisation/all/' . $page . '/' . $limit . '?search=' . urlencode($search) . '&branch_type=aspen&branch=' . $request->get('branch') . $sortstring));
                } else {
                    $response = json_decode(Api::get('api/v1/organisation/all/' . $page . '/' . $limit . '?search=' . urlencode($search) . '&branch_type=aspen' . $sortstring));
                }
            } elseif (Session::get('user')->login_type == 'broker-user') {
                $response = json_decode(Api::get('api/v1/organisation/broker/' . Session::get('user')->broker_id . '/' . $page . '/' . $limit . '?search=' . urlencode($search) . $sortstring . $requestTypeString));
            } else {
                if ($request->has('branch')) {
                    $response = json_decode(Api::get('api/v1/organisation/all/' . $page . '/' . $limit . '?search=' . urlencode($search) . '&branch=' . $request->get('branch') . $sortstring));
                } elseif ($request->has('mga_scheme')) {
                    $response = json_decode(Api::get('api/v1/organisation/broker/' . Session::get('user')->broker_id . '/0/99999999?mga_scheme=' . $request->get('mga_scheme') . $sortstring));
                } else {
                    $response = json_decode(Api::get('api/v1/organisation/all/' . $page . '/' . $limit . '?search=' . urlencode($search) . $sortstring));
                }
            }
        } else {
            $baseUrl = 'api/v1/organisation/all/' . $page . '/' . $limit;
            if (Session::get('user')->login_type == 'aspen-user') {
                $response = json_decode(Api::get($baseUrl . 'api/v1/organisation/all/1/100000?branch_type=aspen'));
            } elseif (Session::get('user')->login_type == 'broker-user') {
                if ($request->has('mga_scheme')) {
                    $response = json_decode(Api::get('api/v1/organisation/broker/' . Session::get('user')->broker_id . '/0/99999999?mga_scheme=' . $request->get('mga_scheme') . $sortstring . $requestTypeString));
                } else {
                    $response = json_decode(Api::get('api/v1/organisation/broker/' . Session::get('user')->broker_id . '/' . $page . '/' . $limit . $sortstringfirst . $requestTypeString));
                }
            } elseif ($request->has('branch')) {
                $response = json_decode(Api::get($baseUrl . '?branch=' . urlencode($request->get('branch')) . $sortstring));
            } elseif ($request->has('mga_scheme')) {
                $response = json_decode(Api::get($baseUrl . '?mga_scheme=' . urlencode($request->get('mga_scheme')) . $sortstring));
            } elseif ($request->has('broker_id')) {
                $response = json_decode(Api::get('api/v1/organisation/broker/' . urlencode($request->get('broker_id')) . '/' . $page . '/' . $limit . $sortstringfirst));
            } elseif ($request->has('underwriter')) {
                $response = json_decode(Api::get($baseUrl . '?underwriter=' . urlencode($request->get('underwriter')) . $sortstring));
            } elseif ($request->has('riskengineer')) {
                $response = json_decode(Api::get($baseUrl . '?riskengineer=' . urlencode($request->get('riskengineer')) . $sortstring));
            } elseif ($request->has('filter_by')) {
                $response = json_decode(Api::get($baseUrl . '?filter_by=' . urlencode($request->get('filter_by')) . $sortstring));
            } else {
                $response = json_decode(Api::get($baseUrl  . '?' . $sortstring));
            }
        }

        $data = array(
            'organisations' => $response->data,
            'total' => $response->total,
            'search' => $search,
            'filter_by' => $filter_by,
            'limit' => $limit,
            'page' => $page,
            'link' => 'organisation.index',
        );

        $filter_bys = array(
            'no-mga' => 'No MGA',
            'mga-only' => 'MGA Only',
        );

        if (Session::get('user')->login_type == 'broker-user') {
            $mga_schemes = json_decode(Api::get('/api/v1/mga-schemes/all?broker_id=' . Session::get('user')->broker_id . '&getNameAndIdOnly=true'));
        } else {
            $mga_schemes = json_decode(Api::get('/api/v1/mga-schemes/all' . '?getNameAndIdOnly=true'));
        }

        if ($request->ajax()) {
            return $data;
        }

        $brokers = json_decode(Api::get('api/v1/brokers/options'));

        $options = json_decode(Api::get(
            static::get_api_uri('options-multiple', 'liberty-users') . '?roles=underwriter,risk-engineer'
        ));

        $underwriters = $options->underwriter ?? [];
        $riskEngineer = 'risk-engineer';
        $riskEngineers = $options->{$riskEngineer} ?? [];

        if ($response->response == 'success') {
            $viewBag=array(
                'organisations' => $response->data,
                'branches' => $response->branches,
                'total' => $response->total,
                'search' => $search,
                'filter_by' => $filter_by,
                'limit' => $limit,
                'page' => $page,
                'link' => 'organisation.index',
                'filter_bys' => $filter_bys,
                'mga_schemes' => $mga_schemes->data,
                'underwriters' => $underwriters,
                'risk_engineers' => $riskEngineers,
                'brokers' => $brokers,
            );

            return view(
                static::TEMPLATE_PATH . '/index',$viewBag);
        }
    }

    public function getRiskGradingDataForOrganisation($org_id, $cron=false)
    {
        $rrTracker = $this->csrReport->getRiskRecommendationTracker($org_id, $cron);

        $rr_tyre = [];
        $rr_status = [];
        $rr_recommendation = [];
        $rr_tyre_label = [];

        // Risk Recommendation Tyre statistics

        if (isset($rrTracker) && isset($rrTracker['RR_tyre'])) {
            $rr_tyre = $rrTracker['RR_tyre'];
            $rr_tyre = array_count_values($rr_tyre);
            $total = array_sum($rr_tyre);
            $running_percentage = $rr_count = 0;

            foreach ($rr_tyre as $rr => $tyre) {
                if ($rr_count === count($rr_tyre) - 1) {
                    $rr_tyre[$rr] = 100 - $running_percentage;
                } else {
                    $percent = round(($tyre / $total) * 100);
                    $rr_tyre[$rr] = $percent;
                    $running_percentage += $percent;
                }
                ++$rr_count;
            }
        }

        // Risk Recommendation Tyre Label

        if (isset($rrTracker) && isset($rrTracker['RR_tyre_label'])) {
            $labels = (array)$rrTracker['RR_tyre_label'];
            foreach ($labels as $key => $value) {
                if (isset($labels[$key])) {
                    $rr_tyre_label[$key] = $labels[$key];
                }
            }
        }


        // Open Close statistics
        if (isset($rrTracker) && isset($rrTracker['RR_status'])) {
            $status = [];

            foreach ($rrTracker['RR_status'] as $element) {
                $status[$this->trimString($element[2][0]) . "\n" . $element[2][1]][] = $element[1];
            }
        }

        $submissionCards = json_decode(
            Api::get('api/v1/risk-recommendations/cards?limit=1000&organisation=' . $org_id),
            true
        );
        
        foreach ($submissionCards as $column => $cards) {
            foreach ($cards['data'] as $card) {
                $cardLocation = $card['properties']['Location'];
                $location = $cardLocation ? ($this->trimString($cardLocation['location_name']) . "\n" . $cardLocation['postcode']):'-';
                $col = $card['column'] === "closed" ? "closed" : "open";
                if (empty($rr_status[$location])) {
                    // instantiate
                    $rr_status[$location] = [
                        "open" => 0,
                        "closed" => 0
                    ];
                }
                $rr_status[$location][$col]++;
            }
        }

        uksort(
            $rr_status,
            function ($a, $b) {
                $aa = trim(substr($a, 3));
                $bb = trim(substr($b, 3));

                return ($aa > $bb) ? -1 : 1;
            }
        );

        // survey status statistics
        $possible_statistics = [
            'closed' => 0,
            'open' => 0,
            'greater_than_30' => 0,
            'less_than_30' => 0,
        ];

        $survey_statistics = count($rrTracker) > 0
            ? array_merge($possible_statistics, array_count_values($rrTracker['survey_status']))
            : $possible_statistics;

        // recommendation statistics
        if (isset($rrTracker) && isset($rrTracker['RR_recommendation'])) {
            $rr_recommendation = $rrTracker['RR_recommendation'];
            $rr_recommendation = array_count_values($rr_recommendation);

            ksort($rr_recommendation);

            uasort(
                $rr_recommendation,
                function ($a, $b) {
                    return ($a > $b) ? -1 : 1;
                }
            );

            $rr_recommendation = array_slice($rr_recommendation, 0, 5, true);
            $rrkeys = array_slice($rr_recommendation, 0, 5, true);
        }

        $survey_statistics=[];
        $tracker_count = json_decode(Api::get('api/v1/risk-recommendations/cards?organisation='.$org_id.'&limit=1000'));
        foreach($tracker_count as $key=>$value){
            $survey_statistics[$key]=count($value->data);
        }

        $csrData = $this->csrReport->getCSRReport($org_id);

        $csrData = isset($csrData['data']) ? $csrData['data'] : null;

        $out = [];
        $csrTitles = [];

        foreach ($csrData as $dk => $dv) {
            foreach ($dv as $dvk => $dvv) {
                $dvv = str_replace('SRF', 'CSR', $dvv);
                //$dvv="<span>".$dvv."</span>";

                if ($dvk == 0) {
                    if (strpos($dvv, 'Risk Control') !== false) {
                        $dvv = str_replace('Risk Control', '', $dvv);
                    }

                    $csrTitles[$dvk][$dk] = trim($dvv);
                } else {
                    $out[$dvk][$dk] = $dvv;
                }
            }
        }

        uasort(
            $out,
            function ($a, $b) {
                $ab = preg_match_all('!\d+!', $a[0], $matchesa);
                $bb = preg_match_all('!\d+!', $b[0], $matchesb);

                return ($matchesa[0] > $matchesb[0]) ? -1 : 1;
            }
        );

        $organisationData = array(
            'csrReport'               => $out,
            'csrTitles'               => $csrTitles,
            'rrTracker'               => $survey_statistics,
            'rr_tyre_label'           => $rr_tyre_label,
            'rrTyre'                  => $rr_tyre,
            'rr_status'               => $rr_status,
            'rr_srr_recommendation'   => $rr_recommendation,
            'rr_loss_estimate'        => isset($rrTracker['RR_loss_estimate']) ? ($rrTracker['RR_loss_estimate']) : [],
        );

        return $organisationData;
    }

    public function getOrganisationDetailsForDashboard($id, $fullDetail = true, $cron = false)
    {
        $organisationData=$this->getSetCache("organisation-dashboard-data-admin-{$id}");
        if(!$organisationData){
            $data = json_decode(Api::get('api/v1/organisation/' . $id));
            $organisation_policies = [];

            if (isset($data->data->policy_numbers)) {
                $organisationPolicies = array_map(
                    function ($policies) {
                        return $policies->type;
                    },
                    $data->data->policy_numbers
                );

                $organisation_policies = array_map(
                    function ($policies) {
                        return strtolower($policies->name);
                    },
                    $organisationPolicies
                );
            }

            $allproducts = $this->getCmsProducts();

            if (isset($data->data->products) && !empty($data->data->products)) {
                foreach ($data->data->products as $product) {
                    $products = array_filter(
                        $allproducts,
                        function ($prod) use ($product) {
                            return $product->slug == $prod->slug;
                        }
                    );
                    $product->name = array_values($products)[0]->name;
                }

                $data->data->display_products = array_filter(
                    $data->data->products,
                    function ($prod) use ($organisation_policies) {
                        return !in_array($prod->slug, $organisation_policies);
                    }
                );
            }

            if ($data->response == 'error') {
                return Redirect::route('organisation.index')
                    ->with('error', 'This organisation does not exist.');
            }

            if (!$cron) {
                if (Session::get('user')->login_type == 'aspen-user') {
                    if (!isset($data->data->liberty_branch->is_aspen) || $data->data->liberty_branch->is_aspen != '1') {
                        return Response::make('Unauthorized', 401);
                    }
                }
            }

            $rrTracker = $this->csrReport->getRiskRecommendationTracker($id, $cron);

            $rr_tyre = [];
            $rr_status = [];
            $rr_recommendation = [];
            $rr_tyre_label = [];

            // Risk Recommendation Tyre statistics

            if (isset($rrTracker) && isset($rrTracker['RR_tyre'])) {
                $rr_tyre = $rrTracker['RR_tyre'];
                $rr_tyre = array_count_values($rr_tyre);
                $total = array_sum($rr_tyre);
                $running_percentage = $rr_count = 0;

                foreach ($rr_tyre as $rr => $tyre) {
                    if ($rr_count === count($rr_tyre) - 1) {
                        $rr_tyre[$rr] = 100 - $running_percentage;
                    } else {
                        $percent = round(($tyre / $total) * 100);
                        $rr_tyre[$rr] = $percent;
                        $running_percentage += $percent;
                    }
                    ++$rr_count;
                }
            }

            // Risk Recommendation Tyre Label

            if (isset($rrTracker) && isset($rrTracker['RR_tyre_label'])) {
                $labels = (array)$rrTracker['RR_tyre_label'];
                foreach ($labels as $key => $value) {
                    if (isset($labels[$key])) {
                        $rr_tyre_label[$key] = $labels[$key];
                    }
                }
            }


            // Open Close statistics
            if (isset($rrTracker) && isset($rrTracker['RR_status'])) {
                $status = [];

                foreach ($rrTracker['RR_status'] as $element) {
                    $status[$this->trimString($element[2][0]) . "\n" . $element[2][1]][] = $element[1];
                }

                $rr_status = array_map(
                    function ($v) {
                        return [
                            'open' => count($v) - array_sum($v),
                            'closed' => array_sum($v),
                        ];
                    },
                    $status
                );
            }

            uksort(
                $rr_status,
                function ($a, $b) {
                    $aa = trim(substr($a, 3));
                    $bb = trim(substr($b, 3));

                    return ($aa > $bb) ? -1 : 1;
                }
            );

            // survey status statistics
            $possible_statistics = [
                'closed' => 0,
                'open' => 0,
                'greater_than_30' => 0,
                'less_than_30' => 0,
            ];

            $survey_statistics = count($rrTracker) > 0
                ? array_merge($possible_statistics, array_count_values($rrTracker['survey_status']))
                : $possible_statistics;

            // recommendation statistics
            if (isset($rrTracker) && isset($rrTracker['RR_recommendation'])) {
                $rr_recommendation = $rrTracker['RR_recommendation'];
                $rr_recommendation = array_count_values($rr_recommendation);

                ksort($rr_recommendation);

                uasort(
                    $rr_recommendation,
                    function ($a, $b) {
                        return ($a > $b) ? -1 : 1;
                    }
                );

                $rr_recommendation = array_slice($rr_recommendation, 0, 5, true);
                $rrkeys = array_slice($rr_recommendation, 0, 5, true);
            }

            $survey_statistics=[];
            $tracker_count = json_decode(Api::get('api/v1/risk-recommendations/cards?organisation='.$id.'&limit=1000'));
            foreach($tracker_count as $key=>$value){
                $survey_statistics[$key]=count($value->data);
            }

            if (!$fullDetail) {
                $organisationData = array(
                    'organisationName' => $data->data->name,
                    'rrTracker' => $survey_statistics,
                    'rrTyre' => $rr_tyre,
                    'rr_tyre_label' => $rr_tyre_label,
                    'rr_status' => $rr_status,
                    'rr_srr_recommendation' => $rr_recommendation,
                    'scheme' => isset($data->data->mga_scheme_name) ? $data->data->mga_scheme_name : null,
                );
                return $organisationData;
            }

            $csrData = $this->csrReport->getCSRReport($id);

            $csrData = isset($csrData['data']) ? $csrData['data'] : null;

            $out = [];
            $csrTitles = [];

            foreach ($csrData as $dk => $dv) {
                foreach ($dv as $dvk => $dvv) {
                    $dvv = str_replace('SRF', 'CSR', $dvv);
                    //$dvv="<span>".$dvv."</span>";

                    if ($dvk == 0) {
                        if (strpos($dvv, 'Risk Control') !== false) {
                            $dvv = str_replace('Risk Control', '', $dvv);
                        }

                        $csrTitles[$dvk][$dk] = trim($dvv);
                    } else {
                        $out[$dvk][$dk] = $dvv;
                    }
                }
            }

            uasort(
                $out,
                function ($a, $b) {
                    $ab = preg_match_all('!\d+!', $a[0], $matchesa);
                    $bb = preg_match_all('!\d+!', $b[0], $matchesb);

                    return ($matchesa[0] > $matchesb[0]) ? -1 : 1;
                }
            );

            $organisationDetails = json_decode(Api::get('/api/v1/rhs-organisations/name/' . $id));

            $documents = json_decode(Api::get('/api/v1/document/organisation/' . $id));
            $surveys = json_decode(Api::get('/api/v1/surveys/all?organisation_id=' . $id));

            $links = json_decode(Api::get('api/v1/link/organisation/' . $id));

            $reports = json_decode(Api::get('api/v1/document/report/organisation/' . $id));

            // aspen requests
            $requests = json_decode(Api::get('/api/v1/aspen/organisation/' . $id))->data;

            // aspen documents
            $aspenDocuments = json_decode(Api::get('/api/v1/aspen-documents/organisation/' . $id))->data;

            $notes = json_decode(Api::get('api/v1/organisation/' . $id . '/notes'));

            $claims = json_decode(Api::get('api/v1/organisation/' . $id . '/claims'));

            $contacts = json_decode(Api::get('api/v1/organisation/' . $id . '/contacts'));

            if (!is_null($data->data->logo) && $data->data->logo != '' && $data->data->logo != 'none') {
                $data->data->image_url = $this->files->link($data->data->logo);
            } else {
                $data->data->image_url = '/img/dummy/logo-placeholder.png';
            }

            $options = ['A' => 'blue', 'B' => 'green', 'C' => 'yellow', 'D' => 'orange', 'E' => 'red'];
            $bgcolor = ['blue' => '#31B6FF', 'green' => '#90EE90', 'yellow' => '#feffeb', 'orange' => '#FDDFBB', 'red' => '#FFE5E5'];

            foreach ($options as $key => $value) {
                if ($data->data->risk_grading == $value) {
                    $data->data->risk_grading = (object) ['code' => $key, 'color' => $value, 'background' => $bgcolor[$value]];
                }
            }

            // Other lines of business
            $otherLinesOfBusiness = [];
            $organisation = isset($data->data) ? $data->data : null;
            if (isset($organisation->products) && count($organisation->products) > 0) {
                foreach ($organisation->products as $product) {
                    if (!empty($product->loss_ratio)) {
                        $otherLinesOfBusiness[] = [
                            'name' => $product->name,
                            'loss_ratio' => $product->loss_ratio
                        ];
                    }
                }
            }

            $organisationData = array(
                'organisationDetails'     => $organisationDetails->data,
                'organisation'            => $organisation,
                'documents'               => isset($documents->data) ? $documents->data : null,
                'surveys'                 => isset($surveys->data) ? $surveys->data : null,
                'links'                   => isset($links->data) ? $links->data : null,
                'notes'                   => isset($notes->data) ? $notes->data : null,
                'claims'                  => isset($claims->data) ? $claims->data : null,
                'contacts'                => isset($contacts->data) ? $contacts->data : null,
                'requests'                => isset($requests) ? $requests : null,
                'aspenDocuments'          => isset($aspenDocuments) ? $aspenDocuments : null,
                'reports'                 => isset($reports->data) ? $reports->data : null,
                'csrReport'               => $out,
                'csrTitles'               => $csrTitles,
                'rrTracker'               => $survey_statistics,
                'rr_tyre_label'           => $rr_tyre_label,
                'rrTyre'                  => $rr_tyre,
                'rr_status'               => $rr_status,
                'rr_srr_recommendation'   => $rr_recommendation,
                'rr_loss_estimate'        => isset($rrTracker['RR_loss_estimate']) ? ($rrTracker['RR_loss_estimate']) : [],
                'other_lines_of_business' => $otherLinesOfBusiness

            );
            $this->getSetCache("organisation-dashboard-data-admin-{$id}",$organisationData);
        }

        return $organisationData;
    }

    // Show
    public function show($id)
    {
        $login_type   = Session::get('user')->login_type;
        if ($login_type === 'broker-user') {
            $accessCheck = array_merge(Session::get('NON_MGA_USER_ACCESS'), Session::get('MGA_USER_ACCESS'), Session::get('assigned_organisations'));
            $hasAccess = in_array($id, $accessCheck);
            if (!$hasAccess) {
                return Redirect::route('organisation.index', ['type' => 'self'])
                    ->with('error', 'This organisation does not exist.');
            }
        }

        $user_id=Session::get('user')->id;

        // Check for previsico UI notification
        $data['organisation_id'] = $id;
        $data['liberty_user_id'] = $user_id;

        $cacheKey="previsico-notification-{$user_id}";
        $previsicoHasUiNotifications=$this->getSetCache($cacheKey);
        if(!$previsicoHasUiNotifications){
            $previsicoHasUiNotifications = json_decode(Api::post('/api/v1/liberty-users/get-previsico-ui-notif', $data));
            $hasNotification=$previsicoHasUiNotifications?'yes':'no';
            $this->getSetCache($cacheKey,$hasNotification);
        }
        $previsicoHasUiNotifications=$previsicoHasUiNotifications=='yes'?true:false;

        $cacheKey="organisation-contacts-for-organisation-{$id}";
        $contacts=$this->getSetCache($cacheKey);
        if(!$contacts){
            $contacts = json_decode(Api::get('api/v1/organisation/' . $id . '/contacts'));
            $this->getSetCache($cacheKey,$contacts);
        }

        $organisation = GetOrganisationService::get($id, true);
        if (empty($organisation)) {
            return abort(404);
        }
        $organisation = $this->overrideOrganisationAssets($organisation);

        $OrgViewBag=[
            'has_previsico_ui_notification' => $previsicoHasUiNotifications,
            'organisation'            => $organisation,
            'contacts'                => isset($contacts->data) ? $contacts->data : null,
            'other_lines_of_business' => $organisation->otherLinesOfBusiness,
        ];

        return view(static::TEMPLATE_PATH . '/show',$OrgViewBag);
    }

    public function orgCacheRebuild($id, $forceReCache = false)
    {
        $cacheKey="policy-data-for-organisation-{$id}";
        $policyData=$this->getSetCache($cacheKey);
        if(!$policyData){
            $policy_doc_types = json_decode(Api::get('/api/v1/document-policy/all'));
            $documents = json_decode(Api::get('/api/v1/document/policy/' . $id));
            $policy_types     = json_decode(Api::get('api/v1/policy-types/all'));

            if (isset($policy_doc_types->data) && isset($documents)) {
                $policy_docs = $documents;
                $policy_doc_types = $policy_doc_types->data;
            }
            $policy_docs      = isset($policy_docs) ? $policy_docs : [];
            $policy_doc_types = isset($policy_doc_types) ? $policy_doc_types : [];
            $policyData=(object)['policy_docs' => $policy_docs,'policy_doc_types' => $policy_doc_types,'policy_types' => $policy_types->data];
            $this->getSetCache($cacheKey,$policyData);
        }

        $cacheKey="surveys-data-for-organisation-{$id}";
        $surveys=$this->getSetCache($cacheKey);
        if(!$surveys){
            $surveys = json_decode(Api::get('/api/v1/surveys/all/1/10?organisation_id=' . $id));
            $this->getSetCache($cacheKey,$surveys);
        }

        $cacheKey="account-documents-for-organisation-{$id}";
        $accountDocuments=$this->getSetCache($cacheKey);
        if(!$accountDocuments){
            $accountDocuments = json_decode(Api::get('/api/v1/account-documents/all?organisation_id=' . $id))->data;
            foreach ($accountDocuments as &$accountDocument) {
                $cloudpath = $accountDocument->organisation_id . '/' . $accountDocument->cloudname . '/' . $accountDocument->filename;
                $accountDocument->download = $this->files->link($cloudpath, '2 hours');
            }
            $this->getSetCache($cacheKey,$accountDocuments);
        }

        $cacheKey="organisation-report-for-organisation-{$id}";
        $organisationReports=$this->getSetCache($cacheKey);
        if(!$organisationReports){
            $organisationReports = json_decode(Api::get('/api/v1/organisation/' . $id . '/reports/all'))->data;
            foreach ($organisationReports as &$organisationReport) {
                $cloudpath = $organisationReport->organisation_id . '/' . $organisationReport->cloudname . '/' . $organisationReport->filename;
                $organisationReport->download = $this->files->link($cloudpath, '2 hours');
            }
            $this->getSetCache($cacheKey,$organisationReports);
        }

        $cacheKey="risk-grading-overview-for-organisation-{$id}";
        $riskGradingOverview=$this->getSetCache($cacheKey);
        if(!$riskGradingOverview){
            $riskGradingOverview = json_decode(Api::get('/api/v1/standard-risk/attributes/overview/' . $id));
            $this->getSetCache($cacheKey,$riskGradingOverview);
        }

        $cacheKey="organisation-rhs-details-for-organisation-{$id}";
        $organisationDetails=$this->getSetCache($cacheKey);
        if(!$organisationDetails){
            $organisationDetails = json_decode(Api::get('/api/v1/rhs-organisations/name/' . $id));
            $this->getSetCache($cacheKey,$organisationDetails);
        }

        $cacheKey="organisation-documents-for-organisation-{$id}";
        $documents=$this->getSetCache($cacheKey);
        if(!$documents){
            $documents = json_decode(Api::get('/api/v1/document/organisation/' . $id));
            $this->getSetCache($cacheKey,$documents);
        }

        $cacheKey="organisation-links-for-organisation-{$id}";
        $links=$this->getSetCache($cacheKey);
        if(!$links){
            $links = json_decode(Api::get('api/v1/link/organisation/' . $id));
            $this->getSetCache($cacheKey,$links);
        }

        $cacheKey="organisation-notes-for-organisation-{$id}";
        $notes=$this->getSetCache($cacheKey);
        if(!$notes){
            $notes = json_decode(Api::get('api/v1/organisation/' . $id . '/notes'));
            $this->getSetCache($cacheKey,$notes);
        }

        // @NOTE: Cannot find the collection of this API
        // $cacheKey="organisation-claims-for-organisation-{$id}";
        // $claims=$this->getSetCache($cacheKey);
        // if(!$claims){
        //     $claims = json_decode(Api::get('api/v1/organisation/' . $id . '/claims')); // -> @NOTE: Claims
        //     $this->getSetCache($cacheKey,$claims);
        // }

        $cacheKey="organisation-contacts-for-organisation-{$id}";
        $contacts=$this->getSetCache($cacheKey);
        if(!$contacts){
            $contacts = json_decode(Api::get('api/v1/organisation/' . $id . '/contacts'));
            $this->getSetCache($cacheKey,$contacts);
        }

        $cacheKey="organisation-reports-second-for-organisation-{$id}";
        $reports=$this->getSetCache($cacheKey);
        if(!$reports){
            $reports = json_decode(Api::get('api/v1/document/report/organisation/' . $id));
            $this->getSetCache($cacheKey,$reports);
        }

        $cacheKey="organisation-requests-for-organisation-{$id}";
        $requests=$this->getSetCache($cacheKey);
        if(!$requests){
            $requests = json_decode(Api::get('/api/v1/aspen/organisation/' . $id))->data;
            $this->getSetCache($cacheKey,$requests);
        }

        // @NOTE: It seems this is not use
        // $cacheKey="organisation-aspen-documents-for-organisation-{$id}";
        // $aspenDocuments=$this->getSetCache($cacheKey);
        // if(!$aspenDocuments){
        //     $aspenDocuments = json_decode(Api::get('/api/v1/aspen-documents/organisation/' . $id))->data;
        //     $this->getSetCache($cacheKey,$aspenDocuments);
        // }

        $cacheKey="organisation-cms-all-products-organisation-{$id}";
        $allproducts=$this->getSetCache($cacheKey);
        if(!$allproducts){
            $allproducts = $this->getCmsProducts();
            $this->getSetCache($cacheKey,$allproducts);
        }

        $cacheKey="organisation-details-for-organisation-{$id}";
        $organisation=$this->getSetCache($cacheKey);
        if(!$organisation){
            $organisation = json_decode(Api::get('api/v1/organisation/' . $id));
            if (!is_null($organisation->data->logo) && $organisation->data->logo != '' && $organisation->data->logo != 'none') {
                $organisation->data->image_url = $this->files->link($organisation->data->logo);
            } else {
                $organisation->data->image_url = '/img/dummy/logo-placeholder.png';
            }

            $organisation_policies = [];

            if (isset($organisation->data->policy_numbers)) {
                $organisationPolicies = array_map(
                    function ($policies) {
                        return $policies->type;
                    },
                    $organisation->data->policy_numbers
                );

                $organisation_policies = array_map(
                    function ($policies) {
                        return strtolower($policies->name);
                    },
                    $organisationPolicies
                );
            }

            if (isset($organisation->data->products) && !empty($organisation->data->products)) {
                foreach ($organisation->data->products as $product) {
                    $products = array_filter(
                        $allproducts,
                        function ($prod) use ($product) {
                            return $product->slug == $prod->slug;
                        }
                    );
                    $product->name = array_values($products)[0]->name;
                }

                $organisation->data->display_products = array_filter(
                    $organisation->data->products,
                    function ($prod) use ($organisation_policies) {
                        return !in_array($prod->slug, $organisation_policies);
                    }
                );
            }

            $options = $this->getConstants('gradingColorOptions');
            $bgcolor = $this->getConstants('bgColorOptions');

            foreach ($options as $key => $value) {
                if ($organisation->data->risk_grading == $value) {
                    $organisation->data->risk_grading = (object) ['code' => $key, 'color' => $value, 'background' => $bgcolor[$value]];
                }
            }

            $otherLinesOfBusiness=[];
            $organisation = isset($organisation->data) ? $organisation->data : null;
            if (isset($organisation->products) && count($organisation->products) > 0) {
                foreach ($organisation->products as $product) {
                    if (!empty($product->loss_ratio)) {
                        $otherLinesOfBusiness[] = [
                            'name' => $product->name,
                            'loss_ratio' => $product->loss_ratio
                        ];
                    }
                }
            }

            $organisation->otherLinesOfBusiness=$otherLinesOfBusiness;
            $this->getSetCache($cacheKey,$organisation);
        }

        $cacheKey="organisation-risk-grading-data-for-organisation-{$id}";
        $riskGradingData= $forceReCache ? null : $this->getSetCache($cacheKey);
        if(!$riskGradingData){
            $riskGradingData=$this->getRiskGradingDataForOrganisation($id, true);
            $this->getSetCache($cacheKey,$riskGradingData);
        }
    }

    // Create
    public function create()
    {
        //TODO: implements guard checks to broker user with no schemes
        $sectors = json_decode(Api::get('api/v1/sector/all'));
        $policy_types = json_decode(Api::get('api/v1/policy-types/all'));
        $branch = json_decode(Api::get('/api/v1/liberty-branches/options'));
        $trades = json_decode(Api::get('/api/v1/trades/options/0'));
        $themes = json_decode(Api::get('/api/v1/themes/options'));

        if (Session::get('user')->login_type == 'broker-user') {
            $broker_organisations = json_decode(Api::get('/api/v1/brokers/all?broker_org=' . Session::get('user')->broker_id));
            if (BrokerUserController::getBrokerSchemeCount() <= 0) {
                return Response::make('Unauthorized', 401);
            }

            if (isset(Session::get('user')->broker_id) 
                && !in_array(Session::get('user')->broker_id, config('app.allowed_broker_to_create_org'))) {
                return Response::make('Unauthorized', 401);
            }
        } else {
            $broker_organisations = json_decode(Api::get('api/v1/brokers/all/0/0'));
        }

        if (Session::get('user')->login_type == 'broker-user') {
            $mga_schemes = json_decode(Api::get('/api/v1/mga-schemes/all?broker_id=' . Session::get('user')->broker_id));
        } else {
            $mga_schemes = json_decode(Api::get('/api/v1/mga-schemes/all'));
        }

        $trade_output = [];

        foreach ($trades as $key => $value) {
            $trade_output[$key] = $value;
        }

        $jsonSectors = json_decode(Api::get('api/v1/community/get-sector'))->data;
        $communitySectors = $jsonSectors ? json_decode($jsonSectors)->data : [];
        $arrSectors = [];
        foreach ($communitySectors as $communitySector) {
            $arrSectors[$communitySector->_id] = $communitySector->name;
        }
        uasort(
            $arrSectors,
            function ($lhs, $rhs) {
                return strcmp($lhs, $rhs);
            }
        );

        if ($sectors->response == 'success') {
            return view(
                static::TEMPLATE_PATH . '/create',
                array(
                    'sectors' => $sectors->data,
                    'themes' => $themes,
                    'policy_types' => $policy_types->data,
                    'branches' => $branch,
                    'mga_schemes' => $mga_schemes->data,
                    'trades' => $trade_output,
                    'broker_organisations' => $broker_organisations->data,
                    'subscriptions' => [],
                    'communitySectors' => $arrSectors,
                )
            );
        }

        return Redirect::route('organisation.index')
            ->with('error', 'Failed to create new organisation.');
    }

    private function responsibleBusinessMockedPolicyData()
    {
        return [
            'policy_type_id' => [0 => 1],
            'policy_numbers' => [
                1 => [
                    'policy_number' => 999999,
                    'inception_date_of_cover' => '01/01/1970',
                    'expiry_date_of_cover' => '01/01/1970',
                    'premium' => 1,
                    'loss_ratio' => 1,
                ]
            ]
        ];
    }

    //Store
    public function store(Request $request)
    {
        $data = $request->all();
        $isResponsibleBusiness = false;

        if (
            isset($data['sector'])
            && !empty($data['sector'])
            && (config('app.responsible_business.sector_id') == $data['sector'])
        ) {
            $isResponsibleBusiness = true;
            $data = array_merge($data, $this->responsibleBusinessMockedPolicyData());
        }

        $rules = [
            'name' => 'required|regex:/(^[A-Za-z0-9 ]+$)+/',
            'email' => 'nullable|email',
            'bound' => 'required',
            'sector' => 'required',
            'policy_numbers' => 'required|array',
            'policy_type_id' => 'required|array',
        ];

        $messages = [];

        if (Session::get('user')->login_type == 'broker-user') {
            $broker_rule = ['mga_scheme' => 'required_if:broker_id,==,""', 'broker_id' => 'required_if:mga_scheme,==,""'];
            $rules = array_merge($rules, $broker_rule);
        }

        foreach ($data['policy_numbers'] as $policy_id => $val) {
            if (isset($data['policy_type_id']) && in_array($policy_id, $data['policy_type_id'])) {
                foreach ($val as $k => $v) {
                    // if is bound, documents are required
                    if ($data['bound'] == '1') {

                        if (!isset($data['policy_numbers'][$policy_id]['has_policy_document'])) {
                            $rules['policy_numbers.'.$policy_id.'.policy_document'] = 'required';
                            $messages['policy_numbers.'.$policy_id.'.policy_document'.'.required'] = 'The policy document field is required.';
                        }

                        if ($k == 'policy_document') {
                            $rules['policy_numbers.' . $policy_id . '.' . $k] = 'required';
                            $messages['policy_numbers.' . $policy_id . '.' . $k . '.required'] = 'The policy document field is required.';
                        }

                        if ($k != 'renewal') {
                            $rules['policy_numbers.' . $policy_id . '.' . $k] = 'required';
                            $messages['policy_numbers.' . $policy_id . '.' . $k . '.required'] = 'The ' . str_replace('_', ' ', $k) . ' field is required.';
                        }
                    } else {
                        if ($k != 'policy_document' && $k != 'renewal') {
                            $rules['policy_numbers.' . $policy_id . '.' . $k] = 'required';
                            $messages['policy_numbers.' . $policy_id . '.' . $k . '.required'] = 'The ' . str_replace('_', ' ', $k) . ' field is required.';
                        }
                    }
                }
            }
        }

        $validator = Validator::make($data, $rules, $messages);

        if ($validator->fails()) {
            return Redirect::back()->withInput()
                ->withErrors($validator->errors());
        } else {
            // Unset un-needed policy numbers
            foreach ($data['policy_numbers'] as $id => $policy_number) {
                if (in_array($id, $data['policy_type_id'])) {
                    foreach ($policy_number as $key => $item) {
                        if ($item == '') {
                            unset($data['policy_numbers'][$id][$key]);
                        }
                    }

                    if (count($data['policy_numbers'][$id]) == 0) {
                        unset($data['policy_numbers'][$id]);
                    }
                } else {
                    unset($data['policy_numbers'][$id]);
                }
            }

            unset($data['_token']);

            if (isset($data['cover_type'])) {
                // $data['cover_type'] = implode(',',$data['cover_type']);
            }

            foreach ($data['policy_numbers'] as $key => $policy_number) {
                if (isset($policy_number['inception_date_of_cover'])) {
                    $data['policy_numbers'][$key]['inception_date_of_cover'] = strtotime(str_replace('/', '-', $policy_number['inception_date_of_cover']));
                } else {
                    $data['policy_numbers'][$key]['inception_date_of_cover'] = 0;
                }

                if (isset($policy_number['expiry_date_of_cover'])) {
                    $data['policy_numbers'][$key]['expiry_date_of_cover'] = strtotime(str_replace('/', '-', $policy_number['expiry_date_of_cover']));
                } else {
                    $data['policy_numbers'][$key]['expiry_date_of_cover'] = 0;
                }

                if (isset($policy_number['renewal'])) {
                    $data['policy_numbers'][$key]['renewal'] = strtotime(str_replace('/', '-', $policy_number['renewal']));
                } else {
                    $data['policy_numbers'][$key]['renewal'] = "";
                }

                if (isset($policy_number['policy_document'])) {
                    $file = $policy_number['policy_document'];
                    $uuid = Str::uuid()->toString();

                    if ($isResponsibleBusiness) {
                        $fileName = config('app.responsible_business.file_name');
                    } else {
                        $fileName = strtolower(preg_replace('/[\s_]|^%20/', '-', $policy_number['policy_document']->getClientOriginalName()));

                        $upload = $this->documents->upload($file, $uuid, 0);

                        if ($upload['response'] == 'success') {
                            $data['policy_numbers'][$key]['document_title'] = $fileName;
                            $data['policy_numbers'][$key]['document_store_name'] = $uuid;
                        }
                    }
                } else {
                    // ...
                }
            }

            if (isset($data['policy_numbers']) && empty($data['policy_numbers'])) {
                // return Redirect::route(static::ROUTE_PREFIX . '.create')
                //     ->withInput($request->old())
                //     ->withErrors(['Policy numbers are required']);
                redirect()->back()->withInput()->withErrors(['Policy numbers are required']);
            }

            // Upload organisation logo
            if ($request->hasFile('image')) {
                $file = $request->file('image');
                $ext = $request->file('image')->getClientOriginalExtension();
                $name = Str::uuid()->toString();

                if (!is_bool($this->files->upload($file->getRealPath(), $name . '.' . $ext))) {
                    return Redirect::back()->with('error', 'Failed to upload image')->withInput();
                }

                $data['logo'] = $name . '.' . $ext;
            }

            unset($data['image']);

            if ($request->hasFile('dashboard_image')) {
                $file = $request->file('dashboard_image');
                $ext = $request->file('image')->getClientOriginalExtension();
                $name = Str::uuid()->toString();

                if (!is_bool($this->files->upload($file->getRealPath(), $name . '.' . $ext))) {
                    return Redirect::back()->with('error', 'Failed to upload image')->withInput();
                }

                $data['dashboard_image'] = $name . '.' . $ext;
            }

            $data['set_by'] = Session::get('user')->id;
            $data['login_type'] = Session::get('user')->login_type;

            $response = json_decode(Api::post('api/v1/organisation/store', $data));

            if ($response->response == 'success') {
                //Fix cache
                $organisation = json_decode(Api::get('api/v1/organisation/' . $response->id));

                // Create an entry for organisation section settings
                $defaultSettings = [
                    'organisation_id'      => $response->id,
                    'settings'             => [
                        'risk_engineering' => 0,
                        'survey'           => 0,
                        'your_team'        => 0,
                        'services'         => '{}',
                    ],
                ];
                $orgSettings = json_decode(Api::post('api/v1/organisation-settings/storeOrUpdate', $defaultSettings));
                if ($organisation->response == 'success') {
                    if ($organisation->data->mgascheme) {
                        $broker_id = $organisation->data->mgascheme->broker_id;
                        Cache::forget('dashboard_statistics_broker_id_' . $broker_id);
                    }
                    if ($organisation->data->broker_id) {
                        Cache::forget('dashboard_statistics_broker_id_' . $organisation->data->broker_id);
                    }
                }

                if (isset($data['subscriptions'])) {
                    $subscriptions = json_decode($data['subscriptions']);

                    foreach ($subscriptions as $key => $subscription) {
                        Api::post(
                            route(
                                'api.community.sector.org-subscribe',
                                [
                                    'organisation' => $response->id,
                                    'sector_id' => $key,
                                ],
                                false
                            )
                        );
                    }
                }

                $this->resetOrgOptionCache();
                return Redirect::route(static::ROUTE_PREFIX . '.index')
                    ->with('success', 'Organisation added');
            } else {
                if ($response->errors) {
                    return Redirect::route(static::ROUTE_PREFIX . '.create')
                        ->withInput()
                        ->withErrors($response->errors);
                } else {
                    return Redirect::route(static::ROUTE_PREFIX . '.create')
                        ->withInput()
                        ->withErrors($response->message);
                }
            }
        }
    }

    // Destroy
    public function destroy($id)
    {
        $data = ['id' => $id];
        $response = json_decode(Api::post('api/v1/organisation/delete', $data));

        if ($response->response == 'success') {
            $this->resetOrgOptionCache();
            return Redirect::route(static::ROUTE_PREFIX . '.index')
                ->with('success', 'Organisation Deleted');
        } else {
            return Redirect::route(static::ROUTE_PREFIX . '.index')
                ->with('error', $response->message);
        }
    }

    public function deleteSov($organisation_id)
    {
        $response = json_decode(Api::post('api/v1/organisation/' . $organisation_id . '/delete-sov'));

        return Response::json(
            [
                'response' => 'success',
            ]
        );
    }

    //Edit
    public function edit($id)
    {
        $data = json_decode(Api::get('api/v1/organisation/' . $id));

        if (Session::get('user')->login_type == 'broker-user' && (!isset($data->data->mga_scheme) || $data->data->mga_scheme == null)) {
            return Response::make('Unauthorized', 401);
        }
        
        $orgData = (object)[];
        $cacheKey = "all_edit_organisation_data";

        $apiEndpoints = [
            'sectors' => 'api/v1/sector/all',
            'policy_types' => 'api/v1/policy-types/all',
            'branch' => 'api/v1/liberty-branches/options',
            'trades' => 'api/v1/trades/options/0',
            'themes' => 'api/v1/themes/options'
        ];

        if (Cache::has($cacheKey)) {
            $orgData = Cache::get($cacheKey);
            $dataKeys = ['sectors', 'policy_types', 'branch', 'trades', 'themes'];
            foreach ($dataKeys as $key) {
                $$key = $orgData->$key;
            }
        } else {
            foreach ($apiEndpoints as $key => $endpoint) {
                $orgData->$key = json_decode(Api::get($endpoint));
            }

            Cache::put($cacheKey, $orgData, 7200);
        }

        foreach ($apiEndpoints as $key => $endpoint) {
            $$key = isset($$key) ? $$key : $orgData->{$key};
        }
        
        $brokerId = Session::get('user')->broker_id ?: 0;
        $broker_organisations = GetBrokerService::getBrokerById($brokerId);

        if (Session::get('user')->login_type == 'broker-user' && $brokerId) {
            $mga_schemes = GetMgaSchemeService::getMgaSchemeById(id: $brokerId);
        } else {
            $mga_schemes = GetMgaSchemeService::get(id: '');
        }

        $trade_output = [];

        foreach ($trades as $key => $value) {
            $trade_output[$key] = $value;
        }

        if ($data->response != 'error') {
            try {
                if (!is_null($data->data->logo) && $data->data->logo != '') {
                    $logo = $this->files->link($data->data->logo);
                } else {
                    $logo = null;
                }
            } catch (Exception $e) {
                $logo = null;
            }

            try {
                if (!is_null($data->data->dashboard_image) && $data->data->dashboard_image != '') {
                    $dashboard_image = $this->files->link($data->data->dashboard_image);
                } else {
                    $dashboard_image = null;
                }
            } catch (Exception $e) {
                $dashboard_image = null;
            }

            if (!is_null($data->data->logo) && $data->data->logo != '' && $data->data->logo != 'none') {
                $data->data->image_url = $this->files->link($data->data->logo);
            } else {
                $data->data->image_url = '/img/dummy/logo-placeholder.png';
            }

            if (isset($data->data->sov_cloudname)) {
                $data->data->sov_link = $this->files->link($data->data->sov_cloudname);
                $data->data->sov_filesize = $this->files->formatBytes($data->data->sov_filesize);
            }

            $policyUndewritersDropdown = [];
            $brokerUsers = json_decode(Api::get('api/v1/organisation/options/broker-user/' . $id));

            $options = json_decode(Api::get(
                static::get_api_uri('options-multiple', 'liberty-users') . '?roles=underwriter,risk-engineer'
            ));
    
            $underwriters = $options->underwriter ?? [];
            $riskEngineer = 'risk-engineer';
            $riskEngineers = $options->{$riskEngineer} ?? [];

            $clientContacts = $data->data->clientUsers;
            $secondaryclientContacts = $data->data->secondaryclientUsers;
            $claimLaisonContacts = $data->data->claimsLaisonUser;

            $c_clientContacts = $data->data->clientContacts;
            $s_clientContacts = $data->data->secondaryclientContacts;
            $all_contacts = array_merge($c_clientContacts, $s_clientContacts);
            $allContacts = array_unique($all_contacts, SORT_REGULAR);

            $jsonSectors = json_decode(Api::get('api/v1/community/get-sector'))->data;
            $communitySectors = $jsonSectors ? json_decode($jsonSectors)->data : [];

            $arrSectors = [];
            foreach ($communitySectors as $communitySector) {
                $arrSectors[$communitySector->_id] = $communitySector->name;
            }

            uasort(
                $arrSectors,
                function ($lhs, $rhs) {
                    return strcmp($lhs, $rhs);
                }
            );

            $subscriptions = json_decode(Api::get(route('api.community.sector.org-subscriptions', ['id' => $id], false)))->data;

            $orgSubscriptions = [];
            foreach ($subscriptions as $subscription) {
                if (array_key_exists($subscription, $arrSectors)) {
                    $orgSubscriptions[$subscription] = $arrSectors[$subscription];
                }
            }

            $allProducts = $this->getAllProducts();
            $cmsSectors = $this->getCmsSector($allProducts);
            $cmsSubSectors = $this->getCmsSubSector($allProducts, $data->data->product_sector);
            $cmsProducts = [];

            if (isset($data->data->product_sector) && isset($data->data->product_sector)) {
                $cmsProducts = $this->getCmsProducts($allProducts, $data->data->product_sector, $data->data->product_subsector);
            }

            if (count($cmsProducts) > 0 && count($data->data->products) > 0) {
                foreach ($data->data->products as $key => $value) {
                    $slug = $data->data->products[$key]->slug;
                    $name = array_values(
                        array_filter(
                            $cmsProducts,
                            function ($v) use ($slug) {
                                return $v->slug == $slug;
                            }
                        )
                    );
                    if (count($name) > 0) {
                        $data->data->products[$key]->name = $name[0]->name;
                    }
                }
            }

            if (isset($data->data->products)) {
                foreach ($data->data->products as $product) {
                    $purchased = array_filter(
                        $cmsProducts,
                        function ($cmsProduct) use ($product) {
                            return $product->slug == $cmsProduct->slug;
                        }
                    );
                    $purchased = array_pop($purchased);
                    if (isset($purchased->name)) {
                        $product->name = $purchased->name;
                    }
                }
            }

            $organisation_policies = [];

            if (isset($data->data->policy_numbers) && isset($policy_types->data)) {
                $policyUndewritersDropdown = array_map(
                    function ($policies) {
                        return $policies->type;
                    },
                    $data->data->policy_numbers
                );

                $final  = [];
                foreach ($policyUndewritersDropdown as $current) {
                    $organisation_policies[] = strtolower($current->name);

                    if (!in_array($current, $final)) {
                        $final[] = $current;
                    }
                }
                $policyUndewritersDropdown = $final;
            }

            $data->data->organisation_policies = $organisation_policies;

            $selected_uw_policies = [];
            foreach ($data->data->uw_policies as $policies) {
                foreach ($policies as $policy) {
                    if (!array_key_exists($policy->underwriter_id, $selected_uw_policies)) {
                        $selected_uw_policies[$policy->underwriter_id] = [$policy->policy_id];
                    } else {
                        array_push($selected_uw_policies[$policy->underwriter_id], $policy->policy_id);
                    }
                }
            }
            $all_policies = [];
            $org_policies = [];
            foreach ($policyUndewritersDropdown as $policies) {
                foreach ($policies as $policy) {
                    $org_policies[] = $policy;
                }
            }
            $org_policies = array_unique($org_policies);
            foreach ($policy_types->data as $policies) {
                if (in_array($policies->id, $org_policies)) {
                    $all_policies[] = ['value' => $policies->id, 'label' => $policies->name];
                }
            }
            
            $claimTypes = [];
            if (!empty($data->data->sector)) {
                $claimTypes  = json_decode(Api::get('api/v1/claim-types/all/' . $data->data->sector));
            }

            $orgSettings = json_decode(Api::get('api/v1/organisation-settings/settings/' . $id));
            $settings    = !is_null($orgSettings) ? json_decode($orgSettings->access_setting, true) : [];
            Cache::forget("organisation-dashboard-data-admin-{$id}");
            return view(
                static::TEMPLATE_PATH . '/edit',
                [
                    'organisation'                => $data->data,
                    'sectors'                     => $sectors->data,
                    'themes'                      => $themes,
                    'logo'                        => $logo,
                    'dashboard_image'             => $dashboard_image,
                    'policy_types'                => $policy_types->data,
                    'branches'                    => $branch,
                    'mga_schemes'                 => $mga_schemes->data,
                    'trades'                      => $trade_output,
                    'claimTypes'                  => isset($claimTypes->data) ? $claimTypes->data : [],
                    'underwriters'                => $underwriters,
                    'riskEngineers'               => $riskEngineers,
                    'brokerUsers'                 => $brokerUsers,
                    'clientContacts'              => $clientContacts,
                    'secondaryclientContacts'     => $secondaryclientContacts,
                    'claimLaisonContacts'         => $claimLaisonContacts,
                    'allContacts'                 => $allContacts,
                    'broker_organisations'        => $broker_organisations->data,
                    'subscriptions'               => $orgSubscriptions,
                    'communitySectors'            => $arrSectors,
                    'policy_underwriter_dropdown' => $policyUndewritersDropdown,
                    'cmsSectors'                  => $cmsSectors,
                    'cmsSubSectors'               => $cmsSubSectors,
                    'cmsProducts'                 => $cmsProducts,
                    'allPolicies'                 => $all_policies,
                    'selectedPolicies'            => $selected_uw_policies,
                    'services'                    => config('client_dashboard.services'),
                    'selected_settings'           => $settings,
                ]
            );
        } else {
            // Return to index
            return Redirect::route(static::ROUTE_PREFIX . '.index')
                ->with('error', $data->message);
        }
    }

    //Update
    public function update(Request $request)
    {
        $data = $request->all();
        $isResponsibleBusiness = false;

        if (
            isset($data['sector']) && !empty($data['sector'])
            && (config('app.responsible_business.sector_id') == $data['sector'])
        ) {
            $isResponsibleBusiness = true;
            $data = array_merge($data, $this->responsibleBusinessMockedPolicyData());
        }

        $data['has_previsico_access'] = ($request->has('has_previsico_access') &&
            ($request->get('has_previsico_access') == 'on' || $request->get('has_previsico_access') == 1)
        ) ? 1 : 0;

        $rules = [
            'name'                      => 'required|regex:/(^[A-Za-z0-9 ]+$)+/',
            'email'                     => 'nullable|email',
            'country'                   => 'required',
            'sector'                    => 'required',
            'policy_numbers'            => 'array',
            'policy_type_id'            => 'required',
            'prop_doc.num_of_toolkit'   => 'nullable|integer',
            'prop_doc.is_ergo_valuator' => 'boolean',
            'prop_doc.num_of_course'    => 'nullable|integer',
        ];

        $messages = [
            'name.regex'                      => 'Organisation name can only have letters, numbers and spaces',
            'prop_doc.num_of_toolkit.integer' => 'Invalid value provided for number of toolkit(s) field',
            'prop_doc.num_of_course.integer'  => 'Invalid value provided for number of course(s) field',
        ];

        if (!$isResponsibleBusiness) {
            foreach ($data['policy_numbers'] as $policy_id => $val) {
                if (isset($data['policy_type_id']) && in_array($policy_id, $data['policy_type_id'])) {
                    foreach ($val as $k => $v) {
                        // if is bound, documents are required
                        if ($data['bound'] == '1') {

                            if (!isset($data['policy_numbers'][$policy_id]['has_policy_document'])) {
                                $rules['policy_numbers.'.$policy_id.'.policy_document'] = 'required';
                                $messages['policy_numbers.'.$policy_id.'.policy_document'.'.required'] = 'The policy document field is required.';
                            }

                            if ($k == 'policy_document' && !isset($data['policy_numbers'][$policy_id]['has_policy_document'])) {
                                $rules['policy_numbers.'.$policy_id.'.'.$k] = 'required';
                                $messages['policy_numbers.'.$policy_id.'.'.$k.'.required'] = 'The policy document field is required.';
                            }

                            if ($k != 'policy_document' && $k != 'renewal' && $k != 'has_policy_document' && $k != 'policy_document_id') {
                                $rules['policy_numbers.'.$policy_id.'.'.$k] = 'required';
                                $messages['policy_numbers.'.$policy_id.'.'.$k.'.required'] = 'The '.str_replace('_', ' ', $k).' field is required.';
                            }
                        } else {
                            if ($k != 'policy_document' && $k != 'renewal') {
                                $rules['policy_numbers.'.$policy_id.'.'.$k] = 'required';
                                $messages['policy_numbers.'.$policy_id.'.'.$k.'.required'] = 'The '.str_replace('_', ' ', $k).' field is required.';
                            }
                        }
                    }
                }
            }
        }

        $validator = Validator::make($data, $rules, $messages);
        if ($validator->fails()) {
            return Redirect::route(static::ROUTE_PREFIX.'.edit', $data['id'])
                ->withInput()
                ->withErrors($validator->errors());
        } else {
            // Unset the not needed policy numbers

            foreach ($data['policy_numbers'] as $id => $policy_number) {
                if (in_array($id, $data['policy_type_id'])) {
                    foreach ($policy_number as $key => $item) {
                        if ($item == '') {
                            unset($data['policy_numbers'][$id][$key]);
                        }
                    }

                    if (count($data['policy_numbers'][$id]) == 0) {
                        unset($data['policy_numbers'][$id]);
                    }
                } else {
                    unset($data['policy_numbers'][$id]);
                }
            }

            // Upload SoV file
            if ($request->hasFile('sov')) {
                $file = $request->file('sov');
                $name = Str::uuid()->toString();

                if (!is_bool($this->files->upload($file->getRealPath(), $name))) {
                    return Redirect::back()->with('error', 'Failed to upload SOV file')->withInput($request->old());
                }

                $data['sov_cloudname'] = $name;
                $data['sov_filename'] = $file->getClientOriginalName();
                $data['sov_filesize'] = $file->getSize();

                unset($data['sov']);
            }

            // Upload organisation logo
            if ($request->hasFile('image')) {
                $file = $request->file('image');
                $name = Str::uuid()->toString();
                //$this->files->delete($data['image_id']);
                if (!is_bool($this->files->upload($file->getRealPath(), $name))) {
                    return Redirect::back()
                        ->with('error', 'Failed to upload organisation image')
                        ->withInput($request->old());
                }

                $data['logo'] = $name;
                unset($data['image']);
            }

            //Upload dashboard image
            if ($request->hasFile('dashboard_image')) {
                $file = $request->file('dashboard_image');
                $name = Str::uuid()->toString();
                //$this->files->delete($data['image_id']);
                if (!is_bool($this->files->upload($file->getRealPath(), $name))) {
                    return Redirect::back()
                        ->with('error', 'Failed to upload dashboard image')
                        ->withInput($request->old());
                }

                $data['dashboard_image'] = $name;
            }

            //Update
            unset($data['_token']);
            unset($data['image_id']);
            unset($data['dashboard_image_id']);
            unset($data['image']);
            unset($data['_method']);
            unset($data['attachment_type']);

            if (isset($data['cover_type'])) {
                $data['cover_type'] = implode(',', $data['cover_type']);
            }

            foreach ($data['policy_numbers'] as $key => $policy_number) {
                if (isset($policy_number['inception_date_of_cover'])) {
                    $data['policy_numbers'][$key]['inception_date_of_cover'] = strtotime(str_replace('/', '-', $policy_number['inception_date_of_cover']));
                } else {
                    $data['policy_numbers'][$key]['inception_date_of_cover'] = 0;
                }

                if (isset($policy_number['expiry_date_of_cover'])) {
                    $data['policy_numbers'][$key]['expiry_date_of_cover'] = strtotime(str_replace('/', '-', $policy_number['expiry_date_of_cover']));
                } else {
                    $data['policy_numbers'][$key]['expiry_date_of_cover'] = 0;
                }

                if (isset($policy_number['renewal'])) {
                    $data['policy_numbers'][$key]['renewal'] = strtotime(str_replace('/', '-', $policy_number['renewal']));
                } else {
                    $data['policy_numbers'][$key]['renewal'] = 0;
                }

                if (isset($policy_number['policy_document'])) {
                    $file = $policy_number['policy_document'];
                    $uuid = Str::uuid()->toString();
                    //dd($policy_number['policy_document']->getClientOriginalName());
                    $fileName = strtolower(preg_replace('/[\s_]|^%20/', '-', $policy_number['policy_document']->getClientOriginalName()));

                    $upload = $this->documents->upload($file, $uuid, 0);

                    if ($upload['response'] == 'success') {
                        $data['policy_numbers'][$key]['document_title'] = $fileName;
                        $data['policy_numbers'][$key]['document_store_name'] = $uuid;
                        $data['policy_numbers'][$key]['new_document_uploaded'] = true;
                    }
                } else {
                    // ...
                }
            }

            foreach ($data as $key => $d) {
                if ((strpos($key, 'tag-') !== false) || (strpos($key, 'tags-') !== false)) {
                    unset($data[$key]);
                }
            }

            $uw_data = json_decode($data['selected_uw']);
            $policy_data = [];
            if (isset($uw_data) && count($uw_data) > 0) {
                foreach ($uw_data as $uw) {
                    foreach ($uw as $key => $value) {
                        if (!is_numeric($value)) {
                            $value = get_object_vars($value);
                            $policy_data[] = $value;
                        } else {
                            $policy_data[] = [$key => $value];
                        }
                    }
                }
            }

            $uw_data = $policy_data;

            if (isset($uw_data) && count($uw_data) > 0) {
                $uw = [];
                foreach ($uw_data as $uw_policy) {
                    foreach ($uw_policy as $key => $value) {
                        if (!array_key_exists($key, $uw)) {
                            $uw[$key] = [$value];
                        } else {
                            array_push($uw[$key], $value);
                        }
                    }
                }
                $data['selected_uw'] = $uw;
            } else {
                unset($data['selected_uw']);
            }

            $oldSector = $data['oldSector'];
            unset($data['oldSector']);
            unset($data['num_of_toolkit_checkbox']);
            unset($data['num_of_course_checkbox']);
            unset($data['has_corpore_assessment']);

            $settings = ['organisation_id' => $data['id']];
            if (isset($data['settings'])) {
                $settings['settings'] = $data['settings'];
                $settings['settings']['your_team'] = 1;
                unset($data['settings']);
            }

            $response = json_decode(Api::post('api/v1/organisation/update', $data));

            if ($response->response == 'success') {

                // Current sector rb to different sector
                if (
                    $oldSector == config('app.responsible_business.sector_id')
                    && $data['sector'] != config('app.responsible_business.sector_id')
                ) {
                    $settings['settings']['risk_engineering'] = 0;
                    $settings['settings']['survey'] = 0;
                    $settings['settings']['your_team'] = 0;
                    $settings['settings']['services'] = '{}';
                }

                // store or update org settings
                if (isset($settings['settings'])) {
                    $settingsResponse = json_decode(Api::post('api/v1/organisation-settings/storeOrUpdate', $settings));
                }

                $organisation = json_decode(Api::get('api/v1/organisation/' . $data['id']));

                if ($organisation->response == 'success') {
                    if ($organisation->data->mgascheme) {
                        $broker_id = $organisation->data->mgascheme->broker_id;
                        Cache::forget('dashboard_statistics_broker_id_' . $broker_id);
                    }
                    if ($organisation->data->broker_id) {
                        Cache::forget('dashboard_statistics_broker_id_' . $organisation->data->broker_id);
                    }
                }

                $this->recacheRelatedData($data['id'] ?? '');
                $this->resetOrgOptionCache();

                return Redirect::route(static::ROUTE_PREFIX . '.show', $data['id'])
                    ->with('success', 'Updated organisation successfully');
            } else {
                if (!is_array($response->message)) {
                    return Redirect::route(static::ROUTE_PREFIX . '.edit', array('id' => $data['id']))
                        ->with('error', $response->message);
                } else {
                    return Redirect::route(static::ROUTE_PREFIX . '.edit', array('id' => $data['id']))
                        ->withErrors($response->message);
                }
            }
        }

        $org_id=$data['id'];
        $this->buildCache(($org_id));
    }

    public function showPolicyNumbers($organisation_id)
    {
        $api_call = json_decode(Api::get('/api/v1/organisation/' . $organisation_id . '/policy-numbers'));

        if (isset($api_call->response) && $api_call->response == 'success') {
            return response()->json([
                'response'             => 'success',
                'data'                 => $api_call->data,
                'organisation_details' => $api_call->organisation_details,
                'mga_scheme'           => $api_call->mga_scheme
            ]);
        }

        return response()->json(['response' => 'error']);
    }

    public function downloadSov($id, $cloudname, $filename)
    {
        $sovUrl = $this->files->download($cloudname, $filename);

        if ($sovUrl) {
            return Response::download($sovUrl, $filename);
        }
    }

    private function getCmsSector($allProducts = null)
    {
        if (empty($allProducts)) {
            $allProducts = $this->getAllProducts();
        }
        $sectors = array_map(
            function ($product) {
                return (object)['name' => $product->sector, 'slug' => $product->sectorslug];
            },
            $allProducts
        );

        $sectors = Helper::getUniqueObjects($sectors);
        uasort(
            $sectors,
            function ($lhs, $rhs) {
                return strcmp($lhs->name, $rhs->name);
            }
        );
        return $sectors;
    }

    public function getCmsSubSector($allProducts, $sector)
    {
        if (empty($allProducts)) {
            $allProducts = $this->getAllProducts();
        }
        $subsector = array_values(
            array_filter(
                $allProducts,
                function ($v) use ($sector) {
                    return $v->sectorslug == $sector;
                }
            )
        );

        $subsector = array_map(
            function ($product) {
                return (object)['name' => $product->subsector, 'slug' => $product->subsectorslug];
            },
            $subsector
        );
        $subsector = Helper::getUniqueObjects($subsector);
        uasort(
            $subsector,
            function ($lhs, $rhs) {
                return strcmp($lhs->name, $rhs->name);
            }
        );
        return $subsector;
    }

    public function getCmsProducts($allProducts = null, $sector = null, $subsector = null)
    {
        if (empty($allProducts)) {
            $allProducts = $this->getAllProducts();
        }

        $products = array_map(
            function ($product) {
                return (object)['slug' => $product->slug, 'name' => htmlspecialchars_decode($product->name)];
            },
            $allProducts
        );

        $products = Helper::getUniqueObjects($products);
        usort(
            $products,
            function ($a, $b) {
                return strcmp($a->name, $b->name);
            }
        );

        return $products;
    }

    public function getAllProducts()
    {
        $cms_products = 'api/v1/cms-products/all-products'; //cached response
        $cms_products = json_decode(Api::get($cms_products));
        return  Helper::getUniqueObjects($cms_products);
    }

    public function getBrokerUsers($mga_id, $broker_org_id)
    {
        $broker_users = [];
        $users = sprintf('api/v1/organisation/options/get-broker-user/%d/%d', $mga_id, $broker_org_id);
        $users = json_decode(Api::get($users));
        if (isset($users)) {
            $users = (array)$users;
            foreach ($users as $key => $value) {
                $userdata = (object)[];
                $userdata->name = $value;
                $userdata->id = $key;
                $broker_users[] = $userdata;
            }
        }
        return $broker_users;
    }

    private function buildCache($org_id){
        $command="riskreduce:build_cache_for_orgs {$org_id}";
        Artisan::call($command);
        
    }

    public function getPrevisicoAssets($org_id)
    {
        $locations=[];
        if((int)$org_id > 0){
            $response = json_decode(Api::get("/api/v1/organisation/{$org_id}/info"));
            if ($response->response == 'success' && isset($response->data->previsico_assets)) {
                $assets=$response->data->previsico_assets;
                $locations = array_map(
                    function ($assets) {
                        return (object)['id' => $assets->id, 'name' => $assets->name];
                    },
                    $assets
                );
            }
        }
      return $locations;
    }

    public function dashboardStatistics($org_id, $returnArray = false)
    {
        $id = $org_id;

        $organisation = GetOrganisationService::get($id);
        $organisation = $this->overrideOrganisationAssets($organisation);
        $riskGradingOverview = GetOrganisationRiskGradingOverview::get($id, true);
        $policyData = GetOrganisationPolicyDocs::get($id);
        $colors         = RiskGradingHelper::getGradingColorCodes();
        $srgTooltip     = RiskGradingHelper::DROPDOWN_LABELS;
        
        $data = [
            'organisation' => $organisation,
            'risk_grading_overview' => $riskGradingOverview->overview,
            'risk_grading_overview_attributes'  => (array) $riskGradingOverview->attributes,
            'policy_types' => $policyData->policy_types,
            'colors'              => $colors,
            'srg_tooltip'         => $srgTooltip,
        ];

        $riskGradingData = GetOrgRiskGradingData::get($id);
        $data += $riskGradingData;

        // Always get updated rrTracker data for risk rec status
        $data['rrTracker'] = $this->getSurveyStatistics($org_id);

        if ($returnArray) {
            return $data;
        }

        return view(static::TEMPLATE_PATH . '/partials/dashboard-statistics', $data);
    }

    public function getSurveyStatistics($organisationId)
    {
        $survey_statistics = [];
        $endpoint = "api/v1/risk-recommendations/cards";
        $tracker_count = json_decode(Api::get($endpoint, ['organisation' => $organisationId, 'limit' => 1000]));
        foreach ($tracker_count as $key => $value) {
            $survey_statistics[$key] = count($value->data);

            if ($key === 'backlog') {
                // recheck required by if it is under past-due
                $pastdue = 0;
                foreach ($value->data as $card) {
                    $requiredBy = $card->properties->{'Required By'};
                    if (!empty($requiredBy) && $requiredBy !== '-') {
                        \Log::info("Required BY: " . $requiredBy);
                        $currentDate = Carbon::createFromFormat('d/m/Y', $requiredBy);
                        if ($currentDate->lt(Carbon::now())) {
                            $pastdue++;
                        }
                    }
                }
            }
        }

        $survey_statistics['backlog'] = $survey_statistics['backlog'] - $pastdue;
        $survey_statistics['past-due'] = $survey_statistics['past-due'] + $pastdue;

        return $survey_statistics;
    }

    /**
     * @param int $id   Organisation id
     */
    public function getOrganisationAccordionData(int $id)
    {
        $cacheKey = "surveys-data-for-organisation-{$id}";
        $surveys = $this->getSetCache($cacheKey);
        if (!$surveys) {
            $surveys = json_decode(Api::get('/api/v1/surveys/all/1/10?organisation_id=' . $id));
            $this->getSetCache($cacheKey, $surveys);
        }

        $cacheKey = "policy-data-for-organisation-{$id}";
        $policyData = $this->getSetCache($cacheKey);
        if (!$policyData) {
            $policy_doc_types = json_decode(Api::get('/api/v1/document-policy/all'));
            $documents = json_decode(Api::get('/api/v1/document/policy/' . $id));
            $policy_types = json_decode(Api::get('api/v1/policy-types/all'));

            if (isset($policy_doc_types->data) && isset($documents)) {
                $policy_docs = $documents;
                $policy_doc_types = $policy_doc_types->data;
            }
            $policy_docs = isset($policy_docs) ? $policy_docs : [];
            $policy_doc_types = isset($policy_doc_types) ? $policy_doc_types : [];
            $policyData = (object)[
                'policy_docs' => $policy_docs,
                'policy_doc_types' => $policy_doc_types,
                'policy_types' => $policy_types->data,
            ];
            $this->getSetCache($cacheKey, $policyData);
        }

        $cacheKey = "account-documents-for-organisation-{$id}";
        $accountDocuments = $this->getSetCache($cacheKey);
        if (!$accountDocuments) {
            $accountDocuments = json_decode(Api::get('/api/v1/account-documents/all?organisation_id=' . $id))->data;
            foreach ($accountDocuments as &$accountDocument) {
                $cloudpath = $accountDocument->organisation_id . '/' . $accountDocument->cloudname . '/'
                    . $accountDocument->filename;
                $accountDocument->download = $this->files->link($cloudpath, '2 hours');
            }
            $this->getSetCache($cacheKey, $accountDocuments);
        }

        $cacheKey = "organisation-report-for-organisation-{$id}";
        $organisationReports = $this->getSetCache($cacheKey);
        if (!$organisationReports) {
            $organisationReports = json_decode(Api::get('/api/v1/organisation/' . $id . '/reports/all'))->data;
            foreach ($organisationReports as &$organisationReport) {
                $cloudpath = $organisationReport->organisation_id . '/' . $organisationReport->cloudname . '/'
                    . $organisationReport->filename;
                $organisationReport->download = $this->files->link($cloudpath, '2 hours');
            }
            $this->getSetCache($cacheKey, $organisationReports);
        }

        $cacheKey = "organisation-rhs-details-for-organisation-{$id}";
        $organisationDetails = $this->getSetCache($cacheKey);
        if (!$organisationDetails) {
            $organisationDetails = json_decode(Api::get('/api/v1/rhs-organisations/name/' . $id));
            $this->getSetCache($cacheKey, $organisationDetails);
        }

        $cacheKey = "organisation-all-flood-alert-{$id}";
        $allFloodAlerts = $this->getSetCache($cacheKey);
        if (!$allFloodAlerts) {
            $allFloodAlerts = json_decode(Api::get("api/v1/organisation/{$id}/all-flood-alerts"));
            $alerts = array_map(
                function ($alertData) {
                    $alert = $alertData->alert;
                    return (object)[
                        'id' => $alert->id,
                        'date' => $alert->date,
                        'location_name' => $alertData->asset->name,
                        'alert_type' => self::ALERT_LEVEL_MAPPING[$alert->alert_level],
                        'actioned' => $alert->is_actioned,
                        'notes' => $alert->actioned_by_notes ?? '-',
                        'client_name' => $alert->actioned_by_user ?? '-',
                    ];
                },
                $allFloodAlerts->items ?? []
            );

            $allFloodAlerts = $alerts;
            $this->getSetCache($cacheKey, $allFloodAlerts);
        }

        $cacheKey = "organisation-links-for-organisation-{$id}";
        $links = $this->getSetCache($cacheKey);
        if (!$links) {
            $links = json_decode(Api::get('api/v1/link/organisation/' . $id));
            $this->getSetCache($cacheKey, $links);
        }

        $organisation = GetOrganisationService::get($id);
        $organisation = $this->overrideOrganisationAssets($organisation);

        $cacheKey = "organisation-notes-for-organisation-{$id}";
        $notes = $this->getSetCache($cacheKey);
        if (!$notes) {
            $notes = json_decode(Api::get('api/v1/organisation/' . $id . '/notes'));
            $this->getSetCache($cacheKey, $notes);
        }

        $isBrokerUser = Session::get('user')?->login_type === 'broker-user';

        return view(static::TEMPLATE_PATH . '/partials/accordions/org-accordions', [
            'accountDocuments' => $accountDocuments,
            'floodAlerts' => $allFloodAlerts ?? [],
            'links' => $links->data ?? [],
            'organisationDetails' => $organisationDetails->data ?? [],
            'organisationReports' => $organisationReports,
            'policy_docs' => $policyData->policy_docs,
            'policy_doc_types' => $policyData->policy_doc_types,
            'policy_types' => $policyData->policy_types,
            'surveys' => $surveys->data ?? [],
            'isBrokerUser' => $isBrokerUser,
            'organisation' => $organisation,
            'notes' => $notes->data ?? [],
        ]);
    }

    public function recacheRelatedData($orgId)
    {
        if (empty($orgId)) {
            return;
        }
        SendSqsMessageService::sendMessages([
            [
                'serviceClass' => GetOrganisationService::class,
                'params' => $orgId ?? '',
            ],
            [
                'serviceClass' => 'App\Services\CacheContent\GetOrganisationDetailsForDashboard',
                'params' => $orgId ?? '',
                'isClient' => true,
            ],
        ]);
    }

    public function resetOrgOptionCache()
    {
        SendSqsMessageService::sendMessages([
            [
                'serviceClass' => OptionListService::class,
                'params'       => 'organisations',
            ],
        ]);
    }

    public function overrideOrganisationAssets($organisation)
    {
        if (isset($organisation->logo) && $organisation->logo != '' && $organisation->logo != 'none') {
            $organisation->image_url = $this->files->link($organisation->logo);
        } else {
            $organisation->image_url = '/img/dummy/logo-placeholder.png';
        }
        return $organisation;
    }
}