<?php

namespace App\Http\Controllers;

use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Models\FileUpload;
use App\Models\Api;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Redirect;
use Exception;
use DateTime;
use DateTimeZone;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\EnquiriesExport;

class EnquiryController extends BaseController
{
    //TEMPLATE PATH
    const TEMPLATE_PATH = '/enquiries';

    //ROUTE PREFIX
    const ROUTE_PREFIX = 'rr-appetite/enquiries';

    public function __construct(Request $request, FileUpload $fileUpload)
    {
        parent::__construct($request);
        $this->files = $fileUpload;
    }

    public function allowForUser(User $user): bool
    {
        return $user->login_type === 'risk-control' || $user?->isRoleAdminOrAccountManager();
    }

    //Index
    public function index()
    {
        $active_forms = json_decode(Api::get('api/v1/rrappetite/new-form/all/active'));
        $archived_forms = json_decode(Api::get('api/v1/rrappetite/new-form/all/archive'));

        if($active_forms->status == 'success' && $archived_forms->status == "success") {
            return view(
                static::TEMPLATE_PATH . '/index',
                array(
                    'active_submissions' => $active_forms->data->submissions,
                    'archived_submissions' => $archived_forms->data->submissions,
                )
            );
        }
    }

    public function show($id)
    {
        // echo '<pre>'; print_r(Api::get('api/v1/rrappetite/new-form/'.$id));exit;
        $form = json_decode(Api::get('api/v1/rrappetite/new-form/'.$id));
        $files = [];
        if (is_null($form->data->submission->timezone) || $form->data->submission->timezone == "+00:00") {
            $form->data->submission->timezone = 'UTC';
        } else {
            $dateTime = new DateTime();
            $dateTime->setTimeZone(new DateTimeZone($form->data->submission->timezone));
            $form->data->submission->timezone = $dateTime->format('T');
        }

        $download_link = null;
        try {
            $file_exists = $this->files->exists('pdf/'.$id.'/liberty-presentation.pdf');
            if ($file_exists) {
                $download_link = $this->files->link('pdf/'.$id.'/liberty-presentation.pdf', '2 hours');
            }
        } catch(Exception $e) {
            $download_link = null;
        }
        if($form->data->submission->files && isset($form->data->submission->files) && !empty($form->data->submission->files)) {
            $splitFiles = explode(',', $form->data->submission->files);
            $downloadFile = null;
            foreach($splitFiles as $file){
                $pdfExist = $this->files->exists($file);
                if ($pdfExist) {
                          $downloadFile = $this->files->link($file, '1 hour');
                }
                $files[$file] = $downloadFile;
            }
        }

        // echo "<pre/>";
        // print_r(array(
        //             'submission' => $form->data->submission,
        //             //'products' => $form->data->products,
        //             'underwriter' => $form->data->underwriter,
        //             'download_link' => $download_link,
        //             'broker'    =>  isset($form->data->broker) ? $form->data->broker : []
        //         )); exit;
        if($form->status == 'success') {
            return view(
                static::TEMPLATE_PATH . '/show',
                array(
                'submission' => isset($form->data->submission) ? $form->data->submission : '',
                'products' => isset($form->data->products) ? $form->data->products : '',
                'underwriter' => isset($form->data->underwriter) ? $form->data->underwriter : '',
                   'region_name' => isset($form->data->region_name) ? $form->data->region_name : '',
                   'primary_product' => isset($form->data->primary_product) ? $form->data->primary_product : '',
                   'primary_sector' => isset($form->data->primary_sector) ? $form->data->primary_sector : '',
                   'primary_subsector' => isset($form->data->primary_subsector) ? $form->data->primary_subsector : '',
                'download_link' => isset($download_link) ? $download_link : '',
                   'broker'    =>  isset($form->data->broker) ? $form->data->broker : [],
                   'files' => $files ? $files : [],
                )
            );
        }
    }

    public function changeArchiveStatus(Request $request, $id)
    {
        $data = [
        'archive' => $request->get('archive'),
        ];

        $archive_form = json_decode(Api::post('api/v1/rrappetite/form/'.$id.'/archive', $data));

        if ($archive_form->status == 'success') {
            $active_forms = json_decode(Api::get('api/v1/rrappetite/form/all/active'));
            $archived_forms = json_decode(Api::get('api/v1/rrappetite/form/all/archive'));

            return view(
                static::TEMPLATE_PATH . '/index',
                array(
                    'active_submissions' => $active_forms->data->submissions,
                    'archived_submissions' => $archived_forms->data->submissions,
                )
            );
        }
    }

    public function changeEnquiryStatus(Request $request, $id)
    {
        $data = [
        'enquiry' => $request->get('enquiry'),
        ];

        $enquiry_status = json_decode(Api::post('api/v1/rrappetite/form/'.$id.'/enquiry', $data));

        if ($enquiry_status->status == 'success') {
            return Redirect::route('rr-appetite.enquiries.show', $id)->with('success', 'Enquiry status set');

        }  else {
            return Redirect::route('rr-appetite.enquiries.index')->with('error', 'Setting enquiry status failed');
        }
    }

    public function resendBrokerEmail($id, $send_to = 'broker')
    {
        // print_r(Api::get('api/v1/rrappetite/new-form/'.$id.'/resend-broker/'.$send_to));exit;
        $broker_email = json_decode(Api::get('api/v1/rrappetite/new-form/'.$id.'/resend-broker/'.$send_to));

        if ($broker_email->status == 'success') {
            return Redirect::route('rr-appetite.enquiries.show', $id)
                ->with('success', 'PDF resent to broker and underwriter');
        }

        return Redirect::route('rr-appetite.enquiries.index')
            ->with('error', 'Broker PDF was not sent successfully');
    }

    /**
     * Export the enquiries either in csv or excel format
     *
     * @param  string $type csv|excel
     * @return mixed
     */
    public function export($type='excel')
    {
        $data = [
            [
                'Enquiry ID',
                'First name' ,
                'Last name',
                'Email',
                'Phone',
                'Require price',
                'Response target date and time',
                'Timezone',
                'Region',
                'Underwriter',
                'Enquiry date',
                'Broker Company (if verified)',
                'Sector',
                'Subsector',
                'Products',
                'Comment'
            ]
        ] ;
        $sep = $type=='csv' ? ';' : ',' ;
        $enquiries = json_decode(Api::get('api/v1/rrappetite/form/all/active'))->data->submissions;

        // TODO: either get this data from the api or have the correct data in the enquiry
        $regions = [] ;
        $dataR = json_decode(Api::get('api/v1/rrappetite/regions'))->data;
        foreach($dataR as $region){
            $regions[$region->id] = $region->name ;
        }

        // TODO: either get this data from the api or have the correct data in the enquiry
        $underwriters = [] ;
        $dataR = json_decode(Api::get('api/v1/liberty-users/all'))->data;
        foreach($dataR as $underwriter){
            $underwriters[$underwriter->id] = $underwriter->first_name .' '.$underwriter->last_name ;
        }

        foreach($enquiries as $enquiry){
            $products = [] ;
            if(isset($enquiry->products)) {
                foreach($enquiry->products as $product){
                    if (! isset($product->name)) {
                        continue;
                    }

                    $products[] = $product->name ;
                }
                unset($enquiry->products);
            }
            $enquiry->products = implode($sep.' ', $products);

            $comment = $enquiry->comment ;
            $enquiry->broker = $enquiry->verified_broker ? $enquiry->broker : '' ;
            $enquiry->underwriter_id = isset($enquiry->underwriter) ? $enquiry->underwriter->first_name.' '.$enquiry->underwriter->last_name : '';
            $sector = isset($enquiry->sector) ? $enquiry->sector : ' ';
            $subsector = isset($enquiry->subsector) ? $enquiry->subsector : ' ';
            $products = isset($enquiry->products) ? $enquiry->products : ' ';
            unset(
                $enquiry->comment,
                $enquiry->deleted_at,
                $enquiry->archived,
                $enquiry->enquiry_status,
                $enquiry->verified_broker,
                $enquiry->underwriter,
                $enquiry->sector,
                $enquiry->subsector,
                $enquiry->products
            );
            $enquiry->region_id = isset($enquiry->region_id) && isset($regions[$enquiry->region_id]) ? $regions[$enquiry->region_id] : '';

            $enquiry->req_price = $enquiry->req_price ? 'Yes' : 'No';
            $enquiry->created_at = date('d.m.Y', strtotime($enquiry->created_at));
            $enquiry->preferred_datetime = date('d.m.Y h:i', strtotime($enquiry->preferred_datetime));
            $dateTime = new DateTime();
            if ($enquiry->timezone != null && $enquiry->timezone != "+00:00") {
                $dateTime->setTimeZone(new DateTimeZone($enquiry->timezone));
                $enquiry->timezone = $dateTime->format('e');
            } else {
                $enquiry->timezone = 'UTC';
            }
            $enquiry->sector = $sector ;
            $enquiry->subsector = $subsector ;
            $enquiry->products = $products ;
            $enquiry->comment = $comment ;
            $data[] = (array)$enquiry ;
        }

        $method = 'export'.ucfirst($type);
        return $this->$method($data);
    }

    /**
     * Export the enquiries in excel format
     *
     * @param  array $data
     * @return excel
     */
    public function exportExcel($data)
    {
        return Excel::download(new EnquiriesExport($data),'rrappetite-export-' . Carbon::now()->format('d-m-Y').'.xlsx');
    }

    /**
     * Export the enquiries in csv format
     *
     * @param  array $data
     * @return csv
     */
    public function exportCsv($data)
    {
        header('Content-Type: application/csv');
        header('Content-Disposition: attachment; filename="rrappetite-export-' . Carbon::now()->format('d-m-Y').'.csv";');
        $f = fopen('php://output', 'w');
        foreach ($data as $line) {
            fputcsv($f, $line);
        }
    }
}
