<?php

namespace App\Http\Controllers\GDPR;

use App\Models\Api;
use Illuminate\View\View;
use Illuminate\Support\Arr;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Session;
use App\Http\Controllers\BaseController;

class DataPrivacyController extends BaseController
{
    /**
     * @var string GPDR_VIEW
     */
    const GPDR_VIEW = 'gdpr.index';

    /**
     * @var string TOKEN
     */
    private const TOKEN = '_token';

    public function index(): View
    {
        return view(self::GPDR_VIEW);
    }

    /**
     * Render user search result
     *
     * @param Request $request
     * @return View
     */
    public function search(Request $request): View
    {
        $data     = $request->except(self::TOKEN);
        $response = json_decode(Api::post(config('gdpr.api') . '/search', $data), true);
        return view(self::GPDR_VIEW, ['user' => $response ?? null]);
    }

    /**
     * Return json contains response(success | error) and message
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function callApiAction(Request $request): JsonResponse
    {
        $data     = $request->all();
        $action   = $data['action'] === 'DELETE' ? '/delete' : '/anonymise';
        $params   = http_build_query($data);

        $url = config('gdpr.api') . '/for-api' . $action . "?" . $params;
        $response = json_decode(Api::get($url, $data));
        return response()->json([
            'response' => $response->response,
            'message'  => $response->message
        ]);
    }
 
    /**
     * Return csv
     *
     * @param Request $request
     * @return mixed
     */
    public function export(Request $request)
    {
        $user = Session::get('user');
        if(!$user) {
            return abort(JsonResponse::HTTP_FORBIDDEN);
        }

        $data     = $request->only(['first_name', 'last_name', 'email']);
        $response = json_decode(Api::post(config('gdpr.api') . '/search', $data), true);

        if(isset($response['link_to_pages'])) {
            $response['link_to_pages'] = implode(",", $response['link_to_pages']);
        }

        header('Content-Type: application/csv');
        header('Content-Disposition: attachment; filename="user-data.csv";');
        $file = fopen('php://output', 'w');
        fputcsv($file, [
            'First Name',
            'Last Name',
            'Email Address',
            'Phone Number',
            'Link to Pages',
        ]);
        foreach ([$response] as $line) {
            $cols = [
                $line['first_name'],
                $line['last_name'], 
                $line['email'],
                $line['phone'],
                $line['link_to_pages']
            ];
            fputcsv($file, $cols);
        }
        fclose($file);
        return ;
    }

}
