<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;
use App\Models\Api;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Pagination\Paginator;
class TradesController extends BaseResourceController
{
    const
        TEMPLATE_PATH = '/trades',
        ROUTE_PREFIX = 'trades',
        RESPOND_TO_AJAX = true;

    public function index(Request $request)
    {
        if ($request->has('page')) {
            $data = json_decode(Api::get('api/v1/trades?page='.$request->get('page')));
        } else {
            $data = json_decode(Api::get('api/v1/trades'));
        }

        $trades = json_decode($data->data);
        $data = !empty($trades->data) ? $trades->data : [] ;
        $paginator = new LengthAwarePaginator($data, $trades->total, $trades->per_page, null, [
            'path' => Paginator::resolveCurrentPath()
        ]);

        return view(
            'trades.index', [
            'resources' => $data,
            'paginator' => $paginator
            ]
        );
    }

    public function store(Request $request)
    {
        $api = json_decode(Api::post('api/v1/trades', $request->all()));
        if ($api->response == 'success') {
            return Redirect::route('trades.index');
        } else{
            if (isset($api->errors)) {
                return Redirect::back()->withErrors($api->errors);
            }
            return Redirect::back()->with(
                $api->response,
                $api->message
            );
        }
    }

    public function edit(Request $request, $id)
    {
        $trade = json_decode(Api::get('api/v1/trades/'.$id));
        return view(
            'trades.edit', [
            'resource' => $trade->data,
            'options' => ['trade_groups' => static::getTradeGroupOptions()]
            ]
        );
    }

    public function update(Request $request, $id)
    {
        $api = json_decode(Api::put('api/v1/trades/'.$id, $request->all()));
        if ($api->response == 'success') {
            return Redirect::back();
        } else {
            return Redirect::back()->with(
                $api->response,
                $api->message
            );
        }
    }

    public function getRouteParams(Request $request, $view, array $params = [])
    {
        return array_merge(
            $params, [
            'options' => [
                'trade_groups' => static::getTradeGroupOptions()
            ]
            ]
        );
    }

    public function getAdditionalViewParams(string $view, $resource)
    {
        return array_merge(
            (array) $resource, [
            'options' => [
                'trade_groups' => static::getTradeGroupOptions()
            ]
            ]
        );
    }

    private static function getTradeGroupOptions()
    {
        return json_decode(
            Api::get(
                static::get_api_uri('options', 'trade-groupings')
            )
        );
    }

    public function options($trade_group)
    {
        $data = Api::get(
            static::get_api_uri('options/'.$trade_group, 'trades')
        );
        return $data ;
    }
}
