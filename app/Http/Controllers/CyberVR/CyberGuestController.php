<?php

namespace App\Http\Controllers\CyberVR;

use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use App\Models\Api;
use App\Helpers\Helpers;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\URL;
class CyberGuestController extends BaseController
{
    const TOKEN_KEY = 'CYBERVR::_ID';
    const ACCEPTABLE_DOMAINS = [
        'libertymutual.com',
        'libertyIU.com',
        'libertyglobalgroup.com',
        'libertyspecialitymarkets.uk.com',
        'LibertyGlobalGroup.com',
        'fastfwd.com',
        'fast-fwd.co.uk',
    ];

    /* Public Properties
    -----------------------------------------------------------*/
    /* Protected Properties
    -----------------------------------------------------------*/
    protected $personId = null;

    /* Magic Methods
    -----------------------------------------------------------*/
    public function __construct()
    {
        $this->personId = Config::get('app.cyber_virtual_rooms.jennifer_id');
    }

    /* Public Methods
    -----------------------------------------------------------*/
    public function authenticate(Request $request)
    {
        $token = $request->get('token');
        $personId = Helpers::decryptString($token, self::TOKEN_KEY);
        /**TEMPORARY FORCE LOGIN CYBER VR */
        $personId = '640aec823123993dcd5e1a02'; // Jennifer id
        if (Config::get('app.cyber_virtual_rooms.jennifer_id') && $personId == Config::get('app.cyber_virtual_rooms.jennifer_id')) {
            Session::put('can-access-guest-cyber-vr', true);
            return Redirect::route('virtual-rooms.cyber-vr');
        }

        return View::make('errors.404');
    }

    public function index()
    {
        $response = json_decode(Api::get('api/v1/cyber-vr/theme-rooms/' . $this->personId));
        if (!empty($response->data) && $response->data->cms->office_title === 'N/A') {
            $office = json_decode(Api::get('api/v1/cms/get-office/' . $this->personId), true);
            $response->data->cms->office_title = $office;
        }

        $data['data']         = $response->data;
        $data['cms']          = $response->data->cms;
        $data['title']        = $response->data->title;
        $data['description']  = $response->data->description;

        return View::make('cyber-vr.guests.index', $data);
    }

    public function space($roomCode)
    {
        if (!$roomCode) {
            Session::flash('failure', json_encode(['title' => 'Warning', 'message' => 'This meeting room has been deleted. Please contact the meeting host.']));

            return redirect()->route('virtual-rooms.cyber-vr');
        }

        $response = json_decode(Api::get('api/v1/cyber-vr/theme-rooms/' . $roomCode . '/' . $this->personId));

        if (!$response || empty($response->data)) {
            Session::flash('failure', json_encode(['title' => 'Warning', 'message' => 'This meeting room has been deleted. Please contact the meeting host.']));

            return redirect()->route('virtual-rooms.cyber-vr');
        }

        if ($response->response !== 'success') {
            return View::make('errors.404');
        }

        $room               = (object) $response->data->room;
        $creator            = isset($response->data->room->cms) ? $response->data->room->cms : [];
        $upcomingSchedules  = json_decode(json_encode($response->data->upcoming_schedules),true);
        $joinRoomLink       = isset($response->data->join_room_link) ? $response->data->join_room_link : "#";

        return View::make('cyber-vr.guests.space', compact(
            'room',
            'creator',
            'upcomingSchedules',
            'joinRoomLink'
        ));
    }

    public function downloadIcs(Request $request, $roomTypeId, $roomId)
    {
        $data = $request->all();
        $data['person_id'] = $this->personId;
        $data['static_download'] = ($request->has('static_download')) ? $request->get('static_download') : 1;

        $url = sprintf('api/v1/virtual-rooms/%s/%s/download-ics', $roomTypeId, $roomId);
        $response = json_decode(Api::post($url, $data), true);

        $hasError = $response['response'] === 'error';

        $status = $hasError ? 'failure' : 'success';
        $title = $hasError ? 'Failed' : 'Successfully Downloaded';
        $message = $hasError ? 'Failed.' : 'Your meeting ics has been downloaded.';

        if (!isset($response['data']['ics']) || $hasError) {
            $title = "Failed";
            $message = "Unable to download ics file.";
            if(isset($response['message'])) {
                $message = $response['message'];
            }
            Session::flash($status, json_encode(['title' => $title, 'message' => $message]));

            return Redirect::to(URL::previous());
        }

        $ics = $response['data']['ics'];

        return Response::make($ics)
            ->header("Content-type","text/calendar; charset=utf-8")
            ->header("Content-disposition","attachment; filename=\"meeting.ics\"");
    }

    public function login()
    {
        if (Session::has('can-access-guest-cyber-vr') && Session::get('can-access-guest-cyber-vr')) {
            return Redirect::route('virtual-rooms.cyber-vr');
        }

        return View::make('cyber-vr.guests.login', ['body_class' => 'cyberguest-login']);
    }

    public function generateLink(Request $request)
    {
        $data      = $request->all();
        $validator = $this->validateEmail($data);

        if ($validator->fails()) {
            return Redirect::back()->withErrors($validator)->withInput($request->old());
        }

        // generate Magic Link
        $response = json_decode(Api::post('api/v1/virtual-rooms/cyber-vr/generateLink', $data));

        if (
            isset($response->response) &&
            !empty($response->response) &&
            $response->response==="success"
        ) {
            return Redirect::route('cyber-vr.login')
                ->with(
                    'success-magic-link',
                    '<p>We have sent an email to the address registered with this account containing the magic link to access the Virtual Rooms dashboard.</p>'
                );
        }

        if (
            isset($response->response) &&
            !empty($response->response) &&
            $response->response==="fail"
        ) {
            return Redirect::route('cyber-vr.login')
                ->with(
                    'error-magic-link',
                    $response->message
                );
        }

        return Redirect::route('cyber-vr.login')
            ->with(
                'error',
                'Ooops.. Something went wrong...'
            );
    }

    /* Protected Methods
    -----------------------------------------------------------*/
    /* Private Methods
    -----------------------------------------------------------*/
    private function validateEmail($data)
    {
        $rules = [
            'email' => [
                'required',
                'email',
                'regex:/.*@(?:' . implode('|', self::ACCEPTABLE_DOMAINS) . ')/m',
            ]
        ];

        $validator = Validator::make($data, $rules);

        return $validator;
    }

    /* Public Static Methods
    -----------------------------------------------------------*/
}
