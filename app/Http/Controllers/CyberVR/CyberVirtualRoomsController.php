<?php

namespace App\Http\Controllers\CyberVR;

use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Session;
use App\Models\Api;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Auth;

class CyberVirtualRoomsController extends BaseController
{
    /* Traits
    -----------------------------------------------------------*/
    /* Constants
    -----------------------------------------------------------*/
    /* Public Properties
    -----------------------------------------------------------*/
    /* Protected Properties
    -----------------------------------------------------------*/
    private $allowedUserIds = [ // <PERSON> and <PERSON> IDs
        '60f1808b31239915bf3c1962',
        '640aec823123993dcd5e1a02'
    ]; // Change to Matt ID

    /* Magic Methods
    -----------------------------------------------------------*/
    /* Public Methods
    -----------------------------------------------------------*/
    public function index()
    {
        $authUserId = !empty(Session::get('socials-user')) ? Session::get('socials-user')['person_id'] : null;
        if ($authUserId && !in_array($authUserId, $this->allowedUserIds)) { // Matthew Hogg ID
            abort(403, 'Unauthorized page.');
        }

        $cyberVR = "";
        if (Session::has('can-access-cyber-vr') && (bool) Session::get('can-access-cyber-vr')) {
            $cyberVR = "&can_access_cyber_vr=true";
        }

        $response = json_decode(Api::get('api/v1/virtual-rooms/theme-rooms?person_id=' . $authUserId . $cyberVR));
        $data['theme_rooms'] = $response->data;
        // $data['pinnedRooms'] = $this->getPinnedRooms();

        return View::make('cyber-vr.admin.index', $data);
    }

    /* Protected Methods
    -----------------------------------------------------------*/
    /* Private Methods
    -----------------------------------------------------------*/
    /* Public Static Methods
    -----------------------------------------------------------*/
}
