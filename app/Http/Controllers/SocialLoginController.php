<?php

namespace App\Http\Controllers;

use Laravel\Socialite\Facades\Socialite;
use SocialiteProviders\Manager\Config;

class SocialLoginController extends Controller
{
    private const ALLOWED_PROVIDERS = ['azure'];

    public function redirect(string $provider)
    {
        if (in_array($provider, self::ALLOWED_PROVIDERS)) {
            return Socialite::driver($provider)
                ->setConfig($this->getConfig())
                ->redirect();
        }
        abort("Invalid provider: " . $provider);
    }

    public function callback(string $provider)
    {
        if (in_array($provider, self::ALLOWED_PROVIDERS)) {
            try {
                $socialUser = Socialite::driver($provider)
                    ->setConfig($this->getConfig())
                    ->user();

                // send the social user to API
                // API will create if not existing, or login if existing
                // API will return the user object, then we can login the user
                // API route is POST /auth/social/{provider}
            } catch (\Exception $e) {
                abort($e->getMessage());
            }
        }
        abort("Invalid provider: " . $provider);
    }

    /**
     * Returns a custom config for this specific Azure AD connection / directory
     * @return Config
     */
    private function getConfig(): Config
    {
        return new Config(
            env('AD_CLIENT_ID', 'some-client-id'), // a different clientID for this separate Azure directory
            env('AD_CLIENT_SECRET'), // a different secret for this separate Azure directory
            url(env('AD_REDIRECT_PATH', '/azuread/callback')),
            // the redirect path i.e. a different callback to the other azureAD callbacks
            [
                'tenant' => env('AD_TENTANT_ID', 'common'),
            ], // this could be something special if need be, but can also be left out entirely
        );
    }
}
