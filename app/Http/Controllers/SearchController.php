<?php

namespace App\Http\Controllers;

use App\Models\Api;
use Illuminate\Http\Request;

class SearchController extends BaseController
{
    public function search(Request $request)
    {
        $data = $request->all();
        $data['search_string'] = $request->get('q');
        $response = json_decode(Api::post('api/v1/search', $data));
        if($response->response == 'success') {
            return view(
                'search/results', [
                    'users' => $response->users,
                    'documents' => $response->documents,
                    'forms' => $response->forms,
                    'organisations' => $response->organisations,
                    'broker_users' => $response->broker_users,
                ]
            );
        }
    }

    public function results()
    {

    }

}
