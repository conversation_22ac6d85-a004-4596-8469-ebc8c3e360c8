<?php

namespace App\Http\Controllers;

use App\Models\Api;
use App\Services\CacheContent\GetBrokerService;
use App\Services\SendSqsMessageService;
use Illuminate\Http\Request;
use App\Http\Controllers\BaseResourceController;
use App\Services\CacheContent\GetBrokerOrgsOptionService;
use App\Services\CacheContent\GetOrgBrokerUserOptionService;
use App\Services\CacheContent\OptionListService;

class BrokerController extends BaseResourceController
{
    const
    TEMPLATE_PATH = '/brokers',
    ROUTE_PREFIX = 'brokers',
        RESPOND_TO_AJAX = true;

    public function __construct(Request $request)
    {
        BaseController::__construct($request);
    }

    public function schemes($id)
    {
        $data = json_decode(Api::get('api/v1/brokers/'.$id));
        if (isset($data)) {
            return view(
                'brokers.schemes', [
                'resource' => $data->data
                ]
            );
        }

        return view('mga-schemes')->with('error', 'Failed to find MGA Scheme');
    }

    public function getValidatorRules(Request $request, $method)
    {
        $rules = [
            'company_name' =>  'required',
            'address_1'    =>  'required',
            'postcode'     =>  'required',
            'city'         =>  'required',
            'country'      =>  'required',
            'email'        =>  'required',
            'phone_number' =>  'required',
        ];

        return $rules;
    }

    public static function onStoreSuccess($data)
    {
        self::resetBrokersOption();
        self::recacheRelatedData('');
    }

    public static function onUpdateSuccess($data)
    {
        self::recacheRelatedData($data['id']);
    }

    public static function onDestroySuccess()
    {
        self::resetBrokersOption();
        self::recacheRelatedData('');
    }

    public static function recacheRelatedData($id)
    {
        if (empty($id)) {
            return;
        }

        SendSqsMessageService::sendMessages([
            [
                'serviceClass' => GetBrokerService::class,
                'params' => $id ?? '',
            ],
            [
                'serviceClass' => OptionListService::class,
                'params' => 'brokers',
            ],
            [
                'serviceClass' => GetBrokerOrgsOptionService::class,
                'params' => $id ?? '',
            ],
            [
                'serviceClass' => GetOrgBrokerUserOptionService::class,
                'params' => $id ?? '',
            ],
        ]);
    }

    public static function resetBrokersOption()
    {
        SendSqsMessageService::sendMessages([
            [
                'serviceClass' => OptionListService::class,
                'params' => 'brokers',
            ],
        ]);
    }
}
