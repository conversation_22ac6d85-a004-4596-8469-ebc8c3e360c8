<?php

namespace App\Http\Controllers;

use Sabre\VObject;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Models\Api;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Response;
use App\Models\FileUpload;
use Illuminate\Support\Str;
use App\Models\Mailgun;
class SubscriptionController extends BaseController
{
    const TEMPLATE_PATH = '/subscriptions';

    public function __construct(FileUpload $fileUpload)
    {
        $this->file = $fileUpload;
    }

    public function suppressions($type = null)
    {
        if($type == null) {
            $suppressionList = json_decode(Api::get('/api/v1/suppressions'));
        } else {
            $suppressionList = json_decode(Api::get('/api/v1/suppressions/'.$type));
        }


        if($suppressionList->response == "success") {
            return view(
                static::TEMPLATE_PATH . '/'.$type,
                array(
                    'suppressions' => $suppressionList->suppressions
                )
            );
        }

    }

    public function deleteSuppression($token)
    {
        $delete = json_decode(Api::get('/api/v1/suppressions/email/delete/'.$token));
        if($delete->response == 'success') {
            return Redirect::back()->with('success', 'Suppression deleted.');
        } else {
            return Redirect::back()->with('error', 'Suppression could not be deleted.');
        }

    }

    public function receiveEmail(Request $request, $brand)
    {
        $data = $request->except('_token');
        //print_r($data); exit;
        $data['business'] = $brand;
        Log::info(json_encode($data));
        $data['parsed_data'] = $this->parseICS($request);

        // If the email sender domain is not valid/allowed
        if($data['parsed_data'] instanceof JsonResponse) {
            return $data['parsed_data'];
        }

        $store = Api::post('/api/v1/email-hook', $data);

        // TODO remove this after testing
        return Response::json(json_decode($store));

        return "success";
    }

    public function parseICS(Request $request)
    {
        // Get ics straight from post data if available
        if($request->has('body-calendar') && !empty($request->get('body-calendar'))) {
            $rawIcs = $request->get('body-calendar');
        }

        if($request->has('attachments')) {
            $attachmentLinks = $this->uploadValidFileAttachments($request->get('attachments'));
        } else {
            $attachmentLinks = "";
        }

        if(!isset($rawIcs) || empty($rawIcs)) {
            return ['error' => 'No ICS attachment found!'];
        }

        try {
            $icsObj = VObject\Reader::read($rawIcs);

            $method = (string)$icsObj->METHOD;

            if($method == 'COUNTER' || $method == 'REPLY') {
                // Get Organizer
                $uid = (string)$icsObj->VEVENT->UID;
                $organizerDetails = json_decode(Api::get('/api/v1/get-lib-rep/' . $uid));

                if($organizerDetails->response == 'success') {
                    $organizerName = $organizerDetails->data->name;
                    $organizerEmail = $organizerDetails->data->email;
                    $organizerId = $organizerDetails->data->id;
                }

                $senderEmail = strtolower($request->get('sender'));
                $repEmail = strtolower($organizerEmail);

                if(strpos($senderEmail, $repEmail) !== false) {
                } else{
                    return ['error' => 'Sender and organisers email do not match.'];
                }

            }


            $timezone = isset($icsObj->VEVENT->DTSTART['TZID']) ? (string)$icsObj->VEVENT->DTSTART['TZID'] : 'Europe/London';
            if(!$this->isValidTimezoneId($timezone)) {
                $timezone = 'Europe/London';
            }
            $bookingData = [
                'created_by_login_type' => 'client',
                'created_by_role' => 'client',
                'liberty_representative' => isset($organizerName) ? $organizerName : (string)$icsObj->VEVENT->ORGANIZER['CN'],
                'liberty_representative_email' => isset($organizerEmail) ? $organizerEmail : $this->cleanIcsEmail($icsObj->VEVENT->ORGANIZER),
                'created_by_id' => isset($organizerEmail) ? $organizerEmail : $this->cleanIcsEmail($icsObj->VEVENT->ORGANIZER),
                'meeting_subject' => (string)$icsObj->VEVENT->SUMMARY,
                'conference_date' => \Carbon\Carbon::parse($icsObj->VEVENT->DTSTART, $timezone)->setTimezone('Europe/London')->format('Y-m-d'),
                'time_start' => \Carbon\Carbon::parse($icsObj->VEVENT->DTSTART, $timezone)->setTimezone('Europe/London')->format('H:i'),
                'time_end' => \Carbon\Carbon::parse($icsObj->VEVENT->DTEND, $timezone)->setTimezone('Europe/London')->format('H:i'),
                'attached_files' => $attachmentLinks,
                'method' => (string)$icsObj->METHOD,
                'uid' => (string)$icsObj->VEVENT->UID,
                'status' => (string)$icsObj->VEVENT->STATUS,
                'timezone' => $timezone,
                'sequence' => (string)$icsObj->VEVENT->SEQUENCE
            ];

            if(isset($icsObj->VEVENT->{'RECURRENCE-ID'})) {
                $bookingData['recurrence_id'] = \Carbon\Carbon::parse($icsObj->VEVENT->{'RECURRENCE-ID'}, $timezone)->setTimezone('Europe/London')->format('Y-m-d H:i:s');
            }



            // dd($this->isValidDomain($bookingData['liberty_representative_email']), $bookingData);

            // TODO: Remove this: For testing only
            // TODO: Remove this: For testing only
            // TODO: Remove this: For testing only
            if(strpos(strtolower($bookingData['liberty_representative_email']), '<EMAIL>') !== false) {
                $bookingData['liberty_representative'] = 'Cristian Cardino';
                $bookingData['liberty_representative_email'] = '<EMAIL>';
                $bookingData['created_by_id'] = '<EMAIL>';
            }

            if(!$this->isValidDomain($bookingData['liberty_representative_email'])) {
                return Response::json(
                    [
                    'response' => 'fail',
                    'data' => ['Invalid domain.']
                    ]
                );
            }


            $guests = [];
            $guestsCount = 1;
            foreach($icsObj->VEVENT->ATTENDEE as $attendee) {
                // skip the invite@bookings attendee, this should not be included as guest
                if(strpos($this->cleanIcsEmail($attendee), 'invite@bookings') !== false || strpos($this->cleanIcsEmail($attendee), 'invite.stage@bookings') !== false) {
                    continue;
                }
                Log::info($method);

                if($method == 'REPLY' && isset($organizerEmail) && isset($attendee['PARTSTAT'])) {
                    $partstat = (string)$attendee['PARTSTAT'];
                    $email = $this->cleanIcsEmail($attendee);
                    Log::info($partstat);
                    Log::info($email);
                    if($partstat == 'DECLINED' && $organizerEmail == $email) {
                        $bookingData['method'] = 'CANCEL';
                    }
                }
                $guests['guest_clients']['guestClient' . $guestsCount] = (string)$attendee['CN'];
                $guests['guest_emails']['guestEmail' . $guestsCount] = $this->cleanIcsEmail($attendee);
                $guests['guest_clientPhones']['guest_clientPhone' . $guestsCount] = '';
                $guests['guest_clientCompanies']['guest_clientCompany' . $guestsCount] = '';

                $guestsCount++;
            }

            $guests['guest_clients'] = json_encode(isset($guests['guest_clients']) ? $guests['guest_clients'] : '');
            $guests['guest_emails'] = json_encode(isset($guests['guest_emails']) ? $guests['guest_emails'] : '');
            $guests['guest_clientPhones'] = json_encode(isset($guests['guest_clientPhones']) ? $guests['guest_clientPhones'] : '');
            $guests['guest_clientCompanies'] = json_encode(isset($guests['guest_clientCompanies']) ? $guests['guest_clientCompanies'] : '');

            $bookingData = array_merge($bookingData, $guests);

            return $bookingData;

        } catch (\Exception $e) {
            Log::error($e->getTraceAsString());
            return ['error' => 'Error when parsing ics file.', 'message' => $e->getMessage()];
        }
    }

    private function isValidTimezoneId($timezoneId)
    {
        $zoneList = timezone_identifiers_list(); // list of (all) valid timezones
        return in_array($timezoneId, $zoneList); // set result
    }

    private function isValidDomain($attendee)
    {
        $attendee = strtolower(trim($attendee));
        if(strpos($attendee, 'fastfwd.com') || strpos($attendee, 'fwd.co.uk') || strpos($attendee, 'libertyglobalgroup.com') || strpos($attendee, 'fastfwdco.onmicrosoft.com')) {
            return true;
        }
        return false;
    }


    private function cleanIcsEmail($email)
    {
        if(!$email) {
            return $email;
        }
        return str_replace('mailto:', '', (string)$email);
    }

    // Upload valid file attachments to cloud storage and then return cloud file name
    private function uploadValidFileAttachments($attachments)
    {
        if(empty($attachments)) {
            return [];
        }

        $attachments = json_decode($attachments);
        $validFileAttachments = $this->getValidFileAttachments($attachments);
        $attachmentLinks = [];
        foreach ($validFileAttachments as $key => $attachment) {
            $filename = $attachment->name;

            // Download attachment from mailgun
            $mailgunFile = Mailgun::get($attachment->url);
            // Save temporarily to the temp folder
            $tempFileUrl = tempnam(sys_get_temp_dir(), $filename);
            file_put_contents($tempFileUrl, $mailgunFile);

            $uuid = Str::uuid()->toString();
            $cloudFile = 'lets-talk/'.$uuid.'/'. $filename;

            $uploadedFile = $this->file->upload($tempFileUrl, $cloudFile, "vr_bucket");
            $attachmentLinks[] =  [$filename => $cloudFile];

            // Delete temporary file
            unlink($tempFileUrl);
        }

        return $attachmentLinks;
    }

    private function getValidFileAttachments($attachments)
    {
        // mimes:pdf,doc,docx,ppt,pptx,xls,xlsx,tiff,png,gif,jpeg
        $validContentTypes = [
            'application/pdf',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.template',
            'application/vnd.ms-powerpoint',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'image/tiff',
            'image/png',
            'image/gif',
            'image/jpeg'
        ];

        $validAttachments = [];

        foreach($attachments as $attachment) {
            if (in_array($attachment->{'content-type'}, $validContentTypes)) {
                $validAttachments[] = $attachment;
            }
            // Max 5 file attachments only
            if(count($validAttachments) === 5) {
                break;
            }
        }

        return $validAttachments;
    }

    public function unsubscribeCommunityEmail($token)
    {
        return view(
            'community.unsubscribe',
            [
                'token' => $token,
                'options' => (isset($optionsResponse->data->options)) ? $optionsResponse->data->options : new \stdClass
            ]
        );
    }

    public function confirmUnsubscribeCommunityEmail($token)
    {
        $store = json_decode(Api::get('/api/v1/unsubscribe-email/'.$token));


        if ($store->response==='success') {
            return view(
                'community.unsubscribed',
                [
                    'options' => (isset($optionsResponse->data->options)) ? $optionsResponse->data->options : new \stdClass,
                ]
            );
        }
    }

}
