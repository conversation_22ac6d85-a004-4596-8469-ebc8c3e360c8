<?php

namespace App\Http\Controllers;

use App\Models\PublicForm;
use App\Models\User;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Request;
use App\Models\Documents;
use App\Models\FileUpload;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Response;
use App\Models\Api;
use App\Services\CacheContent\GetOrganisationService;
use App\Services\CacheContent\GetOrgRiskGradingData;
use App\Services\SendSqsMessageService;
use Illuminate\Support\Facades\Redirect;

class FormSubmissionsController extends BaseController
{
    const PAGE_CONTROLLER = 200;

    public function __construct(Request $request, Documents $doc, FileUpload $fileUpload)
    {
        parent::__construct($request);
        $this->documents = $doc;
        $this->files = $fileUpload;
    }

    public function allowForUser(User $user): bool
    {
        $routeName = request()->route()->getName();
        if (in_array($user->login_type, ['risk-control', 'underwriter', 'risk-engineer'])) {
            return true;
        }
        
        if ($user->login_type === 'broker-user' && in_array($routeName, config('app.allowed-routes.broker-user'))) {
            return true;
        }

        return false;
    }

    private function pageNumber($requests)
    {
        $start = $requests['start'] ?? 0;
        $length = $requests['length'] ?? $this::PAGE_CONTROLLER;
        $page =  $start / $length;

        return $page;
    }

    /*
     * List of all submissions for a form
     */
    public function submissions($form)
    {
        return view(
            'forms/submissions',
            [
                'public' => false,
                'form_id' => $form,
            ]
        );
    }

    public function ajaxSubmissions($form_id)
    {
        $response = json_decode(Api::get('api/v1/form/private/ajax-submissions/'.$form_id));
        return response()->json([
            'data' => $response->data,
        ]);
    }

    /*
    * Edit a submission
    */
    public function edit(Request $request, $submission)
    {
        $data = $request->all();
        $response = json_decode(Api::put('api/v1/form-submission/'.$submission, $data));
        if ($response->response == "success") {
            $this->recacheRelatedData($data['organisation_id'] ?? '');
            return Redirect::back();
        } else {
            die('An error occurred, please try again');
        }
    }

    /*
    * update a form submission
    */
    public function update($submissionID)
    {
        // submit
    }

    /*
    * view a submission
    */
    public function show($submission)
    {

        $response = json_decode(Api::get('api/v1/form-submission/'.$submission));
        $submission = json_decode($response->data);

        $submission->json = $response->data;

        if (isset($submission->is_public)) {
            $response = json_decode(Api::get('api/v1/form/public/'.$submission->form_id));
        } else {
            $response = json_decode(Api::get('api/v1/form/'.$submission->form_id));
        }
        if ($response->response == "success") {
            $form = json_decode($response->data, true);
            $branch = array();
            $branch_options = '';
            if ($form['formType'] == 'accident-reporting') {
                foreach ($form['fields'] as $field) {
                    foreach ($field as $key => $value) {
                        if ($key == 'branch') {
                            $string_val = 'branch-where-accident-happened';
                            $branches = json_decode(Api::get('api/v1/user/org-branch/'.$submission->organisation_id));
                            if ($branches->response == 'success') {
                                $branchUsers = $branches->data;

                                foreach ($branchUsers as $branchUser) {
                                    $branch[$branchUser->id] = $branchUser->branch_name;
                                    if (isset($submission->$string_val) && $submission->$string_val == $branchUser->id) {
                                        $branch_options .= '<option value = \''.$branchUser->id.'\' selected>'.$branchUser->branch_name.'</option>';
                                    } else {
                                        $branch_options .= '<option value = \''.$branchUser->id.'\'>'.$branchUser->branch_name.'</option>';
                                    }
                                }

                            }
                        }
                    }
                }
            }
            $form['fields'] = json_encode($form['fields']);

            $submission_details = json_decode(Api::get('api/v1/user/userorgdetails/'.$submission->user_id));
            //print_r($submission_details); exit;
            if ($submission_details->response == "success") {
                if (isset($submission_details->organisation->name)) {
                    $submission_details_data = [
                        'organisation_name' => $submission_details->organisation->name,
                        'user' => $submission_details->user->email,
                    ];
                } else {
                    $submission_details_data = [
                        'organisation_name' => 'Deleted Organisation',
                        'user' => $submission_details->user->email,
                    ];
                }
            } else {
                $submission_details_data = [
                    'organisation_name' => '',
                    'user' => 'Deleted user',
                ];
            }

            return view(
                'submissions/show',
                [
                    'form' => $form,
                    'submission' => $submission,
                    'branches' => $branch,
                    'branch_options' => $branch_options,
                    'submission_details' => $submission_details_data
                ]
            );
        } else {
            die('An error occurred, please try again');
        }


        /*$response = json_decode( Api::get('api/v1/form-submission/'.$submission) );
        $submission = json_decode($response->data);
        $form = json_decode(json_decode( Api::get('api/v1/form/'.$submission->form_id) )->data, true);
        $fields = [] ;
        foreach($form['fields'] as $k => $f)
        {
            $fields[$f[key($f)][1]['value']] = $f[key($f)][0]['value'] ;
        }
        $submissionId = $submission->_id ;
        unset($submission->form_id, $submission->_id) ;
        if($response->response == "success")
        {
            return view(
              'submissions/show',
              [
                'fields' => $fields,
                'submission' => $submission,
                'submissionId' => $submissionId
            ]
        );
        }
        else
        {
          die('An error occurred, please try again') ;
        }*/
    }

    /*
    * Delete a submission
    */
    public function delete($submission)
    {
        $response = json_decode(Api::delete('api/v1/form-submission/'.$submission));
        if ($response->response == "success") {
            return Redirect::back();
        } else {
            die('An error occurred, please try again');
        }
    }


    /*
     * GET: get attachments from rackspace
     */

    public function retrieveAttachment($name)
    {
        $document = json_decode(Api::get('api/v1/attachment/find/'.$name));

        if ($document->response == 'success') {

            $fileName = $document->submission->organisation_id.'/'.$document->data->document_store_name;
            $file = $this->documents->download($fileName, $name);
            if ($file['response'] == 'success') {
                $decrypt = $this->documents->decrypt($file['data'], $name);
            } else {
                return Response::json(['response' => 'error', 'message' => $file['message']]);
            }
            $fileName = $document->data->document_title;
            return Response::download($decrypt['data'], $fileName, array('Content-Type' => 'text/plain'));
        }
        return Redirect::back()->withErrors('The document does not exist.');
    }

    public function showPdf($submission,$ispublic=null)
    {
        if(isset($ispublic) && $ispublic=='public') {
            $response = json_decode(Api::get('api/v1/form/public/submission/'.$submission));
        }else{
            $response = json_decode(Api::get('api/v1/form-submission/'.$submission));
        }
        $submission = json_decode($response->data);



        $submission->json = $response->data;

        if (isset($submission->is_public)) {
            $response = json_decode(Api::get(PublicForm::PUBLIC_FORM_API_ENDPOINT.'/'.$submission->form_id));
        } else {
            $response = json_decode(Api::get('api/v1/form/'.$submission->form_id));
        }
        if ($response->response == "success") {
            $form = json_decode($response->data, true);

            $form['fields'] = isset($form['fields'])?json_encode($form['fields']):null;
            $user = Session::has('user') ? Session::get('user') : null;
            $branch_image = '';

            if (Session::has('org_logo') && !empty(Session::get('org_logo'))) {
                $branch_image = $this->file->link(Session::get('org_logo'));
            }

            if ($user && isset($user->file) && !empty($user->file)) {
                $branch_image = $this->file->link($user->file);
            }

            $submission->ispublic=$ispublic;

            $data = [
                'form' => $form,
                'submission' => $submission,
                'branch_image' => $branch_image,
            ];

            return view('submissions/show_print', $data);
        } else {
            die('An error occurred, please try again');
        }
    }

    public function printPdf(Request $request)
    {
        $html = stripslashes($request->get('html'));
        $orientation = $request->get('orientation');
        $pdf = Pdf::loadHTML($html)->setPaper('a4', $orientation);
        return $pdf->stream();
    }

    public function recacheRelatedData($orgId)
    {
        if (empty($orgId)) {
            return;
        }
        SendSqsMessageService::sendMessages([
            [
                'serviceClass' => GetOrgRiskGradingData::class,
                'params' => $orgId ?? '',
            ],
            [
                'serviceClass' => GetOrganisationService::class,
                'params' => $orgId ?? '',
            ],
            [
                'serviceClass' => 'App\Services\CacheContent\GetOrganisationDetailsForDashboard',
                'params' => $orgId ?? '',
                'isClient' => true,
            ],
        ]);
    }
}
