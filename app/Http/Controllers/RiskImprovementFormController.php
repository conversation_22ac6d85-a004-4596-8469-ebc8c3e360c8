<?php

namespace App\Http\Controllers;

use App\Helpers\RiskGradingHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Models\Api;
use App\Services\CacheContent\GetOrgRiskGradingData;
use App\Services\CacheContent\OptionListService;
use App\Services\SendSqsMessageService;
use App\Traits\HelperTrait;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Response;

class RiskImprovementFormController extends BaseController
{
    use HelperTrait;
    /*
    * List of all forms
    */
    public function index(Request $request)
    {
        return view('ri_form/index');

        // $page = $request->get('page');
        // $page = isset($page) ? $request->get('page') : 1 ;
        // $limit = Session::has('limit') ? Session::get('limit') : 10;

        // $search = $request->Get('search');

        // if(isset($search) && $search != '') {
        //     $response = json_decode(Api::get('api/v1/risk-improvement/form/'.$page.'/'.$limit . '?search='.urlencode($search)));
        // }
        // else
        // {
        //     $response = json_decode(Api::get('api/v1/risk-improvement/form/'.$page.'/'.$limit));
        // }

        // //print_r($response);exit;

        // if($response->response == "success") {
        //     return view(
        //         'ri_form/index',
        //         array(
        //             'request'       => $request,
        //             'forms'         => $response->data,
        //             'total'         => $response->total,
        //             'limit'         => $limit,
        //             'search'        => $search,
        //             'page'          => $page,
        //             'link'          => 'ri_form.index',
        //             'public'        => false,
        //             'form_id'       =>'57f3bebed2b59b5e37b528ef',
        //             'type'          =>'protected'
        //         )
        //     );
        // }
    }

    /*
    * List of all forms
    */

    public function listAllForms()
    {
        $response = json_decode(Api::get('api/v1/risk-improvement/form/list-all'));
        return response()->json([
            'data' => $response->data,
        ]);
    }

    public function surveyorForms(Request $request, $surveyor_id)
    {
        $page = $request->get('page');
        $page = isset($page) ? $request->get('page') : 1 ;
        $limit = Session::has('limit') ? Session::get('limit') : 10;

        $search = $request->Get('search');

        if(isset($search) && $search != '') {
            $response = json_decode(Api::get('api/v1/risk-improvement/form/'.$page.'/'.$limit . '?search='.urlencode($search).'&surveyor_id='.$surveyor_id));
        }
        else
        {
            $response = json_decode(Api::get('api/v1/risk-improvement/form/'.$page.'/'.$limit.'&surveyor_id='.$surveyor_id));
        }

        //print_r($response);exit;

        if($response->response == "success") {
            return json_encode($response);
        }
    }

    public function mobile_getSurveyforms()
    {
        $logged_in = Api::check_key("external-surveyors");

        if(!$logged_in || json_decode($logged_in)->response != 'success') {
            return Response::json(
                [
                'response' => 'error',
                'message'  => 'Unable to login member'
                ],
                200
            );
        }
        $response = json_decode(Api::get('api/v1/risk-improvement/form/1/1000'));
        return Response::json($response);

    }



    public function form_cache(Request $request, $form_id)
    {
        if (strtolower($request->method()) == 'post') {
            $form_html = $request->get('form_html');
            $result = Cache::forever($form_id, $form_html);
            return Redirect::back()->with('success', 'Form cached!');
        } else {
            if (Cache::has('key')) {
                $form_html = Cache::get($form_id);
                return Response::json(
                    [
                    'response'  =>  'success',
                    'message'   =>  $form_html
                    ]
                );
            }
            return Response::json(
                [
                'response'  =>  'error',
                'message'   =>  'Form not found'
                ]
            );
        }

    }



    /*
    * List of all forms
    */
    public function mobile_surveyorForms(Request $request, $surveyor_id)
    {
        $logged_in = Api::check_key("external-surveyors");

        if(!$logged_in || json_decode($logged_in)->response != 'success') {
            return Response::json(
                [
                'response' => 'error',
                'message'  => 'Unable to login member'
                ],
                200
            );
        }
        $page = $request->get('page');
        $page = isset($page) ? $request->get('page') : 1 ;
        $limit = Session::has('limit') ? Session::get('limit') : 10;

        $search = $request->get('search');

        if(isset($search) && $search != '') {
            $response = json_decode(Api::get('api/v1/risk-improvement/form/'.$page.'/'.$limit . '?search='.urlencode($search).'&surveyor_id='.$surveyor_id));
        }
        else
        {
            $response = json_decode(Api::get('api/v1/risk-improvement/form/'.$page.'/'.$limit.'?surveyor_id='.$surveyor_id));
        }
        //print_r($response);exit;

        if($response->response == "success") {
            return json_encode($response);
        }
    }

    /*
    * get a form
    */
    public function mobile_surveyorForm($form_id)
    {
        $logged_in = Api::check_key("external-surveyors");

        if(!$logged_in || json_decode($logged_in)->response != 'success') {
            return Response::json(
                [
                'response' => 'error',
                'message'  => 'Unable to login member'
                ],
                200
            );
        }

        $response = json_decode(Api::get('api/v1/risk-improvement/form/'.$form_id));

        if(isset($response->response) && $response->response == 'success') {

            return json_encode($response);


        }

    }

    /*
    * List of all claims submissions
    */
    public function claims()
    {
        $response = json_decode(Api::get('api/v1/form/submitted_forms/accident_claims'));
        if($response->response == "success") {
            $forms = json_decode($response->data);
            //print_r($forms);exit;
            return view(
                'forms/claims',
                array('forms' => $forms
                )
            );
        }
        else
        {
            die('An error occurred');
        }
    }

    /*
    * Create a form
    */
    public function create( $copyForm = null )
    {
        $data = [] ;

        // copy a form
        if(!is_null($copyForm) ) {
            $response = json_decode(Api::get('api/v1/risk-improvement/form/'.$copyForm));
            $form = json_decode($response->data, true);
            //print_r($response); exit;
            $form['fields'] = json_encode($form['fields']);
            $form['section_names'] = $form['section_names']  ;
            $form['name'] = $form['name']  ;
            $form['formType'] = isset($form['formType'])? $form['formType'] : '' ;
            $data['form'] = $form ;
            return view(
                'ri_form/copy', $data
            );
        }
        

        return view(
            'ri_form/create', $data
        );
    }

    /*
    * Save a form
    */
    public function store(Request $request)
    {
        $data['form'] = $request->get('form');
        $data['user_id'] = Session::get('user')->id;

        $response = json_decode(Api::post('api/v1/risk-improvement/form', $data));
        if ($response->response == "success") {
            $this->resetFormOptionCache();
            echo $response->data;
        }
    }

    /*
    * Redirect with success
    */
    public function success( )
    {
        return Redirect::to('/forms')
                     ->with('success', 'Form created successfully');
    }

    /*
    * Update a form
    */
    public function update(Request $request, $formID )
    {
        $data['form'] = $request->get('form');
        $data['user_id'] = Session::get('user')->id;
        $response = json_decode(Api::put('api/v1/risk-improvement/form/' . $formID, $data));
        //print_r(Api::put('api/v1/risk-improvement/form/' . $formID, $data)); exit;
        if ($response->response == "success") {
            $this->resetFormOptionCache();
            echo $response->data;
        }
    }

    /*
    * Edit a form
    */
    public function edit( $form )
    {
        $response = json_decode(Api::get('api/v1/risk-improvement/form/'.$form));

        //print_r($response); exit;

        if($response->response == "success") {
            $form = json_decode($response->data, true);

            $form['fields'] = json_encode($form['fields']);
            return view(
                'ri_form/edit',
                [
                'form' => $form
                ]
            );
        }
        else
        {
            die('An error occurred, please try again');
        }
    }

    public function riskImprovementAutoSaveData(Request $request)
    {
        $data = $request->all();

        Cache::forever('temp-data-'.$data['form_id'].'-'.$data['survey_id'], $data);

        return Response::json([
            'response' => 'success',
        ]);
    }

    public function riskGradingFields(Request $request)
    {
        $user = Session::get('user');
        $data = $request->all();
        $form = $data['form_id'];
        $survey_id = $data['survey_id'];

        $response = json_decode(Api::get('api/v1/risk-improvement/form/' . $form . '?survey_id=' . $survey_id . '&user_id=' . $user->id));

        if($response->response == "success") {
            $form = json_decode($response->data, true);
            $riskGradingAttributes = $form['risk_grading_attributes'];

            return Response::json([
                'response' => 'success',
                'data' => $riskGradingAttributes
            ]);
        }
    }

    /*
    * Preview a form
    */
    public function show( $form, $survey_id = "0" )
    {
        if (Cache::has('temp-data-'.$form.'-'.$survey_id)) {
             $tempData = Cache::get('temp-data-'.$form.'-'.$survey_id);
             $requestData = new \Illuminate\Http\Request($tempData);
             $submissionData = $this->createsubmissionTempData($requestData);
             $submmissionId = $submissionData->_id;
             $form = request()->query('form');
             if ($form == 'csr' || $form == 'uwr') {
                 return redirect('/risk-improvement/form/submission/'.$submmissionId.'?form='.$form.'&'.$form.'_status=completed');
             }
        }

        $user     = Session::get('user');
        $autosave = $survey_id != '0' ? "enabled" : 'disabled';
        $response = json_decode(Api::get('api/v1/risk-improvement/form/' . $form . '?survey_id=' . $survey_id . '&user_id=' . $user->id));

        $surveyInfoURI = $survey_id != '0' ? 'api/v1/survey/info/' . $user->id . '/' . $form . '/' . $survey_id :
            'api/v1/survey/info/' . $user->id . '/' . $form;
        $surveyInfo = json_decode(Api::get($surveyInfoURI));

        if ($surveyInfo->response != "success" && $user->type != 'liberty-user') {
            die('An error occurred, please try again. Error Code - RIS01');
        }

        $form_id = $form;
        if($response->response == "success") {
            if($survey_id != '0') {
                $checkIfInProgress = json_decode(Api::get('api/v1/survey-submission/survey/' . $survey_id ));
                if($checkIfInProgress->response == "success") {
                    $csrStatus = data_get($checkIfInProgress, 'data.csr_status', 'completed');
                    $form = request()->query('form');
                    if ($form == 'csr' || $form == 'uwr') {
                        return Redirect::to('/risk-improvement/form/submission/' . $checkIfInProgress->data->_id . '?form='.$form.'&'.$form.'_status=' . $csrStatus);
                    }
                }
            }
            $form                  = json_decode($response->data, true);
            $riskGradingAttributes = $form['risk_grading_attributes'];
            $file_data             = [];
            $rr_data               = [];
            $sections              = explode(',', $form['section_names']);

            $form['gradings_description'] = RiskGradingHelper::transformDataDescription($form['gradings_description']);

            if(isset($form) && isset($form['fields'])) {
                $file_data_in = [];
                foreach ($form['fields'] as $field_types) {
                    foreach ($field_types as $field_type => $field_attr) {
                        $file_data_in_name = 'no-name';
                        $file_data_in = [];
                        if($field_type == 'file' || $field_type == 'risk_recommendation') {
                            foreach ($field_attr as $element) {
                                $file_data_in[$element['name']] = $element['value'];
                                $file_data_in['field_type'] = $field_type;
                                if($element['name'] == 'name') {
                                             $file_data_in_name = $element['value'];
                                }
                            }
                            $file_data[$file_data_in_name] = $file_data_in;
                        }
                        if($field_type == 'risk_recommendation') {
                            foreach ($field_attr as $element) {
                                if($element['name'] == 'name') {
                                             array_push($rr_data, $element['value']);
                                }
                            }
                        }
                    }
                }
            }

            foreach ($file_data as $key => $value) {
                if($value["field_type"] == 'risk_recommendation') {
                    for($i = 1; $i <= 15; $i++) {
                        $old_key = $key;
                        $new_key = $key . "_" . $i;
                        $new_value = $value;
                        $new_value["name"]   = $value["name"] . "_" . $i;
                        $file_data[$new_key] = $new_value;
                    }
                    unset($file_data[$old_key]);
                }
            }

            $form['fields'] = json_encode($form['fields']);
            $data = [
                'form'             => $form,
                'file_data'        => $file_data,
                'sections'         => $sections,
                'form_id'          => $form_id,
                'rr_data'          => $rr_data,
                'survey_details'   => isset($surveyInfo->data) ? $surveyInfo->data : [],
                'autosave'         => $autosave,
                'survey_id'        => $survey_id
            ];
            // dd($data);
            if ($survey_id == '0') {
                $view = 'ri_form/show_old';
            } else {
                $view = 'ri_form/show';
                $data['risk_grading_attributes'] = $riskGradingAttributes;
            }

            return view($view, $data);
        } else {
            die('An error occurred, please try again');
        }
    }

    /*
    * Delete a form
    */
    public function delete( $form )
    {
        $response = json_decode(Api::delete('api/v1/risk-improvement/form/'.$form . '?user_id=' . Session::get('user')->id));
        if($response->response == "success") {
            $this->resetFormOptionCache();
            return Redirect::route('ri_form.index')->with('success', 'Form deleted successfully');
        }
        else
        {
            die('An error occurred, please try again');
        }
    }

    /**
     * Confirms updated successfully
     */
    public function updated()
    {
        return Redirect::route('ri_form.index')->with('success', 'Form updated successfully');
    }

    /**
     * save form
     */
    public function createsubmissionTempData(Request $request)
    {
        $formId = $request->get('form_id');
        $form_data = $request->all();

        $form_data['submitted'] = '0';
        $data['submission'] = json_encode($form_data);
        $data['surveyor_id'] = $request->has('surveyor_id') ? $request->get('surveyor_id') : Session::get('user')->id;
        $data['survey_id']  = $request->get('survey_id');
        $data['form_id'] = $formId;
        $data['autosave'] = 1;

        // Preview form only
        if ($data['survey_id']==0) {
            return Redirect::back()->with('error', 'No data stored preview only...');
        }

        // print_r(Api::post('api/v1/survey-submission', $data)); exit;
        $response = json_decode(Api::post('api/v1/survey-submission', $data));
        return $response;
    }

    /**
     * save form
     */
    public function createsubmission(Request $request)
    {
        $formId = $request->get('form_id');
        $form_data = $request->all();

        $form_data['submitted'] = '0';
        $data['submission'] = json_encode($form_data);
        $data['surveyor_id'] = $request->has('surveyor_id') ? $request->get('surveyor_id') : Session::get('user')->id;
        $data['survey_id']  = $request->get('survey_id');
        $data['form_id'] = $formId;
        $data['autosave'] = $request->ajax() ? 1 : 0;

        // Preview form only
        if ($data['survey_id']==0) {
            return Redirect::back()->with('error', 'No data stored preview only...');
        }

        $csrExternalEmails = isset($form_data['csr_external_emails']) ? json_decode($form_data['csr_external_emails']) : [];
        $data['csr_external_emails'] = $csrExternalEmails;

        $response = json_decode(Api::post('api/v1/survey-submission', $data));

        date_default_timezone_set("Europe/London");

        $message = isset($data['csr_status']) && $data['csr_status'] === 'submitted' ? 'Survey Submitted' : 'Form saved';
        if($response->response == 'success') {
            $this->clearRelatedCache($data['survey_id'] ?? '');

            if($request->ajax()) {
                return Response::json(['response' => 'success', 'message' => 'Form last auto saved at '.date('h:i a', time()), '_id' => $response->_id]);
            }

            if ($response->type == 'dtr') {
                return Redirect::to('/dtr')->with('success', $message);
            }

            if($response->type == 'survey') {
                $this->clearRelatedCache($data['survey_id'] ?? '');

                $create_user_data = (array)$response->create_user;
                // print_r((array)$response->create_user); exit;
                if(!empty((array)$response->create_user)) {
                    if(isset($create_user_data['branch'])) {
                        $create_user_data['branch'] = 1;

                    }
                    else
                    {
                        $create_user_data['branch'] = 0;
                    }
                    if(isset($create_user_data['manager'])) {
                        $create_user_data['manager'] = 1;
                    }
                    else
                    {
                        $create_user_data['manager'] = 0;
                    }

                    if(isset($create_user_data['optionRadios']) && $create_user_data['optionRadios'] == 'triton_access') {
                        $create_user_data['triton_access'] = 1;
                    }
                    else{
                        $create_user_data['triton_access'] = 0;
                    }

                    if (isset($create_user_data['astutis_access'])) {
                        $create_user_data['astutis_access'] = 1;
                    }
                    else {
                        $create_user_data['astutis_access'] = 0;
                    }

                    $create_user_data['is_submission_created'] = 1;

                    // $data['login_type'] = Session::get('user')->login_type;

                    // print_r(Api::post('/api/v1/user/store',$data));exit;

                    $response = json_decode(Api::post('/api/v1/user/store', $create_user_data));
                }

                return Redirect::to('/surveys')->with('success', $message);
            }
            return Redirect::back()->with('error', 'Error submitting this form data')->withInput($request->old());
        }
    }

    /**
     * save form
     */
    public function submit( Request $request, $id )
    {
        $formId = $request->get('form_id');
        $form_data = $request->all();
        $form_data['submitted'] = '1';
        $data['submission'] = json_encode($form_data);
        $response = json_decode(Api::put('api/v1/survey-submission/'.$id, $data));
        if($response->response == "success") {
            // send notifications
            $notifications = array_map('trim', explode(',', $request->get('notifications')));
            // send emails

            return Redirect::to('form/'.$formId);
        }
        else
        {
            die('An error occurred, please try again');
        }
    }

    public function recacheRelatedData($orgId)
    {
        if (empty($orgId)) {
            return;
        }
        SendSqsMessageService::sendMessages([
            [
                'serviceClass' => GetOrgRiskGradingData::class,
                'params' => $orgId ?? '',
            ]
        ]);
    }

    public function resetFormOptionCache()
    {
        SendSqsMessageService::sendMessages([
            [
                'serviceClass' => OptionListService::class,
                'params' => 'forms',
            ]
        ]);
    }

    public function clearRelatedCache($surveyId)
    {
        if (empty($surveyId)) {
            return;
        }
        \Log::info("Clearing Cache For: survey-data-for-surveyid-$surveyId");
        $this->setCache("survey-data-for-surveyid-$surveyId", null);
    }

}
