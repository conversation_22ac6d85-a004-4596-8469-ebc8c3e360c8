<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;
use App\Models\Api;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Validator;

class RhsCustomerController extends BaseController
{
    public function __construct(Request $request)
    {
        parent::__construct($request);

        if (Session::get('user')->login_type != 'risk-control' && !Session::get('user')?->isRoleAdminOrAccountManager()) {
            exit;
        }
    }

    public function show($customer_id)
    {
        $customer = json_decode(Api::get('/api/v1/rhs/customers/' . $customer_id))->data;

        if (! $customer) {
            return Redirect::to('/rhs/customers')->with('error', 'Customer not found');
        }

        return view(
            'rhs/show', [
            'customer' => $customer,
            ]
        );
    }

    public function edit($customer_id)
    {
        $customer = json_decode(Api::get('/api/v1/rhs/customers/' . $customer_id))->data;

        if (! $customer) {
            return Redirect::to('/rhs/customers')->with('error', 'Customer not found');
        }

        return view(
            'rhs/edit', [
            'customer' => $customer,
            ]
        );
    }

    public function update(Request $request, $customer_id)
    {
        $data = $request->except('_token', '_method');

        $validator = Validator::make(
            $data, [
            'full_name' => 'required',
            'position' => 'required',
            'email' => 'required',
            'address_line_one' => 'required',
            'postcode' => 'required',
            ]
        );

        if ($validator->fails()) {
            return Redirect::back()->withErrors($validator)->withInput($request->old());
        }

        $response = json_decode(Api::put('/api/v1/rhs/customers/' . $customer_id, $data));

        return Redirect::to('rhs/customers')->with('success', 'Customer updated successfully');
    }
}
