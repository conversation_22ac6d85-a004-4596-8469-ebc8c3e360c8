<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use App\Models\FileUpload;
use App\Models\Api;
use Illuminate\Support\Str;
use \Exception;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Redirect;
class ProductController extends BaseController
{
    //TEMPLATE PATH
    const TEMPLATE_PATH = '/products';

    //ROUTE PREFIX
    const ROUTE_PREFIX = 'rr-appetite/products';

    public function __construct(Request $request, FileUpload $fileUpload)
    {
        parent::__construct($request);
        $this->files = $fileUpload;
    }

    public function allowForUser(User $user): bool
    {
        return $user->login_type === 'risk-control' || $user?->isRoleAdminOrAccountManager();
    }

    public function index()
    {
          $response = json_decode(Api::get('api/v1/rrappetite/products/all'));

          //print_r( Api::get('api/v1/rrappetite/products/all')); exit;

        if($response->status == "success") {
            return view(
                static::TEMPLATE_PATH . '/index',
                array(
                'products' => $response->data
                )
            );
        }
    }

    public function store(Request $request)
    {
        if ($request->isMethod('get')) {
            $underwriters = json_decode(Api::get('api/v1/liberty-users/all/1/1000/underwriter'));

            return view(
                static::TEMPLATE_PATH . '/create',
                [
                'underwriters' => $underwriters->data,
                ]
            );
        } else {
            $data = $request->except('_token');
            // echo "<pre>";
            // print_r($data); exit;
            // dd($data);
            // print_r(Api::post('api/v1/rrappetite/products/store', $data)); exit;

            if($request->hasFile('image')) {
                $file = $request->file('image');
                $ext = $request->file('image')->getClientOriginalExtension();
                $name = Str::uuid()->toString();
                if(!is_bool($this->files->upload($file->getRealPath(), 'ra_product_images/'.$name->string.'.'.$ext))) {
                    return Redirect::back()->with('error', 'Failed to upload image')->withInput($request->old());
                }

                $data['image'] = $name->string.'.'.$ext;
            }

            if($request->hasFile('pdf_image')) {
                $file = $request->file('pdf_image');
                $ext = $request->file('pdf_image')->getClientOriginalExtension();
                $name = Str::uuid()->toString();
                if(!is_bool($this->files->upload($file->getRealPath(), 'ra_product_images/'.$name->string.'.'.$ext))) {
                    return Redirect::back()->with('error', 'Failed to upload image')->withInput($request->old());
                }

                $data['pdf_image'] = $name->string.'.'.$ext;
            }

            if($request->hasFile('lm_pdf_image')) {
                $file = $request->file('lm_pdf_image');
                $ext = $request->file('lm_pdf_image')->getClientOriginalExtension();
                $name = Str::uuid()->toString();
                if(!is_bool($this->files->upload($file->getRealPath(), 'ra_product_images/'.$name->string.'.'.$ext))) {
                    return Redirect::back()->with('error', 'Failed to upload image')->withInput($request->old());
                }

                $data['lm_pdf_image'] = $name->string.'.'.$ext;
            }

            if($request->hasFile('product_video_image')) {
                $file = $request->file('product_video_image');
                $ext = $request->file('product_video_image')->getClientOriginalExtension();
                $name = Str::uuid()->toString();
                if(!is_bool($this->files->upload($file->getRealPath(), 'product_video_image/'.$name->string.'.'.$ext))) {
                    return Redirect::back()->with('error', 'Failed to upload image')->withInput($request->old());
                }

                $data['product_video_image'] = $name->string.'.'.$ext;
            }

            if(isset($data["region_product_uwr"]["product_video_image"])) {
                $uwr_video_images = $data["region_product_uwr"]["product_video_image"];
                foreach($uwr_video_images as $key => $uwr_video_image) {
                    if(isset($uwr_video_image)) {
                        $file = $uwr_video_image;
                        $ext = $uwr_video_image->getClientOriginalExtension();
                        $name = Str::uuid()->toString();
                        if(!is_bool($this->files->upload($file->getRealPath(), 'product_video_image/'.$name->string.'.'.$ext))) {
                            return Redirect::back()->with('error', 'Failed to upload image')->withInput($request->old());
                        }

                        $data["region_product_uwr"]["product_video_image"][$key] = $name->string.'.'.$ext;
                        unset($data["region_product_uwr"]["product_video_image_id"][$key]);
                    } else {
                        unset($data["region_product_uwr"]["product_video_image_id"][$key]);
                    }
                }
            }

            $response = json_decode(Api::post('api/v1/rrappetite/products/store', $data));

            if($response->status == "success") {
                $products = json_decode(Api::get('api/v1/rrappetite/products/all'));
                return Redirect::route('rr-appetite.products.index')->with('success', 'Product created');
            }
        }

    }

    public function update(Request $request, $product_id)
    {
        if (Request::isMethod('get')) {
            $response = json_decode(Api::get('api/v1/rrappetite/products/'.$product_id.'/details'));

            // echo "<pre/>";
            // print_r(Api::get('api/v1/rrappetite/products/'.$product_id.'/details')); exit;

            $regions = json_decode(Api::get('/api/v1/rrappetite/regions?list=true'));

            $sectors = json_decode(Api::get('/api/v1/rrappetite/sectors?list=true'));

            $subsectors = json_decode(Api::get('/api/v1/rrappetite/subsectors?list=true'));

            $underwriters = json_decode(Api::get('api/v1/liberty-users/all/1/1000/underwriter'));

            try {
                $product_image = $this->files->link('ra_product_images/'.$response->data->product->image);
            } catch(Exception $e) {
                $product_image = null;
            }
            try {
                $pdf_image = $this->files->link('ra_product_images/'.$response->data->product->pdf_image);
            } catch(Exception $e) {
                $pdf_image = null;
            }
            try {
                $lm_pdf_image = $this->files->link('ra_product_images/'.$response->data->product->lm_pdf_image);
            } catch(Exception $e) {
                $lm_pdf_image = null;
            }
            try {
                $product_video_image = !is_null($response->data->product->product_video_image) && $response->data->product->product_video_image != '' ? $this->files->link('product_video_image/'.$response->data->product->product_video_image) : null;
            } catch(Exception $e) {
                $product_video_image = null;
            }

            foreach ($response->data->relationships->uwr_combinations as $uwr_combination) {
                $uwr_combination->product_video_image_link = null;
                try {
                    $uwr_combination->product_video_image_link = !is_null($uwr_combination->product_video_image) && $uwr_combination->product_video_image != '' ? $this->files->link('product_video_image/'.$uwr_combination->product_video_image) : null;
                } catch(Exception $e) {
                    $uwr_combination->product_video_image_link = null;
                }
            }
            // echo "<pre>";
            // print_r(Api::get('api/v1/rrappetite/products/'.$product_id.'/details')); exit;
            if($response->status == "success") {
                return view(
                    static::TEMPLATE_PATH . '/edit',
                    [
                    'product_id' => $product_id,
                    'product'    => $response->data,
                    'regions'    => $regions->data,
                    'sectors'    => $sectors->data,
                    'subsectors'    => $subsectors->data,
                    'underwriters' => $underwriters->data,
                    'product_image' => $product_image,
                    'pdf_image' => $pdf_image,
                    'lm_pdf_image' => $lm_pdf_image,
                    'product_video_image' => $product_video_image
                    ]
                );
            }
        } else {
            $data = $request->except('_token');
            // echo "<pre>";
            // print_r($data); exit;
            if($request->hasFile('image')) {
                $file = $request->file('image');
                $ext = $request->file('image')->getClientOriginalExtension();
                $name = Str::uuid()->toString();
                if(!is_bool($this->files->upload($file->getRealPath(), 'ra_product_images/'.$name->string.'.'.$ext))) {
                    return Redirect::back()->with('error', 'Failed to upload image')->withInput($request->old());
                }

                $data['image'] = $name->string.'.'.$ext;
                unset($data['image_id']);
            } else {
                $data['image'] = $data['image_id'];
            }

            if($request->hasFile('pdf_image')) {
                $file = $request->file('pdf_image');
                $ext = $request->file('pdf_image')->getClientOriginalExtension();
                $name = Str::uuid()->toString();
                if(!is_bool($this->files->upload($file->getRealPath(), 'ra_product_images/'.$name->string.'.'.$ext))) {
                    return Redirect::back()->with('error', 'Failed to upload image')->withInput($request->old());
                }

                $data['pdf_image'] = $name->string.'.'.$ext;
                unset($data['pdf_image_id']);
            } else {
                $data['pdf_image'] = $data['pdf_image_id'];
            }

            if($request->hasFile('lm_pdf_image')) {
                $file = $request->file('lm_pdf_image');
                $ext = $request->file('lm_pdf_image')->getClientOriginalExtension();
                $name = Str::uuid()->toString();
                if(!is_bool($this->files->upload($file->getRealPath(), 'ra_product_images/'.$name->string.'.'.$ext))) {
                    return Redirect::back()->with('error', 'Failed to upload image')->withInput($request->old());
                }

                $data['lm_pdf_image'] = $name->string.'.'.$ext;
                unset($data['lm_pdf_image_id']);
            } else {
                $data['lm_pdf_image'] = $data['lm_pdf_image_id'];
            }

            if($request->hasFile('product_video_image')) {
                $file = $request->file('product_video_image');
                $ext = $request->file('product_video_image')->getClientOriginalExtension();
                $name = Str::uuid()->toString();
                if(!is_bool($this->files->upload($file->getRealPath(), 'product_video_image/'.$name->string.'.'.$ext))) {
                    return Redirect::back()->with('error', 'Failed to upload image')->withInput($request->old());
                }

                $data['product_video_image'] = $name->string.'.'.$ext;
                unset($data['product_video_image_id']);
            } else {
                $data['product_video_image'] = $data['product_video_image_id'];
            }

            if(isset($data["region_product_uwr"]["product_video_image"])) {
                $uwr_video_images = $data["region_product_uwr"]["product_video_image"];
                foreach($data["region_product_uwr"]["product_video_image_id"] as $key => $uwr_video_image) {
                    if(isset($data["region_product_uwr"]["product_video_image"][$key])) {
                        $file = $data["region_product_uwr"]["product_video_image"][$key];
                        $ext = $data["region_product_uwr"]["product_video_image"][$key]->getClientOriginalExtension();
                        $name = Str::uuid()->toString();
                        if(!is_bool($this->files->upload($file->getRealPath(), 'product_video_image/'.$name->string.'.'.$ext))) {
                            return Redirect::back()->with('error', 'Failed to upload image')->withInput($request->old());
                        }

                        $data["region_product_uwr"]["product_video_image"][$key] = $name->string.'.'.$ext;
                        unset($data["region_product_uwr"]["product_video_image_id"][$key]);
                    } else {
                        $data["region_product_uwr"]["product_video_image"][$key] = $data["region_product_uwr"]["product_video_image_id"][$key];
                    }
                }
            }

            $response = json_decode(Api::post('api/v1/rrappetite/products/'.$product_id.'/update', $data));

            if($response->status == "success") {
                return Redirect::back()->with('success', 'Product updated');
            }
        }

    }

    public function combinationPartials($partial, $partial_id, $product_id = 0)
    {

        $view_partial = 'combination';
        if($partial == 'cover_warranties') {
            $view_partial = 'cover_warranties_combination';
        }

        $regions = json_decode(Api::get('/api/v1/rrappetite/regions?list=true'));

        $sectors = json_decode(Api::get('/api/v1/rrappetite/sectors?list=true'));

        $subsectors = json_decode(Api::get('/api/v1/rrappetite/subsectors?list=true'));

        if($product_id == 0) {
            return view(
                static::TEMPLATE_PATH . '/partials/'.$view_partial, [
                'partial' => $partial,
                'partial_id' => $partial_id,
                'regions'    => $regions->data,
                'sectors'    => $sectors->data,
                'subsectors'    => $subsectors->data,
                ]
            );
        }

    }

    public function uwrPartials($product_id = 0)
    {

        $view_partial = 'region_underwriters';

        $regions = json_decode(Api::get('/api/v1/rrappetite/regions?list=true'));

        $underwriters = json_decode(Api::get('api/v1/liberty-users/all/1/1000/underwriter'));

        if($product_id == 0) {
            return view(
                static::TEMPLATE_PATH . '/partials/'.$view_partial, [
                'regions'    => $regions->data,
                'underwriters'    => $underwriters->data
                ]
            );
        }

    }


    public function deleteCombination(Request $request)
    {

        $relationship_id = $request->get('relationship_id');
        $partial_type = $request->get('partial_type');
        $partial_id = $request->get('partial_id');

        $data = [
        'relationship_id' => $relationship_id,
        'partial_type' => $partial_type,
        'partial_id' => $partial_id,
        ];

        // $response = json_decode(Api::post('api/v1/rrappetite/products/relationships/delete', $data));

        // if($response->status == 'success') {
        return Response::json(
            [
            'response' => 'success',
            'message' => 'Combination removed',
            ]
        );
        // }

    }

    /**
     * delete a product
     *
     * @param  int $product_id
     * @return redirect
     */
    public function destroy($product_id)
    {
        $response = json_decode(Api::delete('api/v1/rrappetite/products/'.$product_id.'/delete'));
        if($response->status == "success") {
            return Redirect::back()->with('success', 'Product deleted');
        }
    }

    public function deleteVideoFrame($product_id)
    {
        $response = json_decode(Api::delete('api/v1/rrappetite/products/'.$product_id.'/delete-video-frame'));

        if ($response->status == 'success') {
            return Response::json(
                [
                'response' => 'success',
                'message' => 'Video frame successfully deleted.',
                ]
            );
        }
    }
}
