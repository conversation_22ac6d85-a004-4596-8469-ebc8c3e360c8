<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Models\Api;
class SubsectorController extends BaseController
{
    //TEMPLATE PATH
    const TEMPLATE_PATH = '/subsectors';

    //ROUTE PREFIX
    const ROUTE_PREFIX = 'rr-appetite/subsectors';


    public function allowForUser(User $user): bool
    {
        return $user->login_type === 'risk-control' || $user?->isRoleAdminOrAccountManager();
    }

    //Index
    public function index()
    {
        $response = json_decode(Api::get('api/v1/rrappetite/subsectors/all'));

        if($response->status == "success") {
            return view(
                static::TEMPLATE_PATH . '/index',
                array(
                'subsectors' => $response->data
                )
            );
        }
    }

    public function store(Request $request)
    {
        $siccodes = json_decode(Api::get('api/v1/rrappetite/siccodes?list=true'));
        if (Request::isMethod('get')) {
            return view(
                static::TEMPLATE_PATH . '/create',
                array(
                'sic_codes' => $siccodes->data
                )
            );
        } else {
            $data = $request->except('_token');

            $response = json_decode(Api::post('api/v1/rrappetite/subsectors/store', $data));

            if($response->status == "success") {
                $subsectors = json_decode(Api::get('api/v1/rrappetite/subsectors/all'));
                return view(
                    static::TEMPLATE_PATH . '/index',
                    array(
                    'subsectors' => $subsectors->data
                    )
                );
            }
        }

    }

    public function update(Request $request, $subsector_id)
    {
        if (Request::isMethod('get')) {
            $siccodes = json_decode(Api::get('api/v1/rrappetite/siccodes?list=true'));
            $associatedsiccodes=json_decode(Api::get('api/v1/rrappetite/siccodes/subsector/'.$subsector_id));
            $associatedsiccodes=array_map(
                function ($o) {
                    return $o->sic_code_id; 
                }, (array)($associatedsiccodes->data)
            );
            $tags=json_decode(Api::get('api/v1/rrappetite/subsectors/tags/'.$subsector_id));
            $tags=array_map(
                function ($o) {
                    return $o->tag; 
                }, (array)($tags->data)
            );
            $tags=implode(";", $tags);
            $response = json_decode(Api::get('api/v1/rrappetite/subsectors/'.$subsector_id));
            // echo "<pre>";
            // print_r($response); exit;
            if($response->status == "success") {
                return view(
                    static::TEMPLATE_PATH . '/edit',
                    [
                    'subsector'    => $response->data,
                    'sic_codes' => $siccodes->data,
                    'selected_sic_codes' => $associatedsiccodes,
                    'selected_tags' => $tags,
                    ]
                );
            }
        } else {
            $data = $request->except('_token');

            $response = json_decode(Api::post('api/v1/rrappetite/subsectors/'.$subsector_id.'/update', $data));

            if($response->status == "success") {
                $subsectors = json_decode(Api::get('api/v1/rrappetite/subsectors/all'));
                return view(
                    static::TEMPLATE_PATH . '/index',
                    array(
                    'subsectors' => $subsectors->data
                    )
                );
            }
        }
    }

    public function delete($subsector_id)
    {
        $response = json_decode(Api::delete('api/v1/rrappetite/subsectors/'.$subsector_id.'/delete'));
        // echo "<pre>";
        if($response->status == "success") {
            $subsectors = json_decode(Api::get('api/v1/rrappetite/subsectors/all'));
            return view(
                static::TEMPLATE_PATH . '/index',
                array(
                'subsectors' => $subsectors->data
                )
            );
        }
    }
}
