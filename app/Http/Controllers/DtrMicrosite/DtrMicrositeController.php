<?php

namespace App\Http\Controllers\DtrMicrosite;

use App\Http\Controllers\BaseResourceController;
use App\Models\Api;
use App\Services\CacheContent\GetBrokerService;
use App\Services\CacheContent\GetMgaSchemeService;
use App\Services\CacheContent\GetSurveyService;
use App\Services\SendSqsMessageService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;

class DtrMicrositeController extends BaseResourceController
{
    // Use survey namespace
    const API_NAMESPACE = 'surveys';
    const TEMPLATE_PATH = '/dtr-microsite';
    const ROUTE_PREFIX = 'dtr';

    /**
     * Show all resources.
     *
     * @return Response
     *
     * @throws Exception
     */
    public function index(Request $request)
    {
        $user = Session::get('user');
        $search = $request->get('search', '');
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 10);

        // filter by mga scheme
        $get = [
            'survey_type=dtr'
        ];

        $request->has('organisation_id') ? $get[] = 'organisation_id=' . $request->get('organisation_id') : '';
        $request->has('mga_scheme') ? $get[] = 'mga_scheme=' . $request->get('mga_scheme') : '';
        $request->has('broker_org') ? $get[] = 'broker_org=' . $request->get('broker_org') : '';
        $request->has('search') ? $get[] = 'search=' . urlencode($request->get('search')) : '';
        $request->has('order') ? $get[] = 'order=' . $request->get('order') : $get[] = 'order=desc';
        $request->has('date_of_next_survey') ? $get[] = 'date_of_next_survey=filled' : '';
        $request->has('auto_scheduled_surveys') ? $get[] = 'auto_scheduled_surveys=filled' : '';
        $request->has('col') ? $get[] = 'col=' . $request->get('col') : '';
        $request->has('sort') ? $get[] = 'sort=' . $request->get('sort') : '';
        $request->has('month') ? $get[] = 'month=' . $request->get('month') : '';
        $request->has('year') ? $get[] = 'year=' . $request->get('year') : '';

        if ($user->type == 'external-surveyor' && $user?->isRoleAdminOrAccountManager() || $user->role == 'surveyor') {
            $get[] = 'external_survey_company_id=' . $user->external_survey_company_id;

            $get[] = 'user_id=' . Session::get('user')->id;
            $get[] = 'user_role=' . Session::get('user')->role;
            $get[] = 'user_type=' . Session::get('user')->type;
            $get[] = 'user_login_type=' . Session::get('user')->login_type;
        }

        if ($user->login_type == 'aspen-user') {
            $get[] = 'branch_type=aspen-user';

            // print_r(Api::get(
            //     static::get_api_uri(sprintf(
            //         'all/%d/%d%s',
            //         $page,
            //         $limit,
            //         '?'.implode('&', $get)
            //     )
            // )));exit;

            $resources = json_decode(
                Api::get(
                    static::get_api_uri(
                        sprintf(
                            'all/%d/%d%s',
                            $page,
                            $limit,
                            '?' . implode('&', $get)
                        )
                    )
                )
            );
        } else {
            if ($user->login_type == 'broker-user') {
                $get[]     = 'user_login_type=' . Session::get('user')->login_type;
                $resources = json_decode(Api::get(static::get_api_uri(sprintf('broker/%d/%d/%d%s', $user->broker_id, $page, $limit, '?' . implode('&', $get)))));
            } else {
                $url = static::get_api_uri(
                    sprintf(
                        'all/%d/%d%s',
                        $page,
                        $limit,
                        '?' . implode('&', $get)
                    )
                );

                $resources = json_decode(Api::get($url));
            }
        }

        // get mga schemes
        if ($user->login_type == 'broker-user') {
            if ($user->broker_id) {
                $schemes = GetMgaSchemeService::getMgaSchemeById($user->broker_id);
            }
        } else {
            $schemes = GetMgaSchemeService::get('');
        }

        // get broker orgs
        if ($user->login_type == 'broker-user') {
            if ($user->broker_id) {
                $broker_orgs = GetBrokerService::getBrokerById($user->broker_id);
            }
        } else {
            $broker_orgs = GetBrokerService::get('');
        }

        $months = array(1 => 'Jan', 2 => 'Feb', 3 => 'Mar', 4 => 'Apr', 5 => 'May', 6 => 'Jun', 7 => 'Jul', 8 => 'Aug', 9 => 'Sep', 10 => 'Oct', 11 => 'Nov', 12 => 'Dec');

        if ($resources->response == 'success') {
            return view(
                static::TEMPLATE_PATH . '/index',
                array_merge(
                    [
                        'broker_orgs' => isset($broker_orgs) ? $broker_orgs->data : [],
                        'schemes' => isset($schemes) ? $schemes->data : [],
                        'resources' => $resources->data,
                        'total' => $resources->total,
                        'limit' => $limit,
                        'page' => $page,
                        'search' => $search,
                        'csr_next_survey_due_dates' => isset($resources->dates) ? explode(", ", $resources->dates) : [],
                        'months' => $months,
                        'user_details' => Session::get('user'),
                        'link' => static::ROUTE_PREFIX . '.index',
                    ],
                    static::getAdditionalViewParams('index', $resources->data)
                )
            );
        } else {
            throw new \Exception($resources->message);
        }
    }

    public function exportAll(Request $request)
    {
        $user = Session::get('user');

        if ($user->login_type != 'risk-control' && !$user?->isRoleAdminOrAccountManager()) {
            return Response::make('Unauthorized', 401);
        }

        $get = [
            'survey_type=dtr'
        ];

        $get[] = 'export=true';
        $get[] = 'first_name=' . $user->first_name;
        $get[] = 'last_name=' . $user->last_name;
        $get[] = 'email=' . $user->email;

        if ($request->has('mga_scheme')) {
            $get[] = 'mga_scheme=' . $request->get('mga_scheme');
        }

        if ($request->has('broker_org')) {
            $get[] = 'broker_org=' . $request->get('broker_org');
        }

        if ($request->has('month')) {
            $get[] = 'month=' . $request->get('month');
        }

        if ($request->has('year')) {
            $get[] = 'year=' . $request->get('year');
        }

        if ($request->has('search')) {
            $get[] = 'search=' . $request->get('search');
        }

        $resources = json_decode(
            Api::get(
                static::get_api_uri(
                    sprintf(
                        'all/1/10000%s',
                        '?' . implode('&', $get)
                    )
                )
            )
        );
        return response('OK');
    }

    public function myReviews(Request $request)
    {
        $user = Session::get('user');
        $order = $request->get('order', 'asc');
        $search = $request->get('search', '');
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 10);

        $get = [
            'survey_type=dtr'
        ];
        $request->has('mga_scheme') ? $get[] = 'mga_scheme=' . $request->get('mga_scheme') : '';
        $request->has('broker_org') ? $get[] = 'broker_org=' . $request->get('broker_org') : '';
        $request->has('search') ? $get[] = 'search=' . urlencode($request->get('search')) : '';
        $request->has('order') ? $get[] = 'order=' . $request->get('order') : '';
        $request->has('date_of_next_survey') ? $get[] = 'date_of_next_survey=filled' : '';
        $request->has('auto_scheduled_surveys') ? $get[] = 'auto_scheduled_surveys=filled' : '';

        if ($user->type == 'external-surveyor' && $user?->isRoleAdminOrAccountManager() || $user->role == 'surveyor') {
            $get[] = 'external_survey_company_id=' . $user->external_survey_company_id;
        }

        if ((($user->type == 'liberty-user' && $user->role != 'aspen-user' && $user->role != 'underwriter' && $user->role != 'admin') || $user->type == 'broker-user')) {
            $get[] = 'user_id=' . Session::get('user')->id;
            $get[] = 'user_role=' . Session::get('user')->role;
            $get[] = 'user_type=' . Session::get('user')->type;
            $get[] = 'user_login_type=' . Session::get('user')->login_type;
            $get[] = 'user_external_survey_company_id=' . Session::get('user')->external_survey_company_id;
        }

        if ($user->login_type == 'aspen-user') {
            $get[] = 'branch_type=aspen-user';
            $resources = json_decode(
                Api::get(
                    static::get_api_uri(
                        sprintf(
                            'all/%d/%d%s',
                            $page,
                            $limit,
                            '?' . implode('&', $get)
                        )
                    )
                )
            );
        } else {
            if ($user->login_type == 'broker-user') {
                $resources = json_decode(
                    Api::get(
                        static::get_api_uri(
                            sprintf(
                                'broker/%d/%d/%d%s',
                                $user->broker_id,
                                $page,
                                $limit,
                                '?' . implode('&', $get)
                            )
                        )
                    )
                );
            } else {
                $resources = json_decode(
                    Api::get(
                        static::get_api_uri(
                            sprintf(
                                'all/%d/%d%s',
                                $page,
                                $limit,
                                '?' . implode('&', $get)
                            )
                        )
                    )
                );
            }
        }

        // get mga schemes
        if ($user->login_type == 'broker-user') {
            if ($user->broker_id) {
                $schemes = GetMgaSchemeService::getMgaSchemeById($user->broker_id);
            }
        } else {
            $schemes = GetMgaSchemeService::get('');
        }

        // get broker orgs
        if ($user->login_type == 'broker-user') {
            if ($user->broker_id) {
                $broker_orgs = GetBrokerService::getBrokerById($user->broker_id);
            }
        } else {
            $broker_orgs = GetBrokerService::get('');
        }

        if ($resources->response == 'success') {
            return view(
                static::TEMPLATE_PATH . '/my-reviews',
                array_merge(
                    [
                        'broker_orgs' => isset($broker_orgs) ? $broker_orgs->data : [],
                        'schemes' => isset($schemes) ? $schemes->data : [],
                        'resources' => $resources->data,
                        'total' => $resources->total,
                        'limit' => $limit,
                        'page' => $page,
                        'search' => $search,
                        'user_details' => Session::get('user'),
                        'link' => static::ROUTE_PREFIX . '.my-reviews',
                    ],
                    static::getAdditionalViewParams('my-reviews', $resources->data)
                )
            );
        } else {
            throw new \Exception($resources->message);
        }
    }

    public function edit(Request $request, $id)
    {
        // $this->determineNestedResourceID($id);
        $resource = json_decode(Api::get(static::get_api_uri($id)));

        if (Session::get('user')->login_type == 'broker-user') {

            $orgs_for_user = json_decode(Api::get('api/v1/broker-users/orgs/' . Session::get('user')->broker_id));

            if ($resource && isset($resource->data->organisation) && $resource->data->organisation->mga_scheme == null) {
                return Response::make('Unauthorized', 401);
            }

            if ($resource && isset($resource->organisation) && isset($resource->data) && isset($resource->data->organisation)
                && $orgs_for_user && isset($orgs_for_user->schemes)
            ) {
                if (!in_array($resource->data->organisation->id, $orgs_for_user->schemes)) {
                    return Response::make('Unauthorized', 401);
                }
            }
        }

        $getResponseId = Api::get('api/v1/surveys/get-survey-files-for-id/' . $resource->data->id);
        $files = json_decode($getResponseId);

        foreach ($files->data as $file) {
            $file->download = $this->survey->downloadLink(
                'survey_attachments/file/' . $file->cloud_file_name,
                $file->file_name
            );
        }

        $getSrfResponseId = Api::get('api/v1/surveys/get-srf-for-id/' . $resource->data->id);
        $srf = json_decode($getSrfResponseId);

        return ($resource->response == 'success')
            ? view(
                static::TEMPLATE_PATH . '/edit',
                array_merge(
                    ['resource' => $resource->data], ['files' => $files->data], ['srf' => $srf->data],
                    static::getAdditionalViewParams('edit', $resource->data)
                )
            )
            : redirect()->back()->with($resource->response, $resource->message);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }

    /**
     * Get additional View parameters.
     *
     * @param  string  $view
     * @param null   $resource_id
     *
     * @return array
     */
    public function getAdditionalViewParams(string $view, $resource)
    {
        $params = [];

        if (in_array($view, ['create', 'edit'])) {
            $api_calls = [
                'branches' => ['options', 'liberty-branches'],
                'organisations' => ['options', 'organisation'],
                'underwriters' => ['options/underwriter', 'liberty-users'],
                'riskEngineers' => ['options/risk-engineer', 'liberty-users'],
                'aspenusers' => ['options/aspen-user', 'liberty-users'],
                'brokers' => ['options', 'brokers'],
                'forms' => ['form/list?type=dtr', 'risk-improvement'],
            ];

            if (Session::get('user')->login_type == 'broker-user') {
                $api_calls['organisations'] = ['options/broker/' . Session::get('user')->broker_id, 'organisation'];
                $api_calls['broker_users'] = ['organisation/options/' . Session::get('user')->broker_id, 'broker-users'];
            }
            if (!isset($api_calls['broker_users']) && isset($resource->broker->id)) {
                $api_calls['broker_users'] = ['organisation/options/' . $resource->broker->id, 'broker-users'];
            }

            foreach ($api_calls as $key => $values) {
                $params['options'][$key] = json_decode(
                    Api::get(
                        static::get_api_uri($values[0], $values[1])
                    )
                );

                if ($key === 'forms') {
                    $riForms = $params['options']['forms']->response === 'success' ? $params['options']['forms']->data : [];
                    $finalizeForms = [];
                    foreach ($riForms as $key => $form) {
                        if (!str_contains($form, 'OLD - DO NOT USE')) {
                            $finalizeForms[$key] = $form;
                        }
                    }

                    $params['options']['forms']->data = json_decode(json_encode($finalizeForms));
                }
            }

            $params['options']['user_details'] = Session::get('user');
        }

        if (in_array($view, ['create'])) {
            $organisation_array = (array) $params['options']['organisations'];
            $organisation_first = key($organisation_array);

            $rs_pn = json_decode(Api::get('/api/v1/organisation/' . $organisation_first . '/policy-numbers'));

            $params['options']['policy_numbers'] = isset($rs_pn->data) ? $rs_pn->data : [];
        }

        if (in_array($view, ['edit'])) {
            if (isset($resource->organisation->id)) {
                $params['options']['policy_numbers'] = json_decode(Api::get('/api/v1/organisation/' . $resource->organisation->id . '/policy-numbers'))->data;
            }
        }

        return $params;
    }

    /**
     * Get validation attribute names.
     *
     * @return array of validation attribute names
     */
    public function getValidatorAttributeNames()
    {
        return [
            'type' => 'survey type',
            'client.name' => 'client\'s name',
            'client.phone' => 'client\'s phone number',
            'client.address_1' => 'client\'s first address line',
            'client.city' => 'client\'s city',
            'client.country' => 'client\'s country',
            'client.postcode' => 'client\'s postcode',
            'broker.organisation' => 'broker\'s organisation',
            'broker.name' => 'broker\'s name',
            'broker.phone' => 'broker\'s phone number',
            'deadlines.survey' => 'survey deadline',
            'deadlines.client_survey_report' => 'client survey report deadline',
            'deadlines.underwriter' => 'underwriter report deadline',
            'survey_form' => 'Survey form',
        ];
    }

        /**
     * Get validation rules for a specific method (store/update/etc).
     *
     * @param string $method
     *
     * @return array of validation rules
     */
    public function getValidatorRules(Request $request, $method)
    {
        $rules = [
            'organisation'                   => 'numeric|min:1',
            'location'                       => 'required',
            'branch'                         => 'numeric|min:1',
            'broker.organisation'            => 'required',
            'deadlines.survey'               => 'required',
            'deadlines.client_survey_report' => 'required',
            'deadlines.underwriter'          => 'required',
            'survey_form'                    => 'required',
            'risk_grading_types'             => 'required_without:policy_id',
        ];

        if ($method == 'store' || $method == 'update') {
            unset($rules['deadlines.client_survey_report']);
            unset($rules['deadlines.underwriter']);
        }

        if ($method == 'update_status') {
            $rules = ['branch' => 'numeric|min:1'];
        }

        return $rules;
    }

    public function parseDataBeforeStorage($data)
    {
        $ids = [
            'organisation',
            'branch',
            // 'underwriter',
            'surveyor',
        ];

        foreach ($ids as $id) {
            if (isset($data[$id])) {
                $data[sprintf('%s_id', $id)] = $data[$id];
                unset($data[$id]);
            }
        }

        if (isset($data['broker']['organisation'])) {
            $data['broker_id'] = $data['broker']['organisation'];
            unset($data['broker']['organisation']);
        }

        if (isset($data['deadlines']) && count($data['deadlines'])) {
            foreach ($data['deadlines'] as $key => $value) {
                $data['deadlines'][$key] = date(
                    'Y-m-d H:i:s',
                    strtotime(str_replace('/', '-', $value))
                );
            }
        }

        if(!empty($data['policy_id']) && $data['policy_id'] === 'Select Policy') {
            unset($data['policy_id']);
        }

        if(!empty($data['policy_id']) && $data['policy_id'] !== 'Select Policy') {
            $data['risk_grading_types'] = '';
        }

        if (!empty($data['risk_grading_types'])) {
            $data['risk_grading_types'] = implode(',', $data['risk_grading_types']);
        }

        return array_merge($data, [
            'survey_type' => 'dtr',
            'visit_arrangement' => '',
        ]);
    }

    public function createOrganisation(Request $request)
    {
        $data = $request->all();
        $isResponsibleBusiness = false;

        $rules = [
            'name' => 'required|regex:/(^[A-Za-z0-9 ]+$)+/',
            'location_name' => 'required',
            'city' => 'required',
        ];

        $messages = [];

        if (Session::get('user')->login_type == 'broker-user') {
            $broker_rule = ['mga_scheme' => 'required_if:broker_id,==,""', 'broker_id' => 'required_if:mga_scheme,==,""'];
            $rules = array_merge($rules, $broker_rule);
        }

        $data['bound'] = 0;
        $data['country'] = '-';

        $validator = Validator::make($data, $rules, $messages);

        if ($validator->fails()) {
            return response()->json(
                [
                    'response' => 'error',
                    'errors' => $validator->errors(),
                ],
                200
            );
        } else {
            unset($data['_token']);

            $data['login_type'] = Session::get('user')->login_type;

            $response = json_decode(Api::post('api/v1/organisation/store', $data));

            if ($response->response == 'success') {
                //Fix cache
                $organisation = json_decode(Api::get('api/v1/organisation/' . $response->id));

                // Create an entry for organisation section settings
                $defaultSettings = [
                    'organisation_id'      => $response->id,
                    'settings'             => [
                        'risk_engineering' => 0,
                        'survey'           => 0,
                        'your_team'        => 0,
                        'services'         => '{}',
                    ],
                ];
                $orgSettings = json_decode(Api::post('api/v1/organisation-settings/storeOrUpdate', $defaultSettings));
                if ($organisation->response == 'success') {
                    if ($organisation->data->mgascheme) {
                        $broker_id = $organisation->data->mgascheme->broker_id;
                        Cache::forget('dashboard_statistics_broker_id_' . $broker_id);
                    }
                    if ($organisation->data->broker_id) {
                        Cache::forget('dashboard_statistics_broker_id_' . $organisation->data->broker_id);
                    }
                }

                return response()->json($response);
            } else {
                if ($response->errors) {
                    return response()->json(
                        [
                            'response' => 'error',
                            'errors' => $response->errors,
                        ],
                        200
                    );
                } else {
                    return response()->json(
                        [
                            'response' => 'error',
                            'errors' => ["Cannot create organisation. Please check your inputs."],
                        ],
                        200
                    );
                }
            }
        }
    }

    public static function onUpdateSuccess($data)
    {
        self::recacheRelatedData();
    }

    public static function recacheRelatedData()
    {
        try {
            $surveyId = request()->route('id', request()->get('survey_id'));

            if (!empty($surveyId)) {
                SendSqsMessageService::sendMessages([
                    [
                        'serviceClass' => GetSurveyService::class,
                        'params' => $surveyId ?? '',
                    ],
                ]);
            }
           
        } catch (\Exception $e) {
            \Log::error("[DtrMicrositeController@recacheRelatedData] " . $e->getMessage());
        }        
    }

}
