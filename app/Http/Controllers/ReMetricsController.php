<?php

namespace App\Http\Controllers;

use App\Models\Api;
use Illuminate\Http\Request;
use App\Models\FileUpload;

class ReMetricsController extends BaseController
{
    public function __construct(Request $request, FileUpload $fileUpload)
    {
        parent::__construct($request);

        $this->files = $fileUpload;
    }

    public function index()
    {
        // get filters values
        $sectors = json_decode(Api::get('api/v1/sector/all'))->data;
        $brokers = json_decode(Api::get('api/v1/brokers/all/0/0'))->data;
        $policyTypes = json_decode(Api::get('/api/v1/policy-types/all'))->data;

        $accountEngineers = json_decode(Api::get('api/v1/liberty-users/account-engineers'))->data;
        $underwriters = json_decode(Api::get('api/v1/liberty-users/all/0/99999/underwriter'))->data;
        
        return view('re-metrics/index', compact('sectors', 'brokers', 'policyTypes', 'underwriters', 'accountEngineers'));
    }

    public function fetchChartData(Request $request)
    {
        $method = $request->input('method');
        if (!method_exists($this, $method)) {
            return response()->json(['error' => 'Method not found'], 404);
        }

        return $this->$method($request);
    }

    public function srfsDonutChartData(Request $request)
    {
        $srfs = json_decode(Api::getWithParams('api/v1/re-metrics/organisation/srfs', $request->all()));  
        $srfsHtml = view('re-metrics/charts/pie_chart_srfs', compact('srfs'))->render();

        return response()->json(['srfs' => $srfsHtml]);
    }

    public function firstRowDonutChartData(Request $request)
    {
        $riskRecommendationCards = json_decode(Api::getWithParams('api/v1/re-metrics/organisation/risk-recommendation-count-by-status', $request->all()));
        $riskRecommendationCardsHtml = view('re-metrics/charts/pie_chart_risk_recommendation', compact('riskRecommendationCards'))->render();

        $dtrs = json_decode(Api::getWithParams('api/v1/re-metrics/organisation/dtrs', $request->all()));
        $dtrsHtml = view('re-metrics/charts/pie_chart_desktop_reviews', compact('dtrs'))->render();

        $desktopReviewsImpact = json_decode(Api::getWithParams('api/v1/re-metrics/organisation/desktop-reviews-impact', $request->all()));
        $desktopReviewsImpactHtml = view('re-metrics/charts/pie_chart_desktop_reviews_impact', compact('desktopReviewsImpact'))->render();

        return response()->json([
            'riskRecommendationCards' => $riskRecommendationCardsHtml,
            'dtrs' => $dtrsHtml,
            'desktopReviewsImpact' => $desktopReviewsImpactHtml,
        ]);
    }

    public function secondRowDonutChartData(Request $request)
    {
        $organisationBySector = json_decode(Api::getWithParams('api/v1/re-metrics/organisation/count-by-sector', $request->all()));
        $organisationBySectorHtml = view('re-metrics/charts/pie_chart_accounts_by_sector', compact('organisationBySector'))->render();

        $riskEngineeringMeetingHeld = json_decode(Api::getWithParams('api/v1/re-metrics/organisation/re-meeting-held', $request->all()));
        $riskEngineeringMeetingHeldHtml = view('re-metrics/charts/pie_chart_risk_eng_meeting_held', compact('riskEngineeringMeetingHeld'))->render();

        $organisationOverviewUpdated = json_decode(Api::getWithParams('api/v1/re-metrics/organisation/overview-updated', $request->all()));
        $organisationOverviewUpdatedHtml = view('re-metrics/charts/pie_chart_risk_eng_overview_updated', compact('organisationOverviewUpdated'))->render();

        $organisationWithSurveyProgramme = json_decode(Api::getWithParams('api/v1/re-metrics/organisation/with-survey-programme', $request->all()));
        $organisationWithSurveyProgrammeHtml = view('re-metrics/charts/pie_chart_survey_programme_in_place', compact('organisationWithSurveyProgramme'))->render();

        return response()->json([
            'organisationBySector' => $organisationBySectorHtml,
            'riskEngineeringMeetingHeld' => $riskEngineeringMeetingHeldHtml,
            'organisationOverviewUpdated' => $organisationOverviewUpdatedHtml,
            'organisationWithSurveyProgramme' => $organisationWithSurveyProgrammeHtml,
        ]);
    }

    public function activeAndInactiveOrganisations(Request $request)
    {
        $activeOrganisations = json_decode(Api::getWithParams('api/v1/re-metrics/organisation/active-organisations', $request->all()));

        foreach ($activeOrganisations as &$organisation) {
            if (!is_null($organisation->logo) && $organisation->logo != '' && $organisation->logo != 'none') {
                $organisation->image_url = $this->files->link($organisation->logo);
            } else {
                $organisation->image_url = '/img/dummy/logo-placeholder.png';
            }
        }
        unset($organisation);

        $activeOrganisationsHtml = view('re-metrics/inc/table_active_clients', compact('activeOrganisations'))->render();

        $inactiveOrganisations = json_decode(Api::getWithParams('api/v1/re-metrics/organisation/inactive-organisations', $request->all()));

        foreach ($inactiveOrganisations as &$organisation) {
            if (!is_null($organisation->logo) && $organisation->logo != '' && $organisation->logo != 'none') {
                $organisation->image_url = $this->files->link($organisation->logo);
            } else {
                $organisation->image_url = '/img/dummy/logo-placeholder.png';
            }
        }
        unset($organisation);

        $inactiveOrganisationsHtml = view('re-metrics/inc/table_inactive_clients', compact('inactiveOrganisations'))->render();
        return response()->json([
            'activeOrganisations' => $activeOrganisationsHtml,
            'inactiveOrganisations' => $inactiveOrganisationsHtml,
        ]);
    }

    public function accountsUnderManagementAndAverageRiskGrading(Request $request)
    {
        $accountsUnderManagement = json_decode(Api::getWithParams('api/v1/re-metrics/organisation/accounts-under-management', $request->all()));
        $accountsUnderManagementHtml = view('re-metrics/charts/xy_chart_accounts_under_management', compact('accountsUnderManagement'))->render();

        $averageRiskGrading = json_decode(Api::getWithParams('api/v1/re-metrics/organisation/average-risk-grading', $request->all()));
        $averageRiskGradingHtml = view('re-metrics/charts/xy_chart_ave_risk_grading', compact('averageRiskGrading'))->render();

        return response()->json([
            'accountsUnderManagement' => $accountsUnderManagementHtml,
            'averageRiskGrading' => $averageRiskGradingHtml,
        ]);
    }

    public function desktopReviewsImpact(Request $request)
    {
        $desktopReviewsImpact = json_decode(Api::getWithParams('api/v1/re-metrics/organisation/desktop-reviews-impact', $request->all()));

        $desktopReviewsImpactHtml = view('re-metrics/charts/pie_chart_desktop_reviews_impact', compact('desktopReviewsImpact'))->render();

        return response()->json([
            'desktopReviewsImpact' => $desktopReviewsImpactHtml,
        ]);
    }

    public function organisationTable()
    {
        // get filters values
        $sectors = json_decode(Api::get('api/v1/sector/all'))->data;
        $brokers = json_decode(Api::get('api/v1/brokers/all/0/0'))->data;
        $policyTypes = json_decode(Api::get('/api/v1/policy-types/all'))->data;

        $accountEngineers = json_decode(Api::get('api/v1/liberty-users/account-engineers'))->data;
        $underwriters = json_decode(Api::get('api/v1/liberty-users/all/0/99999/underwriter'))->data;

        return view('re-metrics/organisation-table', compact('sectors', 'brokers', 'policyTypes', 'underwriters', 'accountEngineers'));
    }

    public function fetchOrganisationTableData(Request $request)
    {
        try {
            $base_url = 'api/v1/re-metrics/organisation/organisation-table-data';
            $organisationTableData = json_decode(Api::getWithParams($base_url, $request->all()), true);

            foreach ($organisationTableData as &$organisation) {
                if (!is_null($organisation['logo']) && $organisation['logo'] != '' && $organisation['logo'] != 'none') {
                    $organisation['image_url'] = $this->files->link($organisation['logo']);
                } else {
                    $organisation['image_url'] = '/img/dummy/logo-placeholder.png';
                }
                
                $organisation['survey_completed_percentage'] = round($organisation['survey_completed_percentage'] * 100, 2);
                $organisation['close_risk_rec'] = round($organisation['close_risk_rec'] * 100, 2);
            }
            unset($organisation);

            return view('re-metrics/inc/table_organizations', compact('organisationTableData'));
        } catch (\Exception $e) {
            \Log::error('Error fetching organisation table data: ' . $e->getMessage());
            return back()->withErrors(['error' => 'An error occurred while fetching organisation data.']);
        }
    }

    public function tableByChart(Request $request, $chartType)
    {
        $data = match ($chartType) {
            'srfs' => json_decode(Api::getWithParams('api/v1/re-metrics/organisation/srfs/detailed', $request->all())),
            'dtrs' => json_decode(Api::getWithParams('api/v1/re-metrics/organisation/dtrs/detailed', $request->all())),
            'risk-recommendation' => json_decode(Api::getWithParams('api/v1/re-metrics/organisation/risk-recommendation-count-by-status/detailed', $request->all())),
            'desktop-reviews-impact' => json_decode(Api::getWithParams('api/v1/re-metrics/organisation/desktop-reviews-impact/detailed', $request->all())),
            'survey-programme-in-place' => json_decode(Api::getWithParams('api/v1/re-metrics/organisation/with-survey-programme/detailed', $request->all())),
            're-overview-updated' => json_decode(Api::getWithParams('api/v1/re-metrics/organisation/overview-updated/detailed', $request->all())),
            're-meeting-held' => json_decode(Api::getWithParams('api/v1/re-metrics/organisation/re-meeting-held/detailed', $request->all())),
            'accounts-by-sector' => json_decode(Api::getWithParams('api/v1/re-metrics/organisation/count-by-sector/detailed', $request->all())),
        };

        return view('re-metrics/table-by-chart', compact('chartType', 'data'));
    }
}
