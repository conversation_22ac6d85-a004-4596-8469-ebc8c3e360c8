<?php

namespace App\Http\Controllers;
use App\Http\Controllers\BaseController;
use Illuminate\Http\Request;
use App\Models\Documents;
use App\Models\FileUpload;
use App\Models\Api;
use App\Models\Survey;
use App\Services\CacheContent\GetOrganisationService;
use App\Services\CacheContent\GetOrgRiskGradingData;
use App\Services\ImageCompressorService;
use App\Services\SendSqsMessageService;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Auth;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Session;
use App\Traits\HelperTrait;
use App\Traits\SubmissionGradingTrait;
use Aws\Laravel\AwsFacade as AWS;

class SurveySubmissionsController extends BaseController
{
    use HelperTrait, SubmissionGradingTrait;

    const ROUTE_PREFIX = 'surveys';
    const GRADE_NOT_APPLICABLE_NOT_ASSESSED = 'not_applicable_not_assessed';

    public function __construct(Documents $doc, FileUpload $fileUpload, Survey $survey)
    {
        $newRequest = new Request;
        parent::__construct($newRequest);
        $this->documents = $doc;
        $this->files = $fileUpload;
        $this->survey = $survey;
    }

    function replace_first_character($search_str, $replacement_str, $src_str)
    {
        return (false !== ($pos = strpos($src_str, $search_str))) ? substr_replace($src_str, $replacement_str, $pos, strlen($search_str)) : $src_str;
    }

    public function calculateAttributeGrade(Request $request)
    {
        try {
            $fields = $request->all();
            $data   = $fields['jsonData'] ?? $fields;
    
            $data = array_filter($data, function($value) {
                return $value !== static::GRADE_NOT_APPLICABLE_NOT_ASSESSED;
            });

            return [
                'data' => json_decode(Api::post('/api/v1/standard-risk/calculate', $data)),
            ];
        } catch (\Exception $e) {
            // Send error
            $user = Auth::user();
            $formData = $fields['formData'] ?? [];
            $formData['name'] = $user->first_name . ' ' . $user->last_name;
            $formData['user_id'] = $user->id;
            $formData['email'] = $user->email;

            Api::post('/api/v1/standard-risk/send-error', $formData);
        }
    }

    /**
     * Add attachment to a submission
     */
    public function add_attachment(Request $request)
    {
        $image_files = array('jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'tiff', 'webp');
        if (strtolower($request->method()) == 'post') {

            $fields = $_FILES;
            foreach($fields as $key=>$val) {
                $file_field_name = $key;
            }
            $file = $request->file($file_field_name);
            $size = $request->file($file_field_name)->getSize();
            $path = $file->getRealPath();
            $type = $file->getMimeType();
            $fileSlug = str_replace(['.', ' '], ['_','_'], $request->file($file_field_name)->getClientOriginalName()) . '_' . $request->file_caption_key;

            // $destinationPath = public_path('uploads'); // upload path
            $extension = $request->file($file_field_name)->getClientOriginalExtension(); // getting image extension

            $notes = $request->get('notes');
            
            $data['notes'] = isset($notes[$fileSlug]) ? $notes[$fileSlug] : '';

            if($data['notes'] ===''){
                $mappedFileSlug = str_replace(['.', ' '], ['_','_'], $request->file($file_field_name)->getClientOriginalName());
                $mapped_notes=[];
                foreach($notes as $key => $value){
                    $mapped_notes[substr($key, 0, strrpos($key, '_'))]=$value;
                }
                $data['notes'] = isset($mapped_notes[$mappedFileSlug]) ? $mapped_notes[$mappedFileSlug] : '';
            }
            
            $notes_text = $data['notes'];

            $file      = ImageCompressorService::compress($file);
            $uuid      = Str::uuid()->toString();
            $fileName  = strtolower(preg_replace('/[\s_]|^%20/', '-', $request->file($file_field_name)->getClientOriginalName()));

            $upload = $this->survey->upload($file, $uuid, 'survey_uploads/' . $file_field_name);

            if ($upload['response'] == 'success') {
                $data['file_name'] = $fileName;
                $data['cloud_file_name'] = $uuid;
                $data['survey_id']    =    $request->get('survey_id');
                $data['field_name']    =    $file_field_name;
                $response = json_decode(Api::post('api/v1/surveys/add-attachment-info', $data));

                $this->setCache("csr-microsite-survey-{$request->get('survey_id')}", null);
            }
            else {
                //return Redirect::back()->with('error', $upload['message'])->withInput($request->old());
            }


            $data = [];

            $fileName_cloud = 'survey_uploads/'.$file_field_name.'/'.$uuid;
            $file = $this->survey->downloadLink($fileName_cloud, $fileName);

            array_push(
                $data, [
                "id"            => $response->id??0,
                "name"            => $fileName,
                "cloud"            => $fileName_cloud,
                "notes"            => $notes_text,
                "size"             => $size,
                "type"             => $type,
                "url"            => in_array($extension, $image_files) ? $file : '',
                "extension"     => $extension,
                "thumbnailUrl"     => in_array($extension, $image_files) ? $file : '',
                "deleteUrl"        => config('app.url').'/surveys/'.$request->get('survey_id').'/delete/'.$response->id,
                "deleteType"     => "DELETE"
                ]
            );
            return Response::json(
                [
                "files"    => $data
                ]
            );

        } else {
            if($request->has('field_name') && $request->has('survey_id')) {

                $response = json_decode(Api::get('api/v1/surveys/get-attachment-info/'.$request->get('field_name').'/'.$request->get('survey_id')));
                if($response->response == 'success') {
                    $files['files'] = [];
                    foreach ($response->data as $attachment) {
                        $fileName_cloud = 'survey_uploads/'.$attachment->field_name.'/'.$attachment->cloud_file_name;
                        $file = $this->survey->downloadLink($fileName_cloud, $attachment->file_name);

                        $file_info = pathinfo($attachment->file_name);
                        $extension = $file_info['extension'];

                        $ch = curl_init($file);

                        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                        curl_setopt($ch, CURLOPT_HEADER, true);
                        curl_setopt($ch, CURLOPT_NOBODY, true);

                        $data = curl_exec($ch);
                        $size = curl_getinfo($ch, CURLINFO_CONTENT_LENGTH_DOWNLOAD);

                        curl_close($ch);

                        $arr = [
                        "id"    =>    $attachment->id,
                        "name"    =>    $attachment->file_name,
                        "cloud"    =>    $fileName_cloud,
                        "notes"    =>    $attachment->notes,
                        "url"    =>    in_array($extension, $image_files) ? $file : '',
                        "extension" => $extension,
                        "thumbnailUrl"    =>    in_array($extension, $image_files) ? $file : '',
                        "deleteUrl"    =>    config('app.url').'/surveys/'.$request->get('survey_id').'/delete/'.$attachment->id,
                        "size"    => $size
                        ];
                        array_push($files['files'], $arr);
                    }
                    return json_encode($files);
                }
            }
        }
    }

    public function delete_attachment($survey_id, $attachment_id)
    {
        $response = json_decode(Api::get('api/v1/surveys/'.$survey_id.'/delete/'.$attachment_id));
        $this->setCache("csr-microsite-survey-{$survey_id}", null);
        if($response) {
            return Response::json(
                [
                'response' => 'success',
                'message'  => 'File deleted'
                ],
                200
            );
        }
    }

    /**
     * Add attachment to a submission
     */
    public function mobile_add_attachment(Request $request)
    {
        $logged_in = Api::check_key("external-surveyors");

        if(!$logged_in || json_decode($logged_in)->response != 'success') {
            return Response::json(
                [
                'response' => 'error',
                'message'  => 'Unable to login member'
                ],
                200
            );
        }
        if (strtolower($request->method()) == 'post') {
            if($request->get('save_attachments')) {
                $file_field_name = 'attachment';
                $file = $request->file($file_field_name);
                $size = $request->file($file_field_name)->getSize();
                $path = $file->getRealPath();
                $type = $file->getMimeType();
                $file_name = $request->get('file_name');
                $field_name = $request->get('field_name');
                $notes = $request->get('notes');
                $data['notes'] = $notes[str_replace(['.', ' '], ['_','_'], $request->file($file_field_name)->getClientOriginalName())];

                $notes_text = $data['notes'];

                    $file = $request->file($file_field_name);
                    $uuid = Str::uuid()->toString();
                    $fileName = strtolower(preg_replace('/[\s_]|^%20/', '-', $file_name));

                    $file_parts = explode(',', file_get_contents($file));

                    // print_r($file_parts); exit;

                    File::put(storage_path() .'/file_to_upload/' . $file_name, base64_decode($file_parts[1]));

                    $upload = $this->survey->upload(storage_path() .'/file_to_upload/' . $file_name, $uuid, 'survey_uploads/'.$field_name);


                if ($upload['response'] == 'success') {
                    $data['file_name'] = $fileName;
                    $data['cloud_file_name'] = $uuid;
                    $data['survey_id']    =    $request->get('survey_id');
                    $data['field_name']    =    $field_name;
                    $response = json_decode(Api::post('api/v1/surveys/add-attachment-info', $data));
                }
                else {
                    return Redirect::back()->with('error', $upload['message'])->withInput($request->old());
                }


                $data = [];

                $fileName_cloud = 'survey_uploads/'.$field_name.'/'.$uuid;
                $file = $this->survey->downloadLink($fileName_cloud, $fileName);

                array_push(
                    $data, [
                    "name"            => $fileName,
                    "notes"            => $notes_text,
                    "size"             => $size,
                    "type"             => $type,
                    "url"             => $file,
                    "thumbnailUrl"     => $file,
                    "annotations"    => json_encode([]),
                    "lat"            => '',
                    "long"            => '',
                    "deleteUrl"        => config('app.url').'/surveys/'.$request->get('survey_id').'/delete/'.$response->id,
                    "deleteType"     => "DELETE"
                    ]
                );
                return Response::json(
                    [
                    "files"    => $data
                    ]
                );
            } else {
                $fields = $_FILES;
                foreach($fields as $key=>$val) {
                    $file_field_name = $key;
                }
                $file = $request->file($file_field_name);
                $size = $request->file($file_field_name)->getSize();
                $path = $file->getRealPath();
                $type = $file->getMimeType();

                // $destinationPath = public_path('uploads'); // upload path
                $extension = $request->file($file_field_name)->getClientOriginalExtension(); // getting image extension

                $notes = $request->get('notes');
                // print_r($notes);
                $data['notes'] = $notes[str_replace('.', '_', $request->file($file_field_name)->getClientOriginalName())];
                //print_r($data);
                //exit;
                $notes_text = $data['notes'];

                    $file = $request->file($file_field_name);
                    $uuid = Str::uuid()->toString();
                    $fileName = strtolower(preg_replace('/[\s_]|^%20/', '-', $request->file($file_field_name)->getClientOriginalName()));

                    $upload = $this->survey->upload($file, $uuid, 'survey_uploads/'.$file_field_name);

                if ($upload['response'] == 'success') {
                    $data['file_name'] = $fileName;
                    $data['cloud_file_name'] = $uuid;
                    $data['survey_id']    =    $request->get('survey_id');
                    $data['field_name']    =    $file_field_name;
                    $response = json_decode(Api::post('api/v1/surveys/add-attachment-info', $data));
                }
                else {
                    //return Redirect::back()->with('error', $upload['message'])->withInput($request->old());
                }


                $data = [];

                $fileName_cloud = 'survey_uploads/'.$file_field_name.'/'.$uuid;
                $file = $this->survey->downloadLink($fileName_cloud, $fileName);

                array_push(
                    $data, [
                    "name"            => $fileName,
                    "field_name"    => $file_field_name,
                    "notes"            => $notes_text,
                    "annotations"    => json_encode([]),
                    "lat"            => '',
                    "long"            => '',
                    "size"             => $size,
                    "type"             => $type,
                    "url"             => $file,
                    "thumbnailUrl"     => $file,
                    "deleteUrl"        => config('app.url').'/surveys/'.$request->get('survey_id').'/delete/'.$response->id,
                    "deleteType"     => "DELETE"
                    ]
                );
                return Response::json(
                    [
                    "files"    => $data
                    ]
                );
            }

        } else {
            if($request->has('field_name') && $request->has('survey_id')) {

                $response = json_decode(Api::get('api/v1/surveys/get-attachment-info/'.$request->get('field_name').'/'.$request->get('survey_id')));
                if($response->response == 'success') {
                    $files['files'] = [];
                    foreach ($response->data as $attachment) {
                        $fileName_cloud = 'survey_uploads/'.$attachment->field_name.'/'.$attachment->cloud_file_name;
                        $file = $this->survey->downloadLink($fileName_cloud, $attachment->file_name);

                        $ch = curl_init($file);

                        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                        curl_setopt($ch, CURLOPT_HEADER, true);
                        curl_setopt($ch, CURLOPT_NOBODY, true);

                        $data = curl_exec($ch);
                        $size = curl_getinfo($ch, CURLINFO_CONTENT_LENGTH_DOWNLOAD);

                        curl_close($ch);

                        $arr = [
                        "name"    =>    $attachment->file_name,
                        "field_name"    => $request->get('field_name'),
                        "notes"    =>    $attachment->notes,
                        "annotations" => json_encode($attachment->annotations),
                        "url"    =>    $file,
                        "thumbnailUrl"    =>    $file,
                        "deleteUrl"    =>    config('app.url').'/surveys/'.$request->get('survey_id').'/delete/'.$attachment->id,
                        "size"    => $size,
                        "lat"    =>    isset($attachment->geolocation->lat) ? $attachment->geolocation->lat : "",
                        "long"    =>    isset($attachment->geolocation->long) ? $attachment->geolocation->long : ""
                        ];
                        array_push($files['files'], $arr);
                    }
                    return json_encode($files);
                }
            }
        }
    }

    public function mobile_addCaption(Request $request)
    {
        $logged_in = Api::check_key("external-surveyors");

        if(!$logged_in || json_decode($logged_in)->response != 'success') {
            return Response::json(
                [
                'response' => 'error',
                'message'  => 'Unable to login member'
                ],
                200
            );
        }

        $data = $request->except('_token');
        $response = json_decode(Api::post('api/v1/surveys/add-attachment-caption', $data));
        if($response->response == 'success') {
            return Response::json(
                [
                'response' => 'success',
                'data'  => $response
                ],
                200
            );
        } else {
            return Response::json(
                [
                'response' => 'error',
                'data'  => $response
                ],
                200
            );
        }
    }

    public function mobile_addAnnotation(Request $request)
    {
        $logged_in = Api::check_key("external-surveyors");

        if(!$logged_in || json_decode($logged_in)->response != 'success') {
            return Response::json(
                [
                'response' => 'error',
                'message'  => 'Unable to login member'
                ],
                200
            );
        }

        $data = $request->except('_token');
        $response = json_decode(Api::post('api/v1/surveys/add-attachment-annotation', $data));
        if($response->response == 'success') {
            return Response::json(
                [
                'response' => 'success',
                'data'  => $response
                ],
                200
            );
        } else {
            return Response::json(
                [
                'response' => 'error',
                'data'  => $response
                ],
                200
            );
        }
    }


    public function mobile_addGeolocation(Request $request)
    {
        $logged_in = Api::check_key("external-surveyors");

        if(!$logged_in || json_decode($logged_in)->response != 'success') {
            return Response::json(
                [
                'response' => 'error',
                'message'  => 'Unable to login member'
                ],
                200
            );
        }

        $data = $request->except('_token');
        $response = json_decode(Api::post('api/v1/surveys/add-attachment-geolocation', $data));
        if($response->response == 'success') {
            return Response::json(
                [
                'response' => 'success',
                'data'  => $response
                ],
                200
            );
        } else {
            return Response::json(
                [
                'response' => 'error',
                'data'  => $response
                ],
                200
            );
        }
    }

    /*
    * List of all submissions for a form
    */
    public function submissions( $form )
    {
        $response = json_decode(Api::get('api/v1/form/submissions/'.$form));
        if($response->response == "success") {
            return view(
                'forms/submissions',
                [
                'submissions' => json_decode($response->data)
                ]
            );
        }
    }

    /*
    * get a form
    */
    public function mobile_submission($submission_id)
    {
        $logged_in = Api::check_key("external-surveyors");

        if(!$logged_in || json_decode($logged_in)->response != 'success') {
            return Response::json(
                [
                'response' => 'error',
                'message'  => 'Unable to login member'
                ],
                200
            );
        }

        $response = json_decode(Api::get('api/v1/survey-submission/'.$submission_id));

        if(isset($response->response) && $response->response == 'success') {

            return json_encode($response);


        }

    }

    private function validateExternalEmails(array $externalEmails)
    {
        $validatedEmails = [];
        foreach ($externalEmails as $email) {
            $trimEmail = trim($email);
            if (filter_var($trimEmail, FILTER_VALIDATE_EMAIL) === false) {
                return [
                    'response' => 'error',
                    'error_message' => 'Invalid email listed ' . $trimEmail,
                ];
            }
            $validateExternalEmails[] = $trimEmail;
        }

        return [
            'response' => 'success',
            'data' => $validatedEmails,
        ];
    }

    /*
    * Edit a submission
    */
    public function edit( Request $request, $submission )
    {
        $data = $request->all();

        unset($data['srf_type']); //required for loss estimates calculation

        foreach($data as $key=>$value){
            if (str_contains($key, '_forced_hidden_value')) { 
                $newkey=str_replace("_forced_hidden_value", "",$key);
                unset($data[$key]);
                $data[$newkey]=$value;
            }
        }

        $surveyId = $data['survey_id'];
        $formId = $data['form_id'];

        if (Cache::has('temp-data-'.$formId.'-'.$surveyId)) {
            Cache::delete('temp-data-'.$formId.'-'.$surveyId);
        }

        $data['submitted_by'] = Session::get('user')?->login_type;
        
        if(!isset($data["surveyor_id"])) {
            $data["surveyor_id"] = Session::get('user')?->id;
        }

        $data['autosave'] = $request->ajax() ? 1 : 0;

        $csrExternalEmails = isset($data['csr_external_emails']) ? json_decode($data['csr_external_emails']) : [];
        $data['csr_external_emails'] = $csrExternalEmails;

        // print_r(Api::post('api/v1/survey-submission/'.$submission, $data)); exit;
        $response = json_decode(Api::post('api/v1/survey-submission/'.$submission, $data));
        //print_r(Api::post('api/v1/survey-submission/'.$submission, $data)); exit;
        date_default_timezone_set("Europe/London");

        $message = isset($data['csr_status']) && $data['csr_status'] === 'submitted' ? 'Survey Submitted' : 'Form updated';
        if($response->response == "success") {
            if($request->ajax()) {
                $this->recacheRelatedData($data['organisation_id'] ?? '');
                $this->clearRelatedCache($data['survey_id'] ?? '', $submission);
                return Response::json(['response' => 'success', 'message' => 'Form last auto saved at '.date('h:i a', time())]);
            }
            if($response->type == 'survey') {
                $create_user_data = (array)$response->create_user;
                // print_r((array)$response->create_user); exit;
                if(!empty((array)$response->create_user)) {
                    if(isset($create_user_data['branch'])) {
                        $create_user_data['branch'] = 1;

                    }
                    else
                    {
                        $create_user_data['branch'] = 0;
                    }
                    if(isset($create_user_data['manager'])) {
                        $create_user_data['manager'] = 1;
                    }
                    else
                    {
                        $create_user_data['manager'] = 0;
                    }

                    if(isset($create_user_data['optionRadios']) && $create_user_data['optionRadios'] == 'triton_access') {
                        $create_user_data['triton_access'] = 1;
                    }
                    else{
                        $create_user_data['triton_access'] = 0;
                    }

                    if (isset($create_user_data['astutis_access'])) {
                        $create_user_data['astutis_access'] = 1;
                    }
                    else {
                        $create_user_data['astutis_access'] = 0;
                    }

                    // $data['login_type'] = \Illuminate\Support\Facades\Session::get('user')->login_type;

                    // print_r(Api::post('/api/v1/user/store',$data));exit;

                    $response = json_decode(Api::post('/api/v1/user/store', $create_user_data));
                }

                Cache::forget("admin_survey-submission-for-id-{$submission}");

                if (!$response->has_location_id) {
                    return Redirect::to('/surveys')
                        ->with('success', $message)
                        ->with('error', 'The survey you have updated has no LOCATION ID');
                }

                $this->recacheRelatedData($data['organisation_id'] ?? '');
                $this->clearRelatedCache($data['survey_id'] ?? '', $submission);

                return Redirect::to('/surveys')->with('success', $message);

            } elseif ($response->type == 'dtr') {
                return Redirect::to('/dtr')->with('success', $message);
            } else {
                return Redirect::to('/surveys/re-reviews')->with('success', $message);
            }
        }
        else
        {
            if($request->ajax()) {
                return Response::json(['response' => 'error', 'error_message' => 'Auto save at '.date('h:i a', time()).' failed']);
            }
            die('An error occurred, please try again');
        }
    }

    /*
    * Edit a submission
    */
    public function close_issue(Request $request, $submission )
    {
        $data = $request->all();
        //dd($data);
        $response = json_decode(Api::post('api/v1/survey-submission/close/'.$submission, $data));
        if($response->response == "success") {
            $organisationId = $response->organisation_id ?? "";
            if($organisationId){
                $this->sendRebuildCacheMessage($organisationId);
            }
            $this->recacheRelatedData($data['organisation_id'] ?? '');
            $this->clearRelatedCache($data['survey_id'] ?? '', $submission);
            return json_encode($response);
        }
        else
        {
            return json_encode($response);
        }
    }

    /*
    * update a form submission
    */
    public function update( $submissionID )
    {
        // submit
    }

        /**
         * save form
         */
    public function csubmission(Request $request)
    {
        $form_data = $request->all();

        $form_data['submitted'] = '0';
        $data['submission'] = $request->get('submission');
        $data['surveyor_id'] = $request->get('surveyor_id');
        $data['survey_id']  = $request->get('survey_id');
        $data['autosave'] = $request->ajax() ? 1 : 0;

        $response = json_decode(Api::post('api/v1/survey-submission', $data));
        if($response->response == 'success') {
            return Redirect::to('/surveys')->with('success', 'Form saved');
        }
        return Redirect::back()->with('error', 'Error submitting this form data')->withInput($request->old());
    }

          /**
           * save form
           */
    public function mobile_csubmission(Request $request)
    {
        $logged_in = Api::check_key("external-surveyors");

        if(!$logged_in || json_decode($logged_in)->response != 'success') {
            return Response::json(
                [
                'response' => 'error',
                'message'  => 'Unable to login member'
                ],
                200
            );
        }
        $formId = $request->get('form_id');
        $form_data = $request->all();

        $form_data['submitted'] = '0';
        $data['submission'] = $request->get('submission');
        $data['surveyor_id'] = $request->get('surveyor_id');
        $data['survey_id']  = $request->get('survey_id');
        $data['form_id'] = $formId;
        $data['autosave'] = $request->ajax() ? 1 : 0;
        $response = Api::post('api/v1/survey-submission', $data);
        return $response;
    }

    /*
    * view a submission
    */
    public function show( $submission )
    {
        $response = json_decode(Api::get('api/v1/survey-submission/' . $submission));
        $submission = json_decode($response->data);
        // $risk_grading_attributes = $submission->risk_grading_attributes;
        $isSrgMigrated = $submission->is_srg_migrated;
        $withLegacyData = $submission->with_legacy_data;
        // dd($risk_grading_attributes, $isSrgMigrated, $withLegacyData);
        $response->data = json_decode($response->data);

        unset($response->data->commentary_description);
        unset($response->data->commentary_additional_notes);
        unset($response->data->commentaries);
        unset($response->data->narratives_visible_to_all_users);

        $response->data = json_encode($response->data);

        $submission->json = addslashes(str_replace(['\r\n', '&lt;', '&gt;', '\t'], ['[br]', '<', '>', '&nbsp;'], $response->data));
        $submission->json = str_replace('\n', '[br]', $submission->json);

        $cacheKey="risk-improvement-form-{$submission->form_id}";
        $response=$this->getSetCache($cacheKey);
        if(!$response){
            $dataEndpoint = 'api/v1/risk-improvement/form/'.$submission->form_id;
            $response=$this->getData($dataEndpoint);
            //$this->getSetCache($cacheKey,$response);
        }

        if($response->response == "success") {
            $form = json_decode($response->data, true);
            $file_data = [];
            $rr_data = [];
            $sections = explode(',', $form['section_names']);

            if(isset($form) && isset($form['fields'])) {
                $file_data_in = [];
                foreach ($form['fields'] as $field_types) {
                    foreach ($field_types as $field_type => $field_attr) {
                         $file_data_in_name = 'no-name';
                         $file_data_in = [];
                        if($field_type == 'file' || $field_type == 'risk_recommendation') {
                            foreach ($field_attr as $element) {
                                   $file_data_in[$element['name']] = $element['value'];
                                   $file_data_in['field_type'] = $field_type;
                                if($element['name'] == 'name') {
                                    $file_data_in_name = $element['value'];
                                }
                            }
                            $file_data[$file_data_in_name] = $file_data_in;
                        }
                        if($field_type == 'risk_recommendation') {
                            foreach ($field_attr as $element) {
                                if($element['name'] == 'name') {
                                        array_push($rr_data, $element['value']);
                                }
                            }
                        }
                    }
                }
            }

            $i = 1;
            foreach ($file_data as $key => $value) {
                if($value["field_type"] == 'risk_recommendation') {
                    for($i = 1; $i<=15; $i++) {
                        $old_key = $key;
                        $new_key = $key."_".$i;
                        $new_value = $value;
                        $new_value["name"] = $value["name"]."_".$i;
                        $file_data[$new_key] = $new_value;
                    }
                    unset($file_data[$old_key]);
                }
            }

            $form['fields'] = json_encode($form['fields']);

            //commenting out submission_details below as I can see it is no longer used
            // $cacheKey="submission-details-from-surveyor-{$submission->surveyor_id}";
            // $submission_details=$this->getSetCache($cacheKey);
            // if(!$submission_details){
            //     $dataEndpoint = 'api/v1/user/userorgdetails/'.$submission->surveyor_id;
            //     $submission_details=$this->getData($dataEndpoint);
            //     $this->getSetCache($cacheKey,$submission_details);
            // }
            
            // if($submission_details->response == "success") {
            //     if(isset($submission_details->organisation->name)) {
            //         $submission_details_data = [
            //         'organisation_name'    => $submission_details->organisation->name,
            //         'user'                => $submission_details->user->email,
            //         ];
            //     } else {
            //         $submission_details_data = [
            //         'organisation_name'    => 'Deleted Organisation',
            //         'user'                => $submission_details->user->email,
            //         ];
            //     }
            // } else {
            //     $submission_details_data = [
            //        'organisation_name'    => '',
            //        'user'                => 'Deleted user',
            //     ];
            // }

            $rr_titles = json_encode([]);

            if (Cache::has('rr_titles')) {
                $rr_titles = Cache::get('rr_titles');

                if ($rr_titles == 'null') {
                    Cache::forget('rr_titles');
                    $rr_titles = json_encode([]);
                }
            }
            
            $risk_grading_attributes = json_decode(Api::get('api/v1/microsite/risk-grading/' . (int)$submission->survey_id.'/true'));

            $submission->gradings_description=(object)$form['gradings_description'] ?? [];

            $riskGradings = $risk_grading_attributes->data->settings;
            $this->updateInconsistentGrading($riskGradings, $submission);

            $srf = json_decode(Api::get('api/v1/surveys/get-srf-for-id/' . (int)$submission->survey_id));
            $srfData=$srf->data??[];

            $surveyResponse = json_decode(Api::get('api/v1/surveys/' . (int)$submission->survey_id));
            $location_id = $surveyResponse && $surveyResponse->data ? $surveyResponse->data->location_id ?? $surveyResponse->data->location->location_id  : 0;
            $locationResponse = json_decode(Api::get('api/v1/location/show/' . (int)$location_id));
            $locationData = $locationResponse && $locationResponse->data ? $locationResponse->data : [];

            //Get ICow From srf values
            $iCow = array_column($srfData, 'srf_value', 'srf_key')['i_cow'] ?? null;

            //Fallback to location data if iCow is not set
            if($iCow == null){
                $iCow = $locationData->icow ?? 0;
            }

            // Use the dedicated function to extract issue_closed fields
            $issueClosedFields = $this->extractIssueClosedFields($submission);

            return view(
                'ri_submissions/show',
                [
                    'form' => $form,
                    'submission' => $submission,
                    'file_data' => $file_data,
                    'sections'  => $sections,
                    'rr_data'  =>  $rr_data,
                    'rr_titles' => $rr_titles,
                    'risk_grading_attributes' => $risk_grading_attributes,
                    'is_srg_migrated' => $isSrgMigrated,
                    'with_legacy_data' => $withLegacyData,
                    'srf' => $srfData,
                    'icow' => $iCow,
                    'issueClosedFields' => $issueClosedFields,
                ]
            );
        }
        else
        {
            die('An error occurred, please try again');
        }


        /*$response = json_decode( Api::get('api/v1/form-submission/'.$submission) );
        $submission = json_decode($response->data);
        $form = json_decode(json_decode( Api::get('api/v1/form/'.$submission->form_id) )->data, true);
        $fields = [] ;
        foreach($form['fields'] as $k => $f)
        {
            $fields[$f[key($f)][1]['value']] = $f[key($f)][0]['value'] ;
        }
        $submissionId = $submission->_id ;
        unset($submission->form_id, $submission->_id) ;
        if($response->response == "success")
        {
        return view(
              'submissions/show',
              [
                'fields' => $fields,
                'submission' => $submission,
                'submissionId' => $submissionId
            ]
        );
        }
        else
        {
        die('An error occurred, please try again') ;
        }*/
    }

    /*
    * Delete a submission
    */
    public function delete( $submission )
    {
        $response = json_decode(Api::delete('api/v1/form-submission/'.$submission));
        if($response->response == "success") {
            return Redirect::back();
        }
        else
        {
            die('An error occurred, please try again');
        }
    }

    /**
     * form page
     */
    public function showPdf( $submission, $show= 'uwr' )
    {
        $response = json_decode(Api::get('api/v1/survey-submission/'.$submission));

        $submission = json_decode($response->data);
        $submission->json = $response->data ;

        $param_resource = json_decode(
            Api::get(
                static::get_api_uri($submission->survey_id)
            )
        );

        $params = [
        'resource' => $param_resource->data,
        ];

        $response = json_decode(Api::get('api/v1/risk-improvement/form/'.$submission->form_id));


        if($response->response == "success") {
            $form = json_decode($response->data, true);
            //print_r($form);exit;
            $form['fields'] = json_encode($form['fields']);

            // print_r([
            //     'form' => $form,
            //     'submission' => $submission,
            //     ]); exit;

            $rr_colors = [
            'Requires Improvement'=> '#fc0d1b',
            'Below Average' => '#fdbf2d',
            'Average' => '#fffd38',
            'Good' => '#00b050',
            'Superior' => '#0070c0',
            'Not Applicable / Not Assessed' => '#dddddd',
            'Contact U/W within 24 hours'=> '#fc0d1b',
            'Multiple Requirements identified - monthly updates required' => '#fdbf2d',
            'Single Requirement - monitor progress' => '#fffd38',
            'Recommendations Only -generally reasonable controls' => '#00b050',
            'Satisfactory' => '#00b050',
            'Not Applicable' => '#dddddd'
            ];

            $legacyText = [
            'Contact U/W within 24 hours'=> 'Requires Improvement',
            'Multiple Requirements identified - monthly updates required' => 'Below Average',
            'Single Requirement - monitor progress' => 'Average',
            'Recommendations Only -generally reasonable controls' => 'Good',
            'Satisfactory' => 'Good',
            'Not Applicable' => 'Not Applicable / Not Assessed'
            ];

            $submission_obj = json_decode($submission->json);
            $submission_arr = (array) $submission_obj;
            $form_fields = json_decode($form['fields']);
            $form_sections = $form['section_names'];

            // echo '<pre>';

            $sections = explode(',', $form_sections);

            $new_fields = [];
            $links = [];

            foreach ($sections as $value) {
                foreach ($form_fields as $form_field) {
                    if (key($form_field) == 'select_risk_control') {
                        continue;
                    }


                    foreach ($form_field as $field_type => $form_values) {
                         $array = [];
                         $attrs = [];

                        foreach ($form_values as $form_value) {

                            //if($field_type != 'risk_recommendation') {
                            $attrs[$form_value->name] = $form_value->value;
                            //}


                            if ($form_value->name == 'section' && $form_value->value == $value ) {
                                $arr = [
                                'label' => isset($attrs['label']) ? $attrs['label'] : null,
                                'name' => isset($attrs['name']) ? $attrs['name'] : null,
                                'value' => (isset($attrs['label']) && isset($attrs['name'])) ? (isset($submission_arr[$attrs['name']]) ? $submission_arr[$attrs['name']] : null) : null,
                                'field_type' => $field_type
                                ];
                                if($field_type == 'file') {
                                    $links[$attrs['name']] = $this->fileLink($attrs['name'], $submission->survey_id);
                                }
                                if($field_type == 'risk_recommendation') {
                                    for($i=1; $i <= 15; $i++) {
                                        if(isset($submission_arr[$attrs['name'].'_'.$i.'_classification']) && $submission_arr[$attrs['name'].'_'.$i.'_classification'] != '') {
                                            $links[$attrs['name'].'_'.$i] = $this->fileLink($attrs['name'].'_'.$i, $submission->survey_id);
                                        }
                                    }
                                }
                                $new_fields[$value][] = $arr;
                            }
                        }

                    }
                }
            }


            return view(
                'ri_submissions/show_print',
                [
                'rr_colors'    => $rr_colors,
                'legacyText'=> $legacyText,
                'show'    =>    $show,
                'params' => $params,
                'submission_obj' => $submission_obj,
                'submission_arr' => $submission_arr,
                'form_fields' => $form_fields,
                'form_sections' => $form_sections,
                'sections' => $sections,
                'new_fields' => $new_fields,
                'links' => $links
                ]
            );
        }
        else
        {
            die('An error occurred, please try again');
        }
    }

    public function fileLink($field_name, $survey_id)
    {
        $image_files = array('jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'tiff');
        $response = json_decode(Api::get('api/v1/surveys/get-attachment-info/'.$field_name.'/'.$survey_id));
        if($response->response == 'success') {
            $files['files'] = [];
            foreach ($response->data as $attachment) {
                $fileName_cloud = 'survey_uploads/'.$attachment->field_name.'/'.$attachment->cloud_file_name;
                $file = $this->survey->downloadLink($fileName_cloud, $attachment->file_name, '+120 minutes');

                $file_info = pathinfo($attachment->file_name);
                $extension = $file_info['extension'];

                $ch = curl_init($file);

                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_HEADER, true);
                curl_setopt($ch, CURLOPT_NOBODY, true);

                $data = curl_exec($ch);
                $size = curl_getinfo($ch, CURLINFO_CONTENT_LENGTH_DOWNLOAD);

                curl_close($ch);

                $arr = [
                "name"    =>    $attachment->file_name,
                "cloud"    =>    $fileName_cloud,
                "notes"    =>    $attachment->notes,
                "url"    =>    in_array($extension, $image_files) ? $file : '',
                "extension" => $extension,
                "thumbnailUrl"    =>    in_array($extension, $image_files) ? $file : '',
                "deleteUrl"    =>    config('app.url').'/surveys/'.$survey_id.'/delete/'.$attachment->id,
                "size"    => $size
                ];
                array_push($files['files'], $arr);
            }
            return $files;
        }

    }


    /**
     * Print form
     */
    public function printPdf(Request $request)
    {
        $id = $request->get('id');
        $survey_id = $request->get('survey_id');
        $pdf_type = $request->get('pdf_type');
        $response = json_decode(Api::get('/api/v1/survey-submission/' . $id));
        $submission = json_decode($response->data);
        // dd($submission);
        Event::fire('log', array($submission->_id,'Submission','Print','Submission Printed'));
        $html = stripslashes($request->get('html'));

        $orientation = $request->get('orientation');
        $html = preg_replace('/[\t\n\r\0\x0B]/', '', $html);
        $html = preg_replace('/([\s])\1+/', ' ', $html);
        $html = trim($html);
        $fileName = "pdf_".uniqid().'.html';
        $pdfFileName = "pdf_".uniqid().'.pdf';
        File::put(storage_path() . '/file_to_download/'.$fileName, $html);
        $pdf = PDF::loadFile(storage_path() . '/file_to_download/'.$fileName)
            ->setPaper('a4')
            ->setOrientation($orientation)
            ->setWarnings(false)
            ->setOption('background', true)
        // ->setOption('margin-top','0mm')
        // ->setOption('margin-left','0mm')
        // ->setOption('margin-right','0mm')
            ->setOption('footer-font-size', '10')
            ->setOption('footer-font-name', 'Ariel')
            ->setOption('footer-right', 'Page [page] of [topage]')
            ->setOption('footer-left', 'CONFIDENTIAL')
            ->setOption('footer-spacing', '3')
            ->save(storage_path() . '/file_to_download/'.$pdfFileName);

        $number_of_pages = exec('pdftk '.storage_path() . '/file_to_download/'.$pdfFileName.' dump_data | grep NumberOfPages | awk \'{print $2}\'');

        // echo 'pdftk '.storage_path() . '/file_to_download/'.$pdfFileName.' dump_data | grep NumberOfPages | awk \'{print $2}\'';
        // echo '<br>';

        $first_page = exec('pdftk '.storage_path() . '/file_to_download/'.$pdfFileName.' cat 1-1 output '.storage_path() . '/file_to_download/first_'.$pdfFileName);

        // echo 'pdftk '.storage_path() . '/file_to_download/'.$pdfFileName.' cat 1-1 output '.storage_path() . '/file_to_download/first_'.$pdfFileName;
        // echo '<br>';

        $other_pages = exec('pdftk '.storage_path() . '/file_to_download/'.$pdfFileName.' cat 2-'.$number_of_pages.' output '.storage_path() . '/file_to_download/other_'.$pdfFileName);

        // echo 'pdftk '.storage_path() . '/file_to_download/'.$pdfFileName.' cat 2-'.$number_of_pages.' output '.storage_path() . '/file_to_download/other_'.$pdfFileName;
        // echo '<br>';

        $cover_page = exec('pdftk '.storage_path() . '/file_to_download/first_'.$pdfFileName.' background '.storage_path().'/doc_header.pdf output '.storage_path() . '/file_to_download/cover_'.$pdfFileName);

        // echo 'pdftk '.storage_path() . '/file_to_download/first_'.$pdfFileName.' background '.storage_path().'/doc_header.pdf output '.storage_path() . '/file_to_download/cover_'.$pdfFileName;
        // echo '<br>';

        $file = exec('pdftk '.storage_path() . '/file_to_download/cover_'.$pdfFileName.' '.storage_path() . '/file_to_download/other_'.$pdfFileName.' output '.storage_path() . '/file_to_download/processed_'.$pdfFileName);

        $pdf_path = storage_path() . '/file_to_download/processed_'.$pdfFileName;

        $upload = $this->files->upload($pdf_path, 'survey_attachments/pdf/'.$pdf_type.'_'.$survey_id.'.pdf');


        return Response::download($pdf_path);

    }


    /*
    * GET: get attachments from rackspace
    */

    public function retrieveAttachment($name)
    {

        $document = json_decode(Api::get('/api/v1/attachment/find/'.$name));
        if($document->response == 'success') {

            $fileName = $document->submission->organisation_id . '/' .$document->data->document_store_name;
            $file = $this->documents->download($fileName, $name);
            if($file['response'] == 'success') {
                $decrypt = $this->documents->decrypt($file['data'], $name);
            }
            else
            {
                return Response::json(['response' => 'error', 'message' => $file['message']]);
            }
            $fileName = $document->data->document_title;
            return Response::download($decrypt['data'], $fileName, array('Content-Type' => 'text/plain'));


        }
    }

    public function downloadFileUploads($cloud_folder, $file_uploader_name, $cloud_file_name, $file_name)
    {

        $url = $this->survey->download($cloud_folder.'/'.$file_uploader_name.'/'.$cloud_file_name, $cloud_file_name);

        if($url) {
            return Response::download($url, $file_name);
        }
    }



    public function printPdfBinder(Request $request, $submission, $show= 'UWR' )
    {

        $response = json_decode(Api::get('api/v1/survey-submission/'.$submission));

        $submission = json_decode($response->data);
        if($this->files->exists('survey_attachments/pdf/UWR_'.$submission->survey_id.'.pdf')) {
            return $this->files->link('survey_attachments/pdf/UWR_'.$submission->survey_id.'.pdf', '120 minutes');
        } else {
             $submission->json = $response->data ;

            $param_resource = json_decode(
                Api::get(
                    static::get_api_uri($submission->survey_id)
                )
            );

            $params = [
            'resource' => $param_resource->data,
            ];

            $response = json_decode(Api::get('api/v1/risk-improvement/form/'.$submission->form_id));


            if($response->response == "success") {
                $form = json_decode($response->data, true);
                //print_r($form);exit;
                $form['fields'] = json_encode($form['fields']);

                // print_r([
                //     'form' => $form,
                //     'submission' => $submission,
                //     ]); exit;

                $rr_colors = [
                'Requires Improvement'=> '#fc0d1b',
                'Below Average' => '#fdbf2d',
                'Average' => '#fffd38',
                'Good' => '#00b050',
                'Superior' => '#0070c0',
                'Not Applicable / Not Assessed' => '#dddddd',
                'Contact U/W within 24 hours'=> '#fc0d1b',
                'Multiple Requirements identified - monthly updates required' => '#fdbf2d',
                'Single Requirement - monitor progress' => '#fffd38',
                'Recommendations Only -generally reasonable controls' => '#00b050',
                'Satisfactory' => '#00b050',
                'Not Applicable' => '#dddddd'
                ];

                $legacyText = [
                'Contact U/W within 24 hours'=> 'Requires Improvement',
                'Multiple Requirements identified - monthly updates required' => 'Below Average',
                'Single Requirement - monitor progress' => 'Average',
                'Recommendations Only -generally reasonable controls' => 'Good',
                'Satisfactory' => 'Good',
                'Not Applicable' => 'Not Applicable / Not Assessed'
                ];

                $submission_obj = json_decode($submission->json);
                $submission_arr = (array) $submission_obj;
                $form_fields = json_decode($form['fields']);
                $form_sections = $form['section_names'];

                // echo '<pre>';

                $sections = explode(',', $form_sections);

                $new_fields = [];
                $links = [];

                foreach ($sections as $value) {
                    foreach ($form_fields as $form_field) {
                        if (key($form_field) == 'select_risk_control') {
                            continue;
                        }


                        foreach ($form_field as $field_type => $form_values) {
                            $array = [];
                            $attrs = [];

                            foreach ($form_values as $form_value) {

                                //if($field_type != 'risk_recommendation') {
                                $attrs[$form_value->name] = $form_value->value;
                                //}


                                if ($form_value->name == 'section' && $form_value->value == $value ) {
                                    $arr = [
                                        'label' => isset($attrs['label']) ? $attrs['label'] : null,
                                        'name' => isset($attrs['name']) ? $attrs['name'] : null,
                                        'value' => (isset($attrs['label']) && isset($attrs['name'])) ? (isset($submission_arr[$attrs['name']]) ? $submission_arr[$attrs['name']] : null) : null,
                                        'field_type' => $field_type
                                     ];
                                    if($field_type == 'file') {
                                        $links[$attrs['name']] = $this->fileLink($attrs['name'], $submission->survey_id);
                                    }
                                    if($field_type == 'risk_recommendation') {
                                        for($i=1; $i <= 15; $i++) {
                                            if(isset($submission_arr[$attrs['name'].'_'.$i.'_classification']) && $submission_arr[$attrs['name'].'_'.$i.'_classification'] != '') {
                                                $links[$attrs['name'].'_'.$i] = $this->fileLink($attrs['name'].'_'.$i, $submission->survey_id);
                                            }
                                        }
                                    }
                                     $new_fields[$value][] = $arr;
                                }
                            }

                        }
                    }
                }


                $view = view(
                    'ri_submissions/print_html',
                    [
                    'rr_colors'    => $rr_colors,
                    'legacyText'=> $legacyText,
                    'show'    =>    $show,
                    'params' => $params,
                    'submission_obj' => $submission_obj,
                    'submission_arr' => $submission_arr,
                    'form_fields' => $form_fields,
                    'form_sections' => $form_sections,
                    'sections' => $sections,
                    'new_fields' => $new_fields,
                    'links' => $links
                    ]
                );

                $id = $submission_arr['_id'];
                $survey_id = $submission_arr['survey_id'];
                $pdf_type = "UWR";

                $orientation = $request->get('orientation');
                $html = preg_replace('/[\t\n\r\0\x0B]/', '', $view);
                $html = preg_replace('/([\s])\1+/', ' ', $view);
                $html = trim($html);
                $fileName = "pdf_".uniqid().'.html';
                $pdfFileName = "pdf_".uniqid().'.pdf';
                File::put(storage_path() . '/file_to_download/'.$fileName, $html);
                $pdf = PDF::loadFile(storage_path() . '/file_to_download/'.$fileName)
                    ->setPaper('a4')
                    ->setOrientation($orientation)
                    ->setWarnings(false)
                    ->setOption('background', true)
                // ->setOption('margin-top','0mm')
                // ->setOption('margin-left','0mm')
                // ->setOption('margin-right','0mm')
                    ->setOption('footer-font-size', '10')
                    ->setOption('footer-font-name', 'Ariel')
                    ->setOption('footer-right', 'Page [page] of [topage]')
                    ->setOption('footer-left', 'CONFIDENTIAL')
                    ->setOption('footer-spacing', '3')
                    ->save(storage_path() . '/file_to_download/'.$pdfFileName);

                $number_of_pages = exec('pdftk '.storage_path() . '/file_to_download/'.$pdfFileName.' dump_data | grep NumberOfPages | awk \'{print $2}\'');


                $first_page = exec('pdftk '.storage_path() . '/file_to_download/'.$pdfFileName.' cat 1-1 output '.storage_path() . '/file_to_download/first_'.$pdfFileName);


                $other_pages = exec('pdftk '.storage_path() . '/file_to_download/'.$pdfFileName.' cat 2-'.$number_of_pages.' output '.storage_path() . '/file_to_download/other_'.$pdfFileName);


                $cover_page = exec('pdftk '.storage_path() . '/file_to_download/first_'.$pdfFileName.' background '.storage_path().'/doc_header.pdf output '.storage_path() . '/file_to_download/cover_'.$pdfFileName);


                $file = exec('pdftk '.storage_path() . '/file_to_download/cover_'.$pdfFileName.' '.storage_path() . '/file_to_download/other_'.$pdfFileName.' output '.storage_path() . '/file_to_download/processed_'.$pdfFileName);

                $pdf_path = storage_path() . '/file_to_download/processed_'.$pdfFileName;

                $upload = $this->files->upload($pdf_path, 'survey_attachments/pdf/'.$pdf_type.'_'.$survey_id.'.pdf');


                return Response::download($pdf_path);
            }
            else
            {
                die('An error occurred, please try again');
            }
        }
    }

    private function sendRebuildCacheMessage($organisationId)
    {
        try{
            AWS::createClient('Sqs')->sendMessage([
                'QueueUrl'          => config('app.aws.force_cache_rebuild'),
                'MessageBody'       => $organisationId,
                'MessageAttributes' => [],
                'DelaySeconds'      => 1
            ]);
        }catch(\Exception $e){
            \Log::info($e->getMessage());
        }
        
    }

    public function recacheRelatedData($orgId)
    {
        if (empty($orgId)) {
            return;
        }

        $this->setCache("organisation-risk-grading-data-for-organisation-{$orgId}", null);

        try {
            SendSqsMessageService::sendMessages([
                [
                    'serviceClass' => GetOrgRiskGradingData::class,
                    'params' => $orgId ?? '',
                ],
                [
                    'serviceClass' => GetOrganisationService::class,
                    'params' => $orgId ?? '',
                ],
                [
                    'serviceClass' => 'App\Services\CacheContent\GetOrganisationDetailsForDashboard',
                    'params' => $orgId ?? '',
                    'isClient' => true,
                ],
            ]);
        } catch (\Exception $e) {
            \Log::error($e->getMessage());
        }
        
    }

    public function clearRelatedCache($surveyId, $submission = null) 
    {
        $cacheKeys = [
            !empty($submission) ? "survey-submission-for-id-{$submission}" : null,
            !empty($surveyId) ? "survey-data-for-surveyid-$surveyId" : null,
            !empty($surveyId) ? "csr-microsite-survey-$surveyId" : null,
            !empty($surveyId) ? "csr-microsite-survey-messages-{$surveyId}" : null,
            !empty($surveyId) ? "messages-for-survey-id-{$surveyId}" : null,
            !empty($surveyId) ? "admin_csr-microsite-survey-details-{$surveyId}" : null,
            !empty($surveyId) ? "admin_survey-data-for-surveyid-{$surveyId}" : null,
            !empty($surveyId) ? "admin_survey-microsite-risk-grading-{$surveyId}" : null,
        ];

        foreach (array_filter($cacheKeys) as $cacheKey) {
            Cache::forget($cacheKey);
        }
    }

    /**
     * Extract all fields ending with _issue_closed from submission data
     * 
     * @param array|object $submissionData
     * @return array
     */
    private function extractIssueClosedFields($submissionData): array
    {
        $issueClosedFields = [];
        $submissionArray = is_object($submissionData) ? (array)$submissionData : $submissionData;
        
        foreach ($submissionArray as $key => $value) {
            if (str_ends_with($key, '_issue_closed')) {
                $issueClosedFields[$key] = $value;
            }
        }
        
        return $issueClosedFields;
    }
}
