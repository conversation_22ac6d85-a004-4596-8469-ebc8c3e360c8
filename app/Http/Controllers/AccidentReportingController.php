<?php

namespace App\Http\Controllers;

use App\Models\Api;
use App\Models\FileUpload;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Cache;

class AccidentReportingController extends BaseController
{
    private FileUpload $file;

    public function __construct(Request $request, FileUpload $fileUpload)
    {
        parent::__construct($request);

        $this->file = $fileUpload;
    }

    public $colors = [
        0 => ['#445E8C', '#46BFBD', '#FDB45C'],
        1 => ['#576E98', '#5AD3D1', '#FFC870'],
    ];

    /*
     * Reports Page
     */
    public function index()
    {
        if(Cache::has("all_sectors")) {
            $sectors = Cache::get("all_sectors");
        }else{
            $sectors = json_decode(Api::get('api/v1/sector/all'));
            Cache::put("all_sectors", $sectors, 3000);
        }
        if (!Session::has('accident-reporting.chart')) {
            Session::put('accident-reporting.chart', 'accidents_by_time_of_occurence');
        }
        if (!Session::has('accident-reporting.type')) {
            Session::put('accident-reporting.type', 'bar');
        }
        if (!Session::has('accident-reporting.start')) {
            Session::put('accident-reporting.start', date('d-m-Y', strtotime('-1 month')));
        }
        if (!Session::has('accident-reporting.end')) {
            Session::put('accident-reporting.end', date('d-m-Y'));
        }
        if (!Session::has('accident-reporting.organisation_id')) {
            Session::put('accident-reporting.organisation_id', '1');
        }
        if (!Session::has('accident-reporting.claims')) {
            Session::put('accident-reporting.claims', '0');
        }
        if (!Session::has('accident-reporting.selected_sector')) {
            Session::put('accident-reporting.selected_sector', '0');
        }

        $data = json_decode(
            Api::get(
                'api/v1/reports/' . Session::get('accident-reporting.chart') . '/' .
                strtotime(Session::get('accident-reporting.start')) . '/' .
                strtotime(Session::get('accident-reporting.end')) . '/' .
                Session::get('accident-reporting.organisation_id') . '/' .
                Session::get('accident-reporting.claims') . '/' .
                Session::get('accident-reporting.selected_sector')
            )
        );
        $org = [];
        if(Cache::has("all_organisations")) {
            $org = Cache::get("all_organisations");
        }else{
            $org = json_decode(Api::get('api/v1/organisation/all'))->data;
            Cache::put("all_organisations", $org, 3000);
        }

        return view(
            'accident-reporting/index',
            [
                'chart' => Session::get('accident-reporting.chart'),
                'claims' => Session::get('accident-reporting.claims'),
                'type' => Session::get('accident-reporting.type'),
                'start' => Session::get('accident-reporting.start'),
                'end' => Session::get('accident-reporting.end'),
                'organisation_id' => Session::get('accident-reporting.organisation_id'),
                'selected_sector' => Session::get('accident-reporting.selected_sector'),
                'organisations' => $org,
                'data' => $data,
                'colors' => $this->colors,
                'sectors' => $sectors->data,
            ]
        );
    }

    /*
    * filter charts
    */
    public function filter(Request $request)
    {
        Session::put('accident-reporting.chart', $request->get('chart'));
        Session::put('accident-reporting.type', $request->get('type'));
        Session::put('accident-reporting.start', $request->get('start'));
        Session::put('accident-reporting.end', $request->get('end'));
        Session::put('accident-reporting.organisation_id', $request->get('organisation_id'));
        Session::put('accident-reporting.selected_sector', $request->get('sector'));
        if ($request->has('claims')) {
            Session::put('accident-reporting.claims', $request->get('claims'));
        } else {
            Session::put('accident-reporting.claims', '0');
        }
        return Redirect::back();
    }


    public function printPreview($orientation = "portrait")
    {
        if (!Session::has('accident-reporting.chart')) {
            Session::put('accident-reporting.chart', 'accidents_by_time_of_occurence');
        }
        if (!Session::has('accident-reporting.type')) {
            Session::put('accident-reporting.type', 'bar');
        }
        if (!Session::has('accident-reporting.start')) {
            Session::put('accident-reporting.start', date('d-m-Y', strtotime('-1 month')));
        }
        if (!Session::has('accident-reporting.end')) {
            Session::put('accident-reporting.end', date('d-m-Y'));
        }
        if (!Session::has('accident-reporting.organisation_id')) {
            Session::put('accident-reporting.organisation_id', '1');
        }

        $organisation = json_decode(Api::get('api/v1/organisation/' . Session::get('accident-reporting.organisation_id')));
        if ($organisation->data->logo != 'none') {
            $organisation->data->image_url = $this->file->link($organisation->data->logo);
        } else {
            $organisation->data->image_url = '/img/dummy/logo-placeholder.png';
        }

        $data = json_decode(
            Api::get(
                'api/v1/reports/' . Session::get('accident-reporting.chart') . '/' .
                strtotime(Session::get('accident-reporting.start')) . '/' .
                strtotime(Session::get('accident-reporting.end')) . '/' .
                Session::get('accident-reporting.organisation_id')
            )
        );
        return view(
            'accident-reporting/printPreview',
            [
                'type' => Session::get('accident-reporting.type'),
                'chart' => Session::get('accident-reporting.chart'),
                'data' => $data,
                'colors' => $this->colors,
                'organisation' => $organisation->data,
            ]
        );
    }

    public function printing(Request $request)
    {
        if (!Session::has('accident-reporting.chart')) {
            Session::put('accident-reporting.chart', 'accidents_by_time_of_occurence');
        }
        if (!Session::has('accident-reporting.type')) {
            Session::put('accident-reporting.type', 'bar');
        }
        if (!Session::has('accident-reporting.start')) {
            Session::put('accident-reporting.start', date('d-m-Y', strtotime('-1 month')));
        }
        if (!Session::has('accident-reporting.end')) {
            Session::put('accident-reporting.end', date('d-m-Y'));
        }
        $data = $request->all();
        $orientation = $data['orientation'];
        $html = $data['pdf_html'];

        $pdf = App::make('snappy.pdf.wrapper');
        $pdf->loadHTML($html);
        if ($orientation == "landscape") {
            $pdf->setOrientation('landscape');
        }
        return $pdf->stream();
    }
}