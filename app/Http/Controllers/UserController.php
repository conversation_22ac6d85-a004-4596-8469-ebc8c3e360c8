<?php

namespace App\Http\Controllers;

use App\Models\Api;
use App\Models\FileUpload;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Http\Controllers\BaseController;
use App\Services\CacheContent\GetOrganisationService;
use App\Services\SendSqsMessageService;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Response;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\Validator;
use PragmaRX\Google2FA\Google2FA;

class UserController extends BaseController
{

    //TEMPLATE PATH
    const TEMPLATE_PATH = '/user';

    //ROUTE PREFIX
    const ROUTE_PREFIX = 'user';

    public function __construct(Request $request, FileUpload $fileUpload)
    {
        parent::__construct($request);
        $this->files = $fileUpload;
        $this->middleware('user-controller-checker');
    }

    /**
     * get branch users
     * @throws GuzzleException
     */
    public function branchUsers($org_id = 0)
    {
        if ($org_id == 0) {
            $users = json_decode(Api::get('api/v1/user/branch-users'));
        } else {
            $users = json_decode(Api::get('api/v1/user/branch-users/' . $org_id));
        }

        echo json_encode($users->data);
    }

    /**
     * Reset Login GET
     * @throws GuzzleException
     */

    public function resetLogin($id)
    {
        $resetLogin = json_decode(Api::get('/api/v1/user/reset-login/' . $id));

        if (isset($resetLogin->response) && !empty($resetLogin->response == 'success')) {
            return Redirect::route('liberty-users.edit', $id)->with(array('message' => 'Reset Login Success'));
        }
    }

    /**
     * Reset Login GET
     * @throws GuzzleException
     */

    public function resetLoginExternal($id)
    {
        $resetLoginExternal = json_decode(Api::get('/api/v1/user/reset-login-external/' . $id));

        if (isset($resetLoginExternal->response) && !empty($resetLoginExternal->response == 'success')) {
            return Redirect::route('external-surveyors.edit', $id)->with(array('message' => 'Reset Login Success'));
        }
    }

    public function resetLoginAttempts($id)
    {
        $responseData = json_decode(Api::get('/api/v1/user/reset-login-attempts/' . $id));
        $response = data_get($responseData, 'response');
        
        if ($response === 'success') {
            return redirect()->back()->with(['success' => 'Reset login attempts success!']);
        }

        return redirect()->back()->withErrors(['Reset login attempts failed!']);
    }

    /**
     * Create a new user for Organisation
     *
     * @param  int $id
     * @return void
     */
    public function organisationCreate($id)
    {
        $organisations = json_decode(Api::get('/api/v1/organisation/' . $id));
        if ($organisations->response == "success") {
            return view(
                static::TEMPLATE_PATH . '/create',
                ['organisation' => $organisations->data]
            );
        } else {
            //todo: add error handling
        }
    }

    /**
     * GET: Edit User
     */
    public function organisationEdit($id, $userID)
    {
        //get user and check user is within organisation
        // Use Hashed email t
        $user = json_decode(Api::get('/api/v1/user/find/' . $userID));
        $organisations = json_decode(Api::get('/api/v1/organisation/' . $id . '?user_id=' . $userID));

        if (isset($user) && $user->data->organisation_id == $id) {
            if (!is_null($user->data->file)) {
                $user->data->fileLink = $this->files->link($user->data->file);
            }
            $viewBag=array(
                'user'  => $user->data,
                'organisation' => $organisations->data,
                'type' => 'organisation'
            );
            return view(static::TEMPLATE_PATH . '/edit',$viewBag);
        } else {
            return Redirect::route('organisation.show', $id)
                ->with('error', 'User does not exist');
        }
    }

    /*
     *
     * PUT: Update User
     */
    public function Update(Request $request, $id = null)
    {
        //Rules
        $rules = [
            'email' => 'required|email',
            'first_name' => 'required',
            'last_name' => 'required',
            'optionRadios' => 'required'
        ];
        if ($request->has('branch')) {
            $rules['branch_name'] = 'required';
        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return Redirect::route('organisation.user.edit', array('id' => $request->organisation_id, 'userID' => $request->get('id')))
                ->withErrors($validator->errors())
                ->WithInput($request->old());
        } else {
            $data = $request->all();

            unset($data['_method']);
            unset($data['_token']);

            $user = json_decode(Api::get('/api/v1/user/find/'.$request->get('id')));
            $organisation_id = $user->data->organisation_id;

            if ($request->hasFile('branch_image')) {
                $dimensions = getimagesize($request->file('branch_image'));

                // [0] = width, [1] = height
                if ($dimensions[0] > 218 || $dimensions[1] > 208) {
                    return Redirect::route('organisation.user.edit', array('id' => $id))
                        ->withInput($request->old())
                        ->with('error', 'The image size must be a maximum of 218x208 maximum');
                }

                $file = $request->file('branch_image');

                $data['original_filename'] = $request->file('branch_image')->getClientOriginalName();

                $uuid = Str::uuid()->toString();

                if (!is_bool($this->files->upload($file->getRealPath(), $uuid))) {
                    return Redirect::route('organisation.user.edit', array('id' => $id))
                        ->withInput($request->old())
                        ->with('error', 'Unable to upload branch image');
                }

                $data['file'] = $uuid;

                unset($data['branch_image']);
            } else {
                $user = json_decode(Api::get('/api/v1/user/find/' . $request->get('id')));
                $data['file'] = $user->data->file;
                $data['original_filename'] = $user->data->original_filename;
            }

            $data['branch'] = isset($data['branch']) ? 1 : 0;

            $data['manager'] = isset($data['manager']) ? 1 : 0;

            $data['client_dashboard_access'] = (isset($data['client_dashboard_access']) && isset($data['manager']))
                ? 1 : 0;

            $data['safetymedia_access'] = isset($data['safetymedia_access']) ? 1 : 0;

            $data['astutis_access'] = isset($data['astutis_access']) ? 1 : 0;

            $data['croner_access'] = isset($data['croner_access']) ? 1 : 0;

            switch ($data['optionRadios']) {
                case 'c_live_access':
                    $data['c_live_access'] = 1;
                    break;
                case 'triton_access':
                    $data['triton_access'] = 1;
                    break;
                default:
                    $data['triton_access'] = 0;
                    $data['c_live_access'] = 0;
            }

            unset($data['optionRadios']);

            $data['has_previsico_access'] = isset($data['has_previsico_access']) ? 1 : 0;

            //print_r(Api::post('/api/v1/user/update', $data));exit;

            $response = json_decode(Api::post('/api/v1/user/update', $data));


            if ($response->response == "success") {
                // if (isset($data['send_invite'])) {
                //     Api::get('/api/v1/user/send-welcome/' . $request->get('id'));
                // }
                $this->recacheRelatedData($user->data->organisation_id ?? '');

                if ($data['redirect'] == "all") {
                    if (isset($data['send_invite'])) {
                        return Redirect::route('organisation.user.edit', array('id' => $organisation_id, 'userID' => $data['id']))->with('success', 'User updated, and invite sent.');
                    }
                    return Redirect::route('users.index')->with('success', 'User updated');
                } else {
                    if (isset($data['send_invite'])) {
                        return Redirect::route('organisation.user.edit', array('id' => $organisation_id, 'userID' => $data['id']))->with('success', 'User updated, and invite sent.');
                    }
                    return Redirect::route('organisation.show', array('id' => $organisation_id))
                        ->with('success', 'User Updated successfully');
                }
            } else {

                return Redirect::route(
                    'organisation.user.edit', array('id' => $organisation_id, 'userID' => $data['id'])
                )
                    ->withInput($request->old())
                    ->with('error', $response->message);
            }
        }
    }

    public function UpdateContacts(Request $request, $id)
    {
        $rules = [
            'email' => 'required|email',
            'first_name' => 'required',
            'last_name' => 'required',
        ];

        $validator = Validator::make($request->all(), $rules);
        $user = json_decode(Api::get('/api/v1/user/find/' . $id));

        if ($validator->fails()) {
            return Redirect::route('organisation.edit', array('id' => $user->data->organisation_id))
                ->withErrors($validator->errors())
                ->WithInput($request->old());
        } else {
            $data = $request->all();

            unset($data['_method']);
            unset($data['_token']);

            foreach ($data as $key => $value) {
                $user->data->$key = $value;
            }

            $response = json_decode(Api::post('/api/v1/user/update', $data));

            if ($response->response == "success") {
                $this->recacheRelatedData($user->data->organisation_id ?? '');

                return Redirect::route('organisation.edit', array('id' => $user->data->organisation_id))
                    ->with('success', 'User Updated successfully');
            } else {
                return Redirect::route('organisation.edit', array('id' => $user->data->organisation_id))
                    ->withErrors($validator->errors())
                    ->WithInput($request->old());
            }
        }
    }


    /*
     *
     * POST: Store user
     */

    public function organisationStore(Request $request, $id)
    {
        //validate the info
        $rules = [
            'email' => 'email|required',
            'first_name' => 'required',
            'last_name' => 'required',
            'optionRadios' => 'required'
        ];

        if ($request->has('branch')) {
            $rules['branch_name'] = 'required';
        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return Redirect::route('organisation.user.create', array('id' => $id))
                ->withInput($request->old())
                ->withErrors($validator->errors());
        } else {
            $data = $request->all();

            unset($data['_token']);

            if ($request->hasFile('branch_image')) {
                $dimensions = getimagesize($request->file('branch_image'));

                // [0] = width, [1] = height
                if ($dimensions[0] > 218 || $dimensions[1] > 208) {
                    return Redirect::route('organisation.user.create', array('id' => $id))
                        ->withInput($request->old())
                        ->with('error', 'The image size must be a maximum of 218x208 maximum');
                }

                $file = $request->file('branch_image');

                $data['original_filename'] = $request->file('branch_image')->getClientOriginalName();

                $name = Str::uuid()->toString();

                if (!is_bool($this->files->upload($file->getRealPath(), $name))) {
                    return Redirect::route('organisation.user.create', array('id' => $id))
                        ->withInput($request->old())
                        ->with('error', 'Unable to upload branch image');
                }

                $data['file'] = $name;

                unset($data['branch_image']);
            }

            if (isset($data['branch'])) {
                $data['branch'] = 1;
            } else {
                $data['branch'] = 0;
            }

            if (isset($data['manager'])) {
                $data['manager'] = 1;
            } else {
                $data['manager'] = 0;
            }

            if (isset($data['client_dashboard_access']) && isset($data['manager'])) {
                $data['client_dashboard_access'] = 1;
            } else {
                $data['client_dashboard_access'] = 0;
            }

            if (isset($data['safetymedia_access'])) {
                $data['safetymedia_access'] = 1;
            } else {
                $data['safetymedia_access'] = 0;
            }

            if (isset($data['croner_access'])) {
                $data['croner_access'] = 1;
            } else {
                $data['croner_access'] = 0;
            }

            if (isset($data['astutis_access'])) {
                $data['astutis_access'] = 1;
            } else {
                $data['astutis_access'] = 0;
            }

            if ($data['optionRadios'] == 'c_live_access') {
                $data['c_live_access'] = 1;
            } else {
                $data['c_live_access'] = 0;
            }

            if ($data['optionRadios'] == 'triton_access') {
                $data['triton_access'] = 1;
            } else {
                $data['triton_access'] = 0;
            }

            unset($data['optionRadios']);

            //print_r(Api::post('/api/v1/user/store', $data));exit;

            $response = json_decode(Api::post('/api/v1/user/store', $data));

            if ($response->response == 'success') {
                return Redirect::route('organisation.show', array('id ' => $id))
                    ->with('success', 'User created successfully');
            } else {
                return Redirect::route('organisation.user.create', array('id' => $id))
                    ->withInput($request->old())
                    ->with('error', $response->errors);
            }
        }
    }


    /*
     *
     * DELETE: destroy user
     *
     */
    public function organisationDestroy($id, $userID)
    {
        if (isset($id) && isset($userID)) {
            $data = ['id' => $userID, 'organisation_id' => $id];
            $response = json_decode(Api::post('/api/v1/user/delete', $data));

            if ($response->response == 'success') {
                $this->recacheRelatedData($id);
                
                return Redirect::route('organisation.show', $id)
                    ->with('success', 'User delete successfully');
            } else {
                return Redirect::route('organisation.user.edit', array('organisation' => $id, 'userID' => $userID))
                    ->with('error', $response->message);
            }
        }
    }

    public function generateLink($userID)
    {
        if (isset($userID)) {
            //$data = ['id' => $userID];
            $response = json_decode(Api::get('/api/v1/user/link/' . $userID));

            //print_r($response); exit;

            if ($response->response == "success") {
                return view(
                    static::TEMPLATE_PATH . '/edit',
                    array(
                        'user' => $response->data,
                        'type'  =>   'all'
                    )
                );
            }
        }
    }


    public function organisationUsers($id)
    {

        $response = json_decode(Api::get('/api/v1/user/organisation/' . $id));

        if ($response->response == "success") {
            return Response::json($response->data, '200');
        }
    }


    /**
     * SHow all Users
     *
     * @return mixed
     * @throws Exception
     */
    public function index(Request $request)
    {
        $from_survey = $request->has('from_survey') ? 1 : 0;
        $search = $request->has('search') ? $request->get('search') : '';
        $page = $request->get('page');
        $page = isset($page) ? $request->get('page') : 1;
        $limit = $request->has('limit') ? $request->get('limit') : 10;

        $users = Session::get('user')->login_type == 'broker-user'
            ? json_decode(Api::get('/api/v1/user/broker/' . Session::get('user')->broker_id . '/' . $page . '/' . $limit . '?search=' . urlencode($search) . '&from_survey=' . $from_survey))
            : json_decode(Api::get('/api/v1/user/all/' . $page . '/' . $limit . '?search=' . urlencode($search) . '&from_survey=' . $from_survey));

        if ($users->response == "success") {
            return view(
                static::TEMPLATE_PATH . '/index',
                array(
                    'users'         => $users->data,
                    'total'         => $users->total,
                    'limit'         => $limit,
                    'page'          => $page,
                    'search'        => $search,
                    'link'          => 'users.index'
                )
            );
        }

        throw new \Exception('Users not found');
    }

    /**
     * @throws IncompatibleWithGoogleAuthenticatorException
     * @throws InvalidCharactersException
     * @throws GuzzleException
     * @throws SecretKeyTooShortException
     */
    public function resetTfa($user_id)
    {
        $key = (new Google2FA)->generateSecretKey();

        $api = json_decode(
            Api::post(
                '/api/v1/reset-cl-tfa?user_id=' . $user_id . '&key=' . $key
            )
        );

        if ($api->response == 'success') {
            return Redirect::route(
                'users.index'
            )->with(
                'success',
                $api->message
            );
        }

        return Redirect::route(
            'users.index'
        )->with(
            'error',
            $api->message
        );
    }


    /**
     * Edit a user
     *
     * @param $id
     *
     * @return mixed
     */
    public function edit($id)
    {
        $user          = json_decode(Api::get('/api/v1/user/find/' . $id));
        $organisation  = json_decode(Api::get('/api/v1/organisation/' . $user->data->organisation_id . '/info/' . $user->data->id));
        if ($user->response == "success") {
            return view(static::TEMPLATE_PATH . '/edit', [
                'user'         => $user->data,
                'organisation' => $organisation->data,
                'type'         => 'all'
            ]);
        }

        return Redirect::back()->with('error', 'User not found');
    }

    /**
     * Delete a user
     *
     * @param $id
     *
     * @return mixed
     */

    public function destroy($id)
    {
        $response = json_decode(Api::post('/api/v1/user/delete', array('id' => $id)));
        if ($response->response == "success") {
            return Redirect::back()->with('success', 'User deleted');
        } else {
            return Redirect::back()->with('error', 'User not found');
        }
    }


    /**
     * Create a user
     *
     * @return mixed
     */
    public function create(Request $request)
    {
        if (Session::get('user')->login_type == 'broker-user') {
            $organisations = json_decode(Api::get('api/v1/organisation/broker/' . Session::get('user')->broker_id));
            $orgs = [];
            foreach ($organisations->data as $organisation) {
                $orgs[$organisation->id] = $organisation;
            }
            $organisations = $orgs;
        } else {
            $organisations = json_decode(Api::get('/api/v1/organisation/options?client_users=1'));
        }

        if ($organisations) {
            return view(
                static::TEMPLATE_PATH . '/create',
                array(
                    'organisations' => $organisations,
                    'issecondary' => $request->get('sec') == 1 ? true : null
                )
            );
        } else {
            return Redirect::back()->with('error', $organisations->message)->withInput($request->old());
        }
    }


    public function store(Request $request)
    {
        $rules = array(
            'first_name'        =>  'required',
            'last_name'         =>  'required',
            'email'             =>  'required|email',
            'organisation_id'   =>  'required',
            'optionRadios'      =>  'required'
        );

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return Redirect::back()->withInput($request->old())->withErrors($validator->errors());
        }

        $data                            = $request->all();
        $data['branch']                  = isset($data['branch']) ? 1 : 0;
        $data['manager']                 = isset($data['manager']) ? 1 : 0;
        $data['client_dashboard_access'] = isset($data['client_dashboard_access']) && isset($data['manager']) ? 1 : 0;
        $data['safetymedia_access']      = isset($data['safetymedia_access']) ? 1 : 0;
        $data['croner_access']           = isset($data['croner_access']) ? 1 : 0;
        $data['c_live_access']           = isset($data['optionRadios']) && $data['optionRadios'] == 'c_live_access' ? 1 : 0;
        $data['triton_access']           = isset($data['optionRadios']) && $data['optionRadios'] == 'triton_access' ? 1 : 0;
        $data['astutis_access']          = isset($data['astutis_access']) ? 1 : 0;
        $data['login_type']              = Session::get('user')->login_type;
        $data['secondary_contact']       = isset($data['secondary_contact']);
        $data['has_previsico_access']    = isset($data['has_previsico_access']) ? 1 : 0;
        $data['previsico_asset_ids']    = isset($data['previsico_asset_ids']) ? $data['previsico_asset_ids'] : [];

        unset($data['optionRadios']);
        unset($data['_token']);

        $response = json_decode(Api::post('/api/v1/user/store', $data));

        if ($response->response == "success") {
            $this->recacheRelatedData($data['organisation_id'] ?? '');

            return Redirect::route('users.index')->with('success', 'User was successfully created');
        } else {
            $redirect = Redirect::back()->withInput($request->old());
            if (isset($response->errors)) {
                $redirect->withErrors($response->errors);
            }

            if (isset($response->message)) {
                $redirect->with('error', $response->message);
            }

            return $redirect;
        }
    }

    public function recacheRelatedData($orgId)
    {
        if (empty($orgId)) {
            return;
        }
        SendSqsMessageService::sendMessages([
            [
                'serviceClass' => GetOrganisationService::class,
                'params' => $orgId ?? '',
            ],
            [
                'serviceClass' => 'App\Services\CacheContent\GetOrganisationDetailsForDashboard',
                'params' => $orgId ?? '',
                'isClient' => true,
            ],
        ]);
    }
}
