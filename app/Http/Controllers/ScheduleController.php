<?php

namespace App\Http\Controllers;

use App\Models\Api;
use App\Models\Survey;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;

class ScheduleController extends BaseResourceController
{
    const
        NAV_ID = 'surveys',
        TEMPLATE_PATH = '/schedule',
        ROUTE_PREFIX = 'schedule';

    private static
        $types = [
            'holiday',
            're-admin',
        ];

    private $event_types = [
        'Claims Meeting',
        'Risk Engineering Meeting',
        'Account Meeting',
        'Risk Engineering Plan Review',
        'Other Site Visit',
        'Risk reduce demo',
        'On-boarding meeting',
        'Pre-renewal meeting (internal)',
        'Pre-renewal meeting (external)'
    ];

    public function __construct(Request $request, Survey $survey)
    {
        BaseController::__construct($request);
        $this->survey = $survey;
    }

    public function index(Request $request)
    {
        if ($request->ajax()) {
            $api = json_decode(
                Api::get(
                    static::get_api_uri(
                        implode(
                            '/',
                            [
                                $request->get('start', null),
                                $request->get('end', null)
                            ]
                        )
                    )
                )
            );


            if (count($api->schedule)) {
                $names = [
                    'liberty_branch' => json_decode(
                        Api::get(
                            static::get_api_uri('options', 'liberty-branches')
                        )
                    ),
                    'underwriter' => json_decode(
                        Api::get(
                            static::get_api_uri('options/underwriter?deleted=show', 'liberty-users')
                        )
                    ),
                    'risk_engineer' => json_decode(
                        Api::get(
                            static::get_api_uri('options/risk-engineer?deleted=show', 'liberty-users')
                        )
                    ),
                    'client_organisation' => json_decode(
                        Api::get(
                            static::get_api_uri('options', 'organisation')
                        )
                    ),
                ];



                if (!isset($survey_organisations)) {
                    $survey_organisations = json_decode(
                        Api::get(
                            static::get_api_uri('options/organisation_id', 'surveys')
                        )
                    );
                }



                foreach ($api->schedule as $key => $value) {
                    $schedule_array = $api->schedule;
                    foreach (array_keys($names) as $name) {
                        $property = sprintf('%s_id', $name);
                        if (property_exists($value, $property) && property_exists($names[$name], $value->$property)) {
                            $schedule_array[$key]->$name = $names[$name]->{$value->$property};
                            unset($schedule_array[$key]->$property);
                        }
                    }

                    if ($schedule_array[$key]->type == 'survey') {


                        // $organisation = (isset($survey_organisations) && isset($survey_organisations->{$schedule_array[$key]->id}))
                        //     ? $survey_organisations->{$schedule_array[$key]->id}
                        //     : 'Unknown Organisation';

                        // $schedule_array[$key]->organisation = isset($names['client_organisation']->$organisation) ? $names['client_organisation']->$organisation : '';

                        $schedule_array[$key]->organisation = $schedule_array[$key]->survey->organisation->name;
                        if (isset($schedule_array[$key]->survey_id)) {

                            if (isset($schedule_array[$key]->survey)) {

                                if ($schedule_array[$key]->survey->survey_type == 'rereview') {
                                    $schedule_array[$key]->type = 'rereview';
                                }

                                if (isset($schedule_array[$key]->surveyor_details) && is_object($schedule_array[$key]->surveyor_details)) {
                                    $surveyor = (isset($schedule_array[$key]->surveyor_details) && is_object($schedule_array[$key]->surveyor_details))
                                        ? implode(' ', [$schedule_array[$key]->surveyor_details->first_name, $schedule_array[$key]->surveyor_details->last_name])
                                        : '';
                                } else {
                                    $surveyor = "Not yet assigned";
                                }


                                $survey_company = (isset($schedule_array[$key]->survey->external_survey_company) && is_object($schedule_array[$key]->survey->external_survey_company))
                                    ? $schedule_array[$key]->survey->external_survey_company->name
                                    : '';

                                $schedule_array[$key]->surveyor = implode(
                                    ' at ',
                                    array_filter(
                                        [
                                            $surveyor,
                                            $survey_company
                                        ]
                                    )
                                );
                            }

                            $schedule_array[$key]->url = route(
                                'surveys.show',
                                [
                                    'id'   => $schedule_array[$key]->survey_id,
                                ],
                                0
                            );
                        }
                    } else {
                        $id = $schedule_array[$key]->id;
                        $scheduleType = $schedule_array[$key]->type;
                        $schedule_array[$key]->url = '/calendar/schedule/' . $scheduleType . '/' .$id;
                    }
                }
            }
            return Response::json($api->schedule);
        } else {
            return view(static::TEMPLATE_PATH . '.index');
        }
    }

    public function create($schedule_type)
    {
        $type = $schedule_type;


        Session::put('ApiFilesREAdmin', []);

        if (in_array($type, static::$types)) {
            return view(
                sprintf('%s/create/%s', static::TEMPLATE_PATH, $type),
                static::getRouteParams($type, 'create')
            );
        } else {
            return Response::view(
                'errors/404',
                ['body_class' => 'errors-404'],
                404
            );
        }
    }

    public function store(Request $request)
    {
        $type = last(request()->segments());

        if (in_array($type, static::$types)) {

            return parent::store($request);

        } else {
            if (method_exists(get_called_class(), 'onStoreError')) {
                static::onStoreError($data);
            }

            return Redirect::route(
                static::ROUTE_PREFIX . '.index',
                ['type' => $type]
            )->withInput(
                $request->old()
            )->withErrors(
                [
                    'message' => 'Invalid schedule type.',
                ]
            );
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param int $id
     */
    public function update(Request $request, $id)
    {
        $schduleId = last(request()->segments());
        $type = $id;

        if (in_array($type, static::$types)) {
            return parent::update($request, $schduleId);
        } else {
            if (method_exists(get_called_class(), 'onStoreError')) {
                static::onStoreError($data);
            }

            return Redirect::route(
                static::ROUTE_PREFIX . '.index',
                ['type' => $type]
            )->withInput(
                $request->old()
            )->withErrors(
                [
                    'message' => 'Invalid schedule type.',
                ]
            );
        }
    }

    public function destroy($id)
    {
        $response = json_decode(Api::post('/api/v1/schedule/delete', array('id' => $id)));
        if ($response->response == "success") {
            return Redirect::route(static::getRoutePrefix() . '.index')->with('success', $response->message);
        } else {
            return Redirect::route(static::getRoutePrefix() . '.index')->with('error', $response->message);
        }
    }

    public function edit($schedule_type = null, $id)
    {
        $type = $schedule_type;

        if (in_array($type, static::$types)) {
            $resource = json_decode(
                Api::get(
                    static::get_api_uri($id)
                )
            );

            if ($resource->response == 'success') {
                if ($resource->data->type == $type) {
                    $params = [
                        'resource' => $resource->data,
                        'files'       => $resource->files
                    ];

                    $params = array_merge(
                        $params,
                        static::getRouteParams($type, 'edit')
                    );

                    return view(
                        sprintf('%s/edit/%s', static::TEMPLATE_PATH, $type),
                        $params
                    );
                } else {
                    return Redirect::route(
                        static::ROUTE_PREFIX . '.edit',
                        [
                            'id'   => $id,
                            'type' => $type,
                        ]
                    );
                }
            } else {
                Redirect::back()->with(
                    $resource->response,
                    $resource->message
                );
            }
        }

        return Response::view(
            'errors/404',
            ['body_class' => 'errors-404'],
            404
        );
    }

    public function uploadFiles(Request $request)
    {
        if (strtolower($request->method()) == 'post') {
            $fields = $_FILES;

            foreach ($fields as $key => $val) {
                $file_field_name = $key;
            }

            $file = $request->file($file_field_name);
            $size = $request->file($file_field_name)->getSize();
            $path = $file->getRealPath();
            $type = $file->getMimeType();

            $extension = $request->file($file_field_name)->getClientOriginalExtension(); // getting image extension

            $file = $request->file($file_field_name);
            $uuid = Str::uuid()->toString();

            $fileName = strtolower(preg_replace('/[\s_]|^%20/', '-', $request->file($file_field_name)->getClientOriginalName()));

            $upload = $this->survey->upload($file, $uuid, 're-admin/' . $file_field_name);

            if ($upload['response'] == 'success') {
                $data['file_name']          = $fileName;
                $data['cloud_file_name'] = $uuid;

                // If the form has a survey id
                if ($request->get('schedule_id')) {
                    $data['schedule_id'] = $request->get('schedule_id'); // form has id
                    $insertResponse = Api::post('api/v1/schedule/add-schedule-attachments-empty', $data);
                } else {

                    // form does not have id
                    $insertResponse = Api::post('api/v1/schedule/add-schedule-attachments-empty', $data);
                    $decodedResponse = json_decode($insertResponse);
                    Session::push('ApiFilesREAdmin', $decodedResponse->{'id'});
                }

                $data['field_name'] = $file_field_name;

                return Response::json($insertResponse);
            }
        }
    }

    public function get_attachment($cloud_file_name, $file_name)
    {
        $fileName_cloud = 're-admin/file/' . $cloud_file_name;
        $file = $this->survey->download($fileName_cloud, $file_name);
        if ($file) {
            return Response::download($file, $file_name);
        }
    }

    /**
     * Parse data before storage
     *
     * @param  array $data
     * @return array parsed version of $data
     */
    public function parseDataBeforeStorage($data)
    {
        $scheduleType = (isset($data['is_update'])) ? request()->segments()[2] : last(request()->segments());
        $data['type'] = $scheduleType;

        switch ($data['type']) {
            case 'holiday':
                $data['risk_engineer_id'] = (isset($data['risk_engineer']))
                    ? (int)$data['risk_engineer']
                    : 0;

                // account for duration when saving date(s) - use time to store am/pm.
                switch ($data['duration_range']) {
                    case 'a-day-or-more':
                        $data['start'] = date('Y-m-d 00:00:00', strtotime(str_replace('/', '-', $data['start_date'])));
                        $data['end'] = date('Y-m-d 23:59:59', strtotime(str_replace('/', '-', $data['end_date'])));
                        unset(
                            $data['start_date'],
                            $data['end_date']
                        );
                        break;

                    case 'less-than-a-day':
                        $data['date'] = strtotime(str_replace('/', '-', $data['date']));

                        $data['start'] = date(
                            sprintf('Y-m-d %s:00:00', ($data['part_of_day'] == 'am' ? '00' : '12')),
                            $data['date']
                        );

                        $data['end'] = date(
                            sprintf('Y-m-d %s:59:59', ($data['part_of_day'] == 'am' ? '11' : '23')),
                            $data['date']
                        );

                        unset(
                            $data['date'],
                            $data['part_of_day']
                        );
                        break;
                }

                unset(
                    $data['risk_engineer'],
                    $data['duration_range']
                );
                break;

            case 're-admin':
                $dropdowns = [
                    'liberty_branch',
                    'underwriter',
                    'risk_engineer',
                    'client_organisation',
                ];

                foreach ($dropdowns as $option) {
                    $key = sprintf('%s_id', $option);

                    if (isset($data[$option])) {
                        $data[$key] = (int)$data[$option];
                        unset($data[$option]);
                    } else {
                        $data[$key] = 0;
                    }
                }

                $data['start'] = date('Y-m-d 00:00:00', strtotime(str_replace('/', '-', $data['requested_date'])));
                $data['end'] = date('Y-m-d 23:59:59', strtotime(str_replace('/', '-', $data['scheduled_date'])));

                unset(
                    $data['requested_date'],
                    $data['scheduled_date']
                );

                break;
        }

        return $data;
    }

    /**
     * Get additional view parameters for a specific view (index/create/edit)
     *
     * @param  string $view
     * @param  array  $params
     * @return array of additional parameters
     */
    public function getRouteParams($type, $view, array $params = [])
    {
        if (in_array($view, ['create', 'edit'])) {
            $options = [
                'risk_engineer' => ['options/risk-engineer', 'liberty-users'],
            ];

            if ($type == 're-admin') {
                $options = array_merge(
                    $options,
                    [
                        'liberty_branch'      => ['options', 'liberty-branches'],
                        'underwriter'         => ['options/underwriter', 'liberty-users'],
                        'client_organisation' => ['options', 'organisation'],
                    ]
                );
            }

            foreach ($options as $key => $values) {
                $params['options'][$key] = json_decode(
                    Api::get(
                        static::get_api_uri($values[0], $values[1])
                    )
                );
            }

            switch ($type) {
                case 'holiday':
                    $params['options'] = array_merge(
                        $params['options'],
                        [
                            'duration_range' => [
                                'a-day-or-more' => 'A day or more',
                                'less-than-a-day' => 'Less than a day',
                            ],
                        ]
                    );
                    break;

                case 're-admin':
                    $params['options'] = array_merge(
                        $params['options'],
                        [
                            'method' => static::buildOptionsFromKeys(['office', 'site', 'phone']),
                            'status' => static::buildOptionsFromKeys(['pending', 'complete']),
                            'event_types' => $this->event_types,
                        ]
                    );
                    break;
            }

            $params = array_merge(
                $params,
                array_filter(
                    [
                        'schedule_type' => $type ?: null,
                    ]
                )
            );
        }

        return $params;
    }

    /**
     * Get validation rules for a specific method (store/update/etc)
     *
     * @param  string $method
     * @return array of validation rules
     */
    public function getValidatorRules(Request $request, $method)
    {
        $scheduleType = ($method == 'store') ? last(request()->segments()) : request()->segments()[2];

        switch (implode('/', [$method, $scheduleType])) {
            case 'update/holiday':
            case 'store/holiday':
                return [
                    'start_date'  => 'date_format:d/m/Y|required_if:duration_range,a-day-or-more',
                    'end_date'    => 'date_format:d/m/Y|required_if:duration_range,a-day-or-more',
                    'date'        => 'date_format:d/m/Y|required_if:duration_range,less-than-a-day',
                    'part_of_day' => 'in:am,pm|required_if:duration_range,less-than-a-day',
                ];

            case 'update/re-admin':
            case 'store/re-admin':
                return [
                    'requested_date' => 'date_format:d/m/Y|required',
                    'scheduled_date' => 'date_format:d/m/Y|required',
                ];
        }
    }

    /**
     * Get validation messages for a specific method (store/update/etc)
     *
     * @param  string $method
     * @return array of validation rules
     */
    public function getValidatorMessages($method)
    {
        $scheduleType = ($method == 'store') ? last(request()->segments()) : request()->segments()[2];

        switch (implode('/', [$method, $scheduleType])) {
            case 'update/holiday':
            case 'store/holiday':
                return [
                    'start_date.required_if'  => 'A :attribute is required for holidays lasting a day or more.',
                    'end_date.required_if'    => 'A :attribute is required for holidays lasting a day or more.',
                    'date.required_if'        => 'A :attribute is required for holidays lasting less than a day.',
                    'part_of_day.required_if' => 'A :attribute is required for holidays lasting less than a day.',
                ];

            case 'update/re-admin':
            case 'store/re-admin':
                return [];
        }
    }
}
