<?php

namespace libertyAnywhere;

use app\Http\Controllers\BaseController;
use Illuminate\Support\Facades\View;
use App\Models\Api;
class BookingController extends BaseController
{
    /**
     * Return booking view
     *
     * @param string $date
     * @return View
     */
    public function bookings($date = null)
    {
        $response = json_decode(Api::get('api/v1/liberty-anywhere/get-bookings'));
        $data = [
            'bookings'        => !empty($response) ? $response->data->bookings : [], 
            'representatives' => !empty($response) ? $response->data->representatives: [],
        ];
        return View::make('liberty-anywhere.bookings', $data);
    }
}