<?php

namespace libertyAnywhere;

use app\Http\Controllers\BaseController;
use Illuminate\Support\Facades\View;
use App\Models\Api;
class AuthenticateUserController extends BaseController
{
    public function loginAttemptLogs()
    {
        $response = json_decode(Api::get('api/v1/virtual-rooms/get-login-attempts')); 
        $data['logs'] = $response->response;
        return View::make('liberty-anywhere.login-attempt-logs', $data);
    }
}