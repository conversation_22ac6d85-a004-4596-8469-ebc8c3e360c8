<?php

namespace libertyAnywhere;

use app\Http\Controllers\BaseController;
use App\Models\Api;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\View;
use Illuminate\Http\Request;
class ExternalContactController extends BaseController 
{
    const VALID_MOBILE_NUMBER_REGEX = '/^[+][1-9][0-9]{6,14}/';

    public function index(Request $request)
    {
        // search name query
        $name = $request->get('name');
        if(isset($name) && !empty($name) && $name == 'all'){
            $data = json_decode(Api::get('api/v1/virtual-rooms/external-contacts' . '?name=' . $name), false);
            return Response::json($data);
        }
        if(isset($name) && !empty($name)) {
            $data = json_decode(Api::get('api/v1/virtual-rooms/external-contacts' . '?name=' . $name), false);
            return Response::json($data);
        } else {
            $data = json_decode(Api::get('api/v1/virtual-rooms/external-contacts'), false)->data;
        }

        return View::make('liberty-anywhere.external-contacts', [
            'external_contacts' => $data
        ]);
    }

    public function store(Request $request)
    {
        $data = $request->all();
        $response = json_decode(Api::post('api/v1/virtual-rooms/external-contacts', $data));
        $status = ($response->response === 'error') ? 422 : 200;
        return Response::json($response, $status);
    }

    public function show($id)
    {
        $response = json_decode(Api::get('api/v1/virtual-rooms/external-contacts/' . $id));
        return Response::json($response);
    }

    public function update(Request $request, $id)
    {
        $data = $request->all();
        $response = json_decode(Api::put('api/v1/virtual-rooms/external-contacts/' . $id, $data));
        $status = ($response->response === 'error') ? 422 : 200;

        return Response::json($response, $status);
    }

    public function destroy($id)
    {
        $response = json_decode(Api::delete('api/v1/virtual-rooms/external-contacts/' . $id));
        $status = ($response->response === 'error') ? 422 : 200;

        return Response::json($response, $status);
    }
}