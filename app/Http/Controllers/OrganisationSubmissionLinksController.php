<?php

namespace App\Http\Controllers;

use App\Services\CacheContent\GetBrokerService;
use Illuminate\Http\Request;
use App\Models\FileUpload;
use App\Models\Api;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Redirect;

class OrganisationSubmissionLinksController extends BaseController
{
    public function __construct(Request $request, FileUpload $fileUpload)
    {
        parent::__construct($request);

        $this->files = $fileUpload;
    }

    public function index()
    {
        $submissionLinks = json_decode(Api::get('api/v1/do-facilities/submission-links'))->data;

        return view(
            'do-facilities/submission-links/index', [
            'submissionLinks' => $submissionLinks,
            ]
        );
    }

    public function create()
    {
        $brokers = GetBrokerService::get('')->data;
        $sectors = json_decode(Api::get('api/v1/sector/all'))->data;
        $policy_types = json_decode(Api::get('api/v1/policy-types/all'))->data;

        $data=(object)[];
        $data->brokers=$brokers;
        $data->sectors=$sectors;
        $data->policy_types=$policy_types;

        return view(
            'do-facilities/submission-links/create', [
            'data' => $data,
            ]
        );
    }

    public function store(Request $request)
    {
        $data = $request->all();

        $validation = Validator::make(
            $data, [
            'facility_name' => 'required',
            'broker_id' => 'required',
            'sector_id' => 'required',
            'policy_types_id' => 'required',
            ]
        );

        if ($validation->fails()) {
            return Redirect::back()
                ->withErrors($validation->errors())
                ->withInput($request->old());
        }

        $response = json_decode(Api::post('api/v1/do-facilities/submission-links', $data));

        return Redirect::route('do-facilities.submission-links.index')
            ->with($response->status, $response->message);
    }

    public function show($id)
    {
        $submissionLink = json_decode(Api::get('api/v1/do-facilities/submission-links/' . $id))->data;
        $submissionLink->url = route('organisation.form', $submissionLink->_id);

        return view(
            'do-facilities/submission-links/show', [
            'submissionLink' => $submissionLink,
            ]
        );
    }

    public function revoke($id)
    {
        $response = json_decode(Api::post('api/v1/do-facilities/submission-links/' . $id . '/revoke'));

        return Redirect::route('do-facilities.submission-links.show', $id)
            ->with($response->status, $response->message);
    }
}
