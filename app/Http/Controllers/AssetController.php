<?php

namespace App\Http\Controllers;

use App\Models\Api;
use App\Models\FileUpload;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Str;

class AssetController extends BaseResourceController
{

    public function __construct(Request $request, FileUpload $fileUpload)
    {
        parent::__construct($request);
        $this->files = $fileUpload;
    }

    public function show($entity = 'client_home_video')
    {
        $library = json_decode(Api::get('/api/v1/assets/attributes/' . $entity));

        if ($library->response == 'success') {

            $values = json_decode(Api::get('/api/v1/assets/values/' . $entity));
            return view(
                'asset-library.update',
                ['fields' => json_decode($library->data->attributes), 'values' => $values->data]
            );
        }

        return view('aspen.daily-claims-run')->with('error', 'Failed to fetch Daily Claims Run results');
    }

    public function store(Request $request, $entity = null)
    {
        if (strtolower($request->method()) == 'post') {
            $fields = $_FILES;
            foreach ($fields as $key => $val) {
                $file_field_name = $key;
            }

            $non_file_fields = $request->except($file_field_name);
            if (isset($non_file_fields['_token'])) {
                unset($non_file_fields['_token']);
            }
            foreach ($non_file_fields as $key => $value) {
                $f['attribute'] = $key;
                $f['value'] = $value;
                $response = json_decode(Api::post('api/v1/assets/create', $f));
            }

            $file = $request->file($file_field_name);
            $size = $request->file($file_field_name)->getSize();
            $path = $file->getRealPath();
            $type = $file->getMimeType();

            // $destinationPath = public_path('uploads'); // upload path
            $extension = $request->file($file_field_name)->getClientOriginalExtension(); // getting image extension

            $uuid = Str::uuid()->toString();

            $upload = $this->files->upload($file, 'assets_library/' . $uuid . '.' . $extension);

            if ($upload) {
                //$data['entity'] = $entity;
                $data['attribute'] = $file_field_name;
                $data['value'] = 'assets_library/' . $uuid . '.' . $extension;
                $response = json_decode(Api::post('api/v1/assets/create', $data));
                if ($response) {
                    return Redirect::to('/assets/show/client_home_video');
                }
            } else {
                return Redirect::back()->with('error', $upload['message'])->withInput($request->old());
            }


        } else {
            if (is_null($entity)) {
                $library = json_decode(Api::get('api/v1/assets/entities'));
                return view('asset-library.index');
            } else {
                $library = json_decode(Api::get('api/v1/assets/attributes/' . $entity));
                return view('asset-library.create', ['fields' => json_decode($library->data->attributes)]);
            }
        }
    }
}
