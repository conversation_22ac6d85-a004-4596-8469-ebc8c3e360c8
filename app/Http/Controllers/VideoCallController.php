<?php

namespace App\Http\Controllers;
use App\Models\Api;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\App;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;

class VideoCallController extends BaseController
{
    const MINIMUM_PARTICIPANTS = 2;
    const VALID_MOBILE_NUMBER_REGEX = '/^[+][1-9][0-9]{6,14}/';

    //INDEX
    public function index()
    {
        $login_type = Session::get('user')->login_type;
        $user_role = Session::get('user')->role;
        $user = Session::get('user');

        if ($login_type == 'underwriter'
            || $login_type == 'risk-engineer'
            || ($login_type == 'risk-control' && $user?->isRoleAdminOrAccountManager())
            || ($login_type == 'external-surveyor-admin' && $user?->isRoleAdminOrAccountManager())
        ) {
            return view('video-call.index');
        }

        App::abort(403, 'Unauthorized action.');
    }

    /**
     * @throws GuzzleException
     */
    public function store(Request $request)
    {
        $data = $request->except('_token');

        $data = $this->validateParticipants($request, $data);
        if (is_array($data)) {
            $data['created_by_id'] = Session::get('user')->id;
            $data['created_by_login_type'] = Session::get('user')->login_type;
            $data['created_by_role'] = Session::get('user')->role;
            $response = json_decode(Api::post(route('video-call.post', [], false), $data), true);

            // if everything is okay
            if ($response['response'] === 'success') {
                return Redirect::back()->with(
                    'success',
                    'Room has been successfully created, and participants were sent an invitation'
                );
            }

            // if there are problems with the provided numbers
            if (!empty($response['data'])) {
                return Redirect::back()->with(
                    'error',
                    'The following entries have an invalid number format. Please check and try again. <ul><li>'
                    .implode('</li><li>', $response['data']).'</li></ul>'
                )->withInput($request->old());
            }

            // for any general issues
            return Redirect::back()->with('error', 'There was an error with creating the room or participants.');
        }

        return $data;
    }

    /**
     * Validates the video setup form
     *
     * @param  $data
     * @return |null
     */
    private function validateParticipants(Request $request, $data)
    {
        $empty = true;
        foreach ($data as $datum) {
            if (!empty($datum)) {
                $empty = false;
                break;
            }
        }

        if ($empty) {
            return Redirect::back()->with('error', 'At least two participants are required to setup a video call.');
        }

        $rules = [
            'client' => 'required_with:client_phone',
            'client_phone' => 'required_with:client|regex:' . self::VALID_MOBILE_NUMBER_REGEX,
            'broker' => 'required_with:broker_phone',
            'broker_phone' => 'required_with:broker|regex:' . self::VALID_MOBILE_NUMBER_REGEX,
            'liberty_staff' => 'required_with:liberty_staff_phone',
            'liberty_staff_phone' => 'required_with:liberty_staff|regex:' . self::VALID_MOBILE_NUMBER_REGEX,
        ];
        $messages = [
            'client.required_with' => 'The Client Name field is required when Client Mobile Number is present.',
            'client_phone.required_with' => 'The Client Mobile Number field is required when Client Name is present.',
            'broker.required_with' => 'The Broker Name field is required when Broker Mobile Number is present.',
            'broker_phone.required_with' => 'The Broker Mobile Number field is required when Broker Name is present.',
            'liberty_staff.required_with' => 'The Liberty Staff Name field is required when Liberty Staff Mobile Number is present.',
            'liberty_staff_phone.required_with' => 'The Liberty Staff Mobile Number field is required when Liberty Staff Name is present.',
            'client_phone.regex' => 'The Client Mobile Number phone format is invalid',
            'broker_phone.regex' => 'The Client Mobile Number phone format is invalid',
            'liberty_staff_phone.regex' => 'The Client Mobile Number phone format is invalid',
        ];

        foreach ($data as $key => $datum) {
            if (str_contains($key, '_phone')) {
                $data[$key] = $this->cleanupNumber($datum);
            } else {
                $data[$key] = trim($datum);
            }
        }

        $validator = Validator::make($data, $rules, $messages);
        if ($validator->fails()) {
            return Redirect::back()->withErrors($validator)->withInput($request->old());
        }

        $phones = array_filter([$data['client_phone'], $data['broker_phone'], $data['liberty_staff_phone']]);
        if (count($phones) < self::MINIMUM_PARTICIPANTS) {
            return Redirect::back()->with(
                'error',
                'At least two participants are required to setup a video call.'
            )->withInput($request->old());
        }

        return $data;
    }

    private function cleanupNumber($number)
    {
        // remove all non-printable strings
        $number = preg_replace('/[^[:print:]]/', '', $number);
        $number = preg_replace('/\s/', '', $number);

        return $number;
    }
}
