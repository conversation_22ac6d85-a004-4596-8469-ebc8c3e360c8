<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Controllers\BaseController;
use App\Models\Api;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Response;

class RiskRecTitleController extends BaseController
{
    public function __construct(Request $request, Api $api)
    {
        parent::__construct($request);

        $this->api = $api;
    }

    public function show()
    {
        if(Cache::has('rr_titles') && isset($_GET['return']) && $_GET['return'] == 'json') {
            return Cache::get('rr_titles');
        }
        $response = json_decode(Api::get('/api/v1/risk-improvement/risk-rec/titles'));
        Cache::forget('rr_titles');
        Cache::forever('rr_titles', json_encode($response));
        if(isset($response->response) && $response->response == 'success') {
            if(isset($_GET['return']) && $_GET['return'] == 'json') {

                return json_encode($response);
            }
            return view(
                'risk_rec_title/show',
                array(
                'titles' => $response->data
                )
            );
        }
        //return view('dashboard');
    }

    public function mobile_show()
    {
        $logged_in = Api::check_key("external-surveyors");

        if(!$logged_in || json_decode($logged_in)->response != 'success') {
            return Response::json(
                [
                'response' => 'error',
                'message'  => 'Unable to login member'
                ],
                200
            );
        }
        $response = json_decode(Api::get('/api/v1/risk-improvement/risk-rec/titles'));

        if(isset($response->response) && $response->response == 'success') {

            return json_encode($response);


        }
        //return view('dashboard');
    }

    public function store(Request $request)
    {
        if (strtolower($request->method()) == 'post') {
            $rules = array(
                'title'          => 'required',
                'description'          => 'required',
                'action'   =>  'required'
            );

            $validator = Validator::make($request->all(), $rules);

            if($validator->fails()) {
                return Redirect::back()->withErrors($validator->errors())->withInput($request->old());
            }
            $data = $request->except('_token');

            $response = json_decode($this->api->post('/api/v1/risk-improvement/risk-rec/titles/store', $data));

            if($response->response == "success") {

                return Redirect::route('riskrec.titles')->with('success', 'Title added successfully');

            }

            return Redirect::back()->with('error', $response->message)->withInput($request->old());
        } else {
            $response = json_decode(Api::get('/api/v1/risk-improvement/risk-rec/titles'));

            if(isset($response->response) && $response->response == 'success') {
                return view(
                    'risk_rec_title/store',
                    array(
                    'titles' => $response->data
                    )
                );
            }
        }
    }

    public function update(Request $request, $id)
    {
        if (strtolower($request->method()) == 'post') {
            $rules = array(
                'title'          => 'required',
                'description'          => 'required',
                'action'   =>  'required'
            );

            $validator = Validator::make($request->all(), $rules);

            if($validator->fails()) {
                return Redirect::back()->withErrors($validator->errors())->withInput($request->old());
            }
            $data = $request->except('_token');

            $response = json_decode($this->api->post('/api/v1/risk-improvement/risk-rec/titles/update/'.$id, $data));

            if($response->response == "success") {

                return Redirect::route('riskrec.titles')->with('success', 'Title updated successfully');

            }

            return Redirect::back()->with('error', $response->message)->withInput($request->old());
        } else {
            $response = json_decode(Api::get('/api/v1/risk-improvement/risk-rec/titles/'.$id));

            if(isset($response->response) && $response->response == 'success') {
                return view(
                    'risk_rec_title/update',
                    array(
                    'resource' => $response->data
                    )
                );
            }
        }
    }

    public function delete($id)
    {
        $response = json_decode($this->api->get('/api/v1/risk-improvement/risk-rec/titles/delete/'.$id));

        if(isset($response->response) && $response->response == 'success') {
            return Redirect::route('riskrec.titles')->with('success', 'Title deleted successfully');
        }

        return Redirect::route('riskrec.titles')->with('error', 'Could not delete title');

    }

}
