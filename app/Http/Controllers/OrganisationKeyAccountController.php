<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Api;

class OrganisationKeyAccountController extends BaseController
{
    public function exportKeyAccount()
    {
        $filename = 'key-accounts-' . date('d-m-Y') . '.xlsx';
        $streamFileData = Api::get('api/v1/organisation/keyaccounts/export?keyaccount=1');
        $headers = [
            'Content-Type'        => 'application/octet-stream',
            'Content-Disposition' => 'attachment; filename=' . $filename,
        ];
        return response($streamFileData, 200, $headers);
    }
}
