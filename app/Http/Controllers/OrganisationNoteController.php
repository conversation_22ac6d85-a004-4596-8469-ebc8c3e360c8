<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;
use App\Models\Documents;
use App\Models\Api;
use App\Models\FileUpload;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
class OrganisationNoteController extends BaseController
{
    const MENTION_TAG = 'b';
    private $liberty_users;

    public function __construct(Request $request, Documents $doc, FileUpload $fileUpload)
    {
        parent::__construct($request);
        $this->documents = $doc;
        $this->files = $fileUpload;
    }

    /**
     * @var array
     */
    private $types = [
        'Claims',
        'Risk Engineering',
        'Claims MI Report',
        'Underwriter',
        'Other',
    ];

    public function create($organisation_id)
    {
        $organisation = json_decode(Api::get('api/v1/organisation/' . $organisation_id));
        $this->setLibertyUsers();
        $task_data = Session::get('task_data');
        Session::forget('task_data');

        return view(
            'organisation.notes.create', [
            'organisation' => $organisation->data,
            'users' => $this->liberty_users,
            'types' => $this->types,
            'tag' => self::MENTION_TAG,
            'task_data' => $task_data
            ]
        );
    }

    public function store(Request $request, $organisation_id)
    {
        $data = $request->all();

        $this->setLibertyUsers();
        $user = Session::get('user');

        $data['author'] = $user->first_name . ' ' . $user->last_name;
        $data['type'] = $this->types[$data['type']];
        if ($request->hasFile('attachment')) {
            $file = $request->file('attachment');
            $uuid = Str::uuid()->toString();
            $fileName = strtolower(preg_replace('/[\s_]|^%20/', '-', $request->file('attachment')->getClientOriginalName()));

            $upload = $this->documents->upload($file, $uuid, $organisation_id);

            if ($upload['response'] == 'success') {
                $data['attachment_title'] = $fileName;
                $data['attachment_store_name'] = $uuid;
            } else {
                return Redirect::back()->with('error', $upload['message'])->withInput($request->old());
            }
        }
        $validator = Validator::make(
            $data, [
            'type' => 'required',
            'subject' => 'required',
            'content' => 'required',
            ]
        );
        if ($validator->fails()) {
            return Redirect::back()
                ->withErrors($validator->errors())
                ->withInput($request->old());
        }

        $note = json_decode(Api::post('api/v1/organisation/' . $organisation_id . '/notes', $data));

        if (isset($data['add_note']) && !empty($data['add_note'])) {
            return Redirect::route('schedule.index');
        } 

        return Redirect::route('organisation.show', $organisation_id);
    }

    public function show($organisation_id, $note_id)
    {
        $organisation = json_decode(Api::get('api/v1/organisation/' . $organisation_id));
        $note = json_decode(Api::get('api/v1/organisation/' . $organisation_id . '/notes/' . $note_id));

        if ($note->data) {
            return view(
                'organisation.notes.show', [
                'organisation' => $organisation->data,
                'note' => $note->data,
                ]
            );
        }

        return view('errors.404');
    }

    public function edit($organisation_id, $note_id)
    {
        $organisation = json_decode(Api::get('api/v1/organisation/' . $organisation_id));
        $note = json_decode(Api::get('api/v1/organisation/' . $organisation_id . '/notes/' . $note_id));
        $this->setLibertyUsers();

        return view(
            'organisation.notes.edit', [
            'organisation' => $organisation->data,
            'types' => $this->types,
            'users' => $this->liberty_users,
            'note' => $note->data,
            'tag' => self::MENTION_TAG
            ]
        );
    }

    public function update(Request $request, $organisation_id, $note_id)
    {
        $data = $request->all();
        $note = json_decode(Api::get('api/v1/organisation/' . $organisation_id . '/notes/' . $note_id));

        if (isset($note->data->attachment_store_name) && $request->hasFile('attachment')) {
            $fileName = $organisation_id . '/' . $note->data->attachment_store_name;
            $file = $request->file('attachment');
            $delete = $this->documents->update($fileName, $file);
        }

        if ($request->hasFile('attachment')) {
            $file = $request->file('attachment');
            $uuid = Str::uuid()->toString();
            $fileName = strtolower(preg_replace('/[\s_]|^%20/', '-', $request->file('attachment')->getClientOriginalName()));

            $upload = $this->documents->upload($file, $uuid, $organisation_id);

            if ($upload['response'] == 'success') {
                $data['attachment_title'] = $fileName;
                $data['attachment_store_name'] = $uuid;
            } else {
                return Redirect::back()->with('error', $upload['message'])->withInput($request->old());
            }
        }

        $user = Session::get('user');
        $data['author'] = $user->first_name . ' ' . $user->last_name;
        $data['type'] = $this->types[$data['type']];
        $note = json_decode(Api::post('api/v1/organisation/' . $organisation_id . '/notes/' . $note_id . '/update', $data));

        return Redirect::route('organisation.notes.show', [$organisation_id, $note_id]);
    }

    public function destroy($organisation_id, $note_id)
    {
        $organisation = json_decode(Api::get('api/v1/organisation/' . $organisation_id));
        $note = json_decode(Api::post('api/v1/organisation/' . $organisation_id . '/notes/' . $note_id . '/destroy'));
        return Redirect::route('organisation.show', $organisation_id);
    }

    private function setLibertyUsers()
    {
        $liberty_users_admin = json_decode(Api::get('api/v1/liberty-users/all/1/50/admin'));
        $liberty_users_rw = json_decode(Api::get('api/v1/liberty-users/all/1/50/risk-engineer'));
        $liberty_users_uw = json_decode(Api::get('api/v1/liberty-users/all/1/50/underwriter'));
        $liberty_users_admin_filtered = !empty($liberty_users_admin) ? $this->filterUserColumns($liberty_users_admin) : [];
        $liberty_users_rw_filtered = !empty($liberty_users_rw) ? $this->filterUserColumns($liberty_users_rw) : [];
        $liberty_users_uw_filtered = !empty($liberty_users_uw) ? $this->filterUserColumns($liberty_users_uw) : [];

        $liberty_users = array_merge($liberty_users_admin_filtered, $liberty_users_rw_filtered, $liberty_users_uw_filtered);
        if ($liberty_users) {
            $this->liberty_users = $liberty_users;
        }
    }

    private function filterUserColumns($users)
    {
        $formatted_users = [];
        foreach ($users->data as $user) {
            $formatted_users[] = $this->userColumns($user);
        }
        return $formatted_users;
    }

    private function userColumns($user)
    {
        return [
            'id' => $user->id,
            'value' => sprintf(
                '%s %s &lt;%s&gt;',
                $user->first_name,
                $user->last_name,
                $user->email
            )
        ];
    }
}
