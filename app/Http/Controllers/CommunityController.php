<?php

namespace App\Http\Controllers;

use App\Models\Api;
use App\Models\CommunityMessageTypes;
use App\Models\FileUpload;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Exception;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Redirect;
class CommunityController extends BaseController
{
    public function __construct(FileUpload $fileUpload)
    {
        $this->files = $fileUpload;
    }

    public function index()
    {
        $jsonSectors = json_decode(Api::get(route('api.community.getSector', [], false)));
        $sectors = $jsonSectors
            ? json_decode($jsonSectors->data)->data
            : [];


        $arrSectors = [];
        foreach ($sectors as $sector) {
            $arrSectors[$sector->_id] = $sector->name;
        }
        uasort(
            $arrSectors, function ($lhs, $rhs) {
            return strcmp($lhs, $rhs);
        }
        );

        $jsonTags = Api::get('api/v1/community/get-popular-community-tag');
        $tags = $jsonTags
            ? (array)json_decode($jsonTags)->data
            : [];

        // group equal popularity tags
        foreach ($tags as $keyTags => $valueTags) {
            $setupTags[$valueTags][] = $keyTags;
        }

        // sort per group
        if (isset($setupTags) && !empty($setupTags)) {
            foreach ($setupTags as $key => $value) {
                sort($value);
                foreach ($value as $finalTag) {
                    $finalTags[] = $finalTag;
                }
            }
        } else {
            $finalTags[] = "";
        }

        // get current subscriptions
        $user = Session::get('user')->id;
        $type = User::getUserType();

        $subscriptions = json_decode(
            Api::get(
                route(
                    'api.community.sector.subscriptions',
                    ['id' => $user, 'type' => $type], false
                )
            )
        );

        $userSubscriptions = [];
        if ($subscriptions) {
            foreach ($subscriptions->data as $subscription) {
                if (array_key_exists($subscription, $arrSectors)) {
                    $userSubscriptions[$subscription] = $arrSectors[$subscription];
                }
            }
        }
        uasort(
            $userSubscriptions, function ($lhs, $rhs) {
            return strcmp($lhs, $rhs);
        }
        );

        $deletedSectors = [];
        if (!in_array($type, ['client', 'broker'])) {
            $deletedSectors = json_decode(Api::get(route('api.community.sector.deleted', null, false)));
            $deleted = $deletedSectors
                ? $deletedSectors->data
                : [];
            $deletedSectors = [];
            foreach ($deleted as $val) {
                $deletedSectors[$val] = '';
            }
        }


        $upcomingEvents = [];

        if (!Session::has('shownUpcomingEvents')) {
            Session::put('shownUpcomingEvents', true);
            $upcomingEvents = json_decode(
                Api::get(
                    route(
                        'api.community.messages.upcoming',
                        ['id' => $user, 'type' => $type], false
                    )
                )
            )->data;
        }

        return view(
            'community.index', [
                'messageTypes' => [
                    CommunityMessageTypes::TYPE_DISCUSSION => 'Discussions',
                    CommunityMessageTypes::TYPE_WEBINAR => 'Webinars',
                    CommunityMessageTypes::TYPE_PODCAST => 'Podcasts',
                    CommunityMessageTypes::TYPE_EVENT => 'In-Person Events',
                ],
                'tags' => $finalTags,
                'sectors' => $arrSectors,
                'subscriptions' => $userSubscriptions,
                'deletedSectors' => $deletedSectors,
                'upcomingEvents' => $upcomingEvents,
                'userType' => $type,
            ]
        );
    }

    public function start()
    {
        $jsonSectors = json_decode(Api::get(route('api.community.getSector', [], false)))->data;
        $sectors = $jsonSectors
            ? json_decode($jsonSectors)->data
            : [];
        $userSubscribedSectors = [];
        uasort(
            $sectors, function ($lhs, $rhs) {
            return strcmp($lhs->name, $rhs->name);
        }
        );

        // get current subscriptions
        $user = Session::get('user');
        $subscriptions = json_decode(
            Api::get(
                route(
                    'api.community.sector.subscriptions',
                    ['id' => $user->id, 'type' => User::getUserType()], false
                )
            )
        );

        if ($subscriptions) {
            $subscriptions = $subscriptions->data;
            foreach ($sectors as $sector) {
                if (in_array($sector->_id, $subscriptions)) {
                    array_push($userSubscribedSectors, $sector);
                }
            }
        }

        $jsonTags = json_decode(Api::get('api/v1/community/get-community-tag'))->data;

        $tags = [];
        if ($jsonTags) {
            $dataTags = json_decode($jsonTags)->data;
            foreach ($dataTags as $jsonTag) {
                $tmpTag['value'] = $jsonTag->_id;
                $tmpTag['label'] = $jsonTag->name;

                $tags[] = $tmpTag;
            }
        }


        $loginType = $user->login_type;
        $userRole = $user->role;


        return view(
            'community.thread.discussion.start', [
                'sectors' => $userSubscribedSectors,
                'tags' => $tags,
                'uuid' => Str::uuid()->toString(),
                'loginType' => $loginType,
                'userRole' => $userRole,
                'userType' => $user->type,
            ]
        );
    }

    public function store(Request $request)
    {
        $data = $request->all();

        $userType = User::getUserType();
        $data['user_id'] = Session::get('user')->id;
        $data['user_type'] = $userType;

        if ($userType === 'broker') {
            $data['type'] = 'discussion';
        }
        if (!empty($data['date'])) {
            $data['date'] = Carbon::createFromFormat('d/m/Y', $data['date'])->format('Y-m-d');
        }

        $response = Api::post(route('api.community.message.store', null, false), $data);

        $obj = json_decode($response);

        if ($obj->response == 'success') {
            Session::put('message_id', $obj->id);
        }

        return $response;
    }

    public function storeReply(Request $request)
    {
        $data = $request->all();
        $data['user_id'] = Session::get('user')->id;
        $data['user_type'] = User::getUserType();

        $response = Api::post(route('api.community.message.reply.store', null, false), $data);

        $obj = json_decode($response);

        return $response;
    }

    public function show($id)
    {
        $jsonTags = json_decode(Api::get('api/v1/community/get-community-tag'))->data;

        $response = json_decode(Api::get(route('api.community.message.get', ['id' => $id], false)));

        $data = null;
        if ($response && $response->response == "success") {
            $response->data->tags = array_filter(
                $response->data->tags, function ($tag) {
                static $ids = [];
                if (in_array($tag->tag_id, $ids)) {
                    return false;
                }
                $ids [] = $tag->tag_id;
                return true;
            }
            );
            $data = $response->data;

            switch ($data->user_type->type) {
                case 'admin':
                case 'underwriter':
                case 'risk-engineer':
                    $user = $data->liberty_user;
                    break;

                case 'broker':
                    $user = $data->broker_user;
                    break;

                default:
                    $user = $data->user;
                    break;
            }

            $commentsSection['replies'] = $data->all_replies;
            $commentsSection['comments_count'] = $data->comments_count;
            $commentsSection['likes_count'] = $data->likes_count;

            if (!empty($data->poll)) {
                $data->title = $data->poll->body;
            }
            if (!empty($data->event_date)) {
                $data->event_date = date("Y-m-d H:i:s", strtotime($data->event_date));
            }
            $header = '';
            $files = $data->files;
            if (!empty($files)) {
                for ($i = 0; $i < count($files); $i++) {
                    if ($files[$i]->type == 'header') {
                        $header = $files[$i]->url;
                    }
                }
            }
            $type = $data->type;

            $avatar = ''; // default for admin
            if ($data->user_type->type == 'client' && !empty($user->organisation->logo)) {
                $avatar = $user->organisation->logo ?? "";
            } else {
                if ($data->user_type->type == 'broker' && !empty($user->broker->image)) {
                    $avatar = $user->broker->image;
                }
            }

            $orgName = 'Liberty Specialty Markets'; // default for admin
            if ($data->user_type->type == 'client') {
                $orgName = $user->organisation->name;
            } else {
                if ($data->user_type->type == 'broker' && !empty($user->broker->image)) {
                    $orgName = $user->broker->name;
                }
            }

            $data = [
                'thread' => $data,
                'author' => $user,
                'poll' => $data->poll,
                'tags' => $data->tags,
                'commentsSection' => $commentsSection,
                'avatar' => $avatar,
                'organisation' => $orgName,
                'header' => $header,
            ];

            switch ($type) {
                case CommunityMessageTypes::TYPE_DISCUSSION:
                    return view('community.thread.discussion.view', $data);
                case CommunityMessageTypes::TYPE_EVENT:
                    return view('community.thread.in-person-event.view', $data);
                case CommunityMessageTypes::TYPE_PODCAST:
                    $data['header'] = $header;
                    return view('community.thread.podcast.view', $data);
                case CommunityMessageTypes::TYPE_WEBINAR:
                    return view('community.thread.webinar.view', $data);
                default:
                    throw new \Exception('Unknown thread type.');
            }
        }

        return Redirect::away('https://www.libertyriskreduce.com/404');
    }

    public function showReply($id)
    {
        $response = json_decode(Api::get(route('api.community.message.reply.get', ['id' => $id], false)));

        if ($response->response == "success") {
            switch ($response->data->user_type->type) {
                case 'admin':
                case 'underwriter':
                case 'risk-engineer':
                    $response->data->user = $response->data->liberty_user;
                    break;

                case 'broker':
                    $response->data->user = $response->data->broker_user;
                    break;

                default:
                    break;
            }

            if (empty($response->data->all_replies)) {
                $response->data->all_replies = [];
            }
            $commentsSection['replies'] = $response->data->all_replies;
            $commentsSection['likes_count'] = $response->data->likes_count;

            $view = $response->data->message_reply_id == 0
                ? 'community.thread.common.comment'
                : 'community.thread.common.reply';

            $organisation = in_array(
                $response->data->user_type->type,
                ['client']
            )
                ? $response->data->user->organisation
                : [];

            return view(
                $view, [
                    'organisation' => $organisation,
                    'reply' => $response->data,
                ]
            );
        }

        return Redirect::away('https://www.libertyriskreduce.com/404');
    }

    public function updateReply(Request $request, $id)
    {
        $data = $request->except(['_token']);


        $validator = \Illuminate\Support\Facades\Validator::make(
            $data, [
                'message' => 'required',
            ]
        );

        if ($validator->fails()) {
            return Redirect::back()->withInput($request->old())->withErrors($validator->errors());
        }

        if (!empty($request->get('deletefiles'))) {
            $files = explode(',', trim($request->get('deletefiles'), ', '));
            foreach ($files as $val) {
                $this->deleteFile($val);
            }
        }

        $response = Api::put(route('api.community.message.reply.update', ['id' => $id], false), $data);

        return $response;
    }

    public function delete($id)
    {
        $response = json_decode(Api::get(route('api.community.message.get', ['id' => $id], false)));

        if ($response->response == "success") {
            $data = $response->data;

            switch ($data->user_type->type) {
                case 'admin':
                case 'underwriter':
                case 'risk-engineer':
                    $user = $data->liberty_user;
                    break;

                case 'broker':
                    $user = $data->broker_user;
                    break;

                default:
                    $user = $data->user;
                    break;
            }

            if ($user->id != Session::get('user')->id  && in_array(User::getUserType(), ['client', 'broker']) ) {
                return Redirect::back()->withErrors(["error" => "You're not allowed to delete this!"]);
            } else {
                return Api::delete(route('api.community.message.delete', ['id' => $id], false));
            }
        } else {
            return Redirect::back()->withErrors(["error" => "We couldn't find this ID!"]);
        }
    }

    public function edit($id)
    {
        Session::put('message_id', $id);

        $jsonSectors = json_decode(Api::get('api/v1/community/get-sector'))->data;
        $sectors = $jsonSectors
            ? json_decode($jsonSectors)->data
            : [];
        $userSubscribedSectors = [];
        uasort(
            $sectors, function ($lhs, $rhs) {
            return strcmp($lhs->name, $rhs->name);
        }
        );

        // get current subscriptions
        $user = Session::get('user');
        $type = User::getUserType();

        $subscriptions = json_decode(
            Api::get(
                route(
                    'api.community.sector.subscriptions',
                    ['id' => $user->id, 'type' => $type], false
                )
            )
        );

        if ($subscriptions) {
            $subscriptions = $subscriptions->data;
            foreach ($sectors as $sector) {
                if (in_array($sector->_id, $subscriptions)) {
                    array_push($userSubscribedSectors, $sector);
                }
            }
        }

        $jsonTags = json_decode(Api::get('api/v1/community/get-community-tag'))->data;

        $tags = [];
        if ($jsonTags) {
            $dataTags = json_decode($jsonTags)->data;
            foreach ($dataTags as $jsonTag) {
                $tmpTag['value'] = $jsonTag->_id;
                $tmpTag['label'] = $jsonTag->name;

                $tags[] = $tmpTag;
            }
        }


        $response = json_decode(Api::get(route('api.community.message.get', ['id' => $id], false)));


        if ($response->response == "success") {
            $response->data->user = empty($response->data->user)
                ? $response->data->liberty_user
                : $response->data->user;

            if ($response->data->user->id != Session::get('user')->id  && in_array(User::getUserType(), ['client', 'broker']) ) {
                return Redirect::to(
                    route(
                        'community.discussion.show',
                        ['id' => $id]
                    )
                )->withErrors(["error" => "You're not allowed to edit this!"]);
            }


            $files = $response->data->files;

            $has_header = false;
            $has_audio = false;

            if (!empty($files)) {
                for ($i = 0; $i < count($files); $i++) {
                    if ($files[$i]->type == 'header') {
                        $has_header = true;
                    }
                    if ($files[$i]->type == 'podcast') {
                        $has_audio = true;
                    }
                }
            }

            if (!empty($response->data->poll)) {
                $response->data->title = $response->data->poll->body;


                if ($response->data->poll->votes_count > 0) {
                    return view('community.thread.discussion.poll-edit-disabled', ['id' => $id]);
                }
            }

            return view(
                'community.thread.discussion.edit', [
                    'message' => $response->data,
                    'sectors' => $userSubscribedSectors,
                    'tags' => $tags,
                    'has_header' => $has_header,
                    'has_audio' => $has_audio,
                    'userType' => $user->type,
                ]
            );
        }


        return Redirect::away('https://www.libertyriskreduce.com/404');
    }


    public function update(Request $request, $id)
    {
        $response = json_decode(Api::get(route('api.community.message.get', ['id' => $id], false)));

        if ($response->response == "success") {
            $response->data->user = empty($response->data->user)
                ? $response->data->liberty_user
                : $response->data->user;

            if ($response->data->user->id != Session::get('user')->id  && in_array(User::getUserType(), ['client', 'broker']) ) {
                return Redirect::back()->withErrors(["error" => "You're not allowed to edit this!"]);
            }


            $data = $request->except(['_token']);

            $validator = \Illuminate\Support\Facades\Validator::make(
                $data, [
                    'title' => 'required',
                ]
            );

            if ($validator->fails()) {
                return Redirect::back()->withInput($request->old())->withErrors($validator->errors());
            }
            if (!empty($request->get('delete_header'))) {
                $this->deleteFile($request->get('delete_header'));
            }

            if (!empty($request->get('delete_audio'))) {
                $audio = explode(',', trim($request->get('delete_audio'), ', '));
                foreach ($audio as $val) {
                    $this->deleteFile($val);
                }
            }

            if (!empty($request->get('delete_files'))) {
                $audio = explode(',', trim($request->get('delete_files'), ', '));
                foreach ($audio as $val) {
                    $this->deleteFile($val);
                }
            }

            if (!empty($data['date'])) {
                $data['date'] = Carbon::createFromFormat('d/m/Y', $data['date'])->format('Y-m-d');
            }

            return Api::put(route('api.community.message.update', ['id' => $id], false), $data);
        } else {
            return Redirect::back()->withErrors(["error" => "We couldn't find this ID!"]);
        }
    }

    public function like(Request $request, $id, $reply_id)
    {
        $data = $request->all();
        $data['user_id'] = Session::get('user')->id;
        $data['user_type'] = User::getUserType();
        return Api::put(route('api.community.message.like', ['id' => $id, 'reply' => $reply_id], false), $data);
    }


    public function interested(Request $request, $id)
    {
        $data = $request->all();
        $data['user_id'] = Session::get('user')->id;
        return Api::put(route('api.community.webinar.interested', ['id' => $id], false), $data);
    }


    public function vote(Request $request)
    {
        $data = $request->all();
        $data['type'] = User::getUserType();
        $data['user_id'] = Session::get('user')->id;

        $validator = \Illuminate\Support\Facades\Validator::make(
            $data, [
                'answer_id' => 'required',
            ]
        );
        if ($validator->fails()) {
            return Redirect::back()->withInput($request->old())->withErrors($validator->errors());
        }

        $response = json_decode(Api::post(route('api.community.message.vote', null, false), $data));

        if ($response->response == "success") {
            return Redirect::to(route('community.discussion.show', $request->get('thread_id')))->with(
                'message',
                'Thank you for voting'
            );
        } else {
            return Redirect::back()->withErrors('Failed to post vote');
        }
    }


    public function uploadFile(Request $request)
    {
        $data = $request->all();

        $file = $data['file'];

        $cloud_file = Str::uuid()->toString() . '/' . $data['name'];

        $upload = $this->files->upload($file->getPathName(),
            'community/' . $data['root_parent_id'] . '/' . $cloud_file);

        if ($upload) {
            $api_data = [
                'root_parent_id' => $data['root_parent_id'],
                'parent_id' => $data['parent_id'],
                'cloud_file' => $cloud_file,
                'original_file' => $data['name'],
                'type' => $data['type'],
            ];


            return Api::post(route('api.community.message.file.upload', null, false), $api_data);
        }

        return [
            'response' => 'error',
            'message' => 'The Liberty Community File has failed to upload',
        ];
    }

    public function deleteFile($id)
    {
        return Api::delete(route('api.community.message.file.delete', ['id' => $id], false));
    }

    protected function linkFile($id)
    {
        $response = json_decode(Api::get(route('api.community.message.file.link', ['id' => $id], false)));

        if ($response->response == "success") {
            $url = $response->url;
        } else {
            $url = '#';
        }

        return $url;
    }

    public function getDiscussionWithPoll()
    {
        return view(
            'community.thread.discussion.poll',
            [
                'title' => 'How is your organisation ensuring accurate reporting of near misses?',
                'author_company' => 'Willis Towers Watson',
                'author_name' => 'John Smith',
                'date' => '24th Mar 2021',
                'message' => 'We’ve working with organisation’s who are struggling to introduce near misses into their incident/accident reporting framework. Any ideas on how to implement near miss reporting effectively?',
                'filename' => 'Safety Guidance for Utilities Companies.pdf',
                'poll_results' => [
                    [
                        'title' => 'Title1',
                        'value' => '20%',
                    ],
                    [
                        'title' => 'Title2',
                        'value' => '20%',
                    ],
                    [
                        'title' => 'Title3',
                        'value' => '20%',
                    ],
                    [
                        'title' => 'Title4',
                        'value' => '20%',
                    ],
                    [
                        'title' => 'Title5',
                        'value' => '20%',
                    ],
                ],
                'comments_count' => 14,
                'likes_count' => 6,
                'replies' => [
                    [
                        'author_name' => 'John Smith',
                        'author_company' => 'Liberty Specialty Markets',
                        'message' => 'Hi Jane, we’ve developed an SOP for incident/accident reporting that includes clear definitions on what is/isn’t to be reported; I’ve attached it here. We’ve also recently updated our training on this, for which we provide periodic refresher sessions.',
                        'date' => '24th Mar 2021',
                        'likes_count' => 6,
                        'filename' => 'Safety Guidance for Utilities Companies.pdf',
                        'replies' => [],
                        'is_replying' => true,
                    ],
                    [
                        'author_name' => 'John Smith',
                        'author_company' => 'Liberty Specialty Markets',
                        'message' => 'Hi Jane, we’ve developed an SOP for incident/accident reporting that includes clear definitions on what is/isn’t to be reported; I’ve attached it here. We’ve also recently updated our training on this, for which we provide periodic refresher sessions.',
                        'date' => '24th Mar 2021',
                        'likes_count' => 6,
                        'filename' => 'Safety Guidance for Utilities Companies.pdf',
                        'replies' => [
                            [
                                'author_name' => 'John Smith',
                                'author_company' => 'Liberty Specialty Markets',
                                'message' => 'Hi Jane, we’ve developed an SOP for incident/accident reporting that includes clear definitions on what is/isn’t to be reported; I’ve attached it here. We’ve also recently updated our training on this, for which we provide periodic refresher sessions.',
                                'date' => '24th Mar 2021',
                                'likes_count' => 6,
                                'filename' => 'Safety Guidance for Utilities Companies.pdf',
                                'replies' => [],
                                'is_replying' => true,
                            ],
                        ],
                        'is_replying' => false,
                    ],
                ],
            ]
        );
    }

    public function messageSearch(Request $request)
    {
        $subscriptions = $request->get('subscriptions');
        $search = $request->get('search');
        $user_role = User::getUserType();
        $page = $request->get('page');
        $tag = $request->get('tag');
        $discussionType = $request->get('discussion_type');

        $response = json_decode(
            Api::post(
                'api/v1/community/messages/all',
                [
                    'subscriptions' => json_encode($subscriptions),
                    'search' => $search,
                    'user_role' => $user_role,
                    'page' => $page,
                    'tag' => $tag,
                    'discussion_type' => $discussionType,
                ]
            ), true
        );
        return $response;
    }

    public function compileICSFile($message_id)
    {
        $response = json_decode(Api::get('/api/v1/community/messages/' . $message_id))->data;
        $summary = $response->title;
        $eventDate = Carbon::createFromFormat(
            'Y-m-d H:i:s', $response->event_date,
            $response->timezone
        )->setTimezone("Europe/London");
        $datetime_start = date("Ymd\THis", strtotime($eventDate));
        $datetime_end = date("Ymd\THis", strtotime($eventDate));
        $description = $response->title . ': ' . $response->topic . ',  Hosted by: ' . $response->host . (!empty($response->webinar_url)
                ? ",  URL: " . $response->webinar_url
                : "");
        $datetime_stamp = Carbon::now()->format('Ymd\THis');

        if (in_array($response->user_type->type, ['admin', 'risk-engineer', 'underwriter'])) {
            $submission['email'] = $response->liberty_user->email;
        } else {
            $submission['email'] = $response->user->email;
        }

        $location = (isset($response->location) && !empty($response->location)
            ? $response->location
            : "");

        $ics_file = "BEGIN:VCALENDAR\r\nVERSION:2.0\r\nPRODID:-//Risk Appetite//EN\r\nMETHOD:REQUEST\r\nBEGIN:VEVENT\r\nSUMMARY:" . $summary . "\r\nDTSTART;VALUE=DATE-TIME:" . $datetime_start . "\r\nDTEND;VALUE=DATE-TIME:" . $datetime_end . "\r\nDTSTAMP;VALUE=DATE-TIME:" . $datetime_stamp . "\r\nUID:" . Str::random(40) . "\r\nSEQUENCE:1\r\nATTENDEE:" . $submission['email'] . "\r\nCREATED;VALUE=DATE-TIME:" . $datetime_stamp . "\r\nDESCRIPTION:" . $description . "\r\nLOCATION:" . $location . "\r\nORGANIZER:" . $submission['email'] . "\r\nPRIORITY:5\r\nSTATUS:CONFIRMED\r\nBEGIN:VALARM\r\nACTION:DISPLAY\r\nDESCRIPTION:Reminder\r\nTRIGGER;RELATED=START:-PT1H\r\nEND:VALARM\r\nEND:VEVENT\r\nEND:VCALENDAR";

        $storage = storage_path('icals');

        File::put($storage . $response->_id . '.ics', $ics_file);
        $file = $storage . $response->_id . '.ics';

        $headers = [
            'Content-Type: text/calendar',
        ];

        return Response::download($file, 'ical.ics', $headers)->deleteFileAfterSend(true);
    }

    public function getPopular($type, $subscriptions)
    {
        // popular posts
        $popularPost = json_decode(Api::get('api/v1/community/messages/get-popular-event/' . $type . '/' . urlencode($subscriptions) . '/' . User::getUserType()));

        $response = $popularPost
            ?: [];

        return Response::json($response);
    }
}
