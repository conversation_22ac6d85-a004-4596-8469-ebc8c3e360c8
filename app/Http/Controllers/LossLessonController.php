<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Documents;
use App\Models\Api;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;
class LossLessonController extends BaseController
{
    const TEMPLATE_DIRECTORY = '/lossLesson';

    const ROUTE_PREFIX = 'loss-lesson';

    public function __construct(Request $request, Api $api, Documents $documents)
    {
        parent::__construct($request);
        $this->api = $api;
        $this->documents = $documents;
    }

    public function index($id)
    {
        //get all loss lesson
        $documents = json_decode($this->api->get('api/v1/loss-lesson/organisation/'.$id));
        if($documents->response == 'success') {
            return view(
                static::TEMPLATE_DIRECTORY . '.index',
                array(
                'documents'  =>  $documents->data,
                'organisation'  =>  $id
                )
            );
        }
        else
        {
            return $documents->message;
        }
    }

    public function destroy($id,$lossLesson)
    {
        $document = json_decode($this->api->delete('api/v1/loss-lesson/organisation/'.$id.'/'.$lossLesson));

        if($document->response == "success") {
            return Redirect::route('organisation.{id}.loss-lessons.index', array('id' => $id))
                           ->with('success', 'Loss Lesson Document deleted');
        }

        return Redirect::route('organisation.{id}.loss-lessons.index', array('id' => $id))
        ->with('error', 'Loss Lesson Document not found');
    }

    public function edit($id,$lossLesson)
    {
        $document = json_decode($this->api->get('api/v1/loss-lesson/organisation/'.$id.'/'.$lossLesson));
        if($document->response == "success") {
            return view(
                static::TEMPLATE_DIRECTORY .'/edit',
                array(
                'organisation'  => $id,
                'document'      =>  $document->data
                )
            );
        }

        return Redirect::route('organisation.{id}.loss-lessons.index', array('id' => $id))
                       ->with('error', 'Loss Lesson Document not found');
    }


    public function update(Request $request, $id)
    {
        $rules = array(
        'description' => 'required',
        'document' =>  'max:20480|mimes:pdf,docx,doc,xlsx'
        );

        $validation = Validator::make($request->all(), $rules);

        if($validation->fails()) {
            return Redirect::back()->withErrors($validation->errors())->withInput($request->old());
        }


        $data = $request->except('_token');
        unset($data['_method']);

        if($request->hasFile('document')) {
            $document = json_decode($this->api->get('/api/v1/loss-lesson/organisation/'.$id.'/'.$data['id']));

            $fileName = strtolower(preg_replace('/[\s_]|^%20/', '-', $request->file('document')->getClientOriginalName()));
            $upload = $this->documents->update($id . '/'.$document->data->document_title, $request->file('document'), $fileName, $id);
            if($upload['response'] =='success') {
                $data['document_title'] = $fileName;
                unset($data['document']);
            }
        }
        $response = json_decode($this->api->post('/api/v1/loss-lesson/organisation/'.$id.'/update', $data));
        if($response->response == 'success') {
            return Redirect::route('organisation.{id}.loss-lessons.index', array('id' => $id))
                           ->with('success', 'Loss Lesson Document Updated');
        }

        return Redirect::route('organisation.{id}.loss-lessons.index', array('id' => $id))
                       ->with('error', 'Loss Lesson Document not found');
    }


    public function show($id,$lossLesson)
    {
        $document = json_decode($this->api->get('/api/v1/loss-lesson/organisation/' . $id . '/' . $lossLesson));

        if ($document->response == 'success' ) {
            $fileName = $id . '/' . $document->data->document_title;
            $file     = $this->documents->download($fileName, $document->data->document_title);
            if ($file['response'] == "success" ) {
                $decrypt = $this->documents->decrypt($file['data'], $document->data->document_title);
            } else {
                return Response::json(
                    [
                    'response' => 'error',
                    'message'  => $file['message']
                    ] 
                );
            }

            $response = Response::download(
                $decrypt['data'],
                $document->data->document_title,
                array( 'Content-Type' => 'text/plain' ) 
            );

            return $response;
        }
    }

    /**
     * @param $id
     *
     * @return mixed
     */
    public function create($id)
    {

        $sectors = json_decode(Api::get('/api/v1/sector/all'));
        $covers = json_decode(Api::Get('/api/v1/cover/all'));

        return view(
            static::TEMPLATE_DIRECTORY . '/create',
            array(
            'organisation'  => $id,
            'sectors'       => $sectors->data,
            'covers'        => $covers->data
            )
        );
    }


    public function store(Request $request, $id)
    {
        $rules = array(
            'name'          =>  'required',
        'description'    =>  'required',
        'document' =>  'required|max:20480|mimes:pdf,docx,doc,xlsx'
        );

        $validation = Validator::make($request->all(), $rules);

        if($validation->fails()) {
            return Redirect::back()->withErrors($validation->errors())->withInput($request->old());
        }
        $data = $request->except('_token');
        $data['category'] = 'LOSSLESSON';

        $file = $this->documents->encrypt($request->file('document'));
        $fileName = strtolower(preg_replace('/[\s_]|^%20/', '-', $request->file('document')->getClientOriginalName()));
        $upload = $this->documents->upload($file, $fileName, $id);

        if($upload['response'] == 'success') {
            $data['document_title'] = $fileName;
            unset($data['document']);
        }

        $response = json_decode(Api::post('/api/v1/document/store', $data));
        if($response->response == 'success') {
            return Redirect::route('organisation.{id}.loss-lessons.index', array('id' => $id))
                           ->with('success', 'Loss Lesson Document Created');
        }
        else
        {
            return Redirect::back()
                ->withInput($request->old())
                ->with('error', $response->message);
        }
    }
}
