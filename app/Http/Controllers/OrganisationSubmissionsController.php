<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\FileUpload;
use App\Models\Api;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class OrganisationSubmissionsController extends BaseController
{
    public function __construct(Request $request, FileUpload $fileUpload)
    {
        parent::__construct($request);

        $this->files = $fileUpload;
    }

    public function index()
    {
        $pendingSubmissions = json_decode(Api::get('api/v1/do-facilities/pending-submissions'));

        return view(
            'do-facilities/pending-submissions/index', [
            'pendingSubmissions' => $pendingSubmissions->data,
            ]
        );
    }

    public function show($id)
    {
        $submission = json_decode(Api::get('api/v1/do-facilities/pending-submissions/' . $id));

        if (! isset($submission->data)) {
            return Redirect::route('do-facilities.pending-submissions.index');
        }

        $submission = $submission->data;

        if (isset($submission->logo) && !is_null($submission->logo) && $submission->logo != '' && $submission->logo != 'none') {
            $submission->logo = $this->files->link($submission->logo);
        }

        return view(
            'do-facilities/pending-submissions/show', [
            'submission' => $submission,
            ]
        );
    }

    public function status(Request $request, $id, $status = '')
    {
        if (! $status) {
            return Redirect::route('do-facilities.pending-submissions.show', $id)
                ->with('error', 'Invalid submission status.');
        }

        $data = $request->except('_token');

        $response = json_decode(Api::post('api/v1/do-facilities/pending-submissions/' . $id . '/status/' . $status));

        if ($status == 'approved') {
            return Redirect::route('do-facilities.pending-submissions.show', $id)
                ->with($response->status, $response->message);
        }

        return Redirect::route('do-facilities.pending-submissions.index', $id)
            ->with($response->status, $response->message);
    }

    public function showForm($id)
    {
        // check to see if the submission link exists
        $submissionLink = json_decode(Api::get('api/v1/do-facilities/submission-links/' . $id));

        if ($submissionLink->status == 'error') {
            return Redirect::to('/');
        }

        $submissionLink = $submissionLink->data;

        // and make sure the link is not revoked
        if ($submissionLink->revoked_at && ! Session::has('success')) {
            return view('do-facilities/guest-form', ['linkExpired' => true]);
        }

        $pendingSubmissions = json_decode(Api::get('api/v1/do-facilities/pending-submissions'));

        $libertyBranches = json_decode(Api::get('/api/v1/liberty-branches/options'));

        return view(
            'do-facilities/guest-form', [
            'submissionLink' => $submissionLink,
            'linkExpired' => false,
            'libertyBranches' => $libertyBranches,
            ]
        );
    }

    public function submitForm(Request $request, $id)
    {
        $data = $request->except('_token');

        $data['submission_link_id'] = $id;

        $validation = Validator::make(
            $data, [
            'name' => 'required|regex:/(^[A-Za-z0-9 ]+$)+/',
            'policy_number' => 'required',
            'inception_date_of_cover' => 'required',
            'expiry_date_of_cover' => 'required',
            'description' => 'required',
            'email' => 'required|email',
            'phone' => 'required',
            'address_line_1' => 'required',
            'address_line_2' => 'required',
            'postcode' => 'required',
            'country' => 'required',
            'admin.first_name' => 'required',
            'admin.last_name' => 'required',
            'admin.job_title' => 'required',
            'admin.email' => 'required|email',
            'admin.phone' => 'required',
            ], [
            'name.required' => 'The Company Name is required.',
            'policy_number.required' => 'The Policy Number is required.',
            'inception_date_of_cover.required' => 'The Inception Date Of Cover is required.',
            'expiry_date_of_cover.required' => 'The Expiry Date Of Cover is required.',
            'description.required' => 'The Business Description is required.',
            'email.required' => 'The Company Email is required.',
            'email.email' => 'The Company Email must be a valid email address.',
            'phone.required' => 'The Company Phone is required.',
            'address_line_1.required' => 'The Company Address Line 1 is required.',
            'address_line_2.required' => 'The Company Address Line 2 is required.',
            'postcode.required' => 'The Company Postcode is required.',
            'admin.first_name.required' => 'The Admin First Name is required.',
            'admin.last_name.required' => 'The Admin Last Name is required.',
            'admin.job_title.required' => 'The Admin Job Title is required.',
            'admin.email.required' => 'The Admin Email is required.',
            'admin.email.email' => 'The Admin Email must be a valid email address.',
            'admin.phone.required' => 'The Admin Phone is required.',
            ]
        );

        if ($validation->fails()) {
            return ['error' => $validation->errors()];
        }

        $recaptcha_url = 'https://www.google.com/recaptcha/api/siteverify';
        $recaptcha_secret = config('app.google.secret_key');
        $recaptcha_response = $data['recaptcha_response'];

        $recaptcha = file_get_contents($recaptcha_url . '?secret=' . $recaptcha_secret . '&response=' . $recaptcha_response);
        $recaptcha = json_decode($recaptcha);

        if (isset($recaptcha->score) && !empty($recaptcha->score) && $recaptcha->score >= 0.5) {
            return ['message' => 'If any of the following field have been auto-populated your form will not be submitted. Please refresh this page and fill the form again by manually typing the relevant details in each field.', 'status' => 'fail'];
        }

        $data['inception_date_of_cover'] = strtotime(str_replace('/', '-', $data['inception_date_of_cover']));
        $data['expiry_date_of_cover'] = strtotime(str_replace('/', '-', $data['expiry_date_of_cover']));

        // Upload organisation logo
        if ($request->hasFile('image')) {
            $file = $request->file('image');
            $ext = $request->file('image')->getClientOriginalExtension();
            $name = Str::uuid()->toString();
            $nameExt = sprintf('%s.%s', $name->string, $ext);

            if (! is_bool($this->files->upload($file->getRealPath(), $nameExt))) {
                return ['message' => 'Failed to upload image, please refresh and try again.', 'status' => 'fail'];
            }

            $data['logo'] = $nameExt;
        }

        // Remove image from array
        unset($data['image']);

        $response = json_decode(Api::post('api/v1/do-facilities/pending-submissions', $data));

        return ['message' => $response->message, 'status' => 'success'];;
    }
}
