<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Documents;
use App\Models\FileUpload;
use App\Models\Survey;
use App\Models\Api;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Response;

class LegacySurveySubmissionsController extends BaseController
{

    const ROUTE_PREFIX = 'surveys';

    public function __construct(Request $request, Documents $doc, FileUpload $fileUpload, Survey $survey)
    {
        parent::__construct($request);
        $this->documents = $doc;
        $this->files = $fileUpload;
        $this->survey = $survey;
    }

    /**
     * Add attachment to a submission
     */
    public function add_attachment(Request $request)
    {
        $image_files = array('jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'tiff');

        if (strtolower($request->method()) == 'post') {

            $fields = $_FILES;
            foreach($fields as $key=>$val) {
                $file_field_name = $key;
            }
            $file = $request->file($file_field_name);
            $size = $request->file($file_field_name)->getSize();
            $path = $file->getRealPath();
            $type = $file->getMimeType();

            // $destinationPath = public_path('uploads'); // upload path
            $extension = $request->file($file_field_name)->getClientOriginalExtension(); // getting image extension

            $notes = $request->get('notes');
            // print_r($notes);
            $data['notes'] = isset($notes[str_replace(['.', ' '], ['_','_'], $request->file($file_field_name)->getClientOriginalName())]) ? $notes[str_replace(['.', ' '], ['_','_'], $request->file($file_field_name)->getClientOriginalName())] : '';
            //print_r($data);
            //exit;
            $notes_text = $data['notes'];

                $file = $request->file($file_field_name);
                $uuid = Str::uuid()->toString();
                $fileName = strtolower(preg_replace('/[\s_]|^%20/', '-', $request->file($file_field_name)->getClientOriginalName()));

                $upload = $this->survey->upload($file, $uuid, 'survey_uploads/'.$file_field_name);

            if ($upload['response'] == 'success') {
                $data['file_name'] = $fileName;
                $data['cloud_file_name'] = $uuid;
                $data['survey_id']  =   $request->get('srf');
                $data['field_name'] =   $file_field_name;
                $response = json_decode(Api::post('api/v1/surveys/legacy-add-attachment-info', $data));
            }
            else {
                //return \Illuminate\Support\Facades\Redirect::back()->with('error', $upload['message'])->withInput($request->old());
            }


            $data = [];

            $fileName_cloud = 'survey_uploads/'.$file_field_name.'/'.$uuid;
            $file = $this->survey->downloadLink($fileName_cloud, $fileName);

            array_push(
                $data, [
                "name"          => $fileName,
                "cloud"         => $fileName_cloud,
                "notes"         => $notes_text,
                "size"          => $size,
                "type"          => $type,
                "url"           => in_array($extension, $image_files) ? $file : '',
                "extension"     => $extension,
                "thumbnailUrl"  => in_array($extension, $image_files) ? $file : '',
                "deleteUrl"     => config('app.url').'/surveys/'.$request->get('srf').'/delete/'.$response->id,
                "deleteType"    => "DELETE"
                ]
            );
            return Response::json(
                [
                "files" => $data
                ]
            );

        } else {
                return Response::json(
                    [
                    "foo" => 'bar'
                     ]
                );
            if($request->has('field_name') && $request->has('srf')) {

                $response = json_decode(Api::get('api/v1/surveys/get-attachment-info/'.$request->get('field_name').'/'.$request->get('srf')));
                if($response->response == 'success') {
                    $files['files'] = [];
                    foreach ($response->data as $attachment) {
                        $fileName_cloud = 'survey_uploads/'.$attachment->field_name.'/'.$attachment->cloud_file_name;
                        $file = $this->survey->downloadLink($fileName_cloud, $attachment->file_name);

                        $file_info = pathinfo($attachment->file_name);
                        $extension = $file_info['extension'];

                        $ch = curl_init($file);

                        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                        curl_setopt($ch, CURLOPT_HEADER, true);
                        curl_setopt($ch, CURLOPT_NOBODY, true);

                        $data = curl_exec($ch);
                        $size = curl_getinfo($ch, CURLINFO_CONTENT_LENGTH_DOWNLOAD);

                        curl_close($ch);

                        $arr = [
                            "name"  =>  $attachment->file_name,
                            "cloud" =>  $fileName_cloud,
                            "notes" =>  $attachment->notes,
                            "url"   =>  in_array($extension, $image_files) ? $file : '',
                            "extension" => $extension,
                            "thumbnailUrl"  =>  in_array($extension, $image_files) ? $file : '',
                            "deleteUrl" =>  config('app.url').'/surveys/'.$request->get('srf').'/delete/'.$attachment->id,
                            "size"  => $size
                        ];
                        array_push($files['files'], $arr);
                    }
                    return json_encode($files);
                }
            }
        }
    }

}
