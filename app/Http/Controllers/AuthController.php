<?php

namespace App\Http\Controllers;

use App\Helpers\Helpers;
use App\Models\Api;
use App\Models\User;
use App\Rules\NotPreviouslyUsedPassword;
use Carbon\Carbon;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use PragmaRX\Google2FA\Exceptions\IncompatibleWithGoogleAuthenticatorException;
use PragmaRX\Google2FA\Exceptions\InvalidCharactersException;
use PragmaRX\Google2FA\Exceptions\SecretKeyTooShortException;
use PragmaRX\Google2FA\Google2FA;
use PragmaRX\Google2FAQRCode\Google2FA as Google2FAQRCode;
use PragmaRX\Google2FAQRCode\Exceptions\MissingQrCodeServiceException;
use Illuminate\Support\Facades\Cache;
use Illuminate\Validation\Rules\Password;
use Illuminate\Support\Str;

class AuthController extends BaseController
{
    //Template Path
    const
    TEMPLATE_PATH = 'auth',
    DEFAULT_USER_TYPE = 'liberty-user';

    /**
     * login form
     */
    public function login(Request $request, $user_type = null)
    {
        $params = array_filter(
            [
            'user_type' => $user_type,
            ]
        );

        if (isset($params['user_type'])) {
            if (strtolower($request->method()) == 'post') {
                $trimmedInputs = [
                    'email' => trim($request->get('email')),
                    'password' => $request->get('password')
                ];

                $validator = Validator::make(
                    $trimmedInputs, [
                    'email'    => 'required|email',
                    'password' => 'required',
                    ]
                );

                if ($validator->fails()) {
                    return Redirect::route(
                        'login',
                        $params
                    )->withErrors(
                        $validator
                    )->withInput(
                        $request->old()
                    );
                } else {
                    // create our user data for the authentication
                    $data = [
                        'email'    => Crypt::encrypt($trimmedInputs['email']),
                        'password' => Crypt::encrypt($request->get('password'))
                    ];

                    $api = json_decode(
                        Api::post(
                            static::get_api_uri('auth', $user_type . 's'),
                            $data
                        )
                    );

                    if (data_get($api, 'response') === 'error' && data_get($api, 'password_expired')) {
                        $forgotPasswordHtmlLink = '<a href="'. route('password-reset', ['user_type' => $user_type]) .'">Forgotten Password</a>';
                        $errorMessage = 'Your password has expired. Please use the '.$forgotPasswordHtmlLink.' feature to reset your password.';
                        return redirect()
                            ->back()
                            ->withErrors($errorMessage);
                    }

                    if ($api->response === 'success') {
                        $user = User::recast(
                            'User',
                            $api->data
                        );

                        Auth::login($user);

                        $user->type = $user_type;
                        $user_role = $user->role;
                        $is_aspen = $user->is_aspen;

                        //print_r($user_type.' '.$user_role); exit;

                        switch (true) {
                        case ($user_type == 'liberty-user' && $user_role == 'underwriter'):
                            $login_type = 'underwriter';
                            break;

                        case ($user_type == 'liberty-user' && $user_role == 'risk-engineer'):
                            $login_type = 'risk-engineer';
                            break;

                        case ($user_type == 'liberty-user' && $user_role == 'aspen-user' && $is_aspen == 1):
                            $login_type = 'aspen-user';
                            break;

                        case ($user_type == 'liberty-user' && $user_role == 'branch-admin' && $is_aspen == 1):
                            $login_type = 'aspen-branch-admin';
                            break;

                        case ($user_type == 'liberty-user' && $user_role == 'branch-user' && $is_aspen == 1):
                            $login_type = 'aspen-branch-user';
                            break;

                        case ($user_type == 'liberty-user' && $user_role == 'branch-admin' && $is_aspen == 0):
                            $login_type = 'branch-admin';
                            break;

                        case ($user_type == 'liberty-user' && $user_role == 'branch-user' && $is_aspen == 0):
                            $login_type = 'branch-user';
                            break;
                        case ($user_type == 'liberty-user' && $user_role == 'virtual-rooms'):
                            $login_type = 'virtual-rooms';
                            break;

                        case ($user_type == 'external-surveyor' && $user->isRoleAdminOrAccountManager()):
                            $login_type = 'external-surveyor-admin';
                            break;

                        case ($user_type == 'external-surveyor' && $user_role == 'surveyor'):
                            $login_type = 'external-surveyor';
                            break;

                        case ($user_type == 'broker-user'):
                            $login_type = 'broker-user';
                            Session::put('assigned_organisations', $api->assigned_orgs);
                            Session::put('NON_MGA_USER_ACCESS', []); 
                            Session::put('MGA_USER_ACCESS', []);
                            break;

                        default:
                            $login_type = 'risk-control';
                            break;
                        }

                        $user->login_type = $login_type;

                        // Store relevent data in sessions
                        Session::put('role', $user_role);
                        Session::put('user', $user);
                        Session::put('type', 'User');
                        Session::put('force_logout_at', strtotime(config('session.force_log_out_after', '8 hours')));

                        $gtmUserType = Helpers::getGtmUserType($login_type);
                        if ($user->isAccountManager()) {
                            $gtmUserType = 'Account Engineer';
                        }
                        Session::put('gtm_login_data', [
                            'user_type' => $gtmUserType,
                        ]);
                        Session::put('just_logged_in', true);

                        if(Cache::has("vr_staff".$user->id)) {
                            $timezone = Cache::get("vr_staff".$user->id);
                        }else{
                            $timezone = json_decode(Api::get('/api/v1/virtual-rooms/staff/'.$user->email));
                            Cache::put("vr_staff".$user->id, $timezone, 1000);
                        }
                        
                        if($timezone->response == 'success') {
                            $socialUser['id'] = $user->id;
                            $socialUser['name'] = $timezone->data->representative->name;
                            $socialUser['email'] = $timezone->data->representative->email;
                            $socialUser['mobile'] = $timezone->data->representative->mobile;
                            $socialUser['office_number'] = $timezone->data->representative->office_number;
                            $socialUser['profile_picture'] = $timezone->data->representative->profile_picture;
                            $socialUser['job_title'] = $timezone->data->representative->job_title;
                            $socialUser['timezone'] = $timezone->data->representative->office_timezone;
                            $socialUser['person_id'] = $timezone->data->representative->id;

                            Session::put('socials-user', $socialUser);
                            Session::put('socials-role', 'virtual-rooms');
                        }

                    } else {
                        return Redirect::route(
                            'login',
                            $params
                        )->withErrors(
                            ['login-error' => $api->message]
                        ); 
                    }
                }
            }

            return (Auth::check())
            ? Redirect::intended('/')
            : view(static::TEMPLATE_PATH . '/login', $params);
        } else {
            $params['user_type'] = static::DEFAULT_USER_TYPE;

            if(Cookie::get('login-cookie') !== null) {
                $params['user_type'] = Cookie::get('login-cookie');
                $cookie = Cookie::forget('login-cookie');
                return Redirect::route(
                    'login',
                    $params
                )->withCookie($cookie);
            }

            return Redirect::route(
                'login',
                $params
            );
        }
    }

    /**
     * login form
     */
    public function mobilelogin(Request $request, $user_type = null)
    {

        $params = array_filter(
            [
            'user_type' => $user_type,
            ]
        );

        if (isset($params['user_type'])) {
            if (strtolower($request->method()) == 'post') {
                $validator = Validator::make(
                    $request->all(), [
                    'email'    => 'required|email',
                    'password' => 'required',
                    ]
                );

                if ($validator->fails()) {
                    return Response::json(
                        [
                         'response' => 'error',
                         'message' => 'Could not log in, please try again.'
                        ]
                    );
                } else {
                    // create our user data for the authentication
                    $data = [
                    'email'    => Crypt::encrypt($request->get('email')),
                    'password' => Crypt::encrypt($request->get('password'))
                    ];

                    $api = json_decode(
                        Api::post(
                            static::get_api_uri('mobile/auth', $user_type . 's'),
                            $data
                        )
                    );

                    if ($api->response === 'success') {
                        return Response::json($api);
                    } else {
                        $api = json_decode(
                            Api::post(
                                static::get_api_uri('mobile/auth', 'liberty-user' . 's'),
                                $data
                            )
                        );
                        if ($api->response === 'success') {
                               return Response::json($api);
                        }
                    }
                    return Response::json(
                        [
                        'response' => 'error',
                        'message' => 'failed'
                        ]
                    );
                }
            }

            return (Auth::check())
            ? Redirect::intended('/')
            : view(static::TEMPLATE_PATH . '/login', $params);
        } else {
            $params['user_type'] = static::DEFAULT_USER_TYPE;

            return Redirect::route(
                'login',
                $params
            );
        }
    }

    public function rrInspireLogin()
    {
        if(Auth::check() && (Session::get('user')->login_type == 'risk-control' || Session::get('user')?->isRoleAdminOrAccountManager())) {
            $data = ['id' => Session::get("user")->id];

            $api = json_decode(
                Api::post(
                    '/api/v1/rrappetite/auth',
                    $data
                )
            );

            if ($api->response === 'success') {
                   $url = config('app.inspire_url').'/auth/'.$api->login_key;
                   return Redirect::to($url);
            }

            return Response::json(
                [
                'response' => 'error',
                'message' => 'failed'
                ]
            );
        }
    }

    /**
     * logout form
     */
    public function logout()
    {
        // $api = json_decode(Api::post(
        //     '/api/v1/validated/activate?valid=0&user_id=' . \Illuminate\Support\Facades\Session::get('user')->id
        // ));

        // dd($api);

        // if ($api->response == 'success') {
        $user_type = isset(Session::get('user')->type) ? Session::get('user')->type : static::DEFAULT_USER_TYPE;

        Session::flush();
        Auth::logout();

        return Redirect::route(
            'login',
            ['user_type' => $user_type]
        );
        // }
    }
    /**
     *
     *
     */
    public function passwordReset(Request $request, $user_type = null)
    {
        $params = array_filter(
            [
            'user_type' => $user_type,
            'code'      => $request->get('code', null),
            ]
        );

        if (isset($params['user_type'])) {
            if (strtolower($request->method()) == 'post') {
                $validator = Validator::make(
                    $request->all(), [
                    'email' =>  'email|required'
                    ]
                );

                if (!$validator->fails()) {
                       $data = $request->except('_token');

                    $api = json_decode(
                        Api::post(
                            static::get_api_uri('send-reset-password-code', $user_type . 's'),
                            $data
                        )
                    );

                          $message = ($api->response == 'success')
                           ? 'Password was reset, Please check you emails for instructions on the next step'
                           : $api->message;

                        return Redirect::route(
                            'password-reset',
                            $params
                        )->with(
                            $api->response,
                            $message
                        );
                } else {
                    return Redirect::route(
                        'password-reset',
                        $params
                    )->withErrors(
                        $validator->errors()
                    )->withInput(
                        $request->old()
                    );
                }
            }

            return view(
                static::TEMPLATE_PATH . '/password_reset' . (isset($params['code']) ? '_code' : ''),
                $params
            );
        } else {
            $params['user_type'] = static::DEFAULT_USER_TYPE;

            return Redirect::route(
                'password-reset',
                $params
            );
        }
    }

    /**
     * Reset password with code
     */
    public function passwordResetCode(Request $request, $user_type = null)
    {
        $params = array_filter(
            [
            'user_type' => $user_type,
            'code'      => $request->get('code', null),
            ]
        );
        
        $tableName = Str::snake(Str::camel($user_type)) . 's';

        if (isset($params['user_type'])) {
            $validator = Validator::make(
                $request->all()
                ,[
                    'password'          =>  ['required', Password::defaults(), (new NotPreviouslyUsedPassword(['table' => $tableName]))],
                    'password_confirm'  =>  'required|same:password',
                    'code'              =>  'required'
                ]
            );

            if (!$validator->fails()) {
                $data = $request->except('_token');
                $data['password'] = Crypt::encrypt($data['password']);
                unset($data['password_confirm']);

                $api = json_decode(
                    Api::post(
                        static::get_api_uri('reset-password', $user_type . 's'),
                        $data
                    )
                );

                if (!empty($api->user_id)) {
                    $this->resetTfa($api->user_id, $params['user_type']);
                }

                   return ($api->response == 'success')
                    ? Redirect::route('login', $params)
                    : Redirect::route('password-reset', $params)->with('error', $api->message);
            } else {
                return Redirect::route(
                    'password-reset',
                    $params
                )->withErrors(
                    $validator->errors()
                )->withInput(
                    $request->old()
                );
            }
        } else {
            $params['user_type'] = static::DEFAULT_USER_TYPE;

            return Redirect::route(
                'password-reset-code',
                $params
            );
        }
    }

    /**
     * Acitvate Users
     */
    public function activate(Request $request, $user_type)
    {

        $params = array_filter([
            'user_type' => $user_type,
            'code'      => $request->get('code', null),
        ]);

        if (isset($params['user_type'])) {
            if (strtolower($request->method()) == 'post') {
                $data = $request->except('_token');

                $validator = Validator::make(
                    $data, [
                    'email'            => 'email|required',
                    'password'         => 'required',
                    'password_confirm' => 'required|same:password',
                    ]
                );

                if ($validator->fails()) {
                    return Redirect::route(
                        'activate',
                        $params
                    )->withInput(
                        $request->old()
                    )->withErrors(
                        $validator->errors()
                    );
                }

                $data['password'] = Crypt::encrypt($data['password']);

                unset($data['password_confirm']);

                $api = json_decode(
                    Api::post(
                        static::get_api_uri('activate', $user_type . 's'),
                        $data
                    )
                );
                
                if ($api->response == 'success') {
                    $email = trim($request->get('email'));
                    $userData = [
                        'email'    => Crypt::encrypt($email),
                        'password' => Crypt::encrypt($request->get('password'))
                    ];
    
                    $userApi = json_decode(
                        Api::post(
                            static::get_api_uri('auth', $user_type . 's'),
                            $userData
                        )
                    );

                    if ($userApi->response === 'success') {
                        $user = User::recast(
                            'User',
                            $userApi->data
                        );

                        $user->type = 'liberty-user';
                        $user->login_type = $user->role;

                        Auth::login($user);
                        Session::put('role', $user->role);
                        Session::put('user', $user);
                        Session::put('type', 'User');
                        Session::put('force_logout_at', strtotime(config('session.force_log_out_after', '8 hours')));
                        if ($user->isAccountManager()) {
                            $gtmUserType = 'Account Engineer';

                            Session::put('gtm_login_data', [
                                'user_type' => $gtmUserType,
                            ]);
                        }

                        Session::put('just_logged_in', true);
                        return redirect()->intended('/');
                    }

                    return Redirect::route('login', ['user_type' => $user_type])->with('success', 'User activated');
                } else {
                    return Redirect::route('activate', $params)->with('error', $api->message)->withInput($request->old());
                }
            }

            return (isset($params['code']))
            ? view(static::TEMPLATE_PATH . '/activate', $params)
            : Redirect::route('login', $params);
        } else {
            $params['user_type'] = static::DEFAULT_USER_TYPE;
            return Redirect::route(
                'activate/' . static::DEFAULT_USER_TYPE,
                $params
            );
        }
    }

    /**
     * Two Factor Auth
     */

    public function handleVerify()
    {
        return view('auth.two-factor-verify');
    }

    public function checkVerify(Request $request)
    {
        $validator = Validator::make(
            $request->all(), [
                'secret' => ['required', 'digits:6', 'numeric'],
            ]
        );

        if ($validator->fails()) { 
            return view('auth.two-factor-verify')->withErrors(['2fa-error' => 'The code you entered is invalid. Please try again.']);
        }
        
        $user = Session::get('user');
        $valid = (new Google2FA)->verifyKey($user->secret, $request->get('secret'));

        if (!$valid) {
            return view('auth.two-factor-verify')->withErrors(['2fa-error' => 'The code you entered is invalid. Please try again.']);
        }

        if ($user->established == 0) {
            $api = json_decode(
                Api::post(
                    '/api/v1/established/' . $user->type .'?user_id=' . $user->id
                )
            );

            if ($api->response == 'success') {
                $user->established = 1;
            }
        }

        $twoFactorCookie = config('mfa.cookie') . '_' . md5($user->email);
        $twoFactorCookieLifetime = 60 * 24 * 3; // 3 days default
        if ($request->has('remember_2fa')) {
            $twoFactorCookieLifetime = config('mfa.cookie_lifetime');
        }

        return Redirect::to('/')->withCookie(
            Cookie::make($twoFactorCookie, Carbon::now(), $twoFactorCookieLifetime)
        );
    }

    public function checkNewVerify(Request $request)
    {
        $validator = Validator::make(
            $request->all(), [
                'secret' => ['required', 'min:6', 'numeric'],
            ]
        );
        
        if ($validator->fails()) { 
            return $this->generate2FAErrorResponse();
        }

        $user = Session::get('user');
        $valid = (new Google2FA)->verifyKey($user->secret, $request->get('secret'));

        if (!$valid) {
            return $this->generate2FAErrorResponse();
        }

        if ($user->established == 0) {
            $api = json_decode(
                Api::post(
                    '/api/v1/established/' . $user->type .'?user_id=' . $user->id
                )
            );

            if ($api->response == 'success') {
                $user->established = 1;
            }
        }

        $twoFactorCookie = config('mfa.cookie') . '_' . md5($user->email);
        $twoFactorCookieLifetime = 60 * 24 * 3; // 3 days default
        Cookie::make($twoFactorCookie, Carbon::now(), $twoFactorCookieLifetime);

        return response(view('auth.two-factor-success'))->cookie($twoFactorCookie, true, $twoFactorCookieLifetime);
    }

    /**
     * @throws MissingQrCodeServiceException
     */
    public function handleNew2FA(): Factory|View|Application
    {
        $user = Session::get('user');

        if(!$user) {
            $user = Auth::user();
        }

        $google2fa = new Google2FAQRCode();

        try {
            $google2fa_url = $google2fa->getQRCodeInline(
                'Risk Reduce - Admin',
                $user->email,
                $user->secret
            );
            return view('auth.two-factor', compact('google2fa_url'));
        } catch (
            MissingQrCodeServiceException
            | IncompatibleWithGoogleAuthenticatorException
            | InvalidCharactersException
            | SecretKeyTooShortException $e
        ) {
            return view('auth.two-factor')->withErrors(
                [
                    'error' => 'Error generating QR code.',
                ]
            );
        }
    }

    /**
     * @return RedirectResponse
     */
    public function twoFactorSkip(Request $request): RedirectResponse
    {
        $key = '19kjflire3123dxMnakdj';
        $requestKey = $request->input('key');
        if($requestKey == $key){
            Session::put('skip2fa', true);
            return redirect()->intended('/');
        }
    }

    public function registerNotificationToken(Request $request)
    {
        $data = $request->except('_token');
        $response = json_decode(Api::post('/api/v1/register-notification-token', $data));
        if ($response->response == 'success') {
            return json_encode($response);
        } else {
            return json_encode(['response' => 'error']);
        }
    }

    public function vrSession($business)
    {

        Session::put('vr-session', $business);

        return Redirect::back();
    }

    public function sha1(Request $request)
    {
        $data = $request->except('_token');
        return json_encode(['sha1' => sha1(strtolower($data['text']))]);
    }

    private function resetTfa($user_id, $user_type)
    {
        $tFaCode = 'reset-lu-tfa';

        if ($user_type == 'broker-user') {
            $tFaCode = 'reset-bu-tfa';
        }

        if ($user_type == 'external-surveyor') {
            $tFaCode = 'reset-es-tfa';
        }

        try {
            $key = (new Google2FA)->generateSecretKey();

            json_decode(
                Api::post(
                    '/api/v1/'.$tFaCode.'?user_id=' . $user_id . '&key=' . $key
                )
            );

        } catch (\Exception $e) {
            \Log::error($e->getMessage());
        }
    }

    private function generate2FAErrorResponse() 
    {
        $user = Session::get('user');
        $google2fa = new Google2FAQRCode();
    
        $google2fa_url = $google2fa->getQRCodeInline(
            'Risk Reduce - Admin',
            $user->email,
            $user->secret
        );
        return view('auth.two-factor', compact('google2fa_url'))->withErrors(['2fa-error' => 'The code you entered is invalid. Please try again.']);
    }
}
