<?php

namespace App\Http\Controllers;
use App\Models\PublicForm;
use App\Models\User;
use App\Traits\HasForms;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Str;
use App\Models\Api;

class PublicFormsController extends BaseController
{
    use HasForms;

    public function allowForUser(User $user): bool
    {
        return in_array($user->login_type, ['risk-control', 'underwriter', 'risk-engineer']);
    }

    public function getPublicForms()
    {
        $response = json_decode(Api::get('api/v1/form/public/get-all-forms'));
        return response()->json([
            'data' => $response->data,
        ]);
    }

    /*
     * Create a public form
     */
    public function create($copyForm = null)
    {
        $data = [];

        $organisations = json_decode(Api::get('api/v1/organisation/options'));
        $data['organisations'] = $organisations;

        $sectors = json_decode(Api::get('api/v1/sector/all'))->data;
        $data['sectors'] = $sectors;


        // document levels
        $level1 = json_decode(Api::Get('api/v1/doc_level/null/1'));
        $data['level1'] = $level1->data;
        $data['public'] = true;

        // copy a form
        if (!is_null($copyForm)) {
            $response = json_decode(Api::get(PublicForm::PUBLIC_FORM_API_ENDPOINT . '/' .$copyForm));
            $form = json_decode($response->data, true);
            $form['fields'] = json_encode($form['fields']);
            $data['form'] = $form;
        }

        return view(
            'forms/create', $data
        );
    }

    /*
    * Save a form
    */
    public function store(Request $request)
    {
        $data['form'] = $request->get('form');
        $data['user_id'] = Session::get('user')->id;

        $response = json_decode(Api::post(PublicForm::PUBLIC_FORM_API_ENDPOINT, $data));
        if ($response->response == "success") {
            echo $response->data;
        }
    }

    /*
     * Redirect with success
     */
    public function success()
    {
        return Redirect::to('/forms')
            ->with('success', 'Form created successfully');
    }

    /*
     * Edit a form
     */
    public function edit($publicForm)
    {
        $response = json_decode(Api::get(PublicForm::PUBLIC_FORM_API_ENDPOINT . '/' . $publicForm));
        // document levels

        if ($response->response == "success") {
            $form = json_decode($response->data, true);

            if (isset($form['formType']) && $form['formType'] == 'categorised') {

                $api_url = '/api/v1/doc_levels_all/'.$form['level1_type'];

                if (isset($form['level2_type']) && isset($form['level3_type']) && isset($form['level4_type'])) {

                    $api_url = $api_url.'/'.$form['level2_type'].'/'.$form['level3_type'].'/'.$form['level4_type'];

                } elseif (isset($form['level2_type']) && isset($form['level3_type'])) {

                    $api_url = $api_url.'/'.$form['level2_type'].'/'.$form['level3_type'];

                } elseif (isset($form['level2_type'])) {

                    $api_url = $api_url.'/'.$form['level2_type'];

                } else {
                }

                $doc_levels = json_decode(Api::Get($api_url));

                $doc_levels = $doc_levels->data;

            } else {

                $doc_levels = [];

            }
            $form['fields'] = json_encode($form['fields']);
            $form['selected_sectors']=[]; //selected sectors not required for public forms

            $org_links = json_decode(Api::get(PublicForm::PUBLIC_FORM_API_ENDPOINT . '/org-links/' . $publicForm))->data;
            $level1 = json_decode(Api::Get('/api/v1/doc_level/null/1'));
            $organisations = json_decode(Api::get('api/v1/organisation/options'));
            return view(
                'forms/edit',
                [
                    'form' => $form,
                    'links' => $org_links,
                    'organisations' => $organisations,
                    'level1' => $level1->data,
                    'levels' => $doc_levels,
                    'public' => true,
                    'sectors' => [], //sector not required for public forms
                ]
            );
        } else {
            die('An error occurred, please try again');
        }
    }

    /*
    * Update a form
    */
    public function update(Request $request, $formID)
    {
        $data['form'] = $request->get('form');
        $data['user_id'] = Session::get('user')->id;
        $response = json_decode(Api::put(PublicForm::PUBLIC_FORM_API_ENDPOINT . '/' .$formID, $data));
        if ($response->response == "success") {
            echo $response->data;
        }
    }

    /*
    * Preview a form
    */
    public function show($form)
    {
        $response = json_decode(Api::get(PublicForm::PUBLIC_FORM_API_ENDPOINT . '/' .$form.'?user_id='.Session::get('user')->id));
        if ($response->response == "success") {
            $form = json_decode($response->data, true);

            $form['fields'] = json_encode($form['fields']);
            return view(
                'forms/show',
                [
                    'form' => $form
                ]
            );
        } else {
            die('An error occurred, please try again');
        }
    }

    /*
    * Delete a form
    */
    public function delete($form)
    {
        $response = json_decode(Api::delete(PublicForm::PUBLIC_FORM_API_ENDPOINT . '/' .$form.'?user_id='.Session::get('user')->id));
        if ($response->response == "success") {
            return Redirect::to('forms')->with('success', 'Form deleted successfully');
        } else {
            die('An error occurred, please try again');
        }
    }

    /**
     * Confirms updated successfully
     */
    public function updated()
    {
        return Redirect::route('forms.index')->with('success', 'Form updated successfully');
    }

    public function createLink(Request $request)
    {
        $data = $request->all();

        $response = json_decode(Api::post(PublicForm::PUBLIC_FORM_LINK_API_ENDPOINT . '/' . $data['formId'] . '/' . $data['orgId']));

        if ($response->response === "success") {
            $publicLink = $response->data->domain . $response->data->uuid;
            $publicLinkPreview = substr($publicLink, 8, 23) . '...' . substr($publicLink, -19);
            $response->link_preview = $publicLinkPreview;

            return Response::json($response);
        }

        die('An error has occurred while saving');
    }

    public function deleteLink($link_id)
    {
        $response = json_decode(Api::get('/api/v1/form/public/delete-form-link/'.$link_id));
        return Redirect::route('forms.index')->with('success', 'Form link has been deleted');
    }

    public function sharedLink(Request $request)
    {
        $data = $request->all();
        $data['sharedLink_slug']=Str::slug($data['sharedLink']);
        $response = json_decode(Api::post(PublicForm::PUBLIC_FORM_SHARED_LINK_API_ENDPOINT, $data));
        if ($response->response === "success") {
            $data=$response->data;
            $formdata=(object)[];
            $formdata->slug=$data->shared_link_slug;
            $formdata->formId=$data->_id;
            $formdata->shared_link=$data->domain.$data->shared_link_slug;
            $formdata->display_link=substr($formdata->shared_link, 8, 23) . '...' . substr($formdata->shared_link, -19);
            $formdata->response=$response->response;
            return Response::json($formdata);
        }
        return Response::json($response);
    }

    public function deleteSharedLink($link_id)
    {
        $response = json_decode(Api::post('/api/v1/form/public/delete-shared-link/'.$link_id));
        return Response::json($response);
    }
}
