<?php

namespace App\Http\Controllers;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Response;
use Illuminate\Http\Request;
use App\Models\Api;
use App\Services\CacheContent\GetLibertyUsersService;
use App\Services\CacheContent\OptionListService;
use App\Services\SendSqsMessageService;
use PragmaRX\Google2FA\Google2FA;

class LibertyUserController extends BaseResourceController
{
    const
    NAV_ID = 'our-team',
    TEMPLATE_PATH = '/liberty-users',
    ROUTE_PREFIX = 'liberty-users';

    public function __construct(Request $request)
    {
        BaseController::__construct($request);
    }

    public function resetTfa($user_id)
    {
        $key = (new Google2FA)->generateSecretKey();

        $api = json_decode(
            Api::post(
                '/api/v1/reset-lu-tfa?user_id=' . $user_id . '&key=' . $key
            )
        );

        if ($api->response == 'success') {
            return Redirect::route(
                static::ROUTE_PREFIX . '.index'
            )->with(
                'success',
                $api->message
            );
        }

        return Redirect::route(
            static::ROUTE_PREFIX . '.index'
        )->with(
            'error',
            $api->message
        );
    }

    /**
     * Delete a user
     *
     * @param $id
     *
     * @return mixed
     */

    public function destroy($id)
    {
        $logged_in_user = Session::get('user');

        if (isset($logged_in_user->id) && $logged_in_user->id != $id) {
            return parent::destroy($id);
        } else {
            return Redirect::route(
                static::ROUTE_PREFIX . '.index'
            )->with(
                'error',
                'You are currently logged in as this Liberty User'
            );
        }
    }


    public function generateLink($userID)
    {
        if(isset($userID)) {
            //$data = ['id' => $userID];
            $response = json_decode(Api::get('/api/v1/liberty-users/link/'.$userID));

            // print_r(Api::get('api/v1/liberty-users/link/'.$userID)); exit;

            if($response->response == "success") {
                return Response::json(
                    [
                    'response'  => 'success',
                    'data'      =>  $response->data
                    ]
                );
            }
        }
    }


    /**
     * On update success
     *
     * @param array $data
     */
    public function onUpdateSuccess($data)
    {
        self::recacheRelatedData();

        if (isset($data['send_invite'])) {
            Api::get(static::get_api_uri('send-welcome/' . $data['id']));
        }

        $logged_in_user = Session::get('user');

        if ($logged_in_user->id == $data['id']) {
            foreach($data as $key => $value) {
                if (isset($logged_in_user->$key)) {
                    $logged_in_user->$key = $value;
                }
            }
            Session::put('user', $logged_in_user);
        }
    }

    /**
     * Parse data before storage
     *
     * @param  array $data
     * @return array parsed version of $data
     */
    public function parseDataBeforeStorage($data)
    {
        if (isset($data['branch'])) {
            $data['branch_id'] = $data['branch'];
            unset($data['branch']);
        } else {
            $data['branch_id'] = 0;
        }

        if (($data['role'] ?? '') === 'account-engineer') {
            $data['role'] = 'risk-engineer';
            $data['role_override'] = 'account-engineer';
        }

        $data['claims_notification'] = (int)(isset($data['claims_notification']));

        return $data;
    }

    /**
     * Get additional view parameters for a specific view (index/create/edit)
     *
     * @param  string $view
     * @param  array  $params
     * @return array of additional parameters
     */
    public function getRouteParams(Request $request, $view, $params = [])
    {
        return array_merge(
            $params, [
            'options' => [
            'branch'        => static::getBranchOptions(),
            'aspen_branch'  => static::getASPENBranchOptions(),
            'brokers'       => static::getBrokersOptions(),
            'role'          => static::getRoleOptions(),
            'qualified_for' => static::getQualifiedForOptions(),
            ],
            ]
        );
    }

    /**
     * Get additional view parameters for a specific view (index/create/edit)
     *
     * @param  string  $view
     * @return array of additional parameters
     */
    public function getAdditionalViewParams(string $view, $params = [])
    {
        return array_merge(
            (array)$params, [
            'options' => [
            'branch'        => static::getBranchOptions(),
            'aspen_branch'  => static::getASPENBranchOptions(),
            'brokers'       => static::getBrokersOptions(),
            'role'          => static::getRoleOptions(),
            'qualified_for' => static::getQualifiedForOptions(),
            ],
            ]
        );
    }

    /**
     * Get validation rules for a specific method (store/update/etc)
     *
     * @param  string $method
     * @return array of validation rules
     */
    public function getValidatorRules(Request $request, $method)
    {
        return [
        'first_name' =>  'required',
        'last_name'  =>  'required',
        'email'      =>  'required|email'
        ];
    }

    private static function getBranchOptions()
    {
        return json_decode(
            Api::get(
                static::get_api_uri('options', 'liberty-branches')
            )
        );
    }

    private static function getASPENBranchOptions()
    {
        return json_decode(
            Api::get(
                static::get_api_uri('options/aspen-branches', 'liberty-branches')
            )
        );
    }

    private static function getBrokersOptions()
    {
        return json_decode(
            Api::get(
                static::get_api_uri('options', 'brokers')
            )
        );
    }

    private static function getRoleOptions()
    {
        return static::buildOptionsFromKeys(
            [
            'admin',
            'underwriter',
            'branch-user',
            'branch-admin',
            'aspen-user',
            'risk-engineer',
            'virtual-rooms',
            'account-engineer',
            ]
        );
    }

    private static function getQualifiedForOptions()
    {
        return static::buildOptionsFromKeys(
            [
            'property',
            'casualty',
            'commercial-combined',
            ]
        );
    }

    public static function onDestroySuccess($id)
    {
        self::recacheRelatedData();
    }

    public static function onStoreSuccess($data)
    {
        self::recacheRelatedData();
    }
    
    public static function recacheRelatedData()
    {
        try {
            SendSqsMessageService::sendMessages([
                [
                    'serviceClass' => GetLibertyUsersService::class,
                    'params' => '',
                ],
                [
                    'serviceClass' => OptionListService::class,
                    'params' => 'underwriters',
                ],
                [
                    'serviceClass' => OptionListService::class,
                    'params' => 'aspenusers',
                ],
            ]);
        } catch (\Exception $e) {
            \Log::error("[LibertyUserController@recacheRelatedData] " . $e->getMessage());
        }
    }
}
