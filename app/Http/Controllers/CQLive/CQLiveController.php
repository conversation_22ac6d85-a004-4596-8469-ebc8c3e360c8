<?php

namespace App\Http\Controllers\CQLive;

use App\Http\Controllers\BaseController;
use Illuminate\Http\Request;
use App\Models\Api;


class CQLiveController extends BaseController
{
    public function __construct(Request $request)
    {

    }

    public function getPayload(Request $request)
    {
        $response=json_decode(Api::get('api/v1/cqlive/get-payload'));
        print_r($response);
        exit;
    }

    public function storePayload(Request $request)
    {
        $data = json_decode(file_get_contents('php://input'), true);
        return Api::postCQLive('api/v1/cqlive/add-payload', $data);
    }
}
