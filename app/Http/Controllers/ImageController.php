<?php

namespace App\Http\Controllers;
use Illuminate\Support\Facades\Redirect;
use Intervention\Image\Facades\Image;
use App\Models\FileUpload;
use Illuminate\Http\Request;
class ImageController extends BaseController
{
    const TEMPLATE_DIRECTORY = '/image';
    const ROUTE_PREFIX = 'image';

    public function __construct(Request $request, FileUpload $fileUpload)
    {
        parent::__construct($request);
        $this->files = $fileUpload;
    }

    public function cropper($id)
    {
        $file = $this->files->link($id);
        return view(static::TEMPLATE_DIRECTORY . '/cropper', array('file' => $file, 'id' => $id));
    }

    public function crop($id, $x, $y, $width, $height)
    {
        // get image
        $tmp = $this->files->downloadTemp($id);
        $img = Image::make($tmp);
        // crop it
        $img->crop($width, $height, $x, $y);
        $img->save($tmp);
        // delte from s3
        $this->files->destroy($id);
        // upload crop to s3
        $this->files->upload($tmp, $id);
        return Redirect::to($_GET['return'])->with('message', 'Image Cropped');
    }

}
