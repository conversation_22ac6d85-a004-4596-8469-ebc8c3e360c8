<?php

namespace App\Http\Controllers;

use App\Models\Api;
use App\Models\PublicForm;
use App\Models\User;
use App\Traits\HasForms;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Session;

class FormsController extends BaseController
{
    use HasForms;

    public function allowForUser(User $user): bool
    {
        $login_type = $user->login_type;
        $uri = request()->segment(2);

        return (in_array($login_type, ['risk-control', 'underwriter', 'risk-engineer']) || ($login_type == 'broker-user' && $uri == 'show'));
    }

    /*
     * List of all forms
     */
    public function index(Request $request)
    {
        $page = $request->get('page');
        $page = isset($page) ? $request->get('page') : 1;
        $limit = Session::has('limit') ? Session::get('limit') : 10;
        $type = $request->get('type') ? $request->get('type') : 'protected';

        $search = $request->get('search');

        $searchString = $search ? http_build_query(['search' => $search]) : '';
        $url = $type === 'protected'
            ? 'api/v1/form/'.$page.'/'.$limit . '?' . $searchString
            : PublicForm::PUBLIC_FORM_API_ENDPOINT . '/'.$page.'/'.$limit . '?' . $searchString;

        $forms = json_decode(Api::get($url));

        if ($forms->response == "success") {
            return view(
                'forms/index',
                array(
                    'request' => $request,
                    'forms' => $forms->data,
                    'total' => $forms->total,
                    'limit' => $limit,
                    'search' => $search,
                    'page' => $page,
                    'type' => $type,
                    'link' => 'forms.index'
                )
            );
        }
    }

    public function getPrivateForms()
    {
        $response = json_decode(Api::get('api/v1/form/private/get-all-forms'));
        return response()->json([
            'data' => $response->data,
        ]);
    }

    /*
     * List of all claims submissions
     */
    public function claims()
    {
        $response = json_decode(Api::get('api/v1/form/submitted_forms/accident_claims'));
        if ($response && $response->response == "success") {
            $forms = json_decode($response->data);
            // echp '<pre>';print_r($forms);exit;
            return view(
                'forms/claims',
                array(
                    'forms' => $forms
                )
            );
        } else {
            return view(
                'forms/claims',
                array(
                    'forms' => []
                )
            );
        }
    }

    /*
     * Create a form
     */
    public function create($copyForm = null)
    {
        $data = [];

        $organisations = json_decode(Api::get('api/v1/organisation/options'));
        $data['organisations'] = $organisations;

        $sectors = json_decode(Api::get('api/v1/sector/all'))->data;
        $data['sectors'] = $sectors;

        // document levels
        $level1 = json_decode(Api::Get('api/v1/doc_level/null/1'));
        $data['level1'] = $level1->data;

        // copy a form
        if (!is_null($copyForm)) {
            $response = json_decode(Api::get('api/v1/form/'.$copyForm));
            $form = json_decode($response->data, true);

            if (!isset($form['fields'])) {
                return Redirect::to('/forms')->with('error', 'Form fields not found');
            }

            $form['fields'] = json_encode($form['fields']);
            $data['form'] = $form;
        }

        return view(
            'forms/create', $data
        );
    }

    /*
    * Save a form
    */
    public function store(Request $request)
    {
        $data['form'] = $request->get('form');
        $data['user_id'] = Session::get('user')->id;

        $response = json_decode(Api::post('api/v1/form', $data));
        if ($response->response == "success") {
            echo $response->data;
        }
    }

    /*
     * Redirect with success
     */
    public function success()
    {
        return Redirect::to('/forms')
            ->with('success', 'Form created successfully');
    }

    /*
     * Edit a form
     */
    public function edit($form)
    {
        $response = json_decode(Api::get('api/v1/form/'.$form));

        if ($response->response == "success") {
            $form = json_decode($response->data, true);

            if (isset($form['formType']) && $form['formType'] == 'categorised') {

                $api_url = 'api/v1/doc_levels_all/';

                if(isset($form['level1_type'])) {
                    $api_url =  $api_url.$form['level1_type'];
                }

                if (isset($form['level2_type']) && isset($form['level3_type']) && isset($form['level4_type'])) {

                    $api_url = $api_url.'/'.$form['level2_type'].'/'.$form['level3_type'].'/'.$form['level4_type'];

                } elseif (isset($form['level2_type']) && isset($form['level3_type'])) {

                    $api_url = $api_url.'/'.$form['level2_type'].'/'.$form['level3_type'];

                } elseif (isset($form['level2_type'])) {

                    $api_url = $api_url.'/'.$form['level2_type'];

                } else {
                }

                $doc_levels = json_decode(Api::get($api_url));

                $doc_levels = $doc_levels->data;

            } else {
                $doc_levels = [];
            }

            $form['fields'] = isset($form['fields']) ? json_encode($form['fields']) : null;

            if(!isset($form['selected_sectors'])) {
                $form['selected_sectors']=[];
            }

            if(!isset($form['name'])) {
                $form['name']='';
            }

            if(!isset($form['notifications'])) {
                $form['notifications']='';
            }

            if(!isset($form['formType'])) {
                $form['formType']='';
            }

            if(!isset($form['fileUploads'])) {
                $form['fileUploads']='';
            }

            if(!isset($form['notify_group_user'])) {
                $form['notify_group_user']=false;
            }

            if(!isset($form['notify_liberty_admin'])) {
                $form['notify_liberty_admin']=false;
            }

            $sectors = json_decode(Api::get('api/v1/sector/all'))->data;


            $organisations = json_decode(Api::get('api/v1/organisation/options'));

            // document levels
            $level1 = json_decode(Api::get('api/v1/doc_level/null/1'));

            return view(
                'forms/edit',
                [
                    'form' => $form,
                    'organisations' => $organisations,
                    'sectors' => $sectors,
                    'level1' => $level1->data,
                    'levels' => $doc_levels
                ]
            );
        } else {
            die('An error occurred, please try again');
        }
    }

    /*
    * Update a form
    */
    public function update(Request $request, $formID)
    {
        $data['form'] = $request->get('form');
        $data['user_id'] = Session::get('user')->id;
        $response = json_decode(Api::put('api/v1/form/'.$formID, $data));
        if ($response->response == "success") {
            echo $response->data;
        }
    }

    /*
    * Preview a form
    */
    public function show($form)
    {
        $response = json_decode(Api::get('api/v1/form/'.$form.'?user_id='.Session::get('user')->id));
        if ($response->response == "success") {
            $form = json_decode($response->data, true);

            $form['fields'] = isset($form['fields']) ? json_encode($form['fields']): null;
            
            return view(
                'forms/show',
                [
                    'form' => $form
                ]
            );
        } else {
            die('An error occurred, please try again');
        }
    }

    /*
    * Delete a form
    */
    public function delete($form)
    {
        $response = json_decode(Api::delete('api/v1/form/'.$form.'?user_id='.Session::get('user')->id));
        if ($response->response == "success") {
            return Redirect::to('forms')->with('success', 'Form deleted successfully');
        } else {
            die('An error occurred, please try again');
        }
    }

    /**
     * Confirms updated successfully
     */
    public function updated()
    {
        return Redirect::route('forms.index')->with('success', 'Form updated successfully');
    }
}
