<?php

namespace App\Http\Controllers;

use App\Models\Api;
use App\Models\Documents;
use App\Models\FileUpload;
use App\Services\CacheContent\GetOrganisationPolicyDocs;
use App\Services\SendSqsMessageService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Response;
class DocumentPolicyController extends BaseController
{

    const TEMPLATE_DIRECTORY = '/document';

    const ROUTE_PREFIX = 'organisation.document';

    public function __construct(Request $request, Documents $doc, FileUpload $fileUpload)
    {
        parent::__construct($request);

        $this->documents = $doc;
        $this->files = $fileUpload;
    }

    /**
     * [index description]
     *
     * @return [type] [description]
     */
    public function index()
    {
        $respone = json_decode(Api::get('/api/v1/document-policy/all'));
        $data = array();

        if(isset($respone)) {
            if(isset($respone->data)) {
                $data = $respone->data;
            }
        }
        return view('document_policy.index', array('data' => $data));
    }


    /**
     * [save description]
     *
     * @param  integer $type_id [description]
     * @return [type]           [description]
     */
    public function save(Request $request, $type_id = 0)
    {
        $rules = ['title' => 'required'];
        $params = array(
                    'type_id' => $type_id);

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {

            return Redirect::route('document.type', $params)
                ->withInput($request->old())->withErrors($validator->errors());
        }

        $post_data = $request->except(array('_token'));
        $post_data['desc'] = $post_data['desc'] != null ? $post_data['desc']: " ";

        $response = json_decode(Api::post('/api/v1/document-policy/save/'.$type_id, $post_data));


        if(isset($response)) {
            //$params['type_id'] = $response->data->id;

            return Redirect::route('document.types')->with('success', 'Policy document saved.');
        }

        return Redirect::route('document.type', $params)->with('error', 'Policy type not created');
    }

    /**
     * [delete description]
     *
     * @param  [type] $type_id [description]
     * @return [type]          [description]
     */
    public function delete($type_id)
    {
        $data = json_decode(Api::post('/api/v1/document-policy/type/delete/'.$type_id));

        return Redirect::route('document.types');
    }

    public function delete_policy_document($id, $document_id)
    {
        $data = json_decode(Api::post('/api/v1/document/delete/policy-doc/'.$document_id));

        $this->recacheRelatedData($id);
        
        return Redirect::route('policy.documents.all', array('id' => $id));
    }

    /**
     * [show description]
     *
     * @param  [type] $organisation_id [description]
     * @param  [type] $type_id         [description]
     * @return [type]                  [description]
     */
    public function show($type_id = 0)
    {

        $respone = json_decode(Api::get('/api/v1/document-policy/type/'.$type_id));
        $data = null;

        if(isset($respone->data)) {
            $data = $respone->data;
        }

        return view('document_policy.show', array('data' => $data));
    }

    /**
     * [get_all_documents description]
     *
     * @param  [type] $id [description]
     * @return [type]     [description]
     */
    public function get_all_documents($id)
    {
        $data = array('policy_docs' => [], 'policy_doc_types' => []);
        $policy_doc_types = json_decode(Api::get('/api/v1/document-policy/all'));
        $documents = json_decode(Api::get('/api/v1/document/policy/'.$id));

        if(isset($policy_doc_types->data)) {
            if(isset($documents)) {
                $data = array('policy_docs' => $documents, 'policy_doc_types' => $policy_doc_types->data);
            }
        }
        return view('document_policy.policy_documents', array('organisation_id' => $id, 'policy_docs' => $data['policy_docs'], 'policy_doc_types' => $data['policy_doc_types']));
    }

    /**
     * [get_policydocument_show description]
     *
     * @param  [type]  $id          [description]
     * @param  integer $document_id [description]
     * @return [type]               [description]
     */
    public function get_policydocument_show($id, $document_id = 0)
    {
        $document = null;
        $policy_doc_types = array();

        if ($document_id > 0) {
            $response_document = json_decode(Api::get("/api/v1/document/find/$document_id/organisation/$id"));

            if (isset($response_document)) {

                $document = !empty($response_document->data) ? $response_document->data : null;
            }
        }

        $response = json_decode(Api::get('/api/v1/document-policy/all'));

        if (isset($response)) {
            if(empty($response->data)) {
                return Redirect::route('document.types');
            }

            $policy_doc_types = $response->data;
        }

        return view(
            static ::TEMPLATE_DIRECTORY . '/create_policy', array(
            'policy_doc_types' => $policy_doc_types,
            'document' => $document,
            'organisation' => $id)
        );
    }

    /**
     * [update_policy_type description]
     *
     * @param  [type] $id          [description]
     * @param  [type] $document_id [description]
     * @return [type]              [description]
     */
    public function update_policy_type(Request $request, $id, $document_id)
    {
        $rules = [ 'document_type_id' => 'required'];

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return Redirect::route('policy.documents.all', array('id' => $id))
                ->withInput($request->old())->withErrors($validator->errors());
        }

        $document = null;
        $document_type_id = $request->get('document_type_id');

        if (isset($document_id)) {
            $response_document = json_decode(Api::get("/api/v1/document/find/$document_id/organisation/$id"));

            if (isset($response_document)) {
                $document = $response_document->data;
                $document->document_type_id = $document_type_id;
                $document->document_id = $document_id;

                $response = json_decode(Api::post('/api/v1/document/store', $document));

                if(isset($response) && $response->id > 0) {
                    $this->recacheRelatedData($id);
                    return Redirect::route('policy.documents.all', array('id' => $id))->with('success', 'Policy document updated successfully');
                }
            }
        }

        return Redirect::route('policy.documents.all', array('id' => $id))->with('error', 'Document not updated');
    }

    /**
     * [retrieve_policy_document description]
     *
     * @param  [type] $id          [description]
     * @param  [type] $name        [description]
     * @param  [type] $document_id [description]
     * @return [type]              [description]
     */
    public function retrieve_policy_document($id, $name, $document_id)
    {
        $document = json_decode(Api::get("/api/v1/document/find/$document_id/organisation/$id"));

        if(isset($document)) {
            if ($document->data->id > 0) {
                $fileName = $id . '/' . $document->data->document_store_name;

                $fileNameAlt = '0/' . $document->data->document_store_name;

                if(!$this->documents->exists($fileName, $document->data->document_store_name)) {
                    $fileName = '0/' . $document->data->document_store_name;
                }


                $file = $this->documents->download($fileName, $document->data->document_store_name);
                if ($file['response'] == 'success') {
                    if(strtotime($document->data->created_at)-1480467600 > 0) {
                        $response = Response::download($file['data'], $document->data->document_title, array('Content-Type' => 'text/plain'));
                    } else {
                        $decrypt = $this->documents->decrypt($file['data'], $document->data->document_store_name);
                        $response = Response::download($decrypt['data'], $document->data->document_title, array('Content-Type' => 'text/plain'));
                    }

                }
                else {
                    return Response::json(['response' => 'error', 'message' => $file['message']]);
                }


                return $response;
            }
        }
    }


    public function notify_admins($id, $document_id = 0)
    {
        $response = json_decode(Api::post("/api/v1/document/notify/policy-doc/$id/$document_id"));
        return Redirect::route('policy.documents.all', array('id' => $id))->with('success', $response);
    }

    public function recacheRelatedData($orgId)
    {
        SendSqsMessageService::sendMessages([
            [
                'serviceClass' => GetOrganisationPolicyDocs::class,
                'params' => $orgId ?? '',
            ]
        ]);
    }

}
