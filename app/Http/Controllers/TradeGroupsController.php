<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;
use App\Models\Api;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Pagination\Paginator;
class TradeGroupsController extends BaseResourceController
{
    const
        TEMPLATE_PATH = '/trade-groupings',
        ROUTE_PREFIX = 'trade-groupings',
        RESPOND_TO_AJAX = true;

    public function index(Request $request)
    {
        if ($request->has('page')) {
            $data = json_decode(Api::get('api/v1/trade-groupings?page='.$request->get('page')));
        } else {
            $data = json_decode(Api::get('api/v1/trade-groupings'));
        }

        $tradeGroups = json_decode($data->data);
        $data = !empty($tradeGroups->data) ? $tradeGroups->data : [] ;
        return view(
            'trade-groupings.index', [
            'resources' => $data,
            'paginator' => new Paginator($data, $tradeGroups->total, $tradeGroups->per_page),
            ]
        ); /**TODO: FIX OR REFACTOR PAGINATOR */
    }

    public function defaultTradeGroup($id)
    {
        $data = json_decode(Api::get('api/v1/trade-groupings/'.$id.'/default'));
        return Redirect::back();
    }
}
