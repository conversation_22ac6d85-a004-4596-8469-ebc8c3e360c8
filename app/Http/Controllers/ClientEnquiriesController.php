<?php

namespace App\Http\Controllers;

use App\Models\FileUpload;
use App\Models\User;
use Carbon\Carbon;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Session;
use App\Models\Api;
class ClientEnquiriesController extends BaseController
{

    //TEMPLATE PATH
    const TEMPLATE_PATH = '/clients';
    const ROUTE_PREFIX = 'enquiries';

    public function __construct(Request $request, FileUpload $fileUpload)
    {
        parent::__construct($request);
        $this->files = $fileUpload;
    }

    public function allowForUser(User $user): bool
    {
        return $user->login_type === 'risk-control' || $user?->isRoleAdminOrAccountManager();
    }

    /**
     * @throws GuzzleException
     * @throws \Exception
     */
    public function index(Request $request, )
    {
        $user = Session::get('user');
        $search = $request->get('search', '');
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 10);
        $get = [];

        $get[] = 'search='.urlencode($search);
        $get[] = 'page='.urlencode($page);
        $get[] = 'limit='.urlencode($limit);

        $url='api/v1/clientenquiries/all?'.implode('&', $get);

        $resources = json_decode(Api::get($url));

        if ($resources->response == 'success') {
            return view(
                static::TEMPLATE_PATH.'/client-enquiries-report',
                array_merge(
                    [
                    'resources' => $resources->data,
                    'total' => $resources->total,
                    'limit' => $limit,
                    'page' => $page,
                    'user_details' => $user,
                    'search' => $search,
                    'link' => static::ROUTE_PREFIX.'.client-enquiries',
                    ]
                )
            );
        } else {
            throw new \Exception($resources->message);
        }
    }

    /**
     * Export the enquiries in excel format
     *
     * @param  array $data
     * @return excel
     */
    public function exportExcel($data)
    {
        $excel = Excel::create(
            'rrappetite-export-' . Carbon::now()->format('d-m-Y'), function ($excel) use ($data) {
                $excel->sheet(
                    'Enquiries', function ($sheet) use ($data) {
                        $sheet->fromArray($data, null, 'A1', false, false);
                        $sheet->freezeFirstRowAndColumn();
                        $sheet->setFontFamily('Calibri');
                        $sheet->setFontSize(15);
                        $sheet->setFontBold(false);
                    }
                );
            }
        )->download('xlsx');
    }

    /**
     * Export the enquiries in csv format
     *
     * @param  array $data
     * @return void
     */
    public function exportCsv($data)
    {
        header('Content-Type: application/csv');
        header('Content-Disposition: attachment; filename="rrappetite-export-' . Carbon::now()->format('d-m-Y').'.csv";');
        $f = fopen('php://output', 'w');
        foreach ($data as $line) {
            fputcsv($f, $line);
        }
    }
}
