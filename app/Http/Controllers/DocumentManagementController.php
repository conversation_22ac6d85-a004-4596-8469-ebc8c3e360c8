<?php

namespace App\Http\Controllers;
use App\Http\Controllers\BaseController;
use App\Models\FileUpload;
use App\Models\Api;
use Illuminate\Support\Facades\Response;

class DocumentManagementController extends BaseController
{

    private $valid_dms_user=false;

    public function __construct(FileUpload $fileUpload)
    {
        $this->files = $fileUpload;
        $logged_in = Api::check_dms_key();
        if (isset($logged_in) && isset(json_decode($logged_in)->response) &&  json_decode($logged_in)->response == 'success') {
            $this->valid_dms_user=true;
        }
    }

    //ALL
    public function GetOrganisationDetailsForDMS($organisation_id)
    {
        if (!$this->valid_dms_user) {
            return Response::json(['response' => 'error','message'  => 'Unable to login member'], 200);
        }else{
            $organisationDetails =json_decode(Api::get('/api/v1/organisation/dms/'.$organisation_id));

            if(!is_null($organisationDetails->data->logo) && $organisationDetails->data->logo != '' && $organisationDetails->data->logo != 'none') {
                $organisationDetails->data->image_url = $this->files->link($organisationDetails->data->logo);
            } else {
                $organisationDetails->data->image_url = '/img/dummy/logo-placeholder.png';
            }
            return Response::json(
                [
                'response'  =>  'success',
                'data'      =>  $organisationDetails
                ]
            );
        }
    }

    public function GetAllOrganisations()
    {
        if (!$this->valid_dms_user) {
            return Response::json(['response' => 'error','message'  => 'Unable to login member'], 200);
        }else{
            $organisationDetails = Api::get('/api/v1/organisation/dms/dms-organisations');
            return $organisationDetails;
        }
    }
}
