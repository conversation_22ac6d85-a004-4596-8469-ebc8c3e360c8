<?php

namespace App\Http\Controllers;
use App\Services\CacheContent\GetBrokerService;
use App\Services\CacheContent\GetMgaSchemeService;
use App\Services\SendSqsMessageService;
use Illuminate\Http\Request;
use App\Models\Api;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Pagination\Paginator;

class MgaSchemeController extends BaseController
{
    const
        TEMPLATE_PATH = '/mga-schemes',
        ROUTE_PREFIX = 'mga-schemes',
        RESPOND_TO_AJAX = true;

    public function __construct(Request $request)
    {
        BaseController::__construct($request);
    }

    public function index()
    {

        $data = GetMgaSchemeService::get('');

        return view(
            'mga-schemes.index', [
            'schemes' => isset($data) ? $data->data : []
            ]
        );
    }

    public function create()
    {
        $broker_organisations = GetBrokerService::get('');

        if ($broker_organisations) {
            foreach ($broker_organisations->data as $bo) {
                $brokerorgs[$bo->id] = $bo->name;
            }
        }

        $survey_forms = json_decode(Api::get('api/v1/risk-improvement/form/0/99999'));

        if ($survey_forms) {
            foreach ($survey_forms->data as $sf) {
                $surveyforms[$sf->_id] = $sf->name;
            }
        }

        $liberty_underwriters = json_decode(Api::get('api/v1/liberty-users/all/0/99999/underwriter'));

        if ($liberty_underwriters) {
            foreach ($liberty_underwriters->data as $lu) {
                $libertyunderwriters[$lu->id] = $lu->first_name . ' ' . $lu->last_name;
            }
        }

        $trade_groups = (array)json_decode(
            Api::get(
                static::get_api_uri('options', 'trade-groupings')
            )
        );

        $policy_types = json_decode(Api::get('api/v1/policy-types/all'));
        $pts = [];

        foreach ($policy_types->data as $pt) {
            $pts[$pt->name] = $pt->name;
        }

        return view('mga-schemes.create')->with(
            [
            'policy_types' => $pts,
            'broker_organisations' => isset($brokerorgs) ? $brokerorgs : [],
            'survey_forms' => isset($surveyforms) ? $surveyforms : [],
            'liberty_underwriters' => isset($libertyunderwriters) ? $libertyunderwriters : [],
            'trade_groups' => isset($trade_groups) ? $trade_groups : []
            ]
        );
    }

    public function store(Request $request)
    {
        $data = $request->except('_token');

        $response = json_decode(Api::post('/api/v1/mga-schemes', $data));

        if ($response) {
            if (isset($response->response) && $response->response == 'success') {
                self::recacheRelatedData('');
                return Redirect::route('mga-schemes.index')->with('success', 'MGA Scheme created successfully');
            }
        }

        return Redirect::route('mga-schemes.index')->with('error', 'Failed to create MGA Scheme');
    }

    public function show($id)
    {
        $data = json_decode(Api::get('api/v1/mga-schemes/' . $id));

        if (isset($data)) {
            $broker_organisations = GetBrokerService::get('');

            if ($broker_organisations) {
                foreach ($broker_organisations->data as $bo) {
                    $brokerorgs[$bo->id] = $bo->name;
                }
            }

            $survey_forms = json_decode(Api::get('api/v1/risk-improvement/form/0/99999'));

            if ($survey_forms) {
                foreach ($survey_forms->data as $sf) {
                    $surveyforms[$sf->_id] = $sf->name;
                }
            }

            $liberty_underwriters = json_decode(Api::get('api/v1/liberty-users/all/0/99999/underwriter'));

            if ($liberty_underwriters) {
                foreach ($liberty_underwriters->data as $lu) {
                    $libertyunderwriters[$lu->id] = $lu->first_name . ' ' . $lu->last_name;
                }
            }

            $trade_groups = (array)json_decode(
                Api::get(
                    static::get_api_uri('options', 'trade-groupings')
                )
            );

            $policy_types = json_decode(Api::get('api/v1/policy-types/all'));
            $pts = [];

            foreach ($policy_types->data as $pt) {
                $pts[$pt->name] = $pt->name;
            }

            return view(
                'mga-schemes.edit', [
                'scheme' => $data->data,
                'policy_types' => $pts,
                'broker_organisations' => isset($brokerorgs) ? $brokerorgs : [],
                'survey_forms' => isset($surveyforms) ? $surveyforms : [],
                'liberty_underwriters' => isset($libertyunderwriters) ? $libertyunderwriters : [],
                'trade_groups' => isset($trade_groups) ? $trade_groups : []
                ]
            );
        }

        return view('mga-schemes')->with('error', 'Failed to find MGA Scheme');
    }

    public function update(Request $request)
    {
        $data = $request->except('_token');

        $response = json_decode(Api::put('/api/v1/mga-schemes/' . $data['id'], $data));

        if ($response) {
            if (isset($response->response) && $response->response == 'success') {
                self::recacheRelatedData($data['id']);
                return Redirect::route('mga-schemes.index')->with('success', 'MGA Scheme updated successfully');
            }
        }

        return Redirect::route('mga-schemes.index')->with('error', 'Failed to update MGA Scheme');
    }

    public function destroy($id)
    {
        $response = json_decode(Api::delete('/api/v1/mga-schemes/' . $id));

        if ($response) {
            if (isset($response->response) && $response->response == 'success') {
                self::recacheRelatedData('');
                return Redirect::route('mga-schemes.index')->with('success', 'MGA Scheme deleted successfully');
            }
        }

        return Redirect::route('mga-schemes.index')->with('error', 'Failed to delete MGA Scheme');
    }

    public function mgaCaseSummary(Request $request)
    {
        $orderby = $request->has('orderby') ? $request->get('orderby') : 'name';
        $dir = $request->has('dir') ? $request->get('dir') : 'ASC';
        $page = $request->has('page') ? $request->get('page') : 1 ;
        $data = json_decode(Api::get('api/v1/mga-schemes?orderby='.$orderby.'&dir='.$dir.'&page='.$page.'&submissions=1'));
        $schemes = $data->data;
        $data = !empty($schemes->data) ? $schemes->data : [];

        foreach ($data as $key => $resource) {
            $risk_rec_count = 0;
            $form_id_array = [];

            foreach ($resource->surveys as $survey) {

                if (!$survey->submissions) {
                    continue;
                }

                $submission_obj = $survey->submissions;


                $form_fields = !is_null($survey->risk_rec_form_fields) ? $survey->risk_rec_form_fields : [];
                if(empty($form_fields)) { continue; 
                }
                foreach ($form_fields->risk_recommendation_fields as $field) {
                    for ($i = 1; $i <= 15; $i++) {
                        if(isset($submission_obj->{$field.'_'.$i.'_classification'}) && $submission_obj->{$field.'_'.$i.'_classification'} != '') {
                            $risk_rec_count++;
                        }
                    }
                }

            }

            $resource->risk_rec_count = $risk_rec_count;
        }

        $paginator = new LengthAwarePaginator($data, $schemes->total, $schemes->per_page, null, [
            'path' => Paginator::resolveCurrentPath()
        ]);

        return view(
            'mga-schemes.reports.case-summary', [
            'resources' => $data,
            'paginator' => $paginator,
            'request'   => $request,
            ]
        );
    }

    public function mgaSplitByTrade(Request $request)
    {
        $orderby = $request->has('orderby') ? $request->get('orderby') : 'name' ;
        $dir = $request->has('dir') ? $request->get('dir') : 'ASC' ;
        $data = json_decode(Api::get('api/v1/mga-schemes?orderby='.$orderby.'&dir='.$dir));
        $schemes = $data->data ;
        $data = !empty($schemes->data) ? $schemes->data : [] ;

        foreach($data as &$row){
            // dd($row);
            $total = 0 ;
            $counts = [] ;
            foreach($row->clients as $d){

                if(isset($d->trade->name) && isset($counts[$row->id][$d->trade->name])) {
                    $counts[$row->id][$d->trade->name]++ ;
                } else{
                    if((isset($d->trade->name) )) {
                        $counts[$row->id][$d->trade->name] = 1;
                    }

                }
                $total++ ;
            }
            $row->counts = $counts ;
            $row->total = $total ;
        }

        $paginator = new LengthAwarePaginator($data, $schemes->total, $schemes->per_page, null, [
            'path' => Paginator::resolveCurrentPath()
        ]);

        return view(
            'mga-schemes.reports.split-by-trade', [
            'resources' => $data,
            'paginator' => $paginator,
            'request'   => $request
            ]
        );
    }
    public static function recacheRelatedData($id)
    {
        if (empty($id)) {
            return;
        }
        SendSqsMessageService::sendMessages([
            [
                'serviceClass' => GetMgaSchemeService::class,
                'params' => $id ?? '',
            ]
        ]);
    }
}
