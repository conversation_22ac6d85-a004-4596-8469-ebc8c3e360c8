<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Redirect;
use App\Models\Api;
use Illuminate\Support\Facades\Cache;
class OrganisationOverviewController extends BaseController
{
    /**
     * @var string
     */
    const TEMPLATE_PATH = '/organisation';

    /**
     * @var string
     */
    const ROUTE_PREFIX = 'organisation';

    /**
     * @var array
     */
    private $types = [
        'Account Opinion' => [
            'Overall Opinion',
            'Significant changes',
            'Risk improvements',
            'Key Issues',
            'Key Account Features',
            'Loss Expectancies',
            'Natural Hazards',
            'RE Bursary',
            'Programme Strategy',
            'Account Meetings',
            'Liability Overview',
            'Client Services (Risk Reduce)',
        ],
        'Risk Engineering Programme' => [
            'Survey overview report',
            'Survey Programme',
            'Business Interruption',
        ],
        'Business Description' => [
            'Business Description',
        ],
    ];

    public function index($organisation_id)
    {
        if (Session::get('user')->login_type == 'broker-user') {
            return \Illuminate\Support\Facades\App::abort(401);
        }

        $organisation = json_decode(Api::get('/api/v1/organisation/'.$organisation_id));
        $overview = json_decode(Api::get('/api/v1/organisation/'.$organisation_id.'/overview'));

        

        if ($organisation->response == 'success' && $overview->response == 'success') {
            return view(
                'organisation.overview.index', [
                'organisation' => $organisation->data,
                'overview' => isset($overview->data) ? $overview->data : [],
                'types' => $this->types,
                ]
            );
        }

        return Redirect::route('organisation.index')
            ->with('error', 'Organisation not found');
    }

    public function edit($organisation_id)
    {
        if (Session::get('user')->login_type == 'broker-user') {
            return \Illuminate\Support\Facades\App::abort(401);
        }

        //NOTE:: 'organisation'.$oganisation_id, this cache can be used on Client and Admin
        //Reference cache Risk-Reduce-Client/app/Models/CsrReport getCSRReport($organisation_id) 
        if(Cache::has('organisation'.$organisation_id)){
            $organisation = Cache::get('organisation'.$organisation_id);
        }else{
            $organisation = json_decode(Api::get('api/v1/organisation/'.$organisation_id));
            Cache::put('organisation'.$organisation_id, $organisation, 1000);
        }

        $overview = json_decode(Api::get('/api/v1/organisation/'.$organisation_id.'/overview'));

        if ($organisation->response == 'success') {
            return view(
                'organisation.overview.edit', [
                'organisation' => $organisation->data,
                'overview' => $overview->data,
                'types' => $this->types,
                ]
            );
        }

        return Redirect::route('organisation.index')
            ->with('error', 'Organisation not found');
    }

    public function update(Request $request, $organisation_id)
    {
        if (Session::get('user')->login_type == 'broker-user') {
            return \Illuminate\Support\Facades\App::abort(401);
        }

        $data = $request->except('_token');

        $organisation = json_decode(Api::get('/api/v1/organisation/' . $organisation_id));
        $overview = json_decode(Api::post('/api/v1/organisation/'.$organisation_id.'/overview', $data));

        if ($organisation->response == 'success') {
            return Redirect::route('organisation.overview.edit', $organisation_id);
        }

        return Redirect::route('organisation.index')
            ->with('error', 'Organisation not found');
    }
}
