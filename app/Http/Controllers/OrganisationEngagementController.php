<?php

namespace App\Http\Controllers;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Models\Api;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\App;

class OrganisationEngagementController extends BaseController
{
    /**
     * Template Path
     */
    const TEMPLATE_PATH = '/organisation';

    /**
     * Route prefix
     */
    const ROUTE_PREFIX = 'organisation';

    public function index($id)
    {
        if (Session::get('user')->login_type == 'broker-user') {
            return App::abort(401);
        }

        $response = json_decode(Api::get('api/v1/organisation/' . $id));

        if ($response->response == 'success') {
            $orgdetails = app(OrganisationController::class)->getOrganisationDetailsForDashboard($id, false);

            $userId = Session::get('user')->organisation;
            $userManager = Session::get('user')->manager != null ? Session::get('user')->manager : 0;

            $accidentCompleted = json_decode(Api::get('api/v1/form/submitted_forms/accident_report/'.$id.'/'.Session::get('user')->id.'/1'));
            $accidentCompleted = json_decode($accidentCompleted->data, true);

            $acCount = 0;
            foreach ($accidentCompleted  as $ac) {
                if (isset($ac['form_name'])) {
                    $acCount++;
                }
            }

            $orgdetails['accident-completed'] = $acCount;

            $otherCompleted = json_decode(Api::get('api/v1/form/submitted_forms/'.$id.'/'.Session::get('user')->id.'/1'));


            $otherCompleted = json_decode($otherCompleted->data, true);
            $ocCount = 0;
            foreach ($otherCompleted  as $oc) {
                if (isset($oc['form_name'])) {
                    $ocCount++;
                }
            }

            $orgdetails['other-completed'] = $ocCount;

            $surveyCompletedCount = 0;
            $resources = json_decode(
                Api::get(
                    'api/v1/surveys/all?tracker=yes&client=yes&fetchnocache=yes&organisation_id='.$id
                )
            );

            if(isset($resources->data)) {
                foreach($resources->data as $card){
                    if(isset($card->submission) && ((isset($card->submission->csr_status) && $card->submission->csr_status == 'submitted') && (isset($card->submission->uwr_status) && $card->submission->uwr_status == 'submitted'))) {
                        $surveyCompletedCount++;
                    }
                    //echo "<pre>"; dd($card->submission);
                }
            }
            $orgdetails['surveys-completed'] = $surveyCompletedCount;

            $recsRaised = 0;
            $recsClosed = 0;

            foreach ($orgdetails['rr_status'] as $status) {
                $stat = $status['open'] + $status['closed'];
                $recsRaised += $stat;
                $recsClosed += $status['closed'];
            }

            $orgdetails['recs-raised'] = $recsRaised;
            $orgdetails['recs-closed'] = $recsClosed;

            $cronerUserCount = 0;

            foreach ($response->data->users as $user) {
                if ($user->croner_access) {
                    $cronerUserCount += 1;
                }
            }

            $orgdetails['croner-user-count'] = $cronerUserCount;
            $orgdetails['user-count'] = count($response->data->users);


            // document usage
            if (!isset($date_from)) {
                $today = Carbon::today();
                $today->day = 1;
                $today->subMonth(1);
                $date_from = $today->format('d-m-Y');
            }

            if (!isset($date_to)) {
                $today = Carbon::today();
                $today->day = 0;
                $date_to = $today->format('d-m-Y');
            }

            $documents = json_decode(Api::get('/api/v1/document/organisation/'.$id));
            $documents_id = array();

            foreach ($documents->data as $document) {
                array_push($documents_id, $document->id);
            }

            $type = 'document+access';
            $date_from = "01-01-2014";

            if (!isset($date_to)) {
                $today = Carbon::today();
                $today->day= 0;
                $date_to = $today->format('d-m-Y');
            }

            $dataStats = [
                'date_from' => $date_from,
                'date_to' => $date_to,
                'organisation' => $id,
                'branch' => "",
                "type" => $type
            ];

            $stats = json_decode(Api::post('/api/v1/log/overview', $dataStats));

            if ($stats->response == "success") {
                $stats_data = array(
                    'date_from' => $date_from,
                    'date_to'   => $date_to,
                    'branch'    => null,
                    'type'      => $type,
                    'page'      => 'metric'
                );

                if (isset($stats->data->croner_activation)) {
                    $stats->data->activations['croner_activation'] = $stats->data->croner_activation;
                    $stats->data->activations['safety_activation'] = $stats->data->safety_activation;
                    $stats->data->activations['croner_deactivation'] = $stats->data->croner_deactivation;
                    $stats->data->activations['safety_deactivation'] = $stats->data->safety_deactivation;

                    $count = 0;

                    foreach($stats->data->activations as $key => $result) {
                        $count += count($result);
                    }

                    $stats_data['count'] = $count;
                    $stats_data['total_safety'] = count($stats->data->activations['safety_activation']) - count($stats->data->activations['safety_deactivation']);
                    $stats_data['total_croner'] = count($stats->data->activations['croner_activation']) - count($stats->data->activations['croner_deactivation']);
                }

                if (isset($stats->data->client_create)) {
                    $stats->data->client['client_create'] = $stats->data->client_create;
                    $stats->data->client['client_delete'] = $stats->data->client_delete;
                    $stats->data->client['branch_create'] = $stats->data->branch_create;
                    $stats->data->client['branch_delete'] = $stats->data->branch_delete;
                }

                $stats_data['data'] = $stats->data;

                $downloadStat = (isset($stats_data['data']) && isset($stats_data['data']->$type)) ? count($stats_data['data']->$type) : 0;
            }

            $orgdetails['document-downloads'] = isset($downloadStat) ? $downloadStat : 0;
            // end document usage

            //category types
            $orgdetails['category_types'] = json_decode(Api::get('api/v1/schedule/category-types-for-schedule/'.$id));

            //Claims MI Report

            //Claims MI Report

            $response->data->claimsMiCount=0;
            $organisationReports = json_decode(Api::get('/api/v1/organisation/' . $id . '/reports/all'))->data;
            if (count($organisationReports)>0) {
                $claimsMInumber = array_map(
                    function ($report) {
                        return $report->type=="Claims MI";
                    }, $organisationReports
                );
                $response->data->claimsMiCount = count(array_filter($claimsMInumber));
            }

            //End - Claims MI Report

            $response->data->safetyMediaAccess = $response->data->safetyMediaAccess?"Yes":"No";

            return view(
                'organisation.engagement', [
                'organisation' => $response->data,
                'orgdetails' => $orgdetails,
                ]
            );
        }

        return Redirect::route(static::ROUTE_PREFIX . '.index')
            ->with('error', 'Organisation not found');
    }

    public function newIndex($id)
    {
        if (Session::get('user')->login_type == 'broker-user') {
            return App::abort(401);
        }

        $response = json_decode(Api::get('api/v1/organisation/' . $id), true);
        if ($response['response'] == 'success') {
            return view(
                'organisation.engagement-new', [
                'organisation' => $response['data']
                ]
            );
        }

        return Redirect::route(static::ROUTE_PREFIX . '.index')
            ->with('error', 'Organisation not found');
    }

    public function tilesBlock(Request $request, $id)
    {
        $filters = [
            'filters' => ['year' => $request->get('year', null)]
        ];

        $response = json_decode(Api::get('api/v1/organisation/' . $id), true);
        if ($response['response'] == 'success') {
            // Get the organization details
            $orgdetails = app(OrganisationController::class)
                ->getOrganisationDetailsForDashboard($id, false);

            // Get the category types
            $categories = json_decode(Api::get('api/v1/schedule/category-types-for-schedule/' . $id), true);
            $categoryTypes = [];
            if ($categories && is_array($categories)) {
                foreach ($categories as $category) {
                    $categoryTypes[$category['event_types']] = $category;
                }
            }

            // Get the surveys
            $surveyCompletedCount = 0;
            $resources = json_decode(
                Api::get(
                    'api/v1/surveys/all?tracker=yes&client=yes&organisation_id=' . $id . '&' . http_build_query($filters)
                ), true
            );
            if(isset($resources['data']) && is_array($resources['data'])) {
                foreach($resources['data'] as $card) {
                    if(isset($card['submission']) && isset($card['submission']['csr_status']) && $card['submission']['csr_status'] == 'submitted' && isset($card['submission']['uwr_status']) && $card['submission']['uwr_status'] == 'submitted') {
                        $surveyCompletedCount++;
                    }
                }
            }

            $recsRaised = 0;
            $recsClosed = 0;
            if (isset($orgdetails['rr_status']) && is_array($orgdetails['rr_status'])) {
                foreach ($orgdetails['rr_status'] as $status) {
                    $open = isset($status['open']) ? $status['open'] : 0;
                    $closed = isset($status['closed']) ? $status['closed'] : 0;

                    $stat = $open + $closed;
                    $recsRaised += $stat;
                    $recsClosed += $closed;
                }
            }

            // Get the accident report
            $accidentCompleted = json_decode(Api::get('api/v1/form/submitted_forms/accident_report/'.$id.'/'.Session::get('user')->id.'/1' . '?' . http_build_query($filters)), true);
            $acCount = 0;
            if (isset($accidentCompleted['data']) && is_array($accidentCompleted['data'])) {
                foreach ($accidentCompleted['data']  as $ac) {
                    if (isset($ac['form_name'])) { $acCount++;
                    }
                }
            }

            // Get other completed forms
            $otherCompleted = json_decode(Api::get('api/v1/form/submitted_forms/'.$id.'/'.Session::get('user')->id.'/1'), true);
            $ocCount = 0;
            if (isset($otherCompleted['data']) && is_array($otherCompleted['data'])) {
                foreach ($otherCompleted['data']  as $oc) {
                    if (isset($oc['form_name'])) { $ocCount++;
                    }
                }
            }

            // Get the statistics
            $type = 'document+access';

            // use 01-01-2014 as date from to specify the beginning time of RR as a starting point
            $date_from = "01-01-2014";

            if (!isset($date_to)) {
                $today = Carbon::today();
                $today->day= 0;
                $date_to = $today->format('d-m-Y');
            }

            $dataStats = [
                    'date_from' => $date_from,
                    'date_to' => $date_to,
                    'organisation' => $id,
                    'branch' => '',
                    'type' => $type
                ] + $filters;

            $stats = json_decode(Api::post('/api/v1/log/overview', $dataStats), true);
            if (isset($stats['response']) && $stats['response'] == "success") {
                $stats_data = array(
                    'date_from' => $date_from,
                    'date_to'   => $date_to,
                    'branch'    => null,
                    'type'      => $type,
                    'page'      => 'metric'
                );

                if (isset($stats['data'])) {
                    if (isset($stats['data']['croner_activation'])) {
                        $cronerActivation   = isset($stats['data']['croner_activation']) ? count($stats['data']['croner_activation']) : 0;
                        $safetyActivation   = isset($stats['data']['safety_activation']) ? count($stats['data']['safety_activation']) : 0;
                        $cronerDeactivation = isset($stats['data']['croner_deactivation']) ? count($stats['data']['croner_deactivation']) : 0;
                        $safetyDeactivation = isset($stats['data']['safety_deactivation']) ? count($stats['data']['safety_deactivation']) : 0;

                        $stats_data['count'] = $cronerActivation + $safetyActivation + $cronerDeactivation + $safetyDeactivation;
                        $stats_data['total_safety'] = $safetyActivation - $safetyDeactivation;
                        $stats_data['total_croner'] = $cronerActivation - $cronerDeactivation;
                    }
                    if (isset($stats['data']['client_create'])) {
                        $stats['data']['client']['client_create'] = $stats['data']['client_create'];
                    }

                    if (isset($stats['data']['client_delete'])) {
                        $stats['data']['client']['client_delete'] = $stats['data']['client_delete'];
                    }

                    if (isset($stats['data']['branch_create'])) {
                        $stats['data']['client']['branch_create'] = $stats['data']['branch_create'];
                    }

                    if (isset($stats['data']['branch_delete'])) {
                        $stats['data']['client']['branch_cbranch_deletereate'] = $stats['data']['branch_delete'];
                    }

                    $stats_data['data'] = $stats['data'];
                    $downloadStat = isset($stats_data['data'][$type]) ? count($stats_data['data'][$type]) : 0;
                }
            }

            // Get the claims MI report
            $response['data']['claimsMiCount'] = 0;
            $organisationReports = json_decode(Api::get('/api/v1/organisation/' . $id . '/reports/all?' . http_build_query($filters)), true);

            if (isset($organisationReports['data']) && count($organisationReports['data']) > 0) {
                $claimsMInumber = array_map(
                    function ($report) {
                        return $report['type'] == "Claims MI";
                    }, $organisationReports['data']
                );

                $response['data']['claimsMiCount'] = count(array_filter($claimsMInumber));
            }

            $cronerUserCount = 0;
            if (isset($response['data']['users']) && is_array($response['data']['users'])) {
                foreach ($response['data']['users'] as $user) {
                    if (isset($user['croner_access']) && $user['croner_access']) { $cronerUserCount += 1;
                    }
                }
            }

            $orgdetails['croner-user-count']    = $cronerUserCount;

            $orgdetails['category_types']       = $categoryTypes;
            $orgdetails['user-count']           = count($response['data']['users']);
            $orgdetails['surveys-completed']    = $surveyCompletedCount;
            $orgdetails['recs-raised']          = $recsRaised;
            $orgdetails['recs-closed']          = $recsClosed;
            $orgdetails['accident-completed']   = $acCount;
            $orgdetails['other-completed']      = $ocCount;
            $orgdetails['document-downloads']   = isset($downloadStat) ? $downloadStat : 0;

            return view(
                'organisation.partials.engagement-tiles', [
                'filters' => $filters,
                'organisation' => $response['data'],
                'orgdetails' => $orgdetails
                ]
            );
        }

        return view('organisation.partials.engagement-tiles', ['error' => true]);
    }
}
