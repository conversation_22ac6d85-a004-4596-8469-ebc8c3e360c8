<?php

namespace App\Http\Controllers\PortfolioViews;

use App\Http\Controllers\BaseController;
use App\Models\Api;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class InsightsController extends BaseController
{
    public $templates;

    public function __construct()
    {
        $this->templates = config('portfolio_views.templates');
    }

    public function index(Request $request)
    {
        $url = 'api/v1/portfolio-views/insights';
        $template = $request->get('template');
        if ($template) {
            $url .= "?template=" . $template; 
        }

        $insights = json_decode(Api::get($url));
        if ($request->ajax()) {
            $referer = $request->headers->get('referer');
            $withActionBtn = Str::contains($referer, 'customise');
            $html = view(
                'portfolio-views.components.modals.content-repeater',
                compact('insights', 'withActionBtn')
            )->render();
            return response()->json(compact('insights', 'html'));
        }
        
        $reportTemplates = $this->templates;
        return view('portfolio-views.index', compact('insights', 'reportTemplates'));
    }

    public function customiseIndex()
    {
        $reportTemplates = $this->templates;
        return view('portfolio-views.customise-index', compact('reportTemplates'));
    }

    public function customiseReport($templateCode)
    {
        $template = $this->templates[$templateCode];
        $reportId = request()->get('reportId');
        $reportToEdit = null;
        $isEdit = false;
        if (!empty($reportId)) {
            $reportToEdit = json_decode(Api::get('api/v1/portfolio-views/insights/' . $reportId));
            if (isset($reportToEdit->status) && $reportToEdit->status === 404) {
                return app()->abort(404, 'Insight not found.');
            }
            $isEdit = true;
            $reportToEdit = $reportToEdit->insight;
        }
        $insights = json_decode(Api::get('api/v1/portfolio-views/insights'));
        return view('portfolio-views.customise-report', compact('template', 'reportToEdit', 'isEdit', 'insights'));
    }

    public function store()
    {
        $requestData = request()->all();

        $url = 'api/v1/portfolio-views/insights';

        $reportId = request()->get('reportId');
        if ($reportId) {
            $url .= "/" . $reportId;
        }

        $apiResponse = json_decode(Api::post($url, $requestData));

        if (!empty($apiResponse->response) && $apiResponse->response == 'error') {
            return response()->json(
                [
                    'message' => (!empty($apiResponse->message))
                        ? $apiResponse->message
                        : 'An error occurred while saving insight.'
                ],
                422
            );
        }

        return response()->json($apiResponse);
    }

    public function delete($id)
    {
        $apiResponse = json_decode(Api::delete('api/v1/portfolio-views/insights/' . $id), true);
        return response()->json($apiResponse);
    }

    public function show($id)
    {
        $apiResponse = json_decode(Api::get("api/v1/portfolio-views/insights/{$id}?include_other_insights=true"));

        if (empty($apiResponse)) {
            abort(404);
        }

        $insight = isset($apiResponse->insight) ? $apiResponse->insight : [];
        $insights = isset($apiResponse->other_insights) ? $apiResponse->other_insights : [];
        $template = $this->templates[($insight->template ?? '')] ?? [];
        return view('portfolio-views.show.' . $insight->template, compact('insight', 'insights', 'template'));
    }
}
