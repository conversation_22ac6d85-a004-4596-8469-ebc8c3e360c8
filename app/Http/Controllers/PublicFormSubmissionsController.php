<?php

namespace App\Http\Controllers;

use App\Models\PublicForm;
use App\Models\User;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Request;
use App\Models\Documents;
use App\Models\FileUpload;
use Illuminate\Support\Facades\Session;
use App\Models\Api;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;

class PublicFormSubmissionsController extends BaseController
{
    const PAGE_CONTROLLER = 200;

    public function __construct(Request $request, Documents $doc, FileUpload $fileUpload)
    {
        parent::__construct($request);
        $this->documents = $doc;
        $this->files = $fileUpload;
    }

    public function index(Request $request, string $formId)
    {
        $requests = $request->all();
        $draw = $requests['draw'] ?? 1;
        $page = $this->pageNumber($requests);
        $response = json_decode(Api::get(PublicForm::PUBLIC_FORM_API_ENDPOINT . '/submissions/' . $formId . '/index?page='.$page));

        if ($response->response === 'success') {
            $submissions = json_decode($response->data);
            $recordsTotal = $response->total;

            if ($request->ajax()) {
                return [
                    'data' => $submissions,
                    'recordsTotal' => $recordsTotal,
                    'recordsFiltered' => $recordsTotal,
                    'draw' => $draw,
                ];
            }

            return view(
                'forms/submissions',
                [
                    'submissions' => $submissions,
                    'public' => true,
                    'form_id' => $formId,
                ]
            );
        }
    }

    public function allowForUser(User $user): bool
    {
        $routeName = request()->route()->getName();
        if (in_array($user->login_type, ['risk-control', 'underwriter', 'risk-engineer'])) {
            return true;
        }
        
        if ($user->login_type === 'broker-user' && in_array($routeName, config('app.allowed-routes.broker-user'))) {
            return true;
        }

        return false;
    }

    private function pageNumber($requests)
    {
        $start = $requests['start'] ?? 0;
        $length = $requests['length'] ?? $this::PAGE_CONTROLLER;
        $page =  $start / $length;

        return $page;
    }

    /*
     * List of all submissions for a form
     */
    public function submissions($form)
    {
        return view(
            'forms/submissions',
            [
                'public' => true,
                'form_id' => $form,
            ]
        );
    }

    public function ajaxSubmissions($form_id)
    {
        $response = json_decode(Api::get('api/v1/form/public/ajax-submissions/'.$form_id));
        return response()->json([
            'data' => $response->data,
        ]);
    }

    public function exportSubmissions($form)
    {  
        $currentUser = Session::get('user');
        $get = [];
        $get[] = 'first_name='.urlencode(trim($currentUser->first_name));
        $get[] = 'last_name='.urlencode(trim($currentUser->last_name));
        $get[] = 'email='.urlencode(trim($currentUser->email));

        $url=PublicForm::PUBLIC_FORM_API_ENDPOINT.'/export-submissions/'.$form.'?'.implode('&', $get);
        $response = json_decode(Api::get($url));
        
        // call API endpoint async to respond immediately to frontend
        //This block is not hitting api
        //Http::async()->get($url);

        return Response::json('OK');
    }

    /*
    * Edit a submission
    */
    public function edit(Request $request, $submission)
    {
        $data = $request->all();
        $response = json_decode(Api::put('api/v1/form-submission/'.$submission, $data));
        if ($response->response == 'success') {
            return Redirect::back();
        } else {
            die('An error occurred, please try again');
        }
    }

    public function showPdf($id)
    {
        $ispublic = true;
        $response = json_decode(Api::get(PublicForm::PUBLIC_FORM_API_ENDPOINT.'/submission/'.$id));
        $submission = json_decode($response->data);

        $submission->json = $response->data;
        $response = json_decode(Api::get(PublicForm::PUBLIC_FORM_API_ENDPOINT.'/'.$submission->form_id));

        if ($response->response == 'success') {
            $form = json_decode($response->data, true);
            //print_r($form);exit;
            $form['fields'] = json_encode($form['fields']);
            $user = Session::has('user') ? Session::get('user') : null;
            $branch_image = '';

            if (Session::has('org_logo') && !empty(Session::get('org_logo'))) {
                $branch_image = $this->file->link(Session::get('org_logo'));
            }

            if ($user && isset($user->file) && !empty($user->file)) {
                $branch_image = $this->file->link($user->file);
            }

            $data = [
                'form' => $form,
                'submission' => $submission,
                'branch_image' => $branch_image,
                'ispublic' => $ispublic,
            ];

            return view('submissions/public_show_print', $data);
        } else {
            die('An error occurred, please try again');
        }
    }

    public function printPdf(Request $request)
    {
        $html = stripslashes($request->get('html'));
        $orientation = $request->get('orientation');
        $pdf = Pdf::loadHTML($html)->setPaper('a4', $orientation);

        return $pdf->stream();
    }

    /*
    * update a form submission
    */
    public function update($submissionID)
    {
        // submit
    }

    /*
    * view a submission
    */
    public function show($submission)
    {
        $response = json_decode(Api::get('api/v1/form/public/submission/'.$submission));
        $submission = json_decode($response->data);
        $submission->json = $response->data;

//        $cacheEndpoint = 'api/v1/form/public/'.$submission->form_id;
//        $cacheResponse = CacheServiceFacade::get('show-submission-'.$submission->form_id, $cacheEndpoint, 'get');
//        $response = json_decode($cacheResponse->get());

        $response = json_decode(Api::get('api/v1/form/public/'.$submission->form_id));
        if ($response->response == 'success') {
            $form = json_decode($response->data, true);
            $branch = [];
            $branch_options = '';
            if ($form['formType'] == 'accident-reporting') {
                foreach ($form['fields'] as $field) {
                    foreach ($field as $key => $value) {
                        if ($key == 'branch') {
                            $string_val = 'branch-where-accident-happened';
                            $branches = json_decode(Api::get('/api/v1/user/org-branch/'.$submission->organisation_id));
                            if ($branches->response == 'success') {
                                $branchUsers = $branches->data;

                                foreach ($branchUsers as $branchUser) {
                                    $branch[$branchUser->id] = $branchUser->branch_name;
                                    if (isset($submission->$string_val) && $submission->$string_val == $branchUser->id) {
                                        $branch_options .= '<option value = \''.$branchUser->id.'\' selected>'.$branchUser->branch_name.'</option>';
                                    } else {
                                        $branch_options .= '<option value = \''.$branchUser->id.'\'>'.$branchUser->branch_name.'</option>';
                                    }
                                }
                            }
                        }
                    }
                }
            }
            $form['fields'] = json_encode($form['fields']);

            if(Cache::has("submission-details-".$submission->uuid)) {
                $submission_details = Cache::get("submission-details-".$submission->uuid);
            }else{
                $submission_details = json_decode(Api::get('api/v1/form/public/submission/form/'.$submission->uuid));
                Cache::put("submission-details-".$submission->uuid, $response, 1000);
            }
            if (isset($submission_details->response) && !empty($submission_details->response) && $submission_details->response == 'success') {
                if (isset($submission_details->data->organisation->name)) {
                    $submission_details_data = [
                        'organisation_name' => $submission_details->data->organisation->name,
                    ];
                } else {
                    $submission_details_data = [
                        'organisation_name' => 'Deleted Organisation',
                    ];
                }
            } else {
                $submission_details_data = [
                    'organisation_name' => '',
                ];
            }

            if (isset($submission->organisation_name)) {
                $submission_details_data = [
                    'organisation_name' => $submission->organisation_name,
                ];
            }

            //overwriting organisation name

            if (isset($submission->organisation->name)) {
                $submission_details_data = [
                    'organisation_name' => $submission->organisation->name,
                ];
            }

            return view(
                'submissions/show',
                [
                    'form' => $form,
                    'submission' => $submission,
                    'branches' => $branch,
                    'branch_options' => $branch_options,
                    'submission_details' => $submission_details_data,
                    'public' => true,
                ]
            );
        } else {
            die('An error occurred, please try again');
        }

        /*$response = json_decode( Api::get('api/v1/form-submission/'.$submission) );
        $submission = json_decode($response->data);
        $form = json_decode(json_decode( Api::get('api/v1/form/'.$submission->form_id) )->data, true);
        $fields = [] ;
        foreach($form['fields'] as $k => $f)
        {
            $fields[$f[key($f)][1]['value']] = $f[key($f)][0]['value'] ;
        }
        $submissionId = $submission->_id ;
        unset($submission->form_id, $submission->_id) ;
        if($response->response == "success")
        {
            return view(
              'submissions/show',
              [
                'fields' => $fields,
                'submission' => $submission,
                'submissionId' => $submissionId
            ]
        );
        }
        else
        {
          die('An error occurred, please try again') ;
        }*/
    }

    public function previewSubmission($map_id)
    {
        $url = 'api/v1/forms-mapping-submission/'.$map_id;
        $response = json_decode(Api::get($url));
        $submission_id = '';
        if ($response->response == 'success') {
            $submission_id = $response->data->submission_id;
        }

        return $this->show($submission_id);
    }

    /*
    * Delete a submission
    */
    public function delete($submission)
    {
        $response = json_decode(Api::delete('api/v1/form/public/submission/'.$submission));
        if ($response->response == 'success') {
            return Redirect::back();
        }

        die('An error occurred, please try again');
    }

    /*
     * GET: get attachments from rackspace
     */

    public function retrieveAttachment($name)
    {
        $document = json_decode(Api::get('api/v1/attachment/public/find/'.$name));

        if ($document->response == 'success') {
            $fileName = $document->submission->organisation_id.'/'.$document->data->document_store_name;
            $file = $this->documents->download($fileName, $name);
            if ($file['response'] == 'success') {
                $decrypt = $this->documents->decrypt($file['data'], $name);
            } else {
                return Response::json(['response' => 'error', 'message' => $file['message']]);
            }
            $fileName = $document->data->document_title;

            return Response::download($decrypt['data'], $fileName, ['Content-Type' => 'text/plain']);
        }
    }

    public function verify(Request $request)
    {
        $data = $request->all('id');
        $route = sprintf(
            'api/v1/form/public/submissions/%s/verify/%s/%s',
            $data['id'],
            Auth::id(),
            'admin'
        );
        $response = json_decode(Api::put($route));

        if ($response->response == 'success') {
            //temporary fix
            $this->rebuildPublicFormSubmissionsCache($data['id']);
            return Response::json($response);
        } else {
            die('An error occurred, please try again');
        }
    }

    public function rebuildPublicFormSubmissionsCache($formid)
    {
        $response = json_decode(Api::get(PublicForm::PUBLIC_FORM_API_ENDPOINT.'/submissions/'.$formid));
        Cache::put("public-submissions-".$formid, $response, 1000);
    }
}
