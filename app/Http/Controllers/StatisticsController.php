<?php

namespace App\Http\Controllers;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use App\Models\Api;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\ActivationsExport;
class StatisticsController extends BaseController
{
    const TEMPLATE_DIRECTORY = 'statistics';

    public $colors = [
        0 =>['#445E8C', '#46BFBD', '#FDB45C'],
        1 =>['#576E98', '#5AD3D1', '#FFC870']
    ] ;

    public function __construct(Request $request, Api $api)
    {
        parent::__construct($request);
        $this->api = $api;
    }

    public function allowForUser(User $user): bool
    {
        return $user->login_type === 'risk-control' || $user?->isRoleAdminOrAccountManager();
    }


    public function index(Request $request)
    {
        $date_from = $request->get('date_from');
        $date_to = $request->Get('date_to');
        $organisation = $request->get('organisation');
        $branch = $request->get('branch');
        //$response = $this->stats();

        if(!isset($date_from)) {
            $today = Carbon::today();
            $today->day = 1;
            $today->subMonth(1);
            $date_from = $today->format('d-m-Y');
        }

        if(!isset($date_to)) {
            $today = Carbon::today();
            $today->day= 0;
            $date_to = $today->format('d-m-Y');
        }

        // if($response->response == "success")
        // {
            return view(
                static::TEMPLATE_DIRECTORY . '/index',
                array(
                    'date_from'     =>  $date_from,
                    'date_to'       =>  $date_to,
                    'branch'        =>  $branch,
                    'page'          => 'index',
                    'type'          => ''
                )
            );
        // }
    }

    public function indexReset()
    {
        return Redirect::route('statistics.index');
    }

    public function typeReset($type)
    {
        return Redirect::route('statistics.metric', array('type' => $type));
    }

    public function organisationReset($type,$organisation)
    {
        return Redirect::route('statistics.organisation', array('type' => $type,'organisation' => $organisation));
    }

    public function metric(Request $request,$type)
    {
        $date_from = $request->get('date_from');
        $date_to = $request->get('date_to');
        $organisation = $request->get('organisation');
        $branch = $request->get('branch');


        $response = $this->stats($request, $type);
        if(!isset($date_from)) {
            $today = Carbon::today();
            $today->day = 1;
            $today->subMonth(1);
            $date_from = $today->format('d-m-Y');
        }

        if(!isset($date_to)) {
            $today = Carbon::today();
            $today->day= 0;
            $date_to = $today->format('d-m-Y');
        }

        if($response->response == "success") {
            $stats_data = array(
                'date_from'     =>  $date_from,
                'date_to'       =>  $date_to,
                'branch'        =>  $branch,
                'type'          => $type,
                'page'          => 'metric'
            );

            if(isset($response->data->croner_activation)) {
                $response->data->activations['croner_activation'] = $response->data->croner_activation;
                $response->data->activations['safety_activation'] = $response->data->safety_activation;
                $response->data->activations['croner_deactivation'] = $response->data->croner_deactivation;
                $response->data->activations['safety_deactivation'] = $response->data->safety_deactivation;
                $count = 0;
                foreach($response->data->activations as $key => $result)
                {
                    $count += count($result);
                }
                $stats_data['count'] = $count;
                $stats_data['total_safety'] = count($response->data->activations['safety_activation']) - count($response->data->activations['safety_deactivation']);
                $stats_data['total_croner'] = count($response->data->activations['croner_activation']) - count($response->data->activations['croner_deactivation']);
            }

            if(isset($response->data->client_create)) {
                $response->data->client['client_create'] = $response->data->client_create;
                $response->data->client['client_delete'] = $response->data->client_delete;
                $response->data->client['branch_create'] = $response->data->branch_create;
                $response->data->client['branch_delete'] = $response->data->branch_delete;
            }
            $stats_data['data'] = $response->data;
            return view(
                static::TEMPLATE_DIRECTORY . '/metric',
                $stats_data
            );
        }
    }

    public function page(Request $request,$type,$page)
    {
        $date_from = $request->get('date_from');
        $date_to = $request->Get('date_to');
        $organisation = $request->get('organisation');
        $branch = $request->get('branch');

        if(!isset($date_from)) {
            $today = Carbon::today();
            $today->day = 1;
            $today->subMonth(1);
            $date_from = $today->format('d-m-Y');
        }

        if(!isset($date_to)) {
            $today = Carbon::today();
            $today->day= 0;
            $date_to = $today->format('d-m-Y');
        }

        $data = array('date_from' => $date_from,'date_to' => $date_to,'organisation' => $organisation, 'branch' => $branch,'page' => $page);
        $response = json_decode($this->api->post('/api/v1/log/pages', $data));
        if($response->response == "success") {
            $pages = $response->data;
            foreach($pages as $key => $single)
            {
                if(strtoupper($page) == "NEWS") {

                }
            }
        }
    }

    public function organisation(Request $request, $type, $organisation_name)
    {
        $date_from = $request->get('date_from');
        $date_to = $request->Get('date_to');
        $branch = $request->get('branch');

        if(!isset($date_from)) {
            $today = Carbon::today();
            $today->day = 1;
            $today->subMonth(1);
            $date_from = $today->format('d-m-Y');
        }

        if(!isset($date_to)) {
            $today = Carbon::today();
            $today->day= 0;
            $date_to = $today->format('d-m-Y');
        }

        $organisation = json_decode($this->api->get('/api/v1/organisation_name/'. $organisation_name));
        $data = array();
        if($organisation->response == "success") {
            $stats = $this->stats($request, $type, $organisation->data->id);
            if(isset($stats->data->$type)) {
                foreach($stats->data->$type as $key => $stat)
                {

                    if($stat->organisation->id == $organisation->data->id) {
                        array_push($data, $stat);
                    }
                }

                //get dates
                $labels = array();
                $points = array();
                foreach($data as $point)
                {
                    $date = Carbon::createFromFormat('Y-m-d H:i:s', new Carbon($point->created_at));
                    if(!is_bool($date)) {
                        if(!in_array($date->format('D, d M Y'), $labels)) {
                            array_push($labels, $date->format('D, d M Y'));
                        }
                    }

                    if(!array_key_exists($date->format('D, d M Y'), $points)) {
                        $points[$date->format('D, d M Y')] = 1;
                    }
                    else
                    {
                        $points[$date->format('D, d M Y')] ++;
                    }
                }


                return view(
                    static::TEMPLATE_DIRECTORY . '/organisation',
                    array(
                        'date_from'     =>  $date_from,
                        'date_to'       =>  $date_to,
                        'branch'        =>  $branch,
                        'data'          =>  $data,
                        'type'          =>  $type,
                        'page'          =>  'organisation',
                        'organisation'  =>  $organisation->data
                    )
                );
            }
            return Redirect::route('statistics.metric', array('type' => $type, 'date_from' => $date_from, 'date_to' => $date_to, 'branch' => $branch))
                ->with('error', 'No Data to show');


        }

        return Redirect::route('statistics.metric', array('type' => $type,'date_from' => $request->get('date_from'), 'date_to' => $request->Get('date_to')))
            ->with('error', 'Organisation does not exist');
    }

    public function access(Request $request,$type,$access)
    {
        $date_from = $request->get('date_from');
        $date_to = $request->Get('date_to');
        $branch = $request->get('branch');

        if(!isset($date_from)) {
            $today = Carbon::today();
            $today->day = 1;
            $today->subMonth(1);
            $date_from = $today->format('d-m-Y');
        }

        if(!isset($date_to)) {
            $today = Carbon::today();
            $today->day= 0;
            $date_to = $today->format('d-m-Y');
        }
        $response = $this->stats($request,$type);
        if($response->response == "success") {
            $response = $response->data;
            $data = array();
            foreach($response as $key => $metrics)
            {
                foreach($metrics as $stat)
                {
                    if($stat->content_type == urldecode($access)) {
                        array_push($data, $stat);
                    }
                }
            }
            $organisations = array();
            foreach($data as $stat)
            {
                if(!array_key_exists($stat->organisation->name, $organisations)) {
                    $organisations[$stat->organisation->name] = array();
                }
                array_push($organisations[$stat->organisation->name], $stat);
            }
            return view(
                static::TEMPLATE_DIRECTORY . '/access',
                array(
                    'date_from'     =>  $date_from,
                    'date_to'       =>  $date_to,
                    'branch'        =>  $branch,
                    'data'          =>  $data,
                    'type'          => $type,
                    'page'          => 'metric',
                    'organisations' =>  $organisations
                )
            );
        }
    }



    public function documents(Request $request,$type, $organisation_name)
    {
        $date_from = $request->get('date_from');
        $date_to = $request->Get('date_to');
        $branch = $request->get('branch');

        if(!isset($date_from)) {
            $today = Carbon::today();
            $today->day = 1;
            $today->subMonth(1);
            $date_from = $today->format('d-m-Y');
        }

        if(!isset($date_to)) {
            $today = Carbon::today();
            $today->day= 0;
            $date_to = $today->format('d-m-Y');
        }

        $organisation = json_decode($this->api->get('/api/v1/organisation_name/'. $organisation_name));
        $data = array();
        if($organisation->response == "success") {
            $documents = json_decode($this->api->get('/api/v1/document/organisation/'.$organisation->data->id));
            $documents_id = array();
            foreach($documents->data as $document)
            {
                array_push($documents_id, $document->id);
            }

            $docs = array();
            $stats = $this->stats($request, $organisation->data->id);
            if(isset($stats->data->$type)) {
                foreach($stats->data->$type as $key => $document)
                {
                    if(in_array($document->content_id, $documents_id)) {
                        foreach($documents->data as $current)
                        {
                            if($current->id == $document->content_id) {
                                if(!array_key_exists($document->content_id, $docs)) {
                                    $docs[$document->content_id]['count'] = 1;
                                    $docs[$document->content_id]['data'] = $current;
                                    $docs[$document->content_id]['level1'] = $document->level1;
                                    $docs[$document->content_id]['level2'] = $document->level2;
                                    $docs[$document->content_id]['level3'] = $document->level3;
                                }
                                else{
                                    $docs[$document->content_id]['count'] ++;
                                    $docs[$document->content_id]['data'] = $current;
                                    $docs[$document->content_id]['level1'] = $document->level1;
                                    $docs[$document->content_id]['level2'] = $document->level2;
                                    $docs[$document->content_id]['level3'] = $document->level3;
                                }
                            }
                        }
                    }
                }


                return view(
                    static::TEMPLATE_DIRECTORY . '/document',
                    array(
                        'date_from'     =>  $date_from,
                        'date_to'       =>  $date_to,
                        'branch'        =>  $branch,
                        'data'          =>  $data,
                        'type'          =>  $type,
                        'page'          =>  'document',
                        'organisation'  =>  $organisation->data,
                        'documents'     =>  $docs
                    )
                );
            }

            return Redirect::route('statistics.metric', array('type' => $type,'date_from' => $date_from, 'date_to' => $date_to, 'branch' => $branch))
                ->with('error', 'No data to show');
        }

        return Redirect::route('statistics.metric', array('type' => $type,'date_from' => $date_from, 'date_to' => $date_to, 'branch' => $branch))
            ->with('error', 'Organisation does not exist');
    }




    public function licenses()
    {
        $response = json_decode($this->api->get('/api/v1/user/licenses'));
        if($response->response == "success") {
            $organisations = $response->data;
            $total_safety = 0;
            $total_croner = 0;
            foreach($organisations as $org)
            {
                $total_croner += $org->croner;
                $total_safety += $org->safetymedia;
            }
            return view(
                static::TEMPLATE_DIRECTORY . '/activations',
                array(
                    'organisations' => $organisations,
                    'total_safety'  => $total_safety,
                    'total_croner'  => $total_croner
                )
            );
        }
    }


    public function export(Request $request)
    {
        $data = $request->all();
        $date_from = $request->get('date_from');
        $date_to = $request->Get('date_to');
        $branch = $request->get('branch');

        if(!isset($date_from)) {
            $today = Carbon::today();
            $today->day = 1;
            $today->subMonth(1);
            $date_from = $today->format('d-m-Y');
        }

        if(!isset($date_to)) {
            $today = Carbon::today();
            $today->day= 0;
            $date_to = $today->format('d-m-Y');
        }

        $data = array('date_from' => $date_from, 'date_to' => $date_to,'branch' => $branch);
        $response = json_decode($this->api->post('/api/v1/log/activations', $data));

        //print_r($response); exit;

        if($response->response =="success") {
            $types = $response->data;

            if(count((array)$types) > 0) {
                foreach($types as $type => $collection)
                {
                    foreach($collection as $result)
                    {

                        if(!property_exists($result, 'organisation')) {
                            $result->organisation = new \StdClass();
                            $result->organisation->name = 'Deleted organisation';
                            $organisation[$result->organisation->name][$type] = array('type' => $type,'count' => 1);
                        }
                        elseif(!isset($organisation[$result->organisation->name][$type])) {
                            //print_r($result); exit;
                            $organisation[$result->organisation->name][$type] = array('type' => $type,'count' => 1);
                        } else {
                        }
                        $organisation[$result->organisation->name][$type]['count'] ++;
                        // print_r($result);
                        // print_r('<br>');
                    }
                }

                $data = array();
                array_push($data, array('Organisation Name','Croner Activations','Croner Deactivations','Safety Media Activations','Safety Media Deactivations'));

                foreach($organisation as $organisation_name => $result)
                {
                    $push = array($organisation_name,
                        isset($result['croner_activation']['count']) ? $result['croner_activation']['count'] : '0' ,
                        isset($result['croner_deactivation']['count']) ? $result['croner_deactivation']['count'] : '0',
                        isset($result['safety_activation']['count'])? $result['safety_activation']['count'] : '0',
                        isset($result['safety_deactivation']['count']) ? $result['safety_deactivation']['count'] : '0' );
                    array_push($data, $push);

                }
                
                return Excel::download(new ActivationsExport($data),'activations-'.$date_from.'-'.$date_to.'.xlsx');
            }

        }

    }


    /**
     * @param  int $organisation_id
     * @return array|mixed
     */
    public function stats(Request $request, $type = '', $organisation_id = 0)
    {
        $date_from = $request->get('date_from');
        $date_to = $request->Get('date_to');
        $organisation = $organisation_id != 0 ? $organisation_id : $request->get('organisation');
        $branch = $request->get('branch');


        if(!isset($date_from)) {
            $today = Carbon::today();
            $today->day = 1;
            $today->subMonth(1);
            $date_from = $today->format('d-m-Y');
        }

        if(!isset($date_to)) {
            $today = Carbon::today();
            $today->day= 0;
            $date_to = $today->format('d-m-Y');
        }

        $data = array('date_from' => $date_from, 'date_to' => $date_to, 'organisation' => $organisation, 'branch' => $branch);
        if($type != '') {
            $data['type'] = $type;
        }
        
        return json_decode($this->api->post('/api/v1/log/overview', $data));
    }

}
