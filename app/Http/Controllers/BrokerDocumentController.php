<?php

namespace App\Http\Controllers;
class BrokerDocumentController extends BaseController
{
    public function showDocuments()
    {
        $url = 'https://www.libertycms.dev/api/workspaces/5d498d6f08936701996cc232/content-types/5e85ca4dd293dc52dd426f02/content-entries?query={"status":"publish","operator":"=","order":{"publishing_date_1586246201110":"desc","updated_at":"desc"}}';

        $ch = curl_init($url);

        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt(
            $ch, CURLOPT_HTTPHEADER, array(
            'X-LSMCMS-API-KEY: 3b9a73b2-61da-4c5a-be40-2d272eb89000',
            )
        );

        $response = curl_exec($ch);
        $result = json_decode($response);
        $results = isset($result->data) ? $result->data : [];

        return view(
            'documents', [
            'documents' => $results,
            ]
        );
    }
}
