<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Str;
use App\Models\Api;
use Barryvdh\DomPDF\Facade\Pdf;
class RhsOrderController extends BaseController
{
    const TEMPLATE_PATH = '/affiliates/rhs';

    public function __construct(Request $request)
    {
        parent::__construct($request);

        if (Session::get('user')->login_type != 'risk-control' && !Session::get('user')?->isRoleAdminOrAccountManager()) {
            exit;
        }
    }

    public function create()
    {

    }

    public function store(Request $request)
    {
        $orderData = json_encode($request->all());

        $response = json_decode(Api::get('/api/v1/rhs/submit/customer/order/' . $orderData));

        $template_type = ( $response->customer->customer_type_id == 1 ) ? 'pdf-rhs' : 'pdf-bloom';

        $path = storage_path().'/rhs';

        $pdf = PDF::loadView(
            static::TEMPLATE_PATH . '/' .$template_type, [
            'affiliates' => $response->document,
            'customers' => $response->customer,
            ]
        );

        $uuid = Str::uuid()->toString();

        $pdf->save($path);

        $uploadResult = $this->files->upload($path, $response->document->uuid, $request->get('organisation_id'));

        $imagedata = file_get_contents($path);

        $orderData = json_decode($orderData);
        foreach( $orderData as $key => $value )
        {
            $request[$key] = $value;
        }

        $request['pdf_data'] = base64_encode($imagedata);
        $request['has_insurance'] = true;
        $request['order_id'] = $response->document->order_id;

        // generate email + attachment
        $json_test = json_decode(Api::post('/api/v1/rhs-organisations/document/info', $request));

        $pdf->save($binder_path);
        return Redirect::to('/rhs/organisations');
    }

    public function show($customer_id, $order_id)
    {
        $customer = json_decode(Api::get('/api/v1/rhs/customers/' . $customer_id))->data;

        if (! $customer) {
            return Redirect::to('/rhs/organisations')->with('error', 'Customer not found');
        }

        $customerType = $customer->customer_type_id;

        $productCategories = json_decode(Api::get('/api/v1/rhs/products/categories?customer_type_id=' . $customerType))->data;
        $productDescriptions = json_decode(Api::get('/api/v1/rhs/products/descriptions?customer_type_id=' . $customerType))->data;
        $products = json_decode(Api::get('/api/v1/rhs/products?customer_type_id=' . $customerType))->data;
        $customerOrder = json_decode(Api::get('/api/v1/rhs/get/customer/order/' . $order_id))->data;

        return view(
            'rhs/orders/show', [
            'customer' => $customer,
            'productCategories' => $productCategories,
            'productDescriptions' => $productDescriptions,
            'customerOrder' => $customerOrder,
            'products' => $products
            ]
        );
    }

    public function edit($customer_id, $order_id)
    {
        $customer = json_decode(Api::get('/api/v1/rhs/customers/' . $customer_id))->data;

        if (! $customer) {
            return Redirect::to('/rhs/customers')->with('error', 'Customer not found');
        }

        if ($order_id > 0) {
            $customerOrder = json_decode(Api::get('/api/v1/rhs/get/customer/order/' . $order_id))->data;
        }  else {
            $customerOrder = false;
        }

        $customerType = $customer->customer_type_id;

        $productCategories = json_decode(Api::get('/api/v1/rhs/products/categories?customer_type_id=' . $customerType))->data;
        $productDescriptions = json_decode(Api::get('/api/v1/rhs/products/descriptions?customer_type_id=' . $customerType))->data;
        $products = json_decode(Api::get('/api/v1/rhs/products?customer_type_id=' . $customerType))->data;

        return view(
            'rhs/orders/edit', [
            'customer' => $customer,
            'productCategories' => $productCategories,
            'productDescriptions' => $productDescriptions,
            'customerOrder' => $customerOrder,
            'products' => $products
            ]
        );
    }

    public function update($order_id)
    {
        //
    }
}
