<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Documents;
use App\Models\FileUpload;
use App\Models\Api;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
class DocumentController extends BaseController
{
    const TEMPLATE_DIRECTORY = '/document';

    const ROUTE_PREFIX = 'organisation.document';

    public function __construct(Request $request, Documents $doc, FileUpload $fileUpload)
    {
        parent::__construct($request);

        $this->documents = $doc;
        $this->files = $fileUpload;
    }

    /**
     * GET: Create Risk Guidance doc upload
     */
    public function create($id)
    {

        $sectors = json_decode(Api::get('/api/v1/sector/all'));
        $covers = json_decode(Api::Get('/api/v1/cover/all'));
        $level1 = json_decode(Api::Get('/api/v1/doc_level/null/1'));
        $organisation = json_decode(Api::Get('/api/v1/organisation/' . $id));

        return view(static ::TEMPLATE_DIRECTORY . '/create', array('organisation' => $organisation->data, 'sectors' => $sectors->data, 'level1' => $level1->data));
    }

    /**
     * GET: Create Loss Lessons doc upload
     */
    public function create_loss_lessons($id)
    {
        $sectors = json_decode(Api::get('/api/v1/sector/all'));
        $covers = json_decode(Api::Get('/api/v1/cover/all'));

        return view(static ::TEMPLATE_DIRECTORY . '/create_loss_lessons', array('organisation' => $id, 'sectors' => $sectors->data, 'covers' => $covers->data));
    }

    /**
     * POST: Store
     * WIll upload to rackspace and store the name of the file within DB
     */
    public function store(Request $request, $id)
    {
        $rules = [
        'name' => 'required',
        'description' => 'required',
        'sector' => 'required',
        'document' => 'required|max:20480|mimes:pdf,docx,doc,xlsx,xls,ppt,pptx,wmv,video/x-ms-asf,txt',
        'document_image' => 'required_if:featured,on|image|required_if:recommended,on' ];

        $isPolicyDocument = false;

        if ($request->file('document') !== null) {
            if ($request->file('document')->getClientOriginalExtension() == 'wmv') {
                unset($rules['document']);
            }
        }

        if ($request->has('policy_doc_types')) {
            $isPolicyDocument = true;
            unset($rules['sector']);
            unset($rules['description']);
            unset($rules['document_image']);
            unset($rules['document']);
            $rules['document_type_id'] = 'required';
        }

        if ($request->has('document_id')) {
            unset($rules['document']);
        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            if ($isPolicyDocument) {
                return Redirect::route(static ::ROUTE_PREFIX . '.policy_doc', array('id' => $id))->withInput($request->old())->withErrors($validator->errors());
            }

            return Redirect::route(static ::ROUTE_PREFIX . '.create', array('id' => $id))->withInput($request->old())->withErrors($validator->errors());
        }

        $data = $request->except(array('_token', 'policy_doc_types'));

        if (isset($data['featured'])) {
            $data['featured'] = true;
        }
        if (isset($data['recommended'])) {
            $data['recommended'] = true;
        }

        //Upload Document Image
        if ($request->has('document_image') && $request->hasFile('document_image')) {
            $file = $request->file('document_image');
            $name = Str::uuid()->toString();
            if (!is_bool($this->files->upload($file->getRealPath(), $name->string))) {
                return Redirect::back()->with('error', 'Failed to uploaded document image')->withInput($request->old());
            }

            $data['img_path'] = $name->string;
            unset($data['document_image']);
        }

        if ($request->hasFile('document')) {
            $file = $request->file('document');
            $uuid = Str::uuid()->toString();
            $fileName = strtolower(preg_replace('/[\s_]|^%20/', '-', $request->file('document')->getClientOriginalName()));

            $upload = $this->documents->upload($file, $uuid, $id);

            if ($upload['response'] == 'success') {
                $data['document_title'] = $fileName;
                $data['document_store_name'] = $uuid;
            }
            else {
                return Redirect::back()->with('error', $upload['message'])->withInput($request->old());
            }
        }

        if (!$isPolicyDocument) {
            $data['user_id'] = Session::get('user')->id;
            $data['type'] = 'admin';
        }

        $response = json_decode(Api::post('/api/v1/document/store', $data));

        if (isset($response)) {

            if ($response->response == 'success') {

                if ($isPolicyDocument) {
                    return Redirect::route('policy.documents.all', array('id' => $id))->with('success', 'Policy document updated successfully');
                }

                return Redirect::route('organisation.show', array('id' => $id))->with('success', 'Document created successfully');
            }
            else {
                if (!$isPolicyDocument) {
                    return Redirect::route('organisation.document.create', array('id' => $id))->withInput($request->old())->with('error', $response->message);
                }
            }
        }

        return Redirect::route('organisation.show', array('id' => $id))->with('error', 'Document not created');
    }

    /**
     * get link from rackspace
     *
     * @param $id
     * @param $name
     *
     * @return mixed
     */
    public function retrieve($id, $name)
    {

        //get file name
        $document = json_decode(Api::get('/api/v1/document/find/' . $name));

        if ($document->response == 'success') {
            $fileName = $id . '/' . $document->data->document_store_name;
            $file = $this->documents->download($fileName, $document->data->document_store_name);
            if ($file['response'] == 'success') {
                if(strtotime($document->data->created_at)-1480467600 > 0) {
                    $response = Response::download($file['data'], $document->data->document_title, array('Content-Type' => 'text/plain'));
                } else {
                    $decrypt = $this->documents->decrypt($file['data'], $document->data->document_store_name);
                    $response = Response::download($decrypt['data'], $document->data->document_title, array('Content-Type' => 'text/plain'));
                }

            }
            else {
                return Response::json(['response' => 'error', 'message' => $file['message']]);
            }


            return $response;



        }
    }

    /**
     * Delete File from rackspace and database
     */
    public function destroy($id, $docID)
    {
        $document = json_decode(Api::get('/api/v1/document/find/' . $docID));
        if ($document->response == "success") {
            $fileName = $id . '/' . $document->data->document_store_name;

            $delete = $this->documents->destroy($fileName);
            if ($delete['response'] == 'success') {

                //delete from server
                $response = json_decode(Api::post('/api/v1/document/destroy/' . $docID));
                if ($response->response == 'success') {
                    return Redirect::route('organisation.show', array('id' => $id))->with('success', 'Document Deleted');
                }
                else {
                    return Redirect::route('organisation.show', array('id' => $id))->with('error', 'Cannot find file');
                }
            }
            else {

                //delete from server
                $response = json_decode(Api::post('/api/v1/document/destroy/' . $docID));
                if ($response->response == 'success') {
                    return Redirect::route('organisation.show', array('id' => $id))->with('success', 'Document Deleted');
                }
                else {
                    return Redirect::route('organisation.show', array('id' => $id))->with('error', 'Cannot find file');
                }
            }
        }
        else {
            return Redirect::route('organisation.show', array('id' => $id))->with('error', 'Cannot find file');
        }
    }

    public function get_level($level, $parent)
    {
        $levels = Cache::get('/api/v1/doc_level/null/' . $level . '/' . $parent);

        if ($levels === null) {

            //print_r('/api/v1/doc_level/null/'.$level.'/'.$parent);exit;
            $levels = json_decode(Api::Get('/api/v1/doc_level/null/' . $level . '/' . $parent));
            $result = Cache::add('/api/v1/doc_level/null/' . $level . '/' . $parent, $levels, '1440');
        }

        //Cache::flush();

        if ($levels->response === 'success') {
            $html_string = '';
            foreach ($levels->data as $current_level) {
                $html_string.= '<option value="' . $current_level->level_id . '">' . $current_level->level_name . '</option>';
            }
            return json_encode(['response' => 'success', 'html_data' => $html_string]);
        }
    }

    /*
     * GET: Edit document
    */

    public function edit($id, $docID)
    {
        $document = json_decode(Api::get('/api/v1/document/find/' . $docID));

        if ($document->response == 'success') {

            //get sectors and covers
            $covers = json_decode(Api::Get('/api/v1/cover/all'));
            $organisation = json_decode(Api::Get('/api/v1/organisation/' . $id));
            $image = null;
            if (isset($document->data->img_path)) {
                $image = $this->files->link($document->data->img_path);
            }
            $level1 = json_decode(Api::Get('/api/v1/doc_level/null/1'));
            if (isset($document->data->img_path)) {
                $image = $this->files->link($document->data->img_path);
            }
            if (!is_null($document->data->level4_type)) {
                $doc_levels = json_decode(Api::Get('/api/v1/doc_levels_all/' . $document->data->level1_type . '/' . $document->data->level2_type . '/' . $document->data->level3_type . '/' . $document->data->level4_type));
                $doc_levels = $doc_levels->data;
            }
            else {
                $doc_levels = [];
            }

            return view(
                static ::TEMPLATE_DIRECTORY . '/edit', array(
                'document' => $document->data,
                'covers' => $covers->data,
                'organisation' => $organisation->data,
                'image' => $image,
                'level1' => $level1->data,
                'levels' => $doc_levels)
            );
        }
        else {
            return Redirect::route('organisation.show', array('id' => $id))->with('error', 'Cannot find file');
        }
    }

    /**
     * POST: Update document
     * If a new document has not been selected then update just DB,
     * If there is a new document, delete the old one and upload a new one
     */
    public function update(Request $request, $id)
    {
        $rules = ['name' => 'required', 'description' => 'required', 'document_image' => 'required_if:featured,on|image|required_if:recommended,on', ];
        if ($request->file('document') !== null) {
            if ($request->file('document')->getClientOriginalExtension() == 'wmv') {
                unset($rules['document']);
            }
        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return Redirect::route(
                'organisation.document.edit', array(
                'id' => $id,
                'docID' => $request->get('id'))
            )
            ->withInput($request->old())
            ->withErrors($validator->errors());
        }
        else {
            $document = $request->file('document');
            $data = $request->except('_token');

            if (isset($data['featured'])) {
                $data['featured'] = true;
            }

            if (isset($data['recommended'])) {
                $data['recommended'] = true;
            }

            //Upload Document Image
            if ($request->hasFile('document_image')) {
                $file = $request->file('document_image');
                $name = Str::uuid()->toString();
                if (!is_bool($this->files->upload($file->getRealPath(), $name->string))) {
                    return Redirect::back()->with('error', 'Failed to upload document image')->withInput($request->old());
                }

                $this->files->delete($data['old_document_image']);

                $data['img_path'] = $name->string;
                unset($data['document_image']);
            }
            if (isset($document)) {

                //delete old document and upload new one
                $docID = $request->get('id');
                $oldDocument = json_decode(Api::get('/api/v1/document/find/' . $docID));
                if ($oldDocument->response == 'success') {
                    $oldDocumentName = $id . '/' . $oldDocument->data->document_store_name;
                    $uuid = Str::uuid()->toString();
                    $upload = $this->documents->update($oldDocumentName, $document, $uuid, $id);
                    if ($upload['response'] == 'success') {
                        $fileName = strtolower(preg_replace('/[\s_]|^%20/', '-', $request->file('document')->getClientOriginalName()));
                        $data['document_title'] = $fileName;
                        $data['document_store_name'] = $uuid;
                    }
                }
            }

            $data['sector'] = implode(',', $data['sector']);
            $response = json_decode(Api::post('/api/v1/document/update', $data));

            if ($response->response == 'success') {
                return Redirect::route('organisation.show', array('id' => $id))->with('success', 'Document updated');
            }
            else
            {
                return Redirect::route('organisation.document.edit', array('id' => $id, 'docID' => $data['id']))
                    ->with('error', $response->message)
                    ->withInput($request->old());
            }
        }
    }
}
