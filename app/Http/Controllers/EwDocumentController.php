<?php

namespace App\Http\Controllers;
use App\Models\Api;
use App\Models\Documents;
use App\Models\FileUpload;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
class EwDocumentController extends BaseController
{

    public function __construct(Request $request, Documents $docs, FileUpload $fileUpload)
    {
        BaseController::__construct($request);
        $this->documents = $docs;
        $this->files = $fileUpload;
    }
    public function index()
    {
        $data = json_decode(Api::get('/api/v1/endorsement-wordings/all'));

        if ($data == null) {
            return view('aspen.endorsement-wordings.index')->with('error', 'Failed to fetch Endorsement Wording results');
        }

        if ($data->response == 'success') {
            return view(
                'aspen.endorsement-wordings.index', [
                'data' => $data->data
                ]
            );
        }

        return view('aspen.endorsement-wordings.index')->with('error', 'Failed to fetch Endorsement Wording results');
    }

    public function downloadFile($cloudFileName, $fileName, $id = null)
    {
        if (Session::get('user')->login_type == 'aspen-user') {
            $method = Request::method();
            $path   = Request::path();

            $data = [
                'user_id'    => Session::get('user')->id,
                'first_name' => Session::get('user')->first_name,
                'last_name'  => Session::get('user')->last_name,
                'user_email' => Session::get('user')->email,
                'role'       => Session::get('role'),
                'referer'    => isset($_SERVER["HTTP_REFERER"]) ? $_SERVER["HTTP_REFERER"] : 'direct',
                'segments'   => Request::segments()
            ];

            $data['method'] = $method;
            $data['path']   = $path;

            Api::post('api/v1/weblog', $data);
        }

        if (isset($id)) {
            $ew = json_decode(Api::get('/api/v1/endorsement-wordings/find/'.$id));

            if (! isset($ew) && $ew->response != 'success') {
                return Redirect::back()
                    ->with('error', 'Unable to find endorsement document');
            }

            $file = $this->documents->download('endorsement_documents/' . $cloudFileName, $cloudFileName);

            if ($file['response'] == 'success') {
                $decrypt = $this->documents->decrypt($file['data'], $cloudFileName);
            } else {
                return Response::json(
                    [
                    'response' => 'error',
                    'message'  => $file['message']
                    ]
                );
            }

            return Response::download(
                $decrypt['data'], $fileName, [
                'Content-Type' => 'text/plain'
                ]
            );
        }

        $file = $this->files->download('/endorsement_wordings/'.$cloudFileName, $fileName);

        if ($file) {
            return Response::download($file, $fileName);
        }

        return Redirect::route('aspen.endorsement-wordings')
            ->with('error', 'Unable to download endorsement document');
    }

    public function create()
    {
        return view('aspen.endorsement-wordings.create');
    }

    public function store(Request $request)
    {
        $validator = Validator::make(
            $request->all(), [
            'name'        => 'required',
            'description' => 'required',
            'document'    => 'required'
            ]
        );

        if ($validator->fails()) {
            return Redirect::back()->withInput($request->old())
                ->withErrors($validator->errors());
        }

        $data = $request->except('_token');

        if ($request->hasFile('document')) {
            $file     = $this->documents->encrypt($request->file('document'));
            $uuid     = Str::uuid()->toString();
            $fileName = $request->file('document')->getClientOriginalName();

            $upload = $this->documents->upload($file, $uuid, 'endorsement_documents');

            if ($upload['response'] == 'success') {
                $data['file'] = $uuid;
                $data['original_filename'] = $fileName;
            }
            else {
                return Redirect::back()
                    ->with('error', $upload['message'])
                    ->withInput($request->old());
            }
        }

        $response = json_decode(Api::post('/api/v1/endorsement-wordings/store', $data));

        if (isset($response) && $response->response == 'success') {
            return Redirect::route('aspen.endorsement-wordings')
                ->with('success', 'Endorsement document created successfully');
        } else {
            return Redirect::back()
                ->withInput($request->old())
                ->withErrors($response->errors);
        }
    }

    public function edit($id)
    {
        $ew = json_decode(Api::get('/api/v1/endorsement-wordings/find/'.$id));

        if (isset($ew) && $ew->response == 'success') {
            if ($ew->data->authority == 0 && Session::get('user')->login_type == 'aspen-user') {
                return Redirect::back()
                    ->with('error', 'Unable to find endorsement document');
            }

            return view(
                'aspen.endorsement-wordings.edit', [
                'ew' => $ew->data
                ]
            );
        } else {
            return Redirect::back()
                ->with('error', 'Unable to find endorsement document');
        }

        return view('aspen.endorsement-wordings.edit');
    }

    public function authorize($id)
    {
        $ew = json_decode(Api::get('/api/v1/endorsement-wordings/authorize/'.$id));

        if (isset($ew) && $ew->response == 'success') {
            return Redirect::route('aspen.endorsement-wordings')
                ->with('success', 'Endorsement document has been authorized');
        } else {
            return Redirect::route('aspen.endorsement-wordings')
                ->with('error', 'Unable to authorize endorsement document');
        }

        return view('aspen.endorsement-wordings.edit');
    }

    public function update(Request $request)
    {
        $validator = Validator::make(
            $request->all(), [
            'name'        => 'required',
            'description' => 'required'
            ]
        );

        if ($validator->fails()) {
            return Redirect::back()->withInput($request->old())
                ->withErrors($validator->errors());
        } else {
            $data = $request->except('_token');

            if (! $request->has('id')) {
                return Redirect::back()
                    ->with('error', 'Unable to update endorsement document')
                    ->withInput($request->old());
            }

            if ($request->hasFile('document')) {
                $id = $request->get('id');
                $ew = json_decode(Api::get('/api/v1/endorsement-wordings/find/'.$id));

                if (isset($ew) && $ew->response == 'success') {
                    $file     = $request->file('document');
                    $uuid     = Str::uuid()->toString();
                    $fileName = $request->file('document')->getClientOriginalName();
                    $upload = $this->documents->update('endorsement_documents/'.$ew->data->file, $file, $uuid, 'endorsement_documents');

                    if ($upload['response'] == 'success') {
                        $data['file'] = $uuid;
                        $data['original_filename'] = $fileName;
                    } else {
                        return Redirect::back()
                            ->with('error', 'Unable to update endorsement document due to an error with the document upload')
                            ->withInput($request->old());
                    }
                }
            }

            $response = json_decode(Api::post('/api/v1/endorsement-wordings/update', $data));

            if ($response && $response->response == 'success') {
                return Redirect::route('aspen.endorsement-wordings')
                    ->with('success', 'Endorsement document updated');
            } else {
                return Redirect::back()
                    ->with('error', 'Unable to update endorsement document')
                    ->withInput($request->old());
            }
        }

    }

    public function destroy($id)
    {
        $ew = json_decode(Api::get('/api/v1/endorsement-wordings/find/'.$id));

        if (isset($ew) && $ew->response == 'success') {
            if ($ew->data->authority == 0 && Session::get('user')->login_type == 'aspen-user') {
                return Redirect::back()
                    ->with('error', 'Unable to find endorsement document');
            }

            $response = json_decode(Api::post('/api/v1/endorsement-wordings/destroy/'.$id));

            if ($response->response == 'success') {
                return Redirect::back()
                    ->with('success', 'Endorsement document deleted');
            } else {
                return Redirect::back()
                    ->with('error', 'Unable to delete endorsement document');
            }
        } else {
            return Redirect::back()
                ->with('error', 'Unable to find endorsement document');
        }
    }

}
