<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Api;
use App\Models\LessonObjects;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Str;
use App\Http\Controllers\BaseController;
use stdClass;

class LearningLessonPageController extends BaseController
{

    public LessonObjects $lessonObjects;

    public function __construct(Request $request, LessonObjects $lessonObjects)
    {
        parent::__construct($request);

        $this->lessonObjects = $lessonObjects;
    }


    public function edit($course_id, $lesson_id, $page_id)
    {

        $api_call = json_decode(Api::get('/api/v1/learning/lesson/' . $lesson_id . '/page/' . $page_id));


        if(isset($api_call->response) && $api_call->response == 'success') {

            $api_lesson = json_decode(Api::Get('/api/v1/learning/lesson/' . $lesson_id));


            return view(
                'learning.lesson.components.page-item', array(
                'page_no'   =>  1,
                'page'  =>  $api_call->data,
                'resource' => $api_lesson->data
                )
            );
        }

        return null;

    }



    public function store(Request $request, $course_id, $lesson_id)
    {


        $rules = array(
            'title' =>  'required'
        );

        $validator = Validator::make($request->all(), $rules);

        if($validator->fails()) {

            return Response::json(
                array(
                'response' => 'errors',
                'errors'    =>  $validator->errors()
                )
            );
        }


        $api_call = json_decode(
            Api::post(
                '/api/v1/learning/lesson/' . $lesson_id . '/page', array(
                'title' => $request->Get('title'),
                'course_id' =>  $course_id
                )
            )
        );

        if(isset($api_call->response)) {

            return Response::json($api_call);

        }


        return null;

    }


    public function delete($course_id, $lesson_id, $page_id)
    {

        $api_call = json_decode(Api::delete('/api/v1/learning/lesson/' . $lesson_id . '/page/' . $page_id));

        // print_r(Api::delete('/api/v1/learning/lesson/' . $lesson_id . '/page/' . $page_id)); exit;

        if(isset($api_call->response) && $api_call->response == 'success') {

            return Response::json(array('response' => 'success'));

        }

        return Response::json(array('response' => 'error'));
    }


    public function duplicate($course_id, $lesson_id, $page_id)
    {

        $api_call = json_decode(Api::post('/api/v1/learning/lesson/' . $lesson_id . '/page/' . $page_id . '/duplicate'));

        if(isset($api_call->response) && $api_call->response == 'success') {

            return Response::json(array('response' => 'success','data' => $api_call->data));

        }

        return Response::json(array('response' => 'error'));

    }

    public function showButton($course_id, $lesson_id, $page_id)
    {


        $api_call = json_decode(Api::get('/api/v1/learning/lesson/' . $lesson_id));


        if(isset($api_call->response) && $api_call->response == 'success') {

            if(isset($api_call->data->pages)) {

                $pages = $api_call->data->pages;
                if(count($pages)) {
                    foreach ($pages as $key => $page){

                        if($page->_id == $page_id) {
                            $i = $key + 1;
                        }

                    }
                }else{
                    $i = 1;
                }


                return view(
                    'learning.lesson.components.page-button', array(
                    'i' => $i,
                    'resource' => $api_call->data
                    )
                );
            }else{

                return view(
                    'learning.lesson.components.page-button', array(
                    'i' => 1,
                    'resource' => $api_call->data
                    )
                );
            }

        }

        return null;

    }


    public function showPage($course_id, $lesson_id, $page_id)
    {

        $api_call = json_decode(Api::get('/api/v1/learning/lesson/' . $lesson_id));


        if(isset($api_call->response) && $api_call->response == 'success') {

            if(isset($api_call->data->pages)) {

                $pages = $api_call->data->pages;

                foreach ($pages as $key =>  $page){

                    if($page->_id == $page_id) {

                        return view(
                            'learning.lesson.components.page-item', array(
                            'page_no'   =>  $key - 1,
                            'page'      =>  $page
                            )
                        );

                    }

                }

            }

        }

        return null;

    }

    public function uploadImage(Request $request, $course_id, $lesson_id)
    {
        $page_id = $request->Get('page_id');
        $section_id = $request->Get('section_id');

        $api_call = json_decode(Api::get('api/v1/learning/lesson/' . $lesson_id . '/page/' . $page_id));

        if(isset($api_call->response) && $api_call->response == 'success') {

            $current_section = null;
            foreach ($api_call->data->sections as $section){

                if($section->id == $section_id) {
                    $current_section = $section;
                }

            }

            $file_field_name = "file";
            $file = $request->file($file_field_name);
            $size = $request->file($file_field_name)->getSize();
            $path = $file->getRealPath();
            $type = $file->getMimeType();
            $extension = $request->file($file_field_name)->getClientOriginalExtension(); // getting image extension

            $uuid = Str::uuid()->toString();

            $fileName = $uuid . '.' . $extension;

            $upload = $this->lessonObjects->uploadImage($path, $fileName, $course_id, $lesson_id);

            if($upload != null) {
                $current_section->file_name = $fileName;
                $current_section->uuid = $uuid;
                $current_section->extension = $extension;
                $current_section->size = $size;
                $current_section->file_type = $type;


                $update_response = json_decode(Api::put('api/v1/learning/lesson/' . $lesson_id . '/page/' . $page_id .'/section/' . $section_id, $current_section));

                if(isset($update_response->response) && $update_response->response == 'success') {

                    $update_response->data->url = $this->lessonObjects->getImage($fileName, $course_id, $lesson_id);
                    return Response::json($update_response);

                }

            }
        }

        return Response::json(array('response' => 'error', 'message' => 'Page does not exist'));
    }

    public function filecheck($course_id, $lesson_id, $uu_id)
    {

        $trys = 0;
        $limits = 3;
        $file = null;
        while($file == null){
            $trys++;
            $file = $this->lessonObjects->checkExist2($uu_id, $course_id, $lesson_id);
            if($trys >= $limits) {
                break;
            }
        }
        if($file !== null) {
            $update_response = new stdClass();
            $update_response->response = 'success';
            $update_response->url = $this->lessonObjects->getImage($uu_id, $course_id, $lesson_id);
            $update_response->posterUrl = $this->lessonObjects->getImage($uu_id.'-00001.png', $course_id, $lesson_id);
            return Response::json($update_response);
        }
        return Response::json(array('response' => 'error', 'message' => 'File does not exist'));

    }

    public function uploadAudio(Request $request, $course_id, $lesson_id)
    {
        $page_id = $request->get('page_id');
        $section_id = $request->get('section_id');

        $api_call = json_decode(Api::get('/api/v1/learning/lesson/' . $lesson_id . '/page/' . $page_id));

        if(isset($api_call->response) && $api_call->response == 'success') {

            $current_section = null;
            foreach ($api_call->data->sections as $section){

                if($section->id == $section_id) {
                    $current_section = $section;
                }

            }

            $file_field_name = "file";
            $file = $request->file($file_field_name);
            $size = $request->file($file_field_name)->getSize();
            $path = $file->getRealPath();
            $type = $file->getMimeType();
            $extension = $request->file($file_field_name)->getClientOriginalExtension(); // getting image extension

            $uuid = Str::uuid()->toString();

            $uuid_string = $uuid."";

            $fileName = $uuid_string . '.' . $extension;

            $upload = $this->lessonObjects->uploadAudio($path, $fileName, $course_id, $lesson_id, $uuid_string);

            if($upload != null) {
                $current_section->file_name = "audio-".$fileName;
                $current_section->uuid = "audio-".$uuid_string;
                $current_section->extension = $extension;
                $current_section->size = $size;
                $current_section->file_type = $type;


                $update_response = json_decode(Api::put('api/v1/learning/lesson/' . $lesson_id . '/page/' . $page_id .'/section/' . $section_id, $current_section));

                if(isset($update_response->response) && $update_response->response == 'success') {
                    $update_response->data->course_id = $course_id;
                    $update_response->data->lesson_id = $lesson_id;
                    $update_response->data->uuid =  "audio-".$uuid_string;
                    $update_response->data->url = $this->lessonObjects->getImage("audio-".$fileName, $course_id, $lesson_id);

                    return Response::json($update_response);

                } else {
                    return Response::json(array('response' => 'error', 'message' => 'here'));
                }

            } else {
                return Response::json(array('response' => 'error', 'message' => 'fails here'));
            }
        }

        return Response::json(array('response' => 'error', 'message' => 'Page does not exist'));
    }

    public function uploadVideo(Request $request, $course_id, $lesson_id)
    {
        $page_id = $request->get('page_id');
        $section_id = $request->get('section_id');

        $api_call = json_decode(Api::get('/api/v1/learning/lesson/' . $lesson_id . '/page/' . $page_id));
        //return Response::json(array('response' => 'error', 'data' => '/api/v1/learning/lesson/' . $lesson_id . '/page/' . $page_id));
        if(isset($api_call->response) && $api_call->response == 'success') {

            $current_section = null;
            foreach ($api_call->data->sections as $section){

                if($section->id == $section_id) {
                    $current_section = $section;
                }

            }

            $file_field_name = "file";
            $file = $request->file($file_field_name);
            $size = $request->file($file_field_name)->getSize();
            $path = $file->getRealPath();
            $type = $file->getMimeType();
            $extension = $request->file($file_field_name)->getClientOriginalExtension(); // getting image extension

            $uuid = Str::uuid()->toString();

            $uuid_string = $uuid."";

            $fileName = $uuid_string . '.' . $extension;

            //return Response::json(array('response' => 'error', 'message' => 'here'));

            // print_r($fileName);
            // echo "<br>";
            // print_r($uuid_string); exit;

            $upload = $this->lessonObjects->uploadVideo($path, $fileName, $course_id, $lesson_id, $uuid_string);

            if($upload != null) {
                $current_section->file_name = "video-".$fileName;
                $current_section->uuid = "video-".$uuid_string;
                $current_section->extension = $extension;
                $current_section->size = $size;
                $current_section->file_type = $type;


                $update_response = json_decode(Api::put('api/v1/learning/lesson/' . $lesson_id . '/page/' . $page_id .'/section/' . $section_id, $current_section));

                if(isset($update_response->response) && $update_response->response == 'success') {
                    $update_response->data->course_id = $course_id;
                    $update_response->data->lesson_id = $lesson_id;
                    $update_response->data->uuid =  "video-".$uuid_string;
                    $update_response->data->url = $this->lessonObjects->getImage("video-".$fileName, $course_id, $lesson_id);

                    return Response::json($update_response);

                } else {
                    return Response::json(array('response' => 'error', 'message' => 'here'));
                }

            } else {
                return Response::json(array('response' => 'error', 'message' => 'fails here'));
            }
        }

        return Response::json(array('response' => 'error', 'message' => 'Page does not exist'));
    }


    public function deleteDocument($course_id, $lesson_id, $page_id, $section_id, $document_id)
    {

        $api_call = json_decode(Api::get('/api/v1/learning/lesson/' . $lesson_id . '/page/' . $page_id));

        if(isset($api_call->response) && $api_call->response == 'success') {

            $current_section = null;
            foreach ($api_call->data->sections as $section) {

                if ($section->id == $section_id) {
                    $current_section = $section;
                }

            }


            if(isset($current_section->documents)) {
                foreach ($current_section->documents as $keys => $document){

                    if($document->uuid == $document_id) {
                        if(is_array($current_section->documents)) {
                            unset($current_section->documents[$keys]);
                        }else{
                            unset($current_section->documents->$keys);
                        }

                    }

                }


                $update_response = json_decode(Api::put('/api/v1/learning/lesson/' . $lesson_id . '/page/' . $page_id .'/section/' . $section_id, $current_section));

                if(isset($update_response->response) && $update_response->response == 'success') {
                    return Response::json(array('response' => 'success'));
                }
            }

        }

        return Response::json(array('response' => 'error'));

    }


    public function uploadDocument(Request $request, $course_id, $lesson_id)
    {
        $page_id = $request->get('page_id');
        $section_id = $request->get('section_id');

        $api_call = json_decode(Api::get('/api/v1/learning/lesson/' . $lesson_id . '/page/' . $page_id));

        if(isset($api_call->response) && $api_call->response == 'success') {

            $current_section = null;
            foreach ($api_call->data->sections as $section){

                if($section->id == $section_id) {
                    $current_section = $section;
                }

            }

            $file_field_name = "file";
            $file = $request->file($file_field_name);
            $size = $request->file($file_field_name)->getSize();
            $path = $file->getRealPath();
            $name = pathinfo($request->file($file_field_name)->getClientOriginalName(), PATHINFO_FILENAME);
            $full_name = $request->file($file_field_name)->getClientOriginalName();
            $type = $file->getMimeType();
            $extension = $request->file($file_field_name)->getClientOriginalExtension(); // getting image extension

            $uuid = Str::uuid()->toString();

            $fileName = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $name))) . '.' . $extension;
            do{
                $exists = $this->lessonObjects->checkExist($fileName, $course_id, $lesson_id);
                if($exists == true) {
                    $fileName = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $name))) . '-' . str_random(3) . '.' . $extension;
                }
            }while($exists == true);

            $upload = $this->lessonObjects->uploadImage($path, $fileName, $course_id, $lesson_id);

            if($upload != null) {
                if(isset($current_section->documents)) {
                    $documents = $current_section->documents;
                    if(is_array($documents)) {
                        array_push(
                            $documents, array(
                            'file_name' =>  $fileName,
                            'original_name' =>  $name,
                            'uuid'      =>  $uuid,
                            'extension' =>  $extension,
                            'size'      =>  $size,
                            'file_type' =>  $type
                            )
                        );
                    }else{
                        end($documents);
                        $key = key($documents) + 1;
                        $documents->$key = array(
                            'file_name' =>  $fileName,
                            'original_name' =>  $name,
                            'uuid'      =>  $uuid,
                            'extension' =>  $extension,
                            'size'      =>  $size,
                            'file_type' =>  $type
                        );
                    }

                    $current_section->documents = $documents;

                }else{

                    $documents = array();
                    array_push(
                        $documents, array(
                        'file_name' =>  $fileName,
                        'original_name' =>  $name,
                        'uuid'      =>  $uuid,
                        'extension' =>  $extension,
                        'size'      =>  $size,
                        'file_type' =>  $type
                        )
                    );
                    $current_section->documents = $documents;

                }


                $update_response = json_decode(Api::put('/api/v1/learning/lesson/' . $lesson_id . '/page/' . $page_id .'/section/' . $section_id, $current_section));

                if(isset($update_response->response) && $update_response->response == 'success') {


                    $update_response->data->url = $this->lessonObjects->getImage($fileName, $course_id, $lesson_id);
                    $update_response->data->name = $fileName;
                    return Response::json($update_response);

                }

            }
        }

        return Response::json(array('response' => 'error', 'message' => 'Page does not exist'));

    }


    public function showSection($course_id, $lesson_id, $page_id, $section_id)
    {
        $api_call = json_decode(Api::Get('/api/v1/learning/lesson/' . $lesson_id . '/page/' . $page_id));

        if(isset($api_call->response) && $api_call->response == 'success') {

            //resource
            $api_resource = json_decode(Api::Get('/api/v1/learning/lesson/' . $lesson_id));

            foreach ($api_call->data->sections as $section){


                if($section->id == $section_id) {


                    return view(
                        'learning.lesson.components.' . $section->type, array(
                        'id'            =>  $section->id,
                        'resource'      =>  $api_resource->data,
                        'page'          =>  $api_call->data,
                        'alignment'     =>  isset($section->alignment) ? $section->alignment : null,
                        'content'       =>  isset($section->content) ? $section->content : null
                        )
                    );
                }
            }
        }


        return Response::json(
            array(
            'response' => 'error'
            )
        );

    }


    public function saveSection($course_id, $lesson_id, $page_id, $section_type)
    {
        $api_call = json_decode(Api::get('api/v1/learning/lesson/' . $lesson_id));

        // print_r(Api::get('api/v1/learning/lesson/' . $lesson_id)); exit;


        if(isset($api_call->response) && $api_call->response == 'success') {

            //Add Section
            $add_section_call = json_decode(
                Api::post(
                    'api/v1/learning/lesson/' . $lesson_id . '/page/' . $page_id . '/section', array(
                    'type' => $section_type
                    )
                )
            );

            $page_call = json_decode(Api::get('api/v1/learning/lesson/' . $lesson_id . '/page/' . $page_id));


            if(isset($add_section_call->response) && $add_section_call->response == 'success') {

                return view(
                    'learning.lesson.components.'. $section_type, array(
                    'id' => $add_section_call->data->id,
                    'resource'       => $api_call->data,
                    'page'          =>  $page_call->data,
                    'alignment'    =>  isset($add_section_call->data->alignment) ? $add_section_call->data->alignment : null,
                    'content'       =>  isset($add_section_call->data->content) ? $add_section_call->data->content : null,
                    'url'           =>  isset($add_section_call->data->url) ? $add_section_call->data->url : null
                    )
                );

            }

        }


        return Response::json(
            array(
            'response' => 'error'
            )
        );

    }


    public function deleteSection($course_id, $lesson_id, $page_id, $section_id)
    {

        $api_call = json_decode(Api::delete('/api/v1/learning/lesson/' . $lesson_id . '/page/' . $page_id . '/section/' . $section_id));

        if(isset($api_call->response) && $api_call->response == 'success') {
            return Response::json(
                array(
                'response' => 'success'
                )
            );
        }

        return Response::json(
            array(
            'response' => 'error'
            )
        );
    }

    public function updateSection(Request $request, $course_id, $lesson_id, $page_id, $section_id)
    {

        $data = $request->except('_token');

        $api_call = json_decode(Api::put('/api/v1/learning/lesson/' . $lesson_id . '/page/' . $page_id . '/section/' . $section_id, $data));

        if(isset($api_call->response) && $api_call->response == 'success') {

            return Response::json(array('response' => 'success'));

        }

        return Response::json(array('response' => 'error'));

    }


    public function sort(Request $request, $course_id, $lesson_id)
    {

        $rules = array(
            'page_ids' => 'required|array'
        );

        $validator = Validator::make($request->all(), $rules);

        if($validator->fails()) {

            return Response::json(array('response' => 'error', 'errors' => $validator->errors()));

        }

        $page_ids = $request->Get('page_ids');

        $api_call = json_decode(Api::put('/api/v1/learning/lesson/' . $lesson_id .'/page/sort', array('page_ids' => $page_ids)));

        if(isset($api_call->response) && $api_call->response == 'success') {

            return Response::json(array('response' => 'success', 'data' => $api_call->data));
        }

        return Response::json(array('response' => 'error'));

    }


    /**
     * Sort Page Sections
     *
     * @param  $course_id
     * @param  $lesson_id
     * @param  $page_id
     * @return mixed
     */
    public function sortPage(Request $request, $course_id, $lesson_id, $page_id)
    {

        $rules = array(
            'section_ids' => 'required|array'
        );

        $validator = Validator::make($request->all(), $rules);

        if($validator->fails()) {


            return Response::json(array('response' => 'error', 'errors' => $validator->errors()));


        }

        $section_ids = $request->geT('section_ids');

        $api_call = Api::put('/api/v1/learning/lesson/' . $lesson_id . '/page/' . $page_id . '/sort', array('section_ids' => $section_ids));

        $api_call = json_decode($api_call);

        if(isset($api_call->response) && $api_call->response == 'success') {

            return Response::json(array('response' => 'success', 'data' => $api_call->data));
        }

        return Response::json(array('response' => 'error'));
    }


    public function duplicateSection($course_id, $lesson_id, $page_id, $section_id)
    {

        $api_call = json_decode(Api::post('/api/v1/learning/lesson/' . $lesson_id . '/page/' . $page_id . '/section/' . $section_id . '/duplicate'));
        if(isset($api_call->response) && $api_call->response == 'success') {

            $api_call->data = $this->processSection($api_call->data, $course_id, $lesson_id);


            return Response::json(array('response' => 'success', 'data' => $api_call->data));
        }

        return Response::json(array('response' => 'error'));

    }

}
