<?php

namespace App\Http\Controllers;use Illuminate\Http\Request;
use App\Models\Api;
use App\Models\FileUpload;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Response;
use Barryvdh\DomPDF\Facade\Pdf;
class RhsOrganisationController extends BaseController
{

    //TEMPLATE PATH
    const TEMPLATE_PATH = '/rhs';

    public function __construct(Request $request, FileUpload $fileUpload)
    {
        parent::__construct($request);
        $this->files = $fileUpload;
    }

    public function index()
    {
        $organisations = json_decode(Api::get('/api/v1/rhs/organisations'))->data;

        return view(
            'rhs/organisations/index', [
            'organisations' => $organisations,
            ]
        );
    }

    public function store(Request $request)
    {
        $request = $request->all();

        $response = json_decode(Api::post('/api/v1/rhs-organisations', $request));

        $pdf_type = ($response->customer->customer_type_id==1) ? '.pdf.rhs' : '.pdf.bloom';

        $pdf = PDF::loadView(
            static::TEMPLATE_PATH . $pdf_type, [
            'order' => $response->order,
            'document' => $response->document,
            'customer' => $response->customer,
            'date_of_issuance' => date('dS'). ' of '.date('M Y'),
            'logo' => url('/').'/img/logo.svg'
            ]
        );

        $path = storage_path().'/rhs/'.$request['uuid'].'/'.$request['affiliate_ref'].'.pdf';

        $pdf->save($path);

        $uploadResult = $this->files->upload($path, $request['uuid'], $response->organisation_id);

        $imagedata = file_get_contents($path);

        $request['pdf_data'] = base64_encode($imagedata);

        // generate email + attachment
        $json_test = json_decode(Api::post('/api/v1/rhs-organisations/document/info', $request));

        $validator = Validator::make(
            $request->all(), [
            'email' => 'required',
            'name' => 'required|regex:/(^[A-Za-z0-9 ]+$)+/',
            'country' => 'required',
            'first_name' => 'required',
            'last_name' => 'required',
            'manager' => 'required',
            'branch' => 'required',
            'address_line_1' => 'required_if:branch,on',
            'postcode' => 'required_if:branch,on',
            ]
        );

        if ($validator->fails()) {
            return Response::json(
                [
                'response' => 'error',
                'errors' => $validator->messages()->toArray()
                ], 200
            );
        }

        if (isset($response->response) && $response->response == 'success') {
            return Response::json(
                [
                'response' => 'success',
                'errors' => 'Client created successfully'
                ], 200
            );
        }

        return Response::json(
            [
            'response' => 'error',
            'errors' => 'Client could not be created'
            ], 200
        );
    }

    public function show($organisation_id)
    {
        $organisation = json_decode(Api::get('/api/v1/rhs/organisations/' . $organisation_id))->data;

        return view(
            'rhs/organisations/show', [
            'organisation' => $organisation,
            ]
        );
    }
}
