<?php

namespace App\Http\Controllers;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Response;
use App\Models\Api;
class RhsFinancialReportController extends BaseController
{
    const TEMPLATE_PATH = 'rhs';

    const ROUTE_PREFIX = 'rhs';

    const TYPE_MEMBERSHIP_ID = 1;

    const TYPE_GE_CARD_ID = 4;

    public function __construct(Request $request)
    {
        parent::__construct($request);

        if (Session::get('user')->login_type != 'risk-control' && !Session::get('user')?->isRoleAdminOrAccountManager()) {
            exit;
        }
    }

    public function index(Request $request)
    {
        $get = [];

        $request->has('start') ? $get[] = 'start=' . $request->get('start') : '';
        $request->has('end') ? $get[] = 'end=' . $request->get('end') : '';
        $request->has('page') ? $get[] = 'page=' . $request->get('page') : 1;
        $request->has('limit') ? $get[] = 'limit=' . $request->get('limit') : 10;

        $orders = json_decode(Api::get('/api/v1/rhs/orders?'.implode('&', $get)));

        //print_r($orders);

        return view(
            'rhs/financial-report', [
            'orders' => $orders->data,
            'page' => isset($get['page']) ? $get['page'] : 1,
            'limit' => isset($get['limit']) ? $get['limit'] : 10,
            'total' => $orders->total,
            'search' => '',
            'link' => 'rhs.financial-report'
            ]
        );
    }

    public function export(Request $request)
    {
        $get = [];

        $request->has('start') ? $get[] = 'start=' . $request->get('start') : '';
        $request->has('end') ? $get[] = 'end=' . $request->get('end') : '';
        $request->has('export') ? $get[] = 'export=' . $request->get('export') : '';

        $orders = json_decode(Api::get('/api/v1/rhs/orders?'.implode('&', $get)));

        $data = [];

        $headers = [
            'Content-type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename=financial-report-export-' . Carbon::now()->format('d-m-Y').'.csv',
            'Pragma' => 'no-cache',
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Expires' => '0',
        ];

        $columns = [
            'Order ID',
            'Document ID',
            'Stripe ID',
            'Organisation Name',
            'SECT Period of Ins',
            'SECT IPT GBP',
            'SECT RHS Fee GBP',
            'SECT Premium GBP',
            'PLEL Period of Ins',
            'PLEL IPT GBP',
            'PLEL RHS Fee GBP',
            'PLEL Premium GBP',
            'RHS Reg Fee GBP',
            'GE Card',
            'Created At',
            'Total',
        ];

        $callback = function () use ($orders, $columns) {
            $file = fopen('php://output', 'w');

            fputcsv($file, $columns);

            foreach ($orders->data as $order) {
                if ($order->order->is_manually_created_rr===0) {
                    $sect_premium = $this->safeNumberFormat($order->sect_premium_gbp + $order->sect_ipt_gbp + $order->sect_rhs_fee_gbp);
                    $plel_premium = $this->safeNumberFormat($order->liabs_premium_gbp + $order->liabs_ipt_gbp + $order->liabs_rhs_fee_gbp);

                    fputcsv(
                        $file, [
                        $order->order_id,
                        $order->id,
                        $order->order->customer->affiliate_ref,
                        $order->organisation_name,
                        $order->sect_period_of_ins,
                        $this->safeNumberFormat($order->sect_ipt_gbp),
                        $this->safeNumberFormat($order->sect_rhs_fee_gbp),
                        $sect_premium,
                        $order->liabs_period_of_ins,
                        $this->safeNumberFormat($order->liabs_ipt_gbp),
                        $this->safeNumberFormat($order->liabs_rhs_fee_gbp),
                        $plel_premium,
                        $this->extractProductsFee($order, self::TYPE_MEMBERSHIP_ID),
                        $this->extractProductsFee($order, self::TYPE_GE_CARD_ID),
                        date('Y-m-d H:i:s', strtotime($order->created_at)),
                        $this->safeNumberFormat($sect_premium + $plel_premium),
                        ]
                    );
                }
            }

            fclose($file);
        };

        return Response::stream($callback, 200, $headers);
    }

    public static function safeNumberFormat($number, $decimals = 2)
    {
        if (!is_numeric($number)) {
            $number = 0;
        }

        return number_format($number, $decimals);
    }

    public static function extractProductsFee($data, $membership_id)
    {
        $amount = 0;
        if (isset($data->order->products)) {
            foreach ($data->order->products as $product) {
                if ($product->type->product_category_id == $membership_id) {
                    $amount = $product->price;
                    break;
                }
            }
        }

        return self::safeNumberFormat($amount);
    }
}
