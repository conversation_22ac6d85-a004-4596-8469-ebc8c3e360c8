<?php

namespace App\Http\Controllers\LetsTalk;

use Carbon\Carbon;
use App\Models\Api;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Session;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;

class SocialRoomScheduleController extends BaseController
{
    public function store(Request $request)
    {
        $data = $request->all();
        $apiParams['person_id'] = $this->getAuthPersonId();
        $apiParams['role'] = 'virtual-rooms';
        $validator = $this->validateStore($data);
        if ($validator->fails()) {
            $errorResponse = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
            return Response::json($errorResponse, 400);
        }

        $apiParams['room_id'] = $data['room_id'];

        if ($data['duration_type'] === 'permanent') {
            $startDateCarbon = Carbon::now();
            $apiParams['start_date'] = $startDateCarbon->toDateTimeString();
            $apiParams['end_date'] = $startDateCarbon->toDateTimeString();
        } else {
            $startDateCarbon = Carbon::createFromFormat('d/m/Y', $data['start_date'], 'Europe/London');
            $apiParams['start_date'] = (string)Carbon::parse($startDateCarbon->format('Y-m-d') . ' ' . $data['start_time']);
            $apiParams['end_date'] = (string)Carbon::parse($startDateCarbon->format('Y-m-d') . ' ' . $data['end_time']);
        }

        $apiParams['duration_type'] = $data['duration_type'];
        if ($apiParams['duration_type'] === 'one-off') {
            $apiParams['frequency'] = 1;
            $apiParams['frequency_type'] = 'day';
            $apiParams['end_after_frequency'] = 1;
            $apiParams['end_after_frequency_type'] = 'day';
        } else {
            $apiParams['frequency'] = explode(' ', $data['frequency'])[0];
            $apiParams['frequency_type'] = explode(' ', $data['frequency'])[1];
            $apiParams['end_after_frequency'] = explode(' ', $data['end_after'])[0];
            $apiParams['end_after_frequency_type'] = explode(' ', $data['end_after'])[1];
        }

        $response = json_decode(Api::post('api/v1/virtual-rooms/social-room-schedule', $apiParams), true);

        $hasError = $response['response'] === 'error';
        $status = $hasError ? 'failure' : 'success';
        $title = $hasError ? 'Failed' : 'Successfully Created';
        $message = $hasError ? 'Failed.' : 'Your meetings have been successfully created.';

        Session::flash($status, json_encode(['title' => $title, 'message' => $message]));

        if ($hasError) {
            return Response::json($response, 400);
        }

        return Response::json($response);
    }

    private function validateStore($data)
    {
        $rules = [
            'room_id' => 'required',
            'duration_type' => 'required',
            'start_date' => 'required_if:duration_type,on-a-schedule,one-off',
            'start_time' => 'required_if:duration_type,on-a-schedule,one-off',
            'end_time' => 'required_if:duration_type,on-a-schedule,one-off',
            'frequency' => 'required_if:duration_type,on-a-schedule',
            'end_after' => 'required_if:duration_type,on-a-schedule',
        ];

        // // On update, we don't want to validate email, cause it cannot be updated
        // if($action === 'update') {
        //     unset($rules['email']);
        // }

        $messages = [
            'duration_type.required' => 'Please select duration type.',
            'start_date.required' => 'Please select start date.',
            'start_time.required' => 'Please select start time.',
            'frequency.required' => 'Please select frequency.',
            'end_after.required' => 'Please select end after.',
        ];

        return Validator::make($data, $rules, $messages);
    }

    public function getUpcomingTeamMeetings(Request $request)
    {
        $isRoomReady = false;
        $user = Session::get('socials-user');
        $personId = $user['person_id'];
        // TODO: remove this condition. for testing only

        $response = json_decode(Api::get('api/v1/virtual-rooms/team-meeting/upcoming?person_id=' . $personId), true);

        if ($response['response'] === 'error') {
            return Response::json($response, 400);
        }
        if (isset($response['data'][0]['schedule']['date'])) {
            $now = \Carbon\Carbon::now()->timezone($user['timezone']);
            $latestSchedule = \Carbon\Carbon::parse($response['data'][0]['schedule']['date'], $user['timezone']);
            $isRoomReady = (((int) $now->diffInMinutes($latestSchedule, false) >= -5)) && (((int) $latestSchedule->diffInMinutes($now, false) <= 60)) ?  true : false;
            // office_timezone
            $response['data'][0]['is_room_ready'] = $isRoomReady;
        }
        return Response::json($response);
    }

    public function showManage(Request $request)
    {
        $user = Session::get('socials-user');
        $personId = $user['person_id'];

        // TODO: remove this. This is just for testing
        // TODO: remove this. This is just for testing
        // TODO: remove this. This is just for testing
        // if(!isset($personId)) {
        //     $personId = '5f1ab85d312399328a57e302';
        // }
        $upcomingSchedulesResponse = json_decode(Api::get('api/v1/virtual-rooms/team-meeting/upcoming?person_id=' . $personId), true);
        $upcomingSchedules = [];
        if ($upcomingSchedulesResponse['response'] === 'success') {
            $upcomingSchedules = $upcomingSchedulesResponse['data'];
        }

        $socialRoomTypeData = [];
        $socialRoomType = json_decode(Api::get('api/v1/virtual-rooms/' . html_entity_decode('Team Meetings') . '/social-room-type'), true);
        if ($socialRoomType['response'] == 'success') {
            $socialRoomTypeData = $socialRoomType['data'];
        }

        $teamsResponse = json_decode(Api::get('api/v1/virtual-rooms/team-meeting/teams?person_id=' . $personId), true);
        $teams = [];
        if ($teamsResponse['response'] === 'success') {
            $teams = $teamsResponse['data'];
        }
        // return Response::json([$teamsResponse]);

        return view('lets-talk-socials.manage-team', compact('upcomingSchedules', 'teams', 'socialRoomTypeData'));
    }

    public function showSpace(Request $request, $room)
    {
        $personId = $this->getAuthPersonId();

        $response = json_decode(Api::get('api/v1/virtual-rooms/team-meeting/' . $room . '?person_id=' . $personId));
        $announcementsResponse = json_decode(Api::post('api/v1/virtual-rooms/announcements', ['person_id' => $personId]));
        $socialRoomType = json_decode(Api::get('api/v1/virtual-rooms/' . html_entity_decode('Team Meetings') . '/social-room-type'), true);
        $roomUpcomingSchedule = [];
        $colleagueDetails = [];
        $colleague = [];


        if ($response->response === 'success') {
            $room = $response->data->room;

            $responseMessages = json_decode(Api::get('api/v1/virtual-rooms/discussion/reply?room=' . $room->id . '&person_id=' . $personId));
            $creator = $response->data->room->cms;
            $upcomingSchedules = json_decode(json_encode(array_slice($response->data->upcoming_schedules, 0, 5)), true);

            $room->messages = isset($responseMessages->data->messages) ? $responseMessages->data->messages : [];
            $recentAnnouncements = isset($announcementsResponse->data) ? array_slice($announcementsResponse->data, 0, 5) : '';
            $joinRoomLink = isset($response->data->join_room_link) ? $response->data->join_room_link : "#";
            $colleague = (array) $room->social_room_participants[0];
        }


        // return \Response::json($response);

        return view('lets-talk-socials.space', compact('room', 'creator', 'colleague', 'upcomingSchedules', 'recentAnnouncements', 'socialRoomType', 'joinRoomLink'));
    }

    public function getRepresentatives(Request $request)
    {
        $response = json_decode(Api::get('api/v1/virtual-rooms/team-meeting/representatives'), true);

        if ($response['response'] === 'success') {
            return Response::json($response);
        }

        return Response::json([$response]);
    }

    private function getAuthPersonId()
    {
        $user = Session::get('socials-user');
        return isset($user['person_id']) ? $user['person_id'] : '';
    }

    public function deleteSchedule(Request $request, $room_id)
    {
        //$data = $request->all();
        $data['room_id'] = $room_id;
        $response = Api::post('api/v1/virtual-rooms/delete-schedule',  $data);
    }

    public function downloadIcs(Request $request, $roomTypeId, $roomId)
    {
        $data = $request->all();
        $personId = $this->getAuthPersonId();
        $data['person_id'] = $personId;
        $data['static_download'] = ($request->has('static_download')) ? $request->get('static_download') : 1;
        $data['is_cyber_vr'] = $request->has('cyber_vr') && (bool)$request->get('cyber_vr');

        $url = sprintf('api/v1/virtual-rooms/%s/%s/download-ics', $roomTypeId, $roomId);
        $response = json_decode(Api::post($url, $data), true);

        $hasError = $response['response'] === 'error';

        $status = $hasError ? 'failure' : 'success';
        $title = $hasError ? 'Failed' : 'Successfully Downloaded';
        $message = $hasError ? 'Failed.' : 'Your meeting ics has been downloaded.';

        if (!isset($response['data']['ics']) || $hasError) {
            $title = "Failed";
            $message = "Unable to download ics file.";
            if (isset($response['message'])) {
                $message = $response['message'];
            }
            Session::flash($status, json_encode(['title' => $title, 'message' => $message]));

            return Redirect::to(URL::previous());
        }

        $ics = $response['data']['ics'];

        return Response::make($ics)
            ->header("Content-type", "text/calendar; charset=utf-8")
            ->header("Content-disposition", "attachment; filename=\"meeting.ics\"");
    }
}
