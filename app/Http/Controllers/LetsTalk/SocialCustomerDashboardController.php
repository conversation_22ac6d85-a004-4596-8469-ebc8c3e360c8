<?php

namespace App\Http\Controllers\LetsTalk;

use App\Models\Api;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Http\Controllers\BaseController;

class SocialCustomerDashboardController extends BaseController
{
    public function home(Request $request)
    {
        $customerEmail = Session::get('socials-customer')['customer_email'];
        $spacesResponse = json_decode(Api::post('api/v1/virtual-rooms/customer-spaces', ['customer_email' => $customerEmail]));
        $spaces = isset($spacesResponse->data->rooms) ? $spacesResponse->data->rooms : [];

        return view('lets-talk-socials.customers.customer-dashboard', ['spaces' => $spaces]);
    }
}
