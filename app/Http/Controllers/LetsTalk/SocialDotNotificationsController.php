<?php

namespace App\Http\Controllers\LetsTalk;

use App\Models\Api;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Response;

class SocialDotNotificationsController extends BaseController
{
    private $person_id;
    public function __construct(Request $request)
    {
        $this->person_id = Session::has('socials-user') ? Session::get('socials-user')['person_id'] : '';
    }

    public function getDotNotification(Request $request)
    {
        $data['person_id'] = $this->person_id;
        $response = json_decode(Api::post('api/v1/virtual-rooms/check-dot-notifications', $data));

        return Response::json(
            [
                'status' => 'success',
                'response' => $response,
                'message' => $response->message
            ]
        );
    }

    public function dismissThemeRoomNotification(Request $request)
    {
        $data = $request->all();
        $data['person_id'] = $this->person_id;
        $data['is_community'] = $data['is_community'];
        $response = json_decode(Api::post('api/v1/virtual-rooms/dismiss-theme-room-dot-notifications', $data));

        if (isset($response->message) && !empty($response->message)) {
            return Response::json(
                [
                    'status' => 'success',
                    'response' => $response,
                    'message' => $response->message
                ]
            );
        }

        return Response::json(
            [
                'status' => 'success',
                'response' => [],
                'message' => 'nothing to return'
            ]
        );
    }

    public function dismissThemeRoomNotificationMessage(Request $request)
    {
        $data = $request->all();
        $data['person_id'] = $this->person_id;
        $data['room_id'] = $data['room_id'];
        $response = json_decode(Api::post('api/v1/virtual-rooms/dismiss-theme-room-dot-notification-message', $data));

        return Response::json(
            [
                'status' => 'success',
                'response' => $response,
                'message' => $response->message
            ]
        );
    }
}
