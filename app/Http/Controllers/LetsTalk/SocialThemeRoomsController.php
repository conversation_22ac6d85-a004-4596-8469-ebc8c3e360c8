<?php

namespace App\Http\Controllers\LetsTalk;

use Carbon\Carbon;
use App\Models\Api;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Session;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;

class SocialThemeRoomsController extends BaseController
{
    public function __construct(Request $request)
    {
    }

    public function index(Request $request)
    {
        $personId = $this->getAuthPersonId();

        $response = json_decode(Api::get('api/v1/virtual-rooms/theme-rooms?person_id=' . $personId));
        $data['theme_rooms'] = $response->data;
        $data['pinnedRooms'] = $this->getPinnedRooms();

        return view('lets-talk-socials.theme-rooms', $data);
    }

    public function store(Request $request)
    {
        $data = $request->all();

        $apiParams['created_by_id'] = $this->getAuthPersonId() ? $this->getAuthPersonId() : null;
        $apiParams['role'] = 'virtual-rooms';

        $validator = $this->validateStore($data);
        if ($validator->fails()) {
            $errorResponse = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
            return Response::json($errorResponse, 400);
        }

        // meeting_time contains string value of the schedule frequency
        // $apiParams['meeting_time'] = [
        //     '1 day' => 'Every Day',
        //     '1 week' => 'Every Week',
        //     '2 week' => 'Every 2 Weeks',
        //     '1 month' => 'Every Month',
        //     '1 quarter' => 'Every Quarter',
        // ][$data['frequency']];

        $apiParams['room_code'] = $data['room_code'];
        if (!empty($data['start_date'])) {
            $startDateCarbon = Carbon::createFromFormat('d/m/Y', $data['start_date'], 'Europe/London');
            $apiParams['start_date'] = (string)Carbon::parse($startDateCarbon->format('Y-m-d') . ' ' . $data['start_time']);
            $apiParams['end_date'] = (string)Carbon::parse($startDateCarbon->format('Y-m-d') . ' ' . $data['end_time']);
        }

        $apiParams['duration_type'] = $data['duration_type'];

        if ($apiParams['duration_type'] === 'one-off') {
            $apiParams['frequency'] = 1;
            $apiParams['frequency_type'] = 'day';
            $apiParams['end_after_frequency'] = 1;
            $apiParams['end_after_frequency_type'] = 'day';
            $apiParams['start_time'] = $data['start_time'];
            $apiParams['end_time'] = $data['end_time'];
        } elseif ($apiParams['duration_type'] === 'on-a-schedule') {
            $apiParams['frequency'] = explode(' ', $data['frequency'])[0];
            $apiParams['frequency_type'] = explode(' ', $data['frequency'])[1];
            $apiParams['end_after_frequency'] = explode(' ', $data['end_after'])[0];
            $apiParams['end_after_frequency_type'] = explode(' ', $data['end_after'])[1];
            $apiParams['start_time'] = $data['start_time'];
            $apiParams['end_time'] = $data['end_time'];
            $apiParams['end_after'] = $data['end_after'];
        } else {
        }

        $response = json_decode(Api::post('api/v1/virtual-rooms/theme-rooms', $apiParams), true);
        return Response::json($response);
        if ($response['response'] === 'error') {
            return Response::json($response, 400);
        }

        return Response::json($response);
    }

    private function validateStore($data)
    {
        $rules = [
            'room_code' => 'required',
            'duration_type' => 'required',
            'start_date' => 'required_if:duration_type,on-a-schedule|required_if:duration_type,one-off',
            'start_time' => 'required_if:duration_type,on-a-schedule|required_if:duration_type,one-off',
            'end_time' => 'required_if:duration_type,on-a-schedule|required_if:duration_type,one-off',
            'frequency' => 'required_if:duration_type,on-a-schedule',
            'end_after' => 'required_if:duration_type,on-a-schedule',
        ];

        // // On update, we don't want to validate email, cause it cannot be updated
        // if($action === 'update') {
        //     unset($rules['email']);
        // }

        $messages = [
            'room_code.required' => 'Please refresh the page or contact the administrator.',
            'duration_type.required' => 'Please select duration type.',
            'start_date.required' => 'Please select start date.',
            'start_time.required' => 'Please select start time.',
            'frequency.required'  => 'Please select frequency.',
            'end_after.required' => 'Please select end after.',
        ];

        return Validator::make($data, $rules, $messages);
    }

    private function validateRoomCreation($data)
    {
        $rules = [
            'room_name_theme_rooms' => 'required',
            'room_theme_description' => 'required',
            'duration_type' => 'required',
            'start_date' => 'required_if:duration_type,on-a-schedule|required_if:duration_type,one-off',
            'start_time' => 'required_if:duration_type,on-a-schedule|required_if:duration_type,one-off',
            'end_time' => 'required_if:duration_type,on-a-schedule|required_if:duration_type,one-off',
            'frequency' => 'required_if:duration_type,on-a-schedule',
            'end_after' => 'required_if:duration_type,on-a-schedule',
        ];

        // // On update, we don't want to validate email, cause it cannot be updated
        // if($action === 'update') {
        //     unset($rules['email']);
        // }

        $messages = [
            'room_code.required' => 'Please refresh the page or contact the administrator.',
            'duration_type.required' => 'Please select duration type.',
            'start_date.required' => 'Please select start date.',
            'start_time.required' => 'Please select start time.',
            'frequency.required' => 'Please select frequency.',
            'end_after.required' => 'Please select end after.',
        ];

        return Validator::make($data, $rules, $messages);
    }

    public function showSpace($room_code)
    {
        $personId = $this->getAuthPersonId();

        $response = json_decode(Api::get('api/v1/virtual-rooms/theme-rooms/' . $room_code . '?person_id=' . $personId));

        if ($response->response === 'success') {

            $room = (object) $response->data->room;

            // Check if user has already voted
            if (isset($room->active_poll) && !empty($room->active_poll)) {
                $room->active_poll->current_user_voted = false;
                $room->active_poll->total_votes = count($room->active_poll->votes);

                if (!empty($room->active_poll->votes)) {
                    foreach ($room->active_poll->votes as $vote) {
                        if ($vote->created_by_person_id == $personId) {
                            $room->active_poll->current_user_voted = true;
                            break;
                        }
                    }
                }
            }

            if (isset($room->is_community) && (!$room->is_community || $room->show_messages)) {
                $responseMessages = json_decode(Api::get("api/v1/virtual-rooms/discussion/reply?room={$room->id}&person_id={$personId}&include_polls=1"));
                $room->messages   = isset($responseMessages->data->messages) ? $responseMessages->data->messages : [];
            }

            $creator = isset($response->data->room->cms) ? $response->data->room->cms : [];
            $upcomingSchedules = json_decode(json_encode($response->data->upcoming_schedules), true);
            // $announcementsResponse = json_decode(Api::post('api/v1/virtual-rooms/announcements', ['person_id' => $personId]));
            // $recentAnnouncements = isset($announcementsResponse->data) ? array_slice($announcementsResponse->data, 0, 5) : '';
            $joinRoomLink = isset($response->data->join_room_link) ? $response->data->join_room_link : "#";
        } else {

            // Check for previously added room that has been deleted
            $new_room_response = json_decode(Api::get('api/v1/virtual-rooms/theme-rooms/updated-schedule/' . $room_code));

            if (isset($new_room_response->response) && $new_room_response->response == 'success') {
                $updated_room_code = $new_room_response->updated_room_code;
                return Redirect::to('/virtual-rooms/theme-rooms/' . $updated_room_code);
            }

            if (isset($response->message) && $response->message == 'Social room not found!') {
                Session::flash('failure', json_encode(['title' => 'Warning', 'message' => 'This room is no longer active.']));
            }
            return Redirect::route('virtual-rooms.dashboard');
        }

        $isCyberVr = false;
        if (Session::get('can-access-cyber-vr') === true) {
            $isCyberVr = true;
        }
        return view('lets-talk-socials.space', compact('room', 'creator', 'upcomingSchedules', 'joinRoomLink', 'isCyberVr'));
    }

    public function communities()
    {

        $personId = $this->getAuthPersonId();

        Log::info("Communities 1");

        if (Cache::has('communities-rooms')) {
            $response = Cache::get('communities-rooms');
        } else {
            $response = json_decode(Api::get('api/v1/virtual-rooms/theme-rooms/communities?person_id=' . $personId));
            Cache::forever('communities-rooms', $response);
        }

        Log::info("Communities 2");
        $data['communities'] = $response->data;


        $data['pinnedRooms'] = $this->getPinnedRooms();
        Log::info("Communities 3");

        return view('lets-talk-socials.theme-rooms.communities', $data);
    }

    public function togglepin($room_id)
    {
        $data['person_id'] = $this->getAuthPersonId();
        $data['room_id'] = $room_id;
        $response = json_decode(Api::post('api/v1/virtual-rooms/theme-rooms/togglepin', $data));
        return $response->message;
    }

    public function roomCreation(Request $request)
    {
        $data = $request->all();
        $user = Session::get('socials-user');

        $validator = $this->validateRoomCreation($data);

        if ($validator->fails()) {
            $errorResponse = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
            return Response::json($errorResponse, 400);
        }

        $apiParams['created_by_id'] = $user['person_id'];
        $apiParams['room_theme_description'] = $data['room_theme_description'];
        $apiParams['room_name_theme_rooms'] = $data['room_name_theme_rooms'];
        $apiParams['duration_type'] = $data['duration_type'];


        if (!empty($data['start_date']) && !empty($data['end_time']) && !empty($data['duration_type'])) {
            $startDateCarbon = Carbon::createFromFormat('d/m/Y', $data['start_date'], 'Europe/London');
            $apiParams['start_date'] = (string)Carbon::parse($startDateCarbon->format('Y-m-d') . ' ' . $data['start_time']);
            $apiParams['end_date'] = (string)Carbon::parse($startDateCarbon->format('Y-m-d') . ' ' . $data['end_time']);
            $apiParams['duration_type'] = $data['duration_type'];
        }

        if ($apiParams['duration_type'] === 'one-off') {
            $apiParams['frequency'] = 1;
            $apiParams['frequency_type'] = 'day';
            $apiParams['end_after_frequency'] = 1;
            $apiParams['end_after_frequency_type'] = 'day';
        } elseif ($apiParams['duration_type'] === 'permanent') {
            $apiParams['frequency'] = 1;
            $apiParams['frequency_type'] = 'year';
            $apiParams['end_after_frequency'] = 1;
            $apiParams['end_after_frequency_type'] = 'year';
            $apiParams['start_date'] = (string) Carbon::now()->format('Y-m-d H:i:s');
            $apiParams['end_date'] = (string) Carbon::now()->format('Y-m-d H:i:s');
        } else {
            $apiParams['frequency'] = explode(' ', $data['frequency'])[0];
            $apiParams['frequency_type'] = explode(' ', $data['frequency'])[1];
            $apiParams['end_after_frequency'] = explode(' ', $data['end_after'])[0];
            $apiParams['end_after_frequency_type'] = explode(' ', $data['end_after'])[1];
        }

        $response = json_decode(Api::post('api/v1/virtual-rooms/room-creation', $apiParams));

        $hasError = $response->status === 'failed';

        $status = $hasError ? 'failure' : 'success';
        $title = $hasError ? 'Failed' : 'Successfully Requested';
        $message = $hasError ? $response->message : 'Your request to publish a new theme room has been successfully sent. You will receive a notification email as soon as your request is processed.';

        Session::flash($status, json_encode(['title' => $title, 'message' => $message]));

        if ($response->status == "success") {
            return Response::json(
                [
                    'status' => 'success',
                    'response' => $response->data,
                ]
            );
        }
        return Response::json(
            [
                'status' => 'failed',
            ]
        );
    }

    public function roomApproval($created_by_id, $room_id, $approval_type)
    {
        $response = json_decode(Api::get('api/v1/virtual-rooms/' . $created_by_id . '/' . $room_id . '/' . $approval_type . '/room-approval'));
        $approval_status = $response->approval_type == "approved" ? "Approved" : "Rejected";
        //        dd($response);
        if ($response->status == "success") {
            if (isset($response->has_been_updated) && $response->has_been_updated) {
                return Redirect::route('virtual-rooms.room-approval-status')->with(['approval_type' => $approval_status])->with(['has_been_updated_msg' => $response->message]);
            }
            return Redirect::route('virtual-rooms.room-approval-status')->with(['approval_type' => $approval_status]);
        }
        return Response::json(
            [
                'status' => 'failed'
            ]
        );
    }

    public function roomApprovalStatus()
    {
        return view('lets-talk-socials.theme-rooms.room-approval-status');
    }

    private function getPinnedRooms()
    {

        $apidata['person_id'] = $this->getAuthPersonId();

        $pinnedResponse = json_decode(Api::post('api/v1/virtual-rooms/theme-rooms/pinned', $apidata));

        $pinned = [];
        if (isset($pinnedResponse->data) && !empty($pinnedResponse->data)) {
            foreach ($pinnedResponse->data as $room) {
                $pinned[] = $room->id;
            }
        }
        return $pinned;
    }


    public function deleteSchedule(Request $request)
    {
        // $socialRoomId = $request->get('lt_social_room_id');
        // $scheduleDate = $request->get('date');
        $apiParams['person_id'] = $this->getAuthPersonId();

        $input = $request->all();

        $response = json_decode(
            Api::post(
                'api/v1/virtual-rooms/theme-rooms/delete-schedule',
                array_merge($apiParams, $input)
            ),
            true
        );

        $hasError = $response['response'] === 'error';
        $isEntry = isset($input['date']);

        $status = $hasError ? 'failure' : 'success';
        $title = $hasError ? 'Failed' : 'Successfully Deleted';
        $isEntry = $isEntry ? 'Your entry has been successfully deleted.' : 'Your schedule has been successfully deleted.';
        $message = $hasError ? 'Failed.' : $isEntry;

        Session::flash($status, json_encode(['title' => $title, 'message' => $message]));

        if ($hasError) {
            return Response::json($response, 400);
        }

        return Response::json($response);
    }

    public function deleteSpace($socialRoomId)
    {
        $response = json_decode(
            Api::delete('api/v1/virtual-rooms/theme-rooms/' . $socialRoomId . '?person_id=' . $this->getAuthPersonId()),
            true
        );

        $hasError = $response['response'] === 'error';

        $status = $hasError ? 'failure' : 'success';
        $title = $hasError ? 'Failed' : 'Successfully Deleted';
        $message = $hasError ? 'Failed.' : 'Your space has been successfully deleted.';

        Session::flash($status, json_encode(['title' => $title, 'message' => $message]));

        if ($hasError) {
            return Response::json($response, 400);
        }

        return Response::json($response);
    }

    private function getAuthPersonId()
    {
        $user = Session::get('socials-user');
        return isset($user['person_id']) ? $user['person_id'] : '';
    }

    public function syncCommunities()
    {
        $personId = $this->getAuthPersonId();

        $response = json_decode(Api::get('api/v1/virtual-rooms/theme-rooms/sync-communities'));

        if ($response->response == 'success' && ($response->has_new_data === true)) {
            $communities = json_decode(Api::get('api/v1/virtual-rooms/theme-rooms/communities?person_id=' . $personId));
            Cache::forever('communities-rooms', $communities);
        }

        return Response::json($response);
    }
}
