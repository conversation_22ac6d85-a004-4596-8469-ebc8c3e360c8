<?php

namespace App\Http\Controllers\LetsTalk;

use App\Models\Api;
use Carbon\Carbon;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Session;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class SocialSltRoomsController extends BaseController
{
    const SLT_ROOM_TYPE = 6;

    public function __construct(Request $request)
    {
    }

    public function store(Request $request)
    {
        $data = $request->all();
        $personId = $this->getAuthPersonId();
        $apiParams['created_by_id'] = $this->getAuthPersonId() ? $this->getAuthPersonId() : null;
        $apiParams['role'] = 'virtual-rooms';

        $validator = $this->validateStore($data);
        if ($validator->fails()) {
            $errorResponse = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
            return Response::json($errorResponse, 400);
        }

        // $apiParams['room_code']=$data['room_code'];
        if(!empty($data['start_date'])) {
            $startDateCarbon = Carbon::createFromFormat('d/m/Y', $data['start_date'], 'Europe/London');
            $apiParams['start_date'] = (string)Carbon::parse($startDateCarbon->format('Y-m-d') .' '. $data['start_time']);
            $apiParams['end_date'] = (string)Carbon::parse($startDateCarbon->format('Y-m-d') .' '. $data['end_time']);
        }

        // $apiParams['name'] = $data['name'];
        // $apiParams['description'] = $data['description'];
        $apiParams['duration_type'] = $data['duration_type'];
        $apiParams['name'] = $data['name'];

        if($apiParams['duration_type'] === 'one-off') {
            $apiParams['frequency'] = 1;
            $apiParams['frequency_type'] = 'day';
            $apiParams['end_after_frequency'] = 1;
            $apiParams['end_after_frequency_type'] = 'day';
        }elseif($apiParams['duration_type'] === 'permanent') {
            $apiParams['frequency'] = 1;
            $apiParams['frequency_type'] = 'year';
            $apiParams['end_after_frequency'] = 1;
            $apiParams['end_after_frequency_type'] = 'year';
            $apiParams['start_date'] = (string) Carbon::now()->format('Y-m-d H:i:s');
            $apiParams['end_date'] = (string) Carbon::now()->format('Y-m-d H:i:s');
        }else{
            $apiParams['frequency'] = explode(' ', $data['frequency'])[0];
            $apiParams['frequency_type'] = explode(' ', $data['frequency'])[1];
            $apiParams['end_after_frequency'] = explode(' ', $data['end_after'])[0];
            $apiParams['end_after_frequency_type'] = explode(' ', $data['end_after'])[1];
        }

        $response = json_decode(Api::post('api/v1/virtual-rooms/slt-rooms', $apiParams), true);


        $sltResponse = json_decode(Api::get('api/v1/virtual-rooms/slt-rooms?person_id='.$personId));
        if($sltResponse->status == 'success') {
            Cache::forever('sltResponse', $sltResponse);
        }


        return Response::json($response);

        if($response['response'] === 'error') {
            return Response::json($response, 400);
        }

        return Response::json($response);
    }

    private function validateStore($data)
    {
        $rules = [
            'name' => 'required',
            // 'room_code' => 'required',
            'duration_type' => 'required',
            'start_date' => 'required_if:duration_type,on-a-schedule|required_if:duration_type,one-off',
            'start_time' => 'required_if:duration_type,on-a-schedule|required_if:duration_type,one-off',
            'end_time' => 'required_if:duration_type,on-a-schedule|required_if:duration_type,one-off',
            'frequency' => 'required_if:duration_type,on-a-schedule',
            'end_after' => 'required_if:duration_type,on-a-schedule',
        ];

        // // On update, we don't want to validate email, cause it cannot be updated
        // if($action === 'update') {
        //     unset($rules['email']);
        // }

        $messages = [
            'name.required' => 'Please enter a subject.',
            'duration_type.required' => 'Please select duration type.',
            'start_date.required' => 'Please select start date.',
            'start_time.required' => 'Please select start time.',
            'frequency.required' => 'Please select frequency.',
            'end_after.required' => 'Please select end after.',
        ];

        return Validator::make($data, $rules, $messages);
    }

    public function showSpace(Request $request, $room_code)
    {
        $personId = $this->getAuthPersonId();


        $response = json_decode(Api::post('api/v1/virtual-rooms/slt-rooms/'.$room_code, ['person_id'=>$personId]));
        Log::info("Space 1");
        // $announcementsResponse = json_decode(Api::post('api/v1/virtual-rooms/announcements', ['person_id' => $personId]));
        $announcementsResponse = [];

        if ($response->response === 'success') {
            $room = (object) $response->data->room;
            if(isset($room->is_community) && !$room->is_community) {
                $responseMessages = json_decode(Api::get('api/v1/virtual-rooms/discussion/reply?room='.$room->id.'&person_id='.$personId));
                Log::info("Space 2");
                $room->messages=isset($responseMessages->data->messages) ? $responseMessages->data->messages : [];
            }

            $creator = isset($response->data->room->cms) ? $response->data->room->cms : [];
            $upcomingSchedules = json_decode(json_encode($response->data->upcoming_schedules), true);

            $recentAnnouncements = isset($announcementsResponse->data) ? array_slice($announcementsResponse->data, 0, 5) : '';
            $joinRoomLink = isset($response->data->join_room_link) ? $response->data->join_room_link : "#";

            if(Cache::has('sltResponse') && !$request->has('force_recache')) {
                $checkAuth = json_decode(Api::get('api/v1/virtual-rooms/slt-rooms?person_id='.$personId.'&check=auth'));
                if($checkAuth->status == 'success') {
                    $sltResponse = Cache::get('sltResponse');
                } else {
                    $sltResponse = $checkAuth;
                }
            } else {
                $sltResponse = json_decode(Api::get('api/v1/virtual-rooms/slt-rooms?person_id='.$personId));
                if($sltResponse->status == 'success') {
                    Cache::forever('sltResponse', $sltResponse);
                }
            }

            Log::info("Space 3");

            $leaders = !empty($sltResponse)?$sltResponse->data:[];
        } else {
             // Check for previously added room that has been deleted
             $new_room_response = json_decode(Api::get('api/v1/virtual-rooms/theme-rooms/updated-schedule/'.$room_code));

            if (isset($new_room_response->response) && $new_room_response->response=='success') {
                $updated_room_code = $new_room_response->updated_room_code;
                return Redirect::to('/virtual-rooms/theme-rooms/'.$updated_room_code);
            }
        }
        return view('lets-talk-socials.space', compact('room', 'creator', 'upcomingSchedules', 'recentAnnouncements', 'joinRoomLink', 'leaders'));
    }

    private function validateCreateSchedule($data)
    {
        $rules = [
            'duration_type' => 'required',
            'start_date' => 'required',
            'start_time' => 'required',
            'end_time' => 'required',
            'frequency' => 'required_if:duration_type,on-a-schedule',
            'end_after' => 'required_if:duration_type,on-a-schedule',
        ];

        // // On update, we don't want to validate email, cause it cannot be updated
        // if($action === 'update') {
        //     unset($rules['email']);
        // }

        $messages = [
            'duration_type.required' => 'Please select duration type.',
            'start_date.required' => 'Please select start date.',
            'start_time.required' => 'Please select start time.',
            'frequency.required' => 'Please select frequency.',
            'end_after.required' => 'Please select end after.',
        ];

        return Validator::make($data, $rules, $messages);
    }

    public function createSchedule(Request $request, $roomId)
    {
        $data = $request->all();

        $user = Session::get('socials-user');
        $personId = $user['person_id'];

        $validator = $this->validateCreateSchedule($data);
        if ($validator->fails()) {
            $errorResponse = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
            return Response::json($errorResponse, 400);
        }

        $apiParams['person_id'] = $this->getAuthPersonId();
        $apiParams['lt_social_room_id'] = $roomId;

        $startDateCarbon = Carbon::createFromFormat('d/m/Y', $data['start_date'], 'Europe/London');
        $apiParams['start_date'] = (string)Carbon::parse($startDateCarbon->format('Y-m-d') .' '. $data['start_time']);
        $apiParams['end_date'] = (string)Carbon::parse($startDateCarbon->format('Y-m-d') .' '. $data['end_time']);
        $apiParams['duration_type'] = $data['duration_type'];
        if($apiParams['duration_type'] === 'one-off') {
            $apiParams['frequency'] = 1;
            $apiParams['frequency_type'] = 'day';
            $apiParams['end_after_frequency'] = 1;
            $apiParams['end_after_frequency_type'] = 'day';
        } else {
            $apiParams['frequency'] = explode(' ', $data['frequency'])[0];
            $apiParams['frequency_type'] = explode(' ', $data['frequency'])[1];
            $apiParams['end_after_frequency'] = explode(' ', $data['end_after'])[0];
            $apiParams['end_after_frequency_type'] = explode(' ', $data['end_after'])[1];
        }

        $url = sprintf('api/v1/virtual-rooms/slt-rooms/%s/create-schedule', $roomId);

        $response = json_decode(Api::post($url, $apiParams), true);

        $hasError = $response['response'] === 'error';
        $status = $hasError ? 'failure' : 'success';
        $title = $hasError ? 'Failed' : 'Successfully Created';
        $message = $hasError ? 'Failed.' : 'Your meetings have been successfully created.';

        if($status == 'success') {
            $sltResponse = json_decode(Api::get('api/v1/virtual-rooms/slt-rooms?person_id='.$personId));
            if($sltResponse->status == 'success') {
                Cache::forever('sltResponse', $sltResponse);
            }
        }

        Session::flash($status, json_encode(['title' => $title, 'message' => $message]));

        if ($hasError) {
            return Response::json($response, 400);
        }

        return Response::json($response);
    }

    public function deleteSchedule(Request $request)
    {
        // $socialRoomId = $request->get('lt_social_room_id');
        // $scheduleDate = $request->get('date');
        $apiParams['person_id'] = $this->getAuthPersonId();

        $input = $request->all();

        $response = json_decode(
            Api::post(
                'api/v1/virtual-rooms/slt-rooms/delete-schedule',
                array_merge($apiParams, $input)
            ), true
        );

        $hasError = $response['response'] === 'error';
        $isEntry = isset($input['date']);

        $status = $hasError ? 'failure' : 'success';
        $title = $hasError ? 'Failed' : 'Successfully Deleted';
        $isEntryMessage = $isEntry ? 'Your entry has been successfully deleted.' : 'Your schedule has been successfully deleted.';
        $message = $hasError ? 'Failed.' : $isEntryMessage;

        if($status == 'success') {
            $sltResponse = json_decode(Api::get('api/v1/virtual-rooms/slt-rooms?person_id='.$this->getAuthPersonId()));
            if($sltResponse->status == 'success') {
                Cache::forever('sltResponse', $sltResponse);
            }
        }

        Session::flash($status, json_encode(['title' => $title, 'message' => $message]));

        if ($hasError) {
            return Response::json($response, 400);
        }

        return Response::json($response);
    }

    public function deleteSpace(Request $request, $socialRoomId)
    {
        $response = json_decode(
            Api::delete('api/v1/virtual-rooms/slt-rooms/' . $socialRoomId . '?person_id=' . $this->getAuthPersonId()),
            true
        );

        $hasError = $response['response'] === 'error';

        $status = $hasError ? 'failure' : 'success';
        $title = $hasError ? 'Failed' : 'Successfully Deleted';
        $message = $hasError ? 'Failed.' : 'Your space has been successfully deleted.';

        if($status == 'success') {
            $sltResponse = json_decode(Api::get('api/v1/virtual-rooms/slt-rooms?person_id='.$this->getAuthPersonId()));
            if($sltResponse->status == 'success') {
                Cache::forever('sltResponse', $sltResponse);
            }
        }

        Session::flash($status, json_encode(['title' => $title, 'message' => $message]));

        if ($hasError) {
            return Response::json($response, 400);
        }

        return Response::json($response);
    }

    private function getAuthPersonId()
    {
        $user = Session::get('socials-user');
        return isset($user['person_id']) ? $user['person_id'] : '';
    }

    public function viewAllByCreator(Request $request)
    {
        $params = http_build_query(
            [
            'person_id' => $this->getAuthPersonId(),
            ]
        );

        $response = json_decode(Api::get('api/v1/virtual-rooms/slt-rooms/space-by-creator?'. $params), true);
        $slts = [];
        if ($response['response'] === 'success') {
            $slts = $response['data'];
        }
        // dd($slts);

        return view('lets-talk-socials.slt-view-all', compact('slts'));
    }

    public function showSltWith(Request $request, $withPersonId)
    {
        $personId = $this->getAuthPersonId();
        $params = http_build_query(
            [
            'person_id' => $this->getAuthPersonId(),
            'with_person_id' => $withPersonId,
            ]
        );

        if(Cache::has('sltResponse') && !$request->has('force_recache')) {
            $checkAuth = json_decode(Api::get('api/v1/virtual-rooms/slt-rooms?person_id='.$personId.'&check=auth'));
            if($checkAuth->status == 'success') {
                $sltResponse = Cache::get('sltResponse');
            } else {
                $sltResponse = $checkAuth;
            }
        } else {
            $sltResponse = json_decode(Api::get('api/v1/virtual-rooms/slt-rooms?person_id='.$personId));
            if($sltResponse->status == 'success') {
                Cache::forever('sltResponse', $sltResponse);
            }
        }
        $upcomingSchedules = !empty($sltResponse)?$sltResponse->data:[];
        $upcomingSchedules = array_filter(
            $upcomingSchedules, function ($room) use ($withPersonId) {
                $room->schedule = isset($room->nextsched) ? ['date' => $room->nextsched] : '';
                return isset($room->person_id) && isset($room->nextsched) && $room->person_id === $withPersonId;
            }
        );
        // dd($upcomingSchedules);

        $archiveResponse = json_decode(Api::get('api/v1/virtual-rooms/social-rooms/'.self::SLT_ROOM_TYPE.'/archive?'. $params), true);
        $archives = [];
        $withCms = [];
        if ($archiveResponse['response'] === 'success') {
            $archives = $archiveResponse['rooms'];
            $withCms = $archiveResponse['with_person_cms'];
        }

        $upcomingSchedules = json_decode(json_encode($upcomingSchedules), true);

        return view('lets-talk-socials.slt-with', compact('upcomingSchedules', 'archives', 'withCms'));
    }

}
