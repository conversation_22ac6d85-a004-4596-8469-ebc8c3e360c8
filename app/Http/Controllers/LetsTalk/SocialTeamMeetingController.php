<?php

namespace App\Http\Controllers\LetsTalk;

use Carbon\Carbon;
use App\Models\Api;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;

class SocialTeamMeetingController extends BaseController
{
    const VALID_MOBILE_NUMBER_REGEX = '/^[+][1-9][0-9]{6,14}/';

    public function store(Request $request)
    {
        $data = $request->all();
        $user =  Session::get('socials-user');
        $apiParams['created_by_id'] = isset($user['person_id']) ? $user['person_id'] : null;
        $apiParams['person_id'] = isset($data['person_id']) ? $data['person_id'] : null;
        $apiParams['role'] = 'virtual-rooms';
        $validator = $this->validateStore($data);
        $apiParams['name'] = $data['name'];

        if (isset($data['external_user_company'])) {
            $apiParams['external_user_company'] = $data['external_user_company'];
        }
        if (isset($data['broker_id'])) {
            $apiParams['broker_id'] = $data['broker_id'];
        }

        $dataKeys = join(',', array_keys($data));
        if (strpos($dataKeys, 'person_id') === false && empty($data['external_user_company'])) {
            $validator->errors()->add('person_id_0', 'Person id 0 does not exists.');
            $errorResponse = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
            return Response::json($errorResponse, 400);
        }

        if ($validator->fails()) {
            $errorResponse = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
            return Response::json($errorResponse, 400);
        }
        foreach ($data as $key => $value) {
            if (strpos($key, 'person_id') !== false) {
                $apiParams[$key] = $value;
            }
        }
        // // TODO: remove this. This is just for testing
        // // TODO: remove this. This is just for testing
        // // TODO: remove this. This is just for testing
        // if (!isset($apiParams['created_by_id'])) {
        //     $apiParams['created_by_id'] = '5f1ab85d312399328a57e302';
        // }
        // if (!$apiParams['person_id']) {
        //     $apiParams['person_id'] = '5ef3467031239970014119d2';
        // }
        if ($data['duration_type'] === 'permanent') {
            $startDateCarbon = Carbon::now();
            $apiParams['start_date'] = $startDateCarbon->toDateTimeString();
            $apiParams['end_date'] = $startDateCarbon->toDateTimeString();
        } else {
            $startDateCarbon = Carbon::createFromFormat('d/m/Y', $data['start_date'], 'Europe/London');
            $apiParams['start_date'] = (string)Carbon::parse($startDateCarbon->format('Y-m-d') . ' ' . $data['start_time']);
            $apiParams['end_date'] = (string)Carbon::parse($startDateCarbon->format('Y-m-d') . ' ' . $data['end_time']);
        }

        $apiParams['duration_type'] = $data['duration_type'];
        if ($apiParams['duration_type'] === 'one-off') {
            $apiParams['frequency'] = 1;
            $apiParams['frequency_type'] = 'day';
            $apiParams['end_after_frequency'] = 1;
            $apiParams['end_after_frequency_type'] = 'day';
        } else {
            $apiParams['frequency'] = explode(' ', $data['frequency'])[0];
            $apiParams['frequency_type'] = explode(' ', $data['frequency'])[1];
            $apiParams['end_after_frequency'] = explode(' ', $data['end_after'])[0];
            $apiParams['end_after_frequency_type'] = explode(' ', $data['end_after'])[1];
        }

        if (isset($data['external_users']) && !empty($data['external_users'])) {
            $apiParams['external_users'] = $data['external_users'];
        }

        // dd($apiParams);

        $response = json_decode(Api::post('api/v1/virtual-rooms/team-meeting', $apiParams), true);
        if (isset($response['response']) && $response['response'] === 'error') {
            return Response::json($response, 400);
        }

        return Response::json($response);
    }

    private function validateStore($data)
    {
        $rules = [
            'name' => 'required',
            // 'person_id_0' => 'required',
            'duration_type' => 'required',
            'start_date' => 'required_if:duration_type,on-a-schedule,one-off',
            'start_time' => 'required_if:duration_type,on-a-schedule,one-off',
            'end_time' => 'required_if:duration_type,on-a-schedule,one-off',
            'frequency' => 'required_if:duration_type,on-a-schedule',
            'end_after' => 'required_if:duration_type,on-a-schedule',
            'external_users' => 'array',
        ];

        // // On update, we don't want to validate email, cause it cannot be updated
        // if($action === 'update') {
        //     unset($rules['email']);
        // }

        $messages = [
            'name.required' => 'Please enter meeting title.',
            'duration_type.required' => 'Please select duration type.',
            'start_date.required_if' => 'Please select start date.',
            'start_time.required_if' => 'Please select start time.',
            'frequency.required' => 'Please select frequency.',
            'end_after.required' => 'Please select end after.',
        ];

        if (isset($data['external_users']) && !empty($data['external_users'])) {
            foreach ($data['external_users'] as $key => $externalUser) {
                $rules['external_users.' . $key . '.external_user'] = 'required';
                $rules['external_users.' . $key . '.external_user_email'] = 'required|email';
                $rules['external_users.' . $key . '.external_user_mobile'] = 'required|regex:' . self::VALID_MOBILE_NUMBER_REGEX;

                $messages['external_users.' . $key . '.external_user.required'] = 'Please enter Name.';
                $messages['external_users.' . $key . '.external_user_email.required'] = 'Please enter Email in a valid format.';
                $messages['external_users.' . $key . '.external_user_email.email'] = 'Please enter Email in a valid format.';

                $messages['external_users.' . $key . '.external_user_mobile.required'] = 'Please enter Mobile Phone in a valid format.';
                $messages['external_users.' . $key . '.external_user_mobile.regex'] = 'Please enter Mobile Phone in a valid format.';
            }
        }

        return Validator::make($data, $rules, $messages);
    }

    public function getUpcomingTeamMeetings()
    {
        $isRoomReady = false;
        $user = Session::get('socials-user');
        $personId = $user['person_id'];
        // TODO: remove this condition. for testing only

        $response = json_decode(Api::get('api/v1/virtual-rooms/team-meeting/upcoming?person_id=' . $personId), true);

        if ($response['response'] === 'error') {
            return Response::json($response, 400);
        }
        if (isset($response['data'][0]['schedule']['date'])) {
            $now = Carbon::now()->timezone($user['timezone']);
            $latestSchedule = Carbon::parse($response['data'][0]['schedule']['date'], $user['timezone']);
            $isRoomReady = (((int) $latestSchedule->diffInMinutes($now, false) >= -5)) && (((int) $latestSchedule->diffInMinutes($now, false) <= 60)) ?  true : false;
            // office_timezone
            $response['data'][0]['is_room_ready'] = $isRoomReady;
        }
        return Response::json($response);
    }

    public function showManage()
    {
        $user = Session::get('socials-user');
        $personId = $user['person_id'];

        // TODO: remove this. This is just for testing
        // TODO: remove this. This is just for testing
        // TODO: remove this. This is just for testing
        // if(!isset($personId)) {
        //     $personId = '5f1ab85d312399328a57e302';
        // }
        $upcomingSchedulesResponse = json_decode(Api::get('api/v1/virtual-rooms/team-meeting/upcoming?person_id=' . $personId . '&no_limit=true'), true);
        $upcomingSchedules = [];
        $permanentRooms = [];
        if ($upcomingSchedulesResponse['response'] === 'success') {
            $upcomingSchedules = $upcomingSchedulesResponse['data'];
            $permanentRooms = $upcomingSchedulesResponse['permanent_rooms'];
        }
        $upcomingSchedules = array_merge($upcomingSchedules, $permanentRooms);
        // dd($upcomingSchedules);

        $socialRoomTypeData = [];
        $socialRoomType = 5;
        $teamsResponse = json_decode(Api::get('api/v1/virtual-rooms/social-rooms/' . $socialRoomType . '/archive?person_id=' . $personId), true);
        $teams = [];
        if ($teamsResponse['response'] === 'success') {
            $teams = $teamsResponse['rooms'];
        }

        return view('lets-talk-socials.manage-team', compact('upcomingSchedules', 'teams', 'socialRoomTypeData'));
    }

    public function showSpace($room)
    {
        $personId = $this->getAuthPersonId();
        $apiParams = $this->getAuthUserIdentifier();

        $isCustomer = isset(Session::get('socials-customer')['person_id']);

        $response = json_decode(Api::post('api/v1/virtual-rooms/team-meeting/' . $room, $apiParams));
        // $announcementsResponse = $isCustomer ? [] : json_decode(Api::post('api/v1/virtual-rooms/announcements', ['person_id' => $personId]));
        // $socialRoomType = json_decode(Api::get('api/v1/virtual-rooms/'.html_entity_decode('Team Meetings').'/social-room-type') , true);
        $roomUpcomingSchedule = [];
        $colleagueDetails = [];
        $colleague = [];

        if ($response->response === 'success') {
            $room = $response->data->room;
            if (empty($room)) {

                $status = 'failure';
                $title = 'Access denied';
                $message = 'You don\'t have access to this team meeting.';

                Session::flash($status, json_encode(['title' => $title, 'message' => $message]));

                if ($isCustomer) {
                    return Redirect::route('virtual-rooms.customer-dashboard');
                }

                return Redirect::route('virtual-rooms.dashboard');
            }

            // Check if user has already voted
            if (isset($room->active_poll) && !empty($room->active_poll)) {
                $room->active_poll->current_user_voted = false;
                $room->active_poll->total_votes = count($room->active_poll->votes);

                if (!empty($room->active_poll->votes)) {
                    foreach ($room->active_poll->votes as $vote) {
                        if ($vote->created_by_person_id == $personId) {
                            $room->active_poll->current_user_voted = true;
                            break;
                        }
                    }
                }
            }

            // dd($room);

            $responseMessages = json_decode(Api::get("api/v1/virtual-rooms/discussion/reply?room={$room->id}&person_id={$personId}&include_polls=1"));
            $creator = $response->data->room->cms;
            if (is_array($creator) && empty($creator)) {
                $creator = (object)[];
                $creator->person_id = "";
                $creator->name = "A deleted user";
                $creator->profile_picture = "";
            }
            $upcomingSchedules = json_decode(json_encode($response->data->upcoming_schedules), true);

            $room->messages = isset($responseMessages->data->messages) ? $responseMessages->data->messages : [];
            // $recentAnnouncements = isset(->data) ? array_slice($announcementsResponse->data, 0, 5) : [];
            $joinRoomLink = isset($response->data->join_room_link) ? $response->data->join_room_link : "#";
            $colleague = (array) $room->social_room_participants[0];
            $brokerCompanyLogo = isset($response->data->broker_company_logo) && !empty($response->data->broker_company_logo) ? $response->data->broker_company_logo : "";
        } else {

            // Check for previously added room that has been deleted
            $new_room_response = json_decode(Api::get('api/v1/virtual-rooms/theme-rooms/updated-schedule/' . $room));

            if (isset($new_room_response->response) && $new_room_response->response == 'success') {
                $updated_room_code = $new_room_response->updated_room_code;
                return Redirect::to('/virtual-rooms/team-meeting-rooms/' . $updated_room_code);
            }

            if (isset($response->message) && $response->message == 'Social room not found!') {
                Session::flash('failure', json_encode(['title' => 'Warning', 'message' => 'This room is no longer active.']));
            }
            if ($isCustomer) {
                return Redirect::route('virtual-rooms.customer-dashboard');
            } else {
                return Redirect::route('virtual-rooms.dashboard');
            }
        }

        // return Response::json($response);

        return view('lets-talk-socials.space', compact('room', 'creator', 'colleague', 'upcomingSchedules', 'joinRoomLink', 'brokerCompanyLogo', 'isCustomer'));
    }

    public function getRepresentatives()
    {
        $response = json_decode(Api::get('api/v1/virtual-rooms/team-meeting/representatives'), true);

        if ($response['response'] === 'success') {
            return Response::json($response);
        }

        return Response::json([$response]);
    }

    private function getAuthPersonId()
    {
        if (isset(Session::get('socials-user')['person_id'])) {
            return Session::get('socials-user')['person_id'];
        } else if (isset(Session::get('socials-customer')['person_id'])) {
            return Session::get('socials-customer')['person_id'];
        }
        return null;
    }

    private function getAuthUserIdentifier()
    {
        $personId = $this->getAuthPersonId();
        if (isset($personId)) {
            return [
                'person_id' => $personId,
                'is_customer' => isset(Session::get('socials-customer')['person_id']),
                'email' => isset(Session::get('socials-customer')['customer_email']) ? Session::get('socials-customer')['customer_email'] : ''
            ];
        }
        return [];
    }

    public function getSocialRoomWithId($id)
    {
        $response = json_decode(Api::get('api/v1/virtual-rooms/team-meeting/' . $id . '/social-room'), true);
        return Response::json($response);
    }

    private function validateEdit($data)
    {
        $rules = [
            'external_users' => 'array',
        ];

        if (isset($data['external_users']) && !empty($data['external_users'])) {
            foreach ($data['external_users'] as $key => $externalUser) {
                $rules['external_users.' . $key . '.external_user'] = 'required';
                $rules['external_users.' . $key . '.external_user_email'] = 'required|email';
                $rules['external_users.' . $key . '.external_user_mobile'] = 'required|regex:' . self::VALID_MOBILE_NUMBER_REGEX;

                $messages['external_users.' . $key . '.external_user.required'] = 'Please enter Name.';
                $messages['external_users.' . $key . '.external_user_email.required'] = 'Please enter Email in a valid format.';
                $messages['external_users.' . $key . '.external_user_email.email'] = 'Please enter Email in a valid format.';

                $messages['external_users.' . $key . '.external_user_mobile.required'] = 'Please enter Mobile Phone in a valid format.';
                $messages['external_users.' . $key . '.external_user_mobile.regex'] = 'Please enter Mobile Phone in a valid format.';
            }
        }

        return Validator::make($data, $rules, isset($messages) ? $messages : []);
    }

    public function editRoomTitleMeeting(Request $request, $id)
    {
        $data = $request->all();

        if (empty($data['room_name'])) {
            Session::flash('error', json_encode(['title' => 'Edit Failed', 'message' => 'Room name cannot be empty']));
            return;
        }

        $response = json_decode(
            Api::post('api/v1/virtual-rooms/team-meeting/' . $id . '/edit-room-title', $data),
            true
        );

        $status = 'success';
        $title = 'Successfully Edited';
        $message = 'Your team meeting has been successfully edited.';

        Session::flash($status, json_encode(['title' => $title, 'message' => $message]));

        return Response::json($response);
    }

    public function editTeamMeeting(Request $request, $id)
    {
        $data = $request->all();
        $response = json_decode(
            Api::post('api/v1/virtual-rooms/team-meeting/' . $id . '/edit-social-room', $data),
            true
        );

        $validator = $this->validateEdit($data);

        if ($validator->fails()) {
            $errorResponse = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
            return Response::json($errorResponse, 400);
        }

        $status = 'success';
        $title = 'Successfully Edited';
        $message = 'Your team meeting has been successfully edited.';

        Session::flash($status, json_encode(['title' => $title, 'message' => $message]));

        return Response::json($response);
    }

    public function getBrokers(Request $request)
    {
        $search = $request->get('search', '');
        $page = $request->get('page', 1);
        $limit = 9999999;

        $response = json_decode(Api::get(sprintf('/api/v1/brokers/all/%d/%d', $page, $limit)), true);

        if ($response['response'] === 'success') {
            return Response::json($response);
        }

        return Response::json([$response]);
    }
}
