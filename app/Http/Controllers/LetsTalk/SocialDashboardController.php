<?php

namespace App\Http\Controllers\LetsTalk;

use App\Models\Api;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Session;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Response;

class SocialDashboardController extends BaseController
{
    public function __construct(Request $request)
    {
    }

    public function home(Request $request)
    {
        $user = Session::get('socials-user');
        // dd($user);
        $personId = $user['person_id'];
        $apidata['person_id'] = $personId;

        $socialSpaceRoomsResponse = json_decode(Api::get('api/v1/virtual-rooms/social-space-rooms?person_id=' . $personId));

        $personCategories = (isset($socialSpaceRoomsResponse->response->personCategories)) ? $socialSpaceRoomsResponse->response->personCategories : [];

        $pinnedResponse = json_decode(Api::post('api/v1/virtual-rooms/theme-rooms/pinned', $apidata));
        Log::info("SS3");
        if (Cache::has('sltResponse') && !$request->has('force_recache')) {
            $checkAuth = json_decode(Api::get('api/v1/virtual-rooms/slt-rooms?person_id=' . $personId . '&check=auth'));
            if ($checkAuth->status == 'success') {
                $sltResponse = Cache::get('sltResponse');
            } else {
                $sltResponse = $checkAuth;
            }
        } else {
            $sltResponse = json_decode(Api::get('api/v1/virtual-rooms/slt-rooms?person_id=' . $personId));
            if ($sltResponse->status == 'success') {
                Cache::forever('sltResponse', $sltResponse);
            }
        }
        Log::info("SS4");
        if (isset($sltResponse->auth_users_slt)) {
            $authUsersSlt = $sltResponse->auth_users_slt;
            $data['is_auth_user_slt'] = !in_array($personId, $authUsersSlt) ? false : true;
        } else {
            $data['is_auth_user_slt'] = false;
        }

        $data['is_slt_space_empty'] = isset($sltResponse->is_slt_space_empty) ? $sltResponse->is_slt_space_empty : true;
        $data['leaders'] = !empty($sltResponse) ? $sltResponse->data : [];
        $data['pinnedRooms'] = !empty($pinnedResponse) ? $pinnedResponse->data : [];
        $data['user_person_id'] = $personId;
        $data['social_spaces'] = isset($socialSpaceRoomsResponse->response) ? $socialSpaceRoomsResponse->response : '';
        $data['person_categories'] = $personCategories;

        return view('lets-talk-socials.index', $data);
    }

    public function announcementsForDashboard(Request $request)
    {
        //$data['data'] = !$request->has('force_recache') ? $this->getAnnouncemntsData('refreshed') : $this->getAnnouncemntsData('cached');
        $data = $request->all();
        $data['data'] = $this->getAnnouncemntsData('cached', $data['categories'] ?? []);
        $data['announcements'] = $data['data'];
        return view('lets-talk-socials.announcements.announcements', $data);
    }

    private function getAnnouncemntsData($type = 'refreshed', $categories = [])
    {
        $user = Session::get('socials-user');
        $personId = $user['person_id'];
        $email = $user['email'];

        $apiData = [
            'person_id' => $personId,
            'page' => 1,
            'limit' => 8,
            'type'  => $type,
            'promoted' => 'yes'
        ];

        if (Cache::has('allAnnouncementsResponse')) {
            $announcementsResponse = Cache::get('allAnnouncementsResponse');

            $finalAnnouncementsList = [];
            foreach ($announcementsResponse->data as $key => $value) {
                foreach ($value->category as $category) {
                    if (in_array($category->name, $categories)) {
                        $finalAnnouncementsList[$key] = $value;
                    }
                }
            }

            $announcementsResponse->data = $finalAnnouncementsList;
        } else {
            $announcementsResponse = json_decode(Api::post('api/v1/virtual-rooms/announcements', $apiData));
        }

        $announcements = isset($announcementsResponse->data) ? $announcementsResponse->data : [];

        //$promotedAnnouncementResponse = json_decode(Api::post('api/v1/virtual-rooms/announcements/promoted', ['person_id' => $personId, 'type'  => $type]));
        $promotedAnnouncement = isset($announcementsResponse->promoted) ? $announcementsResponse->promoted : [];

        $latestAnnouncements = !empty($announcements) ? array_filter(
            $announcements,
            function ($announcement) use ($promotedAnnouncement) {
                return isset($promotedAnnouncement->_id) && isset($announcement->_id) ? $announcement->_id != $promotedAnnouncement->_id : true;
            }
        ) : '';
        Log::info("SS4.2");
        $isEditorResponse = json_decode(Api::post('api/v1/virtual-rooms/announcements/editor', ['email' => $email]));
        Log::info("SS4.3");
        $editUrl = $isEditorResponse->data->url;

        $bigAnnouncements = !empty($latestAnnouncements) ? array_slice($latestAnnouncements, 0, 3) : '';
        $smallAnnouncements = !empty($latestAnnouncements) ? array_slice($latestAnnouncements, 3, 5) : '';

        return json_decode(
            json_encode(
                [
                    'bigAnnouncements' => $bigAnnouncements,
                    'smallAnnouncements' => $smallAnnouncements,
                    'promoted' => $promotedAnnouncement,
                    'editUrl' => $editUrl
                ]
            )
        );
    }

    public function socialRooms(Request $request)
    {
        $data = $request->all();
        $response = json_decode(Api::post('api/v1/virtual-rooms/social-rooms', $data));

        return Response::json(
            [
                'status' => 'success',
                'response' => $response->response,
                'message' => null
            ]
        );
    }

    public function joinRoom(Request $request)
    {
        $data = $request->all();
        $user =  Session::get('socials-user');
        $data['person_id'] = $user['person_id'];

        $first_name = (isset($user['first_name']) && !empty($user['first_name'])) ? $user['first_name'] : '';
        $last_name = (isset($user['last_name']) && !empty($user['last_name'])) ? $user['last_name'] : '';

        $data['user_name'] = $first_name . '-' . $last_name;
        $data['role'] = (isset($user['role']) && !empty($user['role'])) ? $user['role'] : 'n/a';

        $response = json_decode(Api::post('api/v1/virtual-rooms/join-room', $data));
        $domain = config('app.client_url');
        //$generated_link =  $domain.'/virtual-rooms/v/'.$response->response->user_code.'/'.$response->response->room_code;
        $generated_link =  $domain . '/virtual-rooms/waiting-room/' . $response->response->room_code;

        return Response::json(
            [
                'status' => 'success',
                'response' => $generated_link,
                'message' => null
            ]
        );
    }

    public function waterCoolerRepresentatives($page = 1, $search = "")
    {
        $user =  Session::get('socials-user');
        $person_id = $user['person_id'];
        $response = json_decode(Api::get('api/v1/virtual-rooms/get-available-representative/' . $page . '/' . $search . '/' . $person_id));
        return Response::json(
            [
                'status' => 'success',
                'response' => $response,
                'message' => null
            ]
        );
    }

    public function chatWithMe(Request $request)
    {
        $data = $request->all();

        $person_id = $data['person_id'];
        $response = json_decode(Api::post('api/v1/virtual-rooms/chat-with-me', $data));

        $domain = config('app.client_url');
        //$generated_link =  $domain.'/virtual-rooms/v/'.$response->response->user_code.'/'.$response->response->room_code;
        $generated_link =  $domain . '/virtual-rooms/waiting-room/' . $response->response->room_code;

        return Response::json(
            [
                'status' => 'success',
                'response' => $generated_link,
                'person_id' => $person_id,
                'message' => null
            ]
        );
    }

    public function getExternalContacts(Request $request)
    {
        // search name query
        $name = $request->get('name');
        if (isset($name) && !empty($name) && $name == 'all') {
            $data = json_decode(Api::get('api/v1/virtual-rooms/external-contacts' . '?name=' . $name), false);
            return Response::json($data);
        }
        if (isset($name) && !empty($name)) {
            $data = json_decode(Api::get('api/v1/virtual-rooms/external-contacts' . '?name=' . $name), false);
            return Response::json($data);
        } else {
            $data = json_decode(Api::get('api/v1/virtual-rooms/external-contacts'), false)->data;
        }

        return view(
            'lets-talk.external-contacts',
            [
                'external_contacts' => $data
            ]
        );
    }

    public function cookieCheck($room_id)
    {
        $user_name = "";
        $email = "";

        if (Session::has('socials-user') && Session::has('socials-role')) {
            $social_user = Session::get('socials-user');
            $user_name = $social_user['name'];
            $email = $social_user['email'];
        }

        $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' || $_SERVER['SERVER_PORT'] == 443) ? "https://" : "http://";
        $url = config('app.client_url') . '/virtual-rooms/waiting-room/' . $room_id . '?visited=true&user_name=' . urlencode($user_name) . '&email=' . urlencode($email);
        $finalUrl = $url;
        return Redirect::to($finalUrl);
    }
}
