<?php

namespace App\Http\Controllers\LetsTalk;

use App\Models\Api;
use App\Helpers\Helpers;
use Illuminate\Support\Arr;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Redirect;

class PublicCustomerVideoRecordController extends BaseController
{
    const ENCRYPT_SALT = '54d9e81e919ab45c2ae6';

    public function viewMyMessage(Request $request, $encodedId)
    {
        if (Session::has('socials-customer')) {
            $realdId = Helpers::decryptString($encodedId, self::ENCRYPT_SALT);
            return Redirect::route('virtual-rooms.customer-single-view-message', $realdId);
        }

        $creator_recipient_id = $request->get('creator_recipient_id');
        $business = $request->get('business');
        if (!$creator_recipient_id) {
            return view('lets-talk-socials.errors.404');
        }
        $response = json_decode(Api::get('api/v1/virtual-rooms/video-record/single-page-view/' . rawurlencode($encodedId) . '?creator_recipient_id=' . rawurlencode($creator_recipient_id)));

        $video = null;
        if (isset($response->status) && $response->status !== 'failed' && isset($response->response)) {
            $video = $response->response;
        } else {
            return view('lets-talk-socials.errors.404');
        }

        $creator_recipient_id = (int)Helpers::decryptString($creator_recipient_id, self::ENCRYPT_SALT);


        $publicUserDetails = Arr::first(
            $video->video_record_recipients,
            function ($recipient, $key) use ($creator_recipient_id) {
                return $recipient->id === $creator_recipient_id;
            }
        );

        $queryParams = http_build_query(
            [
                'token'             => 'T9UwlARffuUx6xyZkkmtyh6LsSLB2RWw',
                'video_message_id'  => $video->id,
                'user_email'        => $publicUserDetails->email,
                'person_id'         => 'external',
                'reply'             => 'creator',
                'business'          => $business ? $business : 'lsm'
            ]
        );

        $recordLink = config('app.client_url') . '/virtual-rooms/video-record?' . $queryParams;

        return view(
            'lets-talk-socials.single-view-message',
            [
                'video' => $video,
                'recordLink' => $recordLink,
                'business' => $business ? $business : 'lsm'
            ]
        );
    }
}
