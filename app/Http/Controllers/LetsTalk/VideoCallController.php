<?php

namespace App\Http\Controllers\LetsTalk;

use Carbon\Carbon;
use App\Models\Api;
use App\Models\FileUpload;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;

class VideoCallController extends BaseController
{
    const MINIMUM_PARTICIPANTS = 1;
    const VALID_MOBILE_NUMBER_REGEX = '/^[+][1-9][0-9]{6,14}/';

    public function __construct(Request $request, FileUpload $fileUpload)
    {
        $this->file = $fileUpload;
    }

    //INDEX
    public function index()
    {
        $login_type = Session::get('user')->login_type;
        $user_role = Session::get('user')->role;

        if (
            $login_type == 'underwriter'
            || $login_type == 'risk-engineer'
            || ($login_type == 'risk-control' && Session::get('user')?->isRoleAdminOrAccountManager())
            || ($login_type == 'external-surveyor-admin' && Session::get('user')?->isRoleAdminOrAccountManager())
        ) {
            return view('lets-talk.index');
        }

        App::abort(403, 'Unauthorized action.');
    }

    public function store(Request $request)
    {
        $data = $request->except('_token');

        $data['receptionist'] = "Receptionist";

        $data['receptionist_phone'] = "+************";

        $data['receptionist_email'] = "<EMAIL>";

        // $data['liberty_representative_phone'] = "+************";

        $data['client_email'] = $data['email'];

        $data = $this->validateParticipants($data);
        if (is_array($data)) {
            $data['created_by_id'] = Session::get('user')->id;
            $data['created_by_login_type'] = Session::get('user')->login_type;
            $data['created_by_role'] = Session::get('user')->role;

            $response = json_decode(Api::post(route('virtual-rooms.video-call.post', [], false), $data), true);

            // if everything is okay
            if ($response['response'] === 'success') {
                return redirect()->back()->with(
                    'success',
                    'Room has been successfully created, and participants were sent an invitation'
                );
            }

            // if there are problems with the provided numbers
            if (!empty($response['data'])) {
                return redirect()->back()->with(
                    'error',
                    'The following entries have an invalid number format. Please check and try again. <ul><li>'
                        . implode('</li><li>', $response['data']) . '</li></ul>'
                )->withInput($request->old());
            }

            // for any general issues
            return redirect()->back()->with('error', 'There was an error with creating the room or participants.');
        }

        return $data;
    }

    /**
     * Validates the video setup form
     *
     * @param  $data
     * @return |null
     */
    private function validateParticipants(Request $request, $data)
    {
        $empty = true;
        foreach ($data as $datum) {
            if (!empty($datum)) {
                $empty = false;
                break;
            }
        }

        if ($empty) {
            return redirect()->back()->with('error', 'At least two participants are required to setup a video call.');
        }

        $rules = [
            'client' => 'required_with:client_phone',
            'client_phone' => 'required_with:client|regex:' . self::VALID_MOBILE_NUMBER_REGEX,
            //'broker' => 'required_with:broker_phone',
            //'broker_phone' => 'required_with:broker|regex:' . self::VALID_MOBILE_NUMBER_REGEX,
            'liberty_staff' => 'required_with:liberty_staff_phone',
            'liberty_staff_phone' => 'required_with:liberty_staff|regex:' . self::VALID_MOBILE_NUMBER_REGEX,
        ];
        $messages = [
            'client.required_with' => 'The Client Name field is required when Client Mobile Number is present.',
            'client_phone.required_with' => 'The Client Mobile Number field is required when Client Name is present.',
            //'broker.required_with' => 'The Broker Name field is required when Broker Mobile Number is present.',
            //'broker_phone.required_with' => 'The Broker Mobile Number field is required when Broker Name is present.',
            'liberty_staff.required_with' => 'The Liberty Staff Name field is required when Liberty Staff Mobile Number is present.',
            'liberty_staff_phone.required_with' => 'The Liberty Staff Mobile Number field is required when Liberty Staff Name is present.',
            'client_phone.regex' => 'The Client Mobile Number phone format is invalid',
            //'broker_phone.regex' => 'The Client Mobile Number phone format is invalid',
            'liberty_staff_phone.regex' => 'The Client Mobile Number phone format is invalid',
        ];

        foreach ($data as $key => $datum) {
            if (str_contains($key, '_phone')) {
                $data[$key] = $this->cleanupNumber($datum);
            } else {
                $data[$key] = trim($datum);
            }
        }

        $validator = Validator::make($data, $rules, $messages);
        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput($request->old());
        }

        $phones = array_filter([$data['client_phone']]);
        if (count($phones) < self::MINIMUM_PARTICIPANTS) {
            return redirect()->back()->with(
                'error',
                'At least two participants are required to setup a video call.'
            )->withInput($request->old());
        }

        return $data;
    }

    private function cleanupNumber($number)
    {
        // remove all non-printable strings
        $number = preg_replace('/[^[:print:]]/', '', $number);
        $number = preg_replace('/\s/', '', $number);

        return $number;
    }

    public function schedule(Request $request)
    {
        $data['representatives'] = json_decode(
            Api::post(
                'api/v1/virtual-rooms/schedules',
                ['business' => Session::get('vr-session', 'lsm')]
            )
        )->data->liberty_representatives;
        return view('lets-talk.schedule', $data);
    }

    public function book(Request $request)
    {
        $data = $request->except('_token');

        $data['receptionist'] = "Receptionist";
        $data['receptionist_phone'] = "+639152527032";
        $data['receptionist_email'] = "<EMAIL>";
        $business = (isset($data['business']) && !empty($data['business']))
            ? $data['business']
            : Session::get('vr-session', 'lsm');

        $data['receptionist_email'] = "<EMAIL>";

        // $data['liberty_representative_phone'] = "+639152527032";

        if (empty($data['liberty_representative_phone'])) {
            unset($data['liberty_representative_phone']);
        }

        $data['booked_timezone'] = $data['liberty_representative_timezone'];

        $data['client_email'] = $data['email'];

        $data = $this->validateParticipantsReprecentatives($data);

        if (is_array($data)) {
            $schedule = explode('to', $data['schedule']);

            $timezone = $data['booked_timezone'] ?: 'Europe/London';
            $from = Carbon::parse($schedule[0], $timezone)->setTimezone('Europe/London');
            $to = Carbon::parse($schedule[1], $timezone)->setTimezone('Europe/London');
            $data['conference_date'] = $from->format('Y-m-d');
            $data['time_start'] = $from->format('H:i');
            $data['time_end'] = $to->format('H:i');

            $data['created_by_id'] = $data['email'];
            $data['created_by_login_type'] = 'client';
            $data['created_by_role'] = 'client';
            $data['booking_type'] = 'future';

            if (isset($data['guest_clients']) && !empty($data['guest_clients'])) {
                $data['guest_clients'] = json_encode($data['guest_clients']);
                $data['guest_clientPhones'] = json_encode($data['guest_clientPhones']);
                $data['guest_emails'] = json_encode($data['guest_emails']);
            }

            // return Api::post('api/v1/virtual-rooms/video-call', $data);

            $response = json_decode(Api::post('api/v1/virtual-rooms/video-call?business=' . $business, $data), true);

            // if everything is okay
            if ($response['response'] === 'success') {
                return response()->json(
                    [
                        'status' => 'success',
                        'message' => 'Room has been successfully created, and participants were sent an invitation.',
                    ]
                );
            }

            if ($response['response'] === 'fail' && !empty($response['data'])) {
                return response()->json(
                    [
                        'status' => 'error 404',
                        'messages' => $response['data'],
                    ],
                    404
                );
            }

            // for any general issues
            return response()->json(
                [
                    'status' => 'error 404',
                    'messages' => 'There was an error with creating the room or participants.',
                ],
                404
            );
        }

        return $data;
    }

    /**
     * Validates the video setup form
     *
     * @param  $data
     * @return |null
     */
    private function validateParticipantsReprecentatives($data)
    {
        $invalidMobileNumberMessage = 'Please enter Mobile Phone in a valid format.';

        $rules = [
            'client' => 'required',
            'company' => 'required',
            'email' => 'required|email',
            'client_phone' => 'required|regex:' . self::VALID_MOBILE_NUMBER_REGEX,
            'meeting_subject' => 'required',
            'schedule' => 'required',
        ];

        $messages = [
            'client.required' => 'Please enter Name.',
            'company.required' => 'Please enter Company name.',
            'email.required' => 'Please enter Email.',
            'client_phone.required' => $invalidMobileNumberMessage,
            'client_phone.regex' => $invalidMobileNumberMessage,
            'meeting_subject.required' => 'Please enter Meeting Subject.',
            'schedule.required' => 'Please select Schedule.',
        ];

        $guestErrorMessages = [];

        // Check if any guests are created (Dynamic Validation)
        if (isset($data['guest_clients'])) {
            // dd($data);
            foreach ($data['guest_clients'] as $key => $clients) {
                $rules['guest_clients.' . $key] = 'required';
                $guestErrorMessages['guest_clients.' . $key . '.required'] = 'Please enter Name.';
            }

            foreach ($data['guest_emails'] as $key => $clients) {
                $rules['guest_emails.' . $key] = 'required|email';
                $guestErrorMessages['guest_emails.' . $key . '.required'] = 'Please enter Email.';
                $guestErrorMessages['guest_emails.' . $key . '.email'] = 'Email address invalid or not recognised.';
            }

            foreach ($data['guest_clientPhones'] as $key => $clients) {
                $rules['guest_clientPhones.' . $key] = 'required|regex:' . self::VALID_MOBILE_NUMBER_REGEX;
                $guestErrorMessages['guest_clientPhones.' . $key . '.required'] = $invalidMobileNumberMessage;
                $guestErrorMessages['guest_clientPhones.' . $key . '.regex'] = $invalidMobileNumberMessage;
            }

            $messages = array_merge($messages, $guestErrorMessages);
        }

        foreach ($data as $key => $datum) {
            if (str_contains($key, '_phone')) {
                $data[$key] = $this->cleanupNumber($datum);
            } else {
                if (gettype($datum) != 'array') {
                    $data[$key] = trim($datum);
                }
            }
        }

        $errorMessages = [];

        $validator = Validator::make($data, $rules, $messages);

        if ($validator->fails()) {
            $errorMessages = $validator->errors()->toArray();
        }

        if (!empty($errorMessages)) {
            return response()->json(
                [
                    'status' => 'error 404',
                    'messages' => $errorMessages,
                ],
                404
            );
        }

        return $data;
    }

    public function representativeCallAvaialbility($date = null)
    {
        $params = [
            'business' => Session::get('vr-session', 'lsm'),
            'return' => 'no-availability',
        ];
        $libreps = json_decode(Api::post('api/v1/virtual-rooms/schedules', $params))->data->liberty_representatives;
        $notifiableRepresentatives = [];
        // filter representatives that has valid mobile phone
        if (isset($libreps)) {
            foreach ($libreps as $key => $representative) {
                if (isset($representative->mobile) && preg_match('/^[+][1-9][0-9]{6,14}$/', $representative->mobile)) {
                    $notifiableRepresentatives[$key] = $representative;
                }
            }
        }
        $data['representatives'] = $notifiableRepresentatives;
        return view('lets-talk.call-availability', $data);
    }

    public function getCallAvaialbilitySlots(Request $request, $date = null)
    {
        if (!isset($date)) {
            $schedDateStartCarbon = Carbon::parse('today 8am');
        } else {
            $schedDateStartCarbon = Carbon::parse($date);
        }

        $data['selected_date'] = $schedDateStartCarbon->copy();

        $params = [
            'timezone' => $request->get('timezone', 'Europe/London'),
            'date' => $schedDateStartCarbon->copy()->format('Y-m-d 00:00:00'),
            'business' => Session::get('vr-session', 'lsm'),
            'name' => $request->get('name')
                ? strtolower(trim($request->get('name')))
                : null,
            'office_title' => $request->get('office_title')
                ? strtolower(trim($request->get('office_title')))
                : null,
            'line_of_business' => $request->get('line_of_business')
                ? strtolower(trim($request->get('line_of_business')))
                : null,
            'business_function' => $request->get('business_function')
                ? strtolower(trim($request->get('business_function')))
                : null,
        ];

        $data['representatives'] = json_decode(
            Api::post(
                'api/v1/virtual-rooms/schedules',
                $params
            )
        )->data->liberty_representatives;

        $notifiableRepresentatives = [];
        // filter representatives that has valid mobile phone
        if (isset($data['representatives'])) {
            foreach ($data['representatives'] as $key => $representative) {
                if (isset($representative->mobile) && preg_match('/^[+][1-9][0-9]{6,14}$/', $representative->mobile)) {
                    $notifiableRepresentatives[$key] = $representative;
                }
            }
        }

        $data['prev_date'] = $schedDateStartCarbon->copy()->subWeekday()->format('Y-m-d 08:00:00');
        $data['next_date'] = $schedDateStartCarbon->copy()->addWeekDay()->format('Y-m-d 08:00:00');

        $data['representatives'] = $notifiableRepresentatives;

        $person_id = $request->get('person_id');

        if (isset($person_id) && !empty($person_id)) {
            return response()->json(
                [
                    'status' => 'success',
                    'message' => 'Your query has been successfull',
                    'response' => $data,
                ],
                200
            );
        } else {
            $html = view('lets-talk.call-availability-slots', $data)->render();
            return response()->json(
                [
                    'html' => $html,
                    'representatives' => $notifiableRepresentatives,
                ]
            );
        }
    }

    public function removeAllRecurringAvailability(Request $request)
    {
        $data = $request->all();
        $response = json_decode(Api::post('api/v1/virtual-rooms/remove-all-repeated-availability-slots', $data));
        return response()->json(
            [
                'status' => 'success',
                'message' => $response,
            ],
            200
        );
    }

    public function updatedRepresentativesData(Request $request, $date = null)
    {
        $timezone = $request->get('timezone', 'Europe/London');
        $params = [
            'date' => $date,
            'business' => Session::get('vr-session', 'lsm'),
            'timezone' => $timezone,
        ];
        $representatives = json_decode(
            Api::post(
                'api/v1/virtual-rooms/schedules',
                $params
            )
        )->data->liberty_representatives;
        return response()->json(
            [
                'representatives' => $representatives,
            ]
        );
    }

    public function createRepresentativeCallAvaialbility(Request $request, $person_id, $date_range)
    {
        $data['person_id'] = $person_id;

        $timezone = $request->get('timezone', 'Europe/London');

        $schedule = explode('to', $date_range);
        $from = Carbon::parse($schedule[0], $timezone)->setTimezone('Europe/London')->format("Y-m-d H:i:s");
        $to = Carbon::parse($schedule[1], $timezone)->setTimezone('Europe/London')->format("Y-m-d H:i:s");

        $data['date'] = implode(' to ', [$from, $to]);

        //return $data['date'];
        $data['is_enabled'] = $request->get('is_enabled');
        $response = json_decode(Api::post('api/v1/virtual-rooms/create-availability', $data));


        if ($response->response === 'success') {
            return response()->json(
                [
                    'status' => 'success',
                    'message' => $response->message,
                    'person_id' => $person_id,
                ]
            );
        } else {
            return response()->json(
                [
                    'status' => $response->response,
                    'message' => $response->message,
                ],
                400
            );
        }
    }

    public function setRecurringAvailability(Request $request)
    {
        $data = $request->all();
        $data = $this->validateRecurringAvailability($data);

        if (is_array($data)) {
            $start_date_time = $request->get('start_date_time');
            $end_date_time = $request->get('end_date_time');
            $start_time = date("H:i:s", strtotime($start_date_time));
            $end_time = date("H:i:s", strtotime($end_date_time));

            $timezone = $request->get('timezone');
            $person_id = $request->get('person_id');
            $day_of_recurrance = strtoupper(date("N", strtotime($start_date_time)));

            $start = Carbon::parse($start_date_time);
            $end = Carbon::parse($end_date_time);

            for ($date = $start; $date->lte($end); $date->addWeek()) {
                $recurring_date = $date->format('Y-m-d');

                $data['person_id'] = $person_id;

                $timezone = $request->get('timezone', 'Europe/London');

                $from = Carbon::parse(
                    $recurring_date . ' ' . $start_time,
                    $timezone
                )->setTimezone('Europe/London')->format("Y-m-d H:i:s");
                $to = Carbon::parse(
                    $recurring_date . ' ' . $end_time,
                    $timezone
                )->setTimezone('Europe/London')->format("Y-m-d H:i:s");

                $data['date'] = implode(' to ', [$from, $to]);

                $data['is_enabled'] = 'true';
                $data['is_recurring'] = 'true';

                json_decode(Api::post('api/v1/virtual-rooms/create-availability', $data));
            }
            return response()->json(
                [
                    'status' => 'success',
                    'messages' => 'Setting of recurring availability has been successfull',
                ],
                200
            );
        } else {
            return $data;
        }
    }

    /**
     * Validates the set recurring availability form
     *
     * @param  $data
     * @return |null
     */
    private function validateRecurringAvailability($data)
    {
        $rules = [
            'start_date_time' => 'required',
            'end_date_time' => 'required',
            'timezone' => 'required',
            'person_id' => 'required',
        ];

        $messages = [
            'start_date_time.required' => 'Please enter start date.',
            'end_date_time.required' => 'Please enter end date.',
        ];

        $errorMessages = [];

        $validator = Validator::make($data, $rules, $messages);

        if ($validator->fails()) {
            $errorMessages = $validator->errors()->toArray();
        }

        if (!empty($errorMessages)) {
            return response()->json(
                [
                    'status' => 'error 404',
                    'messages' => $errorMessages,
                ],
                404
            );
        }

        return $data;
    }

    public function getRepSchedules(Request $request, $person_id = null, $date = null)
    {
        $data = $request->data->{$person_id} ?? [];
        //print_r(Api::get('api/v1/virtual-rooms/rep-schedules/'.$person_id.'/'.rawurlencode($date).'?timezone='.$request->get('timezone','Europe/London')); exit;
        if ($person_id != null) {
            $response = json_decode(
                Api::get(
                    'api/v1/virtual-rooms/rep-schedules/' . $person_id . '/' . rawurlencode($date) . '?timezone=' . $request->get(
                        'timezone',
                        'Europe/London'
                    )
                )
            );
            return response()->json(
                [
                    'status' => 'success',
                    'data' => data_get($response, 'data.' . $person_id, []),
                    'person_id' => $person_id,
                ]
            );
        }

        return view('lets-talk.bookings', $data);
    }

    public function bookings($date = null)
    {
        if ($date == null) {
            $bookings = json_decode(
                Api::get(
                    'api/v1/virtual-rooms/get-bookings?business=' . Session::get(
                        'vr-session',
                        'lsm'
                    )
                )
            )->data;
            $data['bookings'] = $bookings->bookings;
            $data['representatives'] = $bookings->representatives;
        }

        return view('lets-talk.bookings', $data);
    }

    public function deleteBooking($booking_id)
    {
        $data['booking_id'] = $booking_id;
        $response = json_decode(Api::post('api/v1/virtual-rooms/delete-booking', $data))->response;
        if ($response === 'success') {
            return redirect()->back()->with('success', 'A booking has been deleted');
        }
    }

    public function checkCallNowBookings()
    {
        $response = json_decode(Api::get('api/v1/virtual-rooms/check-call-now-bookings'));

        return response()->json(
            [
                'status' => $response->response,
            ]
        );
    }

    public function downloadS3Link($uuid)
    {
        $res = json_decode(Api::get('api/v1/virtual-rooms/' . $uuid . '/getattachedfiles'));
        if ($res->response == 'success') {
            $link = $this->file->link($res->data->cloudname, '10 minutes', "vr_bucket");
            $filename = $res->data->filename;
            $tempStorage = tempnam(sys_get_temp_dir(), $filename);
            copy($link, $tempStorage);

            return Response::download($tempStorage, $filename);
        }

        return response()->json(
            [
                'status' => $res->response,
            ]
        );
    }

    public function receptionNotificationSubscription()
    {
        return view('lets-talk.reception-notification-subscription');
    }

    public function getLibRepByName(Request $request)
    {
        $name = $request->get('name');
        $response = json_decode(Api::get('api/v1/virtual-rooms/get-lib-rep?name=' . $name . '&business=' . Session::get('vr-session')));

        if ($response->status == 200) {
            return response()->json(
                [
                    'data' => $response->data,
                    'msg' => 'success',
                ]
            );
        }

        return response()->json(
            [
                'msg' => 'Error',
            ]
        );
    }

    private function bookOutlook($data)
    {
        $data['receptionist'] = "Receptionist";
        $data['receptionist_phone'] = "+639152527032";
        $data['receptionist_email'] = "<EMAIL>";

        //$data['liberty_representative_phone'] = "+639152527032";

        // if(empty($data['liberty_representative_phone'])) {
        //     unset($data['liberty_representative_phone']);
        // }
        $data['client_email'] = $data['attendee'];

        // $data = $this->validateParticipantsReprecentatives($data);

        if (is_array($data)) {
            $from = $data['meetingStart'];
            $to = $data['meetingEnd'];
            $data['conference_date'] = $from->format('Y-m-d');
            $data['time_start'] = $from->format('H:i');
            $data['time_end'] = $to->format('H:i');

            $data['created_by_id'] = $data['attendee'];
            $data['created_by_login_type'] = 'client';
            $data['created_by_role'] = 'client';
            $data['booking_type'] = 'future';

            // if( isset($data['guest_clients']) && !empty($data['guest_clients']) ) {
            //     $data['guest_clients'] = json_encode($data['guest_clients']);
            //     $data['guest_clientPhones'] = json_encode($data['guest_clientPhones']);
            //     $data['guest_emails'] = json_encode($data['guest_emails']);
            // }

            // return Api::post('api/v1/virtual-rooms/video-call', $data);
            $response = json_decode(
                Api::post(
                    'api/v1/virtual-rooms/video-call?business=' . Session::get(
                        'vr-session',
                        'lsm'
                    ),
                    $data
                ),
                true
            );
            return $response;
            // if everything is okay
            if ($response['response'] === 'success') {
                return response()->json(
                    [
                        'status' => 'success',
                        'message' => 'Room has been successfully created, and participants were sent an invitation.',
                    ]
                );
            }

            if ($response['response'] === 'fail' && !empty($response['data'])) {
                return response()->json(
                    [
                        'status' => 'error 404',
                        'messages' => $response['data'],
                    ],
                    404
                );
            }

            // for any general issues
            return response()->json(
                [
                    'status' => 'error 404',
                    'messages' => 'There was an error with creating the room or participants.',
                ],
                404
            );
        }

        return $data;
    }
}
