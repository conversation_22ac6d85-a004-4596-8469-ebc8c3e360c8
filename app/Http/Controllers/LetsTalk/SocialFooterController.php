<?php

namespace App\Http\Controllers\LetsTalk;

use App\Models\Api;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use App\Http\Controllers\BaseController;

class SocialFooterController extends BaseController
{
    public function index(Request $request)
    {
        $business = $request->get('business') ? $request->get('business') : 'lsm';

        // if(Cache::has($business . '_footer_html')) {
        //     return Cache::get($business . '_footer_html');
        // }

        $response = json_decode(Api::get('api/v1/cms-virtual-rooms/options?business' . $business));
        if (isset($response->data->options)) {
            $data['options'] = $response->data->options;
        } else {
            $data['options'] = (object) [];
        }

        $footerHtml = view('lets-talk-socials.includes.footer-content', $data)->render();

        Cache::put($business . '_footer_html', $footerHtml, 10);

        return $footerHtml;
    }
}
