<?php

namespace App\Http\Controllers\LetsTalk;

use App\Models\Api;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Response;

class SocialRoomPollsController extends BaseController
{
    public function store(Request $request, $roomCode)
    {
        $data = $request->only(['poll_description', 'poll_options']);
        $validator = \Illuminate\Support\Facades\Validator::make(
            $data,
            [
                'poll_description' => 'required|string',
                'poll_options'     => 'required|array',
                'poll_options.1'   => 'required|string',
                'poll_options.2'   => 'required|string',
                'poll_options.3'   => 'string|nullable',
                'poll_options.4'   => 'string|nullable',
                'poll_options.5'   => 'string|nullable',
                'poll_options.6'   => 'string|nullable',
                'poll_options.7'   => 'string|nullable',
                'poll_options.8'   => 'string|nullable',
                'poll_options.9'   => 'string|nullable',
                'poll_options.10'  => 'string|nullable',
            ],
            [
                'poll_options.1.required' => 'The poll choice #1 field is required.',
                'poll_options.2.required' => 'The poll choice #2 field is required.',
            ]
        );

        if ($validator->fails()) {
            return Response::json(
                [
                    'code'   => 422,
                    'errors' => $validator->messages(),
                ],
                422
            );
        }

        $user = Session::get('socials-user');
        $data['person_id'] = $user['person_id'];

        // Send API request
        $response = json_decode(
            Api::post(
                "api/v1/virtual-rooms/social-rooms/{$roomCode}/polls",
                $data
            )
        );

        // Process response and response to client accordingly
        if (!empty($response) && !empty($response->code)) {
            return Response::json(
                (array)$response,
                $response->code
            );
        } else {
            return Response::json(
                [
                    'code'   => 500,
                    'errors' => 'Error encountered. Please try again',
                ],
                500
            );
        }
    }

    public function update(Request $request, $roomCode, $pollId)
    {
        $data = $request->only(['poll_description', 'poll_options', 'status']);

        $validator = \Illuminate\Support\Facades\Validator::make(
            $data,
            [
                'poll_description' => 'required_without_all:status|string',
                'poll_options'     => 'required_without_all:status|array',
                'poll_options.1'   => 'required_without_all:status|string',
                'poll_options.2'   => 'required_without_all:status|string',
                'poll_options.3'   => 'string',
                'poll_options.4'   => 'string',
                'poll_options.5'   => 'string',
                'poll_options.6'   => 'string',
                'poll_options.7'   => 'string',
                'poll_options.8'   => 'string',
                'poll_options.9'   => 'string',
                'poll_options.10'  => 'string',
                'status'           => 'required_without_all:poll_description,poll_options|string'
            ],
            [
                'poll_options.1.required' => 'The poll choice #1 field is required.',
                'poll_options.2.required' => 'The poll choice #2 field is required.',
            ]
        );

        if ($validator->fails()) {
            return Response::json(
                [
                    'code'   => 422,
                    'errors' => $validator->messages(),
                ],
                422
            );
        }

        $user = Session::get('socials-user');
        $data['person_id'] = $user['person_id'];

        // Send API request
        $response = json_decode(
            Api::put(
                "api/v1/virtual-rooms/social-rooms/{$roomCode}/polls/{$pollId}",
                $data
            )
        );

        // Process response and response to client accordingly
        if (!empty($response) && !empty($response->code)) {
            return Response::json(
                (array)$response,
                $response->code
            );
        } else {
            return Response::json(
                [
                    'code'   => 500,
                    'errors' => 'Error encountered. Please try again',
                ],
                500
            );
        }
    }

    public function delete(Request $request, $roomCode, $pollId)
    {
        $user     = Session::get('socials-user');
        $personId = $user['person_id'];

        // Send API request
        $response = json_decode(
            Api::delete(
                "api/v1/virtual-rooms/social-rooms/{$roomCode}/polls/{$pollId}?person_id={$personId}"
            )
        );

        // Process response and response to client accordingly
        if (!empty($response) && !empty($response->code)) {
            return Response::json(
                (array)$response,
                $response->code
            );
        } else {
            return Response::json(
                [
                    'code'   => 500,
                    'errors' => 'Error encountered. Please try again',
                ],
                500
            );
        }
    }
}
