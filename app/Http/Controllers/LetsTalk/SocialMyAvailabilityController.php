<?php

namespace App\Http\Controllers\LetsTalk;

use App\Models\Api;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Response;

class SocialMyAvailabilityController extends BaseController
{
    public function __construct(Request $request)
    {
    }

    public function index(Request $request)
    {
        $user = Session::get('socials-user');
        $announcementsResponse = json_decode(Api::post('api/v1/virtual-rooms/announcements', ['person_id' => $user['person_id']]));
        $recentAnnouncements = isset($announcementsResponse->data) ? array_slice($announcementsResponse->data, 0, 5) : '';
        $data['business'] = Session::has('vr-business') ? Session::get('vr-business') : 'lsm';
        $data['representative'] = $user;
        $data['recentAnnouncements'] = isset($recentAnnouncements) ? $recentAnnouncements : '';

        return view('lets-talk-socials.my-availability', $data);
    }

    // public function getMyAvailability(Request $request)
    // {
    //     $person_id = Session::get('socials-user');
    //     $person_id = $person_id['person_id'];

    //     if ($person_id != null) {
    //         $response = json_decode(Api::get('api/v1/virtual-rooms/rep-schedules/' . $person_id . '/' . rawurlencode($date) . '?timezone=' . $request->get('timezone', 'Europe/London')));

    //         return Response::json(
    //             [
    //                 'status' => 'success',
    //                 'data' => isset($response->data->{$person_id}) ? $response->data->{$person_id} : [],
    //                 'person_id' => $person_id,
    //             ]
    //         );
    //     }
    // }
}
