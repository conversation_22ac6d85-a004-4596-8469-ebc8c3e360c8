<?php

namespace App\Http\Controllers\LetsTalk;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Redirect;

class SessionCheckerController extends BaseController
{
    public function authCheck(Request $request)
    {
        if (!Session::has('socials-user') && !Session::has('socials-role')) {
            $vr_check = 'false';
        } else {
            $vr_check = 'true';
        }
        $data = $request->all();

        $params = array_merge($request->except('url'), ['vr_check' => $vr_check]);
        $queryParams = http_build_query($params);

        $url = $data['url'] . '&' . $queryParams;
        return Redirect::to($url);
    }
}
