<?php

namespace App\Http\Controllers\LetsTalk;

use App\Models\Api;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Http\Controllers\BaseController;

class AnnouncementController extends BaseController
{
    public function index(Request $request)
    {
        $request = $request->all();
        $page = isset($request['page']) ? intval($request['page']) : 1;

        $user = Session::get('socials-user');
        $personId = $user['person_id'];
        $limit = 15;

        $apiData = [
            'person_id' => $personId,
            'page' => $page,
            'limit' =>  $limit
        ];

        $response = json_decode(Api::post('api/v1/virtual-rooms/announcements', $apiData));
        $announcements = $response->data;
        $totalPages = $response->total_pages;

        $data = [
            'announcements' => $announcements,
            'page' => $page,
            'total_pages' => $totalPages,
        ];
        return view('lets-talk-socials.announcements.archive', $data);
    }

    public function show(Request $request, $id)
    {
        $user = Session::get('socials-user');
        $personId = $user['person_id'];
        $force_recache_param = $request->has('force_recache') ? '&force_recache=' . $request->get('force_recache') : "";

        $announcementResponse = json_decode(Api::get('api/v1/virtual-rooms/announcements/' . $id . "?person_id=" . $personId . $force_recache_param));
        $announcement = $announcementResponse->data->announcement;
        // $announcementCategoryId = $announcement->category[0]->{'$oid'};
        // $announcementCategoryResponse = json_decode(Api::get('api/v1/virtual-rooms/announcements/categories/' . $announcementCategoryId));
        $announcementCategory = isset($announcement->category[0]) ? $announcement->category[0] : [];

        $author = isset($announcementResponse->data->author[0]) ? $announcementResponse->data->author[0] : [];

        $promotedAnnouncement = isset($announcementResponse->data->promoted) ? $announcementResponse->data->promoted : null;

        $responseMessages = json_decode(Api::get('api/v1/virtual-rooms/discussion/reply?room=' . $id . '&person_id=' . $personId));
        $room = (object)[];
        $room->id = $id;
        $room->messages = isset($responseMessages->data->messages) ? $responseMessages->data->messages : [];
        // $promotedAnnouncementResponse = json_decode(Api::post('api/v1/virtual-rooms/announcements/promoted', ['person_id' => $personId]));
        // $promotedAnnouncement = $promotedAnnouncementResponse->data;

        // $relatedAnnouncementsResponse = json_decode(Api::get('api/v1/virtual-rooms/announcements/' . $id . '/related'));

        // // dd($relatedAnnouncementsResponse);

        // $relatedAnnouncements = array_slice(array_filter($relatedAnnouncementsResponse->data, function ($announcement) use ($promotedAnnouncement, $id) {
        //     return isset($promotedAnnouncement) ? $announcement->_id != $promotedAnnouncement->_id && $announcement->_id != $id : $announcement->_id != $id;
        // }), 0, 5);

        return view(
            'lets-talk-socials.announcements.show',
            [
                'announcement' => $announcement,
                'author' => $author,
                'category' => $announcementCategory,
                'promoted' => $promotedAnnouncement,
                'room' => $room
                // 'relatedAnnouncements' => $relatedAnnouncements
            ]
        );
    }

    public function showRelatedAnnouncements(Request $request, $id)
    {
        $query = "?promoted_id=" . $request->get('promoted_id');
        $relatedAnnouncementsResponse = json_decode(Api::get('api/v1/virtual-rooms/announcements/' . $id . '/related' . $query));
        $relatedAnnouncements = $relatedAnnouncementsResponse->data;
        return view('lets-talk-socials.announcements.show-related', compact('relatedAnnouncements'));
    }

    public function announcementsForSpace(Request $request)
    {
        $data['announcements'] = !$request->has('force_recache') ? $this->getAnnouncemntsData('refreshed') : $this->getAnnouncemntsData('cached');
        $isCustomer = isset(Session::get('socials-customer')['person_id']);
        if ($isCustomer) {
            return "";
        }
        return view('lets-talk-socials.announcements.announcement-for-space', $data);
    }

    private function getAnnouncemntsData($type = 'refreshed')
    {
        $user = Session::get('socials-user');
        $personId = $this->getAuthPersonId();

        $isCustomer = isset(Session::get('socials-customer')['person_id']);

        $apiData = [
            'person_id' => $personId,
            'page' => 1,
            'limit' => 5,
            'type'  => $type
        ];

        $announcementsResponse = ($isCustomer) ? [] : json_decode(Api::post('api/v1/virtual-rooms/announcements', $apiData));
        $announcements = isset($announcementsResponse->data) ? $announcementsResponse->data : [];
        return $announcements;
    }


    private function getAuthPersonId()
    {
        if (isset(Session::get('socials-user')['person_id'])) {
            return Session::get('socials-user')['person_id'];
        } else if (isset(Session::get('socials-customer')['person_id'])) {
            return Session::get('socials-customer')['person_id'];
        }
        return null;
    }
}
