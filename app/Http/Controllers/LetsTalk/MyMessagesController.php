<?php

namespace App\Http\Controllers\LetsTalk;

use App\Models\Api;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Response;

class MyMessagesController extends BaseController
{
    public function index(Request $request)
    {
        $user = Session::get('socials-user');

        $data['person_id'] = $user['person_id'];
        $data['email'] = $user['email'];

        return view('lets-talk-socials.my-messages', $data);
    }

    public function videoMessages(Request $request)
    {
        $data = $request->all();
        $user = Session::get('socials-user');
        $data['person_id'] = $user['person_id'];
        $data['email'] = $user['email'];
        $data['timezone'] = $user['timezone'];
        $data['selected_page'] = ((isset($data['selected_page']) && !empty($data['selected_page'])) ? $data['selected_page'] : "1");
        $selected_page = $data['selected_page'];
        $config_url = config('app.client_url');

        $response = json_decode(Api::post('api/v1/virtual-rooms/video-record/my-messages', $data));
        $notifications = json_decode(Api::post('api/v1/virtual-rooms/video-record/red-dot-notifications', $data));

        return Response::json(
            [
                'status' => 'success',
                'response' => $response->response,
                'notifications' => $notifications->response,
                'page_count' => $response->page_count,
                'selected_page' => $selected_page,
                'config_url' => $config_url,
                'message' => null
            ]
        );
    }

    public function viewMyMessage(Request $request, $id)
    {
        // video-record/single-page-view/{id}
        $video = null;

        $user = Session::get('socials-user');

        $response = json_decode(Api::get('api/v1/virtual-rooms/video-record/single-page-view/' . $id . '?email=' . rawurlencode($user['email']) . '&timezone=' . rawurlencode($user['timezone'])));
        if (isset($response->status) && $response->status !== 'failed' && isset($response->response)) {
            $video = $response->response;
        } else {
            return view('lets-talk-socials.errors.404');
        }
        return view('lets-talk-socials.single-view-message')->with('video', $video);
    }

    public function getMyMessageMembers(Request $request)
    {
        $data = $request->all();
        $response = json_decode(Api::post('api/v1/virtual-rooms/video-record/my-messages-members', $data));

        return Response::json(
            [
                'status' => 'success',
                'response' => $response,
                'message' => null
            ]
        );
    }

    public function updateNotification(Request $request)
    {
        $data = $request->all();
        $user = Session::get('socials-user');
        $data['email'] = $user['email'];

        json_decode(Api::post('api/v1/virtual-rooms/video-record/update-notification', $data));

        return Response::json(
            [
                'status' => 'success',
                'message' => null
            ]
        );
    }

    public function checkNotifications(Request $request)
    {
        $data = $request->all();
        $user = Session::get('socials-user');
        $data['email'] = $user['email'];
        $response = json_decode(Api::post('api/v1/virtual-rooms/video-record/red-dot-notifications', $data));

        return Response::json(
            [
                'status' => 'success',
                'response' => $response->response,
                'message' => null
            ]
        );
    }

    public function downloadMessage(Request $request, $id)
    {
        $response = json_decode(Api::get('api/v1/virtual-rooms/video-record/single-page-view/' . $id));
        $main_url = $response->response->presigned_url;
        header('Content-Description: File Transfer');
        header('Content-Type: video/mp4');
        header('Cache-Control: must-revalidate');
        header('Pragma: public');
        header('Expires: 0');
        header('Content-disposition:attachment; filename="' . preg_replace('/.mp4$/', '', preg_replace('/[^A-Za-z0-9 _ .-]/', '_', $response->response->subject)) . '.mp4"');
        header("Content-Transfer-Encoding: binary");
        flush(); // Flush system output buffer
        ob_end_clean();
        readfile($main_url);
        exit();
        // $response = Response::download($tempImage, $filename);
        // return Response::json($response->response->presigned_url);
    }
}
