<?php

namespace App\Http\Controllers\LetsTalk;

use App\Models\Api;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;

class SocialRoomPollVotesController extends BaseController
{
    public function store(Request $request, $roomCode, $pollId)
    {
        $data = $request->only(['vote']);

        $validator = Validator::make(
            $data,
            [
                'vote' => 'required'
            ]
        );

        if ($validator->fails()) {
            return Response::json(
                [
                    'code'   => 422,
                    'errors' => $validator->messages(),
                ],
                422
            );
        }

        $user = Session::get('socials-user');

        // User is not staff. Check if customer.
        if (empty($user['person_id'])) {
            $user = Session::get('socials-customer');
        }

        $data['person_id'] = $user['person_id'];

        // Send API request
        $response = json_decode(
            Api::post(
                "api/v1/virtual-rooms/social-rooms/{$roomCode}/polls/{$pollId}/votes",
                $data
            )
        );

        // Process response and response to client accordingly
        if (!empty($response) && !empty($response->code)) {
            return Response::json(
                (array)$response,
                $response->code
            );
        } else {
            return Response::json(
                [
                    'code'   => 500,
                    'errors' => 'Error encountered. Please try again',
                ],
                500
            );
        }
    }
}
