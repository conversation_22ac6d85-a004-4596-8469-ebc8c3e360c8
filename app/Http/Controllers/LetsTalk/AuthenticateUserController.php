<?php

namespace App\Http\Controllers\LetsTalk;

use App\Models\Api;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;

class AuthenticateUserController extends BaseController
{
    public function login(Request $request)
    {
        return view('lets-talk-socials.login', ['body_class' => "vr-social-lm"]);
    }

    public function generateLink(Request $request)
    {
        $data = $request->all();
        $validator = $this->validateEmail($data);

        if ($validator->fails()) {
            return Redirect::back()->withErrors($validator)->withInput($request->old());
        }

        // generate Magic Link
        $response = json_decode(Api::post('api/v1/virtual-rooms/generate-link', $data));

        if (
            isset($response->response)
            && !empty($response->response)
            && $response->response === "success"
        ) {
            return Redirect::route('virtual-rooms.login')
                ->with(
                    'success-magic-link',
                    '<p>We have sent an email to the address registered with this account containing the magic link to access the Virtual Rooms dashboard.</p>'
                );
        }

        if (
            isset($response->response)
            && !empty($response->response)
            && $response->response === "fail"
        ) {
            return Redirect::route('virtual-rooms.login')
                ->with(
                    'error-magic-link',
                    $response->message
                );
        }

        return Redirect::route('virtual-rooms.login')
            ->with(
                'error',
                'Ooops.. Something went wrong...'
            );
    }

    public function authenticate(Request $request, $token)
    {
        // Check if token exist and
        $business = $request->get('business');
        $data['token'] = $token;

        if (!isset($business) && empty($business)) {
            return Redirect::route('virtual-rooms.login')
                ->with(
                    'error',
                    'The link is broken or invalid'
                );
        }

        $response = json_decode(Api::post('api/v1/virtual-rooms/authenticate', $data));

        if ($response->response === 'success' && !$response->socials_customer) {
            $socialUser = (array) $response->socials_user;
            $timezone = json_decode(Api::get('api/v1/virtual-rooms/staff/' . $socialUser['email']));
            $socialUser['name'] = $timezone->data->representative->name;
            $socialUser['email'] = $timezone->data->representative->email;
            $socialUser['mobile'] = $timezone->data->representative->mobile;
            $socialUser['office_number'] = $timezone->data->representative->office_number;
            $socialUser['profile_picture'] = $timezone->data->representative->profile_picture;
            $socialUser['job_title'] = $timezone->data->representative->job_title;
            $socialUser['timezone'] = $timezone->data->representative->office_timezone;

            $api = json_decode(Api::get('api/v1/liberty-users/auth/' . $timezone->data->representative->email));

            if ($api->response === 'success') {
                $user_type = 'liberty-user';
                $user = User::recast(
                    'User',
                    $api->data
                );

                Auth::login($user);

                $user->type = 'liberty-user';
                $user_role = $user->role;
                $is_aspen = $user->is_aspen;

                //print_r($user_type.' '.$user_role); exit;
                if (in_array($response->socials_user->person_id, config('app.cyber_virtual_rooms.can_access'))) {
                    Session::put('can-access-cyber-vr', true);
                }

                switch (true) {
                    case ($user_type == 'liberty-user' && $user_role == 'underwriter'):
                        $login_type = 'underwriter';
                        break;

                    case ($user_type == 'liberty-user' && $user_role == 'risk-engineer'):
                        $login_type = 'risk-engineer';
                        break;

                    case ($user_type == 'liberty-user' && $user_role == 'aspen-user' && $is_aspen == 1):
                        $login_type = 'aspen-user';
                        break;

                    case ($user_type == 'liberty-user' && $user_role == 'branch-admin' && $is_aspen == 1):
                        $login_type = 'aspen-branch-admin';
                        break;

                    case ($user_type == 'liberty-user' && $user_role == 'branch-user' && $is_aspen == 1):
                        $login_type = 'aspen-branch-user';
                        break;

                    case ($user_type == 'liberty-user' && $user_role == 'branch-admin' && $is_aspen == 0):
                        $login_type = 'branch-admin';
                        break;

                    case ($user_type == 'liberty-user' && $user_role == 'branch-user' && $is_aspen == 0):
                        $login_type = 'branch-user';
                        break;
                    case ($user_type == 'liberty-user' && $user_role == 'virtual-rooms'):
                        $login_type = 'virtual-rooms';
                        break;

                    case ($user_type == 'external-surveyor' && $user->isRoleAdminOrAccountManager()):
                        $login_type = 'external-surveyor-admin';
                        break;

                    case ($user_type == 'external-surveyor' && $user_role == 'surveyor'):
                        $login_type = 'external-surveyor';
                        break;

                    case ($user_type == 'broker-user'):
                        $login_type = 'broker-user';
                        break;

                    default:
                        $login_type = 'risk-control';
                        break;
                }

                $user->login_type = $login_type;

                // Store relevent data in sessions
                Session::put('role', $user_role);
                Session::put('user', $user);
                Session::put('type', 'User');
            }

            Session::put('socials-user', $socialUser);
            Session::put('socials-role', 'virtual-rooms');
            Session::put('vr-business', $business);
            Session::put('vr-session', $business);

            $url = Session::get('login-attempt-url');
            Session::forget('login-attempt-url');

            if (isset($url) && !empty($url)) {
                return Redirect::to($url);
            } else {
                return Redirect::to('virtual-rooms/dashboard');
            }
        }

        if ($response->response === 'success' && !$response->socials_user) {
            $socialCustomer = (array) $response->socials_customer;
            Session::put('socials-customer', $socialCustomer);
            Session::put('socials-role', 'virtual-rooms-customer');
            return Redirect::to('virtual-rooms/customer-dashboard');
        }

        // identify fall back page
        if ($response->response === 'fail' && $response->socials_user) {
            return Redirect::route('virtual-rooms.login')
                ->with(
                    'error',
                    $response->message
                );
        }

        if ($response->response === 'fail' && $response->socials_customer) {
            return Redirect::route('virtual-rooms.customer-login')
                ->with(
                    'error',
                    $response->message
                );
        }
    }

    private function validateEmail($data)
    {
        $rules = [
            'email' => 'required|email'
        ];

        $validator = Validator::make($data, $rules);

        return $validator;
    }

    public function customerLogin(Request $request)
    {
        return view('lets-talk-socials.customers.customer-login');
    }

    public function customerGenerateLink(Request $request)
    {
        $data = $request->all();
        $validator = $this->validateEmail($data);

        if ($validator->fails()) {
            return Redirect::back()->withErrors($validator)->withInput($request->old());
        }

        // generate Magic Link
        $response = json_decode(Api::post('api/v1/virtual-rooms/customer-generate-link', $data));

        if (
            isset($response->response)
            && !empty($response->response)
            && $response->response === "success"
        ) {
            return Redirect::route('virtual-rooms.customer-email-sent');
        }

        if (
            isset($response->response)
            && !empty($response->response)
            && $response->response === "fail"
        ) {
            return Redirect::route('virtual-rooms.customer-email-sent');
        }

        return Redirect::route('virtual-rooms.customer-login')
            ->with(
                'error',
                'Ooops.. Something went wrong...'
            );
    }

    public function customerEmailSent(Request $request)
    {
        return view('lets-talk-socials.customers.customer-email-sent');
    }

    public function switchTwilioSms(Request $request, $sms)
    {
        $response = json_decode(Api::get('api/v1/virtual-rooms/sms/' . $sms));
        return Response::json($response);
    }

    public function loginAttemptLogs(Request $request)
    {
        $response = json_decode(Api::get('api/v1/virtual-rooms/get-login-attempts'));
        $data['logs'] = $response->response;
        return view('lets-talk.login-attempt-logs', $data);
    }

    public function deleteLoginAttemptLog(Request $request)
    {
        $data = $request->all();
        $response = json_decode(Api::post('api/v1/virtual-rooms/delete-login-attempt', $data));
    }
}
