<?php

namespace App\Http\Controllers\LetsTalk;

use App\Models\Api;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Response;

class CustomerMyMessagesController extends BaseController
{
    public function index(Request $request)
    {
        $user = Session::get('socials-customer');
        $data['email'] = $user['customer_email'];

        return view('lets-talk-socials.my-messages', $data);
    }

    public function videoMessages(Request $request)
    {
        $data = $request->all();
        $user = Session::get('socials-customer');
        $data['email'] = $user['customer_email'];
        $data['timezone'] = 'Europe/London'; // since guest have no timezone information
        $data['selected_page'] = ((isset($data['selected_page']) && !empty($data['selected_page'])) ? $data['selected_page'] : "1");
        $selected_page = $data['selected_page'];
        $config_url = config('app.client_url');

        $response = json_decode(Api::post('api/v1/virtual-rooms/video-record/customer-my-messages', $data));
        $notifications = json_decode(Api::post('api/v1/virtual-rooms/video-record/red-dot-notifications', $data));

        return Response::json(
            [
                'status' => 'success',
                'response' => $response->response,
                'notifications' => $notifications->response,
                'page_count' => $response->page_count,
                'selected_page' => $selected_page,
                'config_url' => $config_url,
                'message' => null
            ]
        );

        // return Response::json([
        //     'status' => 'success',
        //     'response' => $user,
        //     'message'=> null
        // ]);
    }

    public function viewMyMessage(Request $request, $id)
    {
        // video-record/single-page-view/{id}
        $user = Session::get('socials-customer');
        $video = null;
        $response = json_decode(Api::get('api/v1/virtual-rooms/video-record/single-page-view/' . $id . '?email=' . rawurlencode($user['customer_email'])));
        if (isset($response->status) && $response->status !== 'failed' && isset($response->response)) {
            $video = $response->response;
        } else {
            return view('lets-talk-socials.errors.404');
        }
        return view('lets-talk-socials.single-view-message')->with('video', $video);
    }

    public function getMyMessageMembers(Request $request)
    {
        $data = $request->all();
        $response = json_decode(Api::post('api/v1/virtual-rooms/video-record/my-messages-members', $data));

        return Response::json(
            [
                'status' => 'success',
                'response' => $response,
                'message' => null
            ]
        );
    }

    public function updateNotification(Request $request)
    {
        $data = $request->all();
        $user = Session::get('socials-customer');
        $data['email'] = $user['customer_email'];

        json_decode(Api::post('api/v1/virtual-rooms/video-record/update-notification', $data));

        return Response::json(
            [
                'status' => 'success',
                'message' => null
            ]
        );
    }

    public function checkNotifications(Request $request)
    {
        $data = $request->all();
        $user = Session::get('socials-customer');
        $data['email'] = $user['customer_email'];
        $response = json_decode(Api::post('api/v1/virtual-rooms/video-record/red-dot-notifications', $data));

        return Response::json(
            [
                'status' => 'success',
                'response' => $response->response,
                'message' => null
            ]
        );
    }
}
