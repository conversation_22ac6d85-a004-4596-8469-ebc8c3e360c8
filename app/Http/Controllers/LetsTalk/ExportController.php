<?php

namespace App\Http\Controllers\LetsTalk;

use App\Models\Api;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Response;

class ExportController extends BaseController
{
    public function __construct(Request $request, \App\Models\FileUpload $fileUpload)
    {
        $this->file = $fileUpload;
    }

    public function exportContacts(Request $request)
    {
        $res = json_decode(Api::get('api/v1/virtual-rooms/export-contacts'));

        if ($res->response !== 200) {
            return Response::json(
                [
                    'status' => 'No data found'
                ]
            );
        }
        $data = json_decode(json_encode($res->data), true);
        $newobj = [];

        foreach ($data as $value) {
            array_push(
                $newobj,
                array(
                    'email' => isset($value['email']) ? $this->isNotNullorEmpty($value['email']) : 'No Data',
                    'name' => isset($value['user_name']) ? $this->isNotNullorEmpty($value['user_name']) : 'No Data',
                    'mobile' => isset($value['mobile_number']) ? $this->isNotNullorEmpty($value['mobile_number']) : 'No Data'
                )
            );
        }

        return Excel::create(
            'external-contacts',
            function ($excel) use ($newobj) {
                $excel->sheet(
                    'Sheetname',
                    function ($sheet) use ($newobj) {
                        $sheet->fromArray(
                            $newobj
                        );
                    }
                );
            }
        )->export('xls');
    }

    public function exportProfiles(Request $request)
    {
        $res = json_decode(Api::get('api/v1/virtual-rooms/export-profiles'));

        $publicProfileUrl = trim(config('app.client_url'), '/') . '/virtual-rooms/staff/';

        if ($res->response !== 200) {
            return Response::json(
                [
                    'status' => 'No data found'
                ]
            );
        }
        $data = json_decode(json_encode($res->data), true);

        foreach ($data['lsm_libreps'] as $key => $value) {
            if (!isset($value['mobile']) || !preg_match('/^[+][1-9][0-9]{6,14}$/', $value['mobile'])) {
                continue;
            }
            $lsmLibReps[$key] = [
                'Name' => isset($value['name']) && !empty($value['name']) ? $value['name'] : 'No Data',
                'Profile Link' => isset($value['email']) && !empty($value['email']) ? $publicProfileUrl . $value['email'] : 'No Data',
                'Business Unit' => 'LSM'
            ];
        }

        foreach ($data['lmre_libreps'] as $key => $value) {
            if (!isset($value['mobile']) || !preg_match('/^[+][1-9][0-9]{6,14}$/', $value['mobile'])) {
                continue;
            }
            $lmreLibReps[$key] = [
                'Name' => isset($value['name']) && !empty($value['name']) ? $value['name'] : 'No Data',
                'Profile Link' => isset($value['email']) && !empty($value['email']) ? $publicProfileUrl . $value['email'] : 'No Data',
                'Business Unit' => 'LMRe'
            ];
        }

        $newobj = array_merge($lmreLibReps, $lsmLibReps);

        return Excel::create(
            'public-profiles',
            function ($excel) use ($newobj) {
                $excel->sheet(
                    'Sheetname',
                    function ($sheet) use ($newobj) {
                        $sheet->fromArray(
                            $newobj
                        );
                    }
                );
            }
        )->export('xls');
    }

    private function isNotNullorEmpty($data)
    {
        if ($data !== "" && !empty($data)) {
            return $data;
        }
        return "No Data";
    }

    public function importContacts(Request $request)
    {
        $path = public_path() . '/docs/external-contacts.xls';
        if (!file_exists($path)) {
            return Response::json(
                [
                    'msg' => 'No excel data found',
                    'status' => 404
                ]
            );
        }

        $data = Excel::load(
            $path,
            function ($reader) {
            }
        )->get();

        return Api::post('api/v1/virtual-rooms/import-contacts', ['data' => $data]);
    }

    public function importLibertyUsers(Request $request)
    {
        // $path = public_path() . '/docs/import-contacts.xls';
        $path = storage_path() . '/docs/import-contacts.xlsx';
        if (!file_exists($path)) {
            return Response::json(
                [
                    'msg' => 'No excel data found',
                    'status' => 404
                ]
            );
        }

        $data = Excel::load(
            $path,
            function ($reader) {
            }
        )->get();

        // dd($data->toArray());
        $response = json_decode(Api::post('api/v1/virtual-rooms/import-liberty-users', ['data' => $data->toArray()]), true);
        $failedImports = isset($response->failed_imports) ? $response->failed_imports : [];
        if (empty($failedImports)) {
            $failedImports = isset($response['failed_imports']) ? $response['failed_imports'] : [];
            if (empty($failedImports)) {
                return Response::json($response);
            }
        }

        return Excel::create(
            'failed-import-list',
            function ($excel) use ($failedImports) {
                $excel->sheet(
                    'Sheetname',
                    function ($sheet) use ($failedImports) {
                        $sheet->fromArray(
                            $failedImports
                        );
                    }
                );
            }
        )->export('xls');
    }
}
