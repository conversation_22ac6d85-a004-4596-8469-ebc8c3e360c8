<?php

namespace App\Http\Controllers\LetsTalk;

use Carbon\Carbon;
use App\Models\Api;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Validator as ValidatorType;

class SocialCatchupController extends BaseController
{
    public function __construct(Request $request)
    {
    }

    public function store(Request $request)
    {
        $data = $request->all();
        $apiParams['created_by_id'] = $this->getAuthPersonId() ? $this->getAuthPersonId() : null;
        $apiParams['person_id'] = isset($data['person_id']) ? $data['person_id'] : null;
        $apiParams['role'] = 'virtual-rooms';

        $validator = $this->validateStore($data);
        if ($validator->fails()) {
            $errorResponse = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
            return Response::json($errorResponse, 400);
        }

        $apiParams['name'] = $data['name'];

        // meeting_time contains string value of the schedule frequency
        // $apiParams['meeting_time'] = [
        //     '1 day' => 'Every Day',
        //     '1 week' => 'Every Week',
        //     '2 week' => 'Every 2 Weeks',
        //     '1 month' => 'Every Month',
        //     '1 quarter' => 'Every Quarter',
        // ][$data['frequency']];

        if ($data['duration_type'] === 'permanent') {
            $startDateCarbon = Carbon::now();
            $apiParams['start_date'] = $startDateCarbon->toDateTimeString();
            $apiParams['end_date'] = $startDateCarbon->toDateTimeString();
        } else {
            $startDateCarbon = Carbon::createFromFormat('d/m/Y', $data['start_date'], 'Europe/London');
            $apiParams['start_date'] = (string)Carbon::parse($startDateCarbon->format('Y-m-d') . ' ' . $data['start_time']);
            $apiParams['end_date'] = (string)Carbon::parse($startDateCarbon->format('Y-m-d') . ' ' . $data['end_time']);
        }

        $apiParams['duration_type'] = $data['duration_type'];
        if ($apiParams['duration_type'] === 'one-off') {
            $apiParams['frequency'] = 1;
            $apiParams['frequency_type'] = 'day';
            $apiParams['end_after_frequency'] = 1;
            $apiParams['end_after_frequency_type'] = 'day';
        } else {
            $apiParams['frequency'] = explode(' ', $data['frequency'])[0];
            $apiParams['frequency_type'] = explode(' ', $data['frequency'])[1];
            $apiParams['end_after_frequency'] = explode(' ', $data['end_after'])[0];
            $apiParams['end_after_frequency_type'] = explode(' ', $data['end_after'])[1];
        }

        // dd($apiParams);

        $response = json_decode(Api::post('api/v1/virtual-rooms/catch-up', $apiParams), true);
        return Response::json($response);
        if ($response['response'] === 'error') {
            return Response::json($response, 400);
        }

        return Response::json($response);
    }

    private function validateStore($data): ValidatorType
    {
        $rules = [
            'name' => 'required',
            'person_id' => 'required',
            'duration_type' => 'required',
            'start_date' => 'required_if:duration_type,on-a-schedule,one-off',
            'start_time' => 'required_if:duration_type,on-a-schedule,one-off',
            'end_time' => 'required_if:duration_type,on-a-schedule,one-off',
            'frequency' => 'required_if:duration_type,on-a-schedule',
            'end_after' => 'required_if:duration_type,on-a-schedule',
        ];

        // // On update, we don't want to validate email, cause it cannot be updated
        // if($action === 'update') {
        //     unset($rules['email']);
        // }

        $messages = [
            'name.required' => 'Please enter meeting title.',
            'person_id.required' => 'Please select employee.',
            'duration_type.required' => 'Please select duration type.',
            'start_date.required_if' => 'Please select start date.',
            'start_time.required_if' => 'Please select start time.',
            'frequency.required' => 'Please select frequency.',
            'end_after.required' => 'Please select end after.',
        ];

        return Validator::make($data, $rules, $messages);
    }

    public function getUpcomingCatchups(Request $request)
    {
        $isRoomReady = false;
        $user =  Session::get('socials-user');
        $response = json_decode(Api::get('api/v1/virtual-rooms/catch-up/upcoming?person_id=' . $this->getAuthPersonId()), true);

        if ($response['response'] === 'error') {
            return Response::json($response, 400);
        }
        if (isset($response['data'][0]['schedule']['date'])) {
            $now = \Carbon\Carbon::now()->timezone($user['timezone']);
            $latestSchedule = \Carbon\Carbon::parse($response['data'][0]['schedule']['date'], $user['timezone']);
            $isRoomReady = (((int) $latestSchedule->diffInMinutes($now, false) >= -5)) && (((int) $latestSchedule->diffInMinutes($now, false) <= 60)) ?  true : false;
            $response['data'][0]['is_room_ready'] = $isRoomReady;
        }

        return Response::json($response);
    }

    public function showManage(Request $request)
    {
        $upcomingSchedulesResponse = json_decode(Api::get('api/v1/virtual-rooms/catch-up/upcoming?person_id=' . $this->getAuthPersonId()), true);
        $upcomingSchedules = [];
        if ($upcomingSchedulesResponse['response'] === 'success') {
            $upcomingSchedules = $upcomingSchedulesResponse['data'];
        }

        $socialRoomTypeData = [];
        $socialRoomType = json_decode(Api::get('api/v1/virtual-rooms/' . html_entity_decode('1:1 Catchup') . '/social-room-type'), true);
        if ($socialRoomType['response'] == 'success') {
            $socialRoomTypeData = $socialRoomType['data'];
        }

        $colleaguesResponse = json_decode(Api::get('api/v1/virtual-rooms/catch-up/colleagues?person_id=' . $this->getAuthPersonId()), true);
        $colleagues = [];
        if ($colleaguesResponse['response'] === 'success') {
            $colleagues = $colleaguesResponse['data'];
        }



        return view('lets-talk-socials.manage-page', compact('upcomingSchedules', 'colleagues', 'socialRoomTypeData'));
    }

    public function showCatchupWith(Request $request, $withPersonId)
    {
        $params = http_build_query(
            [
                'person_id' => $this->getAuthPersonId(),
                'with_person_id' => $withPersonId,
            ]
        );
        $upcomingSchedulesResponse = json_decode(Api::get('api/v1/virtual-rooms/catch-up/upcoming?' . $params), true);
        $upcomingSchedules = [];
        $withCms = [];
        $permanentRooms = [];
        if ($upcomingSchedulesResponse['response'] === 'success') {
            $upcomingSchedules = $upcomingSchedulesResponse['data'];
            $withCms = $upcomingSchedulesResponse['with_person_cms'];
            $permanentRooms = $upcomingSchedulesResponse['permanent_rooms'];
        }

        $upcomingSchedules = array_merge($upcomingSchedules, $permanentRooms);

        // $socialRoomTypeData = [];
        // $socialRoomType = json_decode(Api::get('api/v1/virtual-rooms/'. html_entity_decode('1:1 Catchup').'/social-room-type') , true);
        // if($socialRoomType['response'] == 'success'){
        //     $socialRoomTypeData = $socialRoomType['data'];
        // }

        $catchupSocialRoomType = 4;

        $colleaguesResponse = json_decode(Api::get('api/v1/virtual-rooms/social-rooms/' . $catchupSocialRoomType . '/archive?' . $params), true);
        $colleagues = [];
        if ($colleaguesResponse['response'] === 'success') {
            $colleagues = $colleaguesResponse['rooms'];
        }

        return view('lets-talk-socials.catchup-with', compact('upcomingSchedules', 'colleagues', 'socialRoomTypeData', 'withCms'));
    }

    public function showSpace(Request $request, $room)
    {
        $response = json_decode(Api::get('api/v1/virtual-rooms/catch-up/' . $room . '?person_id=' . $this->getAuthPersonId()));

        if ($response->response !== 'success') {
            // TODO:
            // Add redirect here, this error template cannot handle social-room yet
            // return view('errors.404');
        }

        // $announcementsResponse = json_decode(Api::post('api/v1/virtual-rooms/announcements', ['person_id' => $this->getAuthPersonId()]));

        $roomUpcomingSchedule = [];
        $colleagueDetails = [];
        $colleague = [];

        if ($response->response === 'success') {
            $room = $response->data->room;
            $responseMessages = json_decode(Api::get('api/v1/virtual-rooms/discussion/reply?room=' . $room->id . '&person_id=' . $this->getAuthPersonId()));
            $creator = $response->data->room->cms;
            $upcomingSchedules = json_decode(json_encode($response->data->upcoming_schedules), true);
            $room->messages = isset($responseMessages->data->messages) ? $responseMessages->data->messages : [];
            // $recentAnnouncements = array_slice(isset($announcementsResponse->data) ? $announcementsResponse->data : [], 0, 5);
            $joinRoomLink = isset($response->data->join_room_link) ? $response->data->join_room_link : "#";
            $colleague = (array) $room->social_room_participants[0];
        } else {

            // Check for previously added room that has been deleted
            $new_room_response = json_decode(Api::get('api/v1/virtual-rooms/theme-rooms/updated-schedule/' . $room));

            if (isset($new_room_response->response) && $new_room_response->response == 'success') {
                $updated_room_code = $new_room_response->updated_room_code;
                return Redirect::to('/virtual-rooms/catch-up-rooms/' . $updated_room_code);
            }

            if (isset($response->message) && $response->message == 'Room not found.') {
                Session::flash('failure', json_encode(['title' => 'Warning', 'message' => 'This room is no longer active.']));
            }
            return Redirect::route('virtual-rooms.dashboard');
        }

        return view('lets-talk-socials.space', compact('room', 'creator', 'colleague', 'upcomingSchedules', 'joinRoomLink'));
    }

    public function getRepresentatives(Request $request)
    {
        $personId = $this->getAuthPersonId();
        $response = json_decode(Api::get('api/v1/virtual-rooms/catch-up/representatives?person_id=' . $personId), true);

        if ($response['response'] === 'success') {
            return Response::json($response);
        }

        return Response::json([]);
    }

    public function deleteSpace(Request $request, $socialRoomId)
    {
        $response = json_decode(
            Api::delete('api/v1/virtual-rooms/catch-up/' . $socialRoomId . '?person_id=' . $this->getAuthPersonId()),
            true
        );

        $hasError = $response['response'] === 'error';

        $status = $hasError ? 'failure' : 'success';
        $title = $hasError ? 'Failed' : 'Successfully Deleted';
        $message = $hasError ? 'Failed.' : 'Your space has been successfully deleted.';

        Session::flash($status, json_encode(['title' => $title, 'message' => $message]));

        if ($hasError) {
            return Response::json($response, 400);
        }

        return Response::json($response);
    }

    public function deleteSchedule(Request $request)
    {
        // $socialRoomId = $request->get('lt_social_room_id');
        // $scheduleDate = $request->get('date');
        $apiParams['person_id'] = $this->getAuthPersonId();

        $input = $request->all();

        $response = json_decode(
            Api::post(
                'api/v1/virtual-rooms/catch-up/delete-schedule',
                array_merge($apiParams, $input)
            ),
            true
        );

        $hasError = $response['response'] === 'error';
        $isEntry = isset($input['date']);

        $status = $hasError ? 'failure' : 'success';
        $title = $hasError ? 'Failed' : 'Successfully Deleted';
        $isEntry = $isEntry ? 'Your entry has been successfully deleted.' : 'Your schedule has been successfully deleted.';
        $message = $hasError ? 'Failed.' : $isEntry;

        Session::flash($status, json_encode(['title' => $title, 'message' => $message]));

        if ($hasError) {
            return Response::json($response, 400);
        }

        return Response::json($response);
    }

    private function getAuthPersonId()
    {
        $user = Session::get('socials-user');
        return isset($user['person_id']) ? $user['person_id'] : '';
    }
}
