<?php

namespace App\Http\Controllers\LetsTalk;

use App\Models\Api;
use App\Models\FileUpload;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;

class SocialRoomMessageController extends BaseController
{
    public FileUpload $files;

    public function __construct(Request $request, FileUpload $fileupload)
    {
        $this->files = $fileupload;
    }

    public function index(Request $request, $room)
    {
        $personId = $this->getPersonId();

        $response = json_decode(Api::get('api/v1/virtual-rooms/discussion/reply?room=' . $room . '&person_id=' . $personId));
        // $announcementsResponse = json_decode(Api::post('api/v1/virtual-rooms/announcements',
        //     ['person_id' => $personId]));

        if ($response->response === 'success') {
            $room = $response->data->room;
            $creator = $response->data->room->cms;
            $upcomingSchedules = $response->data->upcoming_schedules;
            $room->messages = [];
            // $recentAnnouncements = array_slice($announcementsResponse->data, 0, 5);
        }

        return view(
            'lets-talk-socials.space',
            compact('room', 'creator', 'upcomingSchedules')
        );
    }

    public function show(Request $request, $id)
    {
        $response = json_decode(Api::get('api/v1/virtual-rooms/discussion/reply/' . $id));

        if ($response->response == "success") {

            $level = 0;

            if (!empty($response->data->parent)) {
                $level = $response->data->parent->parent_id == 0 ? 1 : 2;
            }

            return view(
                'lets-talk-socials.partials.social-space-rooms.messages.reply',
                [
                    'reply' => $response->data,
                    'level' => $level,
                    'last' => $level
                ]
            );
        }
    }

    public function store(Request $request)
    {
        $data = $request->all();
        $data['user_id'] = $this->getPersonId();
        $data['user_name'] = $this->getPersonName();

        $validator = $this->validateStore($data);

        if ($validator->fails()) {
            $errorResponse = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
            return Response::json($errorResponse);
        }

        return Api::post('api/v1/virtual-rooms/discussion/reply/store', $data);
    }

    public function update(Request $request, $id)
    {
        $data = $request->all();
        $data['user_id'] = $this->getPersonId();
        $data['user_name'] = $this->getPersonName();

        $validator = $this->validateStore($data);

        if ($validator->fails()) {
            $errorResponse = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
            return Response::json($errorResponse);
        }

        if (!empty($request->get('deletefiles'))) {
            $files = explode(',', trim($request->get('deletefiles'), ', '));
            foreach ($files as $val) {
                $this->deleteFile($val);
            }
        }

        return Api::post('api/v1/virtual-rooms/discussion/reply/' . $id . '/update', $data);
    }


    public function delete(Request $request, $id)
    {
        $data = $request->all();
        $data['user_id'] = $this->getPersonId();

        $response = Api::delete('api/v1/virtual-rooms/discussion/reply/' . $id . '/delete', $data);

        return $response;
    }


    public function uploadFile(Request $request)
    {
        $data = $request->all();

        $file = $data['file'];

        $uuid = Str::uuid()->toString();

        $cloud_file = $uuid . '/' . $data['name'];

        $upload = $this->files->upload(
            $file->getPathName(),
            'virtual-rooms/' . $data['message_id'] . '/' . $cloud_file,
            'vr_bucket'
        );

        if ($upload) {
            $api_data = [
                'message_id' => $data['message_id'],
                'cloud_file' => $cloud_file,
                'original_file' => $data['name'],
                'type' => $data['type'],
            ];


            return Api::post('api/v1/virtual-rooms/discussion/file/upload', $api_data);
        }

        return [
            'response' => 'error',
            'message' => 'The Liberty Community File has failed to upload',
        ];
    }

    public function deleteFile($id)
    {
        return Api::delete('api/v1/virtual-rooms/discussion/file/' . $id . '/delete');
    }

    protected function linkFile($id)
    {
        $response = json_decode(Api::get('api/v1/virtual-rooms/discussion/file/' . $id));

        if ($response->response == "success") {
            $url = $response->url;
        } else {
            $url = '#';
        }

        return $url;
    }


    public function like(Request $request, $id)
    {
        $data = $request->all();
        $data['user_id'] = isset(Session::get('socials-user')['person_id']) ? Session::get('socials-user')['person_id'] : Session::get('socials-customer')['customer_email'];
        return Api::put('api/v1/virtual-rooms/discussion/reply/' . $id . '/like', $data);
    }


    private function validateStore($data)
    {
        $rules = [

            'message' => 'required',
        ];

        $messages = [];

        return Validator::make($data, $rules, $messages);
    }


    private function getPersonId()
    {
        return isset(Session::get('socials-user')['person_id']) ? Session::get('socials-user')['person_id'] : Session::get('socials-customer')['person_id'];
    }

    public function storeContentImage(Request $request)
    {
        $data = $request->all();

        $file = $data['file'];

        $uuid = Str::uuid()->toString();

        $cloud_file = $uuid . '/' . 'file.jpg';

        $upload = $this->files->upload(
            $file->getPathName(),
            'virtual-rooms/' . $cloud_file,
            'vr_bucket'
        );

        $image_link = $this->files->link(
            '/virtual-rooms/' . $cloud_file,
            '10 minutes',
            'vr_bucket'
        );

        if ($upload) {

            return [
                'response' => 'success',
                'image_link' => $image_link,
                'cloud_info' => $cloud_file,
                'message' => 'content image has been uploaded',
            ];
        }

        return [
            'response' => 'error',
            'message' => 'The Liberty Community File has failed to upload',
        ];
    }

    public function updateContentImage(Request $request)
    {
        $data = $request->all();
        $updated_image_links = [];

        if ($data) {
            foreach ($data['cloude_info'] as $key => $value) {
                $image_link = $this->files->link(
                    '/virtual-rooms/' . $value . '/file.jpg',
                    '10 minutes',
                    'vr_bucket'
                );
                $updated_image_links[$value] = $image_link;
            }

            return Response::json(
                [
                    'response' => 'success',
                    'data' => $updated_image_links,
                    'message' => 'content image has been uploaded',
                ]
            );
        }
    }

    private function getPersonName()
    {
        return isset(Session::get('socials-user')['name']) ? Session::get('socials-user')['name'] : Session::get('socials-customer')['customer_email'];
    }
}
