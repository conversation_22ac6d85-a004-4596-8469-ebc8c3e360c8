<?php

namespace App\Http\Controllers;

use App\Models\Api;
use App\Models\FileUpload;
use App\Models\Survey;
use App\Services\CacheContent\GetBrokerUserService;
use App\Services\CacheContent\GetOrgRiskGradingData;
use App\Services\CacheContent\GetSurveyService;
use App\Services\SendSqsMessageService;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use PragmaRX\Google2FA\Exceptions\IncompatibleWithGoogleAuthenticatorException;
use PragmaRX\Google2FA\Exceptions\InvalidCharactersException;
use PragmaRX\Google2FA\Exceptions\SecretKeyTooShortException;
use PragmaRX\Google2FA\Google2FA;
class BaseResourceController extends BaseController
{
    const
        TEMPLATE_PATH = '/base-resource',
        RESPOND_TO_AJAX = false;

    public Survey $survey;

    public function __construct(Request $request, Survey $survey)
    {
        parent::__construct($request);
        $this->survey = $survey;
    }

    private static function onUpdateError(array $data)
    {
    }

    /**
     * Show all resources
     * @throws \Exception
     * @throws GuzzleException
     */
    public function index(Request $request)
    {
        $userDetails = Session::get('user');
        $search      = $request->get('search', '');
        $page        = $request->get('page', 1);
        $limit       = Session::get('limit', 10);
        $appendUrl   = $this->getRequestUrl($request->getRequestUri());
        $loginType   = $userDetails->login_type;
        
        $uriString = sprintf($appendUrl . 'all/%d/%d%s', $page, $limit, ($search !== '' ? '?search=' . urlencode($search): ''));
        if (Route::currentRouteName() === 'external-surveyors.index' &&
            ($loginType === 'external-surveyor' || $loginType === 'external-surveyor-admin')) {
            $companyId = $userDetails->external_survey_company_id;
            $uriString = sprintf($appendUrl . 'all/%d/%d/%d%s', $page, $limit, $companyId, ($search !== '' ? '?search=' . urlencode($search): ''));
        }

        $endpoint  = static::get_api_uri($uriString);
        $resources = Route::currentRouteName() === 'broker-users.index' ? GetBrokerUserService::get() : json_decode(Api::get($endpoint));

        if ($resources->response == 'success') {
            return view(
                static::TEMPLATE_PATH . '/index',
                static::getRouteParams(
                    $request,
                    'index', [
                    'resources' => $resources->data,
                    'total' => $resources->total,
                    'limit' => $limit,
                    'page' => $page,
                    'search' => $search,
                    'user_details' => $userDetails,
                    'link' => static::ROUTE_PREFIX . '.index',
                    ], static::getAdditionalViewParams('index', $resources->data)
                )
            );
        } else {
            throw new \Exception($resources->message);
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {

        if ($request->is('surveys/create')) {
            if ($request->has('organisation_id')) {
                $data = json_decode(Api::get('api/v1/organisation/' . $request->get('organisation_id')));
                if (Session::get('user')->login_type == 'broker-user' && isset($data->data) && $data->data->mga_scheme == null) {
                    return Response::make('Unauthorized', 401);
                }
                Session::put('ApiFiles', []);
            }
        }

        if ($request->segment(1) == 'learning') {
            $params = static::getRouteParams($request, 'create');
            $courses = [];
            foreach ($params['options']['courses']['data'] as $course) {
                $courses[$course['id']] = $course['title'];
            }
            $params['options']['courses'] = $courses;

            $params['options']['remove'] = $params['options']['organisations'];
            //$options['organisations'][0] = 'All';
            $organisations = [];
            $organisations[0] = 'All';
            foreach ($params['options']['organisations'] as $key => $org) {
                $organisations[$key] = $org;
            }
            $params['options']['organisations'] = $organisations;

            return view(
                static::TEMPLATE_PATH . '/create',
                array_merge($params, ['layout' => 'modal'])
            );
        } elseif ($request->segment(1) == 'external-surveyors') {
            return view(
                static::TEMPLATE_PATH . '/create',
                static::getRouteParams($request, 'create')
            );
        } else {
            return view(
                static::TEMPLATE_PATH . '/create',
                static::getAdditionalViewParams('create', [])
            );
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     */
    public function edit(Request $request, $id)
    {
        $this->determineNestedResourceID($id);
        $resource = $request->routeIs('surveys.edit') ? GetSurveyService::get($id) : json_decode(Api::get(static::get_api_uri($id)));

        if ($request->is('surveys/*/edit')) {
            if (Session::get('user')->login_type == 'external-surveyor' || Session::get('user')->login_type == 'external-surveyor-admin') {
                return Response::make('Unauthorized', 401);
            }
            
            if (Session::get('user')->login_type == 'broker-user') {

                $orgs_for_user = json_decode(Api::get('api/v1/broker-users/orgs/' . Session::get('user')->broker_id));

                if ($resource && isset($resource->data->organisation) && $resource->data->organisation->mga_scheme == null) {
                    return Response::make('Unauthorized', 401);
                }

                if ($resource && isset($resource->organisation) && isset($resource->data) && isset($resource->data->organisation)
                    && $orgs_for_user && isset($orgs_for_user->schemes)
                ) {
                    if (!in_array($resource->data->organisation->id, $orgs_for_user->schemes)) {
                        return Response::make('Unauthorized', 401);
                    }
                }
            }

            $getResponseId = Api::get('api/v1/surveys/get-survey-files-for-id/' . $resource->data->id);
            $files = json_decode($getResponseId);

            foreach ($files->data as $file) {
                $file->download = $this->survey->downloadLink(
                    'survey_attachments/file/' . $file->cloud_file_name,
                    $file->file_name
                );
            }

            $getSrfResponseId = Api::get('api/v1/surveys/get-srf-for-id/' . $resource->data->id);
            $srf = json_decode($getSrfResponseId);

            return ($resource->response == 'success')
                ? view(
                    static::TEMPLATE_PATH . '/edit',
                    array_merge(
                        ['resource' => $resource->data], ['files' => $files->data], ['srf' => $srf->data],
                        static::getAdditionalViewParams('edit', $resource->data)
                    )
                )
                : Redirect::back()->with($resource->response, $resource->message);
        }

        if ($request->segment(1) === 'learning') {
            $params = static::getRouteParams($request, 'edit', ['resource' => $resource->data]);
            if (isset($params['resource']->category) && count($params['resource']->category) > 0) {
                $params['categories'] = array_map(
                    function ($category) {
                        return $category->category_id;
                    }, $params['resource']->category
                );
            }

            if (isset($params['resource']->organisation) && count($params['resource']->organisation) > 0) {
                $params['organisations'] = array_map(
                    function ($organisation) {
                        return $organisation->organisation_id;
                    }, $params['resource']->organisation
                );
            }

            return ($resource->response == 'success')
                ? view(static::TEMPLATE_PATH . '/edit', $params)
                : Redirect::back()->with($resource->response, $resource->message);
        }

        if ($request->is('liberty-users/*/edit') || $request->is('brokers/*/edit')) {
            $files = new FileUpload();
            $image_link = null;
            try {
                if ($files->exists('liberty_users_images/' . $resource->data->image)) {
                    $image_link = $files->link('liberty_users_images/' . $resource->data->image);
                }

            } catch (\Exception $e) {
                $image_link = null;
            }

            // $files = new FileUpload();
            // $pdf_image_link = null;
            // try {
            //     if($files->exists('liberty_users_images/'.$resource->data->pdf_image)) {
            //         $pdf_image_link = $files->link('liberty_users_images/'.$resource->data->pdf_image);
            //     }

            // } catch(Exception $e) {
            //     $pdf_image_link = null;
            // }

            return ($resource->response == 'success')
                ? view(
                    static::TEMPLATE_PATH . '/edit',
                    array_merge(
                        ['resource' => $resource->data, 'image_link' => $image_link],
                        static::getAdditionalViewParams('edit', $resource->data)
                    )
                )
                : Redirect::back()->with($resource->response, $resource->message);
        }

        return ($resource->response == 'success')
            ? view(
                static::TEMPLATE_PATH . '/edit',
                array_merge(['resource' => $resource->data], static::getAdditionalViewParams('edit', $resource->data))
            )
            : Redirect::back()->with($resource->response, $resource->message);
    }

    /**
     * Store a newly created resource in storage.
     * @throws GuzzleException
     */
    public function store(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            static::getValidatorRules($request, 'store'),
            static::getValidatorMessages('store')
        );

        if (method_exists(get_called_class(), 'getValidatorAttributeNames')) {
            $validator->setAttributeNames(static::getValidatorAttributeNames());
        }

        if (method_exists(get_called_class(), 'getValidatorValueNames')) {
            $validator->setValueNames(static::getValidatorValueNames());
        }

        if ($validator->fails()) {
            if ($request->ajax() && static::RESPOND_TO_AJAX) {
                return Response::json(
                    [
                    'invalid' => $validator->errors(),
                    ], 400
                );
            } else {
                return Redirect::back()->withInput()->withErrors(
                    $validator->errors()
                );
            }
        }

        $data = $request->all();

        if (isset($data['_token'])) {
            unset($data['_token']);
        }

        if (isset($data['method']) && $data['method'] == 'duplicate') {
            $data['src'] = 'liberty';
            $api_call = json_decode(Api::post(static::get_api_uri() . '/' . $data['source'] . '/duplicate', $data));

            if (isset($api_call->response) && $api_call->response == 'success') {
                if (!$request->ajax()) {
                    return Redirect::to('learning/course/' . $data['source'] . '/edit');
                }

                return Response::json(['response' => $api_call->response, 'message' => $api_call->message]);
            } else {
                return Redirect::to('learning/course/' . $data['source'] . '/edit')
                    ->with(
                        $api_call->response ?? 'error', $api_call->message ?? 'Was not possible duplicate this course'
                    );
            }
        } else {
            if (isset($data['organisationRemove'])) {
                $organisations = static::getRouteParams($request, 'create')['options']['organisations'];
                $orgList = [];
                foreach ($data['organisationRemove'] as $remove) {
                    if (isset($organisations[(int)$remove])) {
                        unset($organisations[(int)$remove]);
                    }
                }

                unset($data['organisationRemove']);
                foreach ($organisations as $key => $org) {
                    $orgList[] = (string)$key;
                }

                $data['organisation'] = $orgList;
            }

            if (str_contains(static::get_api_uri(), 'learning/course')) {
                if (isset($data['owner']) && !empty($data['owner'])
                    && count($data['organisation']) === 1
                    && !empty($data['organisation'][0])
                ) {
                    $data['organisation_id'] = $data['organisation'][0];
                } elseif (isset($data['owner']) && (count($data['organisation']) > 1 || empty($data['organisation'][0]))) {
                    return Response::json(
                        [
                        'invalid' => 'Assigning ownership is allowed only for one organisation',
                        ]
                    );
                }
            }

            if (method_exists(get_called_class(), 'parseDataBeforeStorage')) {
                $data = static::parseDataBeforeStorage($data);
            }

            $data['src'] = 'liberty';
            $validRequestType = $request->is('liberty-users') || $request->is('external-surveyors')
                || $request->is('broker-users');

            if ($validRequestType && strtolower($request->method()) == 'post') {
                try {
                    $data['secret'] = (new Google2FA)->generateSecretKey();
                } catch (
                    IncompatibleWithGoogleAuthenticatorException|
                    InvalidCharactersException|
                    SecretKeyTooShortException $e
                ) {
                    // do nothing
                }
            }

            if ($request->is('liberty-users') || $request->is('brokers')) {
                //Upload underwriter logo
                if ($request->hasFile('image')) {
                    $files = new FileUpload();
                    $file = $request->file('image');
                    $ext = $request->file('image')->getClientOriginalExtension();
                    $uuid = Str::uuid()->toString();
                    if (!is_bool($files->upload($file->getRealPath(), 'liberty_users_images/' . $uuid . '.' . $ext))) {
                        return Redirect::back()->with('error', 'Failed to upload image')->withInput();
                    }

                    $data['image'] = $uuid . '.' . $ext;
                }
            }

            $api = json_decode(
                Api::post(
                    static::get_api_uri(),
                    $data
                )
            );

            if ($api->response == 'success') {

                if (isset($data['method']) && $data['method'] == 'duplicate') {
                    if (isset($api->data->files)) {
                        foreach ($api->data->files as $key => $file_name) {
                            $this->lessonObjects->duplicateFolder(
                                $file_name, $api->data->course_id, $data["source"],
                                $api->data->id
                            );
                        }
                    }
                }

                /** @var array $data */
                unset($data['source']);

                if (($request->is('surveys') || $request->is('dtr')) && strtolower($request->method()) == 'post') {
                    if (Session::get('ApiFiles')) {
                        $api->sessions = Session::get('ApiFiles');
                        Api::post('api/v1/surveys/update-survey-attachments-empty', $api);
                    }

                    if (isset($request->resurvey_id) && !empty($request->file_count)) {
                        $getResponseId = Api::get('api/v1/surveys/get-survey-files-for-id/' . $request->resurvey_id);
                        $files = json_decode($getResponseId);
                        $duplicatedFiles = [];
                        foreach ((array)$files->data as $file) {
                            $duplicatedFiles[] = [
                                'survey_id'       => $api->survey_id,
                                'file_name'       => $file->file_name,
                                'cloud_file_name' => $file->cloud_file_name,
                                'created_at'      => now(),
                                'updated_at'      => now(),
                            ];
                        }

                        Api::post('api/v1/surveys/add-resurvey-attachments', $duplicatedFiles);
                    }

                    Session::forget('ApiFiles');
                }

                if ($request->is('calendar/schedule/re-admin') && strtolower($request->method()) == 'post') {
                    if (Session::get('ApiFilesREAdmin')) {
                        $api->sessions = Session::get('ApiFilesREAdmin');
                    }
                    $updateResponse = Api::post('api/v1/schedule/update-schedule-attachments-empty', $api);
                }

                if (method_exists(get_called_class(), 'onStoreSuccess')) {
                    static::onStoreSuccess($data);
                }

                if (isset($data['add_note']) && !empty($data['add_note']) && $data['add_note'] == "true") {
                    return Redirect::route(
                        'organisation.notes.create',
                        $data['client_organisation_id']
                    )->with(['task_data' => $data]);
                }

                if ($request->ajax() && static::RESPOND_TO_AJAX) {
                    return response()->json([
                        'response' => $api->response,
                        'message' => $api->message,
                    ]);
                } else {
                    return Redirect::route(static::getRoutePrefix() . '.index')->with($api->response, $api->message);
                }
            } else {
                if (method_exists(get_called_class(), 'onStoreError')) {
                    static::onStoreError($data);
                }

                if ($request->ajax() && static::RESPOND_TO_AJAX) {
                    return Response::json(
                        [
                        'errors' => $api->errors,
                        ], 400
                    );
                } else {
                    return Redirect::back()->withErrors(
                        $api->errors
                    )->withInput();
                }
            }
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param int $id
     */
    public function update(Request $request, $id)
    {
        if (!$request->has('update_status')) {
            $validator = Validator::make(
                $request->all(),
                static::getValidatorRules($request, 'update'),
                static::getValidatorMessages('update')
            );
        } else {
            $validator = Validator::make(
                $request->all(),
                static::getValidatorRules($request, 'update_status'),
                static::getValidatorMessages('update_status')
            );
        }

        $this->determineNestedResourceID($id);
        $url_params = Route::current()->parameters();

        if (method_exists(get_called_class(), 'getValidatorAttributeNames')) {
            $validator->setAttributeNames(static::getValidatorAttributeNames());
        }

        if ($validator->fails()) {
            if ($request->ajax() && static::RESPOND_TO_AJAX) {
                return Response::json(
                    [
                    'invalid' => $validator->errors(),
                    ], 400
                );
            } else {
                return Redirect::route(static::getRoutePrefix() . '.edit', $url_params)
                    ->withErrors($validator->errors())
                    ->withInput();
            }
        } else {
            $data = $request->all();
            foreach ($data as $key => $value) {
                if ($key != 'legacy_srf') {
                    $data[$key] = ($value === '')
                        ? null
                        : $value;
                }
            }

            foreach (['_method', '_token'] as $property) {
                if (isset($data[$property])) {
                    unset($data[$property]);
                }
            }

            if (method_exists(get_called_class(), 'parseDataBeforeStorage')) {
                $data['is_update'] = true;
                $data = static::parseDataBeforeStorage($data);
            }

            if ($request->is('liberty-users/*') || $request->is('brokers/*')) {
                // Upload underwriter logo
                if ($request->hasFile('image')) {
                    $files = new FileUpload();
                    $file = $request->file('image');
                    $ext = $request->file('image')->getClientOriginalExtension();
                    $uuid = Str::uuid()->toString();
                    if (!is_bool($files->upload($file->getRealPath(), 'liberty_users_images/' . $uuid . '.' . $ext))) {
                        return Redirect::back()->with('error', 'Failed to upload image')->withInput();
                    }

                    $data['image'] = $uuid . '.' . $ext;
                } else {
                    if (isset($data['image_id'])) {
                        $data['image'] = $data['image_id'];
                    }
                }
            }

            // Set to default empty string if client didn't provide description for Executive Summary
            $data['commentary_description'][0] ??= '';
            $data['commentary_additional_notes'][0] ??= '';

            $api = json_decode(Api::put(static::get_api_uri($id), $data));
            if ($api->response == 'success') {
                if (method_exists(get_called_class(), 'onUpdateSuccess')) {
                    static::onUpdateSuccess($data);
                }

                if (isset($data['update_status'])) {
                    return Redirect::back()->with('success', 'Your update was successful.');
                }

                if ($request->has('survey_type') && $request->get('survey_type') == 'rereview') {
                    return Redirect::route(
                        static::ROUTE_PREFIX . '.re-reviews-list'
                    );
                }

                if ($request->ajax() && static::RESPOND_TO_AJAX) {
                    return Response::json(
                        [
                        'response' => $api->response,
                        'message' => $api->message,
                        ]
                    );
                }

                return Redirect::route(static::getRoutePrefix() . '.edit', $url_params)
                    ->with($api->response, $api->message);
            } else {
                if (method_exists(get_called_class(), 'onUpdateError')) {
                    static::onUpdateError($data);
                }

                if ($request->ajax() && static::RESPOND_TO_AJAX) {
                    return Response::json(
                        [
                        $api->response => $api->message,
                        ], 400
                    );
                } else {
                    if (isset($api->message)) {
                        return Redirect::back()
                            ->withInput()
                            ->with($api->response, $api->message);
                    }
                        return Redirect::back()
                            ->withInput();
                }
            }
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     */
    public function destroy($id)
    {
        $this->determineNestedResourceID($id);
        $url_params = Route::current()->parameters();

        $api = json_decode(
            Api::delete(
                static::get_api_uri($id)
            )
        );

        if (method_exists(get_called_class(), 'onDestroySuccess')) {
            static::onDestroySuccess($id);
        }

        return Redirect::route(
            static::getRoutePrefix() . '.index',
            array_slice($url_params, 0, -1)
        )->with(
            $api->response,
            $api->message
        );
    }

    /**
     * Get additional view parameters for a specific view (index/create/edit)
     *
     * @param  string  $view
     * @param  array  $params
     * @return array|mixed|void
     */
    public function getRouteParams(Request $request, string $view, array $params = [])
    {
        switch ($view) {
        case 'index':
        case 'create':
        case 'edit':
            return $params;
        }
    }

    /**
     * Get additional view parameters for a specific view (index/create/edit)
     *
     * @param  string  $view
     * @return array|void
     */
    public function getAdditionalViewParams(string $view, $resource)
    {
        switch ($view) {
        case 'index':
        case 'create':
        case 'edit':
            return [];
        }
    }

    /**
     * Get validation rules for a specific method (store/update/etc)
     *
     * @param  string $method
     * @return array of validation rules
     */
    public function getValidatorRules(Request $request, $method)
    {
        return [];
    }

    /**
     * Get validation messages for a specific method (store/update/etc)
     *
     * @param  string $method
     * @return array of validation rules
     */
    public function getValidatorMessages($method)
    {
        return [];
    }


    /**
     * If the Resource is nested, determine the $id from the last parameter used in the route
     *
     * @param  integer $id
     */
    protected function determineNestedResourceID(&$id)
    {
        if (count(Route::current()->parameters()) > 1) {
            $routes = Route::current()->parameters();
            $routes = array_slice($routes, -1);
            $id = array_pop($routes);
        }
    }

    private function getRequestUrl(String $url): string
    {
        $defaultUrl = 'api/v1/';
        if(static::get_api_uri() != $defaultUrl) return '';
        if(str_starts_with($url, '/')){
            $url = substr($url, 1) . '/';
            return $url;
        }

        return '';
    }
}

