<?php

namespace App\Http\Controllers;

use App\Models\Api;
use App\Models\User;
use Illuminate\Http\Request;
use App\Imports\BorderauImport;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Validator;
use PhpOffice\PhpSpreadsheet\Shared\Date as PhpSpreadsheetDate;

class OrganisationBordereauController extends BaseController
{
    //TEMPLATE PATH
    const TEMPLATE_PATH = '/bordereau';

    public function allowForUser(User $user): bool
    {
        return $user->login_type === 'risk-control' || $user?->isRoleAdminOrAccountManager();
    }

    //INDEX
    public function index()
    {
        $data['sectors'] = json_decode(Api::get('api/v1/sector/all'))->data;
        $data['policy_types'] = json_decode(Api::get('api/v1/policy-types/all'))->data;
        $data['broker_organisations'] = json_decode(Api::get('api/v1/brokers/all/0/0'))->data;
        $data['mga_schemes'] = json_decode(Api::get('/api/v1/mga-schemes/all'))->data;
        $data['pendingSubmissions'] = json_decode(Api::get('api/v1/bordereau/all'))->data;

        return view(
            static::TEMPLATE_PATH . '/upload',
            $data
        );
    }

    public function upload(Request $request)
    {
        $data = $request->except('_token');

        $validation = Validator::make(
            $data,
            [
                'file' => 'required',
                'sector_id' => 'required',
                'attachment_type' => 'required',
                'policy_type_id' => 'required',
                'broker_id' => 'required_without:mga_scheme',
                'mga_scheme' => 'required_without:broker_id',
            ],
            [
                'file.required' => "The File field is required.",
                'sector_id.required' => "The Sector field is required.",
                'attachment_type.required' => "The Organisation Attachment field is required.",
                'policy_type_id.required' => "The Policy Type field is required.",
                'broker_id.required_without' => "The Broker field is required when client organisation is attached to a Broker.",
                'mga_scheme.required_without' => "The MGA Scheme field is required when client organisation is attached to a MGA Scheme."
            ]
        );

        if ($validation->fails()) {
            return Redirect::back()
                ->withErrors($validation->errors())
                ->withInput($request->old());
        }

        $request->file('file')->move(storage_path(), 'borderau.xlsx');
        $borderauData = Excel::toCollection(new BorderauImport(), storage_path('borderau.xlsx'))->all();
        $borderauData = $borderauData[0] ?? collect([]);

        $insert = [];

        if (!empty($borderauData) && $borderauData->count()) {
            foreach ($borderauData as $key => $value) {
                $value = (object) $value->all();
                if ($value->name == null && $value->policy_number == null) continue;

                $inceptionDate =  (!empty($value->inception_date_of_cover ?: '')) ? PhpSpreadsheetDate::excelToDateTimeObject($value->inception_date_of_cover)->format('Y-m-d H:i:s') : "-";
                $expiryDate =  (!empty($value->expiry_date_of_cover ?: '')) ? PhpSpreadsheetDate::excelToDateTimeObject($value->expiry_date_of_cover)->format('Y-m-d H:i:s') : "-";

                $insert[] = [
                    'name' => $value->name,
                    'attached_organisation_type' => $data['attachment_type'],
                    'attached_organisation_id' => (isset($data['broker_id']) && !empty($data['broker_id'])) ? $data['broker_id'] : $data['mga_scheme'],
                    'sector_id' => $data['sector_id'],
                    'policy_type_id' => $data['policy_type_id'],
                    'liberty_branch' => $value->liberty_branch ?? "",
                    'policy_number' => $value->policy_number,
                    'inception_date_of_cover' => $inceptionDate,
                    'expiry_date_of_cover' => $expiryDate,
                    'premium' => !empty($value->premium) ? $value->premium : "-",
                    'description' => !empty($value->description) ? $value->description : "-",
                    'email' => $value->email,
                    'phone' => $value->phone,
                    'address_line_1' => $value->address_line_1,
                    'address_line_2' => $value->address_line_2,
                    'postcode' => $value->postcode,
                    'country' => $value->country,
                    'image' => $value->image ?? "",
                    'admin_first_name' => $value->admin_first_name,
                    'admin_last_name' => $value->admin_last_name,
                    'admin_job_title' => $value->admin_job_title,
                    'admin_email' => $value->admin_email,
                    'admin_phone' => $value->admin_phone,
                    'manager' => 1,
                    'croner_access' => 1,
                ];
            }
        }

        $data = [
            'insert' => $insert,
        ];

        $response = json_decode(Api::post('api/v1/bordereau/upload', $data));

        return Redirect::back()
            ->with($response->status, $response->message);
    }

    public function show($submission_id)
    {
        $data = [
            'submission_id' => $submission_id
        ];

        $data['submission'] = json_decode(Api::post('api/v1/bordereau/view', $data))->data;

        return view(
            static::TEMPLATE_PATH . '/pending-submission',
            $data
        );
    }

    public function approve(Request $request)
    {
        $data = $request->except('_token');

        if (isset($data['submission_ids']) && !empty($data['submission_ids'])) {
            $data = [
                'submission_ids' => $data['submission_ids'],
                'is_individual_approval' => (isset($data['is_individual_approval']) && !empty($data['is_individual_approval'])) ? $data['is_individual_approval'] : null
            ];

            $response = json_decode(Api::post('api/v1/bordereau/approve', $data));

            return Redirect::back()
                ->with($response->status, $response->message);
        } else {
            return Redirect::back()
                ->with('error', 'Please check boxes to approve submission/s.');
        }
    }

    public function reject(Request $request)
    {
        $data = $request->except('_token');

        if (isset($data['submission_ids']) && !empty($data['submission_ids'])) {
            $data = [
                'submission_ids' => $data['submission_ids']
            ];

            $response = json_decode(Api::post('api/v1/bordereau/reject', $data));

            return Redirect::route('organisation.bordereau.import')->with($response->status, $response->message);
        } else {
            return Redirect::back()
                ->with('error', 'Please check boxes to reject submission/s.');
        }
    }
}
