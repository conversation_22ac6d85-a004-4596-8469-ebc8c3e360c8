<?php

namespace App\Http\Controllers;

use App\Helpers\RiskGradingHelper;
use App\Imports\OrgLocationImport;
use Illuminate\Http\Request;
use App\Models\Api;
use App\Services\CacheContent\GetOrganisationRiskGradingOverview;
use App\Services\SendSqsMessageService;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\App;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Validator;
class OrganisationLocationController extends BaseController
{
    //TEMPLATE PATH
    const TEMPLATE_PATH = '/organisation/location';
    const GRADE_NOT_APPLICABLE_NOT_ASSESSED = 'Not Applicable / Not Assessed';

    public function __construct(Request $request)
    {
        parent::__construct($request);

        // Check allowed uri request
        $is_risk_grading_log = $request->segment(2);

        if (Session::has('user')) {
            $loginType        = Session::get('user')->login_type;
            $allowedUserTypes = ['risk-control', 'risk-engineer', 'underwriter'];

            if (!in_array($loginType, $allowedUserTypes) && $is_risk_grading_log!='risk-grading-logs') {
                return App::abort(401, 'Unauthorized.');
            }
        }
    }

    //INDEX
    public function index($organisation_id)
    {
        $data['organisation'] = json_decode(Api::get('api/v1/organisation/' . $organisation_id))->data;
        $data['locations'] = json_decode(Api::get('api/v1/location/'.$organisation_id))->data;
        $countries=config('countries');

        foreach ($data['locations'] as $key => $value) {
            $value->country_name=isset($countries[$value->country])?$countries[$value->country]:'';
        }

        return view(
            static::TEMPLATE_PATH . '/upload',
            $data
        );
    }

    public function upload(Request $request)
    {
        $data           = $request->except('_token');
        $extension      = pathinfo($data['file']->getClientOriginalName(), PATHINFO_EXTENSION);
        $supportedTypes = ['xlsx','xls'];

        if (!in_array($extension, $supportedTypes)) {
            return Redirect::back()
                ->with('error', 'Please upload the valid excel file');
        }

        $organisation_id = $data['organisation_id'];
        $validation      = Validator::make($data, [
            'file'=>'required',
        ], [
            'file.required' => "Please upload the valid excel file",
        ]);

        if ($validation->fails()) {
            return Redirect::back()
                ->withErrors($validation->errors())
                ->withInput($request->old());
        }

        $request->file('file')->move(storage_path(), 'location.xlsx');
        $locationData = Excel::toCollection(new OrgLocationImport, storage_path('location.xlsx'))->all();
        $locationData = $locationData[0];

        if (!$locationData[0]->has('location_name')) {
            return Redirect::back()
                ->with('error', 'Please upload the valid excel file');
        }

        $insert             = [];
        $countries          = config('countries');
        $InvalidcountryCodes= [];
        if (!empty($locationData) && $locationData->count()) {
            foreach ($locationData as $key => $value) {

                if ($value['county'] !='' && !isset($countries[$value['country']])) {
                    $InvalidcountryCodes[] = $value['country'];
                }

                if ($value['location_name'] != '') {
                    $insert[] = [
                    'organisation_id'   => $organisation_id,
                    'location_id'       => $value['location_id'],
                    'location_name'     => $value['location_name'],
                    'postcode'          => $value['postcode'],
                    'address_line_1'    => $value['address_line_1'],
                    'address_line_2'    => $value['address_line_2'],
                    'city'              => $value['city'],
                    'county'            => $value['county'],
                    'country'           => $value['country'],
                    'buildings'         => $value['buildings'],
                    'me'                => $value['me'],
                    'stock'             => $value['stock'],
                    'other'             => $value['other'],
                    'gross_revenue'     => $value['gross_revenue'],
                    'indefinity_period' => $value['ip'],
                    'icow'              => $value['icow'],
                    ];
                }
            }
        }

        if (count($InvalidcountryCodes)>0) {
            $existing=implode(", ", $InvalidcountryCodes);
            return Redirect::back()
            ->with('error', 'Location import failed. Invalid country code(s) '.$existing);
        }

        $response = json_decode(Api::post('api/v1/location/upload', $insert));

        return Redirect::back()
            ->with($response->status, $response->message);
    }

    public function edit($location_id)
    {
        $data['location']     = json_decode(Api::get('api/v1/location/show/'.$location_id))->data;
        $organisation         = json_decode(Api::get('api/v1/organisation/' . $data['location']->organisation_id))->data;
        $data['organisation'] = (object)['id'=>$organisation->id,'name'=>$organisation->name];

        $data['colors'] = RiskGradingHelper::getGradingColorCodes();
        $data['gradingLabels'] = RiskGradingHelper::MAPPINGS;

        $policyList  = json_decode(Api::get('api/v1/standard-risk/policy-types'));

        foreach($policyList as $type){
            $data['policy_types'][$type->id] = '('.preg_replace('/\b(\w)|./', '$1', $type->name).')';
        }

        $data['countries'] = config('countries');

        return view(
            static::TEMPLATE_PATH . '/edit',
            $data
        );
    }

    public function update(Request $request)
    {
        $data = $request->except('_token');

        $validation = Validator::make(
            $data, [
            'location_name' => 'required',
            ], [
            'location_name.required' => "The Location name field is required.",
            ]
        );

        if ($validation->fails()) {
            return Redirect::back()
                ->withErrors($validation->errors())
                ->withInput($request->old());
        }

        if (($data['is_risk_grading_updated'] ?? 'false') === 'false') {
            unset($data['updated_gradings']);
        }

        $response = json_decode(Api::put('/api/v1/location/update', $data));
        if ($response) {
            if (isset($response->response) && $response->response == 'success') {
                $this->recacheRelatedData($data['organisation_id'] ?? '');
                return Redirect::route('organisation.locations.edit-location', $data['id'])->with('success', 'Location data updated successfully');
            }
        }

        return Redirect::route('organisation.locations.edit-location', $data['id'])->with('error', 'Location data updated failed');
    }

    public function delete($id, $location_id)
    {
        $response = json_decode(Api::post('/api/v1/location/delete/'.$location_id));
        $this->recacheRelatedData($id);
        return Redirect::route('organisation.locations.create', $id)->with($response->response, $response->message);
    }

    public function getLocations($id)
    {
        return json_decode(Api::get('api/v1/location/'.$id))->data;
    }

    public function download()
    {
        $storage = storage_path('locations');

        $file = $storage.'/Location_Management_template.xlsx';
        $headers = [
            'Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ];
        return Response::download($file, 'Location_Management_template.xlsx', $headers);
    }

    public function createLocation($organisation_id)
    {
        $data['organisation'] = json_decode(Api::get('api/v1/organisation/'.$organisation_id))->data;
        $data['countries'] = config('countries');
        return view(static::TEMPLATE_PATH.'/create', $data);
    }

    public function storeLocation(Request $request)
    {
        $data = $request->except('_token');

        $rules = array(
            'location_id' => 'required',
            'location_name' => 'required',
            'postcode' => 'required',
            'address_line_1' => 'required',
            'city' => 'required',
            'country' => 'required',
            'buildings' => 'required',
            'me' => 'required',
            'stock' => 'required',
            'gross_revenue' => 'required',
            'other' => 'required',
            'indefinity_period' => 'required',
            'icow' => 'required',
        );

        $messages = array(
            'location_id.required' => 'The Location ID field is required.',
            'indefinity_period.required' => 'The Indemnity period field is required.',
            'location_name.required' => 'The Location Name field is required.',
            'postcode.required' => 'The Postcode field is required.',
            'address_line_1.required' => 'The Address Line 1 field is required.',
            'city.required' => 'The City field is required.',
            'country.required' => 'The Country field is required.',
            'buildings.required' => 'The Buildings field is required.',
            'me.required' => 'The ME field is required.',
            'stock.required' => 'The Stock field is required.',
            'gross.required' => 'The Gross field is required.',
            'other.required' => 'The Other field is required.',
            'icow.required' => 'The ICOW field is required.',
        );

        $validator = Validator::make($data, $rules, $messages);

        if ($validator->fails()) {
            return Redirect::back()
                ->withInput($request->input())
                ->withErrors($validator->errors());
        }

        $data = [
            'organisation_id' => $data['organisation_id'],
            'location_id' => $data['location_id'],
            'location_name' => $data['location_name'],
            'postcode' => $data['postcode'],
            'address_line_1' =>$data['address_line_1'],
            'address_line_2' =>$data['address_line_2'],
            'city' => $data['city'],
            'county' => $data['county'],
            'country' => $data['country'],
            'buildings' => $data['buildings'],
            'me' => $data['me'],
            'stock' => $data['stock'],
            'other' => $data['other'],
            'gross_revenue' => $data['gross_revenue'],
            'indefinity_period' => $data['indefinity_period'],
            'icow' => $data['icow'],
            'location_creation_type' => 'manual'
        ];

        $response = json_decode(Api::post('api/v1/location/upload', $data));

        if ($response->status==='success') {
            $this->recacheRelatedData($data['organisation_id'] ?? '');

            return Redirect::route('organisation.locations.create', $data['organisation_id'])->with('success', 'Location data updated successfully');
        }

        if ($response->status==='error') {
            return Redirect::route('organisation.locations.create-location', $data['organisation_id'])->withErrors($response->error_messages);
        }
    }

    public function riskGradingLogs(Request $request)
    {
        $data = $request->except('_token');
        $response = json_decode(Api::post('/api/v1/location/risk-grading-logs', $data))->data;
        return Response::json($response);
    }

    public function riskGradingLogsOverview(Request $request)
    {
        $data = $request->except('_token');
        $response = json_decode(Api::post('/api/v1/location/risk-grading-logs-overview', $data));
        return Response::json($response);
    }

    public function calculateAttributeGrade(Request $request)
    {
        $data = $request->except('_token');

        // prepare payload for calculator
        $attributes = $data['updated_gradings'];
        $attr = $data['main_attr'];
        $calculatorPayload = [];

        foreach ($attributes as $attribute => $value) {
            $fields = explode("_", $attribute);

            $subAttribute = $fields[0];
            $policyTypeId = $fields[1];
            $attributeType = $fields[2];
            $attributeField = $fields[3];
            if ($attributeField == $attr && $attributeType=='sub-attribute' && $value != static::GRADE_NOT_APPLICABLE_NOT_ASSESSED) {
                $designatedPolicyId =  $policyTypeId;
                $calculatorPayload['sub-attribute-'.$subAttribute.'|'.str_replace("-", "_", $attr).'-'.$policyTypeId] =  $value;
            }
        }

        $calculatorPayload['attr-'.str_replace("-", " ", $attr)] = '';
        $calculatorPayload['attribute_name'] = str_replace("-", " ", $attr);
        $calculatorPayload['policy_type_id'] = $designatedPolicyId;

        return [
            'data' => json_decode(Api::post('/api/v1/standard-risk/calculate', $calculatorPayload)),
        ];
    }

    public function recacheRelatedData($orgId)
    {
        SendSqsMessageService::sendMessages([
            [
                'serviceClass' => GetOrganisationRiskGradingOverview::class,
                'params' => $orgId ?? '',
            ]
        ]);
    }

    public function showBulkUploadForm($organisation_id)
    {
        $data['organisation'] = json_decode(Api::get('api/v1/organisation/'.$organisation_id))->data;
        $data['countries'] = config('countries');
        return view(static::TEMPLATE_PATH.'/bulk-upload', $data);
    }

    public function bulkUploadLocationValidation($organisation_id, Request $request)
    {
        $data           = $request->except('_token');
        $extension      = pathinfo($data['file']->getClientOriginalName(), PATHINFO_EXTENSION);
        $supportedTypes = ['xlsx','xls','csv'];
        
        if (!in_array($extension, $supportedTypes)) {
            return Redirect::back()
                ->with('error', 'Please upload the valid excel file');
        }

        $organisation_id = $data['organisation_id'];
        $validation      = Validator::make($data, [
            'file' => 'required|file|mimes:csv,xlsx,xls',
        ], [
            'file.required' => "Please upload the valid excel file",
        ]);
        
        if ($validation->fails()) {
            return Redirect::back()
                ->withErrors($validation->errors())
                ->withInput($request->old());
        }

        $request->file('file')->move(storage_path(), 'bulk-upload-location.xlsx');
        $locationData = Excel::toArray(new OrgLocationImport, storage_path('bulk-upload-location.xlsx'));
        $firstSheet = $locationData[0] ?? [];
        $firstRow = $firstSheet[0] ?? [];

        $firstKey = array_key_first($firstRow);

        if ($firstKey !== 'location_name') {
            return Redirect::back()->with('error', 'Please upload the valid excel file using the template.');
        }

        $locationUploadData             = [];
        $countries          = config('countries');
        $InvalidcountryCodes= [];

        if (!empty($firstSheet) && count($firstSheet)) {
            foreach ($firstSheet as $location) {

                if ($location['country'] !='' && !isset($countries[$location['country']])) {
                    $InvalidcountryCodes[] = $location['country'];
                }

                if ($location['location_name'] != '') {
                    $locationUploadData[] = [
                    'organisation_id'   => $organisation_id,
                    'location_name'     => $location['location_name'],
                    'postcode'          => $location['postcode'],
                    'address_line_1'    => $location['address_line_1'] ?? 'placeholder',
                    'city'              => $location['city'] ?? 'placeholder',
                    'country'           => $location['country'],
                    'segment_name'      => $location['segment_name'],
                    'rating_code'       => $location['rating_code'],
                    'treaty_class_occupancy' => $location['treaty_class_occupancy'],
                    'tiv'               => $location['tiv'],
                    ];
                }
            }
        }

        $response = json_decode(Api::post('api/v1/location/bulk-match-locations/'.$organisation_id, $locationUploadData));
        return Redirect::back()->with('response', $response);
    }

    public function bulkUploadLocations($organisation_id, Request $request)
    {
        $data = $request->except('_token');
        $response = json_decode(Api::post('api/v1/location/bulk-upload-locations/'.$organisation_id, $data));
        return response()->json($response);
    }

    public function downloadBulkUploadTemplate()
    {
        $storage = storage_path('locations');

        $file = $storage.'/Bulk_Location_Upload_template.xlsx';
        $headers = [
            'Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ];

        return Response::download($file, 'Bulk_Location_Upload_template.xlsx', $headers);
    }
}
