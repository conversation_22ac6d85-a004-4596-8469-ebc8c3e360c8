<?php

namespace App\Http\Controllers;
use App\Models\User;
use Illuminate\Http\Request;
use App\Models\FileUpload;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Response;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Str;
use Barryvdh\DomPDF\Facade\Pdf;
use \RecursiveIteratorIterator;
use \RecursiveDirectoryIterator;
use \ZipArchive;
class RhsExportController extends BaseController
{

    //TEMPLATE PATH
    const TEMPLATE_PATH = '/affiliates/rhs';

    //ROUTE PREFIX
    const ROUTE_PREFIX = 'affiliates/rhs';

    public function __construct(Request $request, FileUpload $fileUpload)
    {
        parent::__construct($request);
        $this->files = $fileUpload;
    }

    public function allowForUser(User $user): bool
    {
        return $user->login_type === 'risk-control' || $user?->isRoleAdminOrAccountManager();
    }

    public function generatePdf(Request $request)
    {
        if (strtolower($request->method()) == 'post') {
            $path = storage_path().'/rhs';
            $file = $request->file('excel')->move($path, 'rhs-export.xlsx');;

            $data = Excel::load(
                $path.'/rhs-export.xlsx', function ($reader) {
                }
            )->get();
            if (!empty($data) && $data->count()) {
                foreach ($data as $key => $value) {
                    if ($value->affiliate_name == '') {
                        break;
                    }
                    $arr = [
                        'affiliate_name' => $value->affiliate_name,
                        'affiliate_no' => $value->affiliate_no,
                        'sect_policy_no' => $value->sect_policy_no,
                        'sect_period_of_ins' => $value->sect_period_of_ins,
                        'sect_loi_gbp' => $value->sect_loi_gbp,
                        'sect_money_loi_gbp' => $value->sect_money_loi_gbp,
                        'sect_deductible_gbp' => $value->sect_deductible_gbp,
                        'sect_premium_gbp' => $value->sect_premium_gbp,
                        'sect_ipt_gbp' => $value->sect_ipt_gbp,
                        'sect_rhs_fee_gbp' => $value->sect_rhs_fee_gbp,
                        'total_sect_premium_gbp' => $value->total_sect_premium_gbp,
                        'liabs_policy_no' => $value->liabs_policy_no,
                        'liabs_period_of_ins' => $value->liabs_period_of_ins,
                        'pl_loi_gbp' => $value->pl_loi_gbp,
                        'el_loi_gbp' => $value->el_loi_gbp,
                        'tppd_deductible_gbp' => $value->tppd_deductible_gbp,
                        'liabs_premium_gbp' => $value->liabs_premium_gbp,
                        'liabs_ipt_gbp' => $value->liabs_ipt_gbp,
                        'liabs_rhs_fee_gbp' => $value->liabs_rhs_fee_gbp,
                        'total_liabs_premium_gbp' => $value->total_liabs_premium_gbp
                    ];

                    $insert[] = $arr;
                }
            }

            $pdf = PDF::loadView(
                static::TEMPLATE_PATH.'/'.$request->get('viewType'), [
                'affiliates' => $insert,
                'issuance_date' => date('jS F Y', strtotime(str_replace('/', '-', $request->get('issuance_date')))),
                'logo' => url('/').'/img/logo.svg'
                ]
            );

            $uuid = Str::uuid()->toString();

            $binder_path = $path.'/'.$uuid.'/'.'binder.pdf';

            $binder_dir = $path.'/'.$uuid.'/';

            $pdf->save($binder_path);

            $j = 0;

            $from_page = 1;

            for ($i = 1; $i <= count($insert); $i = $i + 1) {

                if (is_numeric($insert[$j]["el_loi_gbp"])) {
                    $to_page = $from_page + 1;
                } else {
                    $to_page = $from_page;
                }

                $pdfName = urlencode($insert[$j]["affiliate_name"]);
                //$pdfName = str_replace(' ', '-', trim($pdfName));

                exec('pdftk '.$binder_path.' cat '.$from_page.'-'.$to_page.' output '.trim($binder_dir.$pdfName).'.pdf');

                $from_page = $to_page + 1;
                $j = $j + 1;
            }


            // Get real path for our folder
            $rootPath = realpath($path.'/'.$uuid);

            // Initialize archive object
            $zip = new ZipArchive();
            $zip->open($path.'/rhs_pdf.zip', ZipArchive::CREATE | ZipArchive::OVERWRITE);

            // Create recursive directory iterator
            /**
 * @var SplFileInfo[] $files
*/
            $files = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($rootPath),
                RecursiveIteratorIterator::LEAVES_ONLY
            );

            foreach ($files as $name => $file) {
                // Skip directories (they would be added automatically)
                if (!$file->isDir()) {
                    // Get real and relative path for current file
                    $filePath = $file->getRealPath();
                    $relativePath = substr($filePath, strlen($rootPath) + 1);

                    // Add current file to archive
                    $zip->addFile($filePath, $relativePath);
                }
            }

            // Zip archive will be created only after closing object
            $zip->close();

            sleep(10);

            $fileUrl = $path.'/rhs_pdf.zip';

            return Response::download(
                $fileUrl, 'rhs_pdf.zip', array(
                'Content-Type: application/octet-stream',
                'Content-Length: '.filesize($fileUrl)
                )
            )->deleteFileAfterSend(false);

            // return $pdf->download('rhs.pdf');

        } else {
            return view(
                static::TEMPLATE_PATH.'/exceltopdf'
            );
        }
    }

}
