<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Session;

class GDPRManager
{
    /**
     * Allowed roles to access gdpr page
     * 
     *  @var array
     */
    const ALLOWED_ROLES = [
        'admin',
        'risk-control',
    ];

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $user = Session::get('user');
        if (isset($user) && !in_array($user->login_type, self::ALLOWED_ROLES) && !$user?->isRoleAdminOrAccountManager()) {
            return redirect('/')
                ->with('error', config('gdpr.messages.errors.401'));
        }
        return $next($request);
    }
}
