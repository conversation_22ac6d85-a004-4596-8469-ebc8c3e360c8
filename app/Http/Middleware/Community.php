<?php

namespace App\Http\Middleware;
use Route;
use Session;

class Community
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $login_type = Session::get('user')->login_type;
        $user_role = Session::get('user')->role;
    
        if (!($login_type == 'underwriter' || $login_type == 'risk-engineer' ||
            ($login_type == 'risk-control' && Session::get('user')?->isRoleAdminOrAccountManager()) || User::isBrokerAndAllowedInCommunity())
        ) {
            App::abort(403, 'Unauthorized action. '.Route::currentRouteName());
        }
        return $next($request);
    }
}
