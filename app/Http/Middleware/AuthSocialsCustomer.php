<?php

namespace App\Http\Middleware;
use Session;
use Request;

class AuthSocialsCustomer
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if (!Session::has('socials-customer') && !Session::has('socials-role')) {
            return Redirect::to('virtual-rooms/customer-login');
        }
        return $next($request);
    }
}
