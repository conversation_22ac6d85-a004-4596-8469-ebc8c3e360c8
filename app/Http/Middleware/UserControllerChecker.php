<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response as SymfonyResponse;

class UserControllerChecker
{
    const LOGIN_USER_TYPE_WHITELIST = [
        'risk-control',
        'underwriter',
        'risk-engineer',
        'broker-user',
    ];

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $login_type = Session::get('user')->login_type ?? "";

        if (!in_array($login_type, self::LOGIN_USER_TYPE_WHITELIST)) {
            abort(SymfonyResponse::HTTP_UNAUTHORIZED);
        }

        return $next($request);
    }
}
