<?php

namespace App\Http\Middleware;

use App\Helpers\Helpers;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;

class AuthPortfolioViews
{
    /**
     * Handle an incoming request.
     *
     * @param  Request  $request
     * @param  \Closure(Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $uri = $request->route()->uri;

        if (Str::contains($uri, 'insights') && !Helpers::canAccessPortfolioView('insights')) {
            abort(401);
        }

        if (Str::contains($uri, 'customise') && !Helpers::canAccessPortfolioView('customise')) {
            abort(401);
        }
        
        return $next($request);
    }
}
