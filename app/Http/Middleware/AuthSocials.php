<?php

namespace App\Http\Middleware;

use Illuminate\Support\Facades\Route;
use Illuminate\Http\Request;
use Closure;
use Illuminate\Support\Facades\Session;

class AuthSocials
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        if (!Session::has('socials-user') && !Session::has('socials-role')) {
            $url = $request->url();
            Session::put('login-attempt-url', $url);
            return redirect()->to('virtual-rooms/login');
        }
        return $next($request);
    }
}
