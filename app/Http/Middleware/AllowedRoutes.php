<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Session;

class AllowedRoutes
{
    /**
     * Handle an incoming request.
     *
     * @param  Request  $request
     * @param  \Closure(Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $user = Session::get('user');
        $types = ['broker-user'];
        if ($user && Route::currentRouteName() && in_array($user->login_type ?? '', $types)) {
            $exceptions = [
                'organisation.engagement',
                'organisation.overview.index',
                'organisation.overview.edit',
                'organisation.overview.update',
                'organisation.document.retrieve_policy_document',
                'organisation.risk-grading-logs',
                'preview.public-form-submission-withmapping',
                'public_form.show',
                'public-form-submission.print',
                'public-form-submission.print.pdf',
                'kanban.surveynext',
                'kanban.next',
                'search.search',
                'surveys.data',
                'surveys.options',
                'organisation.dashboard-statistics',
                'organisation.additional-details',
                'ri_form.submission.close_issue',
            ];
            $regexExceptions = [
                '/^community|microsite\.(.*)/m'
            ];

            $matches = [];
            foreach ($regexExceptions as $pattern) {
                preg_match_all($pattern, Route::currentRouteName(), $matches, PREG_SET_ORDER, 0);
                if (!empty($matches)) {
                    break;
                }
            }

            if (!(in_array(Route::currentRouteName(), $exceptions)) && empty($matches)) {
                $routes = config('app.allowed-routes.' . $user->login_type, []);
                if (!in_array(Route::currentRouteName(), $routes)) {
                    abort(403, 'Unauthorized action. ' . Route::currentRouteName());
                }
            }
        }

        return $next($request);
    }
}
