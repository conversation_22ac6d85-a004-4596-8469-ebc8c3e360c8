<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Auth\Middleware\Authenticate as Middleware;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Session;
use PragmaRX\Google2FA\Google2FA;

class Authenticate extends Middleware
{
    /**
     * @inheritDoc
     */
    public function handle($request, Closure $next, ...$guards)
    {
        $user = Session::get('user');
        if (!$user) {
            if ($request->has('user-type')) {
                $route = $this->getRedirectRouteByUserType($request->get('user-type'));

                if ($route) {
                    return redirect()->route('login', ['user_type' => $request->get('user-type')]);
                }
            }
            $this->authenticate($request, $guards);
            $user = Session::get('user');

            if (!$user) {
                abort(401); // Unauthorized
            }
        }
        if (config('mfa.enabled')) {
            $twoFactorCookie = config('mfa.cookie') . '_' . md5($user->email);

            // Check 2fa reset
            if (!empty($user->secret)
                && $user->established === 0
                && Cookie::has($twoFactorCookie)) {
                // remove the cookie to redirect back to the 2fa create page
                Cookie::forget($twoFactorCookie);
            }
        
            $invalid = !Cookie::get($twoFactorCookie) && !Session::get('skip2fa');
            if ($invalid) {
                if (!$user->established) {
                    return redirect()->route('two-factor-create');
                }
                return redirect('/two-factor-verify');
            }
        }

        if ($user && $this->shouldBeForcedToLogout()) {
            return redirect('login')
                ->withErrors(["Your session has expired. Please login to continue."]);
        }

        $routeController = Route::getCurrentRoute()->getController();
        if (!$user || (method_exists($routeController, 'allowForUser') && !$routeController->allowForUser($user))) {
            abort(401); // Unauthorized
        }

        if($this->notAllowedRoutesForExternalSurveyor($user)){
            abort(401);
        }

        return $next($request);
    }

    /**
     * Get the path the user should be redirected to when they are not authenticated.
     *
     * @param \Illuminate\Http\Request $request
     * @return string|null
     */
    protected function redirectTo($request)
    {
        if (Auth::guest()) {
            if (!$request->expectsJson()) {
                return '/login';
            }

            if (Input::has('u')) {
                $cookie = Cookie::make('login-cookie', Input::get('u'), 15);
                return Redirect::guest('login')->withCookie($cookie);
            }

            if (Input::has('type') && Input::get('type') == 'broker_user') {
                return Redirect::guest('/login/broker-user');
            }

            return Redirect::guest('login');
        } else {
            $user = Session::get('user');

            if (isset($user)) {
                $routeName = strtolower(Route::currentRouteName());


                if (!empty($routeName)) {
                    $type = Config::get('app.' . $user->type); //$configuredRoutes[$user->type];

                    if (isset($type)) {
                        if (isset($type['roles'])) {
                            $roles = $type['roles'];
                            if (isset($roles[$user->login_type])) {
                                foreach ($roles[$user->login_type] as $key => $value) {
                                    if (redirectNotAccessRoute($value, $routeName)) {
                                        return Response::make('Unauthorized', 401);
                                    }
                                }
                            }
                        } else {
                            foreach ($type['routes'] as $key => $value) {
                                if (redirectNotAccessRoute($value, $routeName)) {
                                    return Response::make('Unauthorized', 401);
                                }
                            }
                        }
                    }
                }
            }
        }

        if (Request::segment(1) == 'organisation' && is_numeric(intval(Request::segment(2))) && intval(
                Request::segment(2)
            ) > 0) {
            if (Session::get('user')->login_type == 'broker-user') {
                // print_r(Api::get('api/v1/broker-users/orgs/'.Session::get('user')->broker_id));exit;
                $orgs_for_broker = json_decode(
                    Api::get('api/v1/broker-users/orgs/' . Session::get('user')->broker_id) . '?open_market=true'
                );

                if ($orgs_for_broker && isset($orgs_for_broker->schemes)) {
                    if (!in_array(intval(Request::segment(2)), $orgs_for_broker->schemes)) {
                        return Response::make('Unauthorized', 401);
                    }
                }
            }
        }

        if (Request::segment(1) == 'users' && is_numeric(intval(Request::segment(2))) && intval(
                Request::segment(2)
            ) > 0) {
            if (Session::get('user')->login_type == 'broker-user') {
                // print_r(Api::get('/api/v1/user/find/'.Request::segment(2)));exit;
                $user = json_decode(Api::get('api/v1/user/find/' . Request::segment(2)));

                // print_r(Api::get('api/v1/broker-users/orgs/'.Session::get('user')->broker_id));exit;
                $orgs_for_user = json_decode(Api::get('api/v1/broker-users/orgs/' . Session::get('user')->broker_id));

                if (
                    $user && isset($user->data) && isset($user->data->organisation_id) &&
                    $orgs_for_user && isset($orgs_for_user->schemes)
                ) {
                    if (!in_array($user->data->organisation_id, $orgs_for_user->schemes)) {
                        return Response::make('Unauthorized', 401);
                    }
                }
            }
        }

        if (Session::get('user')->login_type == 'virtual-rooms' && (Request::segment(
                    1
                ) != 'virtual-rooms' && Request::segment(1) != 'suppression' && Request::segment(
                    1
                ) != 'sha1' && Request::segment(1) != 'register-notification-token')) {
            return Redirect::route('virtual-rooms.representative-call-availability');
        }
    }

    private function notAllowedRoutesForExternalSurveyor($user)
    {
        if($user->login_type === 'external-surveyor' || $user->login_type === 'external-surveyor-admin'){
            $currentRoute = Route::getCurrentRoute()->uri;

            $routes = [
                'brokers',
                'brokers/create',
                'broker-users',
                'mga-schemes',
                'mga-schemes/create',
                'liberty-users',
                'liberty-branches',
                'external-survey-companies',
                'surveys/create-legacy',
                'tracker/surveys',
                'tracker/risk-recommendations',
                'surveys/export/standard-risk-gradings',
                'surveys/export/all-organisations/filter'
            ];
            if(in_array($currentRoute, $routes)){
                return true;
            }
        }

        return false;
    }
    private function getRedirectRouteByUserType(string $userType): ?string
    {
        if (in_array($userType, ['liberty-user', 'external-surveyor', 'broker-user', 'dms-user'])) {
            return route('login', ['user_type' => $userType]);
        }

        return null;
    }
    
    protected function shouldBeForcedToLogout()
    {
        if (Session::has('force_logout_at') && Session::get('force_logout_at') <= time()) {
            Auth::logout();
            return true;
        }

        return false;
    }
}
