<?php

return array(

	'environment' => 'remote',

	'show_lsm'	=> true,

	/*
	|--------------------------------------------------------------------------
	| Application Debug Mode
	|--------------------------------------------------------------------------
	|
	| When your application is in debug mode, detailed error messages with
	| stack traces will be shown on every error that occurs within your
	| application. If disabled, a simple generic error page is shown.
	|
	*/

	'debug' => true,

	/*
	|--------------------------------------------------------------------------
	| Application URL
	|--------------------------------------------------------------------------
	|
	| This URL is used by the console to properly generate URLs when using
	| the Artisan command line tool. You should set this to the root of
	| your application so that it is used when running Artisan tasks.
	|
	*/

	'url' => 'https://adminuat.lsmriskreduce.com',

	'client_url' => 'https://clientuat.lsmriskreduce.com',

	/*
	|--------------------------------------------------------------------------
	| Api details
	|--------------------------------------------------------------------------
	|
	| For the Risk Reduce API
	|
	*/

	'api' => [
		'endpoint'      => 'https://apiuat.lsmriskreduce.com/', //trailing slash required
		'username'      => 'admin',
		'password'      => 'Password@123'
	],

	/*
	|--------------------------------------------------------------------------
	| Api details Rackspace
	|--------------------------------------------------------------------------
	|
	| For Rack space API
	|
	*/

	'rackspace' => [
		'username'  =>  'brandformula',
		'apikey'    =>  '71bcff66015e47b9bb8f3512b672e250',
		'region'    =>  'IAD',
		'container' =>  'document_store_live',
		'image_container'   =>  'image_store_live',
		'survey_container'	=>	'survey_container_live'
	],

	/*
	|--------------------------------------------------------------------------
	| Safety Media Creds
	|--------------------------------------------------------------------------
	|
	| For Rack space API
	|
	*/

	'sm' => [
		'username'  =>  'InsightAssured',
		'password'    =>  'wKtbDt7PtkdAPkxZ',
	],



    /*
	|--------------------------------------------------------------------------
	| Sentry Credentials
	|--------------------------------------------------------------------------
	|
	|
	|
	*/

    'sentry' => array(
        'dsn'   => 'https://10bc7c5805454295bf4605f700f8e0c2:<EMAIL>/44268'
    ),



	/*
	|--------------------------------------------------------------------------
	| AWS Credentials
	|--------------------------------------------------------------------------
	|
	|
	|
	*/

	// 'aws' => array(
	// 	'username'		=>	'riskreduce',
	// 	'access_key'	=>	'********************',
	// 	'secret_key'	=>	'KxeYeSAB9mmJhEmmsEzc+QC5KqZkdO14LKLXxNAx',
	// 	'region'		=>	'eu-west-1',
	// 	'bucket'		=>	'riskreducedev'
	// ),

	'aws' => [
	    'access_key' => '********************',
	    'secret_key' => '9rEPcFFONeuXyPKdaqwxWK3ht4zAHKnAvXE/S/M9',
	    'region' => 'eu-west-1',
	    'bucket' => 'risk-reduce-staging'
	],

	/*
	|--------------------------------------------------------------------------
	| Application Timezone
	|--------------------------------------------------------------------------
	|
	| Here you may specify the default timezone for your application, which
	| will be used by the PHP date and date-time functions. We have gone
	| ahead and set this to a sensible default for you out of the box.
	|
	*/

	'timezone' => 'Europe/London',

	/*
	|--------------------------------------------------------------------------
	| Application Locale Configuration
	|--------------------------------------------------------------------------
	|
	| The application locale determines the default locale that will be used
	| by the translation service provider. You are free to set this value
	| to any of the locales which will be supported by the application.
	|
	*/

	'locale' => 'en',

	/*
	|--------------------------------------------------------------------------
	| Application Fallback Locale
	|--------------------------------------------------------------------------
	|
	| The fallback locale determines the locale to use when the current one
	| is not available. You may change the value to correspond to any of
	| the language folders that are provided through your application.
	|
	*/

	'fallback_locale' => 'en',

	/*
	|--------------------------------------------------------------------------
	| Encryption Key
	|--------------------------------------------------------------------------
	|
	| This key is used by the Illuminate encrypter service and should be set
	| to a random, 32 character string, otherwise these encrypted strings
	| will not be safe. Please do this before deploying an application!
	|
	*/

	'key' => '2ET2a0PBiqTueXIxqNdiEcagK1VxyUkl',

	'cipher' => MCRYPT_RIJNDAEL_128,

	/*
	|--------------------------------------------------------------------------
	| Autoloaded Service Providers
	|--------------------------------------------------------------------------
	|
	| The service providers listed here will be automatically loaded on the
	| request to your application. Feel free to add your own services to
	| this array to grant expanded functionality to your applications.
	|
	*/

	'providers' => array(

		'Illuminate\Foundation\Providers\ArtisanServiceProvider',
		'Illuminate\Auth\AuthServiceProvider',
		'Illuminate\Cache\CacheServiceProvider',
		'Illuminate\Session\CommandsServiceProvider',
		'Illuminate\Foundation\Providers\ConsoleSupportServiceProvider',
		'Illuminate\Routing\ControllerServiceProvider',
		'Illuminate\Cookie\CookieServiceProvider',
		'Illuminate\Database\DatabaseServiceProvider',
		'Illuminate\Encryption\EncryptionServiceProvider',
		'Illuminate\Filesystem\FilesystemServiceProvider',
		'Illuminate\Hashing\HashServiceProvider',
		'Illuminate\Html\HtmlServiceProvider',
		'Illuminate\Log\LogServiceProvider',
		'Illuminate\Mail\MailServiceProvider',
		'Illuminate\Database\MigrationServiceProvider',
		'Illuminate\Pagination\PaginationServiceProvider',
		'Illuminate\Queue\QueueServiceProvider',
		'Illuminate\Redis\RedisServiceProvider',
		'Illuminate\Remote\RemoteServiceProvider',
		'Illuminate\Auth\Reminders\ReminderServiceProvider',
		'Illuminate\Database\SeedServiceProvider',
		'Illuminate\Session\SessionServiceProvider',
		'Illuminate\Translation\TranslationServiceProvider',
		'Illuminate\Validation\ValidationServiceProvider',
		'Illuminate\View\ViewServiceProvider',
		'Illuminate\Workbench\WorkbenchServiceProvider',
		'Artisaninweb\SoapWrapper\ServiceProvider',
		'Barryvdh\DomPDF\ServiceProvider',
        'Barryvdh\Snappy\ServiceProvider',
        'Maatwebsite\Excel\ExcelServiceProvider',
        'VTalbot\Markdown\MarkdownServiceProvider',
        'Nutsweb\LaravelPrerender\LaravelPrerenderServiceProvider',
        'Aws\Laravel\AwsServiceProvider',
        'PragmaRX\Google2FA\Vendor\Laravel\ServiceProvider',
	),

	/*
	|--------------------------------------------------------------------------
	| Service Provider Manifest
	|--------------------------------------------------------------------------
	|
	| The service provider manifest is used by Laravel to lazy load service
	| providers which are not needed for each request, as well to keep a
	| list of all of the services. Here, you may set its storage spot.
	|
	*/

	'manifest' => storage_path().'/meta',

	/*
	|--------------------------------------------------------------------------
	| Class Aliases
	|--------------------------------------------------------------------------
	|
	| This array of class aliases will be registered when this application
	| is started. However, feel free to register as many as you wish as
	| the aliases are "lazy" loaded so they don't hinder performance.
	|
	*/

	'aliases' => array(

		'App'               => 'Illuminate\Support\Facades\App',
		'Artisan'           => 'Illuminate\Support\Facades\Artisan',
		'Auth'              => 'Illuminate\Support\Facades\Auth',
		'Blade'             => 'Illuminate\Support\Facades\Blade',
		'Cache'             => 'Illuminate\Support\Facades\Cache',
		'ClassLoader'       => 'Illuminate\Support\ClassLoader',
		'Config'            => 'Illuminate\Support\Facades\Config',
		'Controller'        => 'Illuminate\Routing\Controller',
		'Cookie'            => 'Illuminate\Support\Facades\Cookie',
		'Crypt'             => 'Illuminate\Support\Facades\Crypt',
		'DB'                => 'Illuminate\Support\Facades\DB',
		'Eloquent'          => 'Illuminate\Database\Eloquent\Model',
		'Event'             => 'Illuminate\Support\Facades\Event',
		'File'              => 'Illuminate\Support\Facades\File',
		'Form'              => 'Illuminate\Support\Facades\Form',
		'Hash'              => 'Illuminate\Support\Facades\Hash',
		'HTML'              => 'Illuminate\Support\Facades\HTML',
		'Input'             => 'Illuminate\Support\Facades\Input',
		'Lang'              => 'Illuminate\Support\Facades\Lang',
		'Log'               => 'Illuminate\Support\Facades\Log',
		'Mail'              => 'Illuminate\Support\Facades\Mail',
		'Paginator'         => 'Illuminate\Support\Facades\Paginator',
		'Password'          => 'Illuminate\Support\Facades\Password',
		'Queue'             => 'Illuminate\Support\Facades\Queue',
		'Redirect'          => 'Illuminate\Support\Facades\Redirect',
		'Redis'             => 'Illuminate\Support\Facades\Redis',
		'Request'           => 'Illuminate\Support\Facades\Request',
		'Response'          => 'Illuminate\Support\Facades\Response',
		'Route'             => 'Illuminate\Support\Facades\Route',
		'Schema'            => 'Illuminate\Support\Facades\Schema',
		'Seeder'            => 'Illuminate\Database\Seeder',
		'Session'           => 'Illuminate\Support\Facades\Session',
		'SoftDeletingTrait' => 'Illuminate\Database\Eloquent\SoftDeletingTrait',
		'SSH'               => 'Illuminate\Support\Facades\SSH',
		'Str'               => 'Illuminate\Support\Str',
		'URL'               => 'Illuminate\Support\Facades\URL',
		'Validator'         => 'Illuminate\Support\Facades\Validator',
		'View'              => 'Illuminate\Support\Facades\View',
		'Uuid' 				=> 'Webpatser\Uuid\Uuid',
		'SoapWrapper' 		=> 'Artisaninweb\SoapWrapper\Facades\SoapWrapper',
        'PDF'               => 'Barryvdh\Snappy\Facades\SnappyPdf',
        'Image'             => 'Barryvdh\Snappy\Facades\SnappyImage',
        'Excel'             => 'Maatwebsite\Excel\Facades\Excel',
        'Markdown' 			=> 'VTalbot\Markdown\Facades\Markdown',
        'PDFG' 				=> 'Barryvdh\DomPDF\Facade',
        'AWS'             	=> 'Aws\Laravel\AwsFacade',
        'Google2FA' 		=> 'PragmaRX\Google2FA\Vendor\Laravel\Facade',
	),

	/*
	|--------------------------------------------------------------------------
	| Route names for external-surveyors
	|--------------------------------------------------------------------------
	|
	| This array of route names will be registered when this application
	| is started.
	|
	*/

	'external-surveyor' => array('routes' => ['surveys.create', 'surveys.edit']),

	/*
	|--------------------------------------------------------------------------
	| Route names for liberty-user
	|--------------------------------------------------------------------------
	|
	| This array of route names will be registered when this application
	| is started.
	|
	*/
	'liberty-user' => array('roles' =>
		array(
			'underwriter' => [
				'brokers.index',
				'brokers.create',
				'liberty-users.index',
				'liberty-branches.index',
				'external-surveyors.index',
				'external-survey-companies.index'
			],
			'admin' => [],
			'risk-engineer' => ['kanban.index'],
			'risk-control' => [],
			'branch-admin' => [],
			'aspen-user' => [
				'organisation.user.edit',
				'organisation.user.create',
				'organisation.user.update',
				'organisation.user.destroy',
				'organisation.link.create',
				'organisation.document.create',
				'organisation.document.destroy',
				'organisation.document.update',
				'organisation.document.store',
				'organisation.{id}.report.create',
				'organisation.destroy',
				'organisation.create',
				'organisation.update',
				'surveys.create',
				'surveys.update',
				'surveys.edit',
				'ri_form.delete_files',
				'ri_form.get_files',
				'ri_form.sdsdsd',
				'ri_form.index',
				'surveys.get-surveys',
				'search.search',
				'ri_form.index',
				'ri_form.create',
				'ri_form.edit',
				'ri_form.show',
				'ri_form.store',
				'ri_form.delete',
				'ri_form.update',
				'ri_form.save',
				'ri_form.submission.save',
				'ri_form.submission.delete',
				'ri_form.submission.show',
				'ri_form.submission.close_issue',
				'accident-reporting.index',
				'accident-reporting.filter',
				'accident-reporting.printing',
				'accident-reporting.print',
				'organisation.document.get_level',
				'users.branchuser',
				'surveys.decline-survey',
				'surveys.actual-dates',
				'surveys.update-surveyor',
				'surveys.re-action-update',
				'surveys.update-agenda',
				'surveys.delete',
				'aspen.endorsement-wordings.create',
				'aspen.endorsement-wordings.edit',
				'aspen.endorsement-wordings.delete',
				'aspen.documents-access'
			]
			)),
	/*
    |--------------------------------------------------------------------------
    | Google reCaptcha Credentials
    |--------------------------------------------------------------------------
    |
    |
    |
    */

    'google' => array(
        'site_key' => '6Le-LvoUAAAAAGYp85oWW3TWZEoEL4wTeyuTq-Gd',
        'secret_key' => '6Le-LvoUAAAAADOhHZNMHSLojnd9XJhv1d0AjVoM',
    ),

);
