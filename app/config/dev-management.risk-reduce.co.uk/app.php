<?php

return array(

	'environment' => 'local',
  
  'show_lsm' => true,

	/*
	|--------------------------------------------------------------------------
	| Application Debug Mode
	|--------------------------------------------------------------------------
	|
	| When your application is in debug mode, detailed error messages with
	| stack traces will be shown on every error that occurs within your
	| application. If disabled, a simple generic error page is shown.
	|
	*/

	'debug' => true,


  /*
  |--------------------------------------------------------------------------
  | Api details
  |--------------------------------------------------------------------------
  |
  | For the Risk Reduce API
  |
  */

  'api' => [
    'endpoint'      => 'http://dev-business.risk-reduce.co.uk/', //trailing slash required
    'username'      => 'admin',
    'password'      => 'Password@123'
  ],


  /*
  |--------------------------------------------------------------------------
  | Api details Rackspace
  |--------------------------------------------------------------------------
  |
  | For Rack space API
  |
  */
      
      'rackspace' => [
          'username'  =>  'brandformula',
          'apikey'    =>  '71bcff66015e47b9bb8f3512b672e250',
          'region'    =>  'IAD',
          'container' =>  'document_store_test',
          'image_container'   =>  'image_store_test',
          'document_key'  =>  '24D76465B0B88CAF4B15996F93DE4D5DD031ABFB6F4ACC1BDF5B5C0A8BE215FD'
      ],

      'aws' => [
        'access_key' => '********************',
        'secret_key' => '4JXS8b4W8Sqx+ertEBgxMWXb5iAZgd1UA3LaG5Za',
        'region' => 'eu-west-1',
        'bucket' => 'risk-reduce-test'
      ],
  

  /*
  |--------------------------------------------------------------------------
  | Application URL
  |--------------------------------------------------------------------------
  |
  | This URL is used by the console to properly generate URLs when using
  | the Artisan command line tool. You should set this to the root of
  | your application so that it is used when running Artisan tasks.
  |
  */

  'url' => 'http://dev-management.risk-reduce.co.uk',

  'client_url' => 'http://client-web-portal.dev',

);
