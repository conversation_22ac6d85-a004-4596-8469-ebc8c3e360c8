<?php

return array(

  'environment' => 'local',

	/*
	|--------------------------------------------------------------------------
	| Application Debug Mode
	|--------------------------------------------------------------------------
	|
	| When your application is in debug mode, detailed error messages with
	| stack traces will be shown on every error that occurs within your
	| application. If disabled, a simple generic error page is shown.
	|
	*/

	'debug' => true,
	
  /*
  |--------------------------------------------------------------------------
  | Api details
  |--------------------------------------------------------------------------
  |
  | For the Risk Reduce API
  |
  */

  // 'api' => [
  //   'endpoint'      => 'http://shashi.api.riskreduce.com/', //trailing slash required
  //   'username'      => 'admin',
  //   'password'      => 'Password@123'
  // ],
  
  'api' => [
    'endpoint'      => 'http://dev.rrapi.com/', //trailing slash required
    'username'      => 'admin',
    'password'      => 'Password@123'
  ],


  /*
  |--------------------------------------------------------------------------
  | Api details Rackspace
  |--------------------------------------------------------------------------
  |
  | For Rack space API
  |
  */

  // 'aws' => [
  //     'access_key' => '********************',
  //     'secret_key' => '4JXS8b4W8Sqx+ertEBgxMWXb5iAZgd1UA3LaG5Za',
  //     'region' => 'eu-west-1',
  //     'bucket' => 'risk-reduce-test'
  // ],

  'aws' => [
      'access_key' => '********************',
      'secret_key' => '4JXS8b4W8Sqx+ertEBgxMWXb5iAZgd1UA3LaG5Za',
      'region' => 'eu-west-1',
      'bucket' => 'risk-reduce-staging'
  ],
  // 'aws' => [
  //     'access_key' => '********************',
  //     'secret_key' => '4JXS8b4W8Sqx+ertEBgxMWXb5iAZgd1UA3LaG5Za',
  //     'region' => 'eu-west-1',
  //     'bucket' => 'risk-reduce-production'
  // ],
  /*
  |--------------------------------------------------------------------------
  | Application URL
  |--------------------------------------------------------------------------
  |
  | This URL is used by the console to properly generate URLs when using
  | the Artisan command line tool. You should set this to the root of
  | your application so that it is used when running Artisan tasks.
  |
  */

  'url' => 'http://dev.rradmin.com',

  'client_url' => 'http://dev.rrclient.com',

  'inspire_url' => 'http://ss.rrappetite.com',


  'previsico' => [
      'prelaunch_mode' => true,
      'flood_tiles'    => [
          'url'       => 'https://dev-api.previsico.live/floodmap/%s/%s/%s/%s/%s/%s/%s/%s',
          'key'       => 'x-api-key',
          'password'  => 'FyRUqt7kNN4bSGha2YwOneaSE8oHjaU1jIEe9BH5'
      ]
  ],

  'responsible_business' => array(
    'sector_id' => 7,
    'file_name' => 'rb.txt'
  )

);
