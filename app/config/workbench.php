<?php

return array(

	/*
	|--------------------------------------------------------------------------
	| Workbench Author Name
	|--------------------------------------------------------------------------
	|
	| When you create new packages via the Artisan "workbench" command your
	| name is needed to generate the composer.json file for your package.
	| You may specify it now so it is used for all of your workbenches.
	|
	*/

	'name' => '',

	/*
	|--------------------------------------------------------------------------
	| Workbench Author E-Mail Address
	|--------------------------------------------------------------------------
	|
	| Like the option above, your e-mail address is used when generating new
	| workbench packages. The e-mail is placed in your composer.json file
	| automatically after the package is created by the workbench tool.
	|
	*/

	'email' => '',

);
