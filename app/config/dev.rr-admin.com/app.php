<?php

return array(

  'environment' => 'local',

	/*
	|--------------------------------------------------------------------------
	| Application Debug Mode
	|--------------------------------------------------------------------------
	|
	| When your application is in debug mode, detailed error messages with
	| stack traces will be shown on every error that occurs within your
	| application. If disabled, a simple generic error page is shown.
	|
	*/

	'debug' => true,
	
  /*
  |--------------------------------------------------------------------------
  | Api details
  |--------------------------------------------------------------------------
  |
  | For the Risk Reduce API
  |
  */

  // 'api' => [
  //   'endpoint'      => 'http://shashi.api.riskreduce.com/', //trailing slash required
  //   'username'      => 'admin',
  //   'password'      => 'Password@123'
  // ],
  
  'api' => [
    'endpoint'      => 'https://api.libertyriskreduce.com/', //trailing slash required
    'username'      => 'admin',
    'password'      => 'Password@123'
  ],


  /*
  |--------------------------------------------------------------------------
  | Api details Rackspace
  |--------------------------------------------------------------------------
  |
  | For Rack space API
  |
  */

  // 'aws' => [
  //     'access_key' => '********************',
  //     'secret_key' => '4JXS8b4W8Sqx+ertEBgxMWXb5iAZgd1UA3LaG5Za',
  //     'region' => 'eu-west-1',
  //     'bucket' => 'risk-reduce-test'
  // ],

  'aws' => [
      'access_key' => '********************',
      'secret_key' => '4JXS8b4W8Sqx+ertEBgxMWXb5iAZgd1UA3LaG5Za',
      'region' => 'eu-west-1',
      'bucket' => 'risk-reduce-staging'
  ],
  // 'aws' => [
  //     'access_key' => '********************',
  //     'secret_key' => '4JXS8b4W8Sqx+ertEBgxMWXb5iAZgd1UA3LaG5Za',
  //     'region' => 'eu-west-1',
  //     'bucket' => 'risk-reduce-staging'
  // ],
  'aws' => [
      'access_key' => '********************',
      'secret_key' => '9rEPcFFONeuXyPKdaqwxWK3ht4zAHKnAvXE/S/M9',
      'region' => 'eu-west-1',
      'bucket' => 'risk-reduce-production'
  ],
  /*
  |--------------------------------------------------------------------------
  | Application URL
  |--------------------------------------------------------------------------
  |
  | This URL is used by the console to properly generate URLs when using
  | the Artisan command line tool. You should set this to the root of
  | your application so that it is used when running Artisan tasks.
  |
  */

  'url' => 'http://dev.rr-admin.com',

  'client_url' => 'http://dev.rr-client.com',

  'inspire_url' => 'http://ss.rrappetite.com'

);
