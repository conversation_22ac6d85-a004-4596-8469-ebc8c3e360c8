var AWS = require('aws-sdk');

var s3 = new AWS.S3({apiVersion: '2006-03-01'});

var eltr = new AWS.ElasticTranscoder({

	apiVersion: '2012-09-25',

	region: 'eu-west-1'

});

var pipelineId = '1485354144039-kji04n';

var webPreset = '1351620000001-100240';

var outputPath = 'transcoded/vid';

var webPresetAudio = '1351620000001-300040';

var outputPathAudio = 'transcoded/audio/';

var filename = 'transcoded';

exports.handler = function(event, context) {

	var bucket = event.Records[0].s3.bucket.name;

	var key = event.Records[0].s3.object.key;

	s3.getObject({

		Bucket:bucket,

		Key:key

	},

	function (err, data) {

		console.log('err :::' + err );

		console.log('data :::' + data );

		if (err) {

			console.log('error getting object ' + key + ' from bucket ' + bucket + '. Make sure they exist and your bucket is in the same region as this function.');

			context.done('ERROR', 'error getting file' + err);

		}

		else {

			console.log('Reached B' );

			if (typeof data.Metadata.s3path === "undefined") {
			    console.log('Not a video' );
			} else {

				console.log('Reached C' );



				console.log('Found new video: ' + key + ', sending to ET');

				outputPath = data.Metadata.s3path;

				filename = data.Metadata.filename;

				if (typeof data.Metadata.isaudio === "undefined") {
					
					console.log('Found new video: ' + key + ', sending to ET');
					
					sendVideoToET(key);
					
				} else {
					
					console.log('Found new audio: ' + key + ', sending to ET');
					
					sendAudioToET(key);
					
				}

			}

			/* Below section can be used if you want to put any check based on metadata

			if (data.Metadata.Content-Type == ‘video/x-msvideo’) {

				console.log(‘Reached C’ );

				console.log(‘Found new video: ‘ + key + ‘, sending to ET’);

				sendVideoToET(key);

			} else {

				console.log(‘Reached D’ );

				console.log(‘Upload ‘ + key + ‘was not video’);

				console.log(JSON.stringify(data.Metadata));

			}

			*/

		}

	}

);

};

function sendVideoToET(key){

	console.log('Sending ' + key + ' to ET');

	var params = {

		PipelineId: pipelineId,

		OutputKeyPrefix: outputPath,

		Input: {

			Key: key,

			FrameRate: 'auto',

			Resolution: 'auto',

			AspectRatio: 'auto',

			Interlaced: 'auto',

			Container: 'auto'

		},

		Output: {

			Key: filename,

			ThumbnailPattern: filename+'-{count}',

			PresetId: webPreset,

			Rotate: 'auto'

		}

	};

	eltr.createJob(params, function (err, data) {

		if (err) {

			console.log('Failed to send new video ' + key + ' to ET');

			console.log(err);

			console.log(err.stack)

		} else {

			console.log('Error');

			console.log(data);

		}

		//context.done(null,”);

	});

}

function sendAudioToET(key){

	console.log('Sending ' + key + ' to ET');

	var params1 = {

		PipelineId: pipelineId1,

		OutputKeyPrefix: outputPathAudio,

		Input: {

			Key: key,
			
			FrameRate: 'auto',

			Resolution: 'auto',

			AspectRatio: 'auto',

			Interlaced: 'auto',

			Container: 'auto'

		},

		Output: {

			Key: filename+'-mp3',

			PresetId: webPresetAudio

		}

	};


	eltr.createJob(params1, function (err, data) {

		if (err) {

			console.log('Failed to send new video ' + key + ' to ET');

			console.log(err);

			console.log(err.stack)

		} else {

			console.log('Error');

			console.log(data);

		}

		//context.done(null,”);

	});
}