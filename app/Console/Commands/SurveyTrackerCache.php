<?php
namespace App\Console\Commands;

use Illuminate\Console\Command;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Input\InputArgument;
use Carbon\Carbon;
use App\Models\Api;
use Illuminate\Support\Facades\Cache;

class SurveyTrackerCache extends Command {

	/**
	 * The console command name.
	 *
	 * @var string
	 */
	protected $name = 'riskreduce:surveytrackercache';

	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = 'Caches Survey Tracker data';

	/**
	 * Create a new command instance.
	 *
	 * @return void
	 */
	public function __construct()
	{
		parent::__construct();
	}

	/**
	 * Execute the console command.
	 *
	 * @return mixed
	 */
	public function handle()
	{

        $surveyTrackerData = json_decode(Api::get('api/v1/surveys/all?tracker=yes'));

        if($surveyTrackerData->response == 'success') {
            Cache::forever('SurveyTrackerData', $surveyTrackerData);
        }

	}

	/**
	 * Get the console command arguments.
	 *
	 * @return array
	 */
	protected function getArguments()
	{
		return array(
		);
	}

	/**
	 * Get the console command options.
	 *
	 * @return array
	 */
	protected function getOptions()
	{
		return array(
		);
	}

}


