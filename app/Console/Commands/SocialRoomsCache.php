<?php
namespace App\Console\Commands;

use Illuminate\Console\Command;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Input\InputArgument;
use Carbon\Carbon;
use App\Models\Api;

class SocialRoomsCache extends Command {

	/**
	 * The console command name.
	 *
	 * @var string
	 */
	protected $name = 'riskreduce:socialroomscache';

	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = 'Caches Social Rooms data';

	/**
	 * Create a new command instance.
	 *
	 * @return void
	 */
	public function __construct()
	{
		parent::__construct();
	}

	/**
	 * Execute the console command.
	 *
	 * @return mixed
	 */
	public function handle()
	{
		$person_id = "5e33fda6d293dc102e1b7dd2";
		
        $this->info('Start building SLT cache');

        $sltResponse = json_decode(Api::get('api/v1/virtual-rooms/slt-rooms?person_id='.$person_id));

        if($sltResponse->status == 'success') {
            Cache::forever('sltResponse', $sltResponse);
        }
        $this->info('SLT cache built');

        $apiData = [
            'person_id' => $person_id,
            'page' => 1,
            'limit' => 8,
            'type'  => "refreshed"
        ];

        $this->info('Start building Announcements cache');
        $announcementsResponse = json_decode(Api::post('api/v1/virtual-rooms/announcements', $apiData));
        $promotedAnnouncementResponse = json_decode(Api::post('api/v1/virtual-rooms/announcements/promoted', ['person_id' => '5e33fda6d293dc102e1b7dd2', 'type'  => 'refreshed']));

        $apiData = [
            'page' => 1,
            'limit' => 99999,
            'type'  => "refreshed",
            'promoted' => 'yes'
        ];
        $allAnnouncementsResponse = json_decode(Api::post('api/v1/virtual-rooms/announcements/all', $apiData));
        if($allAnnouncementsResponse->status == 'success') {
            Cache::forever('allAnnouncementsResponse', $allAnnouncementsResponse);
        }
        $this->info('Announcements cache built');

        $this->info('Start building communities cache');
        $communities = json_decode(Api::get('api/v1/virtual-rooms/theme-rooms/communities?person_id='.$person_id));
        Cache::forever('communities-rooms', $communities);
        $this->info('Finish building communities cache');

	}

	/**
	 * Get the console command arguments.
	 *
	 * @return array
	 */
	protected function getArguments()
	{
		return array(
		);
	}

	/**
	 * Get the console command options.
	 *
	 * @return array
	 */
	protected function getOptions()
	{
		return array(
		);
	}

}


