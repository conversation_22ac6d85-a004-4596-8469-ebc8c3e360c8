<?php

namespace App\Console\Commands;

use App\Services\ProcessQueueMessages;
use Illuminate\Console\Command;

class TempInvalidateCacheCommand extends Command
{
    //use HelperTrait;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cache:invalidate_cache_temp';

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Invalidate and Re-Cache Command';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $queueName = config('app.aws.invalidate_cache_sqs_temp');
        (new ProcessQueueMessages($queueName))->process();
    }
}
