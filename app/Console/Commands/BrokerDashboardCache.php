<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Api;
use App\Http\Controllers\OrganisationController;
use Illuminate\Support\Facades\Cache;

class BrokerDashboardCache extends Command
{

    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'riskreduce:brokerdashboardcache';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Caches broker dashboard data';

    protected $possibleStatistics = [
        'closed'          => 0,
        'open'            => 0,
        'greater_than_30' => 0,
        'less_than_30'    => 0
    ];

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $statistics_data['rrTyre'] = [];
        $statistics_data['rr_status'] = [];
        $statistics_data['rr_srr_recommendation'] = [];
        $scheme_tracker = [];
        $activeTab = '';

        $brokers = json_decode(Api::get('api/v1/brokers/all/1/200'));

        foreach ($brokers->data as $broker) {
            $broker_id = $broker->id;

            $this->info('Caching for broker ID: ' . $broker_id);

            $organisations = json_decode(Api::get('api/v1/broker-users/orgs/' . $broker_id));
            $organisation_details = [];
            $organisation_rrtracker = [];
            $hasMgaSchemes = false;

            $openMarket = json_decode(Api::get('api/v1/broker-users/open-market/' . $broker_id));

            $openMarketOrgs = $openMarket->orgs ? $openMarket->orgs : [];
            if ($organisations->response != "error" && !empty($organisations->schemes)) {

                $hasMgaSchemes = true;

                foreach ($organisations->schemes as $orgid) {
                    $scheme_tracker = [];
                    $tyreLabels = [];
                    $orgdetails = app(OrganisationController::class)->getOrganisationDetailsForDashboard($orgid, false, true);

                    $organisation_details[] = $orgdetails;

                    $orgname = json_decode(Api::get('api/v1/organisation/' . $orgid))->data->name;

                    $orgnameWidth = strlen($orgname) / 2;
                    $orgname = wordwrap($orgname, $orgnameWidth, "\n");

                    $organisation_rrtracker[] = [
                        $orgname,
                        $orgdetails['rrTracker']
                    ];


                    if (!$activeTab) {
                        $activeTab = strtolower(str_replace(' ', '-', $orgdetails['scheme']));
                    }

                    foreach ($orgdetails["rr_tyre_label"] as $key => $value) {
                        $tyreLabels["" . $key . ""] = $value;
                    }

                    if (isset($scheme_tracker[$orgdetails['scheme']]['rrTyreLabel'])) {
                        $scheme_tracker[$orgdetails['scheme']]['rrTyreLabel'] = array_merge($scheme_tracker[$orgdetails['scheme']]['rrTyreLabel'], $tyreLabels);
                    } else {
                        $scheme_tracker[$orgdetails['scheme']]['rrTyreLabel'] = $tyreLabels;
                    }

                    $scheme_tracker[$orgdetails['scheme']]['orgData'][$orgdetails['organisationName']]['rrTracker'] = $orgdetails['rrTracker'];
                    $scheme_tracker[$orgdetails['scheme']]['selector'] = strtolower(str_replace(' ', '-', $orgdetails['scheme']));
                    $scheme_tracker[$orgdetails['scheme']]['orgData'][$orgdetails['organisationName']]['details'] = $orgdetails;
                    $scheme_tracker[$orgdetails['scheme']]['rrOverview'] = $this->possibleStatistics;

                    $scheme_tracker[$orgdetails['scheme']]['orgRrTracker'][$orgdetails['organisationName']] =  [
                        $orgname,
                        $orgdetails['rrTracker']
                    ];
                }
            }


            if (!$activeTab) {
                $activeTab = 'Open Market';
            }

            $rrTyreTotal = 0;
            if (!empty($scheme_tracker)) {
                foreach ($scheme_tracker as $scheme => $mga) {
                    foreach ($mga['orgData'] as $orgData) {
                        $scheme_tracker = $this->rrOverview($scheme_tracker, $scheme, $orgData);
                        $scheme_tracker = $this->rrTyre($scheme_tracker, $scheme, $orgData, $rrTyreTotal);
                        $scheme_tracker = $this->rrRecommendationByStatus($scheme_tracker, $scheme, $orgData);
                        $scheme_tracker = $this->rrRecommendationByTitle($scheme_tracker, $scheme, $orgData);
                    }
    
                    $orgout = [];
                    if (count($mga['orgRrTracker']) > 0) {
                        foreach ($mga['orgRrTracker'] as $org) {
                            if (!isset($org[1]['closed'])) {
                                $org[1]['closed'] = 0;
                            }
                            if (!isset($org[1]['open'])) {
                                $org[1]['open'] = 0;
                            }
                            if (!isset($org[1]['greater_than_30'])) {
                                $org[1]['greater_than_30'] = 0;
                            }
                            if (!isset($org[1]['less_than_30'])) {
                                $org[1]['less_than_30'] = 0;
                            }
    
                            $orgout[$org[0]] = [
                                'closed' => $org[1]['closed'],
                                'open' => $org[1]['open'] + $org[1]['greater_than_30'] + $org[1]['less_than_30'],
                            ];
                        }
                    }
                    $scheme_tracker[$scheme]['organisation_rrtracker'] = $orgout;
                }
    
                foreach ($scheme_tracker as $scheme => $mga) {
                    if (isset($mga['rrTyre'])) {
                        $scheme_tracker = $this->processRrTyre($scheme_tracker, $scheme, $mga['rrTyre'], $rrTyreTotal);
                    }
                    if (isset($mga['rr_srr_recommendation'])) {
                        arsort($mga['rr_srr_recommendation']);
                        $scheme_tracker[$scheme]['rr_srr_recommendation'] = array_slice($mga['rr_srr_recommendation'], 0, 5, true);
                    }
                }
            }

            if ($organisation_rrtracker) {
                $orgout = [];
                foreach ($organisation_rrtracker as $org) {
                    if (!isset($org[1]['closed'])) {
                        $org[1]['closed'] = 0;
                    }
                    if (!isset($org[1]['open'])) {
                        $org[1]['open'] = 0;
                    }
                    if (!isset($org[1]['greater_than_30'])) {
                        $org[1]['greater_than_30'] = 0;
                    }
                    if (!isset($org[1]['less_than_30'])) {
                        $org[1]['less_than_30'] = 0;
                    }
    
                    $orgout[$org[0]] = [
                        'closed' => $org[1]['closed'],
                        'open' => $org[1]['open'] + $org[1]['greater_than_30'] + $org[1]['less_than_30'],
                    ];
                }
                $statistics_data['organisation_rrtracker'] = $orgout;
    
                $rr_Tracker = array_map(
                    function ($v) {
                        return $v['rrTracker'];
                    },
                    $organisation_details
                );
    
    
                $rrTracker = $this->sumArrayItem($rr_Tracker);
    
                $survey_statistics = count($rrTracker) > 0 ? array_merge($this->possibleStatistics, array_count_values($rrTracker)) : $this->possibleStatistics;
    
                $statistics_data['rrTracker'] = $survey_statistics;
    
                foreach ($organisation_rrtracker as $org) {
                    if (!isset($org[1]['closed'])) {
                        $org[1]['closed'] = 0;
                    }
                    if (!isset($org[1]['open'])) {
                        $org[1]['open'] = 0;
                    }
                    if (!isset($org[1]['greater_than_30'])) {
                        $org[1]['greater_than_30'] = 0;
                    }
                    if (!isset($org[1]['less_than_30'])) {
                        $org[1]['less_than_30'] = 0;
                    }
    
                    $statistics_data['rrTracker']['closed'] += $org[1]['closed'];
                    $statistics_data['rrTracker']['open'] += $org[1]['open'];
                    $statistics_data['rrTracker']['greater_than_30'] += $org[1]['greater_than_30'];
                    $statistics_data['rrTracker']['less_than_30'] += $org[1]['less_than_30'];
                }
            }

            $rr_Tyre = array_map(
                function ($v) {
                    return $v['rrTyre'];
                },
                $organisation_details
            );

            $rrTyre = $this->sumArrayItem($rr_Tyre);

            $total = array_sum($rrTyre);

            $rr_tyre = array_map(
                function ($v) use ($total) {
                    return floor(($v / $total) * 100);
                },
                $rrTyre
            );

            $statistics_data['rrTyre'] = $rr_tyre;

            $rr_Status = array_map(
                function ($v) {
                    return $v['rr_status'];
                },
                $organisation_details
            );

            $rrstatus = [];
            foreach ($rr_Status as $status) {
                foreach ($status as $key => $value) {
                    $rrstatus[$key] = $value;
                }
            }

            $statistics_data['rr_status'] = $rrstatus;

            $rr_srr_recommendation = array_map(
                function ($v) {
                    return $v['rr_srr_recommendation'];
                },
                $organisation_details
            );


            $rrRecommendation = $this->sumArrayItem($rr_srr_recommendation);
            $price = [];
            foreach ($rrRecommendation as $key => $row) {
                $price[$key] = $row;
            }
            array_multisort($price, SORT_DESC, $rrRecommendation);
            $rrRecommendation = array_slice($rrRecommendation, 0, 5, true);

            $statistics_data['rr_srr_recommendation'] = $rrRecommendation;

            $rr_tyre_label = array_map(
                function ($v) {
                    return $v['rr_tyre_label'];
                },
                $organisation_details
            );
            $tyreLabels = [];
            foreach ($rr_tyre_label as $label) {
                foreach ($label as $key => $value) {
                    $tyreLabels["" . $key . ""] = $value;
                }
            }

            $statistics_data['rr_tyre_label'] = $tyreLabels;

            Cache::put("dashboard_statistics_broker_id_" . $broker_id, array_merge($statistics_data, ['schemes' => $scheme_tracker, 'activeTab' => $activeTab, 'openMarket' => $openMarketOrgs, 'hasMgaSchemes' => $hasMgaSchemes]), 2880);

            $this->info('Dashboard cached for broker ID: ' . $broker_id);
        }
    }

    public function sumArrayItem($array)
    {
        $final = [];
        array_walk_recursive($array, function ($item, $key) use (&$final) {
            $final[$key] = isset($final[$key]) ?  $item + $final[$key] : $item;
        });
        return $final;
    }

    /**
     * Get the console command arguments.
     *
     * @return array
     */
    protected function getArguments()
    {
        return [];
    }

    /**
     * Get the console command options.
     *
     * @return array
     */
    protected function getOptions()
    {
        return [];
    }

    public function rrOverview($scheme_tracker, $scheme, $orgTracker)
    {
        if (isset($orgTracker['rrTracker']['closed'])) {
            $scheme_tracker[$scheme]['rrOverview']['closed'] += $orgTracker['rrTracker']['closed'];
        }
        if (isset($orgTracker['rrTracker']['open'])) {
            $scheme_tracker[$scheme]['rrOverview']['open'] += $orgTracker['rrTracker']['open'];
        }
        if (isset($orgTracker['rrTracker']['greater_than_30'])) {
            $scheme_tracker[$scheme]['rrOverview']['greater_than_30'] += $orgTracker['rrTracker']['greater_than_30'];
        }
        if (isset($orgTracker['rrTracker']['less_than_30'])) {
            $scheme_tracker[$scheme]['rrOverview']['less_than_30'] += $orgTracker['rrTracker']['less_than_30'];
        }
        return $scheme_tracker;
    }

    public function rrTyre($scheme_tracker, $scheme, $orgRRTyre, &$rrTyreTotal)
    {
        foreach ($orgRRTyre['details']['rrTyre'] as $tyre => $val) {
            $currentVal = isset($scheme_tracker[$scheme]['rrTyre'][$tyre]) ? $scheme_tracker[$scheme]['rrTyre'][$tyre] : 0;
            $scheme_tracker[$scheme]['rrTyre'][$tyre] = $currentVal + $val;
            $rrTyreTotal += $val;
        }
        return $scheme_tracker;
    }

    public function rrRecommendationByStatus($scheme_tracker, $scheme, $orgRRStatus)
    {
        foreach ($orgRRStatus['details']['rr_status'] as $status => $val) {
            $closed = isset($scheme_tracker[$scheme]['rrStatus'][$status]['closed']) ? $scheme_tracker[$scheme]['rrStatus'][$status]['closed'] : 0;
            $scheme_tracker[$scheme]['rrStatus'][$status]['closed'] = $closed + $val['closed'];

            $open = isset($scheme_tracker[$scheme]['rrStatus'][$status]['open']) ? $scheme_tracker[$scheme]['rrStatus'][$status]['open'] : 0;
            $scheme_tracker[$scheme]['rrStatus'][$status]['open'] = $open + $val['open'];
        }
        return $scheme_tracker;
    }

    public function rrRecommendationByTitle($scheme_tracker, $scheme, $orgTitle)
    {
        foreach ($orgTitle['details']['rr_srr_recommendation'] as $title => $val) {
            $currentVal = isset($scheme_tracker[$scheme]['rr_srr_recommendation'][$title]) ? $scheme_tracker[$scheme]['rr_srr_recommendation'][$title] : 0;
            $scheme_tracker[$scheme]['rr_srr_recommendation'][$title] = $currentVal + $val;
        }
        return $scheme_tracker;
    }

    public function processRrTyre($scheme_tracker, $scheme, $rrTyre, &$rrTyreTotal)
    {
        $running_percentage = $rr_count = 0;

        foreach ($rrTyre as $tyre => $value) {
            if ($rr_count === count($rrTyre) - 1) {
                $scheme_tracker[$scheme]['rrTyre'][$tyre] = 100 - $running_percentage;
            } else {
                $percent = round(($value / $rrTyreTotal) * 100);
                $scheme_tracker[$scheme]['rrTyre'][$tyre] = $percent;
                $running_percentage += $percent;
            }
            ++$rr_count;
        }
        return $scheme_tracker;
    }
}
