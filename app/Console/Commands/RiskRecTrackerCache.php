<?php
namespace App\Console\Commands;

use Illuminate\Console\Command;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Input\InputArgument;
use Carbon\Carbon;
use App\Models\Api;
use Illuminate\Support\Facades\Cache;

class RiskRecTrackerCache extends Command {

	/**
	 * The console command name.
	 *
	 * @var string
	 */
	protected $name = 'riskreduce:riskrectrackercache';

	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = 'Caches Risk Rec Tracker data';

	/**
	 * Create a new command instance.
	 *
	 * @return void
	 */
	public function __construct()
	{
		parent::__construct();
	}

	/**
	 * Execute the console command.
	 *
	 * @return mixed
	 */
	public function handle()
	{

        // $refresh = json_decode(
        //     Api::get('/api/v1/survey-submission/rr-all-tracker?refresh_cache=yes&admin_call=yes')
        // );
        $cards = json_decode(
            Api::get('/api/v1/survey-submission/rr-all-tracker')
        );
        if($cards->response == 'success') {
        	Cache::forever('rr-all', $cards);
        }

	}

	/**
	 * Get the console command arguments.
	 *
	 * @return array
	 */
	protected function getArguments()
	{
		return array(
		);
	}

	/**
	 * Get the console command options.
	 *
	 * @return array
	 */
	protected function getOptions()
	{
		return array(
		);
	}

}


