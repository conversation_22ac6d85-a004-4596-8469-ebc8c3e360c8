<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Api;

class RebuildOrganisationCache extends Command
{
    //use HelperTrait;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'riskreduce:build_organisation_cache';

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Rebuild Cache For Organisation';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->rebuildCache();
    }

    public function rebuildCache()
    {
        $this->info('Started At: '.date('Y-m-d H:i:s'));
        $organisations = json_decode(Api::get('api/v1/organisation/get-organisation-with-surveys-and-risk-engineer-type'));

        if ($organisations && data_get($organisations, 'response') !== 'error') {
            foreach ($organisations as $organisation) {
                try {
                    $organisationId = $organisation->organisation_id;
                    app()->call('App\Http\Controllers\OrganisationController@orgCacheRebuild', ['id' => $organisationId]);
                    \Log::info("Success Cache Rebuild for Org: " . $organisationId);
                } catch (\Exception $e) {
                    \Log::info("Error: riskreduce:build_organisation_cache: " . $e->getMessage());
                }
            }
        }
        $this->info('Ended At: '.date('Y-m-d H:i:s'));
    }
}
