<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Api;
use App\Http\Controllers\OrganisationController;
use Aws\Laravel\AwsFacade as AWS;

class RebuildOrganisationCacheSqs extends Command
{
    //use HelperTrait;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'riskreduce:build_organisation_cache_sqs';
    private $flag;
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Rebuild Cache For Organisation';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->recieveMessages();
    }

    private function recieveMessages()
    {
        $url = (string) config('app.aws.force_cache_rebuild');
        try{
            if (isset($url) && !empty($url)) {
                $messages = AWS::createClient('Sqs')->receiveMessage([
                    'QueueUrl'              => $url,
                    'MaxNumberOfMessages'   => 10,
                    'MessageAttributeNames' => [],
                ]);
            } else {
                \Log::info('Invalid configuration');
            }
        }catch(\Exception $e){
            $this->info($e->getMessage());
        }

        if(!$messages) return;

        foreach($messages as $message){
            foreach($message as $msg){
                try{
                    $body = isset($msg['Body']) ? $msg['Body'] : '';
                    if(!$body) continue;

                    $receiptHandle = isset($msg['ReceiptHandle']) ? $msg['ReceiptHandle'] : '';
                    $this->rebuildSpecificOrgCache($body);
                    $this->deleteAllQueue($receiptHandle);
                }catch(\Exception $e){
                    $this->info($e->getMessage());
                }
            }
        }
        // $this->deleteAllQueue($messages);
    }

    public function rebuildSpecificOrgCache($organisationId): void
    {
        try {
            app(OrganisationController::class)->orgCacheRebuild($organisationId,true);
            $this->info("Force Recache success - organisation: " . $organisationId);
            \Log::info("Force Recache success - organisation: " . $organisationId);
        }catch (\Exception $e){
            \Log::info("Error: riskreduce:build_organisation_cache: " . $organisationId . $e->getMessage());
        }

        return;
    }

    private function deleteAllQueue($message)
    {
        if($message){
            $url = (string) config('app.aws.force_cache_rebuild');
            AWS::createClient('Sqs')->deleteMessage([
                'QueueUrl'      => $url,
                'ReceiptHandle' => $message
            ]);
            \Log::info("$message has been deleted");
        }else{
            \Log::info("Nothing to delete...");
            \Log::info("Please check configuration...");
        }
    }
}
