<?php
namespace App\Console\Commands;

use Illuminate\Console\Command;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Input\InputArgument;
use Carbon\Carbon;

class CleanUp extends Command {

	/**
	 * The console command name.
	 *
	 * @var string
	 */
	protected $name = 'riskreduce:cleanup';

	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = 'Cleans up all files that are older than 5 minutes';

	/**
	 * Create a new command instance.
	 *
	 * @return void
	 */
	public function __construct()
	{
		parent::__construct();
	}

	/**
	 * Execute the console command.
	 *
	 * @return mixed
	 */
	public function handle()
	{
		//
        $this->info('Start Cleaning up files');
        $folders = array('file_to_decrypt','file_to_download','file_to_encrypt','file_to_upload');
        $count = 0;
        foreach($folders as $folder)
        {
            $storage = storage_path($folder);
            if(file_exists($storage))
            {
                $files = scandir($storage);
                foreach($files as $file)
                {
                    if($file != '.' && $file != '..')
                    {
                        $time = Carbon::now();
                        $time->addMinutes(-30);
                        try
                        {
                            if($time->timestamp > filemtime($file))
                            {
                                unlink($storage . '/'. $file);
                                $count ++;
                            }
                        }
                        catch(Exception $ex){
                            unlink($storage . '/' . $file);
                            $count ++;
                        }
                    }
                }
            }

        }
        $this->info('Files Deleted: '.$count);


	}

	/**
	 * Get the console command arguments.
	 *
	 * @return array
	 */
	protected function getArguments()
	{
		return array(
		);
	}

	/**
	 * Get the console command options.
	 *
	 * @return array
	 */
	protected function getOptions()
	{
		return array(
		);
	}

}
