<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Http\Request;
use App\Http\Controllers\APIControllerForCommand;
use Exception;


class CleanRRTrackerData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'riskreduce:clean-rr-tracker-data {flag}';

    protected $form;
    protected $request;

    public function __construct()
    {
        parent::__construct();
        $this->request = new Request();
        $this->form = new APIControllerForCommand($this->request);
    }


    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean RR Tracker Data and Close';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        $flag = $this->argument('flag');
        if ($flag == 'p') {
            $surveys=$this->getDataToCloseSurveyFromDB();
            $this->closeSRFForSurveys($surveys);
        } else {
            $this->fetchDataAndStore($flag);
        }
    }

    private function closeSRFForSurveys($surveys)
    {
        $this->info("closing survey from db, ");
        $total = count($surveys);
        $counter = 1;

        foreach($surveys as $survey){
            $start = microtime(true);
            $this->info("Processing survey: {$survey->survey_id}, {$counter} of {$total} surveys");
            try {
                $this->processSurvey($survey);
                \Log::info("Success processing survey: {$survey->survey_id}");
                $success_submissions[] = $survey->submission_id;
            } catch (Exception $e) {
                $this->info("Error: {$e->getMessage()}");
                $failed_submissions[] = $survey->submission_id;
                \Log::info("Error processing survey: {$survey->survey_id}" . PHP_EOL . $e->getMessage());
            }
            $counter++;

            $end = microtime(true);
            $totaltime = number_format(($end - $start), 2);
            $this->info("Time: {$totaltime} sec" . PHP_EOL);
        }
    }

    private function getDataToCloseSurveyFromDB()
    {
        $this->info("processing survey from db");
        $data = $this->form->getDataInLogTable();
        $surveys=[];
        if (count($data) > 0) {
            $grouped=[];
            foreach ($data as $survey) {
                $survey_id = $survey->survey_id;
                if (!isset($grouped[$survey_id])) {
                    $grouped[$survey_id] = [];
                }
                $grouped[$survey_id][] = $survey;
            }

            foreach($grouped as $group){
                $survey=$group[0];
                $attributes=[];
                foreach($group as $srf){
                    $attributes[]=$srf->grading_id;
                }
                $survey->attributes=$attributes;
                $surveys[]=$survey;
            }
        }
        return $surveys;
    }

    private function fetchDataAndStore($flag)
    {
        $this->info("Fetching surveys for flag {$flag}");
        $flag = $this->argument('flag');
        $data = $this->form->getTrackerDataForCleanUP($flag);
        $total = count($data);
        $srfchunks = 20;
        $counter = 1;
        $maindata = [];

        foreach ($data as $survey) {
            $start = microtime(true);

            $this->info("Storing survey: {$survey->survey_id}, {$counter} of {$total} surveys");

            try {
                $srf = $this->getDataForSurvey($survey);
                if ($srf) {
                    $survey = $srf;
                } else {
                    $survey->attributes = [0];
                    $survey->submission_id = 'na';
                }
                $maindata[] = $survey;
                if ($counter % $srfchunks == 0) {
                    $this->form->saveDataInLogTable($maindata);
                    $maindata = [];
                }
            } catch (Exception $e) {
                $this->info("Error: {$e->getMessage()}");
                \Log::info("Error storing survey: {$survey->survey_id}" . PHP_EOL . $e->getMessage());
            }
            $counter++;
            $end = microtime(true);
            $totaltime = number_format(($end - $start), 2);
            $this->info("Time: {$totaltime} sec" . PHP_EOL);
        }
    }

    private function getDataForSurvey($survey)
    {
        $gradings = $this->form->getGradingAttributes($survey);
        if ($gradings && count($gradings->data) > 0) {
            $survey->attributes = $gradings->data;
            $survey->submission_id = $gradings->submission_id;
            return $survey;
        }
        return null;
    }

    private function processSurvey($survey)
    {
        $this->registerMessage($survey);
        $this->form->closeRiskRecommendation($survey);
    }

    private function registerMessage($survey)
    {
        $input = [];
        $input['survey_id']         = $survey->survey_id;
        $input['message']           = 'Closed from cron';
        $input['attachment']        = '<div class="message-list__item-files"></div>';
        $input['role']              = 'admin';
        $input['type']              = 'liberty-user';
        $input['login_type']        = 'risk-control';
        $input['user_id']     = 2; //shashi
        $input['sender_name'] = 'Cron Job';
        $input['from_cron'] = true;
        $survey->input = $input;
        $this->form->registerMessage($survey);
        sleep(1);
    }
}
