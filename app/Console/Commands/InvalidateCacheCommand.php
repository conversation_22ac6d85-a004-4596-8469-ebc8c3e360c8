<?php

namespace App\Console\Commands;

use App\Services\ProcessQueueMessages;
use Illuminate\Console\Command;

class InvalidateCacheCommand extends Command
{
    //use HelperTrait;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cache:invalidate_cache';

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Invalidate and Re-Cache Command';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        (new ProcessQueueMessages())->process();
    }
}
