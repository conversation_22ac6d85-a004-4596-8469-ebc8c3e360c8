<?php

namespace App\Console\Commands;
use Illuminate\Console\Command;
use App\Http\Controllers\CacheRebuildController;
use App\Models\CsrReport;
use App\Models\FileUpload;
use Exception;


class RebuildCacheForOrganisations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
protected $signature = 'riskreduce:build_cache_for_orgs {id?}';

    protected $csrReport;
    protected $builder;
    protected $files;

    public function __construct()
    {
        parent::__construct();
        $this->csrReport = new CsrReport();
        $this->files = new FileUpload();
        $this->builder = new CacheRebuildController($this->files,$this->csrReport);
    }


    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Rebuild Cache For Organisations';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $orgId=$this->argument('id');
        $this->ProcessOrgUsers($orgId);
    }

    private function ProcessOrgUsers($orgId=null){
        $orgIds=$orgId?[$orgId]:array_unique(array_map(function($orgUsers)
        {
            return $orgUsers->organisation_id ;
        },$this->builder->getOrgsWithMaxSurveys(5))); //same api call is used on client side

        $orgIds=array_values($orgIds);

        $total=count($orgIds);
        $this->comment("Start processing {$total} organisations");

        $totaltime=0;
        foreach($orgIds as $index=>$orgid){
            $this->comment("processing organisation: {$orgid}");
            \Log::info("processing organisation: {$orgid}");
            $start = microtime(true);
            $status=false;
            try{
                $status=$this->builder->RebuildCache($orgid);
            }
            catch(Exception $e) {
                \Log::info("Error building Cache for Orgid: {$orgid}, {$e->getMessage()}");
            }
            
            if($status)
            {
                $processed=round((($index+1)/$total)*100);
                $message = "Building cache for organisation: {$orgid}, processing: {$processed} %";
                $this->info($message);
            }

            $end = microtime(true);
            $time = number_format(($end - $start), 2);
            $this->info("Time: {$time} sec");
            $totaltime += $time;
        }

        $totaltime = round($totaltime/60,2);
        $this->comment("Total time for processing {$total} organisations : {$totaltime} minutes");
    }
}
