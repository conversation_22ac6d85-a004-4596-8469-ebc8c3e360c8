<?php

namespace App\Rules;

use App\Models\Api;
use Illuminate\Contracts\Validation\DataAwareRule;
use Illuminate\Contracts\Validation\InvokableRule;
use Illuminate\Support\Facades\Crypt;

class NotPreviouslyUsedPassword implements DataAwareRule, InvokableRule
{
    /**
     * All of the data under validation.
     *
     * @var array
     */
    protected $data = [];

    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct(public array $param)
    {
        //
    }

    /**
     * Run the validation rule.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     * @return void
     */
    public function __invoke($attribute, $value, $fail)
    {
        $data['code'] = $this->data['code'] ?? '';
        $data['table'] = $this->param['table'] ?? 'users';
        $data['password'] = Crypt::encrypt($this->data['password'] ?? '');

        $response = json_decode(Api::post('/api/v1/previous-passwords/check', $data));
        if ($response?->is_previously_used) {
            $fail('The :attribute field must not be in your last four passwords.');
        }
    }

    /**
     * Set the data under validation.
     *
     * @param  array  $data
     * @return $this
     */
    public function setData($data)
    {
        $this->data = $data;

        return $this;
    }
}
