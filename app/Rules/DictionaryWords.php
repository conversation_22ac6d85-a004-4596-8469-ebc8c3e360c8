<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\InvokableRule;

class DictionaryWords implements InvokableRule
{
    const DICTIONARY_FILE = __DIR__.'/../../storage/password_rules/dictionary_words.txt';
    const COMMON_PASSWORD_FILE = __DIR__.'/../../storage/password_rules/common_passwords.txt';

    private $words = [];
    private $common_passwords = [];

    public function __construct()
    {
        $this->words = explode("\n", file_get_contents(self::DICTIONARY_FILE));
        $this->common_passwords = explode("\n", file_get_contents(self::COMMON_PASSWORD_FILE));
    }

    /**
     * Run the validation rule.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     * @return void
     */
    public function __invoke($attribute, $value, $fail)
    {
        $trimmedValue = trim($value);

        if (in_array(strtolower($trimmedValue), $this->words) || in_array($trimmedValue, $this->words)) {
            $fail('The :attribute must not be dictionary words list.');
        }

        if (in_array(strtolower($trimmedValue), $this->common_passwords) || in_array($trimmedValue, $this->common_passwords)) {
            $fail('The :attribute must not be in common passwords list.');
        }
    }
}
