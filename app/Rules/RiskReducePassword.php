<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\InvokableRule;

class RiskReducePassword implements InvokableRule
{
    /**
     * Run the validation rule.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     * @return void
     */
    public function __invoke($attribute, $value, $fail)
    {
        if (preg_match('/(.)\\1{2}/', $value)) {
            $fail('The :attribute field must have a maximum of two repeated characters only.');
        }
    }
}
