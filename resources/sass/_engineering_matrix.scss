body.scrolled {
    &::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        z-index: 9;
        width: 100%;
        height: 150px;
        background: rgb(255,255,255);
        background: -moz-linear-gradient(180deg, rgba(255,255,255,1) 0%, rgba(255,255,255,0) 100%);
        background: -webkit-linear-gradient(180deg, rgba(255,255,255,1) 0%, rgba(255,255,255,0) 100%);
        background: linear-gradient(180deg, rgba(255,255,255,1) 0%, rgba(255,255,255,0) 100%);
        filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#ffffff",endColorstr="#ffffff",GradientType=1);
    }
}

.engineering-matrix-wrapper {

    --light-grey: #F7F8FA;
    --light-blue: #337AB7;
    --primary: #2E6DA4;
    --body-colour: #5E6983;
    --border-colour: #ecedf3;

    --green: #49C993;
    --purple: #7F97DC;
    --cyan: #49B2C9;
    --yellow: #FDCA41;
    --orange: #FF9D43;
    --red: #DC6788;

    background-color: var(--light-grey);
    font-weight: 500;

    [class^="col"] {
        padding-left: 8px;
        padding-right: 8px;
    }

    .topbar {
        background-color: #fff;
    }

    .btn { 
        border: 1px solid rgba(0, 0, 0, 0.20);
        border-radius: 4px;
        text-transform: none;
        font-weight: bold;
        letter-spacing: 0.14px;
        font-size: 14px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-width: 100px;
        text-decoration: none !important;
        .icon {
            // font-size: 16px;
            margin-right: 8px;
            font-weight: bold;
        }
        &.btn-primary {
            background-color: var(--light-blue);
        }
        &.btn-success {
            background-color: var(--green);
        }
        &.btn-sm {
            padding: 5px 10px;
            font-size: 12px;
        }
    }

    .badge {
        background-color: var(--light-grey);
        color: $navy;
        font-size: 14px;
        padding: 0.625rem 1.25rem;
        border-radius: 50px;
        .icon {
            color: var(--primary);
            font-weight: 700;
            margin-right: .5rem;
        }
    }

    .card {
        width: 100%;
        border-radius: 12px;
        background-color: #fff;
        border: 1px solid var(--border-colour) !important;
        color: var(--body-colour);
        box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.04);
        opacity: 1;
        transition: opacity 0.2s ease-in-out;
        
        &-title {
            color: var(--secondary);
            margin-bottom: 0;
            font-size: 16px;
            padding: 12px 10px;
            border-bottom: 1px solid var(--border-colour);
            line-height: 1;
            .icon {
                margin-right: 4px;
            }

            &[data-toggle=collapse] {
                background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none'%3E%3Cpath d='M12 16.5002L4.5 9.0002L5.55 7.9502L12 14.4002L18.45 7.9502L19.5 9.0002L12 16.5002Z' fill='%235E6983'/%3E%3C/svg%3E");
                background-size: 12px 12px;
                background-position: 98% center;
                background-repeat: no-repeat;
                padding-right: 2.5rem;
                &[aria-expanded=true] {
                    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none'%3E%3Cpath d='M12 7.4998L4.5 14.9998L5.55 16.0498L12 9.5998L18.45 16.0498L19.5 14.9998L12 7.4998Z' fill='%235E6983'/%3E%3C/svg%3E");
                }
            }
        }
        &-body {
            padding: 10px;
            min-height: auto;
            border-radius: inherit;
            position: relative;
            a {
                text-decoration: underline;
            }
        }
        &-header {
            &[data-toggle=collapse] {
                background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none'%3E%3Cpath d='M12 16.5002L4.5 9.0002L5.55 7.9502L12 14.4002L18.45 7.9502L19.5 9.0002L12 16.5002Z' fill='%235E6983'/%3E%3C/svg%3E");
                background-size: 24px 24px;
                background-position: 99% center;
                background-repeat: no-repeat;
                padding-right: 2.5rem;
                &[aria-expanded=true] {
                    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none'%3E%3Cpath d='M12 7.4998L4.5 14.9998L5.55 16.0498L12 9.5998L18.45 16.0498L19.5 14.9998L12 7.4998Z' fill='%235E6983'/%3E%3C/svg%3E");
                }
            }
        }
        &-image {
            @include aspect-ratio(16, 8);
            width: 100%;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            border-radius: 12px 12px 0 0;
        }

        &-chart {
            min-height: 200px;
        }

        &.no-shadow {
            box-shadow: none;
        }

        &.disabled {
            opacity: 0.5;
            pointer-events: none;
        }

        &.focused {
            box-shadow: 0px 6px 6px 2px rgba(0, 0, 0, 0.05);
        }
    }

    .border-bottom {
        border-color: var(--border-colour) !important;
    }

    .table-filter-wrapper {
        .badge {
            font-weight: 500;
            padding: 9px 20px;
            border: 1px solid var(--light-grey);

            &.active {
                border-radius: 44px;
                border: 1px solid #D8DCE6;
            }

            &.active-orange {
                border-radius: 44px;
                border: 1px solid #FF9D43;
                background: rgba(255, 157, 67, 0.20);
            }
        }
    }

    .table {
        margin-bottom: 0;

        thead {
            background-color: var(--light-grey);
            padding: 0 4px;
            th {
                border: none;
                padding: 18px 14px;
                font-size: 12px;
                line-height: 14px;
                font-weight: 700;
                color: var(--body-colour);
                text-align: center;
                min-width: 80px;
                &:first-of-type {
                    border-radius: 8px 0 0 8px;
                    min-width: 200px;
                    max-width: 250px;
                    // text-align: left;
                }
                &:last-of-type {
                    border-radius: 0 8px 8px 0;
                }
            }
        }
        tbody {
            tr {
                td {
                    padding: 8px 12px;
                    font-size: 12px;
                    font-weight: 500;
                    text-align: center;
                    border-top: none;
                    border-bottom: 1px solid var(--border-colour);

                    a {
                        color: $blue !important;
                        font-weight: 500 !important;
                        &:hover {
                            color: $navy;
                        }
                    }
                }

                &.collapse.show {
                    display: table-row;
                }
            }

            tr.disabled {
                td {
                    color: rgba(#5E6983, 0.5);
                    a {
                        color: var(--body-colour) !important;
                    }
                }
            }
        }

        &.ai-table {
            tr.selected {
                td {
                    background-color: var(--light-grey);
                    &:first-child {
                        border-radius: 10px 0 0 10px;
                    }
                    &:last-child {
                        border-radius: 0 10px 10px 0;
                    }
                }
            }

            td {
                border: none;
            }


            tr:not(.selected) {
                &:not(:last-child) {
                    td {
                        border-bottom: 1px solid var(--light-grey);
                    }
                }
            }
        }
    }

    #tableResourceAllocation {
        button[data-toggle=collapse] {
            background-color: transparent;
            border: none;
            padding: 8px;

            i.icon {
                color: var(--secondary);
                transform: rotate(0deg);
                transition: transform 0.2s ease-in-out;
            }

            &[aria-expanded=true] {
                i.icon {
                    transform: rotate(180deg);
                }
            }
        }

        tr.collapse.show {
            > td {
                border-bottom: none;
            }
        }
    }

    #tableNewAccountsBound {
        td {
            .badge-success {
                color: #1B8C5D;
                font-size: 12px;
                font-weight: 600;
                background-color: rgba(#49C993, 0.2);
                padding: 4px 10px;
                margin-left: 10px;
            }
        }
    }

    .breadcrumb {
        font-size: 12px;
        &-item {
            &:before {
                font-size: 12px;
                top: 0;
            }
        }
    }

    .selectric {
        min-width: auto;
        .button {
            width: 24px;
            height: 24px;
            border-left: none;
            line-height: 24px;
            &:after {
                font-size: 11px;
            }
        }
        .label {
            margin: 0 24px 0 10px;
            height: 24px;
            line-height: 24px;
            font-size: 11px;
        }
        &-items {
            width: 100% !important;
            li {
                padding: 0px 10px;
                font-size: 11px;
            }
        }
    }

    .custom-checkbox {
        .custom-control-label {
            font-size: 12px;
            font-weight: 500;
            line-height: 16px;
            &:before {
                width: 16px;
                height: 16px;
                left: -26px;
                top: 0;
            }
            &:after {
                width: 16px;
                height: 16px;
                top: 0px;
                left: -23px;
                font-size: 10px;
            }
        }
    }

    /* Style for the range input */
    input[type="range"] {
        -webkit-appearance: none;
        width: 100%;
        height: 8px;
        background: var(--light-grey);
        border-radius: 5px;
        outline: none;
        position: relative;
    }

    input[type="range"]:focus {
        background: var(--light-grey) !important;
        outline: none; 
    }

    /* Prevent background color change on active */
    input[type="range"]:active {
        background: var(--light-grey) !important;
    }

    /* Style for the thumb */
    input[type="range"]::-webkit-slider-thumb {
        -webkit-appearance: none;
        appearance: none;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background: #fff;
        cursor: pointer;
        filter: drop-shadow(0px 0px 10px rgba(0, 0, 0, 0.15));
        border: 1px solid var(--border-colour);
        position: relative;
        top: -2px;
    }
    

    /* Style for the thumb in Firefox */
    input[type="range"]::-moz-range-thumb {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background: #fff;
        cursor: pointer;
        filter: drop-shadow(0px 0px 10px rgba(0, 0, 0, 0.15));
        border: 1px solid var(--border-colour);
        position: relative;
        top: -2px;
    }

    /* Style for the value label */
    .slider_wrapper {
        position: relative;
        &:before,
        &:after {
            position: absolute;
            top: 100%;
        }
        &:before {
            content: '0%';
            left: 0;
        }
        &:after {
            content: '100%';
            right: 0;
            text-align: right;
        }

        .range-value {
            position: absolute;
            top: 15px;
            left: 0;
            transform: translateX(-50%);
            font-weight: bold;
            color: #fff;
            background-color: var(--primary);
            border-radius: 4px;
            padding: 2px 5px;
            white-space: nowrap;
            font-weight: 500;
        }
    }

    .icon-check-rounded {
        display: flex;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        justify-content: center;
        align-items: center;
        background: rgba(#49C993, 0.2);

        &::before {
            content: '';
            display: block;
            width: 15px;
            height: 14px;
            background-image: url("data:image/svg+xml,%3Csvg width='15' height='14' viewBox='0 0 15 14' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12.0625 3.625L5.875 9.8125L3.0625 7' stroke='%2349C993' stroke-width='1.125' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A");
            background-size: contain;
            background-repeat: no-repeat;
        }
    }

    .icon-x-rounded {
        display: flex;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        justify-content: center;
        align-items: center;
        background: rgba(#DC6788, 0.2);

        &::before {
            content: '';
            display: block;
            width: 14px;
            height: 14px;
            background-image: url("data:image/svg+xml,%3Csvg width='14' height='14' viewBox='0 0 14 14' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10.1875 3.625L3.4375 10.375' stroke='%23DC6788' stroke-width='1.125' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M3.4375 3.625L10.1875 10.375' stroke='%23DC6788' stroke-width='1.125' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A");
            background-size: contain;
            background-repeat: no-repeat;
        }
    }

    .risk-league-donut_chart-title {
        color: #5E6983;
        text-align: center;
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
    }

    .date-range-picker-wrapper {
        position: relative;
        width: 220px;

        &::after {
            content: '';
            display: block;
            width: 18px;
            height: 18px;
            background-image: url("data:image/svg+xml,%3Csvg width='18' height='18' viewBox='0 0 18 18' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M14.25 3H3.75C2.92157 3 2.25 3.67157 2.25 4.5V15C2.25 15.8284 2.92157 16.5 3.75 16.5H14.25C15.0784 16.5 15.75 15.8284 15.75 15V4.5C15.75 3.67157 15.0784 3 14.25 3Z' stroke='%23337AB7' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M12 1.5V4.5' stroke='%23337AB7' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M6 1.5V4.5' stroke='%23337AB7' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M2.25 7.5H15.75' stroke='%23337AB7' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A");
            background-size: contain;
            background-repeat: no-repeat;
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
        }
    }

    input[type="text"] {
        font-weight: 500;
        height: 40px;
        line-height: 40px;
        background-color: transparent;

        &::placeholder {
            color: rgb(94, 105, 131);
        }
    }

    .selectric {
        min-width: 200px;

        .button {
            width: 38px;
            height: 38px;
            border-left: none;
            line-height: 38px;
            display: flex;
            align-items: center;
            justify-content: center;
            
            &:after {
                font-size: 10px;
                content: '';
                display: block;
                width: 18px;
                height: 18px;
                background-image: url("data:image/svg+xml,%3Csvg width='18' height='18' viewBox='0 0 18 18' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M4.5 6.75L9 11.25L13.5 6.75' stroke='%23337AB7' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A");
                background-size: contain;
                background-repeat: no-repeat;
            }
        }
        .label {
            margin: 0 24px 0 10px;
            height: 38px;
            line-height: 38px;
            font-size: 14px;
        }
        &-items {
            width: 100% !important;
            li {
                padding: 6px 10px;
                font-size: 14px;
            }
        }

        @media (max-width: 1699.98px) {
            min-width: 200px;
        }

        @media (max-width: 1580px) {
            min-width: 180px;
        }

        @media (max-width: 1440px) {
            min-width: 160px;
        }

        @media (max-width: 1320px) {
            min-width: 140px;
        }
    }

    .sticky-filter-wrapper {
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .chart-dtrs {
        width: 100%;
        height: 280px;
    }

    .chart-dtrs-engagement {
        width: 100%;
        height: 200px;
    }

    .chart-new-accounts-bound,
    .chart-recommendations {
        width: 100%;
        height: 200px;
    }

    .touchpoint-survey {
        font-size: 12px;
        font-weight: 600;
        color: #49C993;
        background-color: rgba(#49C993, 0.2);
        border-radius: 12px;
        padding: 4px 8px;
    }

    .touchpoint-client-meeting {
        font-size: 12px;
        font-weight: 600;
        color: #67B7DC;
        background-color: rgba(#67B7DC, 0.2);
        border-radius: 12px;
        padding: 4px 8px;
    }

    .touchpoint-risk-eng-meeting,
    .touchpoint-risk-engineering-meeting {
        font-size: 12px;
        font-weight: 600;
        color: #AE67CB;
        background-color: rgba(#AE67CB, 0.2);
        border-radius: 12px;
        padding: 4px 8px;
    }

    .form-check-label {
        font-size: 14px;
        font-weight: 400;
        color: #5E6983;
    }

    .icon-list-2 {
        background-image: url("data:image/svg+xml,%3Csvg width='16' height='16' viewBox='0 0 16 16' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cg id='list 2'%3E%3Cpath id='Vector' d='M5.33301 4H13.9997' stroke='%235E6983' stroke-width='1.33333' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath id='Vector_2' d='M5.33301 8H13.9997' stroke='%235E6983' stroke-width='1.33333' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath id='Vector_3' d='M5.33301 12H13.9997' stroke='%235E6983' stroke-width='1.33333' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath id='Vector_4' d='M2 4H2.00667' stroke='%235E6983' stroke-width='1.33333' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath id='Vector_5' d='M2 8H2.00667' stroke='%235E6983' stroke-width='1.33333' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath id='Vector_6' d='M2 12H2.00667' stroke='%235E6983' stroke-width='1.33333' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/g%3E%3C/svg%3E%0A");
        background-repeat: no-repeat;
        background-size: 16px 16px;
        background-position: center;
        width: 24px;
        height: 24px;

        cursor: pointer;
        padding: 6px;
        border-radius: 4px;
        display: inline-block;

        &:hover {
            background-color: rgba(94, 105, 131, 0.15);
        }
    }

    .toggle-column-list {
        list-style: none;
        border-radius: 6px;
        border: 1px solid rgba(#D8DCE6, 0.5);
        background: #FFF;
        box-shadow: 0px 10px 20px 0px rgba(0, 0, 0, 0.10);
        width: 220px;
        padding: 16px;

        position: absolute;
        top: 75%;
        right: 20px;
        z-index: 1;
        display: none; /* Hide by default */

        li {
            text-align: left;
            margin-bottom: 8px;

            a {
                color: #5E6983 !important;
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
                text-decoration: none !important;

                &.column-hidden {
                    .icon-check {
                        opacity: 0;
                    }
                }
            }
            
            &:last-child {
                margin-bottom: 0;
            }
        }
    }
    
    .toggle-column-list.show {
        display: block; /* Show when 'show' class is added */
    }

    .renewal-date {
        padding-right: 20px;
        position: relative;
        display: inline-block;
        text-wrap-mode: nowrap;

        &.renewal-date-expired {
            color: #DC6788;
            font-weight: 700;

            &:after {
                content: '';
                display: block;
                width: 15px;
                height: 14px;
                background-image: url("data:image/svg+xml,%3Csvg width='15' height='14' viewBox='0 0 15 14' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cg id='alert-circle 1' clip-path='url(%23clip0_5674_2264)'%3E%3Cpath id='Vector' d='M7.16732 12.8334C10.389 12.8334 13.0007 10.2217 13.0007 7.00002C13.0007 3.77836 10.389 1.16669 7.16732 1.16669C3.94566 1.16669 1.33398 3.77836 1.33398 7.00002C1.33398 10.2217 3.94566 12.8334 7.16732 12.8334Z' stroke='%23DC6788' stroke-width='1.16667' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath id='Vector_2' d='M7.16699 4.66669V7.00002' stroke='%23DC6788' stroke-width='1.16667' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath id='Vector_3' d='M7.16699 9.33331H7.17366' stroke='%23DC6788' stroke-width='1.16667' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/g%3E%3Cdefs%3E%3CclipPath id='clip0_5674_2264'%3E%3Crect width='14' height='14' fill='white' transform='translate(0.166992)'/%3E%3C/clipPath%3E%3C/defs%3E%3C/svg%3E%0A");
                background-size: contain;
                background-repeat: no-repeat;
                background-position: center;

                position: absolute;
                top: 3px;
                right: 0;
            }
        }
    }

    #sector_donut_chart,
    #subsector_donut_chart {
        width: 100%;
        height: 450px;
    }
    
    .pie_chart {
        width: 100%;
        height: 200px;
    }
    
    .chart_colspan_6 {
        width: 100%;
        height: 300px;
    }

    .input-daterange {
        border: 1px solid #E0E3EB;
        border-radius: 4px;
        width: 230px;
        position: relative;

        &::before {
            content: '—';
            position: absolute;
            top: 9px;
            left: 40%;
        }

        &::after {
            content: '';
            display: block;
            width: 18px;
            height: 18px;
            background-image: url("data:image/svg+xml,%3Csvg width='18' height='18' viewBox='0 0 18 18' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M14.25 3H3.75C2.92157 3 2.25 3.67157 2.25 4.5V15C2.25 15.8284 2.92157 16.5 3.75 16.5H14.25C15.0784 16.5 15.75 15.8284 15.75 15V4.5C15.75 3.67157 15.0784 3 14.25 3Z' stroke='%23337AB7' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M12 1.5V4.5' stroke='%23337AB7' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M6 1.5V4.5' stroke='%23337AB7' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M2.25 7.5H15.75' stroke='%23337AB7' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A");
            background-size: contain;
            background-repeat: no-repeat;
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
        }

        > input {
            text-align: left;
            border: none;
            padding-top: 10px;
            padding-bottom: 10px;
            height: 38px;
        }
    }

    .shimmer {
        // width: 200px;
        // height: 20px;
        background: linear-gradient(90deg, #fff 25%, #f6f7f8 50%, #fff 75%);
        background-size: 200% 100%;
        animation: shimmer 3s infinite linear;
        border-radius: 5px;
      }

    @keyframes shimmer {
        0% {
          background-position: -200% 0;
        }
        100% {
          background-position: 200% 0;
        }
      }
}

.container-nav-metric {
    padding-top: 10px;
    padding-bottom: 10px;

    .burger-menu-wrapper {
        border: 1px solid #D8DCE6;
        box-shadow: 0px 5px 5px 0px #00000005;
        padding: 15px;
        display: flex;
        gap: 5px;
        border-radius: 6px;
        margin-right: 24px;

        &:hover {
            text-decoration: none;
        }

        .menu-label {
            color: #0B2562;
            font-family: Roboto;
            font-weight: 700;
            font-size: 12px;
            line-height: 14px;
        }

        @media (max-width: 480px) {
            padding: 15px;
            margin-right: 15px;

            .menu-label {
                display: none;
            }
        }
    }

    .form-search {
        width: 340px;
        margin-right: 24px;

        @media (max-width: 640px) {
            width: auto;
        }
    }
}

.container-metric-header {
    border-top: 1px solid #D8DCE680;
    border-bottom: 1px solid #D8DCE680;
    padding-top: 10px;
    padding-bottom: 10px;
    
    h1.title {
        font-size: 28px;

        @media (max-width: 767px) {
            font-size: 26px;   
        }

        @media (max-width: 575px) {
            font-size: 24px;   
        }
    }
}
.icon-person-x {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="%23DC6788" class="bi bi-person-x" viewBox="0 0 16 16"><path d="M11 5a3 3 0 1 1-6 0 3 3 0 0 1 6 0M8 7a2 2 0 1 0 0-4 2 2 0 0 0 0 4m.256 7a4.5 4.5 0 0 1-.229-1.004H3c.001-.246.154-.986.832-1.664C4.484 10.68 5.711 10 8 10q.39 0 .74.025c.226-.341.496-.65.804-.918Q8.844 9.002 8 9c-5 0-6 3-6 4s1 1 1 1z"/><path d="M12.5 16a3.5 3.5 0 1 0 0-7 3.5 3.5 0 0 0 0 7m-.646-4.854.646.647.646-.647a.5.5 0 0 1 .708.708l-.647.646.647.646a.5.5 0 0 1-.708.708l-.646-.647-.646.647a.5.5 0 0 1-.708-.708l.647-.646-.647-.646a.5.5 0 0 1 .708-.708"/></svg>');
    background-repeat: no-repeat;
    background-position: center;
    width: 28px;
    height: 35px;
    display: inline-block;
}
.nowrap {
    white-space: nowrap;
}
.toggle-container {
    background-color: #f8f9fa;
    padding: 2rem;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    max-width: 600px;
    margin: 0 auto;
}

/* iOS Toggle Switch Styles */
.ios-toggle {
    position: relative;
    display: inline-block;
    width: 51px;
    height: 31px;
}

.ios-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #e9ecef;
    border: 2px solid #e9ecef;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    border-radius: 31px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 27px;
    width: 27px;
    left: 0px;
    bottom: 0px;
    background-color: white;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2), 0 1px 2px rgba(0, 0, 0, 0.1);
}

.ios-toggle input:checked + .toggle-slider {
    background-color: #34c759;
    border-color: #34c759;
}

.ios-toggle input:checked + .toggle-slider:before {
    transform: translateX(20px);
}

/* Hover and focus states */
.ios-toggle input:focus + .toggle-slider {
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1), 0 0 0 3px rgba(52, 199, 89, 0.2);
}

.toggle-slider:hover {
    opacity: 0.8;
}

/* Active state animation */
.ios-toggle input:active + .toggle-slider:before {
    transform: scale(1.1);
}

.ios-toggle input:checked:active + .toggle-slider:before {
    transform: translateX(20px) scale(1.1);
}

/* Demo styles */
.demo-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.toggle-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #e9ecef;
}

.toggle-item:last-child {
    border-bottom: none;
}

.toggle-label {
    font-size: 16px;
    color: #333;
    margin: 0;
}

.toggle-description {
    font-size: 14px;
    color: #666;
    margin: 0;
}

.status-text {
    font-weight: 600;
    margin-left: 1rem;
}

.status-on {
    color: #34c759;
}

.status-off {
    color: #8e8e93;
}

.equal-width-columns {
    table-layout: fixed;
    width: 100%;
}

.equal-width-columns thead tr th,
.equal-width-columns tbody tr td {
    max-width: 50px !important;              
    width: 50px !important;        
    white-space: normal !important;
    word-wrap: break-word;
    overflow-wrap: break-word;
}