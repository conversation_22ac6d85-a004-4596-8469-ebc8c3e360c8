@php
    use App\Helpers\RiskInsightsHelper;
@endphp
<div class="card">
    <div class="card-title">
        <i class="icon icon-list"></i>
        Locations
    </div>
    <div class="card-body">
        <div class="table-responsive table-benchmark-location-wrapper">
            <table class="table risk-league-table-sortable equal-width-columns">
                <thead>
                    <tr>
                        <th data-sort="location" style="width: 200px !important;">Location</th>
                        <th data-sort="postcode">Postcode</th>
                        <th data-sort="tiv">TIV</th>
                        <th data-sort="risk-score">Risk Score</th>
                        @foreach ($locations->attributes as $attribute)
                            <th data-sort="{{ Str::slug($attribute) }}">{{ Str::title($attribute) }}</th>
                        @endforeach
                        
                    </tr>
                </thead>
                <tbody>
                    @foreach ($locations->data as $location)
                        @php
                            $overAllScore = 0;
                        @endphp
                        <tr data-location="{{ $location->location_name }}" 
                        data-postcode="{{ $location->postcode }}"
                        data-tiv="{{ $location->tiv ?? 0 }}"
                        @if (isset($_GET['loc']) && str_contains(urldecode($_GET['loc']), $location->location_name)) style="background-color: #f0f8ff;" @endif>


                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="risk-league-company-logo mr-2"
                                        style="background-image: url('/img/risk-league/map.png');"></div>
                                    <a href="{{ route('risk-insights.risk-location', ['loc' => urlencode($location->location_name), 'name' => urlencode($location->organisation_name), 'slug' => Str::slug($location->organisation_name), 'organisation_id' => $organisationId]) }}"
                                        class="text-left fs-15">{{ $location->location_name }}</a>
                                </div>
                            </td>
                            @foreach ($location->risk_scores as $attr)
                                @php
                                    $score = intval($attr->score);
                                    $colorClass = RiskInsightsHelper::getColorClass($score);
                                    $overAllScore += $score;
                                @endphp
                               
                            @endforeach
                            
                            <td class="text-left">{{ $location->postcode }}</td>
                            <td class="nowrap text-left">£ {{ number_format($location->tiv, 0) }}</td>
                            <td>
                                <div class="d-flex align-items-center justify-content-center">

                                    @php
                                        $riskScore = round($overAllScore / count($location->risk_scores));
                                        $colorClass = RiskInsightsHelper::getColorClass($riskScore);
                                        $colorHex = RiskInsightsHelper::getColorHexByScore($riskScore);
                                    @endphp
                                    <div class="risk-league-rating lg {{ $colorClass }} text-center">{{ $riskScore }}
                                    </div>
                                </div>
                            </td>
                            
                            @foreach ($location->risk_scores as $attr)
                                @php
                                    $score = intval($attr->score);
                                    $colorClass = RiskInsightsHelper::getColorClass($score);
                                    $overAllScore += $score;
                                @endphp
                                <td>
                                    <div class="risk-league-rating {{ $colorClass }}">{{ $score }}</div>
                                </td>
                            @endforeach
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>
