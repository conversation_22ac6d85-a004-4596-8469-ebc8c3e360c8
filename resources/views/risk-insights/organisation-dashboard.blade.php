

@extends('risk-insights.layouts.app')

@section('content')

<div class="risk-league-wrapper">

    <div class="risk-league-topbar py-3">
        <div class="container">
            <div class="d-flex flex-column flex-lg-row align-items-lg-center">
                <h1 class="fs-28 fw-700 mb-3 mb-lg-0"><i class="icon icon-bar-chart-2 mr-2 fs-22"></i> Risk insights</h1>

                <div class="d-flex ml-lg-auto align-items-center justify-content-lg-end">
                    <a href="#" class="btn btn-primary d-none"><i class="icon icon-arrow-up"></i> Upload files</a>
                    <input type="file" id="fileUpload" style="display: none;">
                    <a href="#" data-toggle="modal" data-target="#uploadRiskReportFileModal" class="btn btn-success ml-2"><i class="icon icon-plus"></i> Upload Risk Reports</a>
                </div>
            </div>
        </div>
    </div>

    <div class="container py-4">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="01_dashboard.php">Risk insights</a></li>
                <li class="breadcrumb-item active"><span><?php echo $company_name; ?></span></li>
            </ol>
        </nav>
    </div>

    <div class="container pb-4 d-none">
        <div class="row">
            <div class="col">
                <div class="card py-2 px-3">
                    <div class="d-flex align-items-center my-1">
                        <div class="text-secondary"><strong>Filter</strong></div>
                        <div class="d-flex align-items-center ml-lg-4 mb-3 mb-lg-0 flex-wrap">
                            <div class="badge mr-2"><i class="icon icon-folder"></i> All</div>
                            <div class="badge mr-2"><i class="icon icon-briefcase"></i> Property</div>
                            <div class="badge mr-2"><i class="icon icon-shopping-bag"></i> Retail</div>
                            <div class="badge mr-2"><i class="icon icon-shopping-cart"></i> Grocery</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="card"> 
            <div class="card-body px-0">
                <div class="d-flex align-items-center justify-content-between px-3 pb-2">
                    <div class="d-flex align-items-center">
                        <div class="risk-league-company-logo lg mr-2" style="background-image: url('/img/risk-league/<?php echo $company_slug; ?>.png');"></div>
                        <div class="lh-16">
                            <div class="fs-15 text-navy"><?php echo htmlspecialchars($company_name); ?></div>
                            <a href="#" class="fs-11">Upload logo</a>
                        </div>
                    </div>
                    <div class="risk-league-portfolio-score">
                        Organisation Score is
                        <div class="risk-league-rating <?php echo $score_color_class; ?> lg ml-2"><?php echo $organisation_score; ?></div>
                    </div>
                </div>
                <div class="border-top d-flex align-items-center justify-content-between px-3 pt-2">
                    <div class="meta-broker">
                        <div class="d-flex justify-content-center align-items-center">
                            <img src="/img/risk-league/marsh.png" width="36" height="36" alt="Marsh Logo" class="rounded">
                            <div class="fs-11 lh-16 ml-2">
                                <p class="mb-0">Broker</p>
                                <p class="fs-13 text-secondary">Marsh</p>
                                <a href="#">Upload logo</a>
                            </div>
                        </div>
                    </div>
                    <div class="meta-insurer">
                        <div class="d-flex justify-content-center align-items-center">
                            <img src="/img/risk-league/aig.png" width="36" height="36" alt="AIG Logo" class="rounded">
                            <div class="fs-11 lh-16 ml-2">
                                <p class="mb-0">Lead Insurer</p>
                                <p class="fs-13 text-secondary">AIG</p>
                                <a href="#">Upload logo</a>
                            </div>
                        </div>
                    </div>
                    <div class="fs-11 lh-16 ml-2">
                        <p class="mb-0">Renewal Date</p>
                        <p class="fs-13 text-secondary">29/08/24</p>
                    </div>
                    <div class="fs-11 lh-16 ml-2">
                        <p class="mb-0">Sector</p>
                        <p class="fs-13 text-secondary">Retail</p>
                    </div>
                    <div class="fs-11 lh-16 ml-2">
                        <p class="mb-0">Subsector</p>
                        <p class="fs-13 text-secondary">Grocery</p>
                    </div>
                    <div class="fs-11 lh-16 ml-2">
                        <p class="mb-0">Position</p>
                        <p class="fs-13 text-secondary">Follow</p>
                    </div>
                    <div class="fs-11 lh-16 ml-2">
                        <p class="mb-0">Line Size</p>
                        <p class="fs-13 text-secondary">7.5%</p>
                    </div>
                    <div class="fs-11 lh-16 ml-2"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="container py-4">
        <div class="row row-cols-1 row-cols-lg-3 my-2 sm-gutters">
            <div class="col d-flex mb-3 mb-lg-0">
                <div class="card">
                    <div class="card-title">
                        <i class="icon icon-calendar"></i>
                        Organisation Performance
                    </div>
                    <div class="card-body">
                    <?php
                        foreach($benchmarking['organisations'] as $org) {
                            if ($org['slug'] == $_GET['slug']) {
                                $risk_score = $org['risk_score'];
                                $portfolio_score = $risk_score['risk_league_rating']['value'];
                            }
                        }

                        // $portfolio_score = $dashboard_company['current_score']['portfolio_score'];
                        $score_color_class = getColorClass($portfolio_score);
                        $score_color_hex = getColorHexByScore($portfolio_score);

                        $market_value_score = $portfolio_score + 10;
                        $market_value_score_color_class = getColorClass($market_value_score);
                        $market_value_score_color_hex = getColorHexByScore($market_value_score);
                        ?>
                        <div class="d-flex justify-content-end">
                            <div class="rating-wrapper">
                                <div class="risk-league-rating risk-league-rating-<?php echo $portfolio_score; ?> <?php echo $score_color_class; ?> ml-2" 
                                     data-color="<?php echo $score_color_hex; ?>">
                                    <?php echo $portfolio_score; ?>
                                </div>

                                <div class="risk-league-rating risk-league-rating-<?php echo $market_value_score; ?> <?php echo $market_value_score_color_class; ?> ml-2" 
                                     data-color="<?php echo $market_value_score_color_hex; ?>">
                                    <?php echo $market_value_score; ?>
                                </div>
                            </div>
                        </div>
                        <?php include('charts/current_score_chart_company.php'); ?>
                    </div>
                </div>
            </div>
            <div class="col d-flex mb-3 mb-lg-0">
                <div class="card">
                    <div class="card-title">
                        <i class="icon icon-list"></i>
                        Risk Grading
                    </div>
                    <div class="card-body" id="risk-grading-section">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th class="text-left">Category</th>
                                    <th>Organisation Grading</th>
                                    <th>Market Grading</th>
                                </tr>
                            </thead>
                            <tbody>
                        <?php
                        if (isset($dashboard_company['risk_grading']) && is_array($dashboard_company['risk_grading'])) {
                            foreach ($dashboard_company['risk_grading'] as $index => $grade) {
                                $borderClass = ($index < count($dashboard_company['risk_grading']) - 1) ? 'border-bottom' : '';
                                $colorClass = getColorClass($grade['score']);
                                $colorClass2 = getColorClass($grade['score2']);
                        ?>
                            <!-- <div class="risk-league-grading <?php echo $grade['status']; ?> mr-2"></div> -->
                             <tr>
                                <td class="text-left"><a href="03_dashboard.php?lob=<?php echo urlencode($grade['category']); ?>&name=<?php echo urlencode($company_name); ?>&slug=<?php echo urlencode($company_slug); ?>"><?php echo htmlspecialchars($grade['category']); ?></a></td>
                                <td><div class="risk-league-rating <?php echo $colorClass; ?> <?php echo isHighlightScore($grade['score'], $grade['score2']) ?> ml-auto"><?php echo $grade['score']; ?></div></td>
                                <td><div class="risk-league-rating <?php echo $colorClass2; ?> <?php echo isHighlightScore($grade['score2'], $grade['score']) ?> ml-auto"><?php echo $grade['score2']; ?></div></td>
                            </tr>
                        <?php
                            }
                        } else {
                            echo "<p>No risk grading data available.</p>";
                        }
                        ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col d-flex">
                <div class="card">
                    <div class="card-title">
                        <i class="icon icon-alert-triangle"></i>
                        Loss Estimates
                    </div>
                    <div class="card-body">
                      @include('risk-insights.components.charts.loss_estimates_chart')
                    </div>
                </div>
            </div>
        </div>
    </div>


<div class="container">
        <div class="card">
            <div class="card-title">
                <i class="icon icon-list"></i>
                Locations
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table risk-league-table-sortable">
                        <thead>
                            <tr>
                                <th data-sort="location">Location</th>
                                <th data-sort="postcode">Postcode</th>
                                <th data-sort="c_e">Construction and exposure</th>
                                <th data-sort="oh">Other Hazards</th>
                                <th data-sort="ssow">Safety Systems of Work</th>
                                <th data-sort="fd_p">Fire Detection & Protection</th>
                                <th data-sort="security">Security</th>
                                <th data-sort="utilities">Utilities</th>
                                <th data-sort="sp">Special Perils</th>
                                <th data-sort="fr">Financial Risks</th>
                                <th data-sort="risk-score">Risk Score</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($organisation_benchmarking['locations'] as $index => $location): ?>
                                <tr 
                                    data-location="<?php echo htmlspecialchars($location['location']); ?>" 
                                    data-postcode="<?php echo htmlspecialchars($location['postcode']); ?>" 
                                    data-c_e="<?php echo intval($location['c_e']); ?>" 
                                    data-oh="<?php echo intval($location['oh']); ?>" 
                                    data-ssow="<?php echo intval($location['SSoW']); ?>" 
                                    data-fd_p="<?php echo intval($location['fd_p']); ?>" 
                                    data-security="<?php echo intval($location['security']); ?>" 
                                    data-utilities="<?php echo intval($location['utilities']); ?>" 
                                    data-sp="<?php echo intval($location['sp']); ?>" 
                                    data-fr="<?php echo intval($location['fr']); ?>" 
                                    data-risk-score="<?php echo $location['risk_score']['risk_league_rating']['value']; ?>">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="risk-league-company-logo mr-2" style="background-image: url('/img/risk-league/map.png');"></div>
                                            <?php
                                            $loc_parts = explode(' ', htmlspecialchars($location['location']));
                                            $loc_parts[1] = $company_name;

                                            $new_loc = '';
                                            foreach($loc_parts as $part) $new_loc .= $part . ' ';
                                            ?>
                                            <a href="04_dashboard.php?loc=<?php echo urlencode($location['location']); ?>&name=<?php echo urlencode($company_name); ?>&slug=<?php echo urlencode($company_slug); ?>" class="text-left fs-15"><?php echo $new_loc; ?></a>
                                        </div>
                                    </td>
                                    <td><?php echo htmlspecialchars($location['postcode']); ?></td>
                                    <?php
                                    $fields = ['c_e', 'oh', 'SSoW', 'fd_p', 'security', 'utilities', 'sp', 'fr'];
                                    foreach ($fields as $field):
                                        $score = intval($location[$field]);
                                        $color_class = getColorClass($score);
                                    ?>
                                        <td><div class="risk-league-rating <?php echo $color_class; ?>"><?php echo $score; ?></div></td>
                                    <?php endforeach; ?>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <?php
                                            $risk_score = $location['risk_score']['risk_league_rating']['value'];
                                            $color_class = getColorClass($risk_score);
                                            $color_hex = getColorHexByScore($risk_score);
                                            $chart_data = json_encode($location['risk_score']['chart_data']);
                                            ?>
                                            <div id="risk_score_chart_<?php echo $index; ?>" class="risk-league-risk_score_chart" 
                                                 data-series-color="<?php echo $color_hex; ?>" 
                                                 data-chart-data='<?php echo $chart_data; ?>'></div>
                                            <div class="risk-league-rating lg <?php echo $color_class; ?> ml-3"><?php echo $risk_score; ?></div>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>


    @include('risk-insights.components.modals.upload-risk-report-file-modal')
</div> 


@endsection



