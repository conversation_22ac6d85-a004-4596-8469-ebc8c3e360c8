<?php include('inc/head.php'); ?>


<div class="risk-league-wrapper">

    <div class="risk-league-topbar py-3">
        <div class="container">
            <div class="d-flex flex-column flex-lg-row align-items-lg-center">
                <h1 class="fs-28 fw-700 mb-3 mb-lg-0"><i class="icon icon-bar-chart-2 mr-2 fs-22"></i> Risk insights</h1>

                <div class="d-flex align-items-center ml-lg-4 mb-3 mb-lg-0 flex-wrap">
                    <div class="badge mr-2"><i class="icon icon-folder"></i> All</div>
                    <div class="badge mr-2"><i class="icon icon-briefcase"></i> Property</div>
                    <div class="badge mr-2"><i class="icon icon-shopping-bag"></i> Retail</div>
                    <div class="badge mr-2"><i class="icon icon-shopping-cart"></i> Grocery</div>
                </div>

                <div class="d-flex ml-lg-auto align-items-center justify-content-lg-end">
                    <a href="#" class="btn btn-primary d-none"><i class="icon icon-arrow-up"></i> Upload files</a>
                    <input type="file" id="fileUpload" style="display: none;">
                    <a href="#" data-toggle="modal" data-target="#uploadRiskReportFileModal" class="btn btn-success ml-2"><i class="icon icon-plus"></i> Upload Risk Reports</a>
                </div>
            </div>
        </div>
    </div>

    <?php
    $company_name = $_GET['name'] ?? 'Tesco';
    $lob = $_GET['lob'] ?? 'Construction & Exposures';
    $risk = $_GET['lob'] ?? 'Unknown Risk';
    // $location_score = $dashboard_location['location_score'] ?? 0;
    $slug = $_GET['slug'] ?? 'tesco';
    $location = $_GET['loc'] ?? 'Chichester Tesco Express';

    // Create new company name
    $loc_parts = explode(' ', htmlspecialchars($location));
    $loc_parts[1] = $company_name;

    $new_loc = '';
    foreach($loc_parts as $part) $new_loc .= $part . ' ';

    // Get location score
    foreach($organisation_benchmarking['locations'] as $loc) {
        if ($loc['location'] == urldecode($location)) {
            $risk_score = $loc['risk_score'];
            $location_score = $risk_score['risk_league_rating']['value'];
        }
    }    
    $score_color_class = getColorClass($location_score);
    ?>
    <div class="container py-4">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="01_dashboard.php">Risk insights</a></li>
                <li class="breadcrumb-item"><a href="02_dashboard.php?name=<?php echo urlencode($company_name); ?>&slug=<?php echo $slug; ?>"><?php echo $company_name; ?></a></li>
                <!-- <li class="breadcrumb-item"><a href="03_dashboard.php?lob=<?php echo urlencode($lob); ?>&name=<?php echo urlencode($company_name); ?>&slug=<?php echo $slug; ?>"><?php echo $lob; ?></a></li> -->
                <li class="breadcrumb-item active"><span><?php echo $location; ?></span></li>
            </ol>
        </nav>
    </div>

    <div class="container pb-4">
        <div class="row">
            <div class="col">
                <div class="card py-2 px-3">
                    <div class="d-flex align-items-center my-1">
                        <div class="text-secondary"><strong>Filter</strong></div>
                        <div class="d-flex align-items-center ml-lg-4 mb-3 mb-lg-0 flex-wrap">
                            <div class="badge mr-2"><i class="icon icon-folder"></i> All</div>
                            <div class="badge mr-2"><i class="icon icon-briefcase"></i> Property</div>
                            <div class="badge mr-2"><i class="icon icon-shopping-bag"></i> Retail</div>
                            <div class="badge mr-2"><i class="icon icon-shopping-cart"></i> Grocery</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container pb-4">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-between mb-3">
                    <div class="d-flex align-items-center">
                        <div class="risk-league-company-logo lg mr-2" style="background-image: url('/img/risk-league/map.png');"></div>
                        <!-- <div class="risk-league-company-logo lg mr-2" style="background-image: url('/img/risk-league/<?php echo htmlspecialchars($slug); ?>.png');"></div> -->
                        <div>
                            <span class="fs-15 text-navy"><?php echo htmlspecialchars($new_loc); ?></span><br/>
                            <a href="/templates-league/05_view-report.php?name=<?php echo urlencode($company_name); ?>&loc=<?php echo urlencode($location); ?>&ref=d4" class="text-decoration-none fs-12 mr-2"><i class="icon icon-file"></i> View Risk Insights Assessment</a>
                            <a href="/templates-league/05_view-report-pdf.php" class="text-decoration-none fs-12"><i class="icon icon-download"></i> View Full Survey Report</a>
                        </div>
                    </div>
                    <div class="risk-league-portfolio-score">
                        Location Score is
                        <div class="risk-league-rating <?php echo $score_color_class; ?> lg ml-2"><?php echo $location_score; ?></div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-8 pr-0">
                        <?php include('charts/location_score_chart.php'); ?>
                    </div>
                    <div class="col-4">

                        <div class="card">
                            <div class="card-title">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="fs-16 text-navy">Risk Type</div>
                                    <div class="d-none"> 
                                        <select class="">
                                            <option>A-Z</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body" id="risk-grading-section">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th class="text-left">Category</th>
                                            <th>Location Grading</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                <?php foreach ($dashboard_location['risk_grading'] as $index => $risk): 
                                    $color_class = getColorClass($risk['score']);
                                    $is_last = $index === count($dashboard_location['risk_grading']) - 1;
                                ?>
                                    <tr>
                                        <td class="text-left"><a href="03_dashboard.php?lob=<?php echo urlencode($risk['category']); ?>&name=<?php echo urlencode($company_name); ?>&slug=<?php echo urlencode($slug); ?>"><?php echo htmlspecialchars($risk['category']); ?></a></td>
                                        <td><div class="risk-league-rating <?php echo $color_class; ?>"><?php echo $risk['score']; ?></div></td>
                                <?php endforeach; ?>
                                    <!-- <div class="<?php echo $is_last ? '' : 'border-bottom'; ?> py-1 px-1 d-flex align-items-center"> -->
                                        <!-- <div class="risk-league-grading <?php echo $risk['status']; ?> mr-2"></div> -->
                                    <!-- </div> -->
                                    </tbody>
                                </table>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="card">
            <div class="card-title">
                <i class="icon icon-list"></i>
                Locations
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-locations risk-league-table-sortable">
                        <thead>
                            <tr>
                                <th data-sort="location">Location</th>
                                <th data-sort="postcode">Postcode</th>
                                <th data-sort="c_e">Construction and exposure</th>
                                <th data-sort="oh">Other Hazards</th>
                                <th data-sort="ssow">Safety Systems of Work</th>
                                <th data-sort="fd_p">Fire Detection & Protection</th>
                                <th data-sort="security">Security</th>
                                <th data-sort="utilities">Utilities</th>
                                <th data-sort="sp">Special Perils</th>
                                <th data-sort="fr">Financial Risks</th>
                                <th data-sort="risk-score">Risk Score</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($organisation_benchmarking['locations'] as $index => $loc): ?>
                                <tr 
                                    class="<?php if ($loc['location'] == $location) echo 'active'; ?>"
                                    data-location="<?php echo htmlspecialchars($loc['location']); ?>" 
                                    data-postcode="<?php echo htmlspecialchars($loc['postcode']); ?>" 
                                    data-c_e="<?php echo intval($loc['c_e']); ?>" 
                                    data-oh="<?php echo intval($loc['oh']); ?>" 
                                    data-ssow="<?php echo intval($loc['SSoW']); ?>" 
                                    data-fd_p="<?php echo intval($loc['fd_p']); ?>" 
                                    data-security="<?php echo intval($loc['security']); ?>" 
                                    data-utilities="<?php echo intval($loc['utilities']); ?>" 
                                    data-sp="<?php echo intval($loc['sp']); ?>" 
                                    data-fr="<?php echo intval($loc['fr']); ?>" 
                                    data-risk-score="<?php echo $loc['risk_score']['risk_league_rating']['value']; ?>">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="risk-league-company-logo mr-2" style="background-image: url('/img/risk-league/map.png');"></div>
                                            <?php
                                            $loc_parts = explode(' ', htmlspecialchars($loc['location']));
                                            $loc_parts[1] = $company_name;

                                            $new_loc = '';
                                            foreach($loc_parts as $part) $new_loc .= $part . ' ';
                                            ?>
                                            <a href="04_dashboard.php?loc=<?php echo urlencode($loc['location']); ?>&name=<?php echo urlencode($company_name); ?>&slug=<?php echo urlencode($slug); ?>" class="text-left fs-15"><?php echo $new_loc; ?></a>
                                        </div>
                                    </td>
                                    <td><?php echo htmlspecialchars($loc['postcode']); ?></td>
                                    <?php
                                    $fields = ['c_e', 'oh', 'SSoW', 'fd_p', 'security', 'utilities', 'sp', 'fr'];
                                    foreach ($fields as $field):
                                        $score = intval($loc[$field]);
                                        $color_class = getColorClass($score);
                                    ?>
                                        <td><div class="risk-league-rating <?php echo $color_class; ?>"><?php echo $score; ?></div></td>
                                    <?php endforeach; ?>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <?php
                                            $risk_score = $loc['risk_score']['risk_league_rating']['value'];
                                            $color_class = getColorClass($risk_score);
                                            $color_hex = getColorHexByScore($risk_score);
                                            $chart_data = json_encode($loc['risk_score']['chart_data']);
                                            ?>
                                            <div id="risk_score_chart_<?php echo $index; ?>" class="risk-league-risk_score_chart" 
                                                 data-series-color="<?php echo $color_hex; ?>" 
                                                 data-chart-data='<?php echo $chart_data; ?>'></div>
                                            <div class="risk-league-rating lg <?php echo $color_class; ?> ml-3"><?php echo $risk_score; ?></div>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>


    @include('risk-insights.components.modals.upload-risk-report-file-modal')
</div> 





<?php include('inc/footer.php'); ?>