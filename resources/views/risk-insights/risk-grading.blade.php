@extends('risk-insights.layouts.app')

@section('content')

    @include('risk-insights.components.topbar')
    @include('risk-insights.breadcrumb')

    <div class="container <?php echo (isset($_GET['slug'])) ? 'py-4' : 'pb-4'; ?>">
        <div class="card">
            <div class="card-title">
                <i class="icon icon-map-pin"></i>
                <?php echo $organisationRisk->attribute->label; ?>
            </div>
            @include('risk-insights.components.cards.risk-grading-donut-card', ['riskDashboard' => $riskDashboard])
        </div>
    </div>

    <div class="container">
        <div class="card">
            <div class="card-title">
                <i class="icon icon-list"></i>
                Locations
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    @include('risk-insights.components.locations-table', ['companyName' => $risk, 'locations' => $organisationRisk->scores_per_location, 'attribute' => $organisationRisk->attribute])
                </div>
            </div>
        </div>

        @include('risk-insights.components.modals.upload-risk-report-file-modal')
    </div>

@endsection