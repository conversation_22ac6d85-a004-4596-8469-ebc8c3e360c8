@extends('risk-insights.layouts.app')

@section('content')
    @include('risk-insights.components.topbar')
    @include('risk-insights.breadcrumb')

    @php
        $company_name       = $dashboardCompany->name;
        $organisation_score = $dashboardCompany->risk_score;
        $score_color_class  = \App\Helpers\RiskInsightsHelper::getColorClass($organisation_score);
    // $company_slug = $_GET['slug'] ?? $dashboardCompany->slug;

    // $loss_estimates = $dashboardCompany->loss_estimates;
    // foreach($loss_estimates as $index => $est) {
    //     $dashboardCompany->loss_estimates[$index]->extraLabel = $company_name . ' Distribution Centre ' . ($index + 1);
    // }
    @endphp
    <div class="container">
        @include('risk-insights.components.cards.company-details-card')
    </div>

    <div class="container py-4">
        @include('risk-insights.charts')
    </div>

    <div class="container">
        @include('risk-insights.benchmark-locations')

        @include('risk-insights.components.modals.upload-risk-report-file-modal')
    </div>
@endsection