<div class="row">
    <div class="col-8 pr-0">
        @include('risk-insights.components.charts.location-score-chart')
    </div>
    <div class="col-4">

        <div class="card">
            <div class="card-title">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="fs-16 text-navy">Risk Quality</div>
                    <div class="d-none">
                        <select class="">
                            <option>A-Z</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="card-body" id="risk-grading-section">
                <table class="table">
                    <thead>
                    <tr>
                        <th class="text-left">Attribute</th>
                        <th>Risk Grading</th>
                    </tr>
                    </thead>
                    <tbody>        
                    @if ($selected_location)

                    <?php foreach ($selected_location->sub_attribute_scores as $index => $subAttributeScore):
                        $color_class = \App\Helpers\RiskInsightsHelper::getColorClassByGrade((int)$subAttributeScore->score);
                        $is_last = $index === count((array)$selected_location->sub_attribute_scores) - 1;
                        ?>
                    <tr>
                        <td class="text-left"><a href="{{ route('risk-insights.risk-grading', ['risk' => $subAttributeScore->label, 'attributeId' => $subAttributeScore->id ?? 1]) }}"><?php echo htmlspecialchars($subAttributeScore->label); ?></a></td>
                        <td><div class="risk-league-rating <?php echo $color_class; ?>"><?php echo (int)$subAttributeScore->score; ?></div></td>
                        <?php endforeach; ?>
    
                    </tr>
                    @endif
                    </tbody>
                </table>
            </div>
        </div>

    </div>
</div>