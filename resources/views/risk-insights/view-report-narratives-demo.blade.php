@extends('risk-insights.layouts.app')

<?php
use App\Helpers\RiskInsightsHelper;

$ref = isset($_GET['ref']) ? $_GET['ref'] : false;
$company_name = $_GET['name'] ?? 'Tesco';
$location = $_GET['loc'] ?? 'Chichester Tesco Express';
$loc_parts = explode(' ', htmlspecialchars($location));
?>
<div class="risk-league-wrapper">

    <div class="risk-league-topbar py-3">
        <div class="container">
            <div class="row row-cols-3 align-items-center justify-content-between">
                <div class="col fs-19">
                    <span class="text-secondary fw-700"> Risk Report </span>
                    <span class="fw-400">2025-06-17</span>
                    <div class="fs-12 mt-2">ID:{{ $locationData->document_id ?? '123456' }}</div>
                </div>

                <div class="col">
                    <?php  $title = ($ref && $ref == 'd4') ? $location : 'Sainsbury, Liverpool, UK'; ?>
                    <h1 class="fs-28 fw-700 text-center">
                        {{ $locationReportData['client_name'] ?? 'Tesco' }}, {{ $locationReportData['location_name'] ?? 'Danes Way' }}
                    </h1>
                    
                </div>

                <div class="col d-flex align-items-center justify-content-end">
                    <!-- <a href="#" class="btn btn-primary"><i class="icon icon-file-text"></i> Preview</a>
                    <a href="#" class="btn btn-success ml-2"><i class="icon icon-send"></i> Send to Client</a> -->
                </div>
            </div>
        </div>
    </div>
    <div class="container py-4">
        <div class="card">
            <div class="card-body">
                <div class="p-3">
                    <div class="align-items-center">
                        <a href="{{ route('risk-insights.download-full-survey-report') }}" class="btn btn-default my-3"><i class="icon icon-download"></i> Download Survey Results</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container py-4">
        <div class="card">
            <div class="card-body">
                <div class="p-3">
                    <div class="d-flex align-items-center justify-content-between mb-4">
                        <div class="fs-28 fw-700 text-secondary"><i class="icon icon-message-circle mr-2 fs-22"></i> Commentary</div>
                        <i class="icon icon-eye fs-20 d-none"></i>
                    </div>

                    <div class="row py-3">
                        <div id="propExecutiveSummary" class="field-wrapper col d-flex flex-column">
                            <div class="fs-16 text-secondary mb-3">
                                Executive Summary
                            </div>

                            <div class="card no-shadow flex-grow-1">
                                <div class="card-body bg-lighter fw-400 content-wrapper" id="content-executive-summary">
                                    <p>{{ $locationReportData['executive_summary'] ?? 'This is a demo executive summary' }}</p>
                                </div>
                            </div>
                        </div>
                        <!-- <div class="col-4 d-flex flex-column">
                            <div class="fs-16 text-secondary mb-3">Additional Notes</div>

                            <div class="card no-shadow flex-grow-1">
                                <div class="card-body bg-lighter fw-400">
                                    <p>Note that sprinkler system requires 25-year service - Broker should be able to advise whether this has been completed since the last survey.</p>
                                </div>
                            </div>
                        </div> -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="card">
            <div class="card-body">
                <div class="p-3">
                    <div class="fs-24 fw-700 text-secondary"><i class="icon icon-bar-chart mr-2"></i> Risk Grading</div>

                    <div class="card no-shadow flex-grow-1 my-3">
                        <div class="card-body bg-lighter fw-400 p-2">
                            <div class="d-flex align-items-center">
                                <div class="risk-league-legend blue">Superior</div>
                                <div class="risk-league-legend green">Above Average</div>
                                <div class="risk-league-legend yellow">Average</div>
                                <div class="risk-league-legend orange">Below Average</div>
                                <div class="risk-league-legend red">Poor</div>
                            </div>
                        </div>
                    </div>

                    <div class="font-20 text-secondary fw-700 mb-3">Property</div>

                    @foreach ($locationReportData['attribute_grading'] as $attribute)
                        @php
                            $parentIndex = $loop->index;
                        @endphp
                        <div class="card mb-3 field-wrapper" id="risk-grading-{{ $parentIndex }}">
                            <div class="card-header" data-toggle="collapse" href="#collapseReport{{ $parentIndex }}">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="risk-league-legend lg {{ RiskInsightsHelper::getColorClassByRiskGrading($attribute['attribute_grading']) }}">
                                        {{ $attribute['attribute_name'] }}
                                    </div>
                                </div>
                            </div>
                            <div class="card-body p-0">
                                <div id="collapseReport{{ $parentIndex }}" class="collapse">
                                    <div class="p-3 content-wrapper" id="content-{{ $parentIndex }}">
                                        @foreach ($attribute['sub_attribute_grading'] as $subAttribute)
                                            @php
                                                $childIndex = $loop->index;
                                            @endphp
                                            <div class="card mb-3 field-wrapper" id="risk-grading-{{ $parentIndex }}-{{ $childIndex }}">
                                                <div class="card-header" data-toggle="collapse" href="#collapseReport{{ $parentIndex }}-{{ $childIndex }}">
                                                    <div class="d-flex align-items-center justify-content-between">
                                                        <div class="risk-league-legend lg {{ RiskInsightsHelper::getColorClassByRiskGrading($subAttribute['risk_grading']) }}">
                                                            {{ $subAttribute['sub_attribute_name'] }}
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="card-body p-0">
                                                    <div id="collapseReport{{ $parentIndex }}-{{ $childIndex }}" class="collapse">
                                                        <div class="p-3 content-wrapper commentary-wrapper" id="content-{{ $parentIndex }}-{{ $childIndex }}">
                                                            {{ $subAttribute['narrative'] }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach

                </div>
            </div>

        </div>
    </div>

</div>


