@extends('risk-insights.layouts.app')

<?php
use App\Helpers\RiskInsightsHelper;

$ref = isset($_GET['ref']) ? $_GET['ref'] : false;
$company_name = $_GET['name'] ?? 'Tesco';
$location = $_GET['loc'] ?? 'Chichester Tesco Express';
$loc_parts = explode(' ', htmlspecialchars($location));
$canEdit = $_GET['documentId'] ?? false;

function calculateAverageColorClass($subAttributes) {
    if (empty($subAttributes)) {
        return 'yellow';
    }
    
    $colorValues = [
        'red' => 0,
        'orange' => 0.25,
        'yellow' => 0.5,
        'green' => 0.75,
        'blue' => 1,
    ];
    
    $totalValue = 0;
    $count = 0;
    
    foreach ($subAttributes as $subAttribute) {
        $colorClass = RiskInsightsHelper::getColorClassByRiskGrading($subAttribute['risk_grading']);
        if (isset($colorValues[$colorClass])) {
            $totalValue += $colorValues[$colorClass];
            $count++;
        }
    }
    
    if ($count === 0) {
        return 'yellow';
    }
    
    $averageValue = $totalValue / $count;
    
    if ($averageValue <= 0.2) return 'red';
    if ($averageValue <= 0.4) return 'orange';
    if ($averageValue <= 0.6) return 'yellow';
    if ($averageValue <= 0.8) return 'green';
    return 'blue';
}
?>
<div class="risk-league-wrapper">

    <div class="risk-league-topbar py-3">
        <div class="container">
            <div class="row row-cols-3 align-items-center justify-content-between">
                <div class="col fs-19">
                    <span class="text-secondary fw-700"> Risk Report </span>
                    <span class="fw-400">2025-06-17</span>
                    <div class="fs-12 mt-2">ID:{{ $locationData->document_id ?? '123456' }}</div>
                </div>

                <div class="col">
                    <?php  $title = ($ref && $ref == 'd4') ? $location : 'Sainsbury, Liverpool, UK'; ?>
                    <h1 class="fs-28 fw-700 text-center">
                        {{ $locationReportData['client_name'] ?? 'Tesco' }}, {{ $locationReportData['location_name'] ?? 'Danes Way' }}
                    </h1>
                    
                </div>

                <div class="col d-flex align-items-center justify-content-end">
                </div>
            </div>
        </div>
    </div>
    <div class="container py-4">
        <div class="card">
            <div class="card-body">
                <div class="p-3">
                    <div class="align-items-center">
                        <a href="{{ route('risk-insights.download-full-survey-report') }}" class="btn btn-default my-3"><i class="icon icon-download"></i> Download Survey Results</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container py-4">
        <div class="card">
            <div class="card-body">
                <div class="p-3">
                    <div class="d-flex align-items-center justify-content-between mb-4">
                        <div class="fs-28 fw-700 text-secondary"><i class="icon icon-message-circle mr-2 fs-22"></i> Commentary</div>
                        <i class="icon icon-eye fs-20 d-none"></i>
                    </div>

                    <div class="row py-3">
                        <div id="propExecutiveSummary" class="field-wrapper col d-flex flex-column">
                            <div class="fs-16 text-secondary mb-3">
                                Executive Summary
                            </div>

                            <div class="card no-shadow flex-grow-1">
                                <div class="card-body bg-lighter fw-400 content-wrapper" id="content-executive-summary">
                                    <p>{!! $locationReportData['executive_summary'] ?? 'This is a demo executive summary' !!}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="card">
            <div class="card-body">
                <div class="p-3">
                    <div class="fs-24 fw-700 text-secondary"><i class="icon icon-bar-chart mr-2"></i> Risk Grading</div>

                    <div class="card no-shadow flex-grow-1 my-3">
                        <div class="card-body bg-lighter fw-400 p-2">
                            <div class="d-flex align-items-center">
                                <div class="risk-league-legend blue">Superior</div>
                                <div class="risk-league-legend green">Above Average</div>
                                <div class="risk-league-legend yellow">Average</div>
                                <div class="risk-league-legend orange">Below Average</div>
                                <div class="risk-league-legend red">Poor</div>
                            </div>
                        </div>
                    </div>

                    <div class="font-20 text-secondary fw-700 mb-3">Property</div>

                    @foreach ($locationReportData['attribute_grading'] as $attribute)
                        @php
                            $parentIndex = $loop->index;
                            $averageColorClass = calculateAverageColorClass($attribute['sub_attribute_grading']);
                        @endphp
                        <div class="card mb-3 field-wrapper" id="risk-grading-{{ $parentIndex }}">
                            <div class="card-header" data-toggle="collapse" href="#collapseReport{{ $parentIndex }}">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="risk-league-legend lg {{ $averageColorClass }}">
                                        {{ $attribute['attribute_name'] }}
                                    </div>
                                </div>
                            </div>
                            <div class="card-body p-0">
                                <div id="collapseReport{{ $parentIndex }}" class="collapse">
                                    <div class="p-3 content-wrapper" id="content-{{ $parentIndex }}">
                                        @foreach ($attribute['sub_attribute_grading'] as $subAttribute)
                                            @php
                                                $childIndex = $loop->index;
                                            @endphp
                                            <div class="card mb-3 field-wrapper" id="risk-grading-{{ $parentIndex }}-{{ $childIndex }}">
                                                <div class="card-header" data-toggle="collapse" href="#collapseReport{{ $parentIndex }}-{{ $childIndex }}">
                                                    <div class="d-flex align-items-center justify-content-between">
                                                        <div class="risk-league-legend lg {{ RiskInsightsHelper::getColorClassByRiskGrading($subAttribute['risk_grading']) }}">
                                                            {{ $subAttribute['sub_attribute_name'] }}
                                                            @if ($canEdit)
                                                            <a href="#" data-toggle="modal" data-target="#editCommentaryModal" data-content-title="Materials of Contruction" data-content="content-materials" class="text-decoration-none ml-1 fs-14 editRiskGradingProperty"><i class="icon icon-edit"></i></a>
                                                            @endif
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="card-body p-0">
                                                    <div id="collapseReport{{ $parentIndex }}-{{ $childIndex }}" class="collapse">
                                                        <div class="p-3 content-wrapper commentary-wrapper" id="content-{{ $parentIndex }}-{{ $childIndex }}">
                                                            {{ $subAttribute['narrative'] }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach

                </div>
            </div>

            @if ($canEdit)
            <div class="card-footer d-flex justify-content-end">
                <button class="btn btn-primary save-risk-grading">Save Changes</button>
            </div>
            @endif

        </div>
    </div>

</div>

@include('risk-insights.components.modals.edit-risk-grading-modal')

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const colorClassGrade = {
            'red': 0,
            'orange': 0.25,
            'yellow': 0.5,
            'green': 0.75,
            'blue': 1,
        }

        function updateSectorRiskScore(sectorClass) {
            const fieldWrapper = $('#' + CSS.escape(`risk-grading-${sectorClass}`));
            const riskLegend = fieldWrapper.find('.risk-league-legend').first();
            riskLegend.removeClass('blue green yellow orange red').addClass(colorClass);
        }


        $('.editRiskGradingProperty').click(function(e) {
            const fieldWrapper = $(this).closest('.field-wrapper');
            const targetFieldId = fieldWrapper.attr('id');
            $('#hTargetField').val(targetFieldId);

            const title = this.getAttribute('data-content-title');
            const content = fieldWrapper.find('.commentary-wrapper').first().text().replace(/\s+/g, ' ').trim();
            console.log(content);

            $('#editCommentaryModal .risk-league-textarea textarea').val(content);

            const colorClass = fieldWrapper.find('.risk-league-legend').attr('class')
                .split(' ')
                .filter(cls => cls !== 'risk-league-legend' && cls !== 'lg')[0];

            $('.risk-grading-status-wrapper').find('select').val(colorClass).change().selectric('refresh');

            const riskTitle = fieldWrapper.find('.risk-league-legend').first().text().trim();
            const modalTitleElement = document.querySelector('#editCommentaryModal .field-title');
            const modalDescriptionElement = document.querySelector('#editCommentaryModal .field-description');

            modalTitleElement.textContent = riskTitle;
            modalDescriptionElement.textContent = 'Edit the Risk Grading for ' + riskTitle;
        });

        $('#saveChangesButton').click(function() {
            const updatedContent = $('#editCommentaryModal .risk-league-textarea textarea').val();
            const updatedColorClass = $('#risk-grading-status-select').val();
            const targetFieldId = $('#hTargetField').val();

            if (!targetFieldId) {
                console.error('Invalid target field ID');
                return;
            }

            const fieldWrapper = $('#' + CSS.escape(targetFieldId));

            fieldWrapper.find('.commentary-wrapper').first().text(updatedContent);

            const riskLegend = fieldWrapper.find('.risk-league-legend').first();
            riskLegend.removeClass('blue green yellow orange red').addClass(updatedColorClass);

            console.log('targetFieldId', targetFieldId);

            const [ , , riskIndex, detailIndex ] = targetFieldId.split('-');

            console.log('riskIndex', riskIndex);
            
            updateParentAttributeColor(riskIndex);
        });

        function updateParentAttributeColor(parentIndex) {
            const parentWrapper = $('#' + CSS.escape(`risk-grading-${parentIndex}`));
            const subAttributeWrappers = parentWrapper.find('.field-wrapper[id^="risk-grading-' + parentIndex + '-"]');
            
            const colorValues = {
                'red': 0,
                'orange': 0.25,
                'yellow': 0.5,
                'green': 0.75,
                'blue': 1,
            };
            
            let totalValue = 0;
            let count = 0;
            
            subAttributeWrappers.each(function() {
                const subLegend = $(this).find('.risk-league-legend').first();
                const colorClass = subLegend.attr('class')
                    .split(' ')
                    .filter(cls => ['blue', 'green', 'yellow', 'orange', 'red'].includes(cls))[0];
                
                if (colorClass && colorValues[colorClass] !== undefined) {
                    totalValue += colorValues[colorClass];
                    count++;
                }
            });
            
            if (count === 0) {
                return;
            }
            
            const averageValue = totalValue / count;
            
            let newColorClass = 'yellow';
            if (averageValue <= 0.2) newColorClass = 'red';
            else if (averageValue <= 0.4) newColorClass = 'orange';
            else if (averageValue <= 0.6) newColorClass = 'yellow';
            else if (averageValue <= 0.8) newColorClass = 'green';
            else newColorClass = 'blue';
            
            const parentLegend = parentWrapper.find('.risk-league-legend').first();
            parentLegend.removeClass('blue green yellow orange red').addClass(newColorClass);
        }

        $('.save-risk-grading').click(function() {
            console.log('save-risk-grading');
            window.close();
        });
    });
</script>


