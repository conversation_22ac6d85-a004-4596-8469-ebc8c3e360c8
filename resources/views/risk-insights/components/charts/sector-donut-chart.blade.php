@php
    $chartData = $portfolioDistribution->data->sector->chart_data;
    foreach ($chartData as &$item) {
        $item->color = str_replace('0x', '#', \App\Helpers\RiskInsightsHelper::getColorHexByScore($item->risk_score));
    }
    $donutChartJson = htmlspecialchars(json_encode($chartData), ENT_QUOTES, 'UTF-8');
    $sectorName = $portfolioDistribution->data->sector->selected_sector;
    $totalRiskScore = $portfolioDistribution->data->sector->total_risk_score;
    $totalRiskColor = \App\Helpers\RiskInsightsHelper::getColorHexByScore($totalRiskScore);
@endphp

<div id="sector_donut_chart" class="risk-league-donut_chart" data-chart-series='{!! $donutChartJson !!} ' data-total-risk-color="{{ $totalRiskColor }}"></div>

<script>
    am5.ready(function() {

        var donut_chart_element = document.getElementById("sector_donut_chart");
        if (donut_chart_element) {
            var total_risk_color = donut_chart_element.getAttribute('data-total-risk-color').replace('0x', '#');

            var donut_chart_root = am5.Root.new("sector_donut_chart", {
                width: am5.percent(100),
                height: am5.percent(400)
            });


            donut_chart_root.setThemes([
                am5themes_Animated.new(donut_chart_root)
            ]);

            var donut_chart = donut_chart_root.container.children.push(am5percent.PieChart.new(donut_chart_root, {
                layout: donut_chart_root.verticalLayout,
                innerRadius: am5.percent(50),
                paddingTop: 0,
                paddingLeft: 0,
                paddingRight: 0,
                paddingBottom: 25
            }));

            var donut_chart_colors = am5.ColorSet.new(donut_chart_root, {
                colors: [
                    am5.color(0x49B2C9),
                    am5.color(0x49C993),
                    am5.color(0xFDCA41),                    
                    am5.color(0xFF9D43),
                    am5.color(0xDC6788),
                ]
            });

            var donut_chart_series = donut_chart.series.push(am5percent.PieSeries.new(donut_chart_root, {
                valueField: "risk_score",
                categoryField: "sector",
                legendLabelText: "{sector}: ",
                fillField: "color",
                colors: donut_chart_colors,
                alignLabels: false
            }));

            // Get the chart data from the data attribute
            var chartData = JSON.parse(donut_chart_element.getAttribute('data-chart-series'));
            donut_chart_series.data.setAll(chartData);

            // donut_chart_series.labels.template.set("visible", false);
            // donut_chart_series.ticks.template.set("visible", false);
            donut_chart_series.labels.template.setAll({
                fontSize: 11,
                textType: "circular",
                radius: 20,
                centerX: 0,
                centerY: 0,
                text: "{sector}: {risk_score}"
            })

            donut_chart_series.slices.template.setAll({
                strokeWidth: 1,
                stroke: am5.color(0x7882EA),
                cornerRadius: 4,
            });

            var donut_chart_label = donut_chart.seriesContainer.children.push(am5.Label.new(donut_chart_root, {
                text: "[fontSize:13px; #5E6983]Sector[/]\n[fontSize:16px; #0B2562]" + decodeHTMLEntities("{{ $sectorName }}") + "[/]\n[fontSize:18px; " + total_risk_color + "]" + "{{ $totalRiskScore }}" + "[/]",
                centerX: am5.percent(50),
                centerY: am5.percent(50),
                textAlign: "center",
                fill: am5.color(0x002663),
                fontWeight: '500',
            }));

            function decodeHTMLEntities(text) {
                var textArea = document.createElement('textarea');
                textArea.innerHTML = text;
                return textArea.value;
            }

            // Create custom legend data
            var legendData = [
                { label: "Superior", color: am5.color(0x39C6D8) },
                { label: "Above Average", color: am5.color(0x49C993) },
                { label: "Average", color: am5.color(0xFDCA41) },
                { label: "Below Average", color: am5.color(0xFF9D43) },
                { label: "Poor", color: am5.color(0xDC6788) }
            ];

            // Create a separate container for the legend
            var legendContainer = donut_chart.children.push(
                am5.Container.new(donut_chart_root, {
                    layout: donut_chart_root.horizontalLayout,
                    centerX: am5.p50,
                    x: am5.p50
                })
            );

            // Add legend items
            legendData.forEach(function(item) {
                var legendItem = legendContainer.children.push(
                    am5.Container.new(donut_chart_root, {
                        layout: donut_chart_root.horizontalLayout,
                        marginRight: 10,
                        alignItems: "center"
                    })
                );

                // Legend marker
                legendItem.children.push(
                    am5.RoundedRectangle.new(donut_chart_root, {
                        width: 12,
                        height: 12,
                        fill: item.color,
                        strokeWidth: 1,
                        stroke: am5.color(0x7882EA),
                        cornerRadiusTL: 4,
                        cornerRadiusTR: 4,
                        cornerRadiusBL: 4,
                        cornerRadiusBR: 4,
                        centerY: am5.p50 // Center vertically
                    })
                );

                // Legend label
                legendItem.children.push(
                    am5.Label.new(donut_chart_root, {
                        text: item.label,
                        fontSize: "12px",
                        marginLeft: -5,
                        centerY: am5.p50 // Center vertically
                    })
                );
            });

            // legendContainer.valueLabels.template.setAll({
            //     fontSize: "12px",
            //     fontWeight: "bold"
            // });
        }

        // Function to update the chart data
        function updateCurrentScoreChartForcastSector(newScore) {
            console.log(chartData);

            // Check if the index exists before trying to set properties
            if (chartData.length > 1) {
                // Insert a new element at index 1
                chartData.splice(1, 0, {
                    sector: "Grocery (forecast)",
                    value: newScore
                });
                console.log("Inserted new element at index 1:", chartData[1]);
            } else {
                // If there are not enough elements, just push the new element
                chartData.push({
                    sector: "Grocery (forecast)",
                    value: newScore
                });
                console.log("Added new element to chartData:", chartData[chartData.length - 1]);
            }

            // Update the chart series with new data
            donut_chart_series.data.setAll(chartData); // Redraw the chart with new data

            donut_chart_series.slices.each(function(slice) {
                console.log(slice.dataItem.dataContext.sector);
                if (slice.dataItem.dataContext.sector === "Grocery (forecast)") {
                    slice.set("fill", am5.color(0xffffff)); // Base color
                    slice.set("fillPattern", am5.LinePattern.new(donut_chart_root, {
                        rotation: -45,
                        strokeWidth: 1,
                        width: 2000,
                        height: 2000,
                        fill: am5.color(0x49C993)
                    }));
                }
            });
        }

        // Add event listener to the button
        document.getElementById("previewChangesButton").addEventListener("click", function() {
            var lineSize = $('#dataPredictionModal').find('#rangeSlider').val();
            if(lineSize > 25) {
                newScore = lineSize/10;
            } else {
                newScore = lineSize/10;
            }
            console.log("preview");
            // Call the update function with new data
            updateCurrentScoreChartForcastSector(newScore); // Replace newScore with actual data
        });

    });
</script>
