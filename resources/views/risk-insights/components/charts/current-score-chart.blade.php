<?php
$current_score = $dashboard->current_score->portfolio_score;
$current_score_chart_chart_color_hex = \App\Helpers\RiskInsightsHelper::getColorHexByScore($current_score);
$market_value_chart_color_hex = \App\Helpers\RiskInsightsHelper::getColorHexByScore($dashboard->current_score->market_value_score);
?>

<div id="current_score_chart" class="risk-league-current_score_chart mt-2"
     data-chart-data='<?php echo json_encode($dashboard->current_score->chart_data); ?>'
     data-chart-color='<?php echo $current_score_chart_chart_color_hex; ?>'
     data-market-value='<?php echo json_encode($dashboard->current_score->market_value ?? []); ?>'
     data-market-value-color='<?php echo $market_value_chart_color_hex; ?>'>
</div>

<script>
    am5.ready(function() {

        // Variable to store the original line size when modal opens
        var originalLineSize;

        // Event listener to capture original slider value when modal opens
        $('#dataPredictionModal').on('shown.bs.modal', function() {
            originalLineSize = parseInt($('#dataPredictionModal').find('#rangeSlider').val());
        });

        var current_score_chart_element = document.getElementById("current_score_chart");
        if (current_score_chart_element) {
            // Parse the data from the data attributes
            var current_score_chart_data = JSON.parse(current_score_chart_element.getAttribute('data-chart-data'));
            var current_score_chart_chart_color = parseInt(current_score_chart_element.getAttribute('data-chart-color').replace('0x', ''), 16);
            var market_value_data = JSON.parse(current_score_chart_element.getAttribute('data-market-value'));
            var market_value_chart_color = 0x808080; // Set to gray color
            
            var current_score_chart_root = am5.Root.new("current_score_chart");
            current_score_chart_root.setThemes([
                am5themes_Responsive.new(current_score_chart_root)
            ]);

            var current_score_chart_chart = current_score_chart_root.container.children.push(am5xy.XYChart.new(current_score_chart_root, {
                panX: false,
                panY: false,
                wheelX: "none",
                wheelY: "none",
                pinchZoomX: false,
                paddingLeft: 0,
                paddingRight: 0,
                paddingBottom: 40,
            }));

            var current_score_chart_xAxis = current_score_chart_chart.xAxes.push(am5xy.CategoryAxis.new(current_score_chart_root, {
                maxDeviation: 0,
                categoryField: "month",
                renderer: am5xy.AxisRendererX.new(current_score_chart_root, {
                    minGridDistance: 12,
                    minorGridEnabled: true,
                    cellStartLocation: 0,
                    cellEndLocation: 1,
                    paddingTop: 0,
                    paddingBottom: 0,
                    paddingLeft: 0,
                    paddingRight: 0
                }),
            }));
            current_score_chart_xAxis.get("renderer").labels.template.setAll({
                rotation: -90,
                centerY: am5.p50,
                centerX: am5.p50,
                fontSize: 12,
                dx: -15, // Fine-tune horizontal position if needed
                //dy: -50
            });

            current_score_chart_xAxis.get("renderer").grid.template.setAll({
                strokeGradient: am5.LinearGradient.new(current_score_chart_root, {
                    stops: [{
                        color: am5.color(0xE0E0E0),
                        opacity: 0.5
                    }, {
                        color: am5.color(0xE0E0E0),
                        opacity: 1
                    }],
                    rotation: 90
                }),
                strokeWidth: 1
            });

            current_score_chart_xAxis.get("renderer").setAll({
                paddingTop: 0,
                paddingBottom: 0,
                paddingLeft: 0,
                paddingRight: 0
            });

            var current_score_chart_yAxis = current_score_chart_chart.yAxes.push(am5xy.ValueAxis.new(current_score_chart_root, {
                min: 0,
                renderer: am5xy.AxisRendererY.new(current_score_chart_root, {
                    minGridDistance: 40,
                    inside: false,
                    paddingTop: 0,
                    paddingBottom: 0,
                    paddingLeft: 0,
                    paddingRight: 0
                })
            }));

            current_score_chart_yAxis.get("renderer").grid.template.setAll({
                visible: false
            });

            current_score_chart_yAxis.get("renderer").labels.template.setAll({
                fontSize: 12,
            });

            var legend = current_score_chart_chart.children.push(
                am5.Legend.new(current_score_chart_root, {
                    nameField: "name",
                    fillField: "color",
                    strokeField: "color",
                    centerX: am5.percent(28),
                    x: am5.percent(50),
                    y: am5.percent(110),
                    width: am5.percent(100),
                    layout: current_score_chart_root.horizontalLayout
                })
            );

            var market_value_series = current_score_chart_chart.series.push(am5xy.SmoothedXLineSeries.new(current_score_chart_root, {
                name: "Market Value",
                xAxis: current_score_chart_xAxis,
                yAxis: current_score_chart_yAxis,
                valueYField: "value",
                categoryXField: "month",
                locationX: 0
            }));

            market_value_series.strokes.template.setAll({
                strokeGradient: am5.LinearGradient.new(current_score_chart_root, {
                    stops: [{
                        color: am5.color(market_value_chart_color),
                        opacity: 0
                    }, {
                        color: am5.color(market_value_chart_color),
                        opacity: 1
                    }],
                    rotation: 0
                }),
                strokeWidth: 2
            });

            market_value_series.fills.template.setAll({
                visible: true,
                fillOpacity: 0.1,
                fillGradient: am5.LinearGradient.new(current_score_chart_root, {
                    stops: [{
                        color: am5.color(market_value_chart_color),
                        opacity: 0.2
                    }, {
                        color: am5.color(market_value_chart_color),
                        opacity: 0
                    }],
                    rotation: 90
                })
            });

            market_value_series.toBack();

            var current_score_chart_series = current_score_chart_chart.series.push(am5xy.SmoothedXLineSeries.new(current_score_chart_root, {
                name: "Organisation Grading",
                xAxis: current_score_chart_xAxis,
                yAxis: current_score_chart_yAxis,
                valueYField: "value",
                valueXField: "month",
                categoryXField: "month",
                locationX: 0
            }));

            current_score_chart_series.strokes.template.setAll({
                strokeGradient: am5.LinearGradient.new(current_score_chart_root, {
                    stops: [{
                        color: am5.color(current_score_chart_chart_color),
                        opacity: 0
                    }, {
                        color: am5.color(current_score_chart_chart_color),
                        opacity: 1
                    }],
                    rotation: 0
                }),
                strokeWidth: 2
            });

            current_score_chart_series.fills.template.setAll({
                visible: true,
                fillOpacity: 0.2,
                fillGradient: am5.LinearGradient.new(current_score_chart_root, {
                    stops: [{
                        color: am5.color(current_score_chart_chart_color),
                        opacity: 0.3
                    }, {
                        color: am5.color(current_score_chart_chart_color),
                        opacity: 0
                    }],
                    rotation: 90
                })
            });

            legend.labels.template.setAll({
                fontSize: "12px"
            });

            legend.valueLabels.template.setAll({
                fontSize: "12px",
                fontWeight: "bold"
            });

            legend.markers.template.setAll({
                width: 11,
                height: 11
            });
           
            legend.data.setAll([{
                name: @if(isset($_GET['risk']))
                        "Organisation Grading"
                        @else
                        "Lll Grading"
                        @endif,
                color: am5.color(current_score_chart_chart_color),
            }, {
                name: @if(isset($_GET['risk']))
                        "Sector Grading"
                        @else
                        "Market Grading"
                        @endif,
                color: am5.color(market_value_chart_color),
            }]);

            // Find the last data point
            var lastDataPointIndex = current_score_chart_data.length - 2;
            var lastDataPointCategory = current_score_chart_data[lastDataPointIndex].month;

            // Create axis range specifically for Mar
            var seriesRangeDataItem = current_score_chart_xAxis.createAxisRange(
                current_score_chart_xAxis.makeDataItem({
                    category: lastDataPointCategory,
                    endCategory: lastDataPointCategory,
                    location: 0,
                    endLocation: 1
                })
            );
            <?php if (isset($_GET['forecast']) && $_GET['forecast'] == 1): ?>
            // var seriesRange = current_score_chart_series.createAxisRange(seriesRangeDataItem);
            // seriesRange.fills.template.setAll({
            //     visible: true,
            //     opacity: 0.3,
            //     fillPattern: am5.LinePattern.new(current_score_chart_root, {
            //         color: am5.color(current_score_chart_chart_color),
            //         rotation: -45,
            //         strokeWidth: 2,
            //         width: 2000,
            //         height: 2000,
            //         fill: am5.color(0xffffff)
            //     })
            // });
            <?php endif; ?>

            // Replace the hardcoded data with the parsed data
            current_score_chart_xAxis.data.setAll(current_score_chart_data);
            current_score_chart_series.data.setAll(current_score_chart_data);
            market_value_series.data.setAll(market_value_data);

            current_score_chart_series.appear(1000);
            current_score_chart_chart.appear(1000, 100);
            setTimeout(function() {
                adjustRatingDivPositions();
            }, 1000);
        }

        // Function to update the chart data
        function updateCurrentScoreChartForcastLine(newScore) {
            // Modify current_score_chart_data based on newScore
            // Example: Assuming newScore is an array of new data points
            current_score_chart_data = JSON.parse(current_score_chart_element.getAttribute('data-chart-data')); // Update the data
            current_score_chart_data[11].value = newScore;
            // Find the last data point
            var lastDataPointIndex = current_score_chart_data.length - 2;
            var lastDataPointCategory = current_score_chart_data[lastDataPointIndex].month;

            // Create axis range specifically for Mar
            var seriesRangeDataItem = current_score_chart_xAxis.createAxisRange(
                current_score_chart_xAxis.makeDataItem({
                    category: lastDataPointCategory,
                    endCategory: lastDataPointCategory,
                    location: 0,
                    endLocation: 1
                })
            );

            var seriesRange = current_score_chart_series.createAxisRange(seriesRangeDataItem);
            seriesRange.fills.template.setAll({
                visible: true,
                opacity: 0.3,
                fillPattern: am5.LinePattern.new(current_score_chart_root, {
                    color: am5.color(current_score_chart_chart_color),
                    rotation: -45,
                    strokeWidth: 2,
                    width: 2000,
                    height: 2000,
                    fill: am5.color(0xffffff)
                })
            });
            
            // Update all rating divs with the new score
            var ratingWrapper = document.querySelector('.rating-wrapper');
            var ratingDivs = ratingWrapper.querySelectorAll('.risk-league-rating');
            var ratingDiv = ratingDivs[0];
            ratingDiv.style.bottom = `calc(${newScore}%)`;
            ratingDiv.textContent = newScore;
            
            // Adjust positions to prevent overlap
            
            
            current_score_chart_xAxis.data.setAll(current_score_chart_data);
            current_score_chart_series.data.setAll(current_score_chart_data);

            current_score_chart_series.appear(1000);
            current_score_chart_chart.appear(1000, 100);
            setTimeout(function() {
                adjustRatingDivPositions();
            }, 1000);
        }

        // Function to adjust rating div positions to prevent overlap
        function adjustRatingDivPositions() {
            var ratingWrapper = document.querySelector('.rating-wrapper');
            if (!ratingWrapper) return;
            
            var ratingDivs = ratingWrapper.querySelectorAll('.risk-league-rating');
            if (ratingDivs.length <= 1) return;
            
            // Convert NodeList to Array and sort by bottom position (highest to lowest)
            var divsArray = Array.from(ratingDivs);
            divsArray.sort(function(a, b) {
                var aBottom = parseInt(a.textContent)+30;
                var bBottom = parseInt(b.textContent)+30;
                return bBottom - aBottom; // Sort descending (highest bottom first)
            });
            
            // Check for overlaps and adjust positions
            for (var i = 0; i < divsArray.length - 1; i++) {
                var currentDiv = divsArray[i];
                var nextDiv = divsArray[i + 1];
                
                var currentBottom = parseInt(currentDiv.textContent)+30;
                var nextBottom = parseInt(nextDiv.textContent)+30;
                if(isNaN(currentBottom)) {
                    currentBottom = parseInt(currentDiv.textContent);
                }
                if(isNaN(nextBottom)) {
                    nextBottom = parseInt(nextDiv.textContent);
                }
                var minSeparation = 20;
                
                // Calculate the difference between positions
                var difference = Math.abs(currentBottom - nextBottom);
                // If difference is less than minimum separation, adjust the lower div
                if (difference < minSeparation) {
                    var newBottom = currentBottom - minSeparation;
                    nextDiv.style.bottom = newBottom + 'px';
                }
            }
        }

        // Add event listener to the button
        document.getElementById("previewChangesButton").addEventListener("click", function() {
			var lineSize = parseInt($('#dataPredictionModal').find('#rangeSlider').val());
            var ratingWrapper = document.querySelector('.rating-wrapper');
            var ratingDivs = ratingWrapper.querySelectorAll('.risk-league-rating');
            var currentScore = parseInt(ratingDivs[0].textContent);
            if(lineSize >= originalLineSize) {
                newScore = currentScore + (lineSize-originalLineSize)/5;
            } else {
                newScore = currentScore - (originalLineSize-lineSize)/5;
            }
            newScore = Math.round(newScore);
            if(newScore > 100) {
                newScore = 100;
            } else if(newScore < 0) {
                newScore = 0;
            }
            // Call the update function with new data
            updateCurrentScoreChartForcastLine(newScore); // Replace newScore with actual data
        });
        
        // Call adjustRatingDivPositions on page load to handle initial overlaps
        adjustRatingDivPositions();
        
        // Also call it after a short delay to ensure all elements are rendered
        setTimeout(function() {
            adjustRatingDivPositions();
        }, 100);
        
        // Call it when window is resized to handle any layout changes
        window.addEventListener('resize', function() {
            setTimeout(function() {
                adjustRatingDivPositions();
            }, 100);
        });
    });

</script>