<div class="modal-content">
    <div class="modal-header justify-content-between">
        <div class="risk-league-modal-steps d-flex align-items-center fs-15">
            <div id="organization-selection-step">
                <span>1. Organization Selection</span>
            </div>
            <div class="active" id="upload-files-step">
                <span>2. Upload Files</span>
            </div>
            <div id="review-step">
                <span>3. Review</span>
            </div>
        </div>
    </div>
    <div class="modal-body">
        <div>
            <span class="fs-18 fw-600">{{ $organization->name }}</span>
            <div>
                <ul class="list-group mt-2">
                    @foreach ($locations as $location)
                        <li class="list-group-item fs-16 fw-400">{{ $location->location_name }}</li>
                    @endforeach
                </ul>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="risk-league-modal-upload">
                    <div class="mb-2">
                        Drag files here or use the 'Choose Files' button
                    </div>
                    <input type="file" id="fileUploadMultiple" multiple style="display: none;">
                    
                    <label for="fileUploadMultiple" id="startUploadButton" class="btn btn-sm btn-primary text-white">
                        <i class="icon icon-upload"></i> Choose Files
                    </label>
                </div>
            </div>
        </div>

        <div class="risk-league-uploadedFiles table-responsive mt-3">
            <table class="table">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Size</th>
                        <th></th>
                        <th>Organisation Location</th>
                    </tr>
                </thead>

                <tbody id="uploadedFilesTableBody">
                </tbody>
            </table>

            <div class="card mt-3">
                <div class="card-body">
                    <div class="py-2">
                        <div class="d-flex align-items-left justify-content-left">
                            <label class="ios-toggle me-3">
                                <input type="checkbox" id="skipHumanInTheLoop" checked>
                                <span class="toggle-slider"></span>
                            </label>
                            <span class="mt-2 ml-2">Human in the loop</span>
                        </div>
                        <!-- <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="skipHumanInTheLoop">
                            <label class="custom-control-label" for="skipHumanInTheLoop">Skip Human-in-the-loop</label>
                        </div> -->
                    </div>
                    <div class="py-2">
                        <div class="d-flex align-items-left justify-content-left">
                                <label class="ios-toggle me-3">
                                    <input type="checkbox" id="disableRiskEngineerEvaluation">
                                    <span class="toggle-slider"></span>
                                </label>
                                <span class="mt-2 ml-2">Risk Engineer Evaluation</span>
                        </div>
                        <!-- <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="disableRiskEngineerEvaluation">
                            <label class="custom-control-label" for="disableRiskEngineerEvaluation">Disable Risk Engineer Evaluation</label>
                        </div> -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        $('#startUploadButton').click(function() {
            console.log("startUploadButton");
        });

        var fileUpload = document.getElementById("fileUploadMultiple");
        if (fileUpload) {
            fileUpload.addEventListener("change", function (e) {
                console.log("fileUpload");
                if (this.files.length > 0) {
                    console.log(this.files);
                    addFilesToTable(this.files);
                    startUploadAnimation();
                }
            });
        }

        function addFilesToTable(files) {
            var tableBody = document.getElementById("uploadedFilesTableBody");

            for (var i = 0; i < files.length; i++) {
                var file = files[i];
                var row = document.createElement("tr");

                // File name cell
                var nameCell = document.createElement("td");
                nameCell.innerHTML = '<div class="risk-league-file">' + file.name + '</div>';
                row.appendChild(nameCell);

                // File size cell
                var sizeCell = document.createElement("td");
                sizeCell.textContent = (file.size / 1024).toFixed(2) + ' KB';
                row.appendChild(sizeCell);

                // Action cell (upload progress + remove link)
                var actionCell = document.createElement("td");
                actionCell.innerHTML = `
                    <div class="risk-league-upload">
                        <div class="upload-progress-bar">
                            <div class="progress-bar"></div>
                        </div>
                        <a href="#" class="remove-link">Remove</a>
                    </div>
                `;
                row.appendChild(actionCell);

                // Add event listener for remove link
                $(actionCell).find('.remove-link').on('click', function(e) {
                    e.preventDefault();
                    // Remove the row from the table
                    row.remove();
                });

                // Location select dropdown cell
                var locationCell = document.createElement("td");
                locationCell.innerHTML = `
                <div class="selectric-wrapper">
                    <select name="" class="selectric-location-dropdown">
                        <option value="">Select Option</option>
                        @foreach ($locations as $location)
                            <option value="{{ $location->id }}" data-tiv="{{ $location->tiv }}">{{ $location->location_name }}</option>
                        @endforeach
                    </select>
                </div>
                `;
                row.appendChild(locationCell);

                // Append the row to the table body
                tableBody.appendChild(row);

                $(locationCell).find('.selectric-location-dropdown').selectric();
                $(locationCell).find('.selectric-location-dropdown').on('change', function () {
                    const selectedLocation = $(this).val();
                    $('.risk-league-review-btn').prop('disabled', false);
                });

                // Start animation for the newly added file
                (function($progressBar, $uploadContainerParent) {
                    $progressBar.css("width", "0");
                    $uploadContainerParent.removeClass("upload-complete");

                    setTimeout(function () {
                        $progressBar.css("width", "100%");
                    }, 100 + i * 1000);

                    setTimeout(function () {
                        $uploadContainerParent.addClass("upload-complete");
                    }, 1500 + i * 1000);
                })($(actionCell).find(".progress-bar"), $(actionCell).find(".risk-league-upload"));
            }
        }
    });
</script>

