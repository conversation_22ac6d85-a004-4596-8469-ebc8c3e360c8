<!-- Add amCharts dependencies -->
<script src="https://cdn.amcharts.com/lib/5/index.js"></script>
<script src="https://cdn.amcharts.com/lib/5/xy.js"></script>
<script src="https://cdn.amcharts.com/lib/5/themes/Animated.js"></script>

<div class="modal-body">
    <div class="card mb-3">
        <div class="card-body">
            <div class="py-2 px-2 d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <button class="btn btn-bordered portfolio-impact-back-btn text-center mr-2 p-0" style="width: 24px; height: 24px;"><i class="icon icon-chevron-left text-primary mr-0 fs-12"></i></button>
                    @php
                    $clientName = $riskReport->auditLogs->metadata_update->client_name ?? 'Tesco Plc';
                    @endphp
                    {{ $clientName }}
                </div>
                @if($riskReport->document->assigned_to_user && $riskReport->document->assigned_user->role == 'underwriter')
                <button type="button" class="btn btn-success ml-2 portfolio-impact-add-to-database-btn"><i class="icon icon-check fs-14 mr-2"></i> Add to Database</button>
                @endif
            </div>
        </div>
    </div>

    <div class="card mb-3">
        <div class="card-title">
            <i class="icon icon-info"></i>
            General Information
        </div>
        <div class="card-body">
            <div class="mb-3">
                <label for="">Broker</label>
                <select name="" id="" class="form-control">
                    <option value="">{{ $riskReport->document->organisation->broker->name ?? 'Broker' }}</option>
                </select>
            </div>

            <div class="mb-3">
                <label for="">Lead insurer</label>
                <select name="" id="" class="form-control">
                    <option value="">{{ $riskReport->document->organisation->lead_insurer->name ?? 'LII' }}</option>
                </select>
            </div>

            <div class="mb-3">
                <label for="">Sector</label>
                <select name="" id="" class="form-control">
                    <option value="">{{ $riskReport->document->organisation->sector->handle ?? 'Sector' }}</option>
                </select>
            </div>

            <div>
                <label for="">Sub-sector</label>
                <select name="" id="" class="form-control">
                    <option value="">{{ $riskReport->document->organisation->sub_sector->handle ?? 'Sub-sector' }}</option>
                </select>
            </div>
        </div>
    </div>

    <div class="card mb-3">
        <div class="card-title" data-toggle="collapse" href="#collapseOrganisation01">
            Organisation
        </div>
        <div class="card-body p-0">
            <div class="p-3 collapse" id="collapseOrganisation01">
                <div class="card no-shadow">
                    <div class="card-body bg-lighter fw-400">
                        The overall risk quality of the grocery sub-sector in our portfolio will increase upon binding this risk
                    </div>
                </div>

                <div class="mt-4 slider_wrapper">
                    <input type="range" id="portfolio_impact_slider" min="0" max="100" value="{{ $riskReport->document->organisation->line_size ?? 0 }}" style="width: 100%;" />
                    <div class="range-value mt-2" id="portfolio_impact_range_value">{{ $riskReport->document->organisation->line_size ?? 0 }}%</div>
                </div>

                <div id="portfolio_impact_chart_01" class="my-4 risk-league-portfolio_impact_chart_01" style="width: 100%; height: 300px;"></div>

                <div class="card no-shadow">
                    <div class="card-body bg-lighter fw-400">
                        The target loss estimates for the grocery sector will remain unchanged upon binding this risk
                    </div>
                </div>

                <div class="d-flex align-items-center">
                    <div id="portfolio_impact_chart_02" class="my-4 risk-league-portfolio_impact_chart_02" style="width: 45%; height: 220px;"></div>
                    <div class="px-3">
                        <i class="icon icon-arrow-right"></i>
                    </div>
                    <div id="portfolio_impact_chart_03" class="my-4 risk-league-portfolio_impact_chart_02" style="width: 45%; height: 220px;"></div>
                </div>
            </div>

        </div>
    </div>
</div>

<script>
    const rangeInput = document.getElementById("portfolio_impact_slider");
    const rangeValue = document.getElementById("portfolio_impact_range_value");

    function updateRangeLabelPosition() {
        const value = rangeInput.value;
        const min = rangeInput.min ? rangeInput.min : 0;
        const max = rangeInput.max ? rangeInput.max : 100;
        const newValue = ((value - min) / (max - min)) * 100; // Calculate percentage

        // Update label position
        rangeValue.style.left = `calc(${newValue}% + (${8 - newValue * 0.15}px))`;
        rangeValue.textContent = value + "%";
    }

    rangeInput.addEventListener("input", updateRangeLabelPosition);
    updateRangeLabelPosition();
</script>