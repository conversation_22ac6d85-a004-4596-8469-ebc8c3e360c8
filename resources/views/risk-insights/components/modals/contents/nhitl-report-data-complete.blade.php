<div class="modal-header justify-content-between">
    <div class="d-flex align-items-center fs-15">
        <div>
            Report Data Has Been Added to Database.
            <p>You can view the report data but it cannot be edited.</p>
        </div>
    </div>
</div>
<div class="modal-body">
    <div class="card">
        <div class="card-body">
            <div class="py-2 px-2 d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <i class="icon icon-folder text-primary mr-2"></i>
                    {{ $reportResult->auditLogs?->metadata_update?->client_name }}
                    @if($reportResult->document->do_skip_human_in_the_loop)
                    <i class="icon-person-x fs-16 mr-2 text-warning"></i>
                    @endif
                </div>
                <div class="risk-league-portfolio-score">
                    Organisation performance score
                    <div class="risk-league-rating green lg ml-2 animate__animated animate__repeat-2">79</div>
                </div>
            </div>
            <div class="table-resonsive mt-2">
                <table class="table">
                    <tbody>
                        @foreach ($reportResult->auditLogs?->metadata_update?->locations as $location)
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="icon icon-map-pin fs-16 mr-2 text-primary"></i>
                                    <span>{{ $location->location }}</span>
                                    <div class="risk-league-rating red ml-2">{{ $location->report_level_grading->score}}</div>
                                    <span class="text-secondary ml-2" style="opacity: 0.7; font-size: 0.9em;">{{ $location->document_name }}</span>
                                </div>
                            </td>
                            <td class="text-right">
                                <a href="{{ route('risk-insights.view-report-narratives') }}?name={{ $location->location }}&documentId={{ $reportResult->document->id }}" target="_blank">View <i class="icon icon-arrow-up-right fs-10"></i></a>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="card mt-3">
        <div class="card-body">
            <div class="py-2">
                <div class="d-flex align-items-left justify-content-left">
                    <label class="ios-toggle me-3">
                        <input type="checkbox" id="nhitlTag" {{ $reportResult->document->do_skip_human_in_the_loop ? '' : 'checked' }}>
                        <span class="toggle-slider"></span>
                    </label>
                    <span class="mt-2 ml-2">Human in the loop</span>
                </div>
                <p class="text-secondary" style="opacity: 0.7; font-size: 0.9em;">You can remove the <i class="icon-person-x fs-16 mr-2 text-warning"></i> tag and edit the report data.</p>
            </div>
        </div>
    </div>

    <div class="card mt-3">
        <div id="reportResultData" data-report-result='@json($reportResult)'></div>
    </div>

    <script>
        $(document).ready(function() {
            $('#reviewRiskReportModal .update-document-btn').show();
            $('.update-document-btn').prop('disabled', true);
            $('#reviewRiskReportModal .risk-league-next-btn').hide();
            $('#reviewRiskReportModal .btn-cancel').hide();
            $('.btn-close').show();
            $('#uploadRiskReportFileModal .risk-league-close-and-notify-btn').hide();
            $('#uploadRiskReportFileModal .btn-cancel').hide();

            $('#nhitlTag').change(function() {
                console.log('nhitlTag');
                console.log($(this).prop('checked'));
                if ($(this).prop('checked')) {
                    $('.update-document-btn').prop('disabled', false);
                } else {
                    $('.update-document-btn').prop('disabled', true);
                }
            });
        });
    </script>