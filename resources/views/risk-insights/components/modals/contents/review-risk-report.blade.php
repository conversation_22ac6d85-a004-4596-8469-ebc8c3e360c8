<div class="modal-header justify-content-between">
    <div class="risk-league-modal-steps d-flex align-items-center fs-15">
        <div>
            <span>1. Select Organisation</span>
        </div>
        <div>
            <span>2. Upload Files</span>
        </div>
        <div class="active">
            <span>3. Review</span>
        </div>
    </div>

</div>
<div class="modal-body">
    <div class="card">
        <div class="card-body">
            <div class="py-2 px-2 d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <i class="icon icon-folder text-primary mr-2"></i>
                    {{ $reportResult->auditLogs?->metadata_update?->client_name }}
                    @if($reportResult->document->do_skip_human_in_the_loop)
                    <i class="icon-person-x fs-16 mr-2 text-warning"></i>
                    @endif
                </div>
                <div class="risk-league-portfolio-score">
                    Organisation performance score
                    <div class="risk-league-rating blue lg ml-2 animate__animated animate__repeat-2">89</div>
                </div>
            </div>
            <div class="table-resonsive mt-2">
                <table class="table">
                    <tbody>
                        @foreach ($reportResult->auditLogs?->metadata_update?->locations as $location)
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="icon icon-map-pin fs-16 mr-2 text-primary"></i>
                                    <span>{{ $location->location }}</span>
                                    <div class="risk-league-rating blue ml-2">89</div>
                                    <span class="text-secondary ml-2" style="opacity: 0.7; font-size: 0.9em;">{{ $location->document_name }}</span>
                                </div>
                            </td>
                            <td class="text-right">
                                <a href="{{ route('risk-insights.view-report-narratives') }}?name={{ $location->location }}&documentId={{ $reportResult->document->id }}" target="_blank">View <i class="icon icon-arrow-up-right fs-10"></i></a>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="card mt-3">
        <div class="card-body">
            <div class="py-2">
                <div class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input" id="confirmReview">
                    <label class="custom-control-label" for="confirmReview">By selecting this checkbox, I confirm that I have reviewed the documents and verify the accuracy of the information provided.</label>
                </div>
            </div>
        </div>

        <div id="reportResultData" data-report-result='@json($reportResult)'></div>
    </div>

    <script>
        $(document).ready(function() {
            const reportResult = $('#reportResultData').data('report-result');
            $('#reviewRiskReportModal .update-document-btn').hide();

            if (reportResult.document.assigned_user && reportResult.document.assigned_user.role === 'underwriter') {
                $('#finishReviewButton').hide();
                $('#addToDatabaseButton').show();
            } else {
                $('#addToDatabaseButton').hide();
                $('#finishReviewButton').show();
            }
            $('#confirmReview').change(function() {
                if ($(this).prop('checked')) {
                    $('.add-to-database-btn').prop('disabled', false);
                    $('.finish-review-btn').prop('disabled', false);
                } else {
                    $('.add-to-database-btn').prop('disabled', true);
                    $('.finish-review-btn').prop('disabled', false);
                }
            });
        });
    </script>