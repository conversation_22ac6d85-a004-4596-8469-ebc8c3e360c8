<div class="modal-header justify-content-between">
    <div class="risk-league-modal-steps d-flex align-items-center fs-15">
        <div class="active" id="organization-selection-step">
            <span>1. Organisation Selection</span>
        </div>
        <div>
            <span>2. Upload Files</span>
        </div>
        <div id="review-step">
            <span>3. Review</span>
        </div>
    </div>

</div>
<div class="modal-body">
    <form>
        <div class="lh-18 mb-3">
            <label for="" class="fs-18 field-title">Risk Reports Upload</label>
        </div>
        <div>
            {{ Form::label('organisation','Select Organisation') }}
            {{ Form::select('organisation', $organizations, null, [
                'id'=>'organisation-select-dropdown', 
                'class' => 'form-control select2',
                'placeholder' => 'Select Option'
            ]) }}
        </div>
        <div class="my-2" style="display: flex; flex-direction: column; gap: 5px;">
            <div>
                <a href="/organisation/create" class="fs-12 fw-200 disabled-link" id="edit-organization-link" tabindex="-1" aria-disabled="true" target="_blank">
                    <i class="fa fa-pencil"></i>
                    Edit selected organisation
                </a>
            </div>
            <div>
                <a href="/organisation/create" class="fs-12 fw-200" id="add-organization-link" target="_blank">
                    <i class="fa fa-plus"></i>
                    Can't find organisation? Add one
                </a>
            </div>
        </div>

        <div class="mt-2">
            {{ Form::label('locations','Select Organisation Location(s)') }}
            {{ Form::select('locations[]', $locations, $selectedLocations, [
                'id'=>'organisation-location-select-dropdown', 
                'class' => 'form-control select2',
                'multiple' => true
            ]) }}
        </div>

        <div class="my-2" style="display: flex; flex-direction: column; gap: 5px;">
            <div>
                <a href="/organisation/create" class="fs-12 fw-200" id="edit-location-link" style="pointer-events: none; opacity: 0.5;" tabindex="-1" aria-disabled="true" target="_blank">
                    <i class="fa fa-pencil"></i>
                    Edit location(s)
                </a>
            </div>
            <div>
                <a href="/organisation/create" class="fs-12 fw-200" id="add-location-link" style="pointer-events: none; opacity: 0.5;" tabindex="-1" aria-disabled="true" target="_blank">
                    <i class="fa fa-plus"></i>
                    Can't find location? Add one
                </a>
            </div>
        </div>
        <div id="orgLocationsData" data-org-locations='@json($orgLocations)'></div>
        <div id="selectedLocationsData" data-selected-locations='@json($selectedLocations)'></div>
        <div class="justify-content-between toolbar dataTables_wrapper">
        <div class="table-responsive">
            <table class="table table-striped table-borderless"> 
                <thead>
                    <tr class="sorting">
                        <th id="org-location-name">Organisation Location Name</th>
                        <th id="tiv">TIV</th>
                        <th id="actions"></th>
                    </tr>
                </thead>
                <tbody id="organization-location-table-body">
                    
                </tbody>
            </table>
            </div>
        </div>
    </form>
</div>



<script>
    $(document).ready(function() {
        const orgLocations = $('#orgLocationsData').data('org-locations');
        const $orgSelect = $('#organisation-select-dropdown');
        const $locationSelect = $('#organisation-location-select-dropdown');
        const selectedLocations = $('#selectedLocationsData').data('selected-locations');

        if (orgLocations.length > 0) {
            updateOrganizationLocationDropdown(orgLocations);
            $orgSelect.val(orgLocations[0].organisation_id).trigger('change');
        }

        $orgSelect.select2({
            width: '100%',
            dropdownParent: $('#uploadRiskReportFileModal'),
            minimumInputLength: 0,
        });

        $locationSelect.select2({
            width: '100%',
            dropdownParent: $('#uploadRiskReportFileModal'),
            minimumInputLength: 0,
            allowClear: true,
        });

        function updateOrganizationDropdown(data) {
            const selectedOrg = $orgSelect.val() || null;
            $orgSelect.empty().append($('<option>', {
                value: '',
                text: 'Select Option'
            }));
            data.forEach(function(org) {
                $orgSelect.append($('<option>', {
                    value: org.id,
                    text: org.name
                }));
            });

            $orgSelect.val(selectedOrg);
        }

        function updateOrganizationLocationDropdown(data) {
            // Refresh the selectric plugin
            $locationSelect
                .val(null)
                .html('')
                .trigger('change');


            // Capture the currently selected values
            const selectedValues = $locationSelect.val() || selectedLocations || [];

            let selectedLocationData = [];
            data.forEach(function(location) {
                $locationSelect.append($('<option>', {
                    value: location.id,
                    text: location.location_name,
                    'data-tiv': location.tiv || 0 
                }));

                if (selectedLocations && selectedLocations.length > 0) {
                    if (selectedLocations.includes((String(location.id)))) {
                        selectedLocationData.push({
                            value: location.id,
                            text: location.location_name,
                            tiv: location.tiv || 0 
                        });
                    }
                }
            });

            // Re-select the previously selected values
            $locationSelect.val(selectedValues);

            if (selectedLocations && selectedLocations.length > 0) {
                $locationSelect.val(selectedLocations).trigger('change');
                if (selectedLocationData.length > 0) {
                    updateLocationTable(selectedLocationData);
                }
            }

            // Fetch the new selected values
            const newSelectedValues = $locationSelect.val() || [];

            // Update the table
            const tableBody = $('#organization-location-table-body');
            const existingRows = tableBody.find('tr');

            // Iterate over existing rows and remove those not in the data
            existingRows.each(function() {
                const rowId = $(this).attr('class').match(/org-location-id-(\d+)/)[1];
                if (!newSelectedValues.includes(String(rowId))) {
                    console.log('remove row', rowId)
                    $(this).remove();
                }
            });

        }

        function fetchOrganizationLocations(organizationId) {
            $.ajax({
                url: '{{ route('risk-insights.organization-locations') }}',
                method: 'GET',
                data: {
                    organizationId: organizationId
                },
                success: function(response) {
                    updateOrganizationLocationDropdown(response);
                },
                error: function(xhr, status, error) {
                    console.error('Failed to fetch organization locations:', error);
                }
            });
        }
        
        function updateLocationTable(selectedLocations) {
            const tableBody = $('#organization-location-table-body');
            tableBody.empty();
            
            selectedLocations.forEach(function(location) {
                const tivValue = location.tiv || 0;  // Use 0 if TIV is null
                const formattedTiv = new Intl.NumberFormat().format(tivValue); // Format number with commas
                
                const row = `
                    <tr class="org-location-id-${location.value}">
                        <td class="location-name">${location.text}</td>
                        <td class="text-left location-tiv-value">${formattedTiv}</td>
                        <td>
                            <a href="/organisation/location/${location.value}" 
                            class="btn btn-link btn-icon btn-sm location-edit-link" 
                            target="_blank">
                                <i class="icon icon-eye"></i>Edit
                            </a>
                        </td>
                    </tr>
                `;
                tableBody.append(row);
            });
        }

        $locationSelect.on('change', function() {
            const selectedOptions = $(this).find(':selected');
            const selectedLocations = selectedOptions.map(function() {
                return {
                    value: $(this).val(),
                    text: $(this).text(),
                    tiv: $(this).data('tiv')  
                };
            }).get();
            
            updateLocationTable(selectedLocations);
        });

        $orgSelect.on('change', function() {
            var selectedValue = $(this).val();

            if (selectedValue) {
                $('#edit-organization-link').attr('href', '/organisation/' + selectedValue + '/edit')
                    .css({
                        'pointer-events': 'auto',
                        'opacity': '1'
                    })
                    .attr('tabindex', '0')
                    .removeAttr('aria-disabled');

                $('#add-location-link').attr('href', '/organisation/' + selectedValue + '/locations/create')
                    .css({
                        'pointer-events': 'auto',
                        'opacity': '1'
                    })
                    .attr('tabindex', '0')
                    .removeAttr('aria-disabled');

                $('#edit-location-link').attr('href', '/organisation/' + selectedValue + '/locations')
                    .css({
                        'pointer-events': 'auto',
                        'opacity': '1'
                    })
                    .attr('tabindex', '0')
                    .removeAttr('aria-disabled');

                fetchOrganizationLocations(selectedValue);
            } else {
                $('#edit-organization-link').attr('href', '/organisation/create')
                    .css({
                        'pointer-events': 'none',
                        'opacity': '0.5'
                    })
                    .attr('tabindex', '-1')
                    .attr('aria-disabled', 'true');

                $('#add-location-link').attr('href', '/organisation/create')
                    .css({
                        'pointer-events': 'none',
                        'opacity': '0.5'
                    })
                    .attr('tabindex', '-1')
                    .attr('aria-disabled', 'true');

                $('#edit-location-link').attr('href', '/organisation/create')
                    .css({
                        'pointer-events': 'none',
                        'opacity': '0.5'
                    })
                    .attr('tabindex', '-1')
                    .attr('aria-disabled', 'true');

                $locationSelect
                    .val(null)
                    .html('')
                    .trigger('change');
            }
        });

        function updateLocationRowData(updatedLocation) {
            const className = `org-location-id-${updatedLocation.id}`
            const $row = $(`.${className}`)
            $row.find('.location-name').text(updatedLocation.location_name)
            $row.find('.location-tiv-value').text(updatedLocation.tiv)
        }

        function pollUpdates(tableName, lastUpdated, orgId = null, locationId = null) {
            $.ajax({
                url: '{{ route('risk-insights.poll-updates') }}',
                method: 'GET',
                data: {
                    table: tableName,
                    last_updated: lastUpdated,
                    org_id: orgId,
                    location_id: locationId
                },
                success: function(response) {
                    const data = JSON.parse(response);
                    if (data.updated) {
                        if (tableName === 'organisation_location') {
                            if (data.selected_location) {
                                updateLocationRowData(data.selected_location);
                            }
                            updateOrganizationLocationDropdown(data.organisation_locations);
                        } else if (tableName === 'organisation') {
                            updateOrganizationDropdown(data.organisations);
                        }
                    }
                },
                error: function(err) {
                    console.error('Polling failed', err);
                    setTimeout(pollUpdates, 3000); // Retry after delay
                }
            });
        }

        $('#add-organization-link').on('click', function() {
            pollUpdates('organisation', 0);
        });

        $('#edit-organization-link, #add-location-link').on('click', function() {
            const orgId = $('#organisation-select-dropdown').val();
            pollUpdates('organisation', 0, orgId);
        });

        $('#edit-location-link').on('click', function() {
            const orgId = $('#organisation-select-dropdown').val();
            const locationIds = $('#organisation-location-select-dropdown').val() || [];
            pollUpdates('organisation_location', 0, orgId, locationIds);
        });

        $('#organization-location-table-body').on('click', '.location-edit-link', function (e) {
            const link = $(this).attr('href');
            const orgId = $('#organisation-select-dropdown').val();
            const locationIds = $('#organisation-location-select-dropdown').val() || [];
            
            const locationId = link.split('/').pop();

            pollUpdates('organisation_location', 0, orgId, locationId);
        });

        

    });
</script>