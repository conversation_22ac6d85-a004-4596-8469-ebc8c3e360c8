<style>
    .loading-wrapper {
        position: absolute;
        top: 80%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1051; /* Ensure it's above the modal's z-index */
        width: 100%;
        height: 100%;
        display: none; /* Initially hidden */
        margin-top: 100px;
    }
</style>

<div class="modal fade risk-league-modal-wrapper" id="uploadRiskReportFileModal" tabindex="-1" aria-labelledby="uploadRiskReportFileModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg risk-league-modal">
        <div class="modal-content">
            <div class="modal-content-wrapper" style="min-height: 300px;"></div>
            <div class="loading-wrapper text-center mt-5">
                <div class="spinner-border" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-between default-button-wrapper">
                <button type="button" class="btn btn-light bg-light btn-cancel" data-dismiss="modal">Cancel</button>
                <button type="button" class="risk-league-next-btn btn btn-primary">Next <i class="icon icon-chevron-right fs-14 ml-2"></i></button>
                <button type="button" class="risk-league-prev-btn btn btn-primary">Previous <i class="icon icon-chevron-left fs-14 ml-2"></i></button>
                <button type="button" class="risk-league-review-btn btn btn-primary">Review <i class="icon icon-chevron-right fs-14 ml-2"></i></button>
                <button type="button" class="risk-league-close-and-notify-btn btn btn-primary" data-dismiss="modal">Close and Notify Me When It's Done</button>
                <button type="button" class="btn btn-light bg-light update-document-btn" data-dismiss="modal">Update Document</button>

                <button type="button" class="btn btn-light bg-light btn-close" data-dismiss="modal">Close</button>
                <div class="d-flex ml-auto review-button-wrapper">
                    <button data-dismiss="modal" data-toggle="modal" data-target="#newSubmission04" type="button" class="btn btn-primary portfolio-impact-btn"><i class="icon icon-bar-chart fs-14 mr-2"></i> Portfolio Impact</button>
                    <button id="newDataButton" data-dismiss="modal" disabled type="button" class="btn btn-success ml-2 add-to-database-btn"><i class="icon icon-check fs-14 mr-2"></i> Add to Database</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        let step = 1; 
        const totalSteps = 4;
        let organizationId = null;
        let locationIds = [];
        let documentId = null;

        let lastUpdated = null;
        let tableName = 'organisation';

        function getQueryParam(param) {
            let urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(param);
        }

        function fetchModalContent(step) {
            if (getQueryParam('documentId')) return;
            $('.loading-wrapper').show();
            let params = {
                step: step
            };

            if (step === 2) {
                organizationId = $('#organisation-select-dropdown').val();
                locationIds = $('#organisation-location-select-dropdown').val();
            }

            if (step >= 3) {
                params.document_id = documentId
                params.skip_human_in_the_loop = !$('#skipHumanInTheLoop').is(':checked');
            }

            if (organizationId !== null && locationIds.length > 0) {
                params.organizationId = organizationId;
                params.locationIds = locationIds;
            }

            $('.btn').prop('disabled', true);

            console.log('params', params)
            
            $.ajax({
                url: "{{ route('risk-insights.modal-content') }}",
                type: "GET",
                data: params,
                success: function(response) {
                    $('.loading-wrapper').hide();
                    $('.modal-content-wrapper').html(response);
                    updateButtonVisibility(step);
                },
                error: function(xhr, status, error) {
                    $('.loading-wrapper').hide();
                    console.error(xhr.responseText);
                }
            });
        }

        function updateButtonVisibility() {
            $('.btn').prop('disabled', false);
            $('.risk-league-next-btn').toggle(step == 1);
            $('.risk-league-prev-btn, .risk-league-review-btn').toggle(step == 2);
            $('.portfolio-impact-btn, .add-to-database-btn').toggle(step == 4);
            $('.risk-league-close-and-notify-btn').toggle(step == 3);
            $('.update-document-btn').toggle(step == 3);
            $('.btn-close').toggle(step == 3);
        }

        function goToNextStep() {
            step++;
            fetchModalContent(step);
        }

        function goToPreviousStep() {
            if (step > 1) {
                step--;
                fetchModalContent(step);
            }
        }

        function reviewFiles() {
            $('.loading-wrapper').show();
            $('.risk-league-review-btn').prop('disabled', true);

            var fileUpload = document.getElementById("fileUploadMultiple");

            // Create a FormData object
            let formData = new FormData();
            formData.append('lineOfBusiness', 'RAFA');
            const skipHumanInTheLoop = $('#skipHumanInTheLoop').is(':checked');
            const disableRiskEngineerEvaluation = $('#disableRiskEngineerEvaluation').is(':checked');

            formData.append('skipHumanInTheLoop', !skipHumanInTheLoop);
            formData.append('disableRiskEngineerEvaluation', !disableRiskEngineerEvaluation);

            // Append the organizationId to the FormData object
            if (organizationId !== null) {
                formData.append('organizationId', organizationId);
            }

            const { fileLocationData, selectedLocationIds } = collectFileLocationData();

            if (!fileLocationData.length || (fileUpload.files.length !== fileLocationData.length)) {
                alert('Please associate all the files with a location');
                $('.loading-wrapper').hide();
                return false;
            }

            formData.append('fileLocationData', JSON.stringify(fileLocationData));
            formData.append('selectedLocationIds', selectedLocationIds);

            // Append each file to the FormData object
            for (let i = 0; i < fileUpload.files.length; i++) {
                formData.append('files[]', fileUpload.files[i].name);
            }

            console.log(formData, fileLocationData)

            // TODO: should use plupload instead of ajax
            $.ajax({
                url: "{{ route('risk-insights.process-files-for-rafa') }}",
                type: "POST",
                data: formData,
                processData: false, 
                contentType: false,
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                dataType: 'json',
                success: function(response) {
                    console.log('Success:', response);
                    console.log(response.document_id)
                    documentId = response.document_id
                    $('.loading-wrapper').hide();
                    $('.risk-league-review-btn').prop('disabled', false);
                    goToNextStep();
                },
                error: function(xhr, status, error) {
                    console.log('Error:', xhr.responseText);
                    $('.loading-wrapper').hide();
                    $('.risk-league-review-btn').prop('disabled', false);
                }
            });
        }

        $('.risk-league-next-btn').on('click', function () {
            // temporarily added to open the review modal
            if (step === 3) {
                // const url = "{{ route('risk-insights.index') }}" + "?documentId=" + documentId;
                // window.open(url, '_blank');
                $('#uploadRiskReportFileModal').modal('hide');
            } else if (step < totalSteps) {
                step++;
                fetchModalContent(step);
            }
        });

        // Add function to collect file and location data
        function collectFileLocationData() {
            let fileLocationData = [];
            let selectedLocationIds = [];
            
            
            // Iterate through each row in the table
            $('#uploadedFilesTableBody tr').each(function() {
                let $row = $(this);
                let fileName = $row.find('.risk-league-file').text();
                let locationId = $row.find('.selectric-location-dropdown').val();
                let locationTiv = $row.find('option[value="' + locationId + '"]').data('tiv');
                let locationName = $row.find('.selectric .label').text();

                if (locationId) {
                    const parseLocationId = parseInt(locationId);
                    selectedLocationIds.push(parseLocationId);
                
                    fileLocationData.push({
                        locationId: parseLocationId,
                        fileName: fileName,
                        locationName: locationName,
                        locationTiv: locationTiv
                    });
                }
            });
            
            return {
                fileLocationData: fileLocationData,
                selectedLocationIds: selectedLocationIds
            };
        }

        $('.update-document-btn').on('click', function () {
            $('#uploadRiskReportFileModal .loading-wrapper').show();
            $.ajax({
                url: "{{ route('risk-insights.remove-document-nhitl') }}",
                type: "POST",
                data: { documentId: documentId },
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    $('#uploadRiskReportFileModal .loading-wrapper').hide();
                    alert('Document updated successfully');
                },
                error: function(xhr, status, error) {
                    $('#uploadRiskReportFileModal .loading-wrapper').hide();
                    console.log('Error:', xhr.responseText);
                }
            });
        });

        $('.risk-league-prev-btn').on('click', function () {
            if (step > 1) {
                step--;
                fetchModalContent(step);
            }
        });

        $('.risk-league-review-btn').on('click', reviewFiles);

        $('#uploadRiskReportFileModal').on('hidden.bs.modal', function() {
            if (step === 3) {
                documentId = null;
            }

            if (step !== 1) {
                step = 1;
                localStorage.setItem('org-added', 'true');
                window.location.reload();
            }
        });

        $('#uploadRiskReportFileModal').on('shown.bs.modal', function () {
            step = 1;
            updateButtonVisibility();
            fetchModalContent(step);
        });
    });
</script>