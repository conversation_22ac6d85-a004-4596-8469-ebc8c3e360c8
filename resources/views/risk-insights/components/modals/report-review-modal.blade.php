<style>
    .loading-wrapper {
        position: absolute;
        top: 80%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1051; /* Ensure it's above the modal's z-index */
        width: 100%;
        height: 100%;
        display: none; /* Initially hidden */
        margin-top: 100px;
    }
</style>

<div class="modal fade risk-league-modal-wrapper" id="reviewRiskReportModal" tabindex="-1" aria-labelledby="reviewRiskReportModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg risk-league-modal">
        <div class="modal-content">
            <div class="modal-content-wrapper" style="min-height: 300px;"></div>
            <div class="loading-wrapper text-center mt-5">
                <div class="spinner-border" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-between default-button-wrapper">
                <button type="button" class="btn btn-light bg-light btn-cancel" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-light bg-light btn-close" data-dismiss="modal">Close</button>
                <button type="button" class="risk-league-next-btn btn btn-primary">Next <i class="icon icon-chevron-right fs-14 ml-2"></i></button>
                <div class="d-flex ml-auto review-button-wrapper">
                    <button type="button" class="btn btn-primary portfolio-impact-btn"><i class="icon icon-bar-chart fs-14 mr-2"></i> Portfolio Impact</button>
                    <button id="addToDatabaseButton" disabled type="button" class="btn btn-success ml-2 add-to-database-btn"><i class="icon icon-check fs-14 mr-2"></i> Add to Database</button>
                    <button id="finishReviewButton" disabled type="button" class="btn btn-success ml-2 finish-review-btn"><i class="icon icon-check fs-14 mr-2"></i> Finish Review</button>

                    <button id="updateDocumentButton" disabled type="button" class="btn btn-success ml-2 update-document-btn"><i class="icon icon-check fs-14 mr-2"></i> Update Document</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        let step = 1; 
        const totalSteps = 2;
        let organizationId = null;
        let locationIds = [];
        let reportResultData = null;

        function getQueryParam(param) {
            let urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(param);
        }

        // Check if 'documentid' is present in the URL
        let documentId = getQueryParam('documentId');

        function fetchReviewModalContent(step) {
          console.log('fetchReviewModalContent')
          if(!documentId) {
            return;
          }
            $('#reviewRiskReportModal .loading-wrapper').show();
            console.log('step', step)
            let params = {
                step: step,
                documentId: documentId
            };

            $('#reviewRiskReportModal .btn').prop('disabled', true);

            console.log('params', params)
            
            $.ajax({
                url: "{{ route('risk-insights.review-modal-content') }}",
                type: "GET",
                data: params,
                success: function(response) {
                    $('#reviewRiskReportModal .loading-wrapper').hide();
                    $('#reviewRiskReportModal .modal-content-wrapper').html(response);

                    updateButtonVisibility(step);
                },
                error: function(xhr, status, error) {
                    $('#reviewRiskReportModal .loading-wrapper').hide();
                    console.error(xhr.responseText);
                }
            });
        }

        function updateButtonVisibility() {
            $('#reviewRiskReportModal .btn').prop('disabled', false);
            $('#reviewRiskReportModal .add-to-database-btn').prop('disabled', true);
            $('#reviewRiskReportModal .finish-review-btn').prop('disabled', true);
            $('#reviewRiskReportModal .risk-league-next-btn').toggle(step == 1);
            $('#reviewRiskReportModal .btn-close').hide();

            $('#reviewRiskReportModal .portfolio-impact-btn, #reviewRiskReportModal .add-to-database-btn, #reviewRiskReportModal .finish-review-btn, #reviewRiskReportModal .update-document-btn').toggle(step == 2);
        }


        function goToNextStep() {
            step++;
            fetchReviewModalContent(step);
        }

        function goToPreviousStep() {
            if (step > 1) {
                step--;
                fetchReviewModalContent(step);
            }
        }

        function finishReview() {
            $('#reviewRiskReportModal .loading-wrapper').show();
            const reportResult = $('#reportResultData').data('report-result');
            $.ajax({
                url: "{{ route('risk-insights.complete-review') }}",
                type: "POST",
                data: { documentId: documentId, userId: reportResult.document.assigned_user.id },
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    $('#reviewRiskReportModal .loading-wrapper').hide();
                    $('#reviewRiskReportModal').modal('hide');
                    localStorage.setItem('org-added', 'true');
                    window.location.href = "{{ route('risk-insights.index') }}";
                },
                error: function(xhr, status, error) {
                    $('#reviewRiskReportModal .loading-wrapper').hide();
                    console.log('Error:', xhr.responseText);
                }
            });
        }

        function updateDocumentNHITLTag() {
            $('#reviewRiskReportModal .loading-wrapper').show();
            $.ajax({
                url: "{{ route('risk-insights.remove-document-nhitl') }}",
                type: "POST",
                data: { documentId: documentId },
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    $('#reviewRiskReportModal .loading-wrapper').hide();
                    alert('Document updated successfully');
                },
                error: function(xhr, status, error) {
                    $('#reviewRiskReportModal .loading-wrapper').hide();
                    console.log('Error:', xhr.responseText);
                }
            });
        }

        $('#reviewRiskReportModal .risk-league-next-btn').on('click', function () {
            if ($('#proposed-weightage-table').length) {
                const result = submitReviewWeightage();
                console.log('result', result)
                if (result?.error) {
                    alert(result?.message);
                    $('#reviewRiskReportModal .loading-wrapper').hide();
                    return;
                }
            }

            if (step < totalSteps) {
                step++;
                fetchReviewModalContent(step);
            }
        });

        $('#reviewRiskReportModal .risk-league-prev-btn').on('click', function () {
            if (step > 1) {
                step--;
                fetchReviewModalContent(step);
            }
        });

        $('#reviewRiskReportModal .add-to-database-btn, #reviewRiskReportModal .finish-review-btn').on('click', finishReview);
        $('#reviewRiskReportModal .update-document-btn').on('click', updateDocumentNHITLTag);

        $('#reviewRiskReportModal').on('hidden.bs.modal', function() {
            if (step !== 1) {
                step = 1;
                fetchReviewModalContent(step);
            }
        });

        $('#reviewRiskReportModal').on('shown.bs.modal', function () {
            step = 1;
            updateButtonVisibility();
            fetchReviewModalContent(step);
        });

        function submitReviewWeightage() {
            $('#reviewRiskReportModal .loading-wrapper').show();

            const weightages = {};
            let totalWeightage = 0;
            $('.weightage-input').each(function() {
                weightages[$(this).data('location-id')] = $(this).val();
                totalWeightage += parseFloat($(this).val()) || 0;
            });

            if (totalWeightage !== 100) {
                return {
                    error: true,
                    message: 'Total weightage must be exactly 100%'
                };
            }

            $.ajax({
                url: "{{ route('risk-insights.submit-review-weightage') }}",
                type: "POST",
                data: {
                    documentId: documentId,
                    weightages: weightages,
                },
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    return {
                        error: false,
                        message: 'Weightages submitted successfully'
                    };
                },
                error: function(xhr, status, error) {
                    return {
                        error: true,
                        message: xhr.responseText
                    };
                }
            });
        }

        function getPortfolioImpact() {
            $('#reviewRiskReportModal .loading-wrapper').show();
            $.ajax({
                url: "{{ route('risk-insights.get-portfolio-impact') }}",
                type: "GET",
                data: {
                    documentId: documentId,
                },
                success: function(response) {
                    $('#reviewRiskReportModal .loading-wrapper').hide();
                    $('#reviewRiskReportModal .modal-content-wrapper').html(response);
                    setTimeout(loadPortfolioImpactChart, 500);

                    $('#reviewRiskReportModal .portfolio-impact-btn').hide();
                    $('#reviewRiskReportModal .finish-review-btn').hide();
                    $('#reviewRiskReportModal .add-to-database-btn').hide();

                    $('#reviewRiskReportModal .portfolio-impact-back-btn').on('click', function () {
                        fetchReviewModalContent(step);
                    });

                    $('#reviewRiskReportModal .portfolio-impact-add-to-database-btn').on('click', function () {
                        console.log('portfolio-impact-add-to-database-btn');
                        finishReview();
                    });
                },
                error: function(xhr, status, error) {
                    $('#reviewRiskReportModal .loading-wrapper').hide();
                    console.log('Error:', xhr.responseText);
                }
            });
        }

        $('#reviewRiskReportModal .portfolio-impact-btn').on('click', getPortfolioImpact);
            
    });
</script>