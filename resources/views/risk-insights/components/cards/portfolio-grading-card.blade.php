@php
use App\Helpers\RiskInsightsHelper;
@endphp
<div class="col d-flex mb-3 mb-lg-0">
    <div class="card">
        <div class="card-title">
            <i class="icon icon-calendar"></i>
            Portfolio Grading
        </div>
        <div class="card-body">
            <?php
            $portfolio_score = $dashboard->current_score->portfolio_score;
            $score_color_class = RiskInsightsHelper::getColorClass($portfolio_score);
            $score_color_hex = RiskInsightsHelper::getColorHexByScore($portfolio_score);

            $market_value_score = $dashboard->current_score->market_value_score;
            $market_value_score_color_class = RiskInsightsHelper::getColorClass($market_value_score);
            $market_value_score_color_hex = RiskInsightsHelper::getColorHexByScore($market_value_score);
            ?>
            <div class="d-flex justify-content-end">
                <div class="rating-wrapper">
                    <div class="risk-league-rating risk-league-rating-{{ $portfolio_score }} {{ $score_color_class }} ml-2"
                         data-color="{{ $score_color_hex }}">
                        {{ $portfolio_score }}
                    </div>

                    <div class="risk-league-rating risk-league-rating-{{ $market_value_score }} text-gray ml-2"
                         data-color="#808080">
                        {{ $market_value_score }}
                    </div>
                </div>
            </div>
            @include('risk-insights.components.charts.current-score-chart')
        </div>
    </div>
</div>