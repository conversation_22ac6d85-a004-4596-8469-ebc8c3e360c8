<div class="card">
    <div class="card-body px-0">
        <div class="d-flex align-items-center justify-content-between px-3 pb-2">
            <div class="d-flex align-items-center">
                <div class="risk-league-company-logo lg mr-2" style="background-image: url('{{ $dashboardCompany->image_url }}');"></div>
                <div class="lh-16">
                    <div class="fs-15 text-navy">{{ $dashboardCompany->name }}</div>
                    {{-- <a href="#" class="fs-11">Upload logo</a> --}}
                </div>
            </div>
            <div class="risk-league-portfolio-score">
                Organisation Score is
                <div class="risk-league-rating {{ $score_color_class }} lg ml-2">{{ $dashboardCompany->risk_score ?? 0 }}</div>
            </div>
        </div>
        <div class="border-top d-flex align-items-center justify-content-between px-3 pt-2">
            <div class="meta-broker">
                <div class="d-flex justify-content-center align-items-center">
                    <img src="{{ $dashboardCompany->broker_logo }}" width="36" height="auto" alt="Marsh Logo" class="rounded" onerror="this.remove();">
                    <div class="fs-11 lh-16 ml-2">
                        <p class="mb-0">Broker</p>
                        <p class="fs-13 text-secondary">{{ str_contains($dashboardCompany->broker->name, 'Aon') ? 'Aon' : $dashboardCompany->broker->name }}</p>
                        {{-- <a href="#">Upload logo</a> --}}
                    </div>
                </div>
            </div>
            <div class="meta-insurer">
                <div class="d-flex justify-content-center align-items-center">
                    <img src="{{ $dashboardCompany->lead_insurer_logo }}" width="36" height="auto" alt="AIG Logo" class="rounded" onerror="this.remove();">
                    <div class="fs-11 lh-16 ml-2">
                        <p class="mb-0">Lead Insurer</p>
                        <p class="fs-13 text-secondary">{{ $dashboardCompany->lead_insurer->name ?? 'N/A' }}</p>
                        {{-- <a href="#">Upload logo</a> --}}
                    </div>
                </div>
            </div>
            <div class="fs-11 lh-16 ml-2">
                <p class="mb-0">Renewal Date</p>
                <p class="fs-13 text-secondary">{{ $dashboardCompany->renewal_date }}</p>
            </div>
            <div class="fs-11 lh-16 ml-2">
                <p class="mb-0">Sector</p>
                <p class="fs-13 text-secondary">{{ $dashboardCompany->sector->handle ?? 'N/A' }}</p>
            </div>
            <div class="fs-11 lh-16 ml-2">
                <p class="mb-0">Subsector</p>
                <p class="fs-13 text-secondary">{{ $dashboardCompany->sub_sector->handle ?? 'N/A' }}</p>
            </div>
            <div class="fs-11 lh-16 ml-2">
                <p class="mb-0">Position</p>
                <p class="fs-13 text-secondary">{{ $dashboardCompany->position ?? 'N/A' }}</p>
            </div>
            <div class="fs-11 lh-16 ml-2">
                <p class="mb-0">Line Size</p>
                <p class="fs-13 text-secondary">{{ $dashboardCompany->line_size ?? 0 }}</p>
            </div>
            <div class="fs-11 lh-16 ml-2"></div>
        </div>
    </div>
</div>