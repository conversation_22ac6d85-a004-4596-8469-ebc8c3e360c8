@php
    use App\Helpers\RiskInsightsHelper;
@endphp
<div class="col d-flex mb-3 mb-lg-0">
    <div class="card">
        <div class="card-title">
            <i class="icon icon-list"></i>
            Risk Grading
        </div>
        <div class="card-body" id="risk-grading-section">
            <table class="table">
                <thead>
                <tr>
                    <th class="text-left">Category</th>
                    <th>LII Grading</th>
                    <th>Market Grading</th>
                </tr>
                </thead>
                <tbody>
                @if (isset($dashboard->risk_grading) && is_array($dashboard->risk_grading))
                    @foreach ($dashboard->risk_grading as $index => $grade)
                        @php
                            $subSector = request('sub_sector') ?? null;
                            $organisationSubSector = isset($dashboardCompany) && $dashboardCompany->sub_sector ? $dashboardCompany->sub_sector->id : null;
                            $lii_grade = $grade->score;
                            $market_grade = $grade->score2;
                            if ($subSector == 1 || $organisationSubSector == 1) {
                                $lii_grade = $grade->food_and_beverage->lii_grading;
                                $market_grade = $grade->food_and_beverage->market_grading;
                            } elseif ($subSector == 2 || $organisationSubSector == 2) {
                                $lii_grade = $grade->department_store->lii_grading;
                                $market_grade = $grade->department_store->market_grading;
                            } else {
                                $lii_avg = ($grade->food_and_beverage->lii_grading + $grade->department_store->lii_grading) / 2;
                                $market_avg = ($grade->food_and_beverage->market_grading + $grade->department_store->market_grading) / 2;
                                $lii_grade = intval(round($lii_avg));
                                $market_grade = intval(round($market_avg));
                            }
                            $borderClass = ($index < count($dashboard->risk_grading) - 1) ? 'border-bottom' : '';
                            $colorClass = RiskInsightsHelper::getColorClass($lii_grade);
                            $colorClass2 = RiskInsightsHelper::getColorClass($market_grade);
                        @endphp
                        <!-- <div class="risk-league-grading <?php echo $grade->status; ?> mr-2"></div> -->
                        <tr>
                            <td class="text-left"><a href="{{ route('risk-insights.risk-grading', ['risk' => $grade->category, 'attributeId' => $grade->id ?? 1]) }}"><?php echo htmlspecialchars($grade->category); ?></a></td>
                            <td><div class="risk-league-rating <?php echo $colorClass; ?> <?php echo RiskInsightsHelper::isHighlightScore($lii_grade, $market_grade) ?> ml-auto"><?php echo $lii_grade; ?></div></td>
                            <td><div class="risk-league-rating <?php echo $colorClass2; ?> <?php echo RiskInsightsHelper::isHighlightScore($market_grade, $lii_grade) ?> ml-auto"><?php echo $market_grade; ?></div></td>
                        </tr>
                    @endforeach
                @else
                    <p>No risk grading data available.</p>
                @endif
                </tbody>
            </table>
        </div>
    </div>
</div>
