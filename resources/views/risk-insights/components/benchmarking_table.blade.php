@php
    use App\Helpers\RiskInsightsHelper;
@endphp

<div class="card">
    <div class="card-title">
        <i class="icon icon-list"></i>
        Benchmarking
    </div>
    <div class="card-body">

        <!-- <div class="risk-league-ai-input risk-league-ai-helper">
            <input type="text" placeholder="What can I help you with today?" data-toggle="modal" data-target="#aiModal01">
        </div> -->

        <div class="table-responsive">
            <table class="table risk-league-table-sortable" id="benchmarkingTable">
                <thead>
                <tr>
                    <th data-sort="organisation">Client Name</th>
                    <th data-sort="premium">Premium</th>
                    <th data-sort="sector" class="text-center">Sector</th>
                    <th data-sort="sub-sector" class="text-center">Sub-Sector</th>
                    <th data-sort="lead-insurer" class="text-center">Lead Insurer</th>
                    <th data-sort="broker" class="text-center">Broker</th>
                    <th data-sort="position" class="text-center">Lead/Follow</th>
                    <th data-sort="line-size" class="text-center">Line Size</th>
                    <th data-sort="surveys" class="text-center">Surveys</th>
                    <th class="date-column text-center" data-sort="last-updated">Last Updated</th>
                    <th data-sort="policy-segment" class="text-center">Policy Segment</th>
                    <th class="date-column text-center" data-sort="renewal-date">Renewal Date</th>
                    <th data-sort="underwriter" class="text-center">Underwriter</th>
                    <th data-sort="risk-score" class="text-center">Risk Score</th>
                </tr>
                </thead>
                <tbody>
                @foreach ($benchmarking->data as $index => $org)
                    <tr data-organisation="{{ $org->name }}"
                        data-premium="{{ $org->premium }}"
                        data-sector="{{ $org->sector->handle }}"
                        data-sub-sector="{{ $org->sub_sector->handle }}"
                        data-broker="{{ $org->broker->name }}"
                        data-position="{{ $org->position }}"
                        data-lead-insurer="{{ $org->lead_insurer->name }}"
                        data-line-size="{{ $org->line_size }}"
                        data-surveys="{{ count($org->surveys) }}"
                        data-last-updated="{{ $org->updated_at }}"
                        data-policy-segment="{{ $org->policy_segment }}"
                        data-renewal-date="{{ $org->expiry_date_of_cover }}"
                        data-underwriter="{{ $org->underwriter }}"
                        data-risk-score="{{ $org->risk_score ?? 0 }}"
                        data-index="{{ $index }}"
                        >
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="risk-league-company-logo mr-2" style="background-image: url('{{ $org->link }}');"></div>
                                <a href="{{ route('risk-insights.risk-benchmark', ['organisation_id' => $org->id, 'risk' => urlencode($org->name), 'slug' => Str::slug($org->name)]) }}" class="fs-15 text-left">{{ $org->name }}</a>
                            </div>
                        </td>
                        <td>{{ $org->premium }}</td>
                        <td>{{ $org->sector->handle }}</td>
                        <td>{{ $org->sub_sector->handle }}</td>
                        <td>{{ $org->lead_insurer->name }}</td>
                        <td>{{ str_contains($org->broker->name, 'Aon') ? 'Aon' : $org->broker->name; }}</td>
                        <td class="col-position">{{ $org->position ?? '-' }}</td>
                        <td>
                            <span class="line-size {{ isset($_GET['preview']) && $_GET['preview']==1 && $index==1 ? 'preview' : '' }}" data-toggle="modal" data-target="#dataPredictionModal">{{ $org->line_size ?? 0 }}</span>
                        </td>
                        <td>{{ count($org->surveys) }}</td>
                        <td>{{ Carbon\Carbon::parse($org->updated_at)->format('d/m/Y') }}</td>
                        <td>{{ $org->policy_segment }}</td>
                        <td>{{ Carbon\Carbon::parse($org->expiry_date_of_cover)->format('d/m/Y') }}</td>
                        <td>Lorem Ipsum</td>
                        
                        <td>
                            <div class="d-flex align-items-center">
                                {{-- <div id="risk_score_chart_{{ $index }}" class="risk-league-risk_score_chart"
                                    data-series-color="{{ RiskInsightsHelper::getColorHexByScore($org->risk_score) }}"
                                    data-chart-data='{{ json_encode($org->risk_score->chart_data) }}'></div> --}}
                                <div class="risk-league-rating lg {{ RiskInsightsHelper::getColorClass($org->risk_score) }} ml-3">
                                        {{ $org->risk_score }}
                                </div>
                            </div>
                        </td>

    
                    </tr>
                @endforeach
                </tbody>
            </table>
        </div>

    </div>
</div>

<style>
@keyframes insertRow {
    0% {
        height: 0;
        opacity: 0;
        overflow: hidden;
        background-color: rgb(98, 229, 174);
        transform: scaleY(0);
        transform-origin: top;
    }
    20% {
        height: auto;
        opacity: 1;
        overflow: hidden;
        transform: scaleY(1);
    }
    100% {
        height: auto;
        opacity: 1;
        overflow: visible;
        background-color: transparent;
        box-shadow: none;
    }
}

.animate-insert {
    animation: insertRow 7s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-insert td {
    transition: all 0.3s ease;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Check if 'org-added' and 'org-animated' exist in session (passed from backend)
    const orgAdded = {{ $orgAdded ? 'true' : 'false' }};
    const orgAnimated = {{ $orgAnimated ? 'true' : 'false' }};
    const targetRow = document.querySelector('tr[data-index="0"]');

    if (!targetRow) return;

    if (orgAdded && !orgAnimated) {
        // If 'org-added' exists but 'org-animated' doesn't exist, animate the row insertion
        targetRow.classList.add('animate-insert');

        // After animation completes, make AJAX call to set 'org-animated' in session
        targetRow.addEventListener('animationend', function() {
            // Make AJAX call to set org-animated session variable
            fetch('{{ route("risk-insights.set-org-animated") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('org-animated session variable set successfully');
                }
            })
            .catch(error => {
                console.error('Error setting org-animated session variable:', error);
            });

            targetRow.classList.remove('animate-insert');
        }, { once: true });
    }
});
</script>
