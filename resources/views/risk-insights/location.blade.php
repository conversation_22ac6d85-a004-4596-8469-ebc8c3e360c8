@extends('risk-insights.layouts.app')

@section('content')

    @include('risk-insights.components.topbar')
    @include('risk-insights.breadcrumb')

    @php
    $company_name = $_GET['name'] ?? 'Tesco';
    $lob = $_GET['lob'] ?? 'Construction & Exposures';
    $risk = $_GET['lob'] ?? 'Unknown Risk';
    // $location_score = $dashboard_location['location_score'] ?? 0;
    $slug = $_GET['slug'] ?? 'tesco';
    $location = $_GET['loc'] ?? 'Chichester Tesco Express';

    // Create new company name
    $loc_parts = explode(' ', htmlspecialchars($location));
    $loc_parts[1] = $company_name;

    $new_loc = '';
    foreach($loc_parts as $part) $new_loc .= $part . ' ';
    $selected_location = null;

    // Get location score
    foreach($organisationBenchmarking->locations as $loc) {
        if ($loc->location == urldecode($location)) {
            $risk_score = $loc->risk_score;
            $location_score = $risk_score->risk_league_rating->value;
            $selected_location = $loc;
        }
    }
    $score_color_class = \App\Helpers\RiskInsightsHelper::getColorClassByGrade($location_score);
    @endphp

    <div class="container pb-4">
        @include('risk-insights.components.cards.company-location-card')
    </div>

    <div class="container">
        @include('risk-insights.benchmark-locations')

        @include('risk-insights.components.modals.upload-risk-report-file-modal')
    </div>


@endsection